<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:baselineAligned="false"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2">

            <SurfaceView
                android:id="@+id/surfaceView"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/black"
                android:visibility="gone"
                tools:ignore="ContentDescription" />

        </FrameLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@android:color/white"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="46dp"
                android:gravity="center"
                android:text="@string/goods_list"
                android:textColor="@color/text_color"
                android:textSize="16sp"
                android:textStyle="bold" />

            <View
                android:id="@+id/divider_top"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_below="@+id/tv_title"
                android:background="@color/divider_color" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_goods_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/layout_vip"
                android:layout_below="@+id/divider_top" />

            <LinearLayout
                android:id="@+id/layout_vip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/divider_bottom"
                android:layout_margin="10dp"
                android:background="@drawable/vip_bg"
                android:orientation="vertical"
                android:paddingHorizontal="6dp"
                android:paddingVertical="6dp"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/vip_bg"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_vip_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:ellipsize="end"
                            android:lines="1"
                            android:maxWidth="80dp"
                            android:textColor="@color/text_color"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            tools:text="李大大" />

                        <TextView
                            android:id="@+id/tv_vip_level"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginHorizontal="4dp"
                            android:background="@drawable/level_name_bg"
                            android:ellipsize="end"
                            android:lines="1"
                            android:padding="2dp"
                            android:textColor="#773E0B"
                            android:textSize="11sp"
                            tools:text="黄金会员" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical|end"
                        android:ellipsize="end"
                        android:lines="1"
                        android:maxLength="11"
                        android:textColor="@color/text_color"
                        android:textSize="11sp"
                        android:textStyle="italic"
                        tools:text="18000001111" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_store"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:lines="1"
                        android:maxLength="11"
                        android:textColor="#666666"
                        android:textSize="11sp"
                        tools:text="储值余额:10000" />

                    <TextView
                        android:id="@+id/tv_score"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:lines="1"
                        android:maxLength="11"
                        android:textColor="#666666"
                        android:textSize="11sp"
                        tools:text="储值余额:10000" />

                </LinearLayout>
            </LinearLayout>

            <View
                android:id="@+id/divider_bottom"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_above="@+id/layout_bottom"
                android:background="@color/divider_color" />

            <LinearLayout
                android:id="@+id/layout_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:orientation="vertical"
                android:padding="10dp">

                <TextView
                    android:id="@+id/tv_discount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:textColor="#333333"
                    android:textSize="16sp"
                    android:visibility="gone"
                    tools:text="优惠:￥9.00"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_total"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:gravity="end"
                    android:textColor="#333333"
                    android:textSize="16sp"
                    tools:text="共8件，应付:￥118.00" />

            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@android:color/black"
        android:visibility="gone" />

</FrameLayout>