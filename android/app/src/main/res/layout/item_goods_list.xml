<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="6dp"
    tools:background="@android:color/white">

    <TextView
        android:id="@+id/tv_goods_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:lines="1"
        android:paddingHorizontal="6dp"
        android:textColor="#333333"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/combo_mark"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="卫龙辣条36g" />

    <TextView
        android:id="@+id/combo_mark"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="6dp"
        android:background="@drawable/combo_mark_bg"
        android:paddingHorizontal="3dp"
        android:text="@string/combo_mark"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_goods_name"
        app:layout_constraintEnd_toStartOf="@+id/tv_goods_name"
        app:layout_constraintStart_toEndOf="@+id/promotion_mark"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/promotion_mark"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="6dp"
        android:background="@drawable/promotion_mark_bg"
        android:paddingHorizontal="3dp"
        android:text="@string/promotion_mark"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_goods_name"
        app:layout_constraintEnd_toStartOf="@+id/combo_mark"
        app:layout_constraintStart_toEndOf="@+id/discount_mark"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/discount_mark"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="6dp"
        android:background="@drawable/discount_mark_bg"
        android:paddingHorizontal="3dp"
        android:text="@string/discount_mark"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_goods_name"
        app:layout_constraintEnd_toStartOf="@+id/promotion_mark"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_goods_price"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:ellipsize="end"
        android:lines="1"
        android:paddingStart="6dp"
        android:paddingEnd="0dp"
        android:textColor="#666666"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@+id/tv_goods_num"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_name"
        tools:text="￥8.00/10.00" />

    <TextView
        android:id="@+id/tv_goods_num"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="end"
        android:lines="1"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:textColor="#333333"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@+id/tv_total"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tv_goods_price"
        app:layout_constraintTop_toTopOf="@+id/tv_goods_price"
        tools:text="x2个" />

    <TextView
        android:id="@+id/tv_total"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="end"
        android:lines="1"
        android:paddingStart="0dp"
        android:paddingEnd="6dp"
        android:textColor="#333333"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tv_goods_num"
        app:layout_constraintTop_toTopOf="@+id/tv_goods_price"
        tools:text="￥0.00" />

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginTop="10dp"
        android:background="@drawable/dash_line"
        android:layerType="software"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_price" />

</androidx.constraintlayout.widget.ConstraintLayout>