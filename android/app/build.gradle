plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    compileSdk 35

    compileOptions {
        sourceCompatibility = 11
        targetCompatibility = 11
    }

    kotlinOptions {
        jvmTarget = "11"
    }

    viewBinding {
        enabled = true
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        ndk {
            // 设置支持的SO库架构，bugly
            abiFilters 'x86','armeabi-v7a', 'x86_64', 'arm64-v8a'
//            abiFilters 'armeabi' //, 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.wsgjp.halo_pos"
        minSdkVersion 21
        //noinspection ExpiredTargetSdkVersion
        targetSdkVersion 28
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            storeFile file('haloPos.jks')
            keyAlias 'wsgjp.com'
            keyPassword 'grasp#123'
            storePassword 'grasp#123'
        }
//        debug {
//            storeFile file('haloPos.jks')
//            keyAlias 'wsgjp.com'
//            keyPassword 'grasp#123'
//            storePassword 'grasp#123'
//        }
    }
    buildTypes {
        release {
            minifyEnabled false //是否启用混淆
            shrinkResources false
            signingConfig signingConfigs.release
        }
        debug {
            minifyEnabled false //是否启用混淆
            shrinkResources false
            signingConfig signingConfigs.release
        }

    }
    buildToolsVersion = '30.0.3'
    namespace 'com.wsgjp.halo_pos'
    lint {
        disable 'InvalidPackage'
    }
}

//将所有kotlin任务的jvm都改为11，保持和Andorid的gradle的jvm环境一致
//tasks.withType(org.jetbrains.kotlin.gradle.tasks.KaptGenerateStubs).configureEach{
//    kotlinOptions{
//        jvmTarget = 1.8
//    }
//}

//tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
//    kotlinOptions {
//        jvmTarget = "1.8"  // 或 "8"
//    }
//}

flutter {
    source '../..'
}

dependencies {
//    implementation 'com.tencent.rqd:nativecrashreport:latest.release'
    implementation 'com.tencent.bugly:crashreport:4.1.9.2' //其中latest.release指代最新Bugly SDK版本号，也可以指定明确的版本号，例如4.0.3
//    implementation 'com.tencent.rqd:nativecrashreport:latest.release' //其中latest.release指代最新Bugly NDK版本号，也可以指定明确的版本号，例如3.9.2
    implementation 'androidx.constraintlayout:constraintlayout:2.1.1'
    implementation 'androidx.recyclerview:recyclerview:1.2.0'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    implementation fileTree(include: ['*.jar'], dir: 'libs')

    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    kapt 'com.github.bumptech.glide:compiler:4.11.0'
    api fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.sunmi:printerx:1.0.11'
    implementation 'com.github.mik3y:usb-serial-for-android:3.7.0'
}
