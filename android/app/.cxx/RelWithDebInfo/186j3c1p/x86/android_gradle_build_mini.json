{"buildFiles": ["/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/工作/halo_pos_new/android/app/.cxx/RelWithDebInfo/186j3c1p/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/工作/halo_pos_new/android/app/.cxx/RelWithDebInfo/186j3c1p/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}