android.useAndroidX=true
android.enableJetifier=true
#????????????????
#The option 'android.enableD8' is deprecated.
#The current default is 'true'.
#It was removed in version 7.0 of the Android Gradle plugin
android.enableD8=true

#???AGP??blacklist?ignorelist??
#android.jetifier.blacklist=bcprov-jdk15on
#android.jetifier.ignorelist=bcprov-jdk15on
org.gradle.daemon=true
org.gradle.jvmargs= -Xms512m -Xmx2048m  -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.configureondemand=true
android.suppressUnsupportedCompileSdk=34,35
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false
systemProp.javax.net.ssl.protocols=TLSv1,TLSv1.1,TLSv1.2,TLSv1.3