# POS系统电子秤功能实现文档

## 文档信息
- **创建时间**: 2025-01-27
- **版本**: v1.0
- **作者**: 系统分析
- **项目**: Halo POS

## 目录
1. [概述](#1-概述)
2. [整体架构](#2-整体架构)
3. [内置电子秤实现](#3-内置电子秤实现)
4. [第三方电子秤实现](#4-第三方电子秤实现)
5. [条码秤实现](#5-条码秤实现)
6. [统一工具类和状态管理](#6-统一工具类和状态管理)
7. [业务集成应用](#7-业务集成应用)
8. [平台适配](#8-平台适配)
9. [配置管理](#9-配置管理)
10. [技术特点](#10-技术特点)

## 1. 概述

POS系统的电子秤功能为生鲜商品销售提供了完整的称重解决方案，支持多种类型的电子秤设备，包括内置电子秤、第三方USB电子秤和条码秤。系统通过统一的接口设计，实现了跨平台的电子秤功能集成。

### 1.1 支持的电子秤类型
- **内置电子秤**: 商米电子秤（通过AIDL服务通信）
- **第三方电子秤**: USB串口通信电子秤（如大华电子秤）
- **条码秤**: 通过条码解析重量和金额信息

### 1.2 支持的平台
- **Android**: 通过Kotlin插件实现原生功能
- **Windows**: 通过QuickUsb库实现USB通信

## 2. 整体架构

### 2.1 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                        业务层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   销售开单      │  │   生鲜称重      │  │   条码秤解析    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        工具层                                │
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │   ScaleTool     │  │ ScaleController │                    │
│  │   (统一接口)    │  │  (状态管理)     │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        插件层                                │
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │  ScalePlugin    │  │ThirdPartyScale  │                    │
│  │   (内置秤)      │  │   Plugin        │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        平台层                                │
│  ┌─────────────────┐  ┌─────────────────┐                    │
│  │   Android       │  │    Windows      │                    │
│  │   (Kotlin)      │  │   (QuickUsb)    │                    │
│  └─────────────────┘  └─────────────────┘                    │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

| 组件 | 功能 | 文件路径 |
|------|------|----------|
| ScalePlugin | 内置电子秤插件 | `lib/plugin/scale_plugin.dart` |
| ThirdPartyScalePlugin | 第三方电子秤插件 | `lib/plugin/third_party_scale_plugin.dart` |
| ScaleTool | 电子秤统一工具类 | `lib/bill/tool/scale_tool.dart` |
| ScaleController | 电子秤状态控制器 | `lib/bill/tool/scale_tool.dart` |
| BarcodeScaleConfig | 条码秤配置 | `lib/login/entity/store/barcode_scale_config.dart` |

## 3. 内置电子秤实现

### 3.1 Android插件实现

**文件路径**: `android/app/src/main/kotlin/com/wsgjp/halo_pos/plugins/scale_plugin/ScalePlugin.kt`

#### 3.1.1 核心功能
```kotlin
class ScalePlugin : FlutterPlugin, MethodCallHandler {
    /**
     * 初始化和连接电子秤
     */
    private fun init() {
        if (isConnected) return
        ScaleManager.getInstance(context)
            .connectService(object : ScaleManager.ScaleServiceConnection {
                override fun onServiceConnected() {
                    isConnected = true
                    Log.d("scaleTest", "connected")
                    startListen()
                    invokeMethod("onConnectionChange", isConnected)
                }

                override fun onServiceDisconnect() {
                    isConnected = false
                    Log.d("ScalePlugin", "disConnected")
                    invokeMethod("onConnectionChange", isConnected)
                }
            })
    }
}
```

#### 3.1.2 数据监听
```kotlin
private fun startListen() {
    if (!isConnected) return
    ScaleManager.getInstance(context).getData(object : ScaleResult() {
        /**
         * @param net 净重 g
         * @param tare 皮重 g
         * @param isStable 是否稳定状态
         */
        override fun getResult(net: Int, tare: Int, isStable: Boolean) {
            val arguments = HashMap<String, Any>()
            arguments["weight"] = net
            arguments["tare"] = tare
            arguments["isStable"] = isStable
            ///返回称重结果
            invokeMethod("onWeightChange", arguments)
        }

        /**
         * @param isLightWeight 秤是否过轻(小于20E)
         * @param overload 秤是否过载
         * @param clearZeroErr 秤是否清零错误
         * @param calibrationErr 秤是否标定错误
         */
        override fun getStatus(
            isLightWeight: Boolean,
            overload: Boolean,
            clearZeroErr: Boolean,
            calibrationErr: Boolean
        ) {
            val arguments = HashMap<String, Any>()
            arguments["isLightWeight"] = isLightWeight
            arguments["overload"] = overload
            arguments["clearZeroErr"] = clearZeroErr
            arguments["calibrationErr"] = calibrationErr
            ///返回秤状态
            invokeMethod("onStatusChange", arguments)
        }
    })
}
```

### 3.2 Flutter端接口

**文件路径**: `lib/plugin/scale_plugin.dart`

#### 3.2.1 接口定义
```dart
///商米电子秤插件,参考[https://developer.sunmi.com/docs/zh-CN/xeghjk491/cixeghjk491]

///称重服务连接变化
typedef OnConnectionChange = void Function(bool isConnected);

///称重状态变化的回调
///[weight] 净重 g，不包含皮重
///[tare] 皮重 g，
///[isStable] 是否稳定，
typedef OnWeightChange = void Function(
    {required int weight, required int tare, required bool isStable});

///秤状态变化
///[isLightWeight] 秤是否过轻(小于20E)
///[overload] 秤是否过载
///[clearZeroErr] 秤是否清零错误
///[calibrationErr] 秤是否标定错误
typedef OnStatusChange = void Function(
    {required bool isLightWeight,
    required bool overload,
    required bool clearZeroErr,
    required bool calibrationErr});
```

#### 3.2.2 主要方法
```dart
class ScalePlugin {
  /// 初始化和连接电子秤
  Future<void> init() => _channel.invokeMethod("init");

  /// 断开电子秤服务
  Future<void> dispose() {
    onConnectionChange = null;
    onStatusChange = null;
    onWeightChange = null;
    return _channel.invokeMethod("dispose");
  }

  ///清零
  Future<void> zero() => _channel.invokeMethod("zero");

  /// 去⽪/清⽪
  /// 秤上有种重量是去⽪，没有时清⽪
  Future<void> setTare() => _channel.invokeMethod("tare");

  /// 数字去⽪
  Future<void> digitalTare(int tare) =>
      _channel.invokeMethod("digitalTare", tare);
}
```

### 3.3 数据格式

| 数据项 | 类型 | 单位 | 说明 |
|--------|------|------|------|
| weight | int | g | 净重，不包含皮重 |
| tare | int | g | 皮重 |
| isStable | bool | - | 是否稳定状态 |
| isLightWeight | bool | - | 是否过轻(小于20E) |
| overload | bool | - | 是否过载 |
| clearZeroErr | bool | - | 是否清零错误 |
| calibrationErr | bool | - | 是否标定错误 |

## 4. 第三方电子秤实现

### 4.1 Android USB串口实现

**文件路径**: `android/app/src/main/kotlin/com/wsgjp/halo_pos/plugins/third_party_scale_plugin/ThirdPartyScalePlugin.kt`

#### 4.1.1 设备连接
```kotlin
/**
 * 连接到设备
 */
private fun connectDevice(context: Context, driver: UsbSerialDriver): UsbSerialPort? {
    val manager = context.getSystemService(Context.USB_SERVICE) as UsbManager
    var port: UsbSerialPort? = null
    try {
        var connection = manager.openDevice(driver.device)
        if (connection == null) {
            Toast.makeText(context, "连接失败", Toast.LENGTH_SHORT).show()
            return null
        }
        port = driver.ports.first()
        port.open(connection)
        port.setParameters(9600, 8, UsbSerialPort.STOPBITS_1, UsbSerialPort.PARITY_NONE)
    } catch (e: Exception) {
        Toast.makeText(context, "连接异常", Toast.LENGTH_SHORT).show()
    }
    return port
}
```

#### 4.1.2 数据读取
```kotlin
// 串口参数配置
port.setParameters(
    9600,                           // 波特率
    8,                             // 数据位
    UsbSerialPort.STOPBITS_1,      // 停止位
    UsbSerialPort.PARITY_NONE      // 校验位
)

// 数据解析逻辑
for ((index, byte) in list.withIndex().reversed()) {
    if (byte == 0x0d.toByte()) {
        dIndex = index
    } else if (byte == 0x0a.toByte()) {
        if (dIndex > 0 && index + 1 == dIndex) {
            if (start < 0) {
                start = dIndex + 1;
            } else {
                val data = list.subList(dIndex + 1, start - 2).toList()
                list = list.subList(start, list.size).toMutableList()
                sendData(data)
            }
        }
    }
}
```

### 4.2 Windows平台实现

**文件路径**: `lib/plugin/third_party_scale_plugin.dart`

#### 4.2.1 设备枚举
```dart
static Future<List<SimpleUsbDevice>> getDevices() async {
  if (!Platform.isWindows) return [];
  await QuickUsb.init();
  await disconnectAllDevice();
  _devices = await QuickUsb.getDevicesWithDescription();
  return _devices
      .map((e) => SimpleUsbDevice(
          e.device.vendorId,
          e.device.productId,
          e.serialNumber ?? "",
          e.product ?? e.serialNumber ?? e.device.identifier))
      .toList();
}
```

#### 4.2.2 设备连接和数据读取
```dart
@pragma('vm:entry-point')
static void _connectDevice(Map map) async {
  UsbDeviceDescription device = map["device"];
  SendPort sendPort = map["port"];

  await QuickUsb.init();
  await QuickUsb.openDevice(device.device);
  var configuration = await QuickUsb.getConfiguration(0);
  var interface = configuration.interfaces.first;
  await QuickUsb.claimInterface(interface);

  // 数据读取循环
  streamSubscription = stream.listen((event) async {
    var bulkTransferOut = await QuickUsb.bulkTransferIn(endpoint!, 1024, timeout: 0);
    buffer.addAll(bulkTransferOut);

    // 数据解析：查找0x0a和0x0d标识符
    for (int index = buffer.length - 1; index >= 0; index--) {
      int byte = buffer[index];
      if (byte == 0x0d) {
        dIndex = index;
      } else if (byte == 0x0a) {
        if (dIndex > 0 && index + 1 == dIndex) {
          final data = buffer.sublist(dIndex + 1, start - 2).toList(growable: false);
          buffer = buffer.sublist(start).toList();
          sendPort.send(int.tryParse(String.fromCharCodes(data).trim()) ?? 0);
          break;
        }
      }
    }
  });
}
```

### 4.3 USB设备权限管理

#### 4.3.1 权限申请
```kotlin
private fun checkPermission(context: Context, device: UsbDevice) {
    val manager = context.getSystemService(Context.USB_SERVICE) as UsbManager
    val permissionPendingIntent = PendingIntent.getBroadcast(
        context, 0, Intent(ACTION_USB_PERMISSION),
        PendingIntent.FLAG_MUTABLE
    )
    manager.requestPermission(device, permissionPendingIntent)
}
```

#### 4.3.2 权限广播监听
```kotlin
private val broadcastReceiver = object : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        when (action) {
            ACTION_USB_PERMISSION -> {
                if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                    if (isCurrentDevice) {
                        currentPort = connectDevice(mContext!!, currentDriver!!)
                    }
                } else {
                    Log.d(TAG, "permission denied for device $device")
                }
            }
            ACTION_USB_ATTACHED -> {
                Log.d(TAG, "ACTION_USB_ATTACHED")
            }
            ACTION_USB_DETACHED -> {
                if (isCurrentDevice) {
                    Toast.makeText(context, "电子秤已断开", Toast.LENGTH_SHORT).show()
                }
                disconnectAllDevice()
            }
        }
    }
}
```

## 5. 条码秤实现

### 5.1 条码格式配置

**文件路径**: `lib/login/entity/store/barcode_scale_config.dart`

#### 5.1.1 配置实体
```dart
class BarcodeScaleConfig {
  String? id;
  ///门店id
  String? otypeId;
  ///条码输出格式（0=18位重量+金额码；2=18位金额+重量码；3=13位重量码;4=13位金额码；）
  int outputFormatType = 0;
  ///标识位数(支持1 位/2 位)
  int markDigit = 1;
  ///金额小数位数(支持1 位/2 位)
  int totalDigit = 2;
  ///重量小数位数(支持1 位/2 位)
  int weightDigit = 2;
}
```

#### 5.1.2 条码类型枚举
```dart
enum BarcodeScaleTypeEnum {
  ///0=18位重量+金额码
  weightAndTotal,
  ///1=18位金额+重量码
  totalAndWeight,
  ///2=13位重量码
  onlyWeight,
  ///3=13位金额码
  onlyTotal
}
```

### 5.2 条码解析逻辑

**文件路径**: `lib/bill/bill/bill_edit_sale_normal.dart`

#### 5.2.1 条码格式说明
```
F：标识位 W:商品代码 E：金额 N：重量 C：正校验码 O：反校验码
条码配置位：
- 18位重量+金额码：1F+6W+5N+5E+C(O)；2F+5W+5N+5E+C(O）
- 18位金额+重量码：1F+6W+5E+5N+C(O)；2F+5W+5E+5N+C(O)
- 13位金额码：1F+6W+5E+C(O）；2F+5W+5E+C(O）
- 13位重量码：1F+6W+5N+C(O）；2F+5W+5N+C(O）
```

#### 5.2.2 解析实现
```dart
BarcodeScaleScan? pTypeBarCodeScale(String scanMessage) {
  // 验证条码长度和配置
  if (!enableBarcodeScale || null == barcodeScaleConfig ||
      (BarcodeScaleTypeTool.isBarcodeScale13(barcodeScaleConfig?.outputFormatType) &&
       (searchString.length != 13 && searchString.length != 12)) ||
      (BarcodeScaleTypeTool.isBarcodeScale18(barcodeScaleConfig?.outputFormatType) &&
       searchString.length != 18 && searchString.length != 17)) {
    return null;
  }

  BarcodeScaleScan barcodeScaleScan = BarcodeScaleScan();
  searchString = StringUtil.trim(searchString);

  // 兼容部分扫码设备首位为0时被自动截断
  if (searchString.length == 12 || searchString.length == 17) {
    searchString = "0$searchString";
  }

  // 标识位解析（F）
  barcodeScaleScan.markPosition = searchString.substring(
    0, barcodeScaleConfig?.markDigit ?? 1);

  // 商品代码解析（W）
  barcodeScaleScan.pTypePosition = StringUtil.trimLeft(
    searchString.substring(barcodeScaleScan.markPosition.length, 7),
    trimStr: "0");

  // 第三部分解析（金额或重量）
  String thirdPart = StringUtil.trimLeft(
    searchString.substring(7, 12), trimStr: "0");

  // 第四部分解析（仅18位条码支持）
  String fourPart = "";
  if (BarcodeScaleTypeTool.isBarcodeScale18(barcodeScaleConfig?.outputFormatType) &&
      searchString.length == 18) {
    fourPart = StringUtil.trimLeft(
      searchString.substring(12, searchString.length - 1), trimStr: "0");
  }

  // 根据配置解析金额和重量位置
  if (BarcodeScaleTypeTool.isFirstTotal(barcodeScaleConfig?.outputFormatType)) {
    barcodeScaleScan.totalPosition = handleDigital(thirdPart, barcodeScaleConfig?.totalDigit ?? 0);
    barcodeScaleScan.weightPosition = handleDigital(fourPart, barcodeScaleConfig?.weightDigit ?? 0);
  } else {
    barcodeScaleScan.weightPosition = handleDigital(thirdPart, barcodeScaleConfig?.weightDigit ?? 0);
    barcodeScaleScan.totalPosition = handleDigital(fourPart, barcodeScaleConfig?.totalDigit ?? 0);
  }

  return barcodeScaleScan;
}
```

### 5.3 业务处理模式

**文件路径**: `lib/bill/widget/mixin/sale_business_mixin.dart`

#### 5.3.1 仅金额模式
```dart
if (barcodeScaleConfig.outputFormatType == BarcodeScaleTypeEnum.onlyTotal.index) {
  // 金额码解析和零售价配置验证
  if (!checkTotalValid(details, detailDto, barcodeScaleScan)) {
    return;
  }

  // 通过金额/单价计算数量
  String qty = SystemConfigTool.doubleDivision(
    barcodeScaleScan.totalPosition,
    detailDto.currencyPrice.toString(),
    BillDecimalType.QTY,
  );

  BillPriceComputeHelper().priceCompute.onValueChange(
    detailDto, PtypePopValueChangeType.Qty, qty);
  addGoodsDetails(details);
}
```

#### 5.3.2 仅重量模式
```dart
else if (barcodeScaleConfig.outputFormatType == BarcodeScaleTypeEnum.onlyWeight.index) {
  // 重量码解析和商品重量配置验证
  if (!checkWeightValid(details, detailDto, barcodeScaleScan)) {
    return;
  }

  // 通过重量/商品单重计算数量
  String qty = SystemConfigTool.doubleDivision(
    barcodeScaleScan.weightPosition,
    detailDto.weight.toString(),
    BillDecimalType.QTY,
  );

  BillPriceComputeHelper().priceCompute.onValueChange(
    detailDto, PtypePopValueChangeType.Qty, qty.toString());
  addGoodsDetails(details);
}
```

#### 5.3.3 金额+重量模式
```dart
else if (barcodeScaleConfig.outputFormatType == BarcodeScaleTypeEnum.weightAndTotal.index ||
         barcodeScaleConfig.outputFormatType == BarcodeScaleTypeEnum.totalAndWeight.index) {
  // 同时验证重量和金额的有效性
  if (!checkWeightValid(details, detailDto, barcodeScaleScan) ||
      !checkTotalValid(details, detailDto, barcodeScaleScan)) {
    return;
  }

  // 计算条码秤中配置的零售价
  num barcodeScalePrice = SystemConfigTool.divisionToDecimalFromStr(
    barcodeScaleScan.totalPosition,
    barcodeScaleScan.weightPosition,
    BillDecimalType.PRICE,
  );

  // 计算数量
  num qty = SystemConfigTool.doubleDivisionToDecimal(
    num.parse(barcodeScaleScan.weightPosition),
    detailDto.weight!,
    BillDecimalType.QTY,
  );

  // 验证价格一致性并应用
  // ...
}
```

## 6. 统一工具类和状态管理

### 6.1 ScaleTool统一接口

**文件路径**: `lib/bill/tool/scale_tool.dart`

#### 6.1.1 电子秤初始化
```dart
class ScaleTool {
  static void addScale(BuildContext context, ScaleController scaleController) {
    if (SpTool.getSetting().innerScale) {
      // 使用内置电子秤
      ScalePlugin.instance.init();
    } else {
      // 使用第三方电子秤
      if (SpTool.getSetting().thirdPartyScale != null) {
        ThirdPartyScalePlugin.instance.connectDevice(
            context,
            SpTool.getSetting().thirdPartyScale ??
                const SimpleUsbDevice(0, 0, "", ""));

        // 设置超时检测
        timer ??= Timer.periodic(const Duration(seconds: 1), (Timer value) {
          timerCount += 1;
          if (timerCount >= 2) {
            scaleController.changeWeight(0, 0, false);
            scaleController.onConnection(false);
          }
        });
      }
    }
  }
}
```

#### 6.1.2 重量变化监听
```dart
static void onWeight(ScaleController scaleController) {
  if (SpTool.getSetting().innerScale) {
    // 内置电子秤重量监听
    ScalePlugin.instance.onWeightChange =
        ({required int weight, required int tare, required bool isStable}) {
      double weight = ScalePlugin.instance.weight.toDouble() / 1000;
      double tare = ScalePlugin.instance.tare.toDouble() / 1000;
      bool isStable = ScalePlugin.instance.isStable;
      scaleController.changeWeight(weight, tare, isStable);
    };
  } else {
    // 第三方电子秤重量监听
    ThirdPartyScalePlugin.instance.onReceiveStableWeight = (value) {
      var weight = value.toDouble() / 1000;
      scaleController.changeWeight(weight, 0, true);
      scaleController.onConnection(true);
      timerCount = 0;
    };
  }
}
```

#### 6.1.3 数量自动计算
```dart
static void calculateUnityQty(GoodsDetailDto goodsDetailDto) {
  SettingDto settingDto = SpTool.getSetting();
  if (goodsDetailDto.weight == null ||
      goodsDetailDto.weight == 0 ||
      !settingDto.openFreshWight) {
    return;
  }

  double getWeight = 0;
  if (SpTool.getSetting().innerScale) {
    if (ScalePlugin.instance.weight.toDouble() <= 0) {
      return;
    } else {
      getWeight = ScalePlugin.instance.weight.toDouble();
    }
  } else {
    if (ThirdPartyScalePlugin.instance.weight.toDouble() <= 0) {
      return;
    } else {
      getWeight = ThirdPartyScalePlugin.instance.weight.toDouble();
    }
  }

  // 单位转换和数量计算
  int weightUnity = goodsDetailDto.weightUnit ?? 0;
  num weight = goodsDetailDto.weight ?? 0;

  // 如果单位是千克，换算成g
  if (weightUnity == 1) {
    weight = weight * 1000;
  }

  // 计算数量：电子秤重量 / 商品单重
  num qty = MathUtil.divide(getWeight, weight);

  // 应用计算结果
  BillPriceComputeHelper().priceCompute.onValueChange(
    goodsDetailDto,
    PtypePopValueChangeType.Qty,
    SystemConfigTool.formatDecimal(qty, BillDecimalType.QTY),
  );
}
```

### 6.2 ScaleController状态管理

#### 6.2.1 状态定义
```dart
class ScaleController extends GetxController {
  ///是否连接电子秤服务
  var isConnected = false.obs;

  ///净重 g，不包含皮重
  var weight = 0.000.obs;

  ///皮重 g
  var tare = 0.000.obs;

  ///是否稳定
  var isStable = false.obs;

  ///秤是否过轻(小于20E)
  var isLightWeight = false.obs;

  ///秤是否过载
  var overload = false.obs;

  ///秤是否清零错误
  var clearZeroErr = false.obs;

  ///秤是否标定错误
  var calibrationErr = false.obs;
}
```

#### 6.2.2 状态更新
```dart
void changeWeight(double weight, double tare, bool isStable) {
  ///单位转换为kg
  this.weight = RxDouble(weight);
  this.tare = RxDouble(tare);
  this.isStable = RxBool(isStable);
  update([ScaleTool.weight]);
  update([ScaleTool.state]);
}

void onConnection(bool isConnected) {
  this.isConnected = RxBool(isConnected);
  update([ScaleTool.connection]);
}

void onStatus({
  required bool isLightWeight,
  required bool overload,
  required bool clearZeroErr,
  required bool calibrationErr,
}) {
  this.isLightWeight = RxBool(isLightWeight);
  this.overload = RxBool(overload);
  this.clearZeroErr = RxBool(clearZeroErr);
  this.calibrationErr = RxBool(calibrationErr);
  update([ScaleTool.status]);
}
```

## 7. 业务集成应用

### 7.1 生鲜称重界面

**文件路径**: `lib/bill/bill/bill_edit_sale_fresh.dart`

#### 7.1.1 界面布局
```dart
//电子秤称重数据显示区域
HaloContainer(
  visible: SpTool.getSetting().openFreshWight,
  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
  color: AppColors.weightBackgroundFontColor,
  children: [
    _buildWeight(),              // 重量显示
    Container(
      padding: EdgeInsets.only(top: 18.h),
      child: HaloPosLabel(
        "kg",
        textStyle: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold),
      ),
    ),
    const Expanded(child: SizedBox()),
    HaloContainer(
      direction: Axis.vertical,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildScaleState(),        // 连接状态显示
        _buildScaleWeightState()   // 稳定状态显示
      ],
    )
  ],
)
```

#### 7.1.2 重量显示组件
```dart
_buildWeight() {
  return GetBuilder<ScaleController>(
    id: ScaleTool.weight,
    tag: ScaleTool.scaleTag,
    init: scaleController,
    builder: (_) {
      return Obx(
        () => HaloPosLabel(
          "${scaleController.weight}",
          textStyle: TextStyle(fontSize: 70.sp, fontWeight: FontWeight.normal),
        ),
      );
    },
  );
}
```

#### 7.1.3 状态指示器
```dart
_buildScaleState() {
  return GetBuilder<ScaleController>(
    id: ScaleTool.state,
    tag: ScaleTool.scaleTag,
    init: scaleController,
    builder: (_) {
      return Obx(() {
        bool connected = scaleController.isConnected.value;
        String title = connected ? "已连接" : "未连接";
        Color color = connected ? Colors.green : Colors.red;
        return _buildIndicatorLightView(title, color);
      });
    },
  );
}

_buildScaleWeightState() {
  return GetBuilder<ScaleController>(
    id: ScaleTool.state,
    tag: ScaleTool.scaleTag,
    init: scaleController,
    builder: (_) {
      return Obx(() {
        bool stable = scaleController.isStable.value;
        String title = stable ? "稳定" : "浮动";
        Color color = stable ? Colors.green : Colors.yellow;
        return _buildIndicatorLightView(title, color);
      });
    },
  );
}
```

### 7.2 销售开单集成

#### 7.2.1 商品扫描处理
```dart
@override
Widget buildRightBody(BuildContext context) {
  return BillGoodsAndKeyboardWidget(
      searchByPtype: SpTool.getSetting().searchByPtype,
      focusNode: searchFocusNode,
      goodsClickCallback: ((goods) {
        ScanTool.handleScanResult(context, goods, goodsList, billType)
            .then((list) {
          if (list != null) {
            String vchType = BillTypeData[billType]!;
            for (var element in list) {
              element.vchtype = vchType;
              if (element.comboRow == false &&
                  !BillTool.comboDetailRow(element)) {
                // 自动计算生鲜商品数量
                ScaleTool.calculateUnityQty(element);
              }
            }
            handleSearchResult(list);
          }
        });
      }));
}
```

#### 7.2.2 条码扫描处理
```dart
@override
void doScanCode(
  String text, {
  BarcodeScaleScan? barcodeScaleScan,
  BarcodeScaleConfig? barcodeScaleConfig,
}) {
  searchString = text;

  ///条码秤解析，未开启不会解析，内部处理
  BarcodeScaleScan? barcodeScaleScan = pTypeBarCodeScale(text);
  super.doScanCode(
    searchString,
    barcodeScaleScan: barcodeScaleScan,
    barcodeScaleConfig: this.barcodeScaleConfig,
  );
}
```

## 8. 平台适配

### 8.1 Android平台

#### 8.1.1 插件注册
```kotlin
// MainActivity.kt
class MainActivity: FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // 注册电子秤插件
        flutterEngine.plugins.add(ScalePlugin())
        flutterEngine.plugins.add(ThirdPartyScalePlugin())
    }
}
```

#### 8.1.2 权限配置
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.USB_PERMISSION" />
<uses-feature android:name="android.hardware.usb.host" />

<intent-filter>
    <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
</intent-filter>
<meta-data android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
           android:resource="@xml/device_filter" />
```

### 8.2 Windows平台

#### 8.2.1 依赖配置
```yaml
# pubspec.yaml
dependencies:
  quick_usb: ^0.4.1

dev_dependencies:
  quick_usb_windows: ^0.4.1
```

#### 8.2.2 插件注册
```cpp
// windows/flutter/generated_plugin_registrant.cc
void RegisterPlugins(flutter::PluginRegistry* registry) {
  QuickUsbPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("QuickUsbPlugin"));
}
```

## 9. 配置管理

### 9.1 电子秤设置界面

**文件路径**: `lib/settting/widget/normal_setting_page.dart`

#### 9.1.1 内置/外接切换
```dart
///内置电子秤开关
Widget _buildSwitch() {
  return buildRow([
    buildText("是否使用内置电子秤", style: _style),
    HaloPosSwitch(
      width: 73.w,
      height: 40.h,
      value: _innerScale,
      onChanged: (value) => setState(() {
        _innerScale = value;
        _saveIsOpen();
        if (!_innerScale) {
          connectDevice(_thirdPartyScale);
        }
      }),
    ),
  ]);
}
```

#### 9.1.2 外接设备选择
```dart
///电子秤选择
Widget _buildDeviceSelector() {
  return buildRow([
    Text("选择外接电子秤", style: _style),
    GestureDetector(
      onTap: () async {
        List<SimpleUsbDevice> list =
            await ThirdPartyScalePlugin.instance.getDevices();
        if (context.mounted) {
          SimpleUsbDevice? dto = await showDialog<SimpleUsbDevice>(
            context: context,
            builder: (context) => SingleSelectDialog<SimpleUsbDevice>(
                title: "请选择外接电子秤",
                list: list,
                contentGetter: (device) => device.productName),
          );
          if (dto != null) {
            setState(() {
              _thirdPartyScale = dto;
              _saveScale();
            });
            connectDevice(dto);
          }
        }
      },
      child: IconFont(IconNames.shezhi, size: 40.w),
    )
  ]);
}
```

### 9.2 配置存储

#### 9.2.1 设置保存
```dart
///保存电子秤类型
void _saveIsOpen() {
  final setting = SpTool.getSetting();
  setting.innerScale = _innerScale;
  SpTool.saveSetting(setting);
}

///保存选中的三方电子秤
void _saveScale() async {
  final setting = SpTool.getSetting();
  setting.thirdPartyScale = _thirdPartyScale;
  SpTool.saveSetting(setting);
}
```

#### 9.2.2 配置读取
```dart
///从本地存储读取，并且初始化
void _initFromStore() {
  final setting = SpTool.getSetting();
  setState(() {
    _innerScale = setting.innerScale;
    _thirdPartyScale = setting.thirdPartyScale;
  });
}
```

## 10. 技术特点

### 10.1 架构优势

| 特点 | 说明 | 优势 |
|------|------|------|
| **多平台支持** | Android、Windows平台适配 | 跨平台兼容性强 |
| **多设备兼容** | 内置、USB外接、条码秤 | 满足不同硬件需求 |
| **统一接口** | ScaleTool提供一致的API | 降低业务层复杂度 |
| **响应式管理** | 使用GetX进行状态管理 | 实时UI更新 |
| **插件化设计** | 模块化的插件架构 | 易于扩展和维护 |

### 10.2 功能完善性

#### 10.2.1 实时数据监控
- **重量监控**: 实时显示净重、皮重
- **状态监控**: 连接状态、稳定状态、错误状态
- **自动计算**: 根据重量自动计算商品数量

#### 10.2.2 错误处理机制
- **连接异常**: 自动重连、超时检测
- **设备异常**: 过载、过轻、标定错误检测
- **权限管理**: USB设备权限申请和管理

#### 10.2.3 业务集成深度
- **销售流程**: 与开单流程无缝集成
- **生鲜称重**: 专门的生鲜称重界面
- **条码解析**: 支持多种条码秤格式

### 10.3 数据处理精度

#### 10.3.1 单位转换
```dart
// 重量单位统一处理
double weight = rawWeight.toDouble() / 1000;  // g转kg
int weightUnity = goodsDetailDto.weightUnit ?? 0;
if (weightUnity == 1) {
  weight = weight * 1000;  // kg转g
}
```

#### 10.3.2 精度控制
```dart
// 小数位数控制
String qty = SystemConfigTool.formatDecimal(
  calculatedQty,
  BillDecimalType.QTY
);
```

#### 10.3.3 计算准确性
```dart
// 数量计算：重量/商品单重
num qty = MathUtil.divide(scaleWeight, goodsWeight);

// 金额计算：数量*单价
num total = MathUtil.multiply(qty, unitPrice);
```

### 10.4 扩展性设计

#### 10.4.1 新设备支持
- 通过插件接口可轻松添加新的电子秤设备
- 统一的数据格式和回调机制
- 平台特定的实现可独立开发

#### 10.4.2 新功能扩展
- 条码秤格式可通过配置扩展
- 业务逻辑可通过工具类扩展
- UI组件可通过状态管理扩展

#### 10.4.3 配置灵活性
- 支持运行时切换电子秤类型
- 支持多种条码秤格式配置
- 支持精度和单位配置

---

## 总结

POS系统的电子秤功能通过精心设计的架构，实现了对多种电子秤设备的统一支持。从内置的商米电子秤到第三方USB电子秤，再到条码秤的软件解析，系统提供了完整的称重解决方案。

**核心优势**:
1. **统一的接口设计**: 通过ScaleTool提供一致的API，屏蔽了不同设备的差异
2. **响应式状态管理**: 使用GetX实现实时的UI更新和状态同步
3. **跨平台支持**: Android和Windows平台的完整适配
4. **深度业务集成**: 与销售流程的无缝集成，自动化的数量计算
5. **完善的错误处理**: 全面的异常检测和处理机制

这套电子秤功能不仅满足了当前的业务需求，也为未来的功能扩展和设备支持奠定了坚实的基础。
```