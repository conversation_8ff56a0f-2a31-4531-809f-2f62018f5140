# android打包

* 打包
* 加固
* zipalign对齐
* apksigner签名
* 对齐和签名可使用脚本

## 打包

* 输入命令
  ```shell
  flutter build apk
  ```
* 在```/build/app/outputs/flutter/app-release.apk```找到打好的apk包

## 加固

* 使用**360加固助手**对apk进行加固，加固后，**apk包会失去签名**，所以需要进行**重签名**，而签名之前，需要对apk进行
  **对齐**处理，避免部分设备无法使用此apk。
* **对于加固和签名，可以使用我写好的脚本进行操作**，使用之前，请用文本编辑器打开脚本，配置其中的各项参数和路径。

## 对齐

* 命令如下

  ```shell
  zipalign -p -f -v 4 [待对齐.apk] [已对齐.apk]
  ```

* 只需要注意```待对齐.apk```就是加固之后的apk包，```已对齐.apk```就是对齐之后的产物，可自行改任意命名。
  例如：

  ```shell
  zipalign -p -f -v 4 unalign.apk unsigned.apk
  ```

* **对齐必须在签名之前**

## 签名

* 命令如下

  ```shell
  apksigner sign --ks [keyStore文件] --ks-pass pass:[keystore密码] --ks-key-alias [alias别名] --key-pass pass:[alias密码] --out [已签名.apk] [待签名.apk]
  ```

  其中，```keyStore文件```和密码、别名等位于```/android/app/```中，**keyStore**为```haloPos.jks```,**密码别名
  **位于```签名信息.md```中。对于此项目，```keyStore密码```和```alias密码```是一样的。
  例如:

  ```shell
  apksigner sign --ks haloPos.jks --ks-pass pass:"grasp#123" --ks-key-alias "wsgjp.com" --key-pass pass:"grasp#123" --out signed.apk unsigned.apk
  ```
