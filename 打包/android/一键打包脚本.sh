#!/bin/bash
# 回车默认脚本中的路径，第一次使用执行命令后手工录入，会重写脚本路径
# 输出路径 请放入haloPos.jks
# 如果没有权限，请使用命令"chmod +x 此脚本路径"
# 如果给变量设置路径需要用到当前用户文件夹
# 例如 a=~/Desktop，请勿使用双引号包裹，例如 a="~/Desktop"，这样会导致 ~ 符号无法被转义识别

# --- 确保使用 UTF-8 编码环境 ---
# 这有助于正确处理包含中文字符的路径，尤其是在 sed 命令中
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8
# 你也可以尝试使用你的中文 locale，例如 export LANG=zh_CN.UTF-8
# 如果不确定你的系统支持哪些 locale，可以运行命令 'locale -a' 查看

# --- 默认配置 (这些值可能会在脚本运行时被修改) ---
# Flutter 项目路径的默认值
default_flutter_project_path=~/工作/halo_pos_new
# 输出路径的默认值
default_output_path=~/工作/打包目录
# 加固后 APK 的默认存放目录（360加固助手输出目录示例）
# 脚本会在此目录下查找加固后的文件，文件名会根据版本号动态生成
default_jiagu_output_base_path=~/Library/Containers/com.360jiaguzhushou.jgb/Data/Document/jiagu.bundle/Contents/Resources/jiagu/output/<EMAIL>

# Android 工具集路径，一般位于 sdk/build-tools/版本号/
build_tools_path=~/Library/Android/sdk/build-tools/31.0.0
# apkSigner 路径，一般位于 sdk/build-tools/版本号/apksigner，若配置了环境变量则无需填写
apksigner_path=$build_tools_path/apksigner
# zipalign 路径，一般位于 sdk/build-tools/版本号/zipalign，若配置了环境变量则无需填写
zipalign_path=$build_tools_path/zipalign
# aapt 路径，一般位于 sdk/build-tools/版本号/aapt，若配置了环境变量则无需填写
aapt_path=$build_tools_path/aapt

# 签名别名
key_alias="wsgjp.com"
# 签名密码
password="grasp#123"

# --- 变量，将根据用户输入或默认值确定 ---
# 这些变量将在获取用户输入后被赋值
flutter_project_path=""
output_path=""
jiagu_output_base_path="" # 用于存放加固后APK的目录
keystore_path="" # keyStore 路径，将根据 output_path 确定

# --- 依赖于上面变量的路径，将在它们确定后计算 ---
apk_output_path="" # Flutter 打包输出的 APK 路径
unsigned_apk_path="" # 对齐后未签名的 apk 路径
unalign_apk_path="" # 加固之后，待对齐和签名的 apk 路径（用户手动放置）

# 获取当前脚本的路径
script_path="$0"

# 检查文件或命令是否存在
# 参数 ${1} 要检测的路径或命令
function check_exist() {
    if [ -z "$1" ]; then
        return 1
    elif [ -f "$1" ] || command -v "$1" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

version_name="unknown"
version_name_no_dot="unknown"

# 获取版本号
function get_version_name() {
    # 确保 apk_output_path 已经设置
    if [ -z "$apk_output_path" ]; then
        echo "错误：APK 输出路径未设置，无法提取版本号。"
        version_name="unknown"
        version_name_no_dot="unknown"
        return 1
    fi

    if check_exist "$aapt_path"; then
        version_name=$("$aapt_path" dump badging "$apk_output_path" 2>/dev/null | grep versionName | awk -F"'" '{print $6}')
        if [ -z "$version_name" ]; then
            echo "无法提取版本号，使用默认值 'unknown'"
            version_name="unknown"
            version_name_no_dot="unknown"
        else
            # 保留原始版本号用于最终签名文件名
            echo "提取版本号成功：$version_name"
            # 移除版本号中的点（.）用于加固路径
            version_name_no_dot=$(echo "$version_name" | sed 's/\.//g')
        fi
    else
        echo "警告：aapt 工具不可用，无法提取版本号，但不影响对齐和签名流程"
    fi
}

# --- 获取用户输入或使用默认值，并更新脚本文件 ---

# 获取 Flutter 项目路径
read -p "请输入 Flutter 项目路径 (回车默认为 $default_flutter_project_path): " user_flutter_project_path
if [ -n "$user_flutter_project_path" ]; then
    flutter_project_path="$user_flutter_project_path"
    # 使用 sed 修改脚本文件中的默认值
    # 使用捕获组来匹配变量名和等号，只替换后面的值
    # 使用 | 作为分隔符，以防路径中包含 /
    # 添加 '' 到 -i 后面以兼容 macOS 的 BSD sed
    sed -i '' "s|^\(default_flutter_project_path=\).*$|\1$flutter_project_path|" "$script_path"
    echo "已更新脚本中的默认 Flutter 项目路径为: $flutter_project_path"
else
    flutter_project_path="$default_flutter_project_path"
    echo "使用默认 Flutter 项目路径: $flutter_project_path"
fi


# 获取输出路径
read -p "请输入输出路径 (回车默认为 $default_output_path): " user_output_path
if [ -n "$user_output_path" ]; then
    output_path="$user_output_path"
    # 使用 sed 修改脚本文件中的默认值
    # 使用捕获组来匹配变量名和等号，只替换后面的值
    # 添加 '' 到 -i 后面以兼容 macOS 的 BSD sed
    sed -i '' "s|^\(default_output_path=\).*$|\1$output_path|" "$script_path"
    echo "已更新脚本中的默认输出路径为: $output_path"
else
    output_path="$default_output_path"
    echo "使用默认输出路径: $output_path"
fi

# 获取加固后 APK 存放目录
read -p "请输入加固后 APK 的存放目录 (回车默认为 $default_jiagu_output_base_path): " user_jiagu_output_base_path
if [ -n "$user_jiagu_output_base_path" ]; then
    jiagu_output_base_path="$user_jiagu_output_base_path"
    # 使用 sed 修改脚本文件中的默认值
    # 使用捕获组来匹配变量名和等号，只替换后面的值
    # 添加 '' 到 -i 后面以兼容 macOS 的 BSD sed
    sed -i '' "s|^\(default_jiagu_output_base_path=\).*$|\1$jiagu_output_base_path|" "$script_path"
    echo "已更新脚本中的默认加固后 APK 存放目录为: $jiagu_output_base_path"
else
    jiagu_output_base_path="$default_jiagu_output_base_path"
    echo "使用默认加固后 APK 存放目录: $jiagu_output_base_path"
fi


# --- 根据确定的路径更新依赖变量 ---
apk_output_path="$flutter_project_path/build/app/outputs/flutter-apk/app-release.apk"
unsigned_apk_path="$output_path/unsigned.apk"
keystore_path="$output_path/haloPos.jks" # keyStore 路径现在根据确定的 output_path 设置

# 步骤 1：检查工具路径，优先使用脚本中的路径，若无效则使用环境变量 $PATH
if ! check_exist "$apksigner_path"; then
    apksigner_path="apksigner"
    if ! check_exist "$apksigner_path"; then
        echo "错误：apksigner 路径异常，请将其配置到 \$PATH 中或修改脚本中的路径"
        exit 1
    fi
    echo "使用环境变量中的 apksigner 命令"
fi

if ! check_exist "$zipalign_path"; then
    zipalign_path="zipalign"
    if ! check_exist "$zipalign_path"; then
        echo "错误：zipalign 路径异常，请将其配置到 \$PATH 中或修改脚本中的路径"
        exit 1
    fi
    echo "使用环境变量中的 zipalign 命令"
fi

if ! check_exist "$aapt_path"; then
    aapt_path="aapt"
    if ! check_exist "$aapt_path"; then
        echo "警告：aapt 工具不可用，无法提取版本号，但不影响对齐和签名流程"
    fi
    echo "使用环境变量中的 aapt 命令（如果可用）"
fi

# 步骤 2：检查其他配置
if [ ! -d "$flutter_project_path" ]; then
    echo "错误：Flutter 项目路径不存在，请检查路径：$flutter_project_path"
    exit 1
fi

# 检查输出目录并创建
if [ -z "$output_path" ]; then
    echo "错误：输出路径为空，请在脚本中配置或手动输入"
    exit 1
else
    if [ ! -d "$output_path" ]; then
        mkdir -p "$output_path"
        echo "已创建输出目录：$output_path"
    fi
fi

# 检查 keystore 文件 (现在使用根据 output_path 确定的 keystore_path)
if [ ! -f "$keystore_path" ]; then
    echo "错误：keyStore 文件不存在，请检查路径：$keystore_path"
    exit 1
fi

if [ -z "$key_alias" ]; then
    echo "错误：keyAlias 为空，请在脚本中配置"
    exit 1
fi

if [ -z "$password" ]; then
    echo "错误：password 为空，请在脚本中配置"
    exit 1
fi


# 步骤 3：切换到 Flutter 项目目录并打包 APK
cd "$flutter_project_path" || { echo "错误：无法切换到项目目录 $flutter_project_path"; exit 1; } # 添加错误检查
echo "开始打包 Flutter APK..."
flutter build apk --release
if [ $? -ne 0 ]; then
    echo "错误：Flutter 打包失败，请检查错误信息"
    exit 1
fi
echo "打包成功，APK 路径：$apk_output_path"

# 步骤 4：提示用户手动加固 APK，并自动打开文件夹
echo "请使用 360 加固助手对以下 APK 进行加固：$apk_output_path"
# 自动打开 APK 所在文件夹
apk_output_dir=$(dirname "$apk_output_path")
echo "正在打开文件夹：$apk_output_dir"
# 使用 open 命令打开文件夹 (适用于 macOS)
# 如果在 Linux 上，可以使用 xdg-open "$apk_output_dir"
# 如果在 Windows (Git Bash/WSL) 上，可以使用 explorer.exe "$apk_output_dir" 或 start "$apk_output_dir"
open "$apk_output_dir" 2>/dev/null || xdg-open "$apk_output_dir" 2>/dev/null || explorer.exe "$apk_output_dir" # 尝试多种打开方式

# 提前获取版本号以用于文件名
get_version_name

# 动态生成加固后 APK 的完整路径，使用用户输入的或默认的加固输出目录和去掉点的版本号
# 注意：这里假设加固后的文件名格式是固定的，如果加固工具输出的文件名格式不同，需要修改这里
unalign_apk_path="$jiagu_output_base_path/app-release_${version_name_no_dot}_jiagu.apk"

echo "请将打开的文件夹的APK文件拖入到360加固："
read -p "加固完成后，请按 Enter 继续..."

# 检查加固后的文件是否存在
if [ ! -f "$unalign_apk_path" ]; then
    echo "错误：未找到加固后的 APK 文件：$unalign_apk_path"
    echo "请确认文件已放置到指定路径，并且文件名格式正确。"
    exit 1
fi
echo "检测到加固后的文件：$unalign_apk_path"

# 步骤 5：对齐 APK
echo "开始对齐 APK：$unalign_apk_path"
"$zipalign_path" -p -f -v 4 "$unalign_apk_path" "$unsigned_apk_path"
if [ $? -ne 0 ] || [ ! -f "$unsigned_apk_path" ]; then
    echo "错误：对齐失败，请检查 zipalign 工具或输入文件"
    exit 1
fi
echo "对齐成功，输出文件：$unsigned_apk_path"

# 步骤 6：签名 APK
signed_apk_path="$output_path/halopos_$version_name.apk"
echo "开始签名 APK，输出文件：$signed_apk_path"
"$apksigner_path" sign --ks "$keystore_path" --ks-pass pass:"$password" --ks-key-alias "$key_alias" --key-pass pass:"$password" --out "$signed_apk_path" "$unsigned_apk_path"
if [ $? -ne 0 ] || [ ! -f "$signed_apk_path" ]; then
    echo "错误：签名失败，请检查 keystore 文件、密码或别名是否正确"
    exit 1
fi
echo "签名成功，最终文件：$signed_apk_path"

# 步骤 7：清理中间文件
echo "开始清理中间文件..."
rm -f "$unsigned_apk_path"
# 注意：这里不再自动删除加固后的原始文件 ($unalign_apk_path)，因为用户可能想保留它。
# 如果需要自动删除，可以取消下一行的注释
# rm -f "$unalign_apk_path"
echo "中间文件清理完成"

echo "所有步骤完成！最终 APK 位于：$signed_apk_path"

# 步骤 8：自动打开最终 APK 所在目录
final_apk_dir=$(dirname "$signed_apk_path")
echo "正在打开最终 APK 所在目录：$final_apk_dir"
# 使用 open 命令打开文件夹 (适用于 macOS)
# 如果在 Linux 上，可以使用 xdg-open "$final_apk_dir"
# 如果在 Windows (Git Bash/WSL) 上，可以使用 explorer.exe "$final_apk_dir" 或 start "$final_apk_dir"
open "$final_apk_dir" 2>/dev/null || xdg-open "$final_apk_dir" 2>/dev/null || explorer.exe "$final_apk_dir" # 尝试多种打开方式

exit 0
