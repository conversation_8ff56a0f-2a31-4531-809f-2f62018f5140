#!/bin/bash
# 如果没有权限，请使用命令"chmod +X 此脚本路径"
# 如果给变量设置路径需要用到当前用户文件夹
# 例如 a=~/Desktop ,请勿使用双引号包裹,例如a="~/Desktop",这样会导致~符号无法被转义识别

#android工具集路径，一般位于 sdk/build-tools/版本号/
build_tools_path=~/Library/Android/sdk/build-tools/32.0.0
#apkSigner路径，一般位于sdk/build-tools/版本号/apksigner,若配置了环境变量则无需填写
apksigner_path=$build_tools_path/apksigner
#apkSigner路径，一般位于sdk/build-tools/版本号/zipalign，若配置了环境变量则无需填写
zipalign_path=$build_tools_path/zipalign
#aapt路径，一般位于sdk/build-tools/版本号/aapt，若配置了环境变量则无需填写
aapt_path=$build_tools_path/aapt
#加固之后，待对齐和签名的apk路径，必填
unalign_apk_path=~/Desktop/unalign.apk
#签名keyStore路径,必填
keystore_path=~/Desktop/haloPos.jks
#签名别名
key_alias="wsgjp.com"
#签名密码
password="grasp#123"

#输出路径，无需更改
output_path=~/Desktop/signOutput
unsigned_apk_path=$output_path/unsigned.apk


#检查路径是否存在
#参数${1} 要检测的路径
function check_exist() {
if test -z $1;
then 
    return 1
elif command -v $1  >/dev/null 2>&1;
then    
    return 0
else
    return 1
fi
}

version_name=

#获取版本号
function get_version_name() {
    version_name=$($aapt_path dump badging $unalign_apk_path | grep versionName | awk -F"'" '{print $6}')
    echo $version_name
}

#优先使用脚本中的路径，若该路径是错误的，则使用环境变量$PATH中配置的
if (! $(check_exist $apksigner_path));
then
    apksigner_path="apksigner";
    if (! $(check_exist $apksigner_path));
    then
        echo "apksigner路径异常,将其配置到\$PATH中或者此脚本中"
        exit
    fi
fi

if (! $(check_exist $zipalign_path));
then
    zipalign_path="zipalign";
    if (! $(check_exist $zipalign_path));
    then
        echo "zipalign路径异常,将其配置到\$PATH中或者此脚本中"
        exit
    fi
fi

if (! $(check_exist $aapt_path));
then
    aapt_path="aapt";
    if (! $(check_exist $aapt_path));
    then
        echo "aapt路径异常,将其配置到\$PATH中或者此脚本中"
        exit
    fi
fi

if [ ! -f $keystore_path ];
then
    echo "keyStore文件不存在,请检查路径"
    exit
fi

if [ -z $key_alias ];
then
    echo "keyAlias为空,请检查"
    exit
fi

if [ -z $password ];
then
    echo "password为空,请检查"
    exit
fi

if [ ! -f $unalign_apk_path ];
then
    echo "源文件apk不存在，请检查"
    exit
fi

if [ -z $output_path ];
then
    echo "输出路径为空,请检查"
    exit
else
    if [ ! -d $output_path ];
    then
        mkdir $output_path
    fi
fi

#  zipalign -p -f -v 4 unalign.apk unsigned.apk
#  apksigner sign --ks haloPos.jks --ks-pass pass:"grasp#123" --ks-key-alias "wsgjp.com" --key-pass pass:"grasp#123" --out signed.apk unsigned.apk

#对齐
$zipalign_path -p -f -v 4 $unalign_apk_path $unsigned_apk_path
if [ ! -f $unsigned_apk_path ];
then
    echo "对齐失败"
    exit
fi
#签名
get_version_name
signed_apk_path="$output_path/halopos_$version_name.apk"
$apksigner_path sign --ks $keystore_path --ks-pass pass:$password --ks-key-alias $key_alias --key-pass pass:$password --out $signed_apk_path $unsigned_apk_path

if [ ! -f $signed_apk_path ];
then
    echo "签名失败"
else
    echo "签名成功"
fi

rm -rf $unsigned_apk_path

exit