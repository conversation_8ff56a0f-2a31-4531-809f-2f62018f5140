# Windows打包
* 修改版本号
* 构建Flutter 
* 将VC Runtime和脚本复制到构建出来的Flutter包中
* 修改Inno Setup的打包脚本

## 修改版本号
![修改版本号](image/更改版本号.png)
如图，找到```./windows/runner/Runner.rc```,修改对应处版本号

## 构建Flutter
* 使用Terminal，对应Android Studio快捷键（Alt+F12）
* 确保当前路径在项目根目录
* 输入```Flutter build windows```进行构建
* 输出路径在```./build/windows/windows/runner/Release```
  ![Flutter构建输出路径](image/Flutter构建输出路径.png)
  
  该***Release***文件夹即为构建出来的项目，其中包含一个exe启动文件，以及各插件dll、资源文件。

## 将VC Runtime和脚本复制到构建出来的Flutter包中
* 我们将构建出来的***Release***文件夹复制到其他地方，例如桌面。
* 将```./windows/support```文件夹复制到Flutter构建出来的文件夹

  ***support***文件夹如下

  ![support文件夹](image/support文件夹.png)

  **复制之后待打包的文件夹应该如下**

  ![待打包的文件夹](image/复制之后待打包的文件夹.png)

## 修改Inno Setup的打包脚本
* 安装Inno Setup软件,(该软件我已传到钉钉)
* 使用Inno Setup打开```windows打包.iss```
* 修改脚本
   
  ![脚本](image/脚本说明.png)

  如图：
  * ```MyAppVersion```为版本号
  * ```MySource```为**待打包文件夹**路径
* 修改之后，保存并使用Inno Setup编译脚本即可。
* 打包结果如图

  ![打包结果](image/打包结果.png)
* **需要注意，打包输出路径默认和iss脚本在一个地方**
