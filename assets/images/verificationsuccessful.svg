<?xml version="1.0" encoding="UTF-8"?>
<svg width="246px" height="207px" viewBox="0 0 246 207" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 2</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F5F8FF" offset="0%"></stop>
            <stop stop-color="#DCE6FF" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-2" points="54.6576577 93.5225225 176.657658 93.5225225 176.657658 98.5 176.657658 203 54.6576577 203 54.6576577 199 54.6576577 194.5 54.6576577 98.5"></polygon>
        <filter x="-4.9%" y="-5.5%" width="109.8%" height="111.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.823174304   0 0 0 0 0.870781223   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="23.935986%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#4679FC" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#4679FC" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="选择发货" transform="translate(-1278.000000, -381.000000)">
            <g id="编组-2" transform="translate(1278.558559, 381.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="40.5495495" width="227.783784" height="107.711712" rx="10"></rect>
                <rect id="矩形" fill="#000000" x="48.972973" y="85.9279279" width="133" height="10.5945946" rx="5"></rect>
                <g id="矩形">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <path stroke="#B2C7FF" stroke-width="1" d="M176.157658,94.0225225 L176.157658,202.5 L55.1576577,202.5 L55.1576577,94.0225225 L176.157658,94.0225225 Z" stroke-linejoin="square" fill="#F6F8FF" fill-rule="evenodd"></path>
                </g>
                <g id="编组" transform="translate(79.459459, 112.945946)" fill="#272636" fill-rule="nonzero">
                    <path d="M58.244879,71.0494092 L58.244879,64.6171025 L51.6713899,64.6171025 L51.6713899,71.0540541 L58.244879,71.0494092 Z M68.0560847,71.0540541 C69.6653437,71.0540541 70.9818788,69.7374119 70.9818788,68.1282515 L70.9818788,64.6172057 L64.6816055,64.6172057 L64.6816055,71.0494092 M25.7294038,6.49090239 L25.7294038,25.8015507 L6.41881161,25.8015507 L6.41881161,6.49090239 L25.7294038,6.49090239 M29.2404392,0.0540540541 L2.90777605,0.0540540541 C1.29862021,0.0540540541 -0.018018018,1.37069621 -0.018018018,2.97985663 L-0.018018018,29.3124932 C-0.018018018,30.9217569 1.29862032,32.2382957 2.90777605,32.2382957 L29.240336,32.2382957 C30.8494919,32.2382957 32.1661301,30.9216536 32.1661301,29.3124932 L32.1661301,2.97985663 C32.1661301,1.37069621 30.8495951,0.0540540541 29.2404392,0.0540540541 L29.2404392,0.0540540541 Z M12.8557444,16.1462781 C12.8557444,17.9237647 14.2966778,19.3647023 16.0741592,19.3647023 C17.8516407,19.3647023 19.292574,17.9237647 19.292574,16.1462781 C19.292574,14.3687915 17.8516407,12.927854 16.0741592,12.927854 C14.2966778,12.927854 12.8557444,14.3687915 12.8557444,16.1462781 Z M64.5450492,6.49090239 L64.5450492,25.8015507 L45.2345602,25.8015507 L45.2345602,6.49090239 L64.5450492,6.49090239 M68.0560847,0.0540540541 L41.7235247,0.0540540541 C40.1142657,0.0540540541 38.7977307,1.37069621 38.7977307,2.97985663 L38.7977307,29.3124932 C38.7977307,30.9217569 40.114369,32.2382957 41.7235247,32.2382957 L68.0560847,32.2382957 C69.6653437,32.2382957 70.9818788,30.9216536 70.9818788,29.3124932 L70.9818788,2.97985663 C70.9818788,1.37069621 69.6653436,0.0540540541 68.0560847,0.0540540541 L68.0560847,0.0540540541 Z M51.6713898,16.1462781 C51.6713898,17.9237647 53.1123232,19.3647023 54.8898046,19.3647023 C56.6672861,19.3647023 58.1082194,17.9237647 58.1082194,16.1462781 C58.1082194,14.3687915 56.6672861,12.927854 54.8898046,12.927854 C53.1123232,12.927854 51.6713898,14.3687915 51.6713898,16.1462781 Z M25.7294038,45.3065574 L25.7294038,64.6172057 L6.41881161,64.6172057 L6.41881161,45.3065574 L25.7294038,45.3065574 M29.2404392,38.8697091 L2.90777605,38.8697091 C1.29862021,38.8697091 -0.018018018,40.1863513 -0.018018018,41.7955117 L-0.018018018,68.1281482 C-0.018018018,69.7374119 1.29862032,71.0539508 2.90777605,71.0539508 L29.240336,71.0539508 C30.8495951,71.0539508 32.1661301,69.7373086 32.1661301,68.1281482 L32.1661301,41.7955118 C32.1661301,40.1863513 30.8495951,38.8697091 29.2404392,38.8697091 L29.2404392,38.8697091 Z M12.8557444,54.96183 C12.8557444,56.7393166 14.2966778,58.1802541 16.0741592,58.1802541 C17.8516407,58.1802541 19.292574,56.7393166 19.292574,54.96183 C19.292574,53.1843434 17.8516407,51.7434058 16.0741592,51.7434058 C14.2966778,51.7434058 12.8557444,53.1843434 12.8557444,54.96183 L12.8557444,54.96183 Z M68.0560847,38.8697091 L64.4673267,38.8697091 L64.4673267,51.7434058 L58.0304971,51.7434058 L58.030497,38.8697091 L41.7235247,38.8697091 C40.1142657,38.8697091 38.7977307,40.1863513 38.7977307,41.7955117 L38.7977307,68.1281482 C38.7977307,69.7374119 40.114369,71.0539508 41.7235247,71.0539508 L45.2345603,71.0539508 L45.2345603,64.6171024 L45.2345603,58.1802541 L45.2345603,51.7434058 L51.6713899,51.7434058 L51.6713899,58.1802541 L70.981982,58.1802541 L70.981982,41.7955117 C70.981982,40.1863513 69.6653436,38.8697091 68.0560847,38.8697091 Z" id="形状"></path>
                </g>
                <rect id="矩形" fill="url(#linearGradient-4)" x="67.0990991" y="118.243243" width="97.1171171" height="28.2522523"></rect>
                <circle id="椭圆形" stroke="#4679FC" stroke-width="4" fill="#FFFFFF" cx="204.828829" cy="40.6126126" r="38.6126126"></circle>
                <polyline id="路径-5" stroke="#4679FC" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" transform="translate(204.441441, 36.500000) rotate(-45.000000) translate(-204.441441, -36.500000) " points="190.941441 28.5 190.941441 44.5 217.941441 44.5"></polyline>
            </g>
        </g>
    </g>
</svg>