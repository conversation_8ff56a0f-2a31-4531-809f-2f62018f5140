<?xml version="1.0" encoding="UTF-8"?>
<svg width="63px" height="45px" viewBox="0 0 63 45" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 57.1 (83088) - https://sketch.com -->
    <title>形状结合</title>
    <desc>Created with <PERSON>ket<PERSON>.</desc>
    <defs>
        <path d="M3431,262 C3432.65685,262 3434,263.343146 3434,265 L3434,288.6 C3434,290.256854 3432.65685,291.6 3431,291.6 L3411.888,291.6 L3405.88889,299 L3399.888,291.6 L3382,291.6 C3380.34315,291.6 3379,290.256854 3379,288.6 L3379,265 C3379,263.343146 3380.34315,262 3382,262 L3431,262 Z" id="path-1"></path>
        <filter x="-12.7%" y="-13.5%" width="125.5%" height="137.8%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.153900787 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="商品弹窗" transform="translate(-3375.000000, -260.000000)">
            <g id="形状结合">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
        </g>
    </g>
</svg>