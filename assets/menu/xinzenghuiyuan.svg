<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 10</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFF8EE" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="菜单" transform="translate(-103.000000, -423.000000)">
            <g id="编组-10" transform="translate(103.000000, 423.000000)">
                <rect id="矩形" fill="#F9BC31" x="0" y="0" width="66" height="66" rx="16"></rect>
                <path d="M32,33.05 C33.2426407,33.05 34.4600727,33.1093622 35.6434328,33.223212 L36.4271946,33.3071306 C45.8991828,34.4250502 53,39.0564418 53,44.6 C53,47.3927278 51.1978608,49.9539539 48.1982945,51.9510867 C28.4017055,52.3018112 17.6028425,52.3018112 15.8017055,51.9510867 C13.1,51.425 11,47.3927278 11,44.6 C11,39.0564418 18.1008172,34.4250502 27.5728054,33.3071306 L28.3565672,33.223212 C29.5399273,33.1093622 30.7573593,33.05 32,33.05 Z M31.475,11 C36.9840404,11 41.45,15.4659596 41.45,20.975 C41.45,26.4840404 36.9840404,30.95 31.475,30.95 C25.9659596,30.95 21.5,26.4840404 21.5,20.975 C21.5,15.4659596 25.9659596,11 31.475,11 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                <circle id="椭圆形" stroke="#F9BC31" stroke-width="3" fill="#FFFFFF" cx="50" cy="43" r="9.5"></circle>
                <rect id="矩形" fill="#F9BC31" x="45" y="42" width="10" height="2" rx="1"></rect>
                <rect id="矩形" fill="#F9BC31" transform="translate(50.000000, 43.000000) rotate(-270.000000) translate(-50.000000, -43.000000) " x="45" y="42" width="10" height="2" rx="1"></rect>
            </g>
        </g>
    </g>
</svg>