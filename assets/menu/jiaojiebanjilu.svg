<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 57.1 (83088) - https://sketch.com -->
    <title>编组 22</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.1659628%" id="linearGradient-1">
            <stop stop-color="#E65252" offset="0%"></stop>
            <stop stop-color="#F76266" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFD3D4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0.78125%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFAA8D" offset="0%"></stop>
            <stop stop-color="#FE722F" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#FFEDBA" offset="0%"></stop>
            <stop stop-color="#FFE890" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="菜单" transform="translate(-983.000000, -676.000000)">
            <g id="编组-22" transform="translate(983.000000, 676.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="66" height="66" rx="16"></rect>
                <ellipse id="椭圆形" fill="#FFFFFF" cx="32" cy="23.5" rx="12" ry="11.5"></ellipse>
                <path d="M35.1643293,27.4507691 C34.5212917,28.4291696 33.5436579,29 32.5,29 C31.4563421,29 30.4787083,28.4291696 29.8356707,27.4507691 C29.5323389,26.989241 28.9122973,26.8609975 28.4507691,27.1643293 C27.989241,27.4676611 27.8609975,28.0877027 28.1643293,28.5492309 C29.164178,30.0705297 30.7560371,31 32.5,31 C34.2439629,31 35.835822,30.0705297 36.8356707,28.5492309 C37.1390025,28.0877027 37.010759,27.4676611 36.5492309,27.1643293 C36.0877027,26.8609975 35.4676611,26.989241 35.1643293,27.4507691 Z" id="形状" fill="#FF6B36" fill-rule="nonzero"></path>
                <path d="M21.6784455,34 L42.3215545,34 C44.7720127,34 46.8613719,35.7759027 47.2562189,38.1943405 L49.0521372,49.1943405 C49.4970907,51.9196804 47.6484722,54.489711 44.9231323,54.9346644 C44.6567825,54.9781501 44.3873491,55 44.1174728,55 L19.8825272,55 C17.1211034,55 14.8825272,52.7614237 14.8825272,50 C14.8825272,49.7301237 14.9043771,49.4606903 14.9478628,49.1943405 L16.7437811,38.1943405 C17.1386281,35.7759027 19.2279873,34 21.6784455,34 Z" id="矩形" fill="url(#linearGradient-2)"></path>
                <path d="M29.0496151,36.3664101 L31,37.6666667 L31,37.6666667 L31,38.9166667 L28,48.0833333 L32.5,51 L37,48.0833333 L34.5,39.3333333 L34.5,37.6666667 L36.4503849,36.3664101 C36.5422906,36.3051396 36.5671254,36.1809657 36.5058549,36.08906 C36.4687618,36.0334202 36.4063155,36 36.3394449,36 L29.1605551,36 C29.0500982,36 28.9605551,36.0895431 28.9605551,36.2 C28.9605551,36.2668706 28.9939754,36.3293169 29.0496151,36.3664101 Z" id="路径-3" fill="url(#linearGradient-3)"></path>
                <circle id="椭圆形" fill="url(#linearGradient-4)" cx="53" cy="53" r="12"></circle>
                <path d="M47,49.25 C46.3096441,49.25 45.75,49.8096441 45.75,50.5 C45.75,51.1903559 46.3096441,51.75 47,51.75 L59.5,51.75 C60.6136311,51.75 61.1713396,50.4035726 60.3838835,49.6161165 L56.8838835,46.1161165 C56.3957281,45.6279612 55.6042719,45.6279612 55.1161165,46.1161165 C54.6279612,46.6042719 54.6279612,47.3957281 55.1161165,47.8838835 L56.482233,49.25 L47,49.25 Z" id="路径-4" fill="#E55151" fill-rule="nonzero" transform="translate(53.251253, 48.750000) rotate(-360.000000) translate(-53.251253, -48.750000) "></path>
                <path d="M47,58.25 C46.3096441,58.25 45.75,58.8096441 45.75,59.5 C45.75,60.1903559 46.3096441,60.75 47,60.75 L59.5,60.75 C60.6136311,60.75 61.1713396,59.4035726 60.3838835,58.6161165 L56.8838835,55.1161165 C56.3957281,54.6279612 55.6042719,54.6279612 55.1161165,55.1161165 C54.6279612,55.6042719 54.6279612,56.3957281 55.1161165,56.8838835 L56.482233,58.25 L47,58.25 Z" id="路径-4" fill="#E55151" fill-rule="nonzero" transform="translate(53.251253, 57.750000) rotate(-180.000000) translate(-53.251253, -57.750000) "></path>
            </g>
        </g>
    </g>
</svg>