<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 57.1 (83088) - https://sketch.com -->
    <title>编组 23</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#3863EE" offset="0%"></stop>
            <stop stop-color="#7AA1F7" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="1.96646571%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#6D95F5" offset="0%"></stop>
            <stop stop-color="#C3D8FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="1.15105967%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#E0E8FF" offset="0%"></stop>
            <stop stop-color="#C8D7FF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="菜单" transform="translate(-1323.000000, -671.000000)">
            <g id="编组-23" transform="translate(1323.000000, 671.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="66" height="66" rx="12"></rect>
                <g id="编组-13" transform="translate(9.000000, 12.000000)">
                    <path d="M42,0 C43.6568542,-3.04359188e-16 45,1.34314575 45,3 L45,7 L45,7 L39,7 L39,3 C39,1.34314575 40.3431458,3.04359188e-16 42,0 Z" id="矩形" fill="url(#linearGradient-2)"></path>
                    <polygon id="三角形" fill="#BFD5FE" transform="translate(44.000000, 7.500000) rotate(-180.000000) translate(-44.000000, -7.500000) " points="44 7 45 8 43 8"></polygon>
                    <polygon id="三角形" fill="#BFD5FE" transform="translate(42.000000, 7.500000) rotate(-180.000000) translate(-42.000000, -7.500000) " points="42 7 43 8 41 8"></polygon>
                    <polygon id="三角形" fill="#BFD5FE" transform="translate(40.000000, 7.500000) rotate(-180.000000) translate(-40.000000, -7.500000) " points="40 7 41 8 39 8"></polygon>
                    <path d="M42.5,0 C40.1666667,0 39,1.83333333 39,5.5 L39,30 C38.4477153,30 38,30.4477153 38,31 C38,31.5522847 38.4477153,32 39,32 L39,37 L6,37 L6,32 C6.55228475,32 7,31.5522847 7,31 C7,30.4477153 6.55228475,30 6,30 L6,5 C6,1.66666667 7.16666667,0 9.5,0 L42.5,0 Z" id="路径" fill="#FFFFFF"></path>
                    <path d="M29,37 C29,39.7614237 31.2385763,42 34,42 C36.7614237,42 39,39.7614237 39,37" id="路径" fill="#FFFFFF"></path>
                    <path d="M7.5,31.5 L8.5,31.5 L8.5,30.5 L7.5,30.5 L7.5,31.5 Z M9.5,31.5 L10.5,31.5 L10.5,30.5 L9.5,30.5 L9.5,31.5 Z M11.5,31.5 L12.5,31.5 L12.5,30.5 L11.5,30.5 L11.5,31.5 Z M13.5,31.5 L14.5,31.5 L14.5,30.5 L13.5,30.5 L13.5,31.5 Z M15.5,31.5 L16.5,31.5 L16.5,30.5 L15.5,30.5 L15.5,31.5 Z M17.5,31.5 L18.5,31.5 L18.5,30.5 L17.5,30.5 L17.5,31.5 Z M19.5,31.5 L20.5,31.5 L20.5,30.5 L19.5,30.5 L19.5,31.5 Z M21.5,31.5 L22.5,31.5 L22.5,30.5 L21.5,30.5 L21.5,31.5 Z M23.5,31.5 L24.5,31.5 L24.5,30.5 L23.5,30.5 L23.5,31.5 Z M25.5,31.5 L26.5,31.5 L26.5,30.5 L25.5,30.5 L25.5,31.5 Z M27.5,31.5 L28.5,31.5 L28.5,30.5 L27.5,30.5 L27.5,31.5 Z M29.5,31.5 L30.5,31.5 L30.5,30.5 L29.5,30.5 L29.5,31.5 Z M31.5,31.5 L32.5,31.5 L32.5,30.5 L31.5,30.5 L31.5,31.5 Z M33.5,31.5 L34.5,31.5 L34.5,30.5 L33.5,30.5 L33.5,31.5 Z M35.5,31.5 L36.5,31.5 L36.5,30.5 L35.5,30.5 L35.5,31.5 Z" id="路径-7" fill="#9BB6FF" fill-rule="nonzero"></path>
                    <rect id="矩形" fill="#CFDCFF" x="17" y="10" width="17" height="2"></rect>
                    <rect id="矩形" fill="#CFDCFF" x="11" y="10" width="2" height="2"></rect>
                    <rect id="矩形" fill="#CFDCFF" x="17" y="16" width="17" height="2"></rect>
                    <rect id="矩形" fill="#7FEF97" x="11" y="16" width="2" height="2"></rect>
                    <rect id="矩形" fill="#CFDCFF" x="17" y="22" width="17" height="2"></rect>
                    <rect id="矩形" fill="#CFDCFF" x="11" y="22" width="2" height="2"></rect>
                    <path d="M0,37 L28.9473684,37 C28.9473684,38.1111111 29.3684211,39.2222222 30.2105263,40.3333333 C31.0526316,41.4444444 32.3157895,42 34,42 L3.45510995,42 C2.37977349,41.8333333 1.56140351,41.3333333 1,40.5 C0.438596491,39.6666667 0.105263158,38.5 0,37 Z" id="矩形" fill="url(#linearGradient-3)"></path>
                </g>
                <circle id="椭圆形" fill="#343A4E" cx="47.5" cy="47.5" r="10.5"></circle>
                <g id="编组-12" transform="translate(42.000000, 37.000000)" fill="#FFFFFF" fill-rule="nonzero">
                    <path d="M4.26675084,6.83333333 L4.26675084,3.16666667 C4.26675084,2.61438192 3.81903559,2.16666667 3.26675084,2.16666667 C2.71446609,2.16666667 2.26675084,2.61438192 2.26675084,3.16666667 L2.26675084,7.83333333 C2.26675084,8.38561808 2.71446609,8.83333333 3.26675084,8.83333333 L7.93341751,8.83333333 C8.48570226,8.83333333 8.93341751,8.38561808 8.93341751,7.83333333 C8.93341751,7.28104858 8.48570226,6.83333333 7.93341751,6.83333333 L4.26675084,6.83333333 Z" id="路径" transform="translate(5.600084, 5.500000) rotate(-45.000000) translate(-5.600084, -5.500000) "></path>
                    <path d="M2,10.5833333 L9,10.5833333 C9.55228475,10.5833333 10,10.1356181 10,9.58333333 C10,9.03104858 9.55228475,8.58333333 9,8.58333333 L2,8.58333333 C1.44771525,8.58333333 1,9.03104858 1,9.58333333 C1,10.1356181 1.44771525,10.5833333 2,10.5833333 Z" id="路径-9"></path>
                    <path d="M2,14 L9,14 C9.55228475,14 10,13.5522847 10,13 C10,12.4477153 9.55228475,12 9,12 L2,12 C1.44771525,12 1,12.4477153 1,13 C1,13.5522847 1.44771525,14 2,14 Z" id="路径-9"></path>
                    <path d="M4.5,9.58333333 L4.5,15.4166667 C4.5,15.9689514 4.94771525,16.4166667 5.5,16.4166667 C6.05228475,16.4166667 6.5,15.9689514 6.5,15.4166667 L6.5,9.58333333 C6.5,9.03104858 6.05228475,8.58333333 5.5,8.58333333 C4.94771525,8.58333333 4.5,9.03104858 4.5,9.58333333 Z" id="路径-9"></path>
                </g>
            </g>
        </g>
    </g>
</svg>