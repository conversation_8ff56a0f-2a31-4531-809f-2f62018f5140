<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 57.1 (83088) - https://sketch.com -->
    <title>编组 18</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F37B3C" offset="0%"></stop>
            <stop stop-color="#FE9133" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFDCDB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FAC342" offset="0%"></stop>
            <stop stop-color="#F2901F" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="菜单" transform="translate(-1493.000000, -197.000000)">
            <g id="编组-18" transform="translate(1493.000000, 197.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="66" height="66" rx="12"></rect>
                <g id="编组-10" transform="translate(14.000000, 8.000000)">
                    <path d="M5.57243874,9.38888889 L32.4275613,9.38888889 C34.559058,9.38888889 36.3161287,11.060301 36.4225706,13.1891384 L37.7900125,40.5379756 C37.9003316,42.7443584 36.2011363,44.6224163 33.9947536,44.7327355 C33.928222,44.736062 33.8616178,44.7377261 33.7950031,44.7377261 L4.20499688,44.7377261 C1.99585788,44.7377261 0.204996879,42.9468651 0.204996879,40.7377261 C0.204996879,40.6711114 0.206660946,40.6045072 0.209987523,40.5379756 L1.57742938,13.1891384 C1.68387125,11.060301 3.44094197,9.38888889 5.57243874,9.38888889 Z" id="矩形" fill="url(#linearGradient-2)"></path>
                    <rect id="矩形" fill="#FFE2E2" x="7.38888889" y="9.38888889" width="6.33333333" height="34.8333333"></rect>
                    <rect id="矩形" fill="#FFE2E2" x="24.2777778" y="9.38888889" width="6.33333333" height="34.8333333"></rect>
                    <path d="M12.0555556,18.8888889 L12.0555556,10.6309148 C12.0555556,6.68537498 15.1721107,3.5 19,3.5 C22.8278893,3.5 25.9444444,6.68537498 25.9444444,10.6309148 L25.9444444,18.8888889 L28.9444444,18.8888889 L28.9444444,10.6309148 C28.9444444,5.0430094 24.4995865,0.5 19,0.5 C13.5004135,0.5 9.05555556,5.0430094 9.05555556,10.6309148 L9.05555556,18.8888889 L12.0555556,18.8888889 Z" id="路径" fill="#FFDADA" fill-rule="nonzero"></path>
                    <circle id="椭圆形" fill="#EC5957" cx="10.5555556" cy="17.8333333" r="2.11111111"></circle>
                    <circle id="椭圆形" fill="#EC5957" cx="27.4444444" cy="17.8333333" r="2.11111111"></circle>
                </g>
                <g id="编组-9" transform="translate(46.877025, 40.050938) rotate(-45.000000) translate(-46.877025, -40.050938) translate(29.877025, 35.050938)">
                    <polygon id="矩形" fill="url(#linearGradient-3)" points="6.7826087 0 27.4782609 0 27.4782609 9.04347826 6.7826087 9.04347826"></polygon>
                    <rect id="矩形" fill="#F29221" x="6.7826087" y="3.39130435" width="20.6956522" height="2.26086957"></rect>
                    <path d="M26.6086957,3.55271368e-15 L32.1413043,3.55271368e-15 C32.8316603,3.20385275e-15 33.3913043,0.559644063 33.3913043,1.25 L33.3913043,7.79347826 C33.3913043,8.4838342 32.8316603,9.04347826 32.1413043,9.04347826 L26.6086957,9.04347826 L26.6086957,9.04347826 L26.6086957,3.55271368e-15 Z" id="矩形" fill="#FA4516"></path>
                    <polygon id="三角形" fill="#FFC585" transform="translate(3.391304, 4.521739) rotate(-90.000000) translate(-3.391304, -4.521739) " points="3.39130435 1.13043478 7.91304348 7.91304348 -1.13043478 7.91304348"></polygon>
                    <polygon id="三角形" fill="#1F2722" transform="translate(1.695652, 4.521739) rotate(-90.000000) translate(-1.695652, -4.521739) " points="1.69565217 2.82608696 3.95652174 6.2173913 -0.565217391 6.2173913"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>