<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 57.1 (83088) - https://sketch.com -->
    <title>编组 21</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#8D4FFF" offset="0%"></stop>
            <stop stop-color="#A475FD" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="菜单" transform="translate(-1323.000000, -438.000000)">
            <g id="编组-21" transform="translate(1323.000000, 438.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="66" height="66" rx="16"></rect>
                <g id="编组-8" transform="translate(14.000000, 9.000000)">
                    <rect id="矩形" stroke="#5B12E3" stroke-width="3" fill="#FFFFFF" x="1.5" y="4.95454545" width="35" height="40.8461538" rx="6"></rect>
                    <rect id="形状结合" fill="#14CD9B" x="14.6153846" y="16.5641026" width="17.006993" height="2.12587413"></rect>
                    <rect id="形状结合" fill="#E1E5E6" x="14.6153846" y="22.4102564" width="17.006993" height="2.12587413"></rect>
                    <rect id="形状结合" fill="#E1E5E6" x="6.82051282" y="22.4102564" width="2.12587413" height="2.12587413"></rect>
                    <rect id="形状结合" fill="#E1E5E6" x="14.6153846" y="27.2820513" width="17.006993" height="2.12587413"></rect>
                    <rect id="形状结合" fill="#E1E5E6" x="6.82051282" y="27.2820513" width="2.12587413" height="2.12587413"></rect>
                    <rect id="形状结合" fill="#E1E5E6" x="14.6153846" y="32.1538462" width="17.006993" height="2.12587413"></rect>
                    <rect id="形状结合" fill="#E1E5E6" x="6.82051282" y="32.1538462" width="2.12587413" height="2.12587413"></rect>
                    <rect id="形状结合" fill="#E1E5E6" x="14.6153846" y="37.025641" width="17.006993" height="2.12587413"></rect>
                    <rect id="形状结合" fill="#E1E5E6" x="6.82051282" y="37.025641" width="2.12587413" height="2.12587413"></rect>
                    <polygon id="路径-6" fill="#14CD9B" fill-rule="nonzero" transform="translate(8.769231, 16.564103) rotate(-45.000000) translate(-8.769231, -16.564103) " points="7.51648352 16.9538462 7.51648352 14.6153846 5.84615385 14.6153846 5.84615385 18.5128205 11.6923077 18.5128205 11.6923077 16.9538462"></polygon>
                    <path d="M10.7692308,4.87179487 L27.2307692,4.87179487 C28.3353387,4.87179487 29.2307692,5.76722537 29.2307692,6.87179487 L29.2307692,11.1608392 L29.2307692,11.1608392 L8.76923077,11.1608392 L8.76923077,6.87179487 C8.76923077,5.76722537 9.66466127,4.87179487 10.7692308,4.87179487 Z" id="矩形" fill="#404040"></path>
                    <circle id="椭圆形" stroke="#404040" stroke-width="2.5" fill="#9156FE" cx="19" cy="4.38461538" r="3.13461538"></circle>
                </g>
            </g>
        </g>
    </g>
</svg>