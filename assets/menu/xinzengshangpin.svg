<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 57.1 (83088) - https://sketch.com -->
    <title>编组 28</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#0FA382" offset="0%"></stop>
            <stop stop-color="#1BBB96" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFF2DB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FFF7B9" offset="0%"></stop>
            <stop stop-color="#FDE182" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-28">
            <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="66" height="66" rx="16"></rect>
            <path d="M19.5724387,18.3888889 L46.4275613,18.3888889 C48.559058,18.3888889 50.3161287,20.060301 50.4225706,22.1891384 L51.7900125,49.5379756 C51.9003316,51.7443584 50.2011363,53.6224163 47.9947536,53.7327355 C47.928222,53.736062 47.8616178,53.7377261 47.7950031,53.7377261 L18.2049969,53.7377261 C15.9958579,53.7377261 14.2049969,51.9468651 14.2049969,49.7377261 C14.2049969,49.6711114 14.2066609,49.6045072 14.2099875,49.5379756 L15.5774294,22.1891384 C15.6838713,20.060301 17.440942,18.3888889 19.5724387,18.3888889 Z" id="矩形" fill="url(#linearGradient-2)"></path>
            <rect id="矩形" fill="#FFF2DD" x="21.3888889" y="18.3888889" width="6.33333333" height="34.8333333"></rect>
            <rect id="矩形" fill="#FFF2DD" x="38.2777778" y="18.3888889" width="6.33333333" height="34.8333333"></rect>
            <path d="M26.0555556,27.8888889 L26.0555556,19.6309148 C26.0555556,15.685375 29.1721107,12.5 33,12.5 C36.8278893,12.5 39.9444444,15.685375 39.9444444,19.6309148 L39.9444444,27.8888889 L42.9444444,27.8888889 L42.9444444,19.6309148 C42.9444444,14.0430094 38.4995865,9.5 33,9.5 C27.5004135,9.5 23.0555556,14.0430094 23.0555556,19.6309148 L23.0555556,27.8888889 L26.0555556,27.8888889 Z" id="路径" fill="#FFD4AF" fill-rule="nonzero"></path>
            <circle id="椭圆形" fill="#FE9133" cx="24.5555556" cy="26.8333333" r="2.11111111"></circle>
            <circle id="椭圆形" fill="#FE9133" cx="41.4444444" cy="26.8333333" r="2.11111111"></circle>
            <circle id="椭圆形" fill="url(#linearGradient-3)" cx="53" cy="53" r="12"></circle>
            <rect id="矩形" fill="#FE9133" x="46" y="52" width="15" height="3" rx="1.5"></rect>
            <rect id="矩形" fill="#FE9133" transform="translate(53.500000, 53.500000) rotate(-270.000000) translate(-53.500000, -53.500000) " x="46" y="52" width="15" height="3" rx="1.5"></rect>
        </g>
    </g>
</svg>