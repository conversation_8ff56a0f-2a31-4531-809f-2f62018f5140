import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../iconfont/icon_font.dart';

/// 积分兑换数字加减器组件
/// 用于积分兑换弹窗和列表页面的数量选择
class CreditsQuantityStepper extends StatelessWidget {
  /// 当前数量
  final int quantity;

  /// 数量变化回调
  final ValueChanged<int>? onQuantityChanged;

  /// 最小数量，默认为0
  final int minQuantity;

  /// 最大数量，可选
  final int? maxQuantity;

  /// 按钮大小，默认36.w
  final double buttonSize;

  /// 按钮间距，默认20.w
  final double spacing;

  /// 减号按钮边框颜色
  final Color decreaseButtonBorderColor;

  /// 加号按钮背景颜色
  final Color increaseButtonColor;

  /// 数字文本颜色
  final Color textColor;

  /// 数字字体大小
  final double fontSize;

  /// 字体粗细
  final FontWeight fontWeight;

  const CreditsQuantityStepper({
    Key? key,
    required this.quantity,
    this.onQuantityChanged,
    this.minQuantity = 0,
    this.maxQuantity,
    this.buttonSize = 36.0,
    this.spacing = 20.0,
    this.decreaseButtonBorderColor = const Color(0xFFCECECE),
    this.increaseButtonColor = const Color(0xFF4679FC),
    this.textColor = const Color(0xFF333333),
    this.fontSize = 24.0,
    this.fontWeight = FontWeight.w500,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 减号按钮
        _buildDecreaseButton(),
        SizedBox(width: spacing),
        // 数量显示
        _buildQuantityDisplay(),
        SizedBox(width: spacing),
        // 加号按钮
        _buildIncreaseButton(),
      ],
    );
  }

  /// 构建减号按钮
  Widget _buildDecreaseButton() {
    final bool isEnabled = quantity > minQuantity;

    return GestureDetector(
      onTap: isEnabled ? _onDecreasePressed : null,
      child: Container(
        width: buttonSize,
        height: buttonSize,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(buttonSize / 2),
          border: Border.all(color: decreaseButtonBorderColor, width: 1.w),
        ),
        child: Center(
          child: Container(
            width: 12.w,
            height: 2.w,
            color: isEnabled ? Colors.black : Colors.grey,
          ),
        ),
      ),
    );
  }

  /// 构建数量显示
  Widget _buildQuantityDisplay() {
    return Text(
      quantity.toString(),
      style: TextStyle(
        color: textColor,
        fontSize: fontSize.sp,
        fontFamily: 'Avenir-Medium',
        fontWeight: fontWeight,
        height: 1.3,
      ),
    );
  }

  /// 构建加号按钮
  Widget _buildIncreaseButton() {
    final bool isEnabled = maxQuantity == null || quantity < maxQuantity!;

    return GestureDetector(
      onTap: isEnabled ? _onIncreasePressed : null,
      child: Container(
        width: buttonSize,
        height: buttonSize,
        decoration: BoxDecoration(
          color: isEnabled ? increaseButtonColor : Colors.grey,
          borderRadius: BorderRadius.circular(buttonSize / 2),
        ),
        child: Center(
          child: IconFont(IconNames.shangchuan, color: "#FFFFFF", size: 18.w),
        ),
      ),
    );
  }

  /// 减号按钮点击事件
  void _onDecreasePressed() {
    if (quantity > minQuantity && onQuantityChanged != null) {
      onQuantityChanged!(quantity - 1);
    }
  }

  /// 加号按钮点击事件
  void _onIncreasePressed() {
    if ((maxQuantity == null || quantity < maxQuantity!) &&
        onQuantityChanged != null) {
      onQuantityChanged!(quantity + 1);
    }
  }
}
