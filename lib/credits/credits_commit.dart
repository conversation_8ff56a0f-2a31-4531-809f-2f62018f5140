import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/bill/tool/goods_tool.dart';
import '../../../credits/entity/promotion_credits_dto.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../bill/entity/goods_detail_dto.dart';
import '../bill/entity/ptype/ptype_serial_no_dto.dart';
import '../bill/tool/scan_tool.dart';
import '../bill/widget/ptype/goods_bind_batch_page.dart';
import '../bill/widget/ptype/goods_bind_sn_page.dart';
import '../common/tool/sp_tool.dart';
import '../iconfont/icon_font.dart';
import 'widget/credits_quantity_stepper.dart';

class CreditsCommit extends StatefulWidget {
  final Function(int quantity)? onCommit;
  final VoidCallback? onCancel;
  final PromotionCreditsDTO promotionCreditsDTO;

  const CreditsCommit({
    super.key,
    this.onCommit,
    required this.promotionCreditsDTO,
    this.onCancel,
  });

  @override
  State<CreditsCommit> createState() => _CreditsCommitState();
}

class _CreditsCommitState extends State<CreditsCommit> {
  final TextEditingController controllerSn = TextEditingController();
  final TextEditingController controllerBatch = TextEditingController();
  late int quantity; // 添加数量变量
  late GoodsDetailDto detailDto;

  @override
  initState() {
    super.initState();
    // 初始化数量，如果有选择过就用已选数量，否则默认为1
    quantity =
        widget.promotionCreditsDTO.localSelectQty > 0
            ? widget.promotionCreditsDTO.localSelectQty
            : 1;
    initData();
  }

  initData() async {
    if (widget.promotionCreditsDTO.ptypeGroup == 3) {
      ///不做套餐的批次
      var storeInfo = SpTool.getStoreInfo()!;
      List<GoodsDetailDto> details = [];

      GoodsDetailDto dto =
          GoodsDetailDto()
            ..kfullname = storeInfo.ktypeName
            ..ktypeId = storeInfo.ktypeId
            ..pFullName = widget.promotionCreditsDTO.creditsName ?? ""
            ..ptypeId = widget.promotionCreditsDTO.ptypeId ?? "0"
            ..skuId = widget.promotionCreditsDTO.skuId ?? "0"
            ..unitId = widget.promotionCreditsDTO.unitId ?? "0"
            ..unitRate =
                num.tryParse(widget.promotionCreditsDTO.unitRate ?? "1") ?? 1
            ..unitName = widget.promotionCreditsDTO.unitName ?? ""
            ..unitQty = 1
            ..stockQty = 1
            ..fullbarcode = widget.promotionCreditsDTO.fullbarcode ?? ""
            ..batchenabled = widget.promotionCreditsDTO.batchenabled == 1
            ..picUrl = widget.promotionCreditsDTO.picUrl
            ..costMode = widget.promotionCreditsDTO.costMode ?? 0
            ..protectDays = widget.promotionCreditsDTO.protectDays ?? 0
            ..snenabled = widget.promotionCreditsDTO.snenabled ?? 0
      ..batchNo = widget.promotionCreditsDTO.batchNo??""
      ..serialNoList = widget.promotionCreditsDTO.serialNoList;
      details.add(dto);
      if (widget.promotionCreditsDTO.batchNo?.isEmpty ?? true) {
        await ScanTool.getPtypeAutoBatch(details, context);
        widget.promotionCreditsDTO.batchNo = details.first.batchNo;
        widget.promotionCreditsDTO.produceDate = details.first.produceDate;
        widget.promotionCreditsDTO.expireDate = details.first.expireDate;
        widget.promotionCreditsDTO.costId = details.first.costId;
        widget.promotionCreditsDTO.batchPrice = details.first.batchPrice;
        widget.promotionCreditsDTO.pUserCode = details.first.pUserCode;
      }
      detailDto = details.first;
      setState(() {
        controllerBatch.text = widget.promotionCreditsDTO.batchNo ?? "";
        controllerSn.text = getSnStr(widget.promotionCreditsDTO.serialNoList);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color.fromRGBO(0, 0, 0, 0.6),
      body: Center(
        child: Container(
          width: 550.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.w),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 492.w,
                margin: EdgeInsets.symmetric(horizontal: 28.w, vertical: 29.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 图片区域
                    Container(
                      width: 483.w,
                      height: 326.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.w),
                      ),
                      child: PromotionCreditsDTO.getPic(
                        ptypeGroup: widget.promotionCreditsDTO.ptypeGroup,
                        picUrl: widget.promotionCreditsDTO.picUrl ?? "",
                        valueType: int.tryParse(
                          widget.promotionCreditsDTO.valueType ?? "",
                        ),
                      ),
                    ),
                    // 商品名称
                    Container(
                      margin: EdgeInsets.only(top: 14.w),
                      child: Text(
                        widget.promotionCreditsDTO.ptypeGroup! == 7
                            ? "储值金额：${widget.promotionCreditsDTO.price}元"
                            : widget.promotionCreditsDTO.getName()!,
                        style: TextStyle(
                          color: ColorUtil.stringColor("#333333"),
                          fontSize: 24.sp,
                          fontFamily: 'PingFangSC-Medium',
                          fontWeight: FontWeight.w500,
                          height: 33.w / 24.sp,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // 积分和数量选择器
              Container(
                width: 492.w,
                height: 43.w,
                margin: EdgeInsets.symmetric(horizontal: 28.w),
                child: Row(
                  children: [
                    // 积分显示
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: "${widget.promotionCreditsDTO.preferential!}",
                            style: TextStyle(
                              color: ColorUtil.stringColor("#FF4141"),
                              fontSize: 26.sp,
                              fontFamily: 'Avenir-Medium',
                              fontWeight: FontWeight.w500,
                              height: 36.w / 26.sp,
                            ),
                          ),
                          TextSpan(
                            text: "积分",
                            style: TextStyle(
                              color: ColorUtil.stringColor("#FF4141"),
                              fontSize: 26.sp,
                              fontFamily: 'PingFangSC-Medium',
                              fontWeight: FontWeight.w500,
                              height: 36.w / 26.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    // 数量选择器
                    CreditsQuantityStepper(
                      quantity: quantity,
                      onQuantityChanged: (newQuantity) {
                        // 检查序列号数量限制
                        if (_checkSerialNumberQuantity(newQuantity)) {
                          setState(() {
                            quantity = newQuantity;
                            widget.promotionCreditsDTO.localSelectQty = quantity;
                          });
                        }
                      },
                      buttonSize: 36.w,
                      spacing: 20.w,
                      fontSize: 24.0,
                      decreaseButtonBorderColor: ColorUtil.stringColor(
                        "#CECECE",
                      ),
                      increaseButtonColor: ColorUtil.stringColor("#4679FC"),
                      textColor: ColorUtil.stringColor("#333333"),
                    ),
                  ],
                ),
              ),

              // 批次号
              Visibility(
                visible: widget.promotionCreditsDTO.batchenabled == 1,
                child: Container(
                  width: 486.w,
                  height: 35.w,
                  margin: EdgeInsets.only(left: 28.w, top: 30.w),
                  child: Row(
                    children: [
                      Text(
                        "批次号：",
                        style: TextStyle(
                          color: ColorUtil.stringColor("#666666"),
                          fontSize: 20.sp,
                          fontWeight: FontWeight.normal,
                          height: 1.2,
                        ),
                      ),
                      Text(
                        controllerBatch.text,
                        style: TextStyle(
                          color: ColorUtil.stringColor("#666666"),
                          fontSize: 20.sp,
                          fontWeight: FontWeight.normal,
                          height: 1.2,
                        ),
                      ),
                      const Spacer(),
                      GestureDetector(
                        onTap: () {
                          _btnBatch(detailDto);
                        },
                        child: Image.asset('assets/images/edit.png'),
                      ),
                    ],
                  ),
                ),
              ),
              // 序列号
              Visibility(
                visible:
                    widget.promotionCreditsDTO.snenabled == 1 ||
                    widget.promotionCreditsDTO.snenabled == 2,
                child: Container(
                  width: 486.w,
                  height: 35.w,
                  margin: EdgeInsets.only(left: 28.w, top: 10.w),
                  child: Row(
                    children: [
                      Text(
                        "序列号：",
                        style: TextStyle(
                          color: ColorUtil.stringColor("#666666"),
                          fontSize: 20.sp,
                          fontWeight: FontWeight.normal,
                          height: 30.w / 22.sp,
                        ),
                      ),
                      Text(
                        controllerSn.text,
                        style: TextStyle(
                          color: ColorUtil.stringColor("#666666"),
                          fontSize: 20.sp,
                          fontWeight: FontWeight.normal,
                          height: 1.2,
                        ),
                      ),
                      const Spacer(),
                      GestureDetector(
                        onTap: () {
                          _btnSn(detailDto);
                        },
                        child: Image.asset('assets/images/edit.png'),
                      ),
                    ],
                  ),
                ),
              ),
              // 按钮
              Container(
                width: 490.w,
                height: 60.w,
                margin: EdgeInsets.only(
                  left: 30.w,
                  right: 30.w,
                  top: 45.w,
                  bottom: 30.w,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 取消按钮
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        if (widget.onCancel != null) {
                          widget.onCancel!();
                        }
                      },
                      child: Container(
                        width: 235.w,
                        height: 60.w,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: ColorUtil.stringColor("#DADADA"),
                            width: 1.5.w,
                          ),
                          borderRadius: BorderRadius.circular(8.w),
                        ),
                        child: Center(
                          child: Text(
                            "取消",
                            style: TextStyle(
                              color: ColorUtil.stringColor("#333333"),
                              fontSize: 22.sp,
                              fontWeight: FontWeight.normal,
                              height: 30.w / 22.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                    // 确认按钮
                    GestureDetector(
                      onTap: () {
                        if (widget.onCommit != null) {
                          widget.onCommit!(quantity);
                        }
                      },
                      child: Container(
                        width: 235.w,
                        height: 60.w,
                        decoration: BoxDecoration(
                          color: ColorUtil.stringColor("#4679FC"),
                          borderRadius: BorderRadius.circular(8.w),
                        ),
                        child: Center(
                          child: Text(
                            "确认",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 22.sp,
                              fontWeight: FontWeight.normal,
                              height: 30.w / 22.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String getSnStr(List<PtypeSerialNoDto> snList) {
    return snList.map((e) => e.snno).toList().join(",");
  }

  /// 检查序列号数量限制
  /// 当数量为1时点击减少不需要判断（清空操作）
  /// 返回true表示可以修改数量，false表示不能修改
  bool _checkSerialNumberQuantity(int newQuantity) {
    // 当数量为0时，清空批次和序列号信息
    if (newQuantity == 0) {
      _clearBatchAndSerialInfo();
      return true;
    }

    // 如果不是序列号商品，直接允许修改
    if (widget.promotionCreditsDTO.snenabled == 0) {
      return true;
    }

    // 当前已选择的序列号数量
    int currentSerialCount = widget.promotionCreditsDTO.serialNoList.length;

    // 如果没有选择序列号，直接允许修改
    if (currentSerialCount == 0) {
      return true;
    }

    // 检查新数量是否小于已选择的序列号数量
    if (newQuantity < currentSerialCount) {
      HaloToast.showInfo(context,
        msg: "商品数量不能小于序列号数量($currentSerialCount)，请先减少序列号");
      return false;
    }

    return true;
  }

  /// 清空批次和序列号信息
  void _clearBatchAndSerialInfo() {
    widget.promotionCreditsDTO.serialNoList.clear();
    widget.promotionCreditsDTO.batchNo = null;
    widget.promotionCreditsDTO.produceDate = null;
    widget.promotionCreditsDTO.expireDate = null;
    widget.promotionCreditsDTO.batchPrice = 0;
    widget.promotionCreditsDTO.costId = null;
    widget.promotionCreditsDTO.pUserCode = null;

    // 同时更新UI显示
    setState(() {
      controllerBatch.text = "";
      controllerSn.text = "";
    });
  }

  ///批次号
  _btnBatch(GoodsDetailDto goods) {
    // 更新商品数量为当前用户选择的数量
    goods.unitQty = quantity;

    showDialog(
      context: context,
      builder: (context) => GoodsBindBatchPage(goods: goods),
    ).then((value) {
      setState(() {
        controllerBatch.text = goods.batchNo;
        widget.promotionCreditsDTO.batchNo = goods.batchNo;
        widget.promotionCreditsDTO.produceDate = goods.produceDate;
        widget.promotionCreditsDTO.expireDate = goods.expireDate;
        widget.promotionCreditsDTO.batchPrice = goods.batchPrice;
        widget.promotionCreditsDTO.costId = goods.costId;
        widget.promotionCreditsDTO.batchPrice = goods.batchPrice;
        widget.promotionCreditsDTO.pUserCode = goods.pUserCode;
      });
    });
  }

  ///序列号选择
  _btnSn(GoodsDetailDto goods) {
    // 更新商品数量为当前用户选择的数量
    goods.unitQty = quantity;

    //如果是批次号商品，且没有选择批次号，那么需要先选择批次号
    if (goods.batchenabled && !GoodsTool.isGoodsBindWithBatch(goods)) {
      HaloToast.showInfo(context, msg: "请先选择批次号");
      return;
    }
    //点击跳转到选择商品序列号弹窗
    showDialog(
      context: context,
      builder:
          (context) => GoodsBindSnPage(
            goods: goods,
            existSN: [...goods.serialNoList.map((e) => e.snno ?? "")],
            limitMaxCount: true,
          ),
    ).then((value) {
      if (value != null) {
        controllerSn.text = getSnStr(goods.serialNoList);
        widget.promotionCreditsDTO.serialNoList = goods.serialNoList;
        setState(() {});
      }
    });
  }
}
