import 'package:flutter/cupertino.dart';
import '../../../common/net/http_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../credits/entity/promotion_credits_dto.dart';
import '../../../credits/entity/promotion_credits_entity.dart';
import '../../../credits/entity/promotion_exchange_goods_requset.dart';
import 'package:halo_utils/http/base_model.dart';

class CreditsModel {
  ///获取积分兑换列表
  static Future<PromotionCreditsEntity?> getPromotionCreditsList(
    BuildContext context,
    int page,
    int pageSize,
    int sortType,
    int? betweenMin,
    int? betweenMax,
  ) async {
    if (page < 1) {
      page = 1;
    }
    if (pageSize <= 0) {
      pageSize = 20;
    }

    var data = {"pageIndex": page, "pageSize": pageSize, "queryParams": {}};

    Map queryParams = {"sortType": sortType};

    if (betweenMin != null) {
      queryParams.addAll({"betweenMin": betweenMin});
    }
    if (betweenMax != null) {
      queryParams.addAll({"betweenMax": betweenMax});
    }
    queryParams.addAll({"otypeId": int.parse(SpTool.getStoreInfo()!.otypeId!)});
    data["queryParams"] = queryParams;
    ResponseModel response = await HttpUtil.request(
      context,
      method: "/shopsale/promotion/getPromtionCredits",
      data: data,
    );
    if (response.data == null) {
      return null;
    }
    PromotionCreditsEntity entity = PromotionCreditsEntity();
    entity.list =
        (response.data?["list"] as List?)
            ?.map((json) => PromotionCreditsDTO.fromJson(json))
            .toList();
    entity.total = response.data["total"];
    return entity;
  }

  ///兑换
  static Future<Map?> exchangeCreditsGoods(
    BuildContext context,
    ExchangeGoodsRequest exchangeGoodsRequest,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: "/shopsale/promotion/exchangeCreditsGoods",
      data: exchangeGoodsRequest.toJson(),
    );
    return response.data;
  }

  ///批量兑换
  static Future<Map?> batchExchangeCreditsGoods(
    BuildContext context,
    List<ExchangeGoodsRequest> exchangeGoodsRequests,
  ) async {
    List<Map<String, dynamic>> requestsData =
        exchangeGoodsRequests.map((e) => e.toJson()).toList();
    ResponseModel response = await HttpUtil.request(
      context,
      method: "/shopsale/promotion/batchExchangeCreditsGoodsWithPost",
      data: requestsData,
    );
    return response.data;
  }
}
