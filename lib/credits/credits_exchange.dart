import 'dart:io';

import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import './../bill/entity/ptype/ptype_prop_dto.dart';
import './../bill/entity/ptype/ptype_serial_no_dto.dart';
import './../bill/entity/ptype_suit_model.dart';
import './../bill/model/bill_model.dart';
import './../bill/tool/scan_tool.dart';
import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../bill/model/ptype_model.dart';
import '../bill/tool/promotion/card.dart';
import '../common/login/login_center.dart';
import '../common/style/app_color_helper.dart';
import '../common/tool/dialog_util.dart';
import '../common/tool/sp_tool.dart';
import '../entity/system/permission_dto.dart';
import '../enum/bill_post_state.dart';
import '../enum/bill_type.dart';
import '../iconfont/icon_font.dart';
import '../plugin/secondary_screen_windows.dart';
import '../startup.dart';
import '../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../vip/utils/svip_util.dart';
import '../widgets/base/base_stateful_page.dart';
import '../widgets/halo_pos_label.dart';
import 'credits_commit.dart';
import 'credits_detail_item.dart';
import 'entity/promotion_credits_dto.dart';
import 'entity/promotion_credits_entity.dart';
import 'entity/promotion_exchange_goods_requset.dart';
import 'model/credits_model.dart';

///积分兑换页面
class CreditsExchangePages extends BaseStatefulPage {
  final VipWithLevelAssertsRightsCardDTO vipInfo;

  const CreditsExchangePages({Key? key, required this.vipInfo})
    : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _CreditsExchangePagesState();
}

class _CreditsExchangePagesState
    extends BaseStatefulPageState<CreditsExchangePages> {
  List<String> sortStr = ["默认排序", "积分从低到高", "积分从高到低"];
  List<String> sortCreditsStr = [
    "全部积分",
    "1-100",
    "101-1000",
    "1001-3000",
    "3001以上",
  ];
  int sorStrIndex = 0;
  int _sortCreditsStrIndex = 0;
  late PermissionDto permissionDto;

  // 已选商品列表
  List<PromotionCreditsDTO> selectedGoods = [];
  // 总积分
  int totalScore = 0;

  int get sortCreditsStrIndex {
    return _sortCreditsStrIndex;
  }

  set sortCreditsStrIndex(int value) {
    _sortCreditsStrIndex = value;
    if (_sortCreditsStrIndex == 0) {
      betweenMin = null;
      betweenMax = null;
      return;
    }
    if (_sortCreditsStrIndex == 1) {
      betweenMin = 1;
      betweenMax = 100;
      return;
    }
    if (_sortCreditsStrIndex == 2) {
      betweenMin = 101;
      betweenMax = 1000;
      return;
    }
    if (_sortCreditsStrIndex == 3) {
      betweenMin = 1001;
      betweenMax = 3000;
      return;
    }
    if (_sortCreditsStrIndex == 4) {
      betweenMin = 3001;
      betweenMax = null;
      return;
    }
  }

  int? betweenMin;
  int? betweenMax;

  int? page;
  int? pageSize;
  int? total;

  bool isHaveMore = false;
  int? clickIndex;

  PromotionCreditsEntity creditsEntity = PromotionCreditsEntity();
  List<PromotionCreditsDTO> list = [];

  @override
  void initState() {
    super.initState();
    page = 1;
    pageSize = 20;
    showScoreExchangeForWin([]);
    initData();
    permissionDto = SpTool.getPermission();
  }

  @override
  void dispose() {
    super.dispose();
    hideScoreExchangeForWin();
  }

  Future<void> initData() async {
    try {
      final value = await CreditsModel.getPromotionCreditsList(
        context,
        page!,
        pageSize!,
        sorStrIndex,
        betweenMin,
        betweenMax,
      );

      if (!mounted) return;

      setState(() {
        creditsEntity = value!;
        if (page == 1) {
          list.clear();
          isHaveMore = true;
          // 刷新时清空选择状态
          selectedGoods.clear();
          totalScore = 0;
        }
        total = int.parse(creditsEntity.total!);
        list.addAll(creditsEntity.list ?? []);
        list = list.where((element) => element.creditsName != null).toList();

        // 根据实际数据量判断是否还有更多数据
        // 如果当前页返回的数据为空，或者已加载的数据量达到总数，则没有更多数据
        if ((creditsEntity.list?.isEmpty ?? true) || list.length >= total!) {
          isHaveMore = false;
        } else {
          isHaveMore = true;
        }

        // 恢复已选商品的选择数量
        for (var item in list) {
          // 查找该商品是否在已选列表中
          var selectedItem = selectedGoods
              .cast<PromotionCreditsDTO?>()
              .firstWhere(
                (selected) =>
                    selected?.promotionPtypeId == item.promotionPtypeId,
                orElse: () => null, // 返回null作为默认值
              );

          // 如果找到了对应的已选商品，恢复其数量；否则设为0
          if (selectedItem != null) {
            item.localSelectQty = selectedItem.localSelectQty;
          } else {
            item.localSelectQty = 0;
          }
        }

        if (Platform.isWindows) {
          showScoreExchangeForWin(
            list
                .map(
                  (e) => ScoreExchangeForWindows(
                    name: e.ptypeGroup == 7 ? "储值金额：${e.price}元" : e.getName(),
                    score: e.preferential,
                    count: e.changeCount,
                    picUrl: e.picUrl,
                    ptypeGroup: e.ptypeGroup,
                    valueType: int.tryParse(e.valueType ?? ""),
                  ),
                )
                .toList(),
          );
        }
      });
    } catch (e) {
      // 处理异常情况
      if (mounted) {
        // 可以在这里添加错误日志记录或用户提示
        // 暂时静默处理，避免影响用户体验
      }
    }
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container(
      color: ColorUtil.stringColor("#F0F0F0"), // 页面背景色
      child: Column(
        children: [
          // 主内容区域
          Expanded(
            child: Container(
              margin: EdgeInsets.only(
                left: 20.w,
                right: 20.w,
                top: 20.w,
              ), // 减少顶部间距
              child: _buildMainContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    if (list.isEmpty) {
      return Center(
        child: HaloEmptyContainer(
          gravity: EmptyGravity.CENTER,
          image: Image.asset('assets/images/nodata.png'),
          title: "暂无数据",
          titleStyle: TextStyle(
            decoration: TextDecoration.none,
            fontSize: ScreenUtil().setSp(30),
            color: Colors.grey,
          ),
        ),
      );
    }

    return EasyRefresh(
      header: MaterialHeader(),
      footer: MaterialFooter(),
      behavior: Platform.isWindows ? WindowsScrollBehavior() : null,
      child: GridView.builder(
        padding: EdgeInsets.zero,
        itemCount: list.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 6, // 根据CSS显示6列
          crossAxisSpacing: 16.w, // 商品间距
          mainAxisSpacing: 16.w,
          childAspectRatio: 300 / 336, // 宽高比 300:336
        ),
        itemBuilder: (BuildContext context, int index) {
          return GestureDetector(
            onTap: () {
              clickIndex = index;
              setState(() {});

              // 保存原始状态，用于取消时恢复
              final originalState = {
                'localSelectQty': list[index].localSelectQty,
                'batchNo': list[index].batchNo,
                'serialNoList': List<PtypeSerialNoDto>.from(list[index].serialNoList),
                'produceDate': list[index].produceDate,
                'expireDate': list[index].expireDate,
                'batchPrice': list[index].batchPrice,
                'costId': list[index].costId,
                'pUserCode': list[index].pUserCode,
              };

              DialogUtil.showAlertDialog(
                context,
                child: CreditsCommit(
                  promotionCreditsDTO: list[index],
                  onCommit: (quantity) async {
                    if (isVipExpired(
                      widget.vipInfo.vip!.validDate,
                      vipType: widget.vipInfo.level!.vipType == true ? 1 : 0,
                    )) {
                      HaloToast.show(context, msg: "该会员已经过期，无法积分兑换");
                      return;
                    }
                    PromotionCreditsDTO promotionCreditsDTO = list[index];
                    promotionCreditsDTO.localSelectQty = quantity;

                    if (check(promotionCreditsDTO)) {
                      // 直接更新主页面状态
                      setState(() {
                        list[index].localSelectQty = quantity;
                      });

                      // 添加到下方选择列表
                      Navigator.pop(context);
                      addToSelectedList(promotionCreditsDTO, quantity);
                    }
                  },
                  onCancel: () {
                    // 恢复原始状态
                    list[index].localSelectQty = originalState['localSelectQty'] as int;
                    list[index].batchNo = originalState['batchNo'] as String?;
                    list[index].serialNoList.clear();
                    list[index].serialNoList.addAll(originalState['serialNoList'] as List<PtypeSerialNoDto>);
                    list[index].produceDate = originalState['produceDate'] as String?;
                    list[index].expireDate = originalState['expireDate'] as String?;
                    list[index].batchPrice = originalState['batchPrice'] as num;
                    list[index].costId = originalState['costId'] as String?;
                    list[index].pUserCode = originalState['pUserCode'] as String?;

                    clickIndex = null;
                    setState(() {});
                  },
                ),
              ).then((value) => {});
            },
            child: CreditsDetailItem(
              list[index],
              isSelect: _isItemSelected(list[index]),
              onExchangeClicked: () {
                if (isVipExpired(
                  widget.vipInfo.vip!.validDate,
                  vipType: widget.vipInfo.level!.vipType == true ? 1 : 0,
                )) {
                  HaloToast.show(context, msg: "该会员已经过期，无法积分兑换");
                  return;
                }
                PromotionCreditsDTO promotionCreditsDTO = list[index];
                if (check(promotionCreditsDTO)) {
                  addToSelectedList(promotionCreditsDTO, 1);
                }
              },
              onQuantityChanged: (quantity) {
                updateItemQuantity(list[index], quantity);
              },
            ),
          );
        },
      ),
      onRefresh: () async {
        page = 1;
        await initData();
      },
      onLoad: isHaveMore ? () async {
        page = page! + 1;
        await initData();
      } : null,
    );
  }

  ///拉取一张出库单
  Future<GoodsBillDto?> _getGoodsBill(
    BuildContext context,
    PromotionCreditsDTO promotionCreditsDTO,
  ) async {
    if (promotionCreditsDTO.ptypeGroup != 2 &&
        promotionCreditsDTO.ptypeGroup != 3) {
      return null;
    }

    //拉取一张新单据
    GoodsBillDto? goodsBillDto = await BillModel.getGoodsBill(
      context,
      "", //vchcode
      BillTypeData[BillType.SaleBill], //vchtype
      BillBusinessTypeString[BillBusinessType.SaleNormal],
    );
    if (goodsBillDto == null || goodsBillDto.vchcode == null) {
      if (mounted) {
        HaloToast.show(context, msg: "获取单据失败");
      }
      return null;
    }
    //拉去好的单据填入基本信息
    var loginUser = LoginCenter.getLoginUser();
    var storeInfo = SpTool.getStoreInfo()!;
    goodsBillDto.createEtypeId = loginUser.employeeId;
    goodsBillDto.vipCardId = widget.vipInfo.vip?.id;
    goodsBillDto.createEfullname = loginUser.user;
    goodsBillDto.createUserCode = loginUser.userCode ?? "";
    goodsBillDto.ktypeId = storeInfo.ktypeId;
    goodsBillDto.kfullname = storeInfo.ktypeName;
    goodsBillDto.btypeId = storeInfo.btypeId;
    goodsBillDto.bfullname = storeInfo.btypeName;
    goodsBillDto.otypeId = storeInfo.otypeId;
    goodsBillDto.ofullname = storeInfo.otypeFullname;
    goodsBillDto.etypeId = loginUser.employeeId;
    goodsBillDto.efullname = loginUser.user ?? "";
    goodsBillDto.source = "积分兑换";
    //这一坨不知道干嘛的
    goodsBillDto.freightAtypeName = "";
    goodsBillDto.freightBillNo = "";
    goodsBillDto.freightBtypeId = "0";
    goodsBillDto.freightBtypeName = "";
    goodsBillDto.freightaTypeTotal = "0";
    goodsBillDto.shareType = 0;
    goodsBillDto.dfullname = "";
    goodsBillDto.billType = "goodsBill";
    goodsBillDto.postState =
        BillPostStateString[BillPostState.PROCESS_COMPLETED];
    goodsBillDto.confirm = true; //避免各种奇奇怪怪的单据异常导致需要再次确认
    goodsBillDto.outDetail;
    //单据金额
    goodsBillDto.currencyBillTotal = "0";
    //处理套餐
    if (promotionCreditsDTO.ptypeGroup == 2) {
      if (!mounted) return null;
      //根据套餐id拉取套餐明细
      List<GoodsDetailDto>? comboGoodsList = await getComboById(
        context,
        promotionCreditsDTO.ptypeId!,
      );
      if (comboGoodsList == null || comboGoodsList.isEmpty) {
        if (mounted) HaloToast.show(context, msg: "获取套餐信息失败");
        return null;
      }
      goodsBillDto.outDetail.addAll(comboGoodsList);
    }
    //处理普通商品
    else if (promotionCreditsDTO.ptypeGroup == 3) {
      List<PtypePropDto> prop = [];
      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId1) &&
          promotionCreditsDTO.propvalueId1 != "0") {
        prop.add(
          PtypePropDto()
            ..propvalueId = promotionCreditsDTO.propvalueId1
            ..propvalueName = promotionCreditsDTO.propValueName1,
        );
      }
      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId2) &&
          promotionCreditsDTO.propvalueId2 != "0") {
        prop.add(
          PtypePropDto()
            ..propvalueId = promotionCreditsDTO.propvalueId2
            ..propvalueName = promotionCreditsDTO.propValueName2,
        );
      }

      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId3) &&
          promotionCreditsDTO.propvalueId3 != "0") {
        prop.add(
          PtypePropDto()
            ..propvalueId = promotionCreditsDTO.propvalueId3
            ..propvalueName = promotionCreditsDTO.propValueName3,
        );
      }

      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId4) &&
          promotionCreditsDTO.propvalueId4 != "0") {
        prop.add(
          PtypePropDto()
            ..propvalueId = promotionCreditsDTO.propvalueId4
            ..propvalueName = promotionCreditsDTO.propValueName4,
        );
      }

      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId5) &&
          promotionCreditsDTO.propvalueId5 != "0") {
        prop.add(
          PtypePropDto()
            ..propvalueId = promotionCreditsDTO.propvalueId5
            ..propvalueName = promotionCreditsDTO.propValueName5,
        );
      }

      if (StringUtil.isNotEmpty(promotionCreditsDTO.propvalueId6) &&
          promotionCreditsDTO.propvalueId6 != "0") {
        prop.add(
          PtypePropDto()
            ..propvalueId = promotionCreditsDTO.propvalueId6
            ..propvalueName = promotionCreditsDTO.propValueName6,
        );
      }

      GoodsDetailDto dto =
          GoodsDetailDto()
            ..prop = prop
            ..kfullname = storeInfo.ktypeName
            ..ktypeId = storeInfo.ktypeId
            ..pFullName = promotionCreditsDTO.creditsName ?? ""
            ..ptypeId = promotionCreditsDTO.ptypeId ?? "0"
            ..skuId = promotionCreditsDTO.skuId ?? "0"
            ..unitId = promotionCreditsDTO.unitId ?? "0"
            ..unitRate = num.tryParse(promotionCreditsDTO.unitRate ?? "1") ?? 1
            ..unitName = promotionCreditsDTO.unitName ?? ""
            ..unitQty = 1
            ..stockQty = 1
            ..fullbarcode = promotionCreditsDTO.fullbarcode ?? ""
            ..batchenabled = promotionCreditsDTO.batchenabled == 1
            ..picUrl = promotionCreditsDTO.picUrl
            ..costMode = promotionCreditsDTO.costMode ?? 0
            ..snenabled = promotionCreditsDTO.snenabled ?? 0
            ..batchNo = promotionCreditsDTO.batchNo ?? ""
            ..serialNoList = promotionCreditsDTO.serialNoList
            ..produceDate = promotionCreditsDTO.produceDate
            ..expireDate = promotionCreditsDTO.expireDate
            ..pUserCode = promotionCreditsDTO.pUserCode
            ..costId = promotionCreditsDTO.costId
            ..batchPrice = promotionCreditsDTO.batchPrice;
      goodsBillDto.outDetail.add(dto);
    } else {
      return null;
    }
    if (!mounted) return null;
    //处理自动带出批次
    // await ScanTool.getPtypeAutoBatch(goodsBillDto.outDetail, context);
    for (int i = 0; i < goodsBillDto.outDetail.length; i++) {
      GoodsDetailDto dto = goodsBillDto.outDetail[i];
      dto.currencyPrice = 0;
      dto.currencyTaxTotal = 0;
      dto.currencyTotal = 0;
      dto.currencyDisedPrice = 0;
      dto.currencyDisedTaxedPrice = 0;
      dto.currencyDisedTaxedTotal = 0;
      dto.currencyDisedTotal = 0;
      dto.preferentialDiscount = 0;
      dto.memo = "促销：积分兑换";
      // dto.costPrice = 0;
      //排除套餐行
      if (dto.comboRow) {
        continue;
      }
      if (!mounted) return null;
      //处理批次和序列号，如果有任意商品没有选择序列号，则中断流程
      // List<GoodsDetailDto>? result = await ScanTool.handleScanResult(
      //     context, dto, goodsBillDto.outDetail, BillType.SaleBill,
      //     snLimitGoodsCount: true);
      // if (result?.any((element) => element.batchNo.isEmpty) == true) {
      //   if (mounted) HaloToast.show(context, msg: "请选择批次号");
      //   return null;
      // }
    }
    return goodsBillDto;
  }

  ///获取套餐
  Future<List<GoodsDetailDto>?> getComboById(
    BuildContext context,
    String comboId,
  ) async {
    PtypeSuitModel? combo = await BillModel.getComboById(context, comboId);
    if (null != combo) {
      combo.count = 1;
      return ScanTool.transformComboToGoodsList(combo, BillType.SaleBill);
    }
    return null;
  }

  @override
  String getActionBarTitle() => "";

  @override
  buildAppBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(81.w), // 根据CSS高度81px
      child: Container(
        width: double.infinity,
        height: 81.w,
        color: Colors.white, // 白色背景
        child: Row(
          children: [
            // 返回按钮
            Container(
              width: 25.w,
              height: 25.w,
              margin: EdgeInsets.only(left: 33.w, top: 25.w),
              child: GestureDetector(
                onTap: () => NavigateUtil.pop(context),
                child: IconFont(
                  IconNames.ngp_left_back,
                  size: 25.w,
                  color: "#333333",
                ),
              ),
            ),
            // 标题
            Expanded(
              child: Container(
                alignment: Alignment.centerRight,
                padding: EdgeInsets.only(left: 150.w),
                margin: EdgeInsets.only(top: 15.w),
                child: HaloPosLabel(
                  "积分兑换",
                  textStyle: TextStyle(
                    color: ColorUtil.stringColor("#333333"),
                    fontSize: 26.w,
                    fontWeight: FontWeight.w600,
                    height: 45.w / 32.w,
                  ),
                ),
              ),
            ),
            // 会员信息
            Flexible(
              child: Container(
                margin: EdgeInsets.only(right: 22.w, top: 24.w),
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 会员名称部分，设置最大宽度避免过长
                    Flexible(
                      child: Text(
                        "${widget.vipInfo.vip?.name ?? ""}（",
                        style: TextStyle(
                          fontSize: 24.w,
                          color: ColorUtil.stringColor("#494848"),
                          fontWeight: FontWeight.normal,
                          height: 1.0, // 调整行高避免底部被截断
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        textAlign: TextAlign.right,
                      ),
                    ),
                    // 积分信息部分，确保始终显示完整
                    Text(
                      "可用积分：",
                      style: TextStyle(
                        fontSize: 24.w,
                        color: ColorUtil.stringColor("#494848"),
                        fontWeight: FontWeight.normal,
                        height: 1.0, // 调整行高避免底部被截断
                      ),
                    ),
                    Text(
                      "${widget.vipInfo.asserts?.availableScore ?? 0}",
                      style: TextStyle(
                        fontSize: 24.w,
                        color: ColorUtil.stringColor("#FF4141"),
                        fontWeight: FontWeight.w500,
                        height: 1.0, // 调整行高避免底部被截断
                      ),
                    ),
                    Text(
                      "分）",
                      style: TextStyle(
                        fontSize: 24.w,
                        color: ColorUtil.stringColor("#494848"),
                        fontWeight: FontWeight.normal,
                        height: 1.0, // 调整行高避免底部被截断
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget buildTopBody(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 140.w, // 增加高度
      margin: EdgeInsets.only(left: 20.w, right: 20.w, top: 25.w), // 适当增加顶部间距
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.w),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // 排序选项
          Container(
            height: 70.w, // 增加高度
            padding: EdgeInsets.only(left: 56.w, top: 5.w), // 增加顶部padding
            child: Row(
              children: [
                _buildSortOption("默认排序", 0, sorStrIndex),
                SizedBox(width: 86.w),
                _buildSortOption("积分从低到高", 1, sorStrIndex),
                SizedBox(width: 91.w),
                _buildSortOption("积分从高到低", 2, sorStrIndex),
              ],
            ),
          ),
          // 分割线
          Container(
            width: double.infinity,
            height: 1.w,
            color: ColorUtil.stringColor("#DBDBDB"),
          ),
          // 积分筛选选项
          Container(
            height: 59.w, // 减少高度
            padding: EdgeInsets.only(left: 56.w, top: 12.w), // 减少顶部padding
            child: Row(
              children: [
                _buildSortOption("全部积分", 0, sortCreditsStrIndex),
                SizedBox(width: 87.w),
                _buildSortOption("1-100", 1, sortCreditsStrIndex),
                SizedBox(width: 165.w),
                _buildSortOption("101-1000", 2, sortCreditsStrIndex),
                SizedBox(width: 128.w),
                _buildSortOption("1001-3000", 3, sortCreditsStrIndex),
                SizedBox(width: 132.w),
                _buildSortOption("3001以上", 4, sortCreditsStrIndex),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortOption(String title, int index, int currentIndex) {
    bool isSelected = index == currentIndex;
    return GestureDetector(
      onTap: () async {
        setState(() {
          // 更精确的判断逻辑：根据具体的标题来区分
          if (title == "全部积分" ||
              title == "1-100" ||
              title == "101-1000" ||
              title == "1001-3000" ||
              title == "3001以上") {
            sortCreditsStrIndex = index;
          } else {
            sorStrIndex = index;
          }
          page = 1;
          // 切换排序时清空选择状态
          selectedGoods.clear();
          totalScore = 0;
        });
        await initData();
      },
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18.w,
          color:
              isSelected
                  ? ColorUtil.stringColor("#4679FC")
                  : ColorUtil.stringColor("#494848"),
          fontWeight: FontWeight.normal,
          height: 1.2,
        ),
      ),
    );
  }

  @override
  Widget buildBottomBody(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 110.w,
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
        children: [
          // 商品信息和积分显示
          Expanded(
            child: Container(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: EdgeInsets.only(right: 19.w),
                child:
                    selectedGoods.isEmpty
                        ? Text(
                          "请选择要兑换的商品",
                          style: TextStyle(
                            fontSize: 26.w,
                            color: ColorUtil.stringColor("#999999"),
                            fontWeight: FontWeight.normal,
                            height: 1.2,
                          ),
                        )
                        : RichText(
                          text: TextSpan(
                            text: "共 ${_getTotalQuantity()} 件商品，需 ",
                            style: TextStyle(
                              fontSize: 26.w,
                              color: ColorUtil.stringColor("#333333"),
                              fontWeight: FontWeight.normal,
                              height: 1.2, // 调整行高
                            ),
                            children: [
                              TextSpan(
                                text: "$totalScore",
                                style: TextStyle(
                                  fontSize: 26.w,
                                  color: ColorUtil.stringColor("#FF7800"),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              TextSpan(
                                text: " 积分",
                                style: TextStyle(
                                  fontSize: 26.w,
                                  color: ColorUtil.stringColor("#333333"),
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        ),
              ),
            ),
          ),
          // 确认兑换按钮
          GestureDetector(
            onTap: selectedGoods.isEmpty ? null : _exchangeGoods,
            child: Container(
              width: 369.w,
              height: 80.w,
              margin: EdgeInsets.only(right: 20.w),
              decoration: BoxDecoration(
                color:
                    selectedGoods.isEmpty
                        ? ColorUtil.stringColor("#CCCCCC")
                        : ColorUtil.stringColor("#4679FC"),
                borderRadius: BorderRadius.circular(6.w),
              ),
              child: Center(
                child: Text(
                  "确认兑换",
                  style: TextStyle(
                    fontSize: 24.w,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                    height: 1.2, // 调整行高
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ExchangeGoodsRequest buildRequestData(
    PromotionCreditsDTO promotionCreditsDTO,
  ) {
    ExchangeGoodsRequest request = ExchangeGoodsRequest();
    request.vipId = widget.vipInfo.vip?.id;
    request.ktypeId = SpTool.getStoreInfo()!.ktypeId;
    request.etypeId = LoginCenter.getLoginUser().employeeId;
    request.otypeId = SpTool.getStoreInfo()!.otypeId;
    request.btypeId = SpTool.getStoreInfo()!.btypeId;
    request.propValueId1 = promotionCreditsDTO.propvalueId1;
    request.propValueId2 = promotionCreditsDTO.propvalueId2;
    request.propValueId3 = promotionCreditsDTO.propvalueId3;
    request.propValueId4 = promotionCreditsDTO.propvalueId4;
    request.propValueId5 = promotionCreditsDTO.propvalueId5;
    request.propValueId6 = promotionCreditsDTO.propvalueId6;

    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName1)) {
      request.propValueName1 = promotionCreditsDTO.propValueName1;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName2)) {
      request.propValueName2 = promotionCreditsDTO.propValueName2;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName3)) {
      request.propValueName3 = promotionCreditsDTO.propValueName3;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName4)) {
      request.propValueName4 = promotionCreditsDTO.propValueName4;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName5)) {
      request.propValueName5 = promotionCreditsDTO.propValueName5;
    }
    if (StringUtil.isNotEmpty(promotionCreditsDTO.propValueName6)) {
      request.propValueName6 = promotionCreditsDTO.propValueName6;
    }

    request.ptypeGroup = promotionCreditsDTO.ptypeGroup;
    request.pid = promotionCreditsDTO.pid;
    request.score = Decimal.parse(promotionCreditsDTO.preferential!);
    request.amount = Decimal.parse(promotionCreditsDTO.price.toString());
    request.skuId = promotionCreditsDTO.skuId;
    request.unitId = promotionCreditsDTO.unitId;
    request.promotionPtypeId = promotionCreditsDTO.promotionPtypeId;
    request.changeCount = promotionCreditsDTO.changeCount;
    request.unitRate = promotionCreditsDTO.unitRate;
    request.ptypeId = promotionCreditsDTO.ptypeId;
    request.fullbarcode = promotionCreditsDTO.fullbarcode;
    request.quantity = promotionCreditsDTO.localSelectQty;
    return request;
  }

  bool check(PromotionCreditsDTO promotionCreditsDTO) {
    if (widget.vipInfo.asserts!.availableScore! <
        int.parse(promotionCreditsDTO.preferential!)) {
      HaloToast.showError(context, msg: "会员积分不足!");
      return false;
    }
    return true;
  }

  // 检查商品是否在已选列表中
  bool _isItemSelected(PromotionCreditsDTO item) {
    return item.localSelectQty > 0;
  }

  // 添加商品到已选列表
  void addToSelectedList(PromotionCreditsDTO item, int quantity) {
    setState(() {
      // 先更新原始列表中的数量
      int originalIndex = list.indexWhere(
        (element) => element.promotionPtypeId == item.promotionPtypeId,
      );
      if (originalIndex != -1) {
        list[originalIndex].localSelectQty = quantity;
      }

      // 检查是否已经存在于已选列表
      int index = selectedGoods.indexWhere(
        (element) => element.promotionPtypeId == item.promotionPtypeId,
      );

      if (index == -1) {
        // 不存在，添加新商品
        if (quantity > 0) {
          // 只有数量大于0才添加
          PromotionCreditsDTO newItem = PromotionCreditsDTO(
            id: item.id,
            profileId: item.profileId,
            ptypeGroup: item.ptypeGroup,
            ptypeId: item.ptypeId,
            costMode: item.costMode,
            batchenabled: item.batchenabled,
            snenabled: item.snenabled,
            skuId: item.skuId,
            unitId: item.unitId,
            price: item.price,
            preferential: item.preferential,
            pid: item.pid,
            changeCount: item.changeCount,
            creditsName: item.creditsName,
            picUrl: item.picUrl,
            propId1: item.propId1,
            propId2: item.propId2,
            propvalueId1: item.propvalueId1,
            propvalueId2: item.propvalueId2,
            propvalueId3: item.propvalueId3,
            propvalueId4: item.propvalueId4,
            propvalueId5: item.propvalueId5,
            propvalueId6: item.propvalueId6,
            promotionPtypeId: item.promotionPtypeId,
            valueType: item.valueType,
            propValueName1: item.propValueName1,
            propValueName2: item.propValueName2,
            propValueName3: item.propValueName3,
            propValueName4: item.propValueName4,
            propValueName5: item.propValueName5,
            propValueName6: item.propValueName6,
            unitRate: item.unitRate,
            unitName: item.unitName,
            fullbarcode: item.fullbarcode,
            protectDays: item.protectDays,
            produceDate: item.produceDate,
            expireDate: item.expireDate,
            batchPrice: item.batchPrice,
            costId: item.costId,
            pUserCode: item.pUserCode,
            batchNo: item.batchNo,
            serialNoList: item.serialNoList,
          );
          newItem.localSelectQty = quantity;
          selectedGoods.add(newItem);
        }
      } else {
        // 已存在，更新数量和批次序列号信息
        selectedGoods[index].localSelectQty = quantity;

        // 同时更新批次和序列号信息
        selectedGoods[index].batchNo = item.batchNo;
        selectedGoods[index].produceDate = item.produceDate;
        selectedGoods[index].expireDate = item.expireDate;
        selectedGoods[index].batchPrice = item.batchPrice;
        selectedGoods[index].costId = item.costId;
        selectedGoods[index].pUserCode = item.pUserCode;
        selectedGoods[index].serialNoList = List.from(item.serialNoList);

        // 如果数量为0，从列表中移除
        if (quantity == 0) {
          selectedGoods.removeAt(index);
        }
      }

      // 更新总积分
      _calculateTotalScore();
    });
  }

  // 更新商品数量
  void updateItemQuantity(PromotionCreditsDTO item, int quantity) {
    setState(() {
      // 先更新列表中的数量
      int originalIndex = list.indexWhere(
        (element) => element.promotionPtypeId == item.promotionPtypeId,
      );
      if (originalIndex != -1) {
        list[originalIndex].localSelectQty = quantity;
      }

      // 查找已选列表中的项
      int index = selectedGoods.indexWhere(
        (element) => element.promotionPtypeId == item.promotionPtypeId,
      );

      if (index != -1) {
        // 更新数量和批次序列号信息
        selectedGoods[index].localSelectQty = quantity;

        // 同时更新批次和序列号信息
        selectedGoods[index].batchNo = item.batchNo;
        selectedGoods[index].produceDate = item.produceDate;
        selectedGoods[index].expireDate = item.expireDate;
        selectedGoods[index].batchPrice = item.batchPrice;
        selectedGoods[index].costId = item.costId;
        selectedGoods[index].pUserCode = item.pUserCode;
        selectedGoods[index].serialNoList = List.from(item.serialNoList);

        // 如果数量为0，从列表中移除
        if (quantity == 0) {
          selectedGoods.removeAt(index);
        }
      } else if (quantity > 0) {
        // 不存在但数量大于0，添加到列表
        addToSelectedList(item, quantity);
        return; // addToSelectedList会计算总积分，不需要再次计算
      }

      // 更新总积分
      _calculateTotalScore();
    });
  }

  // 计算总积分
  void _calculateTotalScore() {
    totalScore = 0;
    for (var item in selectedGoods) {
      totalScore += int.parse(item.preferential!) * item.localSelectQty;
    }
  }

  // 计算总数量（按商品数量，不是种类）
  int _getTotalQuantity() {
    int totalQuantity = 0;
    for (var item in selectedGoods) {
      totalQuantity += item.localSelectQty;
    }
    return totalQuantity;
  }

  // 执行兑换操作
  Future<void> _exchangeGoods() async {
    if (selectedGoods.isEmpty) {
      HaloToast.show(context, msg: "请先选择商品");
      return;
    }

    if (widget.vipInfo.asserts!.availableScore! < totalScore) {
      HaloToast.show(context, msg: "会员积分不足");
      return;
    }

    // 准备批量兑换请求
    List<ExchangeGoodsRequest> requests = [];
    // 用于记录商品或套餐类型的商品清单
    List<GoodsBillDto> goodsBillList = [];

    // 准备兑换数据
    for (var item in selectedGoods) {
      ExchangeGoodsRequest request = buildRequestData(item);
      request.quantity = item.localSelectQty; // 设置兑换数量

      // 对于商品或套餐类型，需要准备单据
      if (item.ptypeGroup == 3 || item.ptypeGroup == 2) {
        GoodsBillDto? goodsBillDto = await _getGoodsBill(context, item);
        if (goodsBillDto == null) {
          continue;
        } else {
          goodsBillDto.memo = creditsExchange;
          goodsBillDto.date = DateUtil.formatDate(DateTime.now());
          goodsBillDto.storeManagerId = SpTool.getStoreInfo()?.ownerId ?? "";
          request.goodsBill = goodsBillDto;
          goodsBillList.add(goodsBillDto);
        }
      }

      request.cashierId = SpTool.getCashierInfo().id;
      requests.add(request);
    }

    if (requests.isEmpty) {
      return;
    }

    // 调用批量兑换接口
    if (!mounted) return;
    var value = await CreditsModel.batchExchangeCreditsGoods(context, requests);

    if (value != null && value["code"] == "500" && mounted) {
      showDialog(
        context: context,
        builder: (context) => _FailDialog(value["message"]),
      );
      return;
    } else {
      // 更新库存，只对商品或套餐类型更新
      for (var goodsBill in goodsBillList) {
        PtypeModel.changePtypeStockQtyByGoodsBill(
          outDetail: goodsBill.outDetail,
        );
      }

      // 更新积分和兑换次数
      int totalDeductedScore = 0;
      setState(() {
        // 更新原始列表中的兑换次数和数量
        for (var selectedItem in selectedGoods) {
          int originalIndex = list.indexWhere(
            (element) =>
                element.promotionPtypeId == selectedItem.promotionPtypeId,
          );
          if (originalIndex != -1) {
            // 更新兑换次数
            list[originalIndex].changeCount += selectedItem.localSelectQty;
            // 清零数量
            list[originalIndex].localSelectQty = 0;
            // 清除批次和序列号相关信息
            list[originalIndex].batchNo = null;
            list[originalIndex].serialNoList.clear();
            list[originalIndex].produceDate = null;
            list[originalIndex].expireDate = null;
            list[originalIndex].batchPrice = 0;
            list[originalIndex].costId = null;
            list[originalIndex].pUserCode = null;
          }
          // 计算扣减积分
          totalDeductedScore +=
              int.parse(selectedItem.preferential!) *
              selectedItem.localSelectQty;
        }

        // 更新会员积分
        widget.vipInfo.asserts!.availableScore =
            widget.vipInfo.asserts!.availableScore! - totalDeductedScore;

        // 清空已选列表
        selectedGoods.clear();
        totalScore = 0;
      });

      if (mounted) {
        HaloToast.showSuccess(context, msg: "兑换成功");
      }
    }
  }

  @override
  Future<void> onInitState() async {}
}

///兑换失败弹窗
class _FailDialog extends StatelessWidget {
  final String message;

  const _FailDialog(this.message, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      child: SizedBox(
        width: 488.w,
        height: 444.h,
        child: Dialog(
          insetPadding: EdgeInsets.zero,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.w),
            ),
            padding: EdgeInsets.only(
              top: 50.h,
              bottom: 60.h,
              left: 46.w,
              right: 46.w,
            ),
            child: Column(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Icon(Icons.error, color: Colors.red, size: 72.w),
                      Padding(
                        padding: EdgeInsets.only(top: 22.h, bottom: 14.h),
                        child: Text(
                          "兑换失败",
                          style: TextStyle(
                            color: const Color(0xFF333333),
                            fontSize: 30.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Text(
                        message,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: const Color(0xFF666666),
                          fontSize: 24.sp,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    width: 236.w,
                    height: 66.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.w),
                      border: Border.all(
                        color: const Color(0xFF999999),
                        width: 2.w,
                      ),
                    ),
                    child: Text(
                      "关闭",
                      style: TextStyle(
                        color: const Color(0xFF333333),
                        fontSize: 26.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
