import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../iconfont/icon_font.dart';
import 'entity/promotion_credits_dto.dart';
import 'widget/credits_quantity_stepper.dart';

class CreditsDetailItem extends StatefulWidget {
  final PromotionCreditsDTO promotionCreditsDTO;
  final bool isSelect;
  final Function(int)? onQuantityChanged;
  final VoidCallback? onExchangeClicked;

  const CreditsDetailItem(
    this.promotionCreditsDTO, {
    Key? key,
    this.isSelect = false,
    this.onQuantityChanged,
    this.onExchangeClicked,
  }) : super(key: key);

  @override
  State<CreditsDetailItem> createState() => _CreditsDetailItemState();
}

class _CreditsDetailItemState extends State<CreditsDetailItem> {
  late int _quantity;

  @override
  void initState() {
    super.initState();
    _quantity =
        widget.promotionCreditsDTO.localSelectQty > 0
            ? widget.promotionCreditsDTO.localSelectQty
            : 1;
  }

  @override
  void didUpdateWidget(CreditsDetailItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当外部数据变化时，更新内部状态
    setState(() {
      _quantity =
          widget.promotionCreditsDTO.localSelectQty > 0
              ? widget.promotionCreditsDTO.localSelectQty
              : 1;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 当数量大于0时显示选中状态
    bool isSelected =
        widget.isSelect || widget.promotionCreditsDTO.localSelectQty > 0;

    return Stack(
      children: [
        Container(
          width: 300.w,
          height: 320.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.w),
            color: Colors.white,
            border:
                isSelected
                    ? Border.all(
                      color: ColorUtil.stringColor("#4679FC"),
                      width: 2.w,
                    )
                    : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 商品图片区域
              Container(
                width: 175.w,
                height: 115.w,
                margin: EdgeInsets.only(top: 45.w, left: 67.w), // 按CSS精确定位
                child: PromotionCreditsDTO.getPic(
                  ptypeGroup: widget.promotionCreditsDTO.ptypeGroup,
                  picUrl: widget.promotionCreditsDTO.picUrl ?? "",
                  valueType: int.tryParse(
                    widget.promotionCreditsDTO.valueType ?? "",
                  ),
                ),
              ),
              // 商品名称
              Container(
                width: 264.w,
                margin: EdgeInsets.only(
                  top: 20.w,
                  left: 16.w,
                ), // 减少top margin避免溢出
                child: Text(
                  widget.promotionCreditsDTO.ptypeGroup! == 7
                      ? "储值金额：${widget.promotionCreditsDTO.price}元"
                      : widget.promotionCreditsDTO.getName()!,
                  style: TextStyle(
                    fontSize: 22.w,
                    color: ColorUtil.stringColor("#333333"),
                    fontWeight: FontWeight.normal,
                    height: 30.w / 22.w, // 按CSS精确行高
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // 已兑换次数
              Container(
                width: 150.w, // 增加宽度避免遮挡
                margin: EdgeInsets.only(left: 16.w, top: 8.w), // 增加顶部间距
                child: Text(
                  "已兑换${widget.promotionCreditsDTO.changeCount}",
                  style: TextStyle(
                    fontSize: 16.w, // 减小字体避免遮挡
                    color: ColorUtil.stringColor("#999999"),
                    fontWeight: FontWeight.normal,
                    height: 1.2, // 简化行高
                  ),
                ),
              ),
              // 积分和兑换按钮
              Container(
                width: 269.w,
                height: 36.w,
                margin: EdgeInsets.only(
                  left: 16.w,
                  top: 12.w, // 增加与已兑换文字的间距
                  bottom: 8.w, // 进一步减少底部间距
                ), // 减少底部空间
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 2.w), // 积分文字的垂直对齐
                      child: Text(
                        "${widget.promotionCreditsDTO.preferential!}积分",
                        style: TextStyle(
                          fontSize: 20.w,
                          color: ColorUtil.stringColor("#FF4141"),
                          fontWeight: FontWeight.w500,
                          height: 28.w / 20.w, // 按CSS精确行高
                        ),
                      ),
                    ),
                    widget.promotionCreditsDTO.localSelectQty > 0
                        ? _buildQuantitySelector()
                        : _buildExchangeButton(),
                  ],
                ),
              ),
            ],
          ),
        ),
        // 选中状态的勾选图标
        if (isSelected)
          Positioned(
            top: 0,
            right: 0,
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(8.w), // 保持卡片圆角
              ),
              child: IconFont(IconNames.gouxuan, size: 50.w, color: '#4679FC'),
            ),
          ),
      ],
    );
  }

  Widget _buildExchangeButton() {
    return Container(
      width: 36.w,
      height: 36.w,
      decoration: BoxDecoration(
        color: ColorUtil.stringColor("#4679FC"),
        borderRadius: BorderRadius.circular(18.w),
      ),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _quantity = 1;
            widget.promotionCreditsDTO.localSelectQty = 1;
          });
          if (widget.onQuantityChanged != null) {
            widget.onQuantityChanged!(1);
          }
          if (widget.onExchangeClicked != null) {
            widget.onExchangeClicked!();
          }
        },
        child: Center(
          child: Text(
            "兑",
            style: TextStyle(
              fontSize: 16.w, // 减小字体大小
              color: Colors.white,
              fontWeight: FontWeight.w500,
              height: 1.2, // 调整行高比例
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return CreditsQuantityStepper(
      quantity: _quantity,
      onQuantityChanged: (newQuantity) {
        // 检查序列号数量限制
        if (_checkSerialNumberQuantity(newQuantity)) {
          setState(() {
            _quantity = newQuantity;
          });
          widget.promotionCreditsDTO.localSelectQty = _quantity;
          if (widget.onQuantityChanged != null) {
            widget.onQuantityChanged!(_quantity);
          }
        }
      },
      buttonSize: 36.w,
      spacing: 8.w,
      fontSize: 20.0,
      decreaseButtonBorderColor: ColorUtil.stringColor("#CECECE"),
      increaseButtonColor: ColorUtil.stringColor("#4679FC"),
      textColor: ColorUtil.stringColor("#333333"),
    );
  }

  /// 检查序列号数量限制
  /// 当数量为1时点击减少不需要判断（清空操作）
  /// 返回true表示可以修改数量，false表示不能修改
  bool _checkSerialNumberQuantity(int newQuantity) {
    // 当数量为0时，清空批次和序列号信息
    if (newQuantity == 0) {
      _clearBatchAndSerialInfo();
      return true;
    }

    // 如果不是序列号商品，直接允许修改
    if (widget.promotionCreditsDTO.snenabled == 0) {
      return true;
    }

    // 当前已选择的序列号数量
    int currentSerialCount = widget.promotionCreditsDTO.serialNoList.length;

    // 如果没有选择序列号，直接允许修改
    if (currentSerialCount == 0) {
      return true;
    }

    // 检查新数量是否小于已选择的序列号数量
    if (newQuantity < currentSerialCount) {
      HaloToast.showInfo(context,
        msg: "商品数量不能小于序列号数量($currentSerialCount)，请先减少序列号");
      return false;
    }

    return true;
  }

  /// 清空批次和序列号信息
  void _clearBatchAndSerialInfo() {
    widget.promotionCreditsDTO.serialNoList.clear();
    widget.promotionCreditsDTO.batchNo = null;
    widget.promotionCreditsDTO.produceDate = null;
    widget.promotionCreditsDTO.expireDate = null;
    widget.promotionCreditsDTO.batchPrice = 0;
    widget.promotionCreditsDTO.costId = null;
    widget.promotionCreditsDTO.pUserCode = null;
  }
}
