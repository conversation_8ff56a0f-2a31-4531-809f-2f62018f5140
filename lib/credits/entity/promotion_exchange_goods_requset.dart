import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import '../../../bill/entity/goods_bill.dto.dart';

class ExchangeGoodsRequest {
  //会员id
  String? vipId;

  //促销商品id
  String? promotionPtypeId;

  //仓库id
  String? ktypeId;

  //门店
  String? otypeId;

  //往来单位
  String? btypeId;

  //经手人id
  String? etypeId;

  //"属性值id1
  String? propValueId1;

  //属性值id2
  String? propValueId2;

  String? propValueId3;

  String? propValueId4;

  String? propValueId5;

  String? propValueId6;

  String? propValueName1;

  //属性值id2
  String? propValueName2;

  String? propValueName3;

  //属性值id2
  String? propValueName4;

  String? propValueName5;

  //属性值id2
  String? propValueName6;

  //商品id/权益卡id/优惠券id
  String? pid;

  String? ptypeId;

  //skuId
  String? skuId;

  String? fullbarcode;

  //单位id
  String? unitId;

  String? unitRate;

  //储值金额
  Decimal? amount;

  //积分
  Decimal? score;

  //兑换类型
  int? ptypeGroup;

  //已兑换次数
  int? changeCount;

  // 兑换数量
  int? quantity;

  ///单据
  GoodsBillDto? goodsBill;

  String? cashierId;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['vipId'] = this.vipId;
    data['ktypeId'] = this.ktypeId;
    data['etypeId'] = this.etypeId;
    data['btypeId'] = this.btypeId;
    data['propValueId1'] = this.propValueId1;
    data['propValueId2'] = this.propValueId2;
    data['propValueId3'] = this.propValueId3;
    data['propValueId4'] = this.propValueId4;
    data['propValueId5'] = this.propValueId5;
    data['propValueId6'] = this.propValueId6;
    data['pid'] = this.pid;
    data['fullbarcode'] = this.fullbarcode;
    data['ptypeId'] = this.ptypeId;
    data['skuId'] = this.skuId;
    data['unitId'] = this.unitId;
    data['amount'] = this.amount;
    data['score'] = this.score;
    data['changeCount'] = this.changeCount;
    data['ptypeGroup'] = this.ptypeGroup;
    data['promotionPtypeId'] = this.promotionPtypeId;
    data['otypeId'] = this.otypeId;
    data['propValueName1'] = this.propValueName1;
    data['propValueName2'] = this.propValueName2;
    data['propValueName3'] = this.propValueName3;
    data['propValueName4'] = this.propValueName4;
    data['propValueName5'] = this.propValueName5;
    data['propValueName6'] = this.propValueName6;
    data['unitRate'] = this.unitRate;
    data['goodsBill'] = this.goodsBill?.toJson();
    data['cashierId'] = this.cashierId;
    data['quantity'] = this.quantity;
    return data;
  }
}
