import 'package:flutter/cupertino.dart';
import '../../../common/tool/sp_tool.dart';
import 'package:haloui/widget/halo_image.dart';

import '../../bill/entity/ptype/ptype_serial_no_dto.dart';

class PromotionCreditsDTO {
  String? id;
  String? promotionPtypeId;
  String? profileId;
  int? ptypeGroup;
  String? ptypeId;
  int? costMode;
  int? batchenabled;
  int? snenabled;
  String? skuId;
  String? unitId;
  String? unitRate;
  double? price;
  String? preferential;
  String? pid;
  int changeCount = 0;
  int localSelectQty = 0;
  String? creditsName;
  String? picUrl;
  String? propId1;
  String? propId2;
  String? propvalueId1;
  String? propvalueId2;
  String? propvalueId3;
  String? propvalueId4;
  String? propvalueId5;
  String? propvalueId6;
  String? propValueName1;
  String? propValueName2;
  String? propValueName3;
  String? propValueName4;
  String? propValueName5;
  String? propValueName6;
  String? unitName;
  String? valueType;
  String? fullbarcode;
  int? page;
  int? pageSize;
  num? protectDays;

  ///生产日期
  String? produceDate;

  ///过期日期
  String? expireDate;

  ///序列号
  List<PtypeSerialNoDto> serialNoList = [];

  ///批次
  String? batchNo;

  String? pUserCode;
  num batchPrice = 0;

  ///批次个别计价id
  String? costId;

  String? getName() {
    if (ptypeGroup == 3) {
      String name =
          (creditsName ?? "") +
          ((propValueName1 != null && propValueName1!.isNotEmpty ? " $propValueName1:" : "") +
              (propValueName2 != null && propValueName2!.isNotEmpty ? " $propValueName2:" : "") +
              (propValueName3 != null && propValueName3!.isNotEmpty ? " $propValueName3:" : "") +
              (propValueName4 != null && propValueName4!.isNotEmpty ? " $propValueName4:" : "") +
              (propValueName5 != null && propValueName5!.isNotEmpty ? " $propValueName5:" : "") +
              (propValueName6 != null && propValueName6!.isNotEmpty ? " $propValueName6:" : "") +
              (unitName != null && unitName!.isNotEmpty ? "（$unitName）" : ""));
      // if (unitName == "") {
      //   name = name.substring(0, name.length - 1);
      // }
      return name;
    }

    return creditsName;
  }

  static Widget getPic({
    required int? ptypeGroup,
    required String? picUrl,
    required int? valueType,
  }) {
    if (ptypeGroup == 3 || ptypeGroup == 2) {
      //商品
      return Center(child: HaloImage(picUrl));
    } else if (ptypeGroup == 5) {
      //权益卡
      return Image.asset('assets/images/quanyika.png');
    } else if (ptypeGroup == 6) {
      //优惠券     value_type      tinyint(1)      default 0                    not null comment '0等级权益卡、1普通权益卡、2代金券、3折扣券、4礼品券',
      switch (valueType) {
        case 2:
          return Image.asset('assets/images/daijinquan.png');
          break;
        case 3:
          return Image.asset('assets/images/dikouquan.png');
          break;
        case 4:
          return Image.asset('assets/images/lipingquan.png');
          break;
      }
    } else if (ptypeGroup == 7) {
      return Image.asset('assets/images/chuzhika.png');
    }
    return Image.asset('assets/images/nodata.png');
  }

  PromotionCreditsDTO({
    this.id,
    this.profileId,
    this.ptypeGroup,
    this.ptypeId,
    this.costMode,
    this.batchenabled,
    this.snenabled,
    this.skuId,
    this.unitId,
    this.price,
    this.preferential,
    this.pid,
    required this.changeCount,
    this.creditsName,
    this.picUrl,
    this.propId1,
    this.propId2,
    this.propvalueId1,
    this.propvalueId2,
    this.propvalueId3,
    this.propvalueId4,
    this.propvalueId5,
    this.propvalueId6,
    this.promotionPtypeId,
    this.valueType,
    this.page,
    this.pageSize,
    this.propValueName1,
    this.propValueName2,
    this.propValueName3,
    this.propValueName4,
    this.propValueName5,
    this.propValueName6,
    this.unitRate,
    this.unitName,
    this.fullbarcode,
    this.protectDays,
    this.produceDate,
    this.expireDate,
    required this.batchPrice,
    this.costId,
    this.pUserCode,
    this.batchNo,
    List<PtypeSerialNoDto>? serialNoList,
  }) : this.serialNoList = serialNoList ?? [];

  PromotionCreditsDTO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    profileId = json['profileId'];
    ptypeGroup = json['ptypeGroup'];
    promotionPtypeId = json['promotionPtypeId'];
    ptypeId = json['ptypeId'];
    fullbarcode = json['fullbarcode'];
    costMode = json['costMode'];
    batchPrice = json['batchPrice'] ?? 0;
    costId = json['costId'];
    pUserCode = json['pUserCode'];
    batchenabled = json['batchenabled'];
    snenabled = json['snenabled'];
    skuId = json['skuId'];
    fullbarcode = json['fullbarcode'];
    unitId = json['unitId'];
    price = json['price'];
    protectDays = json['protectDays'];
    preferential = json['preferential'];
    pid = json['pid'];
    changeCount = json['changeCount'] ?? 0;
    creditsName = json['creditsName'];
    unitRate = json['unitRate'];
    unitName = json['unitName'];
    picUrl = json['picUrl'];
    if (picUrl != null) {
      picUrl =
          (picUrl!.contains("http://") || picUrl!.contains("https://"))
              ? picUrl
              : SpTool.getQiNiuThumbnail(picUrl!, true);
    }
    propId1 = json['propId1'];
    propId2 = json['propId2'];
    propvalueId1 = json['propvalueId1'];
    propvalueId2 = json['propvalueId2'];
    propvalueId3 = json['propvalueId3'];
    propvalueId4 = json['propvalueId4'];
    propvalueId5 = json['propvalueId5'];
    propvalueId6 = json['propvalueId6'];

    propValueName1 = json['propValueName1'];
    propValueName2 = json['propValueName2'];
    propValueName3 = json['propValueName3'];
    propValueName4 = json['propValueName4'];
    propValueName5 = json['propValueName5'];
    propValueName6 = json['propValueName6'];

    valueType = json['valueType'];
    page = json['page'];
    pageSize = json['pageSize'];
    produceDate = json['produceDate'];
    expireDate = json['expireDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['profileId'] = this.profileId;
    data['ptypeGroup'] = this.ptypeGroup;
    data['ptypeId'] = this.ptypeId;
    data['fullbarcode'] = this.fullbarcode;
    data['costMode'] = this.costMode;
    data['batchPrice'] = this.batchPrice;
    data['costId'] = this.costId;
    data['pUserCode'] = this.pUserCode;

    data['batchenabled'] = this.batchenabled;
    data['snenabled'] = this.snenabled;
    data['skuId'] = this.skuId;
    data['fullbarcode'] = this.fullbarcode;
    data['unitId'] = this.unitId;
    data['unitName'] = this.unitName;
    data['price'] = this.price;
    data['promotionPtypeId'] = this.promotionPtypeId;
    data['preferential'] = this.preferential;
    data['pid'] = this.pid;
    data['changeCount'] = this.changeCount;
    data['creditsName'] = this.creditsName;
    data['picUrl'] = this.picUrl;
    data['propId1'] = this.propId1;
    data['propId2'] = this.propId2;
    data['propvalueId1'] = this.propvalueId1;
    data['propvalueId2'] = this.propvalueId2;
    data['propvalueId3'] = this.propvalueId3;
    data['propvalueId4'] = this.propvalueId4;
    data['propvalueId5'] = this.propvalueId5;
    data['propvalueId6'] = this.propvalueId6;
    data['protectDays'] = this.protectDays;
    data['valueType'] = this.valueType;
    data['page'] = this.page;
    data['pageSize'] = this.pageSize;
    data['propValueName1'] = this.propValueName1;
    data['propValueName2'] = this.propValueName2;
    data['propValueName3'] = this.propValueName3;
    data['propValueName4'] = this.propValueName4;
    data['propValueName5'] = this.propValueName5;
    data['propValueName6'] = this.propValueName6;
    data['unitRate'] = this.unitRate;
    data['produceDate'] = this.produceDate;
    data['expireDate'] = this.expireDate;

    return data;
  }
}
