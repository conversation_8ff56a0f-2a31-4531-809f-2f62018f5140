import 'package:flutter/cupertino.dart';
import '../../../common/net/http_util.dart';
import '../../../common/net/request_method.dart';
import '../../../shiftchange/entity/shift_changes_dto.dart';
import '../../../shiftchange/entity/shift_changes_requset.dart';
import 'package:halo_utils/http/base_model.dart';

import '../../common/tool/UUID.dart';
import '../../common/tool/performance_capture_util.dart';
import '../entity/shift_changes_sale_record.dart';

class ShiftChangesModel {
  ///保存交接记录
  static Future<ShiftChangesRequset?> saveShiftChangesRecord(
      BuildContext context, ShiftChangesRequset requset) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.shiftChanges);
    requset.newId = UUID.getUUid();
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_SHIFTCHANGES_RECROD, data: requset.toJson());
    PerformanceCaptureUtil.end(PerformanceTimeName.shiftChanges);
    return ShiftChangesRequset.fromJson(response.data);
  }

  ///查询交接记录统计
  static Future<ShiftChangesDto> getShiftChangeStatistics(
      BuildContext context, Map parmas) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_SHIFTCHANGES_STATISTICS, data: parmas);
    return ShiftChangesDto.fromJson(response.data);
  }

  ///销售额明细查询
  static Future<Map?> getAccountBalanceChangeDetail(
      BuildContext context, Map parmas) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.GET_AccountBalanceChangeDetail, data: parmas);
    return response.data;
  }

  ///交接班数据统计（新）
  static Future<List<ShiftChangesNewDto>> getPostTimeAndVchtypeList(
      BuildContext context, Map params) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_getPostTimeAndVchtypeList, data: params);
    List<dynamic>? temp = response.data;
    return temp?.map((element) {
          return ShiftChangesNewDto.fromJson(element);
        }).toList() ??
        [];
  }

  ///销售情况报表  POST_getShiftChangesSaleRecord
  static Future<ResponseModel> getShiftChangesSaleRecord(
      BuildContext context, Map params) async {
    return await HttpUtil.request(context,
        method: RequestMethod.POST_getShiftChangesSaleRecord, data: params);
  }
}
