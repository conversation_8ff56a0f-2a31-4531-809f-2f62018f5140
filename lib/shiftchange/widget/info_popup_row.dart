import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_pos/iconfont/icon_font.dart';

/// 自定义 InfoPopup 组件，用于显示带有问号图标的提示信息
/// 整个区域可点击，但箭头指向问号图标
class InfoPopupRow extends StatefulWidget {
  final String title;
  final String tips;
  final TextStyle? textStyle;

  const InfoPopupRow({
    Key? key,
    required this.title,
    required this.tips,
    this.textStyle,
  }) : super(key: key);

  @override
  State<InfoPopupRow> createState() => _InfoPopupRowState();
}

class _InfoPopupRowState extends State<InfoPopupRow> {
  OverlayEntry? _overlayEntry;
  final GlobalKey _iconKey = GlobalKey();

  @override
  void dispose() {
    _hidePopup();
    super.dispose();
  }

  void _showPopup() {
    _hidePopup();

    // 获取问号图标的位置
    final RenderBox? iconBox =
        _iconKey.currentContext?.findRenderObject() as RenderBox?;
    if (iconBox == null) return;

    final Offset iconPosition = iconBox.localToGlobal(Offset.zero);
    final Size iconSize = iconBox.size;

    // 创建弹出层
    _overlayEntry = OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            // 透明层，用于捕获点击事件关闭弹窗
            Positioned.fill(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: _hidePopup,
                child: Container(color: Colors.transparent),
              ),
            ),
            // 箭头 - 现在在底部，指向下方
            Positioned(
              bottom: MediaQuery.of(context).size.height - iconPosition.dy + 8,
              left: iconPosition.dx + iconSize.width / 2 - 10,
              child: CustomPaint(
                size: const Size(20, 10),
                painter: _ArrowPainter(
                    Colors.white, ColorUtil.stringColor("#DDDDDD")),
              ),
            ),
            // 弹窗内容 - 现在在图标上方
            Positioned(
              bottom: MediaQuery.of(context).size.height - iconPosition.dy + 18,
              left: iconPosition.dx + iconSize.width / 2 - 150, // 居中显示，更宽
              child: Material(
                elevation: 3.0,
                shadowColor: Colors.black.withOpacity(0.3),
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  side: BorderSide(
                    color: ColorUtil.stringColor("#DDDDDD"),
                    width: 1.0,
                  ),
                ),
                child: Container(
                  padding: const EdgeInsets.all(12.0),
                  constraints: const BoxConstraints(maxWidth: 300), // 更宽的气泡
                  child: Text(
                    widget.tips,
                    style: const TextStyle(fontSize: 14.0),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );

    Overlay.of(context).insert(_overlayEntry!);

    // 3秒后自动关闭
    Future.delayed(const Duration(seconds: 3), _hidePopup);
  }

  void _hidePopup() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showPopup,
      behavior: HitTestBehavior.opaque,
      child: Row(
        children: [
          Flexible(
            child: HaloPosLabel(
              widget.title,
              textStyle: widget.textStyle,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 5, top: 2),
            child: SizedBox(
              key: _iconKey,
              width: 30.w,
              height: 30.w,
              child: IconFont(
                IconNames.wenhao_1,
                size: 30.w,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 箭头绘制器
class _ArrowPainter extends CustomPainter {
  final Color color;
  final Color borderColor;

  _ArrowPainter(this.color, this.borderColor);

  @override
  void paint(Canvas canvas, Size size) {
    // 保存画布状态
    canvas.save();

    // 创建向下的箭头路径
    final Path path = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width / 2, size.height)
      ..lineTo(size.width, 0)
      ..close();

    // 绘制阴影
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

    // 偏移阴影路径
    final Path shadowPath = Path()
      ..moveTo(0, 1)
      ..lineTo(size.width / 2, size.height + 1)
      ..lineTo(size.width, 1)
      ..close();

    canvas.drawPath(shadowPath, shadowPaint);

    // 绘制填充
    final Paint fillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // 绘制边框
    final Paint borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // 先绘制填充
    canvas.drawPath(path, fillPaint);
    // 再绘制边框
    canvas.drawPath(path, borderPaint);

    // 恢复画布状态
    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
