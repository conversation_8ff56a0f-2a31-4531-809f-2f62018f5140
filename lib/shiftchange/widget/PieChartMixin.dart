import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/report/entity/store_statistics_dto.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../common/style/app_color_helper.dart';
import '../../common/style/app_colors.dart';
import '../../widgets/halo_pos_label.dart';

mixin PieChartState<T extends StatefulWidget> on State<T> {
  int touchedIndex = -1;
  double radius = 30;
  GlobalKey globalKey = GlobalKey();
  OverlayEntry? overlayEntry;
  List<IncomePayWay> incomePayWays = [];
  static const List<Color> chartsColors = [
    Color(0xFF2288FC),
    Color(0xFFFF786D),
    Color(0xFF00C79A),
    Color(0xFFAFD527),
    Color(0xFFFDAB2B),
    Color(0xFFA26FEA),
    Color(0xFF49A9C5),
    Color(0xFF818B43),
    Color(0xFFFF6B02),
    Color(0xFFFE4C32),
  ];

  Widget buildPieChart(BuildContext context) {
    return HaloContainer(
      padding:  EdgeInsets.only(left: 90.w),
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        Expanded(
          child: SizedBox(
            key: globalKey,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      HaloLabel(
                        "合计收入(元)",
                        textStyle: TextStyle(
                            fontSize: 20.sp,
                            color: const
                            Color(0xFF333333)),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 10.w,),
                      HaloPosLabel(
                        getSummaryDataText().toStringAsFixed(2),
                        textStyle: TextStyle(
                            fontSize: 30.sp,
                            color:
                            const
                            Color(0xFF333333),
                            fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                PieChart(
                  PieChartData(
                    pieTouchData: PieTouchData(
                      touchCallback: (FlTouchEvent event, pieTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              pieTouchResponse == null ||
                              pieTouchResponse.touchedSection == null) {
                            // touchedIndex = -1;
                            return;
                          }
                          removeOverlay();

                          if (pieTouchResponse.touchedSection == null ||
                              pieTouchResponse
                                      .touchedSection!.touchedSectionIndex ==
                                  -1) return;

                          Offset original = event.localPosition!;
                          original =
                              convertToScreenCoordinate(original.dx, original.dy);
                          touchedIndex = pieTouchResponse
                              .touchedSection!.touchedSectionIndex;
                          _showOverlay(
                              context,
                              original,
                              pieTouchResponse
                                      .touchedSection!.touchedSection!,getSummaryData());
                          touchedIndex = pieTouchResponse
                              .touchedSection!.touchedSectionIndex;
                        });
                      },
                    ),
                    startDegreeOffset: 180,
                    borderData: FlBorderData(
                      show: false,
                    ),
                    sectionsSpace: 1,
                    // centerSpaceRadius: radius * 2,
                    sections: showingSections(),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(
          width: 30,
        ),
        Expanded(
            child: ListView.builder(
          itemCount: incomePayWays.length,
          itemBuilder: (context, index) {
            Color color = chartsColors[index % chartsColors.length];
            IncomePayWay payWay = incomePayWays[index];
            return Indicator(
              color: color,
              textSpan: TextSpan(children: [
                TextSpan(
                    text: payWay.payWayName,
                    style: TextStyle(
                        color: touchedIndex == index
                            ? AppColors.titleBoldTextColor
                            : AppColors.secondTextColorDark,
                        fontSize: 26.sp))
              ]),
              textSpanPercent: TextSpan(
                  text:
                  getSummaryData()==0? "   | 0%" : "   | ${((payWay.income / getSummaryData()) * 100).toStringAsFixed(2)}%",
                  style: TextStyle(
                      color: touchedIndex == index
                          ? AppColors.titleBoldTextColor
                          : AppColors.secondTextColorDark,
                      fontSize: 26.sp)),
              textSpanIncome: TextSpan(
                  text: "${payWay.income}元",
                  style: TextStyle(
                      color: touchedIndex == index
                          ? AppColors.titleBoldTextColor
                          : AppColors.secondTextColorDark,
                      fontSize: 26.sp)),
              isSquare: false,
              size: touchedIndex == index ? 14 : 12,
            );
          },
        ))
      ],
    );
  }

  List<PieChartSectionData> showingSections() {
    List<IncomePayWay> filteredIncomePayWays = incomePayWays.where((payWay) => payWay.income > 0).toList();
    return List.generate(
      filteredIncomePayWays.length,
      (i) {
        final isTouched = i == touchedIndex;
        Color color = chartsColors[i % chartsColors.length];
        IncomePayWay payWay = filteredIncomePayWays[i];

        return PieChartSectionData(
          color: color,
          value: payWay.income<=0?0.000001:payWay.income,
          showTitle: false,
          title: payWay.payWayName,
          radius: isTouched ? radius * 1.2 : radius,
          titlePositionPercentageOffset: 0.55,
          borderSide: isTouched
              ? const BorderSide(color: Colors.white, width: 0.5)
              : BorderSide(color: Colors.white.withOpacity(0)),
        );
      },
    );
  }

  // 调用这个函数将组件内部的坐标转换成屏幕坐标
  Offset convertToScreenCoordinate(double componentX, double componentY) {
    final RenderBox renderBox =
        globalKey.currentContext?.findRenderObject() as RenderBox;
    final Offset componentOffset = Offset(componentX, componentY);
    final Offset screenCoordinate = renderBox.localToGlobal(componentOffset);
    return screenCoordinate;
  }

  void _showOverlay(BuildContext context, Offset original,PieChartSectionData section,double sum) {
    // 创建OverlayEntry
    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: original.dy,
        left: original.dx,
        child: Material(
          color: Colors.transparent,
          child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                borderRadius: BorderRadius.circular(5),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.5), // 阴影颜色
                    spreadRadius: 2, // 阴影扩展半径
                    blurRadius: 5, // 阴影模糊半径
                    offset: const Offset(3, 3), // 阴影偏移量
                  ),
                ], // 圆角半径
              ),
              padding: EdgeInsets.only(
                  top: 10.w, bottom: 10.w, right: 10.w, left: 10.w),
              constraints: BoxConstraints(maxWidth: 200.w),
              child: HaloContainer(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                direction: Axis.vertical,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: section.color,
                        ),
                      ),
                      SizedBox(width: 10.w,),
                      HaloPosLabel(
                        section.title,
                        maxLines: 2,
                        textStyle: TextStyle(
                            color: Colors.white, fontSize: 20.sp),
                      )
                    ],
                  ),
                  Padding(
                    padding:  EdgeInsets.only(left: 25.w),
                    child: HaloPosLabel(
                      "金额 ${section.value}",
                      maxLines: 2,
                      textStyle: TextStyle(
                          color: Colors.white, fontSize: 20.sp),
                    ),
                  ),
                  Padding(
                    padding:  EdgeInsets.only(left: 25.w),
                    child: HaloPosLabel(
                        sum==0 ? "占比 0%" :"占比 ${((section.value/sum) * 100).toStringAsFixed(2)}%",
                      maxLines: 2,
                      textStyle: TextStyle(
                          color: Colors.white, fontSize: 20.sp),
                    ),
                  ),
                ],
              )),
        ),
      ),
    );

    // 插入OverlayEntry到Overlay中
    Overlay.of(context).insert(overlayEntry!);
  }

  void removeOverlay() {
    if (overlayEntry != null) {
      overlayEntry?.remove();
      overlayEntry = null;
    }
  }
  getSummaryDataText() {
    double totalIncome =
    incomePayWays.fold(0.0, (sum, item) => sum + item.income);
    return totalIncome;
  }


  getSummaryData() {
    List<IncomePayWay> filteredIncomePayWays = incomePayWays.where((payWay) => payWay.income > 0).toList();
    double totalIncome =
    filteredIncomePayWays.fold(0.0, (sum, item) => sum + item.income);
    return totalIncome;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    removeOverlay();
    super.dispose();
  }
}

class Indicator extends StatelessWidget {
  const Indicator({
    Key? key,
    required this.color,
    required this.textSpan,
    required this.isSquare,
    this.size = 14,
    this.textColor,
    required this.textSpanPercent,
    required this.textSpanIncome,
  }) : super(key: key);
  final Color color;
  final TextSpan textSpan;
  final TextSpan textSpanPercent;
  final TextSpan textSpanIncome;

  final bool isSquare;
  final double size;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      children: <Widget>[
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: isSquare ? BoxShape.rectangle : BoxShape.circle,
            color: color,
          ),
        ),
        const SizedBox(
          width: 4,
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: RichText(
              text: textSpan,
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: RichText(
              text: textSpanPercent,
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: RichText(
              text: textSpanIncome,
            ),
          ),
        )
      ],
    );
  }
}
