import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../common/style/app_color_helper.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';

class PieItemData {
  Color color;
  String text;
  num? value;
  int selectIndex;

  bool isSquare = true;
  double colorWidth = 14.w;
  double colorHeight = 8.w;
  Color textColor = const Color(0xff505050);

  PieItemData(
      {required this.color,
      required this.text,
      this.value,
      this.selectIndex = 0});
}

class PieIndicator extends StatelessWidget {
  final PieItemData itemData;

  const PieIndicator({
    Key? key,
    required this.itemData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      height: 60.w,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: itemData.colorWidth,
          height: itemData.colorHeight,
          margin: EdgeInsets.only(right: 4.w),
          decoration: BoxDecoration(
            shape: itemData.isSquare ? BoxShape.rectangle : BoxShape.circle,
            color: itemData.color,
            borderRadius: BorderRadius.all(Radius.circular(2.0.w)),
          ),
        ),
        Expanded(
          flex: 3,
          child: Container(
            padding: EdgeInsets.only(left: 9.w),
            child: HaloLabel(
              itemData.text,
              maxLines: 1,
              textStyle: TextStyle(
                  fontSize: 24.sp,
                  color: AppColorHelper(context).getTitleBoldTextColor()),
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: Container(
            padding: EdgeInsets.only(left: 0.w),
            child: HaloLabel(
              itemData.value.toString(),
              maxLines: 1,
              textStyle: TextStyle(
                  fontSize: 24.sp,
                  color: AppColorHelper(context).getTitleBoldTextColor()),
            ),
          ),
        ),
      ],
    );
  }
}

class PieChartWidget extends StatefulWidget {
  final int selectIndex;
  final double width;
  final String? resultValue;
  final List<PieIndicator> pieDataList;
  final bool isMore;
  final Function? moreClick;

  const PieChartWidget(
      {Key? key,
      required this.pieDataList,
      required this.width,
      required this.resultValue,
      required this.selectIndex,
      this.isMore = false,
      this.moreClick})
      : super(key: key);

  @override
  State<PieChartWidget> createState() => _PieChartWidgetState();
}

class _PieChartWidgetState extends State<PieChartWidget> {
  List<PieChartSectionData> showingSections() {
    var radius = 15.0;
    num result = widget.pieDataList.fold(
        0,
        (previousValue, element) =>
            previousValue + (element.itemData.value ?? 0));

    if (result == 0) {
      // 无数据
      return List.generate(1, (i) {
        if (widget.selectIndex == i) {
          radius = 20;
        }
        return PieChartSectionData(
          color: ColorUtil.stringColor("#E5E5E5"),
          value: 1 * 1.00,
          radius: radius,
          title: '',
        );
      });
    }

    return List.generate(widget.pieDataList.length, (i) {
      if (widget.selectIndex == i) {
        radius = 20;
      }
      PieIndicator temp = widget.pieDataList[i];
      return PieChartSectionData(
        color: temp.itemData.color,
        value: (temp.itemData.value ?? 0) * 1.00,
        radius: radius,
        title: '',
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: pieChart(),
    );
  }

  Widget pieChart() {
    dynamic reslult = 0;
    // int selectIndex = 0;
    for (var element in widget.pieDataList) {
      reslult += element.itemData.value;
      // selectIndex = element.itemData.selectIndex;
    }

    List<Widget> temp = [];
    temp.addAll(widget.pieDataList);

    var widthPie = widget.width / 3.0 * 2.0;
    return Row(
      mainAxisSize: MainAxisSize.max,
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Expanded(
          child: Stack(
            alignment: Alignment.center,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: PieChart(
                  PieChartData(
                      borderData: FlBorderData(
                        show: false,
                      ),
                      sectionsSpace: 0,
                      centerSpaceRadius: widthPie / 2 - 70,
                      sections: showingSections(),
                      pieTouchData: PieTouchData(
                        enabled: false,
                        // touchCallback: (FlTouchEvent event,
                        //     PieTouchResponse pieTouchResponse) {
                        //   setState(() {
                        //     if (!event.isInterestedForInteractions ||
                        //         pieTouchResponse == null ||
                        //         pieTouchResponse.touchedSection == null) {
                        //       widget.selectIndex = -1;
                        //       return;
                        //     }
                        //     widget.selectIndex = pieTouchResponse
                        //         .touchedSection.touchedSectionIndex;
                        //   }
                        //   );
                        // }
                      )),
                  swapAnimationDuration: const Duration(milliseconds: 0),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                child: HaloLabel(
                  DecimalDisplayHelper.getTotalFixed(
                      widget.resultValue ?? reslult.toString()),
                  textStyle: TextStyle(
                      fontSize: 36.sp,
                      color: AppColorHelper(context).getTitleBoldTextColor()),
                  textAlign: TextAlign.center,
                ),
              )
            ],
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: ListView.builder(
              shrinkWrap: true,
              itemBuilder: (BuildContext context, int index) {
                return temp[index];
              },
              itemCount: temp.length,
            ),
          ),
        )
      ],
    );
  }
}
