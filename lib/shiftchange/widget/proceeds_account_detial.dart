import 'package:decimal/decimal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/src/widgets/framework.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../shiftchange/entity/account_balance_dto.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_search.dart';
import '../../../report/entity/stock_statistics_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../bill/model/bill_model.dart';

class ProceedsAccountDetial extends StatefulWidget {
  final List<AccountBalanceItem> list;

  const ProceedsAccountDetial({Key? key, required this.list}) : super(key: key);

  @override
  _ProceedsAccountDetialState createState() => _ProceedsAccountDetialState();
}

class _ProceedsAccountDetialState extends State<ProceedsAccountDetial> {
  List dataSoure = [];
  String filterValue = "";

  @override
  Widget build(BuildContext context) {
    if (widget.list != null) {
      dataSoure.clear();
      dataSoure.addAll(widget.list);
    }
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Color.fromRGBO(0, 0, 0, 0),
      body: Container(
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 80.w, horizontal: 450.w),
              color: Colors.white,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: 830.h,
                  // maxWidth: ScreenUtil().screenWidth - 900.w,
                ),
                child: Column(
                  children: [
                    HaloContainer(
                      padding: EdgeInsets.only(left: 19.w, right: 15),
                      color: Colors.white24,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      height: 72.h,
                      children: [
                        Text(
                          "收款明细",
                          style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 24.sp,
                              color: AppColorHelper(context)
                                  .getTitleBoldTextColor()),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            color: Colors.transparent,
                            alignment: Alignment.centerRight,
                            width: 100,
                            height: 30,
                            child: IconFont(
                              IconNames.close,
                              size: 30,
                            ),
                          ),
                        ),
                      ],
                    ),
                    // Divider(
                    //   color: AppColors.lineColor,
                    //   height: 1,
                    // ),
                    // _buildSearch(),
                    Divider(
                      color: AppColors.lineColor,
                      height: 1,
                    ),
                    _buildTop(),
                    Divider(
                      color: AppColors.lineColor,
                      height: 1,
                    ),
                    Expanded(
                        child: ListView.builder(
                      itemBuilder: (itemData, int index) {
                        return _buildItem(dataSoure[index]);
                      },
                      itemCount: dataSoure.length,
                    ))
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTop() {
    return HaloContainer(
      padding: EdgeInsets.only(left: 19.w, right: 19.w),
      // mainAxisSize: MainAxisSize.max,
      color: ColorUtil.stringColor("#FAFAFA"),
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      height: 60.h,
      children: [
        Expanded(
            flex: 1,
            child: Text(
              "支付方式",
              style: TextStyle(
                  color: AppColorHelper(context).getTitleTextColor(),
                  fontSize: 20.sp),
            )),
        Container(
          child: Text(
            "金额",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 20.sp),
          ),
          width: 250.w,
        ),
        Container(
          child: Text(
            "单据编号",
            textAlign: TextAlign.start,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 20.sp),
          ),
          width: 300.w,
        ),
      ],
    );
  }

  Widget _buildItem(AccountBalanceItem statisticsDto) {
    return Column(
      children: [
        HaloContainer(
          padding: EdgeInsets.only(left: 19.w, right: 19.w),
          // mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          height: 50,
          children: [
            Expanded(
                flex: 1,
                child: HaloContainer(
                  direction: Axis.vertical,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    HaloPosLabel(
                      statisticsDto.atypeFullname ?? "",
                      maxLines: 1,
                      textStyle: TextStyle(
                          color:
                              AppColorHelper(context).getTitleBoldTextColor(),
                          fontSize: 22.sp),
                    ),
                  ],
                )),
            Container(
              child: Text(
                statisticsDto.total != null
                    ? Decimal.parse(statisticsDto.total).toString()
                    : '0.0',
                textAlign: TextAlign.center,
                style:
                    TextStyle(color: AppColors.redTextColor, fontSize: 22.sp),
              ),
              width: 250.w,
            ),
            Container(
              child: Text(
                "${statisticsDto.billNumber}",
                textAlign: TextAlign.start,
                style: TextStyle(
                    color: AppColorHelper(context).getTitleBoldTextColor(),
                    fontSize: 22.sp),
              ),
              width: 300.w,
            ),
          ],
        ),
        Divider(
          height: 1.w,
          color: AppColors.lineColor,
        )
      ],
    );
  }

  Widget _buildSearch() {
    return HaloSearch(
      value: filterValue,
      inputBackGround: Color(0x00000000),
      isShowClear: true,
      isShowScan: false,
      isShowFilter: false,
      hintText: "商品名称搜索",
      onSubmitted: (text) async {
        filterValue = text;
        _requsetData(text);
      },
    );
  }

  _requsetData(String filterValue) {
    BillModel.getStockStatistics(context, 1, filterValue).then((value) {
      setState(() {
        dataSoure.clear();
        dataSoure.addAll(value);
      });
    });
  }
}
