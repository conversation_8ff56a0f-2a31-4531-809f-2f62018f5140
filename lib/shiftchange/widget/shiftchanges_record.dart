import 'package:flutter/material.dart';
import 'package:halo_pos/iconfont/icon_font.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/math_util.dart';
import 'package:haloui/haloui.dart';
import '../../../common/tool/date_util.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../common/login/login_center.dart';
import '../../common/style/app_colors.dart';
import '../../common/style/app_pos_size.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/widget/datetime_filter.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/custom_table.dart';
import '../entity/shift_changes_sale_record.dart';
import '../model/shift_changes_model.dart';
import 'dart:math' as dartMath;

class ShiftChangesRecord extends BaseStatefulPage {
  const ShiftChangesRecord({Key? key}) : super(key: key);

  @override
  BaseStatefulPageState createState() => _ShiftChangesRecordState();
}

class _ShiftChangesRecordState extends BaseStatefulPageState<ShiftChangesRecord>
    with DateTimeFilterMixin {
  final ScrollController scrollController = ScrollController();
  TextEditingController textPTypeNameController = TextEditingController();
  List<ShiftChangesSaleRecord> saleRecords = [];
  final Map<int, String> columnTitle = {};

  ///排序字段
  String sortName = SaleStatisticsColumnType.actualSaleNum.name;

  ///排序方向
  SortType sortType = SortType.desc;
  int pageIndex = 1;
  final int pageSize = 40;
  int totalCount = 0;
  final ScrollController _scrollController = ScrollController();

  @override
  String getActionBarTitle() {
    return "商品销售统计";
  }

  @override
  Future<void>? onInitState() {
    initColumnConfig();
    fastSetDate(FastTimeType.last7Day);
    onRequestData();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >
          _scrollController.position.maxScrollExtent - 20) {
        if (saleRecords.length < totalCount) {
          pageIndex++;
          onRequestData();
        }
      }
    });
    return null;
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      direction: Axis.vertical,
      children: [_buildTop(), Expanded(child: buildTable())],
    );
  }

  ///顶部过滤
  Widget _buildTop() {
    return HaloContainer(
      height: 100.h,
      color: Colors.white,
      padding: EdgeInsets.only(right: 16.w),
      border: Border(
          bottom: BorderSide(color: AppColors.pageBackgroundColor, width: 1.h)),
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        buildDateTimeQuery(context, showFastSelect: true),
        Container(
          width: 300.w,
          margin: EdgeInsets.only(left: 16.w),
          padding:
              EdgeInsets.only(left: 8.w, right: 8.w, top: 10.h, bottom: 12.h),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.w)),
              border: Border.all(color: AppColors.borderColor, width: 1.w)),
          child: HaloTextField(
            controller: textPTypeNameController,
            contentPadding: 0,
            hintText: "输入商品名称进行查询",
            hintStyle: TextStyle(
                color: AppColors.hintColor,
                fontSize: AppPosSize.totalFontSize.sp),
            textColor: AppColors.normalFontColor,
            fontSize: AppPosSize.totalFontSize.sp,
          ),
        ),
        SizedBox(
          width: 12.w,
        ),
        HaloButton(
          borderRadius: 6.sp,
          height: 56.h,
          text: "查询",
          fontSize: AppPosSize.secondaryTitleFontSize.sp,
          backgroundColor: AppColors.accentColor,
          onPressed: () {
            FocusScopeNode currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus &&
                currentFocus.focusedChild != null) {
              FocusManager.instance.primaryFocus?.unfocus();
            }
            pageIndex = 1;
            onRequestData();
          },
        ),
        SizedBox(
          width: 12.w,
        ),
        HaloButton(
          height: 56.h,
          buttonType: HaloButtonType.outlinedButton,
          borderColor: AppColors.btnBorderColor,
          outLineWidth: 2.w,
          borderRadius: 6.sp,
          textColor: AppColors.normalFontColor,
          fontSize: AppPosSize.secondaryTitleFontSize.sp,
          text: "重置",
          onPressed: () {
            _resetFilter();
          },
        ),
      ],
    );
  }

  ///构建表体
  Widget buildTable() {
    if (saleRecords.isEmpty) {
      return HaloEmptyContainer(
        gravity: EmptyGravity.CENTER,
        image: Image.asset('assets/images/nodata.png'),
        title: "暂无权限查看或无查看数据",
        titleStyle: TextStyle(
            decoration: TextDecoration.none,
            fontSize: ScreenUtil().setSp(30),
            color: Colors.grey),
      );
    }

    return Container(
        color: Colors.white,
        child: CustomColumnTableByList<ShiftChangesSaleRecord>(
          columnConfig: getTableCellWidth(),
          columnTitle: columnTitle,
          data: saleRecords,
          scrollable: true,
          scrollController: _scrollController,
          itemRowDecorationGetter: (item) {
            return const BoxDecoration(
                border: Border(
                    bottom:
                        BorderSide(color: AppColors.borderColor, width: 1)));
          },
          titleRowDecoration: const BoxDecoration(color: Color(0xFFf3f4f7)),
          columnTitleBuilder: (title, columnType) =>
              buildTitle(title, columnType),
          cellBuilder: buildCell,
        ));
  }

  Widget buildTitle(title, columnType) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (columnType == SaleStatisticsColumnType.fullNameAndUserCode.index ||
            columnType == SaleStatisticsColumnType.propNames.index ||
            columnType == SaleStatisticsColumnType.skuFullBarCode.index) {
          return;
        }

        if (sortName == SaleStatisticsColumnType.values[columnType].name) {
          sortType = sortType == SortType.asc ? SortType.desc : SortType.asc;
        } else {
          sortType = SortType.desc;
          sortName = SaleStatisticsColumnType.values[columnType].name;
        }

        onRequestData();
      },
      child: HaloContainer(
        mainAxisAlignment:
            (columnType == SaleStatisticsColumnType.fullNameAndUserCode.index ||
                    columnType == SaleStatisticsColumnType.propNames.index)
                ? MainAxisAlignment.start
                : MainAxisAlignment.center,
        padding: EdgeInsets.only(
            bottom: 16.h,
            top: 16.h,
            left: columnType ==
                        SaleStatisticsColumnType.fullNameAndUserCode.index ||
                    columnType ==
                        SaleStatisticsColumnType.fullNameAndUserCode.index
                ? 16.w
                : 6.w),
        children: [
          Text(title, overflow: TextOverflow.ellipsis),
          Visibility(
              visible:
                  SaleStatisticsColumnType.values[columnType].name == sortName,
              child: Transform.rotate(
                angle: sortType == SortType.desc ? 0 : dartMath.pi,
                child: IconFont(
                  IconNames.jiantou_1,
                  size: 16.w,
                ),
              ))
        ],
      ),
    );
  }

  Widget buildCell(ShiftChangesSaleRecord item, int columnType) {
    switch (SaleStatisticsColumnType.values[columnType]) {
      case SaleStatisticsColumnType.fullNameAndUserCode:
        return buildTableItem(item.fullname,
            subContent: item.usercode,
            textAlign: TextAlign.left,
            padding: EdgeInsets.only(
                left: 16.h, bottom: 8.h, top: 4.h, right: 16.w));
      case SaleStatisticsColumnType.propNames:
        return buildTableItem(item.propNames ?? "",
            textAlign: TextAlign.left,
            padding: EdgeInsets.only(left: 16.h, bottom: 8.h, top: 4.h));
      case SaleStatisticsColumnType.skuFullBarCode:
        return buildTableItem(item.fullbarcode ?? "");
      case SaleStatisticsColumnType.saleNum:
        return buildTableItem(item.saleNum.toString());
      case SaleStatisticsColumnType.saleTotal:
        return buildTableItem("￥${item.saleTotal.toString()}");
      case SaleStatisticsColumnType.returnNum:
        return buildTableItem(item.returnNum.toString());
      case SaleStatisticsColumnType.returnTotal:
        return buildTableItem("￥${item.returnTotal.toString()}");
      case SaleStatisticsColumnType.returnRate:
        return buildTableItem(
            "${MathUtil.multiplication(item.returnRate.toString(), "100")}%");
      case SaleStatisticsColumnType.actualSaleNum:
        return buildTableItem(item.actualSaleNum.toString());
      case SaleStatisticsColumnType.actualSaleTotal:
        return buildTableItem("￥${item.actualSaleTotal.toString()}");
    }
  }

  Widget buildTableItem(String content,
      {String? subContent,
      EdgeInsetsGeometry? padding,
      TextAlign textAlign = TextAlign.center}) {
    return HaloContainer(
      direction: Axis.vertical,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      padding: padding ?? EdgeInsets.only(bottom: 8.h, top: 4.h, left: 8.w),
      children: [
        HaloContainer(
            mainAxisSize: MainAxisSize.max,
            padding: EdgeInsets.symmetric(vertical: 8.h),
            children: [
              Expanded(
                  child: HaloLabel(content ?? "",
                      textAlign: textAlign,
                      maxLines: 1,
                      textStyle: TextStyle(
                          fontSize: AppPosSize.totalFontSize.sp,
                          color: AppColors.normalFontColor)))
            ]),
        HaloContainer(
          visible: StringUtil.isNotEmpty(subContent),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                child: HaloPosLabel(
              subContent ?? "",
              textAlign: textAlign,
              textStyle: TextStyle(
                  fontSize: AppPosSize.contentFontSize.sp,
                  color: AppColors.secondaryFontColor),
            ))
          ],
        )
      ],
    );
  }

  Map<int, double> getTableCellWidth() {
    Map<int, double> columnWidths = {};

    for (SaleStatisticsColumnType item in SaleStatisticsColumnType.values) {
      if (item == SaleStatisticsColumnType.fullNameAndUserCode) {
        columnWidths[item.index] = 3;
      } else if (item == SaleStatisticsColumnType.actualSaleNum ||
          item == SaleStatisticsColumnType.actualSaleTotal) {
        columnWidths[item.index] = 1;
      } else {
        columnWidths[item.index] = 1;
      }
    }
    return columnWidths;
  }

  Future<List<ShiftChangesSaleRecord>> onRequestData() {
    Map queryParams = {
      "startTime": formatDateStringToUtc(textStartTimeController.text),
      "endTime": formatDateStringToUtc(textEndTimeController.text),
      "otypeId": SpTool.getStoreInfo()!.otypeId,
      "pFullName": textPTypeNameController.text,
      "sort": {"dataField": sortName, "ascending": sortType == SortType.asc}
    };

    Map<String, Object> requestParam = {};
    requestParam["pageIndex"] = pageIndex;
    requestParam["pageSize"] = pageSize;
    requestParam["queryParams"] = queryParams;

    return ShiftChangesModel.getShiftChangesSaleRecord(context, requestParam)
        .then((response) {
      if (response.code != 200) {
        HaloToast.show(context, msg: response.message ?? "获取数据出错，请联系管理员");
        return [];
      }
      List<dynamic>? temp = response.data['list'];
      List<ShiftChangesSaleRecord> list = temp?.map((element) {
            return ShiftChangesSaleRecord.fromJson(element);
          }).toList() ??
          [];
      totalCount = int.tryParse(response.data['total']) ?? 0;
      setState(() {
        if (pageIndex == 1) {
          saleRecords.clear();
        }
        saleRecords.addAll(list);
      });
      return list;
    });
  }

  ///初始化列配置
  void initColumnConfig() {
    for (SaleStatisticsColumnType item in SaleStatisticsColumnType.values) {
      columnTitle[item.index] = item.title;
    }
  }

  _resetFilter() {
    pageIndex = 1;
    totalCount = 0;
    textPTypeNameController.text = "";
    fastSetDate(FastTimeType.last7Day);
    setState(() {});
  }
}

enum SaleStatisticsColumnType {
  //商品名称/编号",
  fullNameAndUserCode,
  //属性组合",
  propNames,
  //条码",
  skuFullBarCode,
  //销售数量",
  saleNum,
  //销售金额",
  saleTotal,
  //退货数量",
  returnNum,
  //退货金额",
  returnTotal,
  //退货率",
  returnRate,
  //实际销售数量",
  actualSaleNum,
  //实际销售金额"
  actualSaleTotal,
}

extension SaleStatisticsColumnTypeExtension on SaleStatisticsColumnType {
  String get title {
    switch (this) {
      case SaleStatisticsColumnType.fullNameAndUserCode:
        return "商品名称/编号";
      case SaleStatisticsColumnType.propNames:
        return "属性组合";
      case SaleStatisticsColumnType.skuFullBarCode:
        return "条码";
      case SaleStatisticsColumnType.saleNum:
        return "销售数量";
      case SaleStatisticsColumnType.saleTotal:
        return "销售金额";
      case SaleStatisticsColumnType.returnNum:
        return "退货数量";
      case SaleStatisticsColumnType.returnTotal:
        return "退货金额";
      case SaleStatisticsColumnType.returnRate:
        return "退货率";
      case SaleStatisticsColumnType.actualSaleNum:
        return "实际销售数量";
      case SaleStatisticsColumnType.actualSaleTotal:
        return "实际销售金额";
    }
  }
}

enum SortType {
  ///升序
  asc,

  ///降序
  desc
}
