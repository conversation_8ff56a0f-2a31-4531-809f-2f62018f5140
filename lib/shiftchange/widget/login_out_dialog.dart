import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../bill/bill/channel/widget/base_common_dialog.dart';
import '../../common/style/app_colors.dart';
import '../../widgets/halo_pos_label.dart';

class LoginOutDialog extends StatefulWidget {
  final Function()? printClick;
  final bool isEnabled;
  const LoginOutDialog({Key? key,required this.isEnabled,this.printClick}) : super(key: key);

  @override
  State<LoginOutDialog> createState() => _LoginOutDialogState();
}

class _LoginOutDialogState extends BaseCommonDialogState<LoginOutDialog> {

  @override
  // TODO: implement height
  double get height => 300.w;

  @override
  // TODO: implement title
  String get title => "交接班";

  @override
  // TODO: implement width
  double get width => 550.w;

  @override
  // TODO: implement showCloseBtn
  bool get showCloseBtn => false;

  @override
  Widget buildContent(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Center(child: HaloPosLabel("交接班已完成",textStyle:TextStyle(fontSize: 32.sp,color: Colors.black), maxLines: 1,),),
        SizedBox(height: 15.w,),
        _buildBottomBody(context)
      ],
    );
  }

  Widget _buildBottomBody(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 100.w,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          HaloButton(
            buttonType: HaloButtonType.elevatedButton,
            outLineWidth: 1.w,
            foregroundColor: AppColors.normalTextColor,
            borderColor: AppColors.btnBorderColor,
            height: 50.h,
            width: 260.w,
            text: "补打交接小票",
            textColor: Colors.white,
            fontSize: 26.sp,
            enabled: widget.isEnabled,
            onPressed: () {
              if(widget.printClick!=null){
                widget.printClick!();
              }
            },
          ),
          SizedBox(
            width: 40.w,
          ),
          HaloButton(
            buttonType: HaloButtonType.outlinedButton,
            outLineWidth: 1.w,
            foregroundColor: AppColors.normalTextColor,
            borderColor: AppColors.btnBorderColor,
            height: 50.h,
            width: 190.w,
            text: "退出系统",
            fontSize: 26.sp,
            onPressed: () {
              Navigator.pop(context,true);
            },
          ),

        ],
      ),
    );
  }
  @override
  void onCloseButtonClick(BuildContext context) {
    Navigator.pop(context,true);
  }
}
