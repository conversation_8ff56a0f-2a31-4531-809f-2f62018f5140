import 'package:decimal/decimal.dart';
import 'package:halo_utils/utils/math_util.dart';

class ShiftChangesSaleRecord {
  //商品ID
  String ptypeId = "0";
  String fullname = "";
  String? usercode;

  String? propNames;

  String? fullbarcode;
  double saleNum = 0;
  double saleTotal = 0;
  double returnNum = 0;
  double returnTotal = 0;
  double actualSaleNum = 0;
  double actualSaleTotal = 0;
  double returnRate = 0;

  ShiftChangesSaleRecord.fromJson(Map? jsonMap) {
    if (jsonMap != null) {
      ptypeId = jsonMap["ptypeId"];
      fullname = jsonMap["fullname"];
      usercode = jsonMap["usercode"];
      propNames = jsonMap["propNames"];
      fullbarcode = jsonMap["fullbarcode"];
      saleNum = jsonMap["saleNum"];
      saleTotal = jsonMap["saleTotal"];
      returnNum = jsonMap["returnNum"];
      returnTotal = jsonMap["returnTotal"];
      actualSaleNum = jsonMap["actualSaleNum"];
      actualSaleTotal = jsonMap["actualSaleTotal"];
      returnRate =jsonMap["returnRate"];
    }
  }
}
