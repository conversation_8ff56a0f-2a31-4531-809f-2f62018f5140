import 'package:decimal/decimal.dart';
import '../../../common/login/login_center.dart';
import '../../../common/tool/sp_tool.dart';
import 'package:halo_utils/utils/math_util.dart';

import '../../shiftchange/entity/account_balance_dto.dart';
import '../shiftchanges_page.dart';

class ShiftChangesDto {
  String? shop; //门店
  String? cashier; //收银员
  String? cashierCode; //收银员工号
  String? cashierNumber; //收银机号码
  double? shopOrderCount; //门店单据数量
  double? shopOrderNoPayCount; //门店单据数量-未支付
  double? returnOrderCount; //门店退货数量
  double? exchangeOrderCount; //门店换货数量
  double? exchangeOrderNoPayCount; //门店换货数量-未支付


  double? initialAmount; //初始金额
  double? receivableAmount; //收款金额
  double? depositAmount; //存入金额
  double? takeoutAmount; //取出金额
  double? recAmount; //钱箱收款金额
  double? returnBoxAmount; //钱箱退款金额
  double? returnAmount; //退货金额

  double? saleTotal; //出库金额
  double? exChangTotal; //换货金额
  double? returnTotal; //退货金额
  double? rechargeTotal; //储值金额

  double? noPayAmount; //未支付金额

  double? recharge; //储值金额
  double? rechargeReturn; //储值退款


  List<AtypeModel> multiAtype = [];
  List<ListItemModel> listItemModel = [];
  List<AccountBalanceItem> multiAtypeItem = [];

  AtypeModel payAtype = AtypeModel.fromJson(null);
  AtypeModel cashierAtype = AtypeModel.fromJson(null);
  AtypeModel cardAtype = AtypeModel.fromJson(null);
  AtypeModel storedValueAtype = AtypeModel.fromJson(null);
  AtypeModel otherAtype = AtypeModel.fromJson(null);

  AccountBalanceDto? accountBalanceDto; //收款明细查询
  String? changeTime;

  double get saleAmount {
    Decimal temp = Decimal.zero;
    temp =
        MathUtil.add(cashierAtype.total.toString(), payAtype.total.toString());

    temp = MathUtil.add(cardAtype.total.toString(), temp.toString());

    temp = MathUtil.add(storedValueAtype.total.toString(), temp.toString());

    temp = MathUtil.add(otherAtype.total.toString(), temp.toString());

    return temp.toDouble();
  }

  double get cashBoxAmount{
   return (getDecimalStr(recAmount.toString()) +
        getDecimalStr(
            depositAmount.toString()) -
        getDecimalStr(
            takeoutAmount.toString()) -
        getDecimalStr(
            returnBoxAmount!.abs().toString())).toDouble();
  }

  List<AccountBalanceItem> accountBalanceList = [];
  Decimal getDecimalStr(String decimal) {
    return MathUtil.parse2Decimal(decimal);
  }
  ShiftChangesDto.fromJson(dynamic json) {
    if (json != null) {
      changeTime = json["changeTime"] ?? "";
      shopOrderCount = json["shopOrderCount"] ?? 0;
      shopOrderNoPayCount = json["shopOrderCount"] ?? 0;
      returnOrderCount = json["returnOrderCount"] ?? 0;
      exchangeOrderCount = json["exchangeOrderCount"] ?? 0;
      exchangeOrderNoPayCount = json["exchangeOrderCount"] ?? 0;
      shop = SpTool.getStoreInfo()!.fullname!;
      cashier = LoginCenter.getLoginUser()!.user!;
      cashierNumber = SpTool.getCashierInfo().fullname!;
      initialAmount = json["initialAmount"] ?? 0;
      receivableAmount = json["receivableAmount"] ?? 0;
      depositAmount = json["depositAmount"] ?? 0;
      takeoutAmount = json["takeoutAmount"].abs() ?? 0;
      recAmount = json["recAmount"] ?? 0;
      returnBoxAmount = json["returnBoxAmount"] ?? 0;
      returnAmount = json["returnAmount"] ?? 0;
      rechargeTotal = json["rechargeTotal"] ?? 0;
      recharge = json["recharge"] ?? 0;
      rechargeReturn = json["rechargeReturn"] ?? 0;

      multiAtype = [];
      if (json['multiAtype'] != null) {
        json['multiAtype'].forEach((v) {
          multiAtype.add(AtypeModel.fromJson(v));
        });
      }
      otherAtype = AtypeModel.fromJson(null);
      payAtype = AtypeModel.fromJson(null);
      cashierAtype = AtypeModel.fromJson(null);
      cardAtype = AtypeModel.fromJson(null);
      storedValueAtype = AtypeModel.fromJson(null);
    } else {
      shopOrderCount = 0;
      shopOrderNoPayCount = 0;
      returnOrderCount = 0;
      depositAmount = 0;
      takeoutAmount = 0;
      recAmount = 0;
      returnBoxAmount = 0;
      exchangeOrderCount = 0;
      exchangeOrderNoPayCount = 0;
      rechargeTotal = 0;
      recharge = 0;
      rechargeReturn = 0;
      saleTotal=0;
      returnTotal = 0;
      exChangTotal = 0;
    }
  }
}

class AtypeModel {
  String? atypeFullName;
  String? atypeId;
  double? total;

  AtypeModel.fromJson(dynamic json) {
    if (json != null) {
      atypeFullName = json["atypeFullName"] ?? "";
      atypeId = json["atypeId"] ?? "";
      total = json["total"] ?? 0;
    } else {
      atypeFullName = "";
      atypeId = "";
      total = 0;
    }
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map["atypeFullName"] = atypeFullName;
    map["atypeId"] = atypeId;
    map["total"] = total;
    return map;
  }
}

class ShiftChangesNewDto {
  String? billNumber;
  int? vchtype;
  String? payFullName;
  String? atypeId;
  int? payMethod;
  int? paywayType; //'0=现金，1=银行，2=淘淘谷，3=预存款',
  String? total;
  String? billTotal;
  int? payState;// 支付状态：0未支付，1已支付',

  ShiftChangesNewDto(
      {this.billNumber,
      this.vchtype,
      this.payFullName,
      this.atypeId,
      this.payMethod,
      this.paywayType,this.payState,
      this.total,this.billTotal});

  ShiftChangesNewDto.fromJson(Map<String, dynamic> json) {
    billNumber = json['billNumber'];
    vchtype = json['vchtype'];
    payFullName = json['payFullName'];
    atypeId = json['atypeId'];
    payMethod = json['payMethod'];
    paywayType = json['paywayType'];
    total = json['total'];
    billTotal = json['billTotal'];
    payState = json['payState'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['billNumber'] = this.billNumber;
    data['vchtype'] = this.vchtype;
    data['payFullName'] = this.payFullName;
    data['atypeId'] = this.atypeId;
    data['payMethod'] = this.payMethod;
    data['paywayType'] = this.paywayType;
    data['total'] = this.total;
    data['billTotal'] = this.billTotal;
    data['payState'] = this.payState;

    return data;
  }
}
