class ShiftChangesRequset {
  ///id
  String? newId;
  ///门店
  String? otypeId;

  ///收银机
  String? cashierId;

  ///开始时间
  String? startTime;

  String? changeTime;

  ///结束时间
  String? endTime;

  ///总销售额
  double? totalAmount;

  ///钱箱现金收款
  double? cashAmount;

  ///扫码支付收款
  double? payAmount;

  ///银行转账
  double? transferAmount;

  ///会员储值
  double? storedAmount;

  ///钱箱存入金额
  double? cashboxDeposit;

  ///钱箱取出金额
  double? cashboxTakeout;

  ///门店出库金额
  dynamic saleAmount;

  ///门店退货金额
  dynamic returnAmount;

  ///门店换货金额
  dynamic changeAmount;

  ///门店销售笔数
  dynamic saleTotal;

  ///门店退货笔数
  dynamic returnTotal;

  ///门店换货笔数
  dynamic changeTotal;

  ///未支付笔数
  dynamic unPayTotal;

  ///未支付金额
  dynamic unPayAmount;

  ///第三方金额
  dynamic otherThridAmount;

  ///第三方金额
  dynamic rechargeAmount;

  ///钱箱余额
  dynamic cashboxBalance;

  ShiftChangesRequset.fromJson(Map jsonMap) {
    if (jsonMap != null) {
      otypeId = jsonMap["otypeId"];
      cashierId = jsonMap["cashierId"];
      startTime = jsonMap["startTime"];
      endTime = jsonMap["endTime"];
      totalAmount = jsonMap["totalAmount"];
      cashAmount = jsonMap["cashAmount"];
      payAmount = jsonMap["payAmount"];
      transferAmount = jsonMap["transferAmount"];
      storedAmount = jsonMap["storedAmount"];
      changeTime = jsonMap["changeTime"];
      cashboxDeposit = jsonMap["cashboxDeposit"];
      cashboxTakeout = jsonMap["cashboxTakeout"].abs();
      returnAmount = jsonMap["returnAmount"];
      saleAmount = jsonMap["saleAmount"];
      saleTotal = jsonMap["saleTotal"];
      returnTotal = jsonMap["returnTotal"];
      unPayTotal = jsonMap["unPayTotal"];
      unPayAmount = jsonMap["unPayAmount"];
      otherThridAmount = jsonMap["otherThridAmount"];
      changeAmount = jsonMap["changeAmount"];
      changeTotal = jsonMap["changeTotal"];
      newId = jsonMap["newId"];
      rechargeAmount = jsonMap["rechargeAmount"];
      cashboxBalance = jsonMap["cashboxBalance"];

    }
  }

  Map<String, dynamic> toJson() => {
        //使用jsonDecode必须实现此方法
        "otypeId": otypeId,
        "cashierId": cashierId,
        "startTime": startTime,
    "changeTime": changeTime,
    "endTime": endTime,
        "totalAmount": totalAmount,
    "saleAmount": saleAmount,
    "cashAmount": cashAmount,
        "payAmount": payAmount,
        "transferAmount": transferAmount,
        "storedAmount": storedAmount,
        "cashboxDeposit": cashboxDeposit,
        "cashboxTakeout": cashboxTakeout,
        "returnAmount": returnAmount,
        "saleTotal": saleTotal,
        "returnTotal": returnTotal,
    "unPayTotal": unPayTotal,
    "unPayAmount": unPayAmount,
    "otherThridAmount": otherThridAmount,
    "changeAmount": changeAmount,
    "changeTotal": changeTotal,
    "newId": newId,
    "rechargeAmount": rechargeAmount,
    "cashboxBalance": cashboxBalance,

  };
}
