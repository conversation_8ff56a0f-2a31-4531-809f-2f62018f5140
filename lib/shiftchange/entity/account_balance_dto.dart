/// pageIndex : 1
/// pageSize : 200
/// total : "5"
/// list : [{"billDate":"2021-11-29T03:36:52.000+0000","billNumber":"PXX-********-00044","vchtype":"2000","btypeUsercode":"测试","btypeFullname":"测试门店往来单位","eFullname":"zb","eUsercode":"zb","total":333.********,"billTotal":11.********,"summary":"","memo":"","atypeId":"617794922661027843","typeId":"***************","atypeFullname":"现金","atypeUsercode":"100101","inOrOut":1},{"billDate":"2021-11-29T02:02:58.000+0000","billNumber":"PXX-********-00030","vchtype":"2000","btypeUsercode":"测试","btypeFullname":"测试门店往来单位","eFullname":"zb","eUsercode":"zb","total":12.********,"billTotal":11.********,"summary":"","memo":"","atypeId":"657040214291707045","typeId":"000010000400003","atypeFullname":"账户1","atypeUsercode":"账户1","inOrOut":1},{"billDate":"2021-11-29T03:35:38.000+0000","billNumber":"PXX-********-00041","vchtype":"2000","btypeUsercode":"测试","btypeFullname":"测试门店往来单位","eFullname":"zb","eUsercode":"zb","total":123.********,"billTotal":11.********,"summary":"","memo":"","atypeId":"657040214291707045","typeId":"000010000400003","atypeFullname":"账户1","atypeUsercode":"账户1","inOrOut":1},{"billDate":"2021-11-29T03:36:05.000+0000","billNumber":"PXX-********-00042","vchtype":"2000","btypeUsercode":"测试","btypeFullname":"测试门店往来单位","eFullname":"zb","eUsercode":"zb","total":123.********,"billTotal":11.********,"summary":"","memo":"","atypeId":"657040334550791333","typeId":"***************","atypeFullname":"账户2","atypeUsercode":"账户2","inOrOut":1},{"billDate":"2021-11-29T03:36:39.000+0000","billNumber":"PXX-********-00043","vchtype":"2000","btypeUsercode":"测试","btypeFullname":"测试门店往来单位","eFullname":"zb","eUsercode":"zb","total":1232.********,"billTotal":11.********,"summary":"","memo":"","atypeId":"657040334550791333","typeId":"***************","atypeFullname":"账户2","atypeUsercode":"账户2","inOrOut":1}]

class AccountBalanceDto {
  AccountBalanceDto({
    required int pageIndex,
    required int pageSize,
    required String total,
    required List<AccountBalanceItem> list,
  }) {
    _pageIndex = pageIndex;
    _pageSize = pageSize;
    _total = total;
    _list = list;
  }

  AccountBalanceDto.fromJson(dynamic json) {
    _pageIndex = json['pageIndex'];
    _pageSize = json['pageSize'];
    _total = json['total'];
    if (json['list'] != null) {
      _list = [];
      json['list'].forEach((v) {
        _list.add(AccountBalanceItem.fromJson(v));
      });
    }
  }

  late int _pageIndex;
  late int _pageSize;
  late String _total;
  late List<AccountBalanceItem> _list;

  int get pageIndex => _pageIndex;

  int get pageSize => _pageSize;

  String get total => _total;

  List<AccountBalanceItem> get list => _list;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['pageIndex'] = _pageIndex;
    map['pageSize'] = _pageSize;
    map['total'] = _total;
    if (_list != null) {
      map['list'] = _list.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

/// billDate : "2021-11-29T03:36:52.000+0000"
/// billNumber : "PXX-********-00044"
/// vchtype : "2000"
/// btypeUsercode : "测试"
/// btypeFullname : "测试门店往来单位"
/// eFullname : "zb"
/// eUsercode : "zb"
/// total : 333.********
/// billTotal : 11.********
/// summary : ""
/// memo : ""
/// atypeId : "617794922661027843"
/// typeId : "***************"
/// atypeFullname : "现金"
/// atypeUsercode : "100101"
/// inOrOut : 1

class AccountBalanceItem {
  AccountBalanceItem({
    String? billDate,
    String? billNumber,
    String? vchtype,
    String? btypeUsercode,
    String? btypeFullname,
    String? eFullname,
    String? eUsercode,
    double? total,
    double? billTotal,
    String? summary,
    String? memo,
    String? atypeId,
    String? typeId,
    String? atypeFullname,
    String? atypeUsercode,
    int? inOrOut,
  }) {
    _billDate = billDate;
    _billNumber = billNumber;
    _vchtype = vchtype;
    _btypeUsercode = btypeUsercode;
    _btypeFullname = btypeFullname;
    _eFullname = eFullname;
    _eUsercode = eUsercode;
    _total = total;
    _billTotal = billTotal;
    _summary = summary;
    _memo = memo;
    _atypeId = atypeId;
    _typeId = typeId;
    _atypeFullname = atypeFullname;
    _atypeUsercode = atypeUsercode;
    _inOrOut = inOrOut;
  }

  AccountBalanceItem.fromJson(dynamic json) {
    _billDate = json['billDate'];
    _billNumber = json['billNumber'];
    _vchtype = json['vchtype'];
    _btypeUsercode = json['btypeUsercode'];
    _btypeFullname = json['btypeFullname'];
    _eFullname = json['eFullname'];
    _eUsercode = json['eUsercode'];
    _total = json['total'];
    _billTotal = json['billTotal'];
    _summary = json['summary'];
    _memo = json['memo'];
    _atypeId = json['atypeId'];
    _typeId = json['typeId'];
    _atypeFullname = json['atypeFullname'];
    _atypeUsercode = json['atypeUsercode'];
    _inOrOut = json['inOrOut'];
  }

  String? _billDate;
  String? _billNumber;
  String? _vchtype;
  String? _btypeUsercode;
  String? _btypeFullname;
  String? _eFullname;
  String? _eUsercode;
  dynamic _total;
  double? _billTotal;
  String? _summary;
  String? _memo;
  String? _atypeId;
  String? _typeId;
  String? _atypeFullname;
  String? _atypeUsercode;
  int? _inOrOut;

  String? get billDate => _billDate;

  String? get billNumber => _billNumber;

  String? get vchtype => _vchtype;

  String? get btypeUsercode => _btypeUsercode;

  String? get btypeFullname => _btypeFullname;

  String? get eFullname => _eFullname;

  String? get eUsercode => _eUsercode;

  dynamic get total => _total;

  double? get billTotal => _billTotal;

  String? get summary => _summary;

  String? get memo => _memo;

  String? get atypeId => _atypeId;

  String? get typeId => _typeId;

  String? get atypeFullname => _atypeFullname;

  String? get atypeUsercode => _atypeUsercode;

  int? get inOrOut => _inOrOut;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['billDate'] = _billDate;
    map['billNumber'] = _billNumber;
    map['vchtype'] = _vchtype;
    map['btypeUsercode'] = _btypeUsercode;
    map['btypeFullname'] = _btypeFullname;
    map['eFullname'] = _eFullname;
    map['eUsercode'] = _eUsercode;
    map['total'] = _total;
    map['billTotal'] = _billTotal;
    map['summary'] = _summary;
    map['memo'] = _memo;
    map['atypeId'] = _atypeId;
    map['typeId'] = _typeId;
    map['atypeFullname'] = _atypeFullname;
    map['atypeUsercode'] = _atypeUsercode;
    map['inOrOut'] = _inOrOut;
    return map;
  }
}
