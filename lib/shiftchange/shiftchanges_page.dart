import 'dart:math';

import "package:collection/collection.dart";
import 'package:decimal/decimal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/math_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';

import '../../../bill/tool/decimal_display_helper.dart';
import '../../../common/login/login_center.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/permission_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../print/tool/print_tool.dart';
import '../../../shiftchange/entity/account_balance_dto.dart';
import '../../../shiftchange/entity/shift_changes_dto.dart';
import '../../../shiftchange/entity/shift_changes_requset.dart';
import '../../../shiftchange/model/shift_changes_model.dart';
import '../../../shiftchange/widget/pie_chart.dart';
import '../../../shiftchange/widget/proceeds_account_detial.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/halo_pos_label.dart';
import '../application.dart';
import '../common/tool/performance_capture_util.dart';
import '../common/tool/sp_custom_util.dart';
import '../login/login_page.dart';
import '../login/model/store_model.dart';
import '../offline/offline_tool.dart';
import 'widget/login_out_dialog.dart';
import 'widget/shiftchanges_record.dart';

const List<Color> chartsColors = [
  Color(0xFF2288FC),
  Color(0xFFFF786D),
  Color(0xFF00C79A),
  Color(0xFFAFD527),
  Color(0xFFFDAB2B),
  Color(0xFFA26FEA),
  Color(0xFF49A9C5),
  Color(0xFF818B43),
  Color(0xFFFF6B02),
  Color(0xFFFE4C32),
];

class ListItemModel {
  String title;
  num? value;
  Color color;

  ListItemModel({required this.title, this.value, this.color = Colors.green});
}

class ShiftChangesPages extends BaseStatefulPage {
  const ShiftChangesPages({Key? key}) : super(key: key);

  @override
  BaseStatefulPageState createState() => _ShiftChangesPagesState();
}

class _ShiftChangesPagesState extends BaseStatefulPageState<ShiftChangesPages> {
  ShiftChangesDto shiftChangesDto = ShiftChangesDto.fromJson(null);
  ShiftChangesDto shiftChangesLastDto = ShiftChangesDto.fromJson(null);
  bool isPrint = SpTool.getPrintShiftChanges();
  List<AccountBalanceItem> accountBalanceList = [];
  List<AccountBalanceItem> accountBalanceListNotZero = [];
  final PermissionDto _permissionDto = SpTool.getPermission();

  @override
  void initState() {
    super.initState();
    getData();
  }

  Color getRandomColor({int r = 255, int g = 255, int b = 255, a = 255}) {
    if (r == 0 || g == 0 || b == 0) return Colors.black;
    if (a == 0) return Colors.white;
    return Color.fromARGB(
      a,
      r != 255 ? r : Random.secure().nextInt(r),
      g != 255 ? g : Random.secure().nextInt(g),
      b != 255 ? b : Random.secure().nextInt(b),
    );
  }

  getData() {
    PerformanceCaptureUtil.start(PerformanceTimeName.shiftChangesLoad);
    Map parmas = {
      "startTime":
          DateUtil.formatDate(DateUtil.getDateTimeByMs(SpTool.getLoginTime())),
      // "endTime":
      //     DateUtil.formatDate(DateTime.now().add(const Duration(hours: 1))),
      "otypeId": SpTool.getStoreInfo()!.otypeId,
      "cashierId": SpTool.getCashierInfo().id,
    };

    Map parmasNew = {
      //DateUtil.formatDate(DateUtil.getDateTimeByMs(SpTool.getLoginTime()))
      "postBeginTime":
          DateUtil.formatDate(DateUtil.getDateTimeByMs(SpTool.getLoginTime())),
      // "postEndTime":
      //     DateUtil.formatDate(DateTime.now().add(const Duration(hours: 1))),
      "otypeId": SpTool.getStoreInfo()!.otypeId,
      "createEtypeId": LoginCenter.getLoginUser().employeeId,
      "vchtypes": [2100, 2000, 2200, 4001]
    };

    ShiftChangesModel.getShiftChangeStatistics(context, parmas).then((value) {
      shiftChangesDto = value;
      ShiftChangesModel.getPostTimeAndVchtypeList(context, parmasNew)
          .then((valueNew) {
        _resetShiftChangesDto(valueNew);
        PerformanceCaptureUtil.end(PerformanceTimeName.shiftChangesLoad);
      });
    });
  }

  //对数据做分解
  List<ListItemModel> _getSaleTypeList() {
    var newMap = groupBy(
        accountBalanceListNotZero, (AccountBalanceItem obj) => obj.atypeId);
    List<ListItemModel> temp = [];
    int index = 0;
    newMap.forEach((key, value) {
      var sum = value.map((e) => num.parse(e.total ?? "0")).fold(
          0,
          (num previousValue, element) => num.parse(
              DecimalDisplayHelper.getTotalFixed(
                  (previousValue + element).toString())));
      temp.add(ListItemModel(
          title: value[0].atypeFullname ?? "",
          value: sum,
          color: index > 10 ? getRandomColor() : chartsColors[index]));
      index++;
    });

    shiftChangesDto.listItemModel = temp;
    return temp;
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      direction: Axis.vertical,
      children: [_buildHeaderCard(), _buildContentCard(), _buildFooterCard()],
    );
  }

  //中间饼图卡片
  Widget _buildHeaderCard() {
    return HaloContainer(
      color: Colors.white,
      height: 185.w,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      borderRadius: BorderRadius.all(Radius.circular(15.w)),
      margin: EdgeInsets.only(top: 35.w, left: 34.w, right: 34.w),
      children: [_buildHeaderLeft(), _buildHeaderRight()],
    );
  }

  //顶部信息
  Widget _buildHeaderLeft() {
    return HaloContainer(
      children: [
        ///图片
        Container(
          margin: EdgeInsets.only(left: 35.w),
          width: 116.w,
          height: 116.w,
          child: IconFont(
            IconNames.shouyingyuan,
            size: 116.w,
          ),
        ),
        HaloContainer(
          direction: Axis.vertical,
          width: ScreenUtil().screenWidth - 320.w * 3 - 250.w,
          padding: EdgeInsets.only(left: 20.w),
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            HaloContainer(
              padding: EdgeInsets.only(bottom: 14.w),
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Flexible(
                  child: HaloPosLabel(
                    LoginCenter.getLoginUser().user ?? "",
                    textStyle:
                        TextStyle(fontWeight: FontWeight.w500, fontSize: 36.sp),
                  ),
                ),
                SizedBox(
                  width: 21.w,
                ),
                Flexible(
                  flex: 3,
                  child: HaloPosLabel(
                    "门店：${SpTool.getStoreInfo()!.fullname ?? ""}",
                    textStyle: TextStyle(fontSize: 26.sp),
                  ),
                ),
                SizedBox(
                  width: 21.w,
                ),
                Flexible(
                  flex: 3,
                  child: HaloPosLabel(
                      "收银机：${SpTool.getCashierInfo().fullname ?? ""}",
                      textStyle: TextStyle(fontSize: 26.sp)),
                ),
              ],
            ),
            HaloPosLabel(
                "登录时间：${DateUtil.formatDate(DateUtil.getDateTimeByMs(SpTool.getLoginTime()))}",
                textStyle: TextStyle(fontSize: 26.sp)),
          ],
        ),
      ],
    );
  }

  Widget _buildHeaderRight() {
    return HaloContainer(
      children: [
        _buildTopRightItem(
            "总单据数（已支付）",
            (shiftChangesDto.returnOrderCount!.toInt() +
                    shiftChangesDto.shopOrderCount!.toInt() +
                    shiftChangesDto.exchangeOrderCount!.toInt())
                .toString()),
        verticalLine(),
        _buildTopRightItem("销售收入（已支付）",
            "¥${DecimalDisplayHelper.getTotalFixed(shiftChangesDto.saleAmount.toString())}"),
        verticalLine(),
        _buildTopRightItem("储值金额",
            "¥${DecimalDisplayHelper.getTotalFixed(shiftChangesDto.rechargeTotal.toString())}"),
        verticalLine(),
        _buildTopRightItem("钱箱",
            "¥${getDecimalStr(shiftChangesDto.recAmount.toString()) + getDecimalStr(shiftChangesDto.depositAmount.toString()) - getDecimalStr(shiftChangesDto.takeoutAmount.toString()) - getDecimalStr(shiftChangesDto.returnBoxAmount!.abs().toString())}"),
      ],
    );
  }

  Decimal getDecimalStr(String decimal) {
    return MathUtil.parse2Decimal(decimal);
  }

  Widget verticalLine() {
    return Container(
      width: 1.w,
      height: 36.w,
      color: const Color(0xFF979797),
    );
  }

  Widget _buildTopRightItem(String title, String value) {
    return HaloContainer(
      padding: const EdgeInsets.only(left: 10, right: 10),
      width: 240.w,
      height: 185.w,
      mainAxisAlignment: MainAxisAlignment.center,
      direction: Axis.vertical,
      children: [
        HaloPosLabel(
          value,
          textStyle: TextStyle(
            fontSize: 36.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(
          height: 15.w,
        ),
        HaloPosLabel(
          title,
          textStyle: TextStyle(
              fontSize: 30.sp, color: ColorUtil.stringColor("#666666")),
        ),
      ],
    );
  }

  Widget _buildContentCard() {
    return Expanded(
        child: HaloContainer(
      width: ScreenUtil().screenWidth,
      direction: Axis.vertical,
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 顶部的数据
        Expanded(
          child: HaloContainer(
            margin: EdgeInsets.only(top: 36.w, bottom: 36.w),
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(width: 20.w),
              Expanded(
                child: _buildItemCard(
                    title: "总单据数",
                    topColor: Colors.red,
                    data: getCenterDataList(),
                    bgColor: Colors.lightBlueAccent),
              ),
              SizedBox(width: 20.w),
              Expanded(
                child: _buildItemCard(
                    title: '销售收入（已支付）',
                    topColor: Colors.blueAccent,
                    data: _getSaleTypeList(),
                    bgColor: Colors.lightBlueAccent,
                    isNeedDetail: true,
                    onPressed: () {
                      _showInventory(context);
                    }),
              ),
              SizedBox(width: 20.w),
              Expanded(
                child: _buildItemCard(
                    title: '   钱箱',
                    topColor: Colors.greenAccent,
                    data: [
                      ListItemModel(
                          title: '收款金额:',
                          value: shiftChangesDto.recAmount,
                          color: chartsColors[0]),
                      ListItemModel(
                          title: '退款金额:',
                          value: shiftChangesDto.returnBoxAmount!.abs(),
                          color: chartsColors[1]),
                      ListItemModel(
                          title: '存入金额:',
                          value: shiftChangesDto.depositAmount,
                          color: chartsColors[2]),
                      ListItemModel(
                          title: '取出金额:',
                          value: shiftChangesDto.takeoutAmount,
                          color: chartsColors[3])
                    ],
                    total:
                        (getDecimalStr(shiftChangesDto.recAmount.toString()) +
                                getDecimalStr(
                                    shiftChangesDto.depositAmount.toString()) -
                                getDecimalStr(
                                    shiftChangesDto.takeoutAmount.toString()) -
                                getDecimalStr(shiftChangesDto.returnBoxAmount!
                                    .abs()
                                    .toString()))
                            .toString(),
                    bgColor: Colors.green),
              ),
              SizedBox(width: 20.w),
            ],
          ),
        ),
      ],
    ));
  }

  //底部widget
  Widget _buildFooterCard() {
    return HaloContainer(
      mainAxisAlignment: MainAxisAlignment.center,
      width: double.infinity,
      color: Colors.white,
      height: 160.w,
      children: [
        Expanded(child: cupertionItem()),
        HaloButton(
          height: 106.w,
          borderRadius: 12,
          width: ScreenUtil().screenWidth / 3,
          fontWeight: FontWeight.w500,
          buttonType: HaloButtonType.elevatedButton,
          onPressed: () {
            if (OffLineTool().isOfflineLogin) {
              _offLineLoginOut();
            } else {
              _onLineLoginOut();
            }
          },
          elevation: MaterialStateProperty.all(0),
          text: "交接班",
        ),
      ],
    );
  }

  _offLineLoginOut() {
    HaloToast.showMsg(context, msg: "离线模式下，不支持交接班");
    // LoginCenter.loginOut(context, loginOutSuccess: () {})
    //     .whenComplete(() {
    //   SpTool.saveShiftChangesInfo(0);
    //   SpTool.clearPromotionList();
    //   SpTool.cleanTenderManageConfig();
    //   SpTool.cleanTransferConfig();
    //   SpTool.saveAutoLogin(false);
    //   SpTool.saveEtypeInfo("", "");
    //   DatabaseHelper.close();
    //   StockSyncTimer.stop();
    //   _loginOut();
    // });
  }

  _onLineLoginOut() {
    if (!_permissionDto.shopsaleshiftchangesout!) {
      HaloToast.showError(context, msg: "没有交接班退出的权限");
      return;
    }
    double boxTatol = (getDecimalStr(shiftChangesDto.recAmount.toString()) +
            getDecimalStr(shiftChangesDto.depositAmount.toString()) -
            getDecimalStr(shiftChangesDto.takeoutAmount.toString()) -
            getDecimalStr(shiftChangesDto.returnBoxAmount!.abs().toString()))
        .toDouble();
    // if (boxTatol < 0) {
    //   DialogUtil.showAlertDialog(context,
    //       content: "当前交接班钱箱金额为负数，请处理后再交班");
    //   return;
    // }
    ShiftChangesModel.saveShiftChangesRecord(context, getRequsetBody())
        .then((value) async {
      shiftChangesDto.changeTime = value!.changeTime;
      if (isPrint) {
        PrintTool.printShiftChanges(context, shiftChangesDto: shiftChangesDto);
      }
      await StoreModel.updateLoginRecord(context);
      PrintTool.openCashBox(context);
      if (context.mounted) {
        DialogUtil.showAlertDialog(context,
                child: LoginOutDialog(
                  isEnabled: _permissionDto.shopsaleshiftchangesprint!,
                  printClick: () {
                    ///打印
                    PrintTool.printShiftChanges(context,
                        shiftChangesDto: shiftChangesDto);
                  },
                ),
                dismissOnTouchOutside: false)
            .then((value) {
          if (value) {
            LoginCenter.loginOut(context, loginOutSuccess: () {})
                .whenComplete(() {
              SpTool.saveShiftChangesInfo(0);
              SpTool.clearPromotionList();
              SpTool.cleanTenderManageConfig();
              SpTool.cleanTransferConfig();
              SpTool.saveAutoLogin(false);
              SpTool.saveEtypeInfo("", "");
              SpCustomUtil.backupPreferences();
              _loginOut();
            });
          }
        });
      }
    });
  }

  _loginOut() {
    debugPrint("_loginOut_begin");
    debugPrint("_loginOut_ing+++++${Application.loginOut}");
    if (null != Application.loginOut) {
      debugPrint("_loginOut_ing++++111+${Application.loginOut}");
      Application.loginOut!(context);
      return;
    }
    debugPrint("_loginOut_end");
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false);
  }

  Widget cupertionItem() {
    return HaloContainer(
      mainAxisAlignment: MainAxisAlignment.end,
      padding: EdgeInsets.only(right: 43.w),
      children: [
        _cupertinoSwitch(),
        HaloPosLabel(
          "交接班时打印小票",
          textStyle: TextStyle(fontSize: 30.sp),
        ),
      ],
    );
  }

  //开关
  CupertinoSwitch _cupertinoSwitch() {
    return CupertinoSwitch(
      // 当前 switch 的开关
      value: isPrint,
      // 点击或者拖拽事件
      onChanged: (value) {
        if (!_permissionDto.shopsaleshiftchangesprint!) {
          HaloToast.showError(context, msg: "没有更改交接班打印的权限");
          return;
        }
        setState(() {
          isPrint = value;
          SpTool.savePrintShiftChanges(value);
        });
      },
    );
  }

  //单个数据卡片
  Widget _buildItemCard(
      {required String title,
      required Color topColor,
      List<ListItemModel>? data,
      required Color bgColor,
      String? total,
      bool isNeedDetail = false,
      Function()? onPressed}) {
    List<PieIndicator> children = data
            ?.map((element) => PieIndicator(
                itemData: PieItemData(
                    color: element.color,
                    text: element.title,
                    value: element.value)))
            .toList() ??
        [];
    return HaloContainer(
      padding: EdgeInsets.only(
          top: 40.h, bottom: isNeedDetail ? 80.w : 30.w, left: 40.h),
      borderRadius: BorderRadius.all(Radius.circular(20.w)),
      color: Colors.white,
      direction: Axis.vertical,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        HaloContainer(
          direction: Axis.vertical,
          children: [
            Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: AppColorHelper(context).getTitleBoldTextColor(),
                        fontSize: 32.sp),
                  ),
                ),
                Expanded(
                  child: isNeedDetail
                      ? Container(
                          alignment: Alignment.center,
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 20.w, vertical: 0.h),
                            child: GestureDetector(
                              onTap: onPressed,
                              child: Text("查看明细",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 30.sp,
                                    color: ColorUtil.stringColor("#4679FC"),
                                  )),
                            ),
                          ),
                        )
                      : Container(),
                )
              ],
            ),
            Visibility(
                visible: isNeedDetail,
                child: HaloContainer(
                  direction: Axis.vertical,
                  padding: EdgeInsets.only(left: 0.w, right: 0.w, top: 10.w),
                  children: [
                    HaloContainer(
                      direction: Axis.horizontal,
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: Container(
                                alignment: Alignment.center,
                                child: HaloPosLabel(
                                    "销售出库：${shiftChangesDto.saleTotal ?? 0}"))),
                        Expanded(
                            child: Container(
                                alignment: Alignment.center,
                                child: HaloPosLabel(
                                    "销售退款：${shiftChangesDto.returnTotal ?? 0}"))),
                      ],
                    ),
                    HaloContainer(
                      direction: Axis.horizontal,
                      children: [
                        Expanded(
                            child: Container(
                                alignment: Alignment.center,
                                child: HaloPosLabel(
                                    "销售换货：${shiftChangesDto.exChangTotal ?? 0}"))),
                        Expanded(child: Container())
                      ],
                    ),
                  ],
                ))
          ],
        ),
        //顶部
        // 数据
        Expanded(
          child: PieChartWidget(
            width: double.infinity,
            selectIndex: -1,
            pieDataList: children,
            resultValue: total,
          ),
        ),
      ],
    );
  }

  ///重构交接班dto
  _resetShiftChangesDto(List<ShiftChangesNewDto> list) {
    int saleNum = 0; //门店销售单数
    int returnNum = 0; //门店退货单单数
    int exchangeNum = 0; //门店换货单单数
    int saleNumNoPay = 0; //门店销售单数-未支付
    int exchangeNumNoPay = 0; //门店换货单单数-未支付

    Decimal cashTotal = Decimal.zero; //钱箱现金收款
    Decimal payTotal = Decimal.zero; //扫码支付收款
    Decimal noPayTotal = Decimal.zero; //扫码支付收款-未支付

    Decimal cardTotal = Decimal.zero; //银行转账收款
    Decimal storedValueTotal = Decimal.zero; //会员储值收款 已包含赠金 需要测试
    Decimal otherTotal = Decimal.zero; //其他销售额

    Decimal saleTotal = Decimal.zero; //销售出库
    Decimal returnTotal = Decimal.zero; //销售退款
    Decimal exChangeTotal = Decimal.zero; //销售退款

    Decimal rechargeTotal = Decimal.zero; //储值金额

    List<AtypeModel> multiAtype = []; //其他支付方式
    accountBalanceList.clear();

    //数据解析
    for (var element in list) {
      if (element.vchtype == 2000) {
        Decimal total = Decimal.fromBigInt(BigInt.from(0));
        try {
          total = MathUtil.parse2Decimal(element.total);
        } catch (_) {}

        AccountBalanceItem accountBalanceDto = AccountBalanceItem.fromJson({
          "billNumber": element.billNumber,
          "atypeFullname": element.payFullName,
          "total": element.total,
          "atypeId": element.atypeId
        });

        if (element.payState == 0) {
          noPayTotal = total + noPayTotal;
          continue;
        }

        if (element.paywayType == 0) {
          cashTotal = total + cashTotal;
        } else if (element.paywayType == 2) {
          payTotal = total + payTotal;
        } else if (element.paywayType == 1) {
          cardTotal = total + cardTotal;
        } else if (element.paywayType == 3) {
          storedValueTotal = total + storedValueTotal;
        } else {
          otherTotal = total + otherTotal;
          shiftChangesDto.multiAtypeItem.add(accountBalanceDto);
        }
        if (element.payState == 1) {
          accountBalanceList.add(accountBalanceDto);
          saleTotal = saleTotal + total;
        }
      } else if (element.vchtype == 2200) {
        Decimal total = Decimal.fromBigInt(BigInt.from(0));
        try {
          total = MathUtil.parse2Decimal(element.total!);
        } catch (e) {}

          AccountBalanceItem accountBalanceDto = AccountBalanceItem.fromJson({
            "billNumber": element.billNumber,
            "atypeFullname": element.payFullName,
            "total": element.total??"0",
            "atypeId": element.atypeId
          });
          if(element.payState == 0){
            noPayTotal = total + noPayTotal;
            continue;
          }
          if (element.paywayType == 0) {
            cashTotal = total + cashTotal;
          } else if (element.paywayType == 2) {
            payTotal = total + payTotal;

          } else if (element.paywayType == 1) {
            cardTotal = total + cardTotal;
          } else if (element.paywayType == 3) {
            storedValueTotal = total + storedValueTotal;
          } else {
            otherTotal = total + otherTotal;
            shiftChangesDto.multiAtypeItem.add(accountBalanceDto);
          }
          if(element.payState == 1){
            accountBalanceList.add(accountBalanceDto);
            exChangeTotal=total+exChangeTotal;
          }

      }else if (element.vchtype == 2100){
        Decimal total = Decimal.fromBigInt(BigInt.from(0));
        try {
          total = MathUtil.parse2Decimal(element.total ?? "0");
        } catch (e) {}

        AccountBalanceItem accountBalanceDto = AccountBalanceItem.fromJson({
          "billNumber": element.billNumber,
          "atypeFullname": element.payFullName,
          "total":
              Decimal.tryParse("-${element.total ?? "0"}")?.toString() ?? "0",
          "atypeId": element.atypeId ?? "0"
        });
        if (element.payState == 0) {
          continue;
        }
        if (element.paywayType == 0) {
          cashTotal = cashTotal - total;
        } else if (element.paywayType == 2) {
          payTotal = payTotal - total;
        } else if (element.paywayType == 1) {
          cardTotal = cardTotal - total;
        } else if (element.paywayType == 3) {
          storedValueTotal = storedValueTotal - total;
        } else {
          otherTotal = otherTotal - total;
          shiftChangesDto.multiAtypeItem.add(accountBalanceDto);
        }

        accountBalanceList.add(accountBalanceDto);
        returnTotal = returnTotal + total;
      } else if (element.vchtype == 4001) {
        Decimal total = MathUtil.parse2Decimal(element.total ?? "0");
        rechargeTotal += total;
      }
    }

    final ids = list.map((e) => e.billNumber).toSet();
    list.retainWhere((x) => ids.remove(x.billNumber));
    for (var element in list) {
      if (element.vchtype == 2000 && element.atypeId != null) {
        if (element.payState == 0) {
          saleNumNoPay++;
        } else {
          saleNum++;
        }
      } else if (element.vchtype == 2100) {
        if (element.payState == 1) {
          returnNum++;
        }
      } else if (element.vchtype == 2200) {
        if (element.payState == 0) {
          exchangeNumNoPay++;
        } else {
          exchangeNum++;
        }
      }
    }

    accountBalanceListNotZero =
        accountBalanceList.where((element) => element.atypeId != null).toList();

    AtypeModel atypeModelCash = AtypeModel.fromJson(null);
    atypeModelCash.total = cashTotal.toDouble();
    AtypeModel atypeModelPay = AtypeModel.fromJson(null);
    atypeModelPay.total = payTotal.toDouble();
    AtypeModel atypeModelCard = AtypeModel.fromJson(null);
    atypeModelCard.total = cardTotal.toDouble();
    AtypeModel atypeStoredValue = AtypeModel.fromJson(null);
    atypeStoredValue.total = storedValueTotal.toDouble();
    AtypeModel otherPay = AtypeModel.fromJson(null);
    otherPay.total = otherTotal.toDouble();

    shiftChangesDto.multiAtype = multiAtype;
    shiftChangesDto.shopOrderCount = saleNum.toDouble();
    shiftChangesDto.shopOrderNoPayCount = saleNumNoPay.toDouble();
    shiftChangesDto.returnOrderCount = returnNum.toDouble();
    shiftChangesDto.exchangeOrderCount = exchangeNum.toDouble();
    shiftChangesDto.exchangeOrderNoPayCount = exchangeNumNoPay.toDouble();
    shiftChangesDto.cashierAtype = atypeModelCash;
    shiftChangesDto.payAtype = atypeModelPay;
    shiftChangesDto.cardAtype = atypeModelCard;
    shiftChangesDto.storedValueAtype = atypeStoredValue;
    shiftChangesDto.otherAtype = otherPay;
    shiftChangesDto.saleTotal = saleTotal.toDouble();
    shiftChangesDto.returnTotal = returnTotal.toDouble();
    shiftChangesDto.exChangTotal = exChangeTotal.toDouble();
    shiftChangesDto.noPayAmount = noPayTotal.toDouble();
    shiftChangesDto.rechargeTotal = rechargeTotal.toDouble();
    setState(() {});
  }

  ///构建交接班记录请求体
  ShiftChangesRequset getRequsetBody() {
    return ShiftChangesRequset.fromJson({
      "otypeId": SpTool.getStoreInfo()!.otypeId,
      "cashierId": SpTool.getCashierInfo().id,
      "startTime":
          DateUtil.formatDate(DateUtil.getDateTimeByMs(SpTool.getLoginTime())),
      "changeTime": DateUtil.formatDate(DateTime.now()),
      "totalAmount": shiftChangesDto.saleAmount,
      "cashAmount": shiftChangesDto.recAmount ?? 0,
      "transferAmount": shiftChangesDto.cardAtype.total ?? 0,
      "storedAmount": shiftChangesDto.storedValueAtype.total ?? 0,
      "payAmount": shiftChangesDto.payAtype.total ?? 0,
      "cashboxDeposit": shiftChangesDto.depositAmount ?? 0,
      "cashboxTakeout": shiftChangesDto.takeoutAmount ?? 0,
      "returnAmount": shiftChangesDto.returnTotal?.abs() ?? 0,
      "changeAmount": shiftChangesDto.exChangTotal ?? 0,
      "saleTotal": shiftChangesDto.shopOrderCount?.toInt() ?? 0,
      "returnTotal": shiftChangesDto.returnOrderCount?.abs() ?? 0,
      "changeTotal": shiftChangesDto.exchangeOrderCount?.abs() ?? 0,
      "unPayTotal": (shiftChangesDto.shopOrderNoPayCount?.toInt() ?? 0) +
          (shiftChangesDto.exchangeOrderNoPayCount?.toInt() ?? 0),
      "unPayAmount": shiftChangesDto.noPayAmount ?? 0,
      "otherThridAmount": shiftChangesDto.otherAtype.total ?? 0,
      "saleAmount": shiftChangesDto.saleTotal ?? 0,
      "rechargeAmount": shiftChangesDto.rechargeTotal ?? 0
    });
  }

  List<ListItemModel> getCenterDataList() {
    List<ListItemModel> list = [
      ListItemModel(
          title: '销售:',
          value: shiftChangesDto.shopOrderCount!.toInt(),
          color: chartsColors[0]),
      ListItemModel(
          title: '销售未支付:',
          value: shiftChangesDto.shopOrderNoPayCount!.toInt(),
          color: chartsColors[1]),
      ListItemModel(
          title: '退货:',
          value: shiftChangesDto.returnOrderCount!.toInt(),
          color: chartsColors[3]),
      ListItemModel(
          title: '换货:',
          value: shiftChangesDto.exchangeOrderCount!.toInt(),
          color: chartsColors[4]),
      ListItemModel(
          title: '换货未支付:',
          value: shiftChangesDto.exchangeOrderNoPayCount!.toInt(),
          color: chartsColors[5]),
    ];
    if (shiftChangesDto.shopOrderNoPayCount == 0) {
      list.removeAt(1);
    }
    if (shiftChangesDto.exchangeOrderNoPayCount == 0) {
      list.removeLast();
    }
    return list;
  }

  @override
  String getActionBarTitle() {
    return "交接班";
  }

  @override
  Future<void> onInitState() {
    return Future.value(null);
  }

  ///收款账户明细
  _showInventory(BuildContext context) {
    if (!_permissionDto.shopsaleshiftchangessaledetail!) {
      HaloToast.showError(context, msg: "没有查看明细的权限");
      return;
    }
    DialogUtil.showAlertDialog(context,
        child: ProceedsAccountDetial(
          list: accountBalanceListNotZero,
        ));
  }
}
