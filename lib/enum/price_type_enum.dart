/// author : huang bo
/// time   : 2020/6/22 09:40
/// email  : <EMAIL>
/// desc   : 价格类型

enum PriceTypeEnum {
  /// 采购
  BUY,

  /// 销售
  SALE,

  /// 预估成本
  PROSPECT,

  /// 库存类
  STOCK,

  /// 销售价格控制
  SALE_CONTROL,

  /// 采购价格控制
  BUY_CONTROL,

  /// 全月成本单价
  COST,

  /// 其他
  OTHER,
}

const Map<PriceTypeEnum, String> PriceTypeEnumData = {
  PriceTypeEnum.BUY: "BUY",
  PriceTypeEnum.SALE: "SALE",
  PriceTypeEnum.PROSPECT: "PROSPECT",
  PriceTypeEnum.STOCK: "STOCK",
  PriceTypeEnum.SALE_CONTROL: "SALE_CONTROL",
  PriceTypeEnum.BUY_CONTROL: "BUY_CONTROL",
  PriceTypeEnum.COST: "COST",
  PriceTypeEnum.OTHER: "OTHER",
};
