///
///@ClassName: sync_info_type
///@Description:
///@Author: tanglan
///@Date: 2023/12/26

enum SyncInfoType {
  /// 商品
  pType,

  /// 权限配置信息
  permissionConfig,

  /// 门店
  storeInfo,

  /// 促销信息
  promotion,

  ///积分配置
  scoreConfiguration,

  ///打印信息配置
  printConfiguration,
}

const Map<SyncInfoType, String> syncInfoTypeEnumData = {
  SyncInfoType.pType: "pType",
  SyncInfoType.permissionConfig: "permissionConfig",
  SyncInfoType.storeInfo: "storeInfo",
  SyncInfoType.promotion: "promotion",
  SyncInfoType.scoreConfiguration: "scoreConfiguration",
  SyncInfoType.printConfiguration: "printConfiguration",
};
const Map<SyncInfoType, String> syncInfoMsgData = {
  SyncInfoType.pType: "商品信息",
  SyncInfoType.permissionConfig: "职员权限和系统配置",
  SyncInfoType.storeInfo: "门店资料和设置",
  SyncInfoType.promotion: "促销信息",
  SyncInfoType.scoreConfiguration: "积分策略",
  SyncInfoType.printConfiguration: "打印配置",
};
