/// Copyright (C), 2019-2020, wsgjp.com
/// FileName:bill_fee_share_type_enum
/// Author: lidingwen
/// Date: 8/1/21
/// Description:
enum BillFeeShareTypeEnum { NO, TOTAL, WEIGHT }

const Map<int, BillFeeShareTypeEnum> BillFeeShareTypeEnumData = {
  0: BillFeeShareTypeEnum.NO,
  1: BillFeeShareTypeEnum.TOTAL,
  2: BillFeeShareTypeEnum.WEIGHT,
};
const Map<int, String> BillFeeShareTypeEnumNames = {
  0: "否",
  1: "按折后金额",
  2: "按重量",
};
const Map<int, String> BillFeeShareTypeRateEnumNames = {
  0: "否",
  1: "按折后不含税金额",
  2: "按重量",
};
