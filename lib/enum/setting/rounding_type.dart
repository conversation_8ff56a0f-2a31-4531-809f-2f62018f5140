///默认的抹零类型
const RoundingType DEFAULT_ROUNDING_TYPE = RoundingType.ROUNDING;

///配置抹零类型枚举
enum RoundingType {
  ///四舍五入
  ROUNDING,

  ///只舍不入
  FLOOR,

  ///只入不舍
  CEIL,
}

extension RoundingTypeExtension on RoundingType {

  String get name {
    switch(this) {
      case RoundingType.ROUNDING:
        return "四舍五入";
      case RoundingType.FLOOR:
        return "只舍不入";
      case RoundingType.CEIL:
        return "只入不舍";
    }
    return "";
  }
}
