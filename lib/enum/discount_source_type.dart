/// 折扣来源类型枚举
enum DiscountSourceType {
  /// 缺失
  None,
  /// 销售整单折扣
  SaleDiscount,
  /// 最近销售折扣
  LastSaleDiscount,
  /// 指定折扣
  UserDiscount,
  /// 报价单
  QuotationDiscount,
}

extension DiscountSourceTypeExtension on DiscountSourceType {
  /// 获取枚举对应的整数值
  int get value {
    switch (this) {
      case DiscountSourceType.None:
        return 0;
      case DiscountSourceType.SaleDiscount:
        return 1;
      case DiscountSourceType.LastSaleDiscount:
        return 2;
      case DiscountSourceType.UserDiscount:
        return 3;
      case DiscountSourceType.QuotationDiscount:
        return 4;
    }
  }

  /// 获取枚举对应的中文描述
  String get description {
    switch (this) {
      case DiscountSourceType.None:
        return "缺失";
      case DiscountSourceType.SaleDiscount:
        return "销售整单折扣";
      case DiscountSourceType.LastSaleDiscount:
        return "最近销售折扣";
      case DiscountSourceType.UserDiscount:
        return "指定折扣";
      case DiscountSourceType.QuotationDiscount:
        return "报价单";
    }
  }
}

/// 折扣来源类型工具类
class DiscountSourceTypeUtil {
  /// 从字符串枚举名称转换为整数值
  static int fromStringToInt(dynamic value) {
    if (value == null) return 0;
    
    // 如果已经是整数，直接返回
    if (value is int) return value;
    
    // 如果是字符串，根据枚举名称转换
    if (value is String) {
      switch (value) {
        case 'None':
          return 0;
        case 'SaleDiscount':
          return 1;
        case 'LastSaleDiscount':
          return 2;
        case 'UserDiscount':
          return 3;
        case 'QuotationDiscount':
          return 4;
        default:
          return 0; // 默认返回0
      }
    }
    
    return 0; // 其他情况默认返回0
  }

  /// 从整数值转换为枚举
  static DiscountSourceType fromInt(int value) {
    switch (value) {
      case 0:
        return DiscountSourceType.None;
      case 1:
        return DiscountSourceType.SaleDiscount;
      case 2:
        return DiscountSourceType.LastSaleDiscount;
      case 3:
        return DiscountSourceType.UserDiscount;
      case 4:
        return DiscountSourceType.QuotationDiscount;
      default:
        return DiscountSourceType.None;
    }
  }

  /// 从字符串枚举名称转换为枚举
  static DiscountSourceType fromString(String value) {
    switch (value) {
      case 'None':
        return DiscountSourceType.None;
      case 'SaleDiscount':
        return DiscountSourceType.SaleDiscount;
      case 'LastSaleDiscount':
        return DiscountSourceType.LastSaleDiscount;
      case 'UserDiscount':
        return DiscountSourceType.UserDiscount;
      case 'QuotationDiscount':
        return DiscountSourceType.QuotationDiscount;
      default:
        return DiscountSourceType.None;
    }
  }
}
