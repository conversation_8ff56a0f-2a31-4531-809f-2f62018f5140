enum BillPostState {
  UNCONFIRMED, //未确认
  CONFIRM_WAIT, //待审核
  CONFIRM_COMPLETED, //已审核
  PROCESS_COMPLETED, //发货已确认(待入库)
  DELIVERY_COMPLETED, //已发货
  STOCK_INOUT_PART, //部分出入库
  STOCK_INOUT_COMPLETED, //出入库完成
  STOCK_POST_FAILED, //"库存核算失败"
  STOCK_POST_COMPLETED, //"库存已核算"
  ACCOUNTING_COMPLETED, //"核算完成"
  ACCOUNTING_CANCELLED //"已废弃"
}

extension BillPostStateExtension on BillPostState {
  int get state {
    switch(this) {
      case BillPostState.UNCONFIRMED:
        return 0;
      case BillPostState.CONFIRM_WAIT:
        return 100;
      case BillPostState.CONFIRM_COMPLETED:
        return 300;
      case BillPostState.PROCESS_COMPLETED:
        return 500;
      case BillPostState.DELIVERY_COMPLETED:
        return 500;
      case BillPostState.STOCK_INOUT_PART:
        return 550;
      case BillPostState.STOCK_INOUT_COMPLETED:
        return 600;
      case BillPostState.STOCK_POST_FAILED:
        return 650;
      case BillPostState.STOCK_POST_COMPLETED:
        return 700;
      case BillPostState.ACCOUNTING_COMPLETED:
        return 800;
      case BillPostState.ACCOUNTING_CANCELLED:
        return -100;
    }
  }
}

const Map<BillPostState, String> BillPostStateString = {
  BillPostState.UNCONFIRMED: "UNCONFIRMED",
  BillPostState.CONFIRM_WAIT: "CONFIRM_WAIT",
  BillPostState.CONFIRM_COMPLETED: "CONFIRM_COMPLETED",
  BillPostState.PROCESS_COMPLETED: "PROCESS_COMPLETED",
  BillPostState.DELIVERY_COMPLETED: "DELIVERY_COMPLETED",
  BillPostState.STOCK_INOUT_PART: "STOCK_INOUT_PART",
  BillPostState.STOCK_INOUT_COMPLETED: "STOCK_INOUT_COMPLETED",
  BillPostState.STOCK_POST_FAILED: "STOCK_POST_FAILED",
  BillPostState.STOCK_POST_COMPLETED: "STOCK_POST_COMPLETED",
  BillPostState.ACCOUNTING_COMPLETED: "ACCOUNTING_COMPLETED",
  BillPostState.ACCOUNTING_CANCELLED: "ACCOUNTING_CANCELLED"
};

const Map<BillPostState, String> BillPostStateValue = {
  BillPostState.UNCONFIRMED: "未确认",
  BillPostState.CONFIRM_WAIT: "未审核",
  BillPostState.CONFIRM_COMPLETED: "已审核",
  BillPostState.PROCESS_COMPLETED: "发货已确认",
  BillPostState.DELIVERY_COMPLETED: "已发货",
  BillPostState.STOCK_INOUT_PART: "部分出入库",
  BillPostState.STOCK_INOUT_COMPLETED: "出入库完成",
  BillPostState.STOCK_POST_FAILED: "库存核算失败",
  BillPostState.STOCK_POST_COMPLETED: "库存已核算",
  BillPostState.ACCOUNTING_COMPLETED: "核算完成",
  BillPostState.ACCOUNTING_CANCELLED: "已废弃"
};

const Map<String, String> BillPostStateStringName = {
  "UNCONFIRMED": "未确认",
  "CONFIRM_WAIT": "未审核",
  "CONFIRM_COMPLETED": "已审核",
  "PROCESS_COMPLETED": "发货已确认",
  "DELIVERY_COMPLETED": "已发货",
  "STOCK_INOUT_PART": "部分出入库",
  "STOCK_INOUT_COMPLETED": "出入库完成",
  "STOCK_POST_FAILED": "库存核算失败",
  "STOCK_POST_COMPLETED": "库存已核算",
  "ACCOUNTING_COMPLETED": "核算完成",
  "ACCOUNTING_CANCELLED": "已废弃"
};

const Map<int, String> BillPostStateName = {
  0: "未确认",
  100: "未审核",
  300: "已审核",
  500: "发货已确认",
  550: "部分出入库",
  600: "已发货",
  650: "库存核算失败",
  700: "库存已核算",
  800: "核算完成",
  -100: "已废弃"
};