enum BillType {
  SaleBill, //"销售出库单"
  // Sale, //批发销售出库单
  SaleBackBill, //批发退货单
  SaleChangeBill, //换货单
  GoodsTrans, //内部调拨单
  TransferOrder, //调拨订单
  ChannelBill, //全渠道订单
}

enum BillBusinessType {
  SaleNormal, //普通销售
  GoodsTrans, //普通销售
}

/*
销售模式
 */
enum OrderBillModel {
  STORE_SALE, //门店
}

const Map<OrderBillModel, int> OrderBillModelData = {
  OrderBillModel.STORE_SALE: 6, //门店
};
const Map<String, int> OrderBillModelIntData = {
  "STORE_SALE": 6, //门店
};

const Map<BillType, String> BillTypeData = {
  BillType.SaleBill: "SaleBill", //销售单//TODO 后期使用这个替换
  BillType.SaleBackBill: "SaleBackBill", //销售退货 TODO 后期使用这个替换
  BillType.SaleChangeBill: "SaleChangeBill", //销售换货单
  // BillType.SaleBackBill: "SaleBack", //销售退货
  BillType.TransferOrder: "TransferOrder", //调拨订单
  BillType.GoodsTrans: "GoodsTrans", //调拨单 --直接过账
  BillType.ChannelBill: "ChannelBill", //全渠道订单
};

///单据名称
const Map<BillType, String> billTypeDataName = {
  BillType.SaleBill: "销售单", //销售单
  BillType.SaleBackBill: "销售退货", //销售退货
  BillType.SaleChangeBill: "销售换货单", //销售换货单
  // BillType.SaleBackBill: "SaleBack", //销售退货
  BillType.TransferOrder: "调拨订单", //调拨订单
  BillType.GoodsTrans: "调拨单 --直接过账", //调拨单 --直接过账
  BillType.ChannelBill: "全渠道订单", //全渠道订单
};

const Map<int, String> BillTypeCoreToString = {
  // BillType.SaleBill: "SaleBill", //销售单//TODO 后期使用这个替换
  // BillType.SaleBackBill: "SaleBackBill", //销售退货 TODO 后期使用这个替换
  2000: "SaleBill",
  2100: "SaleBackBill", //销售退货
  2200: "SaleChangeBill", //销售换货
  9005: "TransferOrder", //调拨订单
  3000: "GoodsTrans", //调拨单 --直接过账
};

const Map<String, int> billTypeCodeByStr = {
  "SaleBill": 2000,
  "SaleBackBill": 2100, //销售退货
  "SaleChangeBill": 2200, //销售换货
  "TransferOrder": 9005, //调拨订单
  "GoodsTrans": 3000, //调拨单 --直接过账
};
// const Map<String, int> BillTypeCoreToInt = {
//   // BillType.SaleBill: "SaleBill", //销售单//TODO 后期使用这个替换
//   // BillType.SaleBackBill: "SaleBackBill", //销售退货 TODO 后期使用这个替换
//   "SaleBill": 2000,
//   "SaleBackBill": 2100, //销售退货
//   "TransferOrder": 9005, //调拨订单
//   "GoodsTrans": 3000, //调拨单 --直接过账
// };

const Map<String, BillType> BillTypeString = {
  "SaleBill": BillType.SaleBill, //销售单
  "SaleBackBill": BillType.SaleBackBill, //销售换货
  "SaleChangeBill": BillType.SaleChangeBill, //销售换货
  "TransferOrder": BillType.TransferOrder, //销售换货
  "GoodsTrans": BillType.GoodsTrans, //调拨单 --直接过账
  "ChannelBill": BillType.ChannelBill, //全渠道订单
};

const Map<String, String> billTypeStrName = {
  "SaleBill": "销售单",
  "SaleBackBill": "销售退货",
  "SaleChangeBill": "销售换货单",
  "TransferOrder": "调拨订单",
  //调拨单 --直接过账(兼容处理  本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill)
  "GoodsTransInternalBill": "调拨单",
  //调拨单 --直接过账((兼容处理  本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill))
  "GoodsTrans": "调拨单",
  "ChannelBill": "全渠道订单",
};

const Map<BillType, int> BillCodeData = {
  BillType.SaleBill: 2000,
  BillType.SaleBackBill: 2100,
  BillType.SaleChangeBill: 2200,
  BillType.TransferOrder: 9005,
  BillType.GoodsTrans: 3000,
};
const Map<int, String> billCodeName = {
  2000: "销售出库单",
  2100: "销售退货单",
  2200: "销售换货单",
};

const Map<BillBusinessType, String> BillBusinessTypeString = {
  //TODO 后期调整为SaleNormal
  BillBusinessType.SaleNormal: "SaleNormal",
  BillBusinessType.GoodsTrans: "GoodsTrans",
  // BillBusinessType.SaleNormal: "WholeSaleOffline"
};
const Map<BillBusinessType, int> BillBusinessTypeData = {
  //TODO 后期调整为SaleNormal
  BillBusinessType.SaleNormal: 201,
};
const Map<String, int> BillBusinessTypeIntData = {
  //TODO 后期调整为SaleNormal
  "SaleNormal": 201,
  "GoodsTrans": 300,
  // BillBusinessType.SaleNormal: "WholeSaleOffline"
};

const Map<int, String> BillBusinessTypeIntToString = {
  //TODO 后期调整为SaleNormal
  201: "SaleNormal",
  300: "GoodsTrans",
  // BillBusinessType.SaleNormal: "WholeSaleOffline"
};

enum BillMarkEnum {
  NORMAL_BILL, //正常开单
  INTEGRAL_EXCHANGE, //积分兑换
  RECHARGE_GIFTS, //充值赠送
  RECHARGE_EXCHANGE,
  GIFT_COUPON, //礼品券
}

const Map<BillMarkEnum, String> BillMarkEnumString = {
  BillMarkEnum.NORMAL_BILL: "NORMAL_BILL",
  BillMarkEnum.INTEGRAL_EXCHANGE: "INTEGRAL_EXCHANGE",
  BillMarkEnum.RECHARGE_GIFTS: "RECHARGE_GIFTS",
  BillMarkEnum.RECHARGE_EXCHANGE: "RECHARGE_EXCHANGE",
  BillMarkEnum.GIFT_COUPON: "GIFT_COUPON",
};
const Map<String, BillMarkEnum> BillMarkEnumData = {
  "NORMAL_BILL": BillMarkEnum.NORMAL_BILL,
  "INTEGRAL_EXCHANGE": BillMarkEnum.INTEGRAL_EXCHANGE,
  "RECHARGE_GIFTS": BillMarkEnum.RECHARGE_GIFTS,
  "RECHARGE_EXCHANGE": BillMarkEnum.RECHARGE_EXCHANGE,
  "GIFT_COUPON": BillMarkEnum.GIFT_COUPON,
};
const Map<String, BillMarkEnum> BillMarkEnumInt = {
  "60000000": BillMarkEnum.NORMAL_BILL,
  "60000001": BillMarkEnum.INTEGRAL_EXCHANGE,
  "60000002": BillMarkEnum.RECHARGE_GIFTS,
  "60000003": BillMarkEnum.RECHARGE_EXCHANGE,
  "60000004": BillMarkEnum.GIFT_COUPON,
};
//
// Map<int, int> BillTypeCodeData = {
//   BillCodeData[BillType.SaleBill]: 20,
//   BillCodeData[BillType.SaleBackBill]: 21,
// };
