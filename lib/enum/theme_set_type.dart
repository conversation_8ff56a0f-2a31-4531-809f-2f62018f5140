/// Copyright (C), 2019-2020, wsgjp.com
/// FileName: theme_set_type.dart 
/// Author: lidingwen
/// Date: 2020/10/21 1:59 PM
/// Description:

enum ThemeSetType {
  FollowSystem, //跟随系统
  Light, //浅色模式
  Dark//深色模式
}

const Map<ThemeSetType, int> ThemeSetTypeIndex = {
  ThemeSetType.FollowSystem: -1,
  ThemeSetType.Light: 0,
  ThemeSetType.Dark: 1,
};
const Map<int, ThemeSetType> ThemeSetTypeValue = {
  -1:ThemeSetType.FollowSystem,
    0: ThemeSetType.Light,
  1:ThemeSetType.Dark,
};
