enum PayResultType { SUCCEEDED, FAILED, CA<PERSON><PERSON><PERSON><PERSON>, PENDING ,WAITING_PASSWORD }

const Map<PayResultType, String> PayResultTypeString = {
  PayResultType.SUCCEEDED: "SUCCEEDED",
  PayResultType.FAILED: "FAILED",
  PayResultType.CANCELLED: "CANCELLED",
  PayResultType.PENDING: "PENDING",
  PayResultType.WAITING_PASSWORD: "WAITING_PASSWORD"
};
const Map<String, PayResultType> PayResultTypeData = {
  "SUCCEEDED": PayResultType.SUCCEEDED,
  "FAILED": PayResultType.FAILED,
  "CANCELLED": PayResultType.CANCELLED,
  "PENDING": PayResultType.PENDING,
  "WAITING_PASSWORD": PayResultType.WAITING_PASSWORD
};

enum PayType { Cash, Pay }


enum PaymentQueryTypeEnum {PAY,REFUND}