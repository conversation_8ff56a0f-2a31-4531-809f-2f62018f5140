//提示音插件
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';

import '../common/tool/sp_tool.dart';
import '../entity/system/system_config_dto.dart';

class NotifyVoicePlugin {
  // static const MethodChannel _channel =
  //     const MethodChannel("halo.wsgjp.com/NotifyVoicePlugin");

  static final NotifyVoicePlugin _instance = NotifyVoicePlugin._();

  final AudioPlayer successPlayer;
  final AudioPlayer errorPlayer;

  NotifyVoicePlugin._()
      : successPlayer = AudioPlayer()
          ..setSource(AssetSource("voice/suc.mp3"))
          ..setReleaseMode(ReleaseMode.stop),
        errorPlayer = AudioPlayer()
          ..setSource(AssetSource("voice/error.mp3"))
          ..setReleaseMode(ReleaseMode.stop);

  static void success() {
    // _channel.invokeMethod("success");
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
    if (systemConfigDto.closePosScanTipSound == 1) return;
    final player = _instance.successPlayer;
    player.resume();
  }

  static void failure() {
    // _channel.invokeMethod("failure");
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
    if (systemConfigDto.closePosScanTipSound == 1) return;
    final player = _instance.errorPlayer;
    player.resume();
  }
}
