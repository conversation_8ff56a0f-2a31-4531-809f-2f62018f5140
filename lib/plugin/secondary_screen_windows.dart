import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:decimal/decimal.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:halo_pos/common/num_extension.dart';
import 'package:halo_pos/common/standard.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/screenutil/halo_screen_util.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:video_player_win/video_player_win.dart';

import '../common/change_notifier.dart';
import '../common/style/app_colors.dart';
import '../common/tool/sp_tool.dart';
import '../credits/entity/promotion_credits_dto.dart';
import '../settting/widget/checkbox.dart';
import '../vip/entity/card_coupon_card_template.dart';
import '../vip/entity/recharge_strategy.dart';
import '../vip/entity/vip_level_dto.dart';
import '../vip/level/svip_select_page.dart';
import '../vip/recharge/vip_recharge_page.dart';
import '../widgets/dotted_line.dart';
import 'secondary_screen_plugin.dart';

const int _secondaryWindowId = 1;
bool _isSecondaryWindowShow = false;

///启动副屏app
bool runSecondaryWindowApp(List<String> args) {
  if (!Platform.isWindows) return false;
  if (args.isNotEmpty && args.first == "multi_window") {
    final windowId = int.parse(args[1]);
    if (windowId != _secondaryWindowId) {
      return true;
    }
    Map<String, dynamic> arguments = {};
    if (args.length > 2) {
      final arg = args[2];
      arguments =
          arg.isEmpty ? const {} : jsonDecode(arg) as Map<String, dynamic>;
    }

    runApp(MaterialApp(
      home: _SecondaryWindowForWindows(
        windowController: WindowController.fromWindowId(windowId),
        arguments: arguments,
      ),
    ));
    return true;
  }
  return false;
}

void showSecondaryWindow() {
  if (!Platform.isWindows) return;
  WindowController.fromWindowId(_secondaryWindowId).show();
  // SecondaryScreenPlugin.showGoodsList(
  //     goods: [], total: "0", discount: "0", scoreDiscount: "0");
}

void hideSecondaryWindow() {
  if (!Platform.isWindows) return;
  WindowController.fromWindowId(_secondaryWindowId).hide();
}

///创建副屏
Future<bool> createSecondaryWindow(
    {required String videoUrl,
    required List<String> imageUrls,
    required int imageSwitchPeriod,
    List<GoodsItemBean> goods = const [],
    String total = "0",
    String? discount}) async {
  if (!Platform.isWindows) return false;
  // if (_isSecondaryWindowShow) {
  //   return false;
  // }
  if (!_isSecondaryWindowShow || !await isSecondaryWindowActive()) {
    _isSecondaryWindowShow = true;
    final secondaryWindow = await DesktopMultiWindow.createWindow(jsonEncode({
      "videoUrl": videoUrl,
      "imageUrls": imageUrls,
      "imageSwitchPeriod": imageSwitchPeriod,
      "goods": goods.map((e) => e.toJson()).toList(),
      "total": total,
      "discount": discount,
    }));
    if (secondaryWindow.windowId != _secondaryWindowId) {
      // await secondaryWindow.close();
      return true;
    }
    secondaryWindow
      ..setFrame(const Offset(0, 0) & const Size(1280, 720))
      ..center()
      ..setTitle("云零售");
    return true;
  }
  return false;
}

///副屏是否已经启动
Future<bool> isSecondaryWindowActive() async {
  if (!Platform.isWindows) return false;
  try {
    List<int> ids = await DesktopMultiWindow.getAllSubWindowIds();
    return ids.isNotEmpty;
  } catch (e) {
    return false;
  }
}

///更换图片和视频
Future<void> showImageAndVideoForWin({
  required String videoUrl,
  required List<String> imageUrls,
  required int imageSwitchPeriod,
}) async {
  if (!Platform.isWindows) return;
  if (!await createSecondaryWindow(
      videoUrl: videoUrl,
      imageUrls: imageUrls,
      imageSwitchPeriod: imageSwitchPeriod)) {
    await invokeMethodToSecondaryWindow("showImageAndVideo", {
      "videoUrl": videoUrl,
      "imageUrls": imageUrls,
      "imageSwitchPeriod": imageSwitchPeriod
    });
  }
}

///更新商品列表
Future<void> showGoodsListForWin(
    {required List<GoodsItemBean> goods,
    required String total,
    String? discount,
    VipInfo? vipInfo}) async {
  if (!Platform.isWindows) return;
  final setting = SpTool.getSetting();
  if (!await createSecondaryWindow(
      videoUrl: setting.secondaryVideoUrl ?? "",
      imageUrls: setting.secondaryScreenPictureUrls,
      imageSwitchPeriod: setting.bannerTime,
      goods: goods,
      total: total,
      discount: discount)) {
    await invokeMethodToSecondaryWindow("showGoodsList", {
      "goods": goods.map((e) => e.toJson()).toList(),
      "total": total,
      "discount": discount,
      "vipInfo": vipInfo?.toJson(),
    });
  }
}

///展示积分兑换界面
Future<void> showScoreExchangeForWin(List<ScoreExchangeForWindows> list) async {
  if (!Platform.isWindows) return;
  await invokeMethodToSecondaryWindow("showScoreExchange", {
    "list": list.map((e) => e.toJson()).toList(),
  });
}

///隐藏积分兑换界面
Future<void> hideScoreExchangeForWin() async {
  if (!Platform.isWindows) return;
  await invokeMethodToSecondaryWindow("hideScoreExchange");
}

///展示付费会员充值
Future<void> showSVipForWin(
    {VipLevelDto? currentLevel,
    VipLevelRule? currentRule,
    CardCouponCardTemplate? currentRights,
    List<VipLevelDto> levelList = const [],
    num shouldPayMoney = 0}) async {
  if (!Platform.isWindows) return;
  await invokeMethodToSecondaryWindow("showSVip", {
    "levelList": levelList.map((e) => e.toJson()).toList(),
    "currentLevel": currentLevel?.toJson(),
    "currentRule": currentRule?.toJson(),
    "currentRights": currentRights?.toJson(),
    "shouldPayMoney": shouldPayMoney,
  });
}

///隐藏付费会员充值
Future<void> hideSVipForWin() async {
  if (!Platform.isWindows) return;
  await invokeMethodToSecondaryWindow("hideSVip");
}

// ///展示充值界面
// Future<void> showRechargeForWin({
//   required String rechargeAmount,
//   required String giftAmount,
//   required String gift,
//   required List<RechargeStrategyForWindows> list,
//   int jumpTo = -1,
// }) async {
//   if (!Platform.isWindows) return;
//   await invokeMethodToSecondaryWindow("showRecharge", {
//     "rechargeAmount": rechargeAmount,
//     "giftAmount": giftAmount,
//     "gift": gift,
//     "jumpTo": jumpTo,
//     "list": list.map((e) => e.toJson()).toList(),
//   });
// }

///展示充值界面
Future<void> showRechargeForWin() async {
  if (!Platform.isWindows) return;
  await invokeMethodToSecondaryWindow("showRecharge");
}

///展示充值活动列表
Future<void> changeRechargeStrategyListForWin({
  required List<RechargeStrategyForWindows> list,
  int? jumpTo,
}) async {
  if (!Platform.isWindows) return;
  await invokeMethodToSecondaryWindow("changeRechargeStrategyList", {
    "jumpTo": jumpTo,
    "list": list.map((e) => e.toJson()).toList(),
  });
}

///展示充值信息
Future<void> changeRechargeInfoForWin({
  String? rechargeAmount,
  String? giftAmount,
  bool showSelectedGoods = false,
  String? selectedGoodsTitle,
  String? selectedGoods,
  bool showGift = false,
  String? giftTitle,
  String? gift,
  bool? isExchange,
}) async {
  if (!Platform.isWindows) return;
  await invokeMethodToSecondaryWindow("changeRechargeInfo", {
    "rechargeAmount": rechargeAmount,
    "giftAmount": giftAmount,
    "showSelectedGoods": showSelectedGoods,
    "selectedGoodsTitle": selectedGoodsTitle,
    "selectedGoods": selectedGoods,
    "showGift": showGift,
    "giftTitle": giftTitle,
    "gift": gift,
    "isExchange": isExchange,
  });
}

Future<void> hideRechargeForWin() async {
  if (!Platform.isWindows) return;
  await invokeMethodToSecondaryWindow("hideRecharge");
}

///调用副屏方法
Future<void> invokeMethodToSecondaryWindow(String method,
    [dynamic arguments]) async {
  await DesktopMultiWindow.invokeMethod(_secondaryWindowId, method, arguments);
}

///副屏商品列表和图片、视频播放
class _SecondaryWindowForWindows extends StatefulWidget {
  final WindowController windowController;
  final Map<String, dynamic> arguments;

  const _SecondaryWindowForWindows(
      {Key? key, required this.windowController, this.arguments = const {}})
      : super(key: key);

  @override
  State<_SecondaryWindowForWindows> createState() =>
      _SecondaryWindowForWindowsState();
}

Widget get _divider => Divider(height: 2.h, color: const Color(0xFFe1e1e1));

///副屏商品列表和图片、视频播放
class _SecondaryWindowForWindowsState
    extends State<_SecondaryWindowForWindows> {
  ///是否初始化完成
  bool initialized = false;

  ///商品信息
  List<GoodsItemBean> goods = [];

  ///合计金额
  String total = "0";

  ///合计优惠
  String discount = "0";

  ///合计数量
  String numCount = "0";

  ///会员信息
  VipInfo? vipInfo;

  ///商品列表滚动控制
  final ItemScrollController _scrollController = ItemScrollController();

  ///图片地址列表
  List<String>? imageUrls;

  ///图片切换时间，为0时不会切换
  int imageSwitchPeriod = 10;

  ///当前图片index
  int index = 0;

  ///当期图片地址,为空时，不展示图片
  String? currentImageUrl;

  ///图片切换的定时任务timer
  Timer? imageSwitchTimer;

  ///视频controller
  WinVideoPlayerController? videoPlayerController;

  ///视频地址，为空时不播放视频
  String? videoUrl;

  ///是否展示积分兑换
  bool showScoreExchange = false;

  ///积分兑换controller
  final InterceptValueNotifier<List<ScoreExchangeForWindows>>
      scoreExchangeController = InterceptValueNotifier([]);

  ///是否展示充值活动
  bool showRecharge = false;

  ///充值controller
  final _RechargeController rechargeController = _RechargeController();

  ///是否展示付费会员充值界面
  bool showSVip = false;

  ///付费会员controller
  final SVipSelectController sVipSelectController = SVipSelectController();

  EdgeInsets get _goodsListPadding => EdgeInsets.symmetric(horizontal: 14.w);

  @override
  void initState() {
    super.initState();
    WindowsVideoPlayer.registerWith();
    Future.delayed(const Duration(seconds: 1)).then((value) {
      var boxConstraints = BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width,
          maxHeight: MediaQuery.of(context).size.height);
      HaloScreenUtil.init(boxConstraints, designSize: const Size(750, 1334));
      ScreenUtil.init(context, designSize: const Size(1920, 1080));
      DesktopMultiWindow.setMethodHandler(onMethodCall);
      setState(() {
        initialized = true;
      });
      _showImageAndVideo(widget.arguments);
      _showGoodsList(widget.arguments);
    });
  }

  @override
  void dispose() {
    stopPlayVideo();
    stopSwitchImage();
    DesktopMultiWindow.setMethodHandler(null);
    scoreExchangeController.dispose();
    super.dispose();
  }

  Future<dynamic> onMethodCall(MethodCall call, int fromWindowId) async {
    String method = call.method;
    if (method == "showImageAndVideo") {
      _showImageAndVideo(call.arguments);
    } else if (method == "showGoodsList") {
      _showGoodsList(call.arguments);
    } else if (method == "showScoreExchange") {
      _showScoreExchange(call.arguments);
    } else if (method == "hideScoreExchange") {
      _hideScoreExchange();
    } else if (method == "showRecharge") {
      _showRecharge();
    } else if (method == "changeRechargeStrategyList") {
      _changeRechargeStrategyList(call.arguments);
    } else if (method == "changeRechargeInfo") {
      _changeRechargeInfo(call.arguments);
    } else if (method == "hideRecharge") {
      _hideRecharge();
    } else if (method == "showSVip") {
      _showSVip(call.arguments);
    } else if (method == "hideSVip") {
      _hideSVip();
    }
  }

  void _showSVip(dynamic arguments) {
    if (!showSVip) {
      setState(() => showSVip = true);
    } else {
      List<VipLevelDto> levelList = (arguments["levelList"] as List?)
              ?.map<VipLevelDto>(
                  (e) => VipLevelDto.fromJson(e.cast<String, dynamic>()))
              .toList() ??
          [];
      VipLevelDto? currentLevel = (arguments["currentLevel"] as Map?)
          ?.let((e) => VipLevelDto.fromJson(e));
      VipLevelRule? currentRule = (arguments["currentRule"] as Map?)
          ?.let((e) => VipLevelRule.fromJson(e));
      CardCouponCardTemplate? currentRights =
          (arguments["currentRights"] as Map?)
              ?.let((e) => CardCouponCardTemplate.fromJson(e.cast()));
      num shouldPayMoney = arguments["shouldPayMoney"] ?? 0;
      sVipSelectController.onSecondaryScreenRefresh(
          currentLevel: currentLevel,
          currentRule: currentRule,
          currentRights: currentRights,
          levelList: levelList,
          shouldPayMoney: shouldPayMoney);
    }
  }

  void _hideSVip() {
    setState(() => showSVip = false);
  }

  void _showRecharge() {
    if (!showScoreExchange) {
      setState(() => showRecharge = true);
    }
  }

  void _changeRechargeStrategyList(dynamic arguments) {
    int jumpTo = arguments["jumpTo"] ?? -1;
    List<RechargeStrategyForWindows> list = (arguments["list"] as List?)
            ?.map<RechargeStrategyForWindows>((e) =>
                RechargeStrategyForWindows.fromMap(e.cast<String, dynamic>()))
            .toList() ??
        [];
    rechargeController.changeRechargeStrategyList(list, jumpTo);
  }

  void _changeRechargeInfo(dynamic arguments) {
    String? rechargeAmount = arguments["rechargeAmount"];
    String? giftAmount = arguments["giftAmount"];
    bool? showSelectedGoods = arguments["showSelectedGoods"];
    String? selectedGoodsTitle = arguments["selectedGoodsTitle"];
    String? selectedGoods = arguments["selectedGoods"];
    bool? showGift = arguments["showGift"];
    String? giftTitle = arguments["giftTitle"];
    String? gift = arguments["gift"];
    bool? isExchange = arguments["isExchange"];
    rechargeController.changeRechargeInfo(
      rechargeAmount: rechargeAmount,
      giftAmount: giftAmount,
      showSelectedGoods: showSelectedGoods,
      selectedGoodsTitle: selectedGoodsTitle,
      selectedGoods: selectedGoods,
      showGift: showGift,
      giftTitle: giftTitle,
      gift: gift,
      isExchange: isExchange,
    );
  }

  void _hideRecharge() {
    setState(() => showRecharge = false);
  }

  void _showScoreExchange(dynamic arguments) {
    List? list = arguments["list"];
    final data = list
            ?.map<ScoreExchangeForWindows>((e) =>
                ScoreExchangeForWindows.fromMap(e.cast<String, dynamic>()))
            .toList() ??
        [];
    if (!showScoreExchange) {
      setState(() {
        showScoreExchange = true;
        scoreExchangeController.valueWithoutNotify = data;
      });
    } else {
      scoreExchangeController.value = data;
    }
  }

  void _hideScoreExchange() {
    setState(() {
      showScoreExchange = false;
      scoreExchangeController.valueWithoutNotify = [];
    });
  }

  void _showImageAndVideo(dynamic arguments) {
    videoUrl = arguments["videoUrl"];
    imageUrls = (arguments["imageUrls"] as List?)?.cast<String>();
    imageSwitchPeriod = arguments["imageSwitchPeriod"] ?? 10;
    stopSwitchImage();
    stopPlayVideo();
    if (!startSwitchImage()) {
      startPlayVideo();
    }
  }

  void _showGoodsList(dynamic arguments) {
    var goodsMapList = arguments["goods"];
    goods = goodsMapList?.map<GoodsItemBean>((e) {
          return GoodsItemBean.fromJson(e.cast<String, dynamic>());
        }).toList() ??
        [];
    total = arguments["total"] ?? "0";
    discount = arguments["discount"] ?? "0";
    vipInfo = (arguments["vipInfo"] as Map?)
        ?.let((value) => VipInfo.fromMap(value.cast<String, dynamic>()));
    if (goods.isNotEmpty) {
      //使用reduce时，若列表为空，则会抛异常
      numCount = goods
          .map((e) => Decimal.tryParse(e.count))
          .where((element) => element != null)
          .cast<Decimal>()
          .reduce((value, element) => value + element)
          .toDouble()
          .getIntWhenInteger
          .toString();
    } else {
      numCount = "0";
    }
    setState(() {});
    if (goods.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _scrollController.jumpTo(index: goods.length - 1);
      });
    }
  }

  Future<bool> startPlayVideo() async {
    stopPlayVideo();
    if (videoUrl?.isNotEmpty == true) {
      videoPlayerController = WinVideoPlayerController.file(File(videoUrl!));
      videoPlayerController?.addListener(() {
        if (videoPlayerController?.value.isCompleted == true) {
          // videoPlayerController.play();
        } else if (videoPlayerController?.value.hasError == true) {
          videoUrl = null;
        } else {
          return;
        }
        if (!startSwitchImage()) {
          startPlayVideo();
        }
      });
      await videoPlayerController?.initialize();
      setState(() {});
      if (videoPlayerController?.value.isInitialized == true) {
        setState(() {});
        //开始
        videoPlayerController!.play();
      } else {
        return false;
      }
      return true;
    }
    return false;
  }

  void stopPlayVideo() {
    videoPlayerController?.dispose();
    videoPlayerController = null;
    setState(() {});
  }

  bool startSwitchImage() {
    stopSwitchImage();
    index = 0;
    if (imageUrls?.isNotEmpty == true) {
      setState(() => currentImageUrl = imageUrls!.first);
      if (imageSwitchPeriod > 0) {
        imageSwitchTimer =
            Timer.periodic(Duration(seconds: imageSwitchPeriod), (timer) async {
          if (imageUrls?.isNotEmpty == true) {
            if (++index < imageUrls!.length) {
              setState(() => currentImageUrl = imageUrls![index]);
              return;
            }
          }
          stopSwitchImage();
          if (!await startPlayVideo()) {
            startSwitchImage();
          }
        });
      }
      return true;
    } else {
      return false;
    }
  }

  void stopSwitchImage() {
    index = 0;
    currentImageUrl = null;
    imageSwitchTimer?.cancel();
    setState(() {});
  }

  Widget buildItem(BuildContext context, int index) {
    final item = goods[index];
    final style = TextStyle(
        fontSize: 20.sp,
        color: const Color(0xFF333333),
        overflow: TextOverflow.ellipsis);
    Widget name = Text(item.name,
        maxLines: 1, style: style.copyWith(fontWeight: FontWeight.bold));
    if (item.isCombo || item.isDiscount || item.isPromotion) {
      name = Row(
        children: [
          if (item.isDiscount)
            buildMark(content: "折", color: const Color(0xFFFF8A07)),
          if (item.isPromotion)
            buildMark(content: "促", color: const Color(0xFFFF0000)),
          if (item.isCombo)
            buildMark(content: "套", color: const Color(0xFFCA12BC)),
          Expanded(child: name),
        ],
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 14.h),
        name,
        SizedBox(height: 10.h),
        Row(
          children: [
            Expanded(
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                        text: "￥${item.currentPrice}",
                        style: style.copyWith(
                            color: const Color(0xFF666666), fontSize: 22.sp)),
                    if (item.price.isNotEmpty == true)
                      TextSpan(
                          text: "/${item.price}",
                          style: TextStyle(
                              fontSize: 20.sp,
                              color: Colors.grey,
                              decoration: TextDecoration.lineThrough))
                  ],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: Text(
                  "x${item.count}${item.unit}",
                  textAlign: TextAlign.end,
                  maxLines: 1,
                  style: style.copyWith(color: const Color(0xFF666666)),
                ),
              ),
            ),
            Expanded(
              child: Text(
                "￥${item.total}",
                textAlign: TextAlign.end,
                maxLines: 1,
                style: style.copyWith(fontWeight: FontWeight.bold),
              ),
            )
          ],
        ),
        SizedBox(height: 14.h),
        const DottedLine(
          dashPattern: [4, 4],
          color: Color(0xFFD0D0D0),
        ),
      ],
    );
  }

  ///套餐标记
  Widget buildMark({required String content, required Color color}) {
    return Container(
      width: 28.w,
      height: 28.w,
      margin: EdgeInsets.only(right: 4.w),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          color: color,
          borderRadius: const BorderRadius.all(Radius.circular(3))),
      child: Text(content,
          style: TextStyle(
              color: Colors.white,
              decoration: TextDecoration.none,
              fontSize: 16.sp)),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!initialized) {
      return const Scaffold();
    }
    return Scaffold(
      body: Stack(
        children: [
          _buildGoodsList(),
          if (showScoreExchange) _ScoreExchangeWidget(scoreExchangeController),
          if (showRecharge) _RechargeWidget(rechargeController),
          if (showSVip)
            SVipLevelSelectSubPage(
                controller: sVipSelectController, isSecondaryScreen: true),
        ],
      ),
    );
  }

  ///商品列表和图片、视频
  Widget _buildGoodsList() {
    return Row(
      children: [
        Expanded(
            flex: 2,
            child: Stack(
              children: [
                if (videoPlayerController != null)
                  WinVideoPlayer(videoPlayerController!),
                if (currentImageUrl?.isNotEmpty == true)
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.black,
                    child: Image.file(File(currentImageUrl!)),
                  )
              ],
            )),
        Expanded(
            flex: 1,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    height: 70.h,
                    alignment: Alignment.center,
                    child: Text("购物清单",
                        style: TextStyle(
                            color: const Color(0xFF333333),
                            fontSize: 30.sp,
                            fontWeight: FontWeight.bold)),
                  ),
                  _divider,
                  Expanded(
                    child: ScrollablePositionedList.builder(
                        padding: _goodsListPadding,
                        itemScrollController: _scrollController,
                        initialScrollIndex:
                            goods.isNotEmpty ? goods.length - 1 : 0,
                        itemCount: goods.length,
                        itemBuilder: buildItem),
                  ),
                  if (vipInfo != null)
                    Padding(
                      padding:
                          _goodsListPadding.copyWith(top: 10.h, bottom: 10.h),
                      child: _buildVipInfo(),
                    ),
                  _divider,
                  _buildSettlementInfo(),
                ],
              ),
            )),
      ],
    );
  }

  Widget _buildSettlementInfo() {
    return Container(
        constraints: BoxConstraints(minHeight: 80.h),
        padding: EdgeInsets.symmetric(
            horizontal: _goodsListPadding.left, vertical: 14.h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (discount.isNotEmpty && discount != "0")
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                        text: "优惠:",
                        style: TextStyle(
                            color: const Color(0xFF333333),
                            fontSize: 24.sp,
                            fontWeight: FontWeight.w600)),
                    TextSpan(
                        text: "￥$discount",
                        style: TextStyle(
                            color: Colors.red,
                            fontSize: 28.sp,
                            fontWeight: FontWeight.w700))
                  ],
                ),
                maxLines: 1,
              ),
            Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                      text: "共 $numCount 件,应付:",
                      style: TextStyle(
                          color: const Color(0xFF333333),
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w600)),
                  TextSpan(
                      text: "￥$total",
                      style: TextStyle(
                          color: const Color(0xFF333333),
                          fontSize: 34.sp,
                          fontWeight: FontWeight.bold))
                ],
              ),
              maxLines: 1,
            ),
          ],
        ));
  }

  ///会员信息
  Widget _buildVipInfo() {
    if (vipInfo == null) return Container();
    final vipNameStyle = TextStyle(
        color: const Color(0xFF333333),
        fontSize: 20.sp,
        fontWeight: FontWeight.bold,
        overflow: TextOverflow.ellipsis);
    final storeStyle = TextStyle(
        color: const Color(0xFF666666),
        fontSize: 18.sp,
        overflow: TextOverflow.ellipsis);
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(colors: [
          Color(0xFFFFF5E2),
          Color(0xFFFEE7BC),
        ]),
        borderRadius: BorderRadius.circular(10.w),
      ),
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 14.h),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Row(children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 300.w),
                    child: Text(
                        StringUtil.isNotEmpty(vipInfo!.name)
                            ? vipInfo!.name!
                            : "-",
                        style: vipNameStyle,
                        maxLines: 1),
                  ),
                  SizedBox(width: 10.w),
                  Flexible(
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFE9B6),
                        borderRadius: BorderRadius.circular(4.w),
                      ),
                      padding:
                          EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      child: Text(
                        vipInfo!.level ?? "",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                            color: const Color(0xFF773E0B), fontSize: 16.sp),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                ]),
              ),
              Text(
                (vipInfo!.phone ?? "").let((phone) =>
                    phone.length <= 11 ? phone : phone.substring(0, 11)),
                style: vipNameStyle.copyWith(fontStyle: FontStyle.italic),
                maxLines: 1,
              ),
            ],
          ),
          SizedBox(height: 10.h),
          Row(
            children: [
              Expanded(
                child: Text("储值余额:${vipInfo!.store ?? ""}",
                    style: storeStyle, maxLines: 1),
              ),
              Expanded(
                child: Text("可用积分:${vipInfo!.score ?? ""}",
                    style: storeStyle, maxLines: 1),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

///副屏积分兑换实体类
class ScoreExchangeForWindows {
  final String name;

  final String score;

  final int count;

  final String picUrl;

  final int? ptypeGroup;

  final int? valueType;

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'score': score,
      'count': count,
      'picUrl': picUrl,
      'ptypeGroup': ptypeGroup,
      'valueType': valueType,
    };
  }

  const ScoreExchangeForWindows({
    required String? name,
    required String? score,
    required int? count,
    required String? picUrl,
    required this.ptypeGroup,
    required this.valueType,
  })  : name = name ?? "",
        score = score ?? "0",
        count = count ?? 0,
        picUrl = picUrl ?? "";

  ScoreExchangeForWindows.fromMap(Map<String, dynamic> map)
      : this(
          name: map['name'],
          score: map['score'],
          count: map['count'],
          picUrl: map['picUrl'],
          ptypeGroup: map['ptypeGroup'],
          valueType: map['valueType'],
        );
}

///积分兑换
class _ScoreExchangeWidget extends StatefulWidget {
  final ValueNotifier<List<ScoreExchangeForWindows>> controller;

  const _ScoreExchangeWidget(this.controller, {Key? key}) : super(key: key);

  @override
  State<_ScoreExchangeWidget> createState() => _ScoreExchangeWidgetState();
}

///积分兑换
class _ScoreExchangeWidgetState extends State<_ScoreExchangeWidget> {
  @override
  void initState() {
    super.initState();
    widget.controller.addListener(onChange);
  }

  void onChange() {
    if (!mounted) return;
    setState(() {});
  }

  @override
  void dispose() {
    widget.controller.removeListener(onChange);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.dividerColor,
      child: GridView.builder(
          itemCount: widget.controller.value.length,
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 20.w,
              mainAxisSpacing: 20.h,
              mainAxisExtent: 400.h),
          itemBuilder: (BuildContext context, int index) {
            final item = widget.controller.value[index];
            final divider = SizedBox(height: 5.h);
            const style = TextStyle(overflow: TextOverflow.ellipsis);
            return Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.w),
                  color: Colors.white),
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: SizedBox(
                      width: double.infinity,
                      child: PromotionCreditsDTO.getPic(
                          ptypeGroup: item.ptypeGroup,
                          picUrl: item.picUrl,
                          valueType: item.valueType),
                    ),
                  ),
                  divider,
                  Text(
                    item.name,
                    maxLines: 1,
                    style: style.copyWith(
                        color: const Color(0xFF333333), fontSize: 26.sp),
                  ),
                  divider,
                  Text(
                    "${item.score}积分",
                    maxLines: 1,
                    style: style.copyWith(
                        color: const Color(0xFFFF4141), fontSize: 30.sp),
                  ),
                  divider,
                  Text(
                    "已兑换${item.count}次",
                    maxLines: 1,
                    style: style.copyWith(
                        color: const Color(0xFF999999), fontSize: 24.sp),
                  )
                ],
              ),
            );
          }),
    );
  }
}

///充值界面控制器
class _RechargeController {
  _RechargeWidgetState? _state;

  late List<RechargeStrategyForWindows> _list;

  late String _rechargeAmount;

  late String _giftAmount;

  late bool _showSelectedGoods;

  late String _selectedGoodsTitle;

  late String _selectedGoods;

  late bool _showGift;

  late String _giftTitle;

  late String _gift;

  late bool _isExchange;

  _RechargeController() {
    _resetData();
  }

  void _resetData() {
    _list = [];
    _rechargeAmount = "0";
    _giftAmount = "0";
    _showSelectedGoods = false;
    _selectedGoodsTitle = "";
    _selectedGoods = "";
    _showGift = false;
    _giftTitle = "";
    _gift = "";
    _isExchange = false;
  }

  void setState(VoidCallback fn) {
    if (_state != null && _state!.mounted) {
      _state?._setState(fn);
    }
  }

  void dispose() {
    _state = null;
    _resetData();
  }

  void changeRechargeStrategyList(
      List<RechargeStrategyForWindows> list, int jumpTo) {
    setState(() {
      _list = list;
    });
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (jumpTo >= 0 && jumpTo < _list.length && _state?.mounted == true) {
        _state?._jumpTo(jumpTo);
      }
    });
  }

  void changeRechargeInfo({
    String? rechargeAmount,
    String? giftAmount,
    bool? showSelectedGoods,
    String? selectedGoodsTitle,
    String? selectedGoods,
    bool? showGift,
    String? giftTitle,
    String? gift,
    bool? isExchange,
  }) {
    setState(() {
      _rechargeAmount = rechargeAmount ?? _rechargeAmount;
      _giftAmount = giftAmount ?? _giftAmount;
      _showSelectedGoods = showSelectedGoods ?? _showSelectedGoods;
      _selectedGoodsTitle = selectedGoodsTitle ?? _selectedGoodsTitle;
      _selectedGoods = selectedGoods ?? _selectedGoods;
      _showGift = showGift ?? _showGift;
      _giftTitle = giftTitle ?? _giftTitle;
      _gift = gift ?? _gift;
      _isExchange = isExchange ?? _isExchange;
    });
  }
}

///充值
class _RechargeWidget extends StatefulWidget {
  final _RechargeController controller;

  const _RechargeWidget(this.controller, {Key? key}) : super(key: key);

  @override
  State<_RechargeWidget> createState() => _RechargeWidgetState();
}

///充值
class _RechargeWidgetState extends State<_RechargeWidget> {
  ///滚动控制
  final ItemScrollController _scrollController = ItemScrollController();

  TextStyle get _titleStyle => TextStyle(
      color: const Color(0xFF333333),
      fontSize: 32.sp,
      fontWeight: FontWeight.bold);

  TextStyle get _textStyle =>
      TextStyle(color: const Color(0xFF333333), fontSize: 30.sp);

  @override
  void initState() {
    super.initState();
    widget.controller._state = this;
  }

  @override
  void didUpdateWidget(covariant _RechargeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.controller != widget.controller) {
      oldWidget.controller.dispose();
    }
    widget.controller._state = this;
  }

  @override
  void dispose() {
    widget.controller.dispose();
    super.dispose();
  }

  void _setState(VoidCallback fn) {
    setState(fn);
  }

  void _jumpTo(int jumpTo) {
    _scrollController.jumpTo(index: jumpTo);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 64.w, vertical: 20.h),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: buildStrategyList<RechargeStrategyForWindows>(
              scrollController: _scrollController,
              dataGetter: () => widget.controller._list,
              isSelected: (item) => item.selected,
              gearGetter: (item) => item.gear,
            ),
          ),
          SizedBox(height: 20.h),
          Row(
            children: [
              _buildNumberText(
                  title: "充值金额", content: widget.controller._rechargeAmount),
              SizedBox(width: inputSpacingWidth),
              _buildNumberText(
                  title: "赠送金额", content: widget.controller._giftAmount),
            ],
          ),
          if (widget.controller._showSelectedGoods) ...[
            SizedBox(height: 20.h),
            buildSelectGoodsWidget(
                title: widget.controller._selectedGoodsTitle,
                goodsNames: widget.controller._selectedGoods,
                isExchange: widget.controller._isExchange),
          ],
          if (widget.controller._showGift) ...[
            SizedBox(height: 20.h),
            buildChangeGoodsWidget(
                title: widget.controller._giftTitle,
                content: widget.controller._gift),
          ]
        ],
      ),
    );
  }

  Widget _buildNumberText({required String title, required String content}) {
    final TextStyle style = rechargeTextStyle.copyWith(fontSize: 28.sp);
    return buildContentWithTitle(
      title: title,
      expandedContent: false,
      content: Container(
        width: inputTextFieldWidth,
        height: 70.h,
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 22.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.w),
          border: Border.all(color: const Color(0xFFC5C5C5), width: 1.w),
        ),
        child: Text("￥$content", maxLines: 1, style: style),
      ),
    );
  }
}

///副屏展示充值活动实体类
class RechargeStrategyForWindows {
  ///是否选中
  final bool selected;

  final Gear? gear;

  const RechargeStrategyForWindows({
    bool? selected,
    this.gear,
  }) : selected = selected ?? false;

  Map<String, dynamic> toJson() {
    return {
      'selected': selected,
      'gear': gear?.toJson(),
    };
  }

  RechargeStrategyForWindows.fromMap(Map<String, dynamic> map)
      : this(
          selected: map['selected'],
          gear: (map['gear'] as Map?)?.let((value) => Gear.fromJson(value)),
        );
}
