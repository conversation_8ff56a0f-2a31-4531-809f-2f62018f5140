import 'dart:io';

import 'package:flutter/services.dart';
import 'package:halo_pos/hotkey/hotkey.dart';
import 'package:halo_utils/utils/String_util.dart';

import '../hotkey/hotkey_manager.dart';

final class HotkeyPlugin {
  static const MethodChannel _channel = MethodChannel(
    'halo.wsgjp.com/hotkey_plugin',
  );

  static final HotkeyPlugin instance = HotkeyPlugin._();

  HotkeyPlugin._() {
    _channel.setMethodCallHandler(_onMethodCall);
  }

  static Future<void> _onMethodCall(MethodCall call) async {
    if (call.method == "onHotkeyClick") {
      String? keyType = call.arguments;
      if (StringUtil.isEmpty(keyType)) return;
      for (var type in HotkeyType.values) {
        if (type.name == keyType) {
          HotkeyManager.onKeyHandlerByHotKeyType(type);
          return;
        }
      }
    }
  }

  void registerHotkey({
    required String keyLabel,
    required String modifier,
    required HotkeyType hotkeyType,
  }) async {
    if (!Platform.isAndroid) return;
    await _channel.invokeMethod('registerHotkey', {
      "keyLabel": keyLabel,
      "modifier": modifier,
      "hotkeyType": hotkeyType.name,
    });
  }

  void unregisterAllHotkey() {
    if (!Platform.isAndroid) return;
    _channel.invokeMethod('unregisterAllHotkey');
  }
}
