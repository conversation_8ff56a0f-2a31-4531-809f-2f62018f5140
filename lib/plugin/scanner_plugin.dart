import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ScannerPlugin {
  static const MethodChannel _channel =
      MethodChannel("halo.wsgjp.com/ScannerPlugin");

  ///监听扫码的结果
  ///需要注意在[State.dispose]方法中，将其设置为[null],避免内存泄漏
  static ValueChanged<String>? callback;

  ScannerPlugin._();

  static void init() {
    _channel.setMethodCallHandler((MethodCall call) async {
      if (call.method == "onScanResult") {
        String code = call.arguments;
        callback?.call(code);
      }
      return;
    });
  }
}
