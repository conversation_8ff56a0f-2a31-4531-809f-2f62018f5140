import 'dart:async';
import 'dart:io';
import 'dart:isolate';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:haloui/haloui.dart';
import 'package:quick_usb/quick_usb.dart';


class ThirdPartyScalePlugin {
  static const MethodChannel _channel =
      MethodChannel("halo.wsgjp.com/thirdPartyScalePlugin");

  static final ThirdPartyScalePlugin instance = ThirdPartyScalePlugin._();

  ///接受的重量
  int weight = 0;

  ///接收稳定重量的回调
  ValueChanged<int>? onReceiveStableWeight;

  StreamSubscription? _subscription;

  ThirdPartyScalePlugin._() {
    _channel.setMethodCallHandler(_onMethodCall);
  }

  Future<void> _onMethodCall(MethodCall call) async {
    if (call.method == "receiveStableWeight") {
      int? weight = call.arguments;
      if (weight != null) {
        debugPrint(weight.toString());
        this.weight = weight;
        onReceiveStableWeight?.call(weight);
      }
    }
  }

  ///获取设备
  Future<List<SimpleUsbDevice>> getDevices() async {
    if (Platform.isWindows) {
      return ScaleWindows.getDevices();
    }
    List devices = (await _channel.invokeMethod<List?>("getDevices")) ?? [];
    return devices
        .map((e) => SimpleUsbDevice.fromMap(e.cast<String, dynamic>()))
        .toList(growable: false);
  }

  ///连接设备
  Future<void> connectDevice(
      BuildContext context, SimpleUsbDevice device) async {
    if (Platform.isWindows) {
      _subscription?.cancel();
      _subscription =
          (await ScaleWindows.connectDevice(context, device))?.listen((weight) {
        if (weight is int) {
          debugPrint(weight.toString());
          this.weight = weight;
          onReceiveStableWeight?.call(weight);
        }
      });
    } else {
      await _channel.invokeMethod("connectDevice", {
        "vendorId": device.vendorId,
        "productId": device.productId,
        "serialNumber": device.serialNumber,
        "productName": device.productName,
      });
    }
  }

  ///断开设备
  Future<void> disconnectAllDevice() async {
    if (Platform.isWindows) {
      ScaleWindows.disconnectAllDevice();
    } else {
      await _channel.invokeMethod("disconnectAllDevice");
    }
  }
}

///usb设备实体类
class SimpleUsbDevice {
  ///设备提供商id
  final int vendorId;

  ///产品类型id
  final int productId;

  ///序列号
  final String serialNumber;

  ///产品名称
  final String productName;

  const SimpleUsbDevice(
      this.vendorId, this.productId, this.serialNumber, this.productName);

  Map<String, dynamic> toJson() {
    return {
      'vendorId': vendorId,
      'productId': productId,
      'serialNumber': serialNumber,
      'productName': productName,
    };
  }

  SimpleUsbDevice.fromMap(Map<String, dynamic> map)
      : this(
          map['vendorId'] ?? 0,
          map['productId'] ?? 0,
          map['serialNumber'] ?? "",
          map['productName'] ?? "",
        );
}

class ScaleWindows {
  static List<UsbDeviceDescription> _devices = [];

  static SendPort? _isolateSendPort;

  static Future<void> disconnectAllDevice() async {
    if (!Platform.isWindows) {
      return;
    }
    await QuickUsb.closeDevice();
    _isolateSendPort?.send("close");
    _isolateSendPort = null;
    return Future.delayed(const Duration(milliseconds: 500));
  }

  static Future<List<SimpleUsbDevice>> getDevices() async {
    if (!Platform.isWindows) return [];
    await QuickUsb.init();
    await disconnectAllDevice();
    _devices = await QuickUsb.getDevicesWithDescription();
    return _devices
        .map((e) => SimpleUsbDevice(
            e.device.vendorId,
            e.device.productId,
            e.serialNumber ?? "",
            e.product ?? e.serialNumber ?? e.device.identifier))
        .toList();
  }

  static Future<Stream?> connectDevice(
      BuildContext context, SimpleUsbDevice simpleUsb) async {
    if (!Platform.isWindows) {
      return null;
    }
    await getDevices();
    UsbDeviceDescription? device = _devices
        .cast<UsbDeviceDescription?>()
        .firstWhere(
            (element) =>
                (element!.serialNumber ?? "") == simpleUsb.serialNumber &&
                element.device.vendorId == simpleUsb.vendorId &&
                element.device.productId == simpleUsb.productId,
            orElse: () => null);
    Stream? stream;
    if (device == null) {
      HaloToast.show(context, msg: "未找到该设备,请检查设备是否连接");
    } else {
      ReceivePort receivePort = ReceivePort();
      final broadcast = receivePort.asBroadcastStream();
      await Isolate.spawn(
        _connectDevice,
        {
          "device": device,
          "port": receivePort.sendPort,
        },
      );
      _isolateSendPort = await broadcast.first;
      return broadcast;
    }
    return stream;
  }

  @pragma('vm:entry-point')
  static void _connectDevice(Map map) async {
    if (!Platform.isWindows) {
      return;
    }
    UsbDeviceDescription device = map["device"];
    SendPort sendPort = map["port"];
    Stream stream = Stream.periodic(const Duration(milliseconds: 100));
    StreamSubscription? streamSubscription;
    ReceivePort receivePort = ReceivePort();
    receivePort.listen((message) {
      scheduleMicrotask(() async {
        streamSubscription?.cancel();
        await QuickUsb.closeDevice();
        Isolate.current.kill();
      });
    });
    sendPort.send(receivePort.sendPort);
    QuickUsbWindows.registerWith();
    await QuickUsb.init();
    await QuickUsb.openDevice(device.device);
    var configuration = await QuickUsb.getConfiguration(0);
    var interface = configuration.interfaces.first;
    await QuickUsb.claimInterface(interface);
    // await QuickUsb.releaseInterface(interface);
    List<int> buffer = [];
    UsbEndpoint? endpoint;
    streamSubscription = stream.listen((event) async {
      if (endpoint == null) {
        for (var e in interface.endpoints) {
          try {
            for (int i = 0; i < 2; i++) {
              var testData =
                  await QuickUsb.bulkTransferIn(e, 1024, timeout: 1000);
              buffer.addAll(testData);
            }
            endpoint = e;
            break;
          } catch (_) {
            debugPrint(
                "当前endPoint(number=${e.endpointNumber},direction=${e.direction})读取usb数据失败");
          }
        }
      }
      if (endpoint == null) return;
      var bulkTransferOut =
          await QuickUsb.bulkTransferIn(endpoint!, 1024, timeout: 0);
      buffer.addAll(bulkTransferOut);
      var start = -1;
      int dIndex = -1;
      for (int index = buffer.length - 1; index >= 0; index--) {
        int byte = buffer[index];
        if (byte == 0x0d) {
          dIndex = index;
        } else if (byte == 0x0a) {
          if (dIndex > 0 && index + 1 == dIndex) {
            if (start < 0) {
              start = dIndex + 1;
            } else {
              final data =
                  buffer.sublist(dIndex + 1, start - 2).toList(growable: false);
              buffer = buffer.sublist(start).toList();
              sendPort
                  .send(int.tryParse(String.fromCharCodes(data).trim()) ?? 0);
              break;
            }
          }
        }
      }
    });
  }
}
