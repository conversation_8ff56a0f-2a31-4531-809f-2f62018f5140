import 'dart:io';

import 'package:flutter/services.dart';
import 'package:halo_pos/bill/tool/bill_goods_util.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../settting/entity/setting_dto.dart';

import '../bill/tool/bill_tool.dart';
import 'secondary_screen_windows.dart';

class SecondaryScreenPlugin {
  static const MethodChannel _channel =
      MethodChannel('halo.wsgjp.com/secondaryScreen');

  ///展示副屏图片
  static void showSecondaryScreenImage({bool firstShow = false}) {
    SettingDto setting = SpTool.getSetting();
    if (Platform.isWindows) {
      if (firstShow) {
        showImageAndVideoForWin(
            videoUrl: setting.secondaryVideoUrl ?? "",
            imageUrls: setting.secondaryScreenPictureUrls,
            imageSwitchPeriod: setting.bannerTime);
      }
    }
    SecondaryScreenPlugin.showImageAndVideo(
        videoUrl: setting.secondaryVideoUrl ?? "",
        imageUrls: setting.secondaryScreenPictureUrls,
        imageSwitchPeriod: setting.bannerTime);
  }

  ///隐藏副屏，这样子副屏会展示和主屏一样的内容
  static Future<void> hide() {
    if (Platform.isWindows) return Future.value();
    return _channel.invokeMethod("hide");
  }

  ///展示图片和视频
  /// 采用图片和视频轮播的方式
  /// [videoUrl] 视频播放链接，当为null，不播放视频
  /// [imageUrls] 图片地址列表，当为空时，不轮播图片
  /// [imageSwitchPeriod] 图片切换的间隔，当为0时，只显示第一张图片，不轮播
  static Future<void> showImageAndVideo({
    required String videoUrl,
    required List<String> imageUrls,
    required int imageSwitchPeriod,
  }) async {
    if (Platform.isWindows) {
      return showImageAndVideoForWin(
          videoUrl: videoUrl,
          imageUrls: imageUrls,
          imageSwitchPeriod: imageSwitchPeriod);
    } else {
      return _channel.invokeMethod("showImageAndVideo", {
        "videoUrl": videoUrl,
        "imageUrls": imageUrls,
        "imageSwitchPeriod": imageSwitchPeriod
      });
    }
  }

  ///展示购物清单
  ///[goods] 商品列表
  ///[total] 合计价格
  ///[discount] 优惠金额
  static Future<void> showGoodsList(
      {required List<GoodsItemBean> goods,
      required String total,
      String? discount,
      VipInfo? vipInfo}) async {
    if ((num.tryParse(discount ?? "") ?? 0) == 0) {
      discount = null;
    }
    if (Platform.isWindows) {
      await showGoodsListForWin(
          goods: goods, total: total, discount: discount, vipInfo: vipInfo);
    } else {
      _channel.invokeMethod("showGoodsList", {
        "goods": goods.map((e) => e.toJson()).toList(),
        "total": total,
        "vipInfo": vipInfo?.toJson(),
        "discount": discount
      });
    }
  }

  ///展示全屏遮罩
  static void showCover() {
    if (Platform.isWindows) return;
    _channel.invokeMethod("showCover");
  }

  ///展示购物清单商品转换
  static List<GoodsItemBean> goodsDetailListToGoodsItemList({
    required List<GoodsDetailDto> goodsDetails,
  }) {
    int index = 1;
    return goodsDetails
        .where((element) => !BillTool.comboDetailRow(element))
        .map((e) {
      return GoodsItemBean(
        index: index++,
        name: "${e.pFullName ?? ""} ${e.skuName ?? ""}",
        unit: e.unitName ?? "",
        price: e.currencyPrice.toString(),
        currentPrice: e.currencyDisedTaxedPrice.toString(),
        count: DecimalDisplayHelper.getQtyFixed(e.unitQty.toString()),
        discount: e.currencyPreferentialTotal.toString(),
        total: e.currencyDisedTaxedTotal.toString(),
        isCombo: e.comboRow,
        isDiscount: BillGoodsUtil.isDiscountGoods(e, markGift: false),
        isPromotion: BillGoodsUtil.isPromotionGoods(e),
      );
    }).toList();
  }
}

class VipInfo {
  final String? name;
  final String? level;
  final String? phone;
  final String? store;
  final String? score;

  const VipInfo({
    required this.name,
    required this.level,
    required this.phone,
    required this.store,
    required this.score,
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'level': level,
        'phone': phone,
        'store': store,
        'score': score,
      };

  VipInfo.fromMap(Map<String, dynamic> map)
      : this(
          name: map['name'],
          level: map['level'],
          phone: map['phone'],
          store: map['store'],
          score: map['score'],
        );
}

///副屏展示的商品
class GoodsItemBean {
  ///序号
  final int index;

  ///商品名
  final String name;

  ///单位
  final String unit;

  ///单价
  final String price;

  ///当前价格
  final String currentPrice;

  ///数量
  final String count;

  ///折扣 todo 5.3删除了此字段，为了兼容，需要全面发5.3之后再做删除
  final String discount;

  ///总计
  final String total;

  ///是否是套餐行
  final bool isCombo;

  ///是否是折扣商品
  final bool isDiscount;

  ///是否是促销商品
  final bool isPromotion;

  GoodsItemBean({
    required this.index,
    required this.name,
    required this.unit,
    required this.price,
    required this.currentPrice,
    required this.count,
    required this.discount,
    required this.total,
    required this.isCombo,
    required this.isDiscount,
    required this.isPromotion,
  });

  GoodsItemBean.fromJson(Map<String, dynamic> json)
      : this(
          index: json["index"] ?? 0,
          name: json["name"] ?? "",
          unit: json["unit"] ?? "",
          price: json["price"] ?? "0",
          currentPrice: json["currentPrice"] ?? "0",
          count: json["count"] ?? "0",
          discount: json["discount"] ?? "0",
          total: json["total"] ?? "0",
          isCombo: json["isCombo"] ?? false,
          isDiscount: json["isDiscount"] ?? false,
          isPromotion: json["isPromotion"] ?? false,
        );

  Map<String, dynamic> toJson() => {
        "index": index,
        "name": name,
        "unit": unit,
        "price": price,
        "currentPrice": currentPrice,
        "count": count,
        "discount": discount,
        "total": total,
        "isCombo": isCombo,
        "isDiscount": isDiscount,
        "isPromotion": isPromotion,
      };
}
