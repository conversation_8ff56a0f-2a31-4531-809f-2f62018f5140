import 'package:flutter/services.dart';

///商米电子秤插件,参考[https://developer.sunmi.com/docs/zh-CN/xeghjk491/cixeghjk491]

///称重服务连接变化
typedef OnConnectionChange = void Function(bool isConnected);

///称重状态变化的回调
///[weight] 净重 g，不包含皮重
///[tare] 皮重 g，
///[isStable] 是否稳定，
typedef OnWeightChange = void Function(
    {required int weight, required int tare, required bool isStable});

///秤状态变化
///[isLightWeight] 秤是否过轻(小于20E)
///[overload] 秤是否过载
///[clearZeroErr] 秤是否清零错误
///[calibrationErr] 秤是否标定错误
typedef OnStatusChange = void Function(
    {required bool isLightWeight,
    required bool overload,
    required bool clearZeroErr,
    required bool calibrationErr});

class ScalePlugin {
  final MethodChannel _channel =
      const MethodChannel("halo.wsgjp.com/scale_plugin");

  static ScalePlugin instance = ScalePlugin._();

  ///称重服务连接变化
  OnConnectionChange? onConnectionChange;

  ///称重状态变化的回调
  OnWeightChange? onWeightChange;

  ///称重状态变化的回调
  OnStatusChange? onStatusChange;

  ///是否连接电子秤服务
  bool isConnected = false;

  ///净重 g，不包含皮重
  int weight = 0;

  ///皮重 g
  int tare = 0;

  ///是否稳定
  bool isStable = false;

  ///秤是否过轻(小于20E)
  bool isLightWeight = false;

  ///秤是否过载
  bool overload = false;

  ///秤是否清零错误
  bool clearZeroErr = false;

  ///秤是否标定错误
  bool calibrationErr = false;

  ScalePlugin._() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case "onConnectionChange":
          _onConnectionChange(call);
          break;
        case "onWeightChange":
          _onWeightChange(call);
          break;
        case "onStatusChange":
          _onStatusChange(call);
          break;
      }
    });
  }

  ///电子秤服务连接状态变化
  void _onConnectionChange(MethodCall call) {
    isConnected = call.arguments;
    onConnectionChange?.call(isConnected);
  }

  ///重量变化
  void _onWeightChange(MethodCall call) {
    weight = call.arguments["weight"];
    tare = call.arguments["tare"];
    isStable = call.arguments["isStable"];
    onWeightChange?.call(weight: weight, tare: tare, isStable: isStable);
  }

  ///秤状态变化
  void _onStatusChange(MethodCall call) {
    isLightWeight = call.arguments["isLightWeight"];
    overload = call.arguments["overload"];
    clearZeroErr = call.arguments["clearZeroErr"];
    calibrationErr = call.arguments["calibrationErr"];
    onStatusChange?.call(
        isLightWeight: isLightWeight,
        overload: overload,
        clearZeroErr: clearZeroErr,
        calibrationErr: calibrationErr);
  }

  /// 初始化和连接电子秤
  Future<void> init() => _channel.invokeMethod("init");

  /// 断开电子秤服务
  Future<void> dispose() {
    onConnectionChange = null;
    onStatusChange = null;
    onWeightChange = null;
    return _channel.invokeMethod("dispose");
  }

  ///清零
  Future<void> zero() => _channel.invokeMethod("zero");

  /// 去⽪/清⽪
  /// 秤上有种重量是去⽪，没有时清⽪
  Future<void> setTare() => _channel.invokeMethod("tare");

  /// 数字去⽪
  Future<void> digitalTare(int tare) =>
      _channel.invokeMethod("digitalTare", tare);
}
