import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../common/login/login_center.dart';

import '../application.dart';
import '../login/tool/shop_config_model.dart';

/// 创建时间：2023/6/15
/// 作者：xiaotiaochong
/// 描述：

/// 工具类
class ExceptionReportUtil {
  // 初始化异常捕获配置
  static void initExceptionCatchConfig() {
    // framework异常捕获，转发到当前的 Zone
    FlutterError.onError = (FlutterErrorDetails details) async {
      Zone.current.handleUncaughtError(
          details.exception, details.stack ?? StackTrace.empty);
    };
  }
}

class BuglyPlugin {
  static const MethodChannel _channel =
      MethodChannel('halo.wsgjp.com/bugly_plugin');

  // 上报异常
  static exceptionReport(dynamic error, dynamic stack) {
    // print('捕获的异常类型 >>> : ${error.runtimeType}');
    // print('捕获的异常信息 >>> : $error');
    // print('捕获的异常堆栈 >>> : $stack');
    Map reportMap = {
      "proflileId": LoginCenter.getLoginUser().profileId ?? "",
      "debug": "${Application.isDebug}",
      'type': "${error.runtimeType}",
      'title': error.toString(),
      'description': stack.toString()
    };
    String reportString = jsonEncode(reportMap);
    // 得使用这个
    // print('这是通过convert转的json');
    // print(jsonEncode(reportMap));
    if (null != Application.navigatorKey.currentState) {
      ShopConfigModel.addPosErrorLog(
          Application.navigatorKey.currentState?.overlay?.context,
          jsonEncode(reportString));
    }
    if (!Platform.isWindows)
      _channel.invokeListMethod(
          'bugly_exception_report', {"error": jsonEncode(reportString)});
  }
}
