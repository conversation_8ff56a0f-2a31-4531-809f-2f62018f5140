import 'dart:io';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import '../../bill/tool/scan_tool.dart';
import '../../common/keyboard_hidden.dart';
import '../../common/style/app_color_helper.dart';
import '../../common/style/app_colors.dart';
import '../../common/style/app_pos_size.dart';
import '../../iconfont/icon_font.dart';

class ScanSelectWidget extends StatefulWidget {
  final KeyboardHiddenFocusNode? searchFocusNode;
  final String hint;
  final double? width;
  final double? height;
  final EdgeInsets? margin;
  final Decoration? decoration;
  final TextEditingController? controller;
  final Function(String text) onSubmitted;
  final ValueGetter<bool>? onTapBefore;
  final Color? backgroundColor;
  final Border? border;

  const ScanSelectWidget(
      {Key? key,
        this.searchFocusNode,
        required this.hint,
        this.decoration,
        this.controller,
        required this.onSubmitted,
        this.onTapBefore,
        this.backgroundColor,
        this.margin,
        this.width,
        this.height,
        this.border})
      : super(key: key);

  @override
  State<ScanSelectWidget> createState() => _ScanSelectWidgetState();
}

class _ScanSelectWidgetState extends State<ScanSelectWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height ?? 70.h,
      width: widget.width ?? 460.w,
      margin: widget.margin ?? EdgeInsets.zero,
      padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
      decoration: widget.decoration ??
          BoxDecoration(
            color: widget.backgroundColor ?? Colors.white,
            border:
            widget.border ?? Border.all(color: AppColors.buttonBorderColor),
            borderRadius: BorderRadius.all(Radius.circular(4.w)),
          ),
      child: Row(
        children: [
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                doScan();
              },
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.w),
                child: IconFont(
                  IconNames.saomiao,
                  color: "#67686A",
                  size: 24.w,
                ),
              )),
          Expanded(
            child: KeyboardHiddenTextField(
              controller: widget.controller,
              hint: widget.hint,
              focusNode: widget.searchFocusNode,
              style: TextStyle(
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  fontSize: AppPosSize.secondaryTitleFontSize.sp),
              onSubmitted: (text) {
                widget.onSubmitted(text);
              },
              onTapBefore: () {
                if (widget.onTapBefore != null) {
                  return widget.onTapBefore!();
                }
                return true;
              },
            ),
          ),
        ],
      ),
    );
  }

  ///扫码
  doScan() async {
    if (Platform.isAndroid) {
      ScanTool.openQRScanPage().then((value) {
        widget.onSubmitted(value);
      });
    }
  }
}
