import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import '../../../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../../../bill/entity/ptype/ptype_prop_dto.dart';
import '../../../bill/model/ptype_model.dart';
import '../../../bill/tool/props_helper.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../login/entity/store/store_info.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../bill/entity/bill_ptype_request.dart';

///
///@ClassName: ptype_selector
///@Description: 重复条码选择框
///@Author: tanglan
///@Date: 8/12/21 1:50 PM
class PtypeSelector extends StatefulWidget {
  final String barCode;
  final List<PtypeListModel> sourceList;
  final int pageSize;
  final Function(BuildContext context, PtypeListModel)? onItemClick;

  const PtypeSelector(
      {Key? key,
      required this.barCode,
      required this.sourceList,
      this.onItemClick,
      required this.pageSize})
      : super(key: key);

  @override
  State createState() => _PtypeSelectorState();
}

class _PtypeSelectorState extends State<PtypeSelector> {
  ///因为进来的参数已经是第1页了，所以在请求的时候从第2页开始
  int pageIndex = 2;

  ///是否有更多数据
  late bool _hasMore;

  @override
  void initState() {
    super.initState();
    _hasMore = widget.sourceList.length >= widget.pageSize;
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      margin: EdgeInsets.symmetric(horizontal: 450.w, vertical: 80.w),
      direction: Axis.vertical,
      color: Colors.white,
      children: [
        HaloContainer(
          mainAxisSize: MainAxisSize.max,
          height: 72.w,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Material(
                child: HaloPosLabel(
              "请选择扫码商品",
              textStyle: TextStyle(
                  fontSize: 24.sp,
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  fontWeight: FontWeight.w600),
            )),
            GestureDetector(
              onTap: () {
                NavigateUtil.pop(context);
              },
              child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: IconFont(
                    IconNames.close,
                    color: "#444365",
                    size: 32.w,
                  )),
            )
          ],
        ),
        Container(height: 1, color: AppColors.dividerColor),
        Material(
          child: HaloContainer(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            color: ColorUtil.stringColor("#FAFAFA"),
            height: 60.w,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  "条码/编号/名称",
                  style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColorHelper(context).getNormalTitleTextColor()),
                ),
              ),
              Expanded(
                  flex: 1,
                  child: Text(
                    "单位",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w500,
                        color:
                            AppColorHelper(context).getNormalTitleTextColor()),
                  )),
              Expanded(
                flex: 1,
                child: Text("单价",
                    textAlign: TextAlign.right,
                    style: TextStyle(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w500,
                        color:
                            AppColorHelper(context).getNormalTitleTextColor())),
              )
            ],
          ),
        ),
        Container(
          height: 1,
          color: AppColors.dividerColor,
        ),
        Expanded(
          child: RawKeyboardListener(
            autofocus: true,
            focusNode: FocusNode(),
            onKey: (RawKeyEvent event) {
              if (event is RawKeyDownEvent &&
                  event.data is RawKeyEventDataAndroid) {
                RawKeyDownEvent rawKeyDownEvent = event;
                RawKeyEventDataAndroid rawKeyEventDataAndroid =
                    rawKeyDownEvent.data as RawKeyEventDataAndroid;
                if (rawKeyEventDataAndroid.keyCode == 66) {
                  widget.onItemClick?.call(context, widget.sourceList.first);
                }
              }
            },
            child: EasyRefresh(
              footer: MaterialFooter(),
              onLoad: _hasMore ? request : null,
              child: ListView.builder(
                  itemCount: widget.sourceList.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                        onTap: () {
                          if (null != widget.onItemClick) {
                            widget.onItemClick!(
                                context, widget.sourceList[index]);
                          }
                        },
                        child: _PtypeSelectItem(widget.sourceList[index]));
                  }),
            ),
          ),
        )
      ],
    );
  }

  ///获取数据
  Future<void> request() async {
    StoreInfo storeInfo = SpTool.getStoreInfo()!;

    BillPtypeRequest paramRequest = BillPtypeRequest();
    paramRequest.btypeId = storeInfo.btypeId;
    paramRequest.otypeId = storeInfo.otypeId;
    paramRequest.ktypeId = storeInfo.ktypeId;
    paramRequest.filterValue = widget.barCode;

    List<PtypeListModel> ptypeList = await PtypeModel.selectPtypeAndCombo(
        context,
        queryParam: paramRequest,
        pageIndex: pageIndex,
        pageSize: widget.pageSize);
    setState(() {
      _hasMore = ptypeList.length >= widget.pageSize;
      widget.sourceList.addAll(ptypeList);
      pageIndex++;
    });
  }
}

class _PtypeSelectItem extends StatelessWidget {
  final PtypeListModel ptype;

  const _PtypeSelectItem(this.ptype);

  ///构建商品属性拼接字符串
  String _buildGoodsPropertyString(PtypeListModel item) {
    List<PtypePropDto> prop = PropsHelper.getPropListBySkuProps(item.sku);
    StringBuffer property = StringBuffer();
    for (int i = 0; i < prop.length; i++) {
      if (i != 0) {
        property.write(":");
      }
      property.write(prop[i].propvalueName ?? "");
    }
    return property.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: HaloContainer(
            padding: EdgeInsets.symmetric(vertical: 18.w, horizontal: 20.w),
            border: Border(
                bottom: BorderSide(width: 1, color: AppColors.dividerColor)),
            children: [
          Expanded(
            flex: 2,
            child: HaloContainer(
              crossAxisAlignment: CrossAxisAlignment.start,
              direction: Axis.vertical,
              children: [
                if (!StringUtil.isEmpty(ptype.fullbarcode))
                  Container(
                      padding: EdgeInsets.only(top: 4.w),
                      child: Text(
                        ptype.fullbarcode,
                        style: TextStyle(
                            color:
                                AppColorHelper(context).getTitleBoldTextColor(),
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w600),
                      )),
                if (!StringUtil.isEmpty(ptype.usercode))
                  Container(
                    padding: EdgeInsets.only(top: 4.w),
                    child: Text(ptype.usercode ?? "",
                        style: TextStyle(
                            color:
                                AppColorHelper(context).getTitleBoldTextColor(),
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w600)),
                  ),
                Container(
                    padding: EdgeInsets.only(top: 4.w),
                    child: Text(
                        "${ptype.fullname}  ${StringUtil.trim(_buildGoodsPropertyString(ptype), trimStr: ":")}",
                        style: TextStyle(
                            color:
                                AppColorHelper(context).getTitleBoldTextColor(),
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w500))),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(ptype.unit?.unitName ?? "",
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: AppColorHelper(context).getTitleBoldTextColor(),
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w500)),
          ),
          Expanded(
            flex: 1,
            child: Text("￥${ptype.currencyPrice.toString()}",
                textAlign: TextAlign.right,
                style: TextStyle(
                    color: AppColors.redTextColor,
                    fontSize: 22.sp,
                    fontWeight: FontWeight.w600)),
          )
        ]));
  }
}
