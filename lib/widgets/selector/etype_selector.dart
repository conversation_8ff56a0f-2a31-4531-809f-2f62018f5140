import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_search.dart';

import '../../../common/string_res.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../login/entity/store/store_etype.dart';

///
///@ClassName: etype_selector
///@Description: 职员查询
///@Author: tanglan
///@Date: 8/16/21 9:54 AM
///
class EtypeSelector extends StatefulWidget {
  Function(BuildContext context, StoreEtype)? onItemClick;

  EtypeSelector({Key? key, this.onItemClick}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _EtypeSelectorState();
  }
}

class _EtypeSelectorState extends State<EtypeSelector> {
  List<StoreEtype> sourceList = [];
  String filterString = "";

  @override
  void initState() {
    super.initState();
    return loadData();
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      margin: EdgeInsets.symmetric(horizontal: 450.w, vertical: 80.w),
      direction: Axis.vertical,
      color: Colors.white,
      children: [
        HaloContainer(
          mainAxisSize: MainAxisSize.max,
          height: 72.w,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "业务员选择",
              style: TextStyle(
                  decoration: TextDecoration.none,
                  fontSize: 24.sp,
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  fontWeight: FontWeight.w600),
            ),
            GestureDetector(
              onTap: () {
                NavigateUtil.pop(context);
              },
              child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: IconFont(
                    IconNames.close,
                    color: "#444365",
                    size: 32.w,
                  )),
            )
          ],
        ),
        Container(height: 1, color: AppColors.dividerColor),
        Material(
          color: Colors.transparent,
          child: HaloSearch(
            value: filterString ?? "",
            inputBackGround: Colors.white,
            hintText: StringRes.HINT_FILTER_ETYPE.getText(context),
            textFontSize: 26.w,
            inputHintTextColor: Color(0xff979ca2),
            textFontColor: AppColorHelper(context).getTitleBoldTextColor(),
            padding: EdgeInsets.zero,
            onSubmitted: (value) {
              filterString = value;
              loadData();
            },
          ),
        ),
        Container(height: 1, color: AppColors.dividerColor),
        HaloContainer(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          color: ColorUtil.stringColor("#FAFAFA"),
          height: 60.w,
          children: [
            Expanded(
              flex: 2,
              child: Text(
                "名称",
                style: TextStyle(
                    decoration: TextDecoration.none,
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColorHelper(context).getNormalTitleTextColor()),
              ),
            ),
            Expanded(
                flex: 1,
                child: Text(
                  "编号",
                  textAlign: TextAlign.right,
                  style: TextStyle(
                      decoration: TextDecoration.none,
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColorHelper(context).getNormalTitleTextColor()),
                )),
          ],
        ),
        Container(
          height: 1,
          color: AppColors.dividerColor,
        ),
        Expanded(
          child: ListView.builder(
              itemCount: sourceList.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                    onTap: () {
                      if (null != widget.onItemClick) {
                        widget.onItemClick!(context, sourceList[index]);
                      }
                    },
                    child: _EtypeSelectItem(sourceList[index]));
              }),
        )
      ],
    );
  }

  loadData() {
    sourceList.clear();
    List<StoreEtype> etypeList = SpTool.getStoreInfo()!.etypeList!;
    if (StringUtil.isEmpty(filterString)) {
      sourceList.addAll(etypeList);
    } else {
      sourceList.addAll(etypeList.where((element) =>
          (element.etypeName != null
              ? element.etypeName!.contains(filterString)
              : false) ||
          (element.usercode != null
              ? element.usercode!.contains(filterString)
              : false)));
    }
    setState(() {});
  }
}

class _EtypeSelectItem extends StatelessWidget {
  StoreEtype _etypeInfo;

  _EtypeSelectItem(this._etypeInfo);

  @override
  Widget build(BuildContext context) {
    return Material(
        child: HaloContainer(
            color: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 18.w, horizontal: 20.w),
            border: Border(
                bottom: BorderSide(width: 1, color: AppColors.dividerColor)),
            children: [
          Expanded(
            flex: 2,
            child: Text(_etypeInfo.etypeName ?? "",
                textAlign: TextAlign.left,
                style: TextStyle(
                    color: AppColorHelper(context).getTitleBoldTextColor(),
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w500)),
          ),
          Expanded(
            flex: 1,
            child: Text(_etypeInfo.usercode ?? "",
                textAlign: TextAlign.right,
                style: TextStyle(
                    color: AppColorHelper(context).getTitleBoldTextColor(),
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w500)),
          )
        ]));
  }
}
