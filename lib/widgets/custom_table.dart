import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

typedef TableCellBuilder<T> = Widget Function(T item, int columnType);

abstract class CustomTable<T> extends StatelessWidget {
  ///列配置，key为columnType，value为列的flex，
  ///区别于普通table，这里允许flex为0，当为0，则这一列隐藏
  final Map<int, num> columnConfig;

  ///表头，如果不需要则不传
  ///key为columnType，value为表头名称
  final Map<int, String>? columnTitle;

  ///表头构造器
  final TableCellBuilder<String>? columnTitleBuilder;

  ///表单元格构造器
  final TableCellBuilder<T> cellBuilder;

  ///是否可以滚动（表头不滚动）
  final bool scrollable;

  ///数据源
  final List<T> data;

  ///整个标题装饰，边框
  final Decoration? decoration;

  ///表头装饰
  final Decoration? titleRowDecoration;

  ///单行装饰
  final Decoration? Function(T item)? itemRowDecorationGetter;
  final ScrollController? scrollController;

  const CustomTable(
      {Key? key,
      required this.columnConfig,
      this.columnTitle,
      this.columnTitleBuilder,
      required this.cellBuilder,
      required this.scrollable,
      required this.data,
      this.decoration,
      this.titleRowDecoration,
      this.itemRowDecorationGetter,
      this.scrollController})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<int> columnTypes = [];
    Map<int, num> columnWidths = {};
    for (var entry in columnConfig.entries) {
      int columnType = entry.key;
      num flex = entry.value;
      //flex为0的一律不展示
      if (flex <= 0) {
        continue;
      }
      columnTypes.add(columnType);
      columnWidths[columnType] = flex;
    }
    if (columnTypes.isEmpty) {
      return Container();
    }
    Widget? title = buildTitle(columnTypes, columnWidths);
    Widget result = buildTable(columnTypes, columnWidths, title != null);
    if (scrollable) {
      result = Expanded(
          child: SingleChildScrollView(
              controller: this.scrollController, child: result));
    }
    if (title != null) {
      result = Column(children: [title, result]);
    }
    if (decoration != null) {
      result =
          Expanded(child: Container(decoration: decoration, child: result));
    }
    return result;
  }

  ///构建表头
  Widget? buildTitle(List<int> columnTypes, Map<int, num> columnWidths);

  ///表头列
  List<Widget> buildTitleColumns(
      List<int> columnTypes, Map<int, num> columnWidths) {
    if (columnTitleBuilder != null &&
        columnTitle?.isNotEmpty == true &&
        columnTypes.isNotEmpty &&
        columnTypes.isNotEmpty) {
      return columnTypes
          .map((columnType) => setCellWidth(
                columnTitleBuilder!
                    .call(columnTitle![columnType] ?? "", columnType),
                columnType,
                columnWidths,
              ))
          .toList();
    }
    return [];
  }

  ///构建表格内容
  Widget buildTable(
      List<int> columnTypes, Map<int, num> columnWidths, bool hasTitle);

  ///表格单行中的列
  List<Widget> buildColumns(
      T item, List<int> columnTypes, Map<int, num> columnWidths) {
    return columnTypes
        .map((columnType) => setCellWidth(
            cellBuilder(item, columnType), columnType, columnWidths))
        .toList();
  }

  Widget setCellWidth(
          Widget cell, int columnType, Map<int, num> columnWidths) =>
      cell;
}

///支持表头和列配置的表格
class CustomColumnTable<T> extends CustomTable<T> {
  final TableCellVerticalAlignment defaultVerticalAlignment;

  final TableBorder? border;

  final TextDirection? textDirection;

  final TextBaseline? textBaseline;

  const CustomColumnTable({
    Key? key,
    required Map<int, double> columnConfig,
    required TableCellBuilder<T> cellBuilder,
    List<T> data = const [],
    Map<int, String>? columnTitle,
    this.textDirection,
    this.textBaseline,
    this.border,
    bool scrollable = false,
    this.defaultVerticalAlignment = TableCellVerticalAlignment.top,
    TableCellBuilder<String>? columnTitleBuilder,
    Decoration? decoration,
    Decoration? titleRowDecoration,
    Decoration? Function(T item)? itemRowDecorationGetter,
  }) : super(
            key: key,
            columnConfig: columnConfig,
            cellBuilder: cellBuilder,
            data: data,
            columnTitle: columnTitle,
            columnTitleBuilder: columnTitleBuilder,
            scrollable: scrollable,
            decoration: decoration,
            titleRowDecoration: titleRowDecoration,
            itemRowDecorationGetter: itemRowDecorationGetter);

  Map<int, TableColumnWidth>? getColumnWidths(
          List<int> columnTypes, Map<int, num> columnWidths) =>
      Map.fromEntries(columnTypes.map((element) => MapEntry(
          element, FlexColumnWidth(columnWidths[element]!.toDouble()))));

  ///构建表头
  @override
  Widget? buildTitle(List<int> columnTypes, Map<int, num> columnWidths) {
    if (columnTitle?.isNotEmpty == true && columnTitleBuilder != null) {
      final children = buildTitleColumns(columnTypes, columnWidths);
      //处理border,避免两个table交界的地方出现边界重合，很丑
      TableBorder? titleBorder;
      if (border != null) {
        titleBorder = TableBorder(
            top: border!.top,
            right: border!.right,
            left: border!.left,
            bottom: border!.horizontalInside,
            verticalInside: border!.verticalInside,
            borderRadius: border!.borderRadius);
      }
      return Table(
          defaultVerticalAlignment: defaultVerticalAlignment,
          border: titleBorder,
          textDirection: textDirection,
          columnWidths: getColumnWidths(columnTypes, columnWidths),
          textBaseline: textBaseline,
          children: [
            TableRow(decoration: titleRowDecoration, children: children)
          ]);
    }
    return null;
  }

  ///构建表格内容
  @override
  Widget buildTable(
      List<int> columnTypes, Map<int, num> columnWidths, bool hasTitle) {
    //处理border,避免两个table交界的地方出现边界重合，很丑
    TableBorder? contentBorder;
    if (border != null) {
      contentBorder = TableBorder(
          top: hasTitle ? BorderSide.none : border!.top,
          right: border!.right,
          left: border!.left,
          bottom: border!.bottom,
          horizontalInside: border!.horizontalInside,
          verticalInside: border!.verticalInside,
          borderRadius: border!.borderRadius);
    }
    return Table(
        defaultVerticalAlignment: defaultVerticalAlignment,
        border: contentBorder,
        textDirection: textDirection,
        columnWidths: getColumnWidths(columnTypes, columnWidths),
        textBaseline: textBaseline,
        children: data
            .map((item) => buildRow(item, columnTypes, columnWidths))
            .toList());
  }

  ///构建行
  TableRow buildRow(T item, List<int> columnTypes, Map<int, num> columnWidths) {
    return TableRow(
        decoration: itemRowDecorationGetter?.call(item),
        children: buildColumns(item, columnTypes, columnWidths));
  }
}

class CustomColumnTableByList<T> extends CustomTable<T> {
  final Widget? divider;
  final ScrollController? scrollController;

  const CustomColumnTableByList(
      {Key? key,
      this.divider,
      required Map<int, num> columnConfig,
      required TableCellBuilder<T> cellBuilder,
      List<T> data = const [],
      Map<int, String>? columnTitle,
      bool scrollable = false,
      TableCellBuilder<String>? columnTitleBuilder,
      Decoration? decoration,
      Decoration? titleRowDecoration,
      Decoration? Function(T item)? itemRowDecorationGetter,
      this.scrollController})
      : super(
            key: key,
            columnConfig: columnConfig,
            cellBuilder: cellBuilder,
            data: data,
            columnTitle: columnTitle,
            columnTitleBuilder: columnTitleBuilder,
            scrollable: scrollable,
            decoration: decoration,
            titleRowDecoration: titleRowDecoration,
            itemRowDecorationGetter: itemRowDecorationGetter);

  @override
  Widget setCellWidth(
          Widget cell, int columnType, Map<int, num> columnWidths) =>
      Expanded(flex: columnWidths[columnType]?.toInt() ?? 1, child: cell);

  @override
  Widget? buildTitle(List<int> columnTypes, Map<int, num> columnWidths) {
    if (columnTitle?.isNotEmpty == true && columnTitleBuilder != null) {
      final children = buildTitleColumns(columnTypes, columnWidths);
      if (children.isNotEmpty) {
        Widget title = Row(children: children);
        if (titleRowDecoration != null) {
          return Container(decoration: titleRowDecoration, child: title);
        }
      }
    }
    return null;
  }

  @override
  Widget buildTable(
      List<int> columnTypes, Map<int, num> columnWidths, bool hasTitle) {
    return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) =>
            buildRow(data[index], columnTypes, columnWidths),
        separatorBuilder: (context, index) => divider ?? Container(),
        itemCount: data.length);
  }

  Widget buildRow(T item, List<int> columnTypes, Map<int, num> columnWidths) {
    Widget row = Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: buildColumns(item, columnTypes, columnWidths));
    Decoration? decoration = itemRowDecorationGetter?.call(item);
    if (decoration != null) {
      row = Container(decoration: decoration, child: row);
    }
    return row;
  }
}
