import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/widget/halo_bottom_sheet.dart';

class DatePicker extends StatefulWidget {
  ///显示模式
  final CupertinoDatePickerMode mode;

  ///设置默认选择日期
  final DateTime? initialDateTime;

  ///选择器可以滚动到的最小日期 没有限制则为空
  final DateTime? minimumDate;

  ///选择器可以滚动到的最大日期 没有限制则为空
  final DateTime? maximumDate;

  ///背景色
  final Color? backgroundColor;

  ///高度
  final double? height;

  ///标题
  final String title;

  ///确定的回调
  final ValueChanged<DateTime> listener;

  DatePicker(
      {Key? key,
      CupertinoDatePickerMode? mode,
      DateTime? initialDateTime,
      this.minimumDate,
      this.maximumDate,
      double? height,
      required this.title,
      required this.listener,
      Color? backgroundColor})
      : this.height = height ?? 200.h,
        this.mode = mode ?? CupertinoDatePickerMode.date,
        this.initialDateTime = initialDateTime ?? DateTime.now(),
        this.backgroundColor = backgroundColor ?? Colors.white,
        super(key: key);

  @override
  _DatePickerState createState() => _DatePickerState();

  static void showDatePicker(
    BuildContext context, {
    CupertinoDatePickerMode? mode,
    DateTime? initialDateTime,
    DateTime? minimumDate,
    DateTime? maximumDate,
    double? height,
    Color? backgroundColor,
    required String title,
    required ValueChanged<DateTime> listener,
  }) {
    HaloBottomSheet.show(
      context,
      DatePicker(
        mode: mode,
        initialDateTime: initialDateTime,
        minimumDate: minimumDate,
        maximumDate: maximumDate,
        height: height,
        backgroundColor: backgroundColor,
        title: title,
        listener: listener,
      ),
    );
  }
}

class _DatePickerState extends State<DatePicker> {
  late DateTime _date;

  @override
  void initState() {
    _date = widget.initialDateTime!;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HaloBottomSheet(
      context,
      topBar: Container(
        height: 50,
        padding: EdgeInsets.fromLTRB(20, 13, 20, 0),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
                color: Colors.white,
                offset: Offset(0.0, 1.0), //阴影xy轴偏移量
                spreadRadius: 1.0 //阴影扩散程度
                ),
          ],
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  "取消",
                  style: TextStyle(color: Colors.grey[400], fontSize: 16),
                )),
            Text(
              widget.title ?? "",
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w500),
            ),
            TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  widget.listener.call(_date);
                },
                child: Text(
                  "确定",
                  style: TextStyle(color: Colors.blue, fontSize: 16),
                )),
          ],
        ),
      ),
      contentWidget: Container(
        width: double.infinity,
        height: widget.height,
        child: CupertinoDatePicker(
          initialDateTime: _date,
          mode: widget.mode,
          minimumDate: widget.minimumDate,
          maximumDate: widget.maximumDate,
          backgroundColor: widget.backgroundColor,
          onDateTimeChanged: (DateTime date) => _date = date,
        ),
      ),
    );
  }
}
