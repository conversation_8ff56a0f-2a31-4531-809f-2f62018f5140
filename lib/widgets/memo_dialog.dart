import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:halo_utils/halo_utils.dart';

import '../bill/widget/ptype/base_goods_dialog.dart';

///备注输入
class MemoInputDialog extends StatefulWidget {
  final TextEditingController controller;

  const MemoInputDialog({Key? key, required this.controller}) : super(key: key);

  @override
  State<MemoInputDialog> createState() => _MemoInputDialogState();
}

class _MemoInputDialogState extends BaseGoodsDialogState<MemoInputDialog> {
  @override
  final height = 420.h;

  @override
  final width = 574.w;

  @override
  final title = "备注";

  @override
  final dialogPadding = EdgeInsets.all(20.w);

  @override
  Widget buildContent(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      margin: dialogPadding,
      // padding: EdgeInsets.all(20.w),
      color: Colors.white,
      // decoration: BoxDecoration(
      //   color: Colors.white,
      //   borderRadius: BorderRadius.circular(4.w),
      //   border: Border.all(color: _colorBlue, width: 1.w),
      // ),
      child: TextField(
        maxLines: null,
        controller: widget.controller,
        style: TextStyle(fontSize: 30.sp, color: const Color(0xFF333333)),
        decoration: const InputDecoration(
          isCollapsed: true,
          border: OutlineInputBorder(borderSide: BorderSide.none),
        ),
        inputFormatters: [LengthLimitingTextInputFormatter(200)],
      ),
    );
  }
}
