import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:halo_utils/halo_utils.dart';
import 'dart:convert' as convert;
import 'package:halo_utils/utils/string_util.dart';

/// Time：2022/9/6 17:16
/// Author：yi.zhang
/// E-mail: <EMAIL>
/// Description：省市区选择器

enum PICKER_TYPE {
  PROVINCE, //省
  CITY, //市
  DISTRICT, //区
  STREET //街道
}

class CityPicker extends StatefulWidget {
  final Function(Map)? callback;
  final Map? addressMap;

  const CityPicker({Key? key, this.callback, this.addressMap})
      : super(key: key);

  @override
  State<CityPicker> createState() => _CityPickerState();
}

class _CityPickerState extends State<CityPicker> with TickerProviderStateMixin {
  ///页面控制
  late PageController pageController; //page控制器
  late TabController tabController; //tab控制器
  List<String> tabs = ["", "", "", ""]; //tabbar标题
  int currentPageIndex = 0; //当前页面

  ///各个列表页的数据
  List<CityItem> provinceList = []; //省
  List<CityItem> cityList = []; //市
  List<CityItem> districtList = []; //区
  List<CityItem> streetList = []; //街道

  Map choosedMap = {}; //当前选中的地址
  bool isLoading = false;

  ///展示上级页面传过来的数据
  Map addressMap = {}; //传过来的地址
  late int provinceIndex; //省坐标
  late int cityIndex; //市坐标
  late int districtIndex; //区坐标
  late int streetIndex; //街道坐标

  @override
  void initState() {
    initController();
    initData();
    super.initState();
  }

  ///初始化控制器
  void initController() {
    tabController =
        TabController(length: 4, vsync: this, initialIndex: currentPageIndex);
    pageController = PageController(initialPage: currentPageIndex);
  }

  ///初始化数据
  void initData() async {
    addressMap = widget.addressMap ?? {};
    setState(() {
      isLoading = true;
    });

    //本地获取省市区信息
    String result = await loadAssetJSON();
    //省市区json转换成数组
    Map<String, dynamic> map = convert.jsonDecode(result);
    provinceList = (map['data'] as List<dynamic>)
        .map((e) => CityItem.fromMap((e as Map<String, dynamic>)))
        .toList();
    //展示上级页面选中的省市区数据并选中
    showCheckedAddress();
    setState(() {
      isLoading = false;
    });

    //不要删！！！！网络获取数据（当需要更新数据时，调这个接口，数据在halobackend断点获取，复制到到city.json中更新数据）
    // ShellModel.getAddress(context).then((value) {
    //   // List<Map<String, dynamic>> listMap = new List<Map<String, dynamic>>.from(value);
    //   // print("address---->"+listMap.length.toString());
    // });
  }

  //展示上级页面选中的省市区数据并选中
  void showCheckedAddress() {
    int mapLength = addressMap.length;
    if (mapLength == 0) {
      return;
    }

    //省
    if (mapLength > 0 && StringUtil.isNotEmpty(addressMap['province'])) {
      provinceIndex =
          provinceList.indexWhere((e) => e.name == addressMap['province']);
      if (provinceIndex > -1) {
        provinceList[provinceIndex].isChecked = true;
      }
      choosedMap['province'] = addressMap['province'];
    }

    //市
    if (mapLength > 1 && StringUtil.isNotEmpty(addressMap['city'])) {
      cityList = provinceList[provinceIndex].child;
      cityIndex = cityList.indexWhere((e) => e.name == addressMap['city']);
      if (cityIndex > -1) {
        cityList[cityIndex].isChecked = true;
      }
      choosedMap['city'] = addressMap['city'];
    }

    //区
    if (mapLength > 2 && StringUtil.isNotEmpty(addressMap['district'])) {
      districtList = cityList[cityIndex].child;
      districtIndex =
          districtList.indexWhere((e) => e.name == addressMap['district']);
      if (districtIndex > -1) {
        districtList[districtIndex].isChecked = true;
      }
      choosedMap['district'] = addressMap['district'];
    }

    //街道
    if (mapLength > 3 && StringUtil.isNotEmpty(addressMap['street'])) {
      streetList = districtList[districtIndex].child;
      streetIndex =
          streetList.indexWhere((e) => e.name == addressMap['street']);
      if (streetIndex > -1) {
        streetList[streetIndex].isChecked = true;
      }
      choosedMap['street'] = addressMap['street'];
    }

    tabs = [
      addressMap['province'] ?? "",
      addressMap['city'] ?? "",
      addressMap['district'] ?? "",
      addressMap['street'] ?? ""
    ];

    List tempValues = [];
    for (var element in addressMap.values) {
      if (StringUtil.isNotEmpty(element)) {
        tempValues.add(element);
      }
    }
    if (StringUtil.isNotEmpty(addressMap['province'])) {
      tabController = TabController(
          length: 4, vsync: this, initialIndex: tempValues.length - 1);
      pageController = PageController(initialPage: tempValues.length - 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        constraints: BoxConstraints(
          maxHeight: ScreenUtil().screenHeight * 4 / 5,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: ScreenUtil().setHeight(100),
              alignment: Alignment.center,
              padding: EdgeInsets.only(
                  left: ScreenUtil().setWidth(30),
                  right: ScreenUtil().setWidth(30)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(width: ScreenUtil().setWidth(50)),
                  Center(
                    child: Text("选择所在地区",
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: ScreenUtil().setSp(32),
                            color: const Color(0xFF333333))),
                  ),
                  GestureDetector(
                    child: Icon(
                      Icons.close,
                      size: ScreenUtil().setWidth(50),
                      color: const Color(0xFF999999),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                    },
                  )
                ],
              ),
            ),
            Container(
              height: ScreenUtil().setHeight(1),
              width: ScreenUtil().screenWidth,
              color: const Color(0xFFE9E9E9),
            ),
            Visibility(
              visible: StringUtil.isNotEmpty(tabs[0]),
              child: Container(
                alignment: Alignment.topLeft,
                margin: EdgeInsets.only(
                    bottom: ScreenUtil().setHeight(40),
                    top: ScreenUtil().setHeight(25)),
                child: TabBar(
                  controller: tabController,
                  padding: EdgeInsets.zero,
                  indicatorColor: Colors.transparent,
                  labelColor: const Color(0xFF2288FC),
                  labelStyle: TextStyle(
                      color: const Color(0xFF2288FC),
                      fontSize: ScreenUtil().setSp(28),
                      fontWeight: FontWeight.w500),
                  unselectedLabelColor: const Color(0xFF666666),
                  unselectedLabelStyle: TextStyle(
                      color: const Color(0xFF666666),
                      fontSize: ScreenUtil().setSp(28),
                      fontWeight: FontWeight.w500),
                  isScrollable: true,
                  tabs: tabs.map((e) => Text(e)).toList(),
                  onTap: (int index) {
                    currentPageIndex = index;
                    pageController.animateToPage(currentPageIndex,
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.linear);
                  },
                ),
              ),
            ),
            Visibility(
                visible: isLoading,
                child: const Expanded(
                  child: SizedBox(
                    width: double.infinity,
                    child: CupertinoActivityIndicator(
                      radius: 14,
                    ),
                  ),
                )),
            Visibility(
              visible: !isLoading,
              child: Expanded(
                child: PageView(
                  onPageChanged: (index) {
                    currentPageIndex = index;
                    tabController.animateTo(index,
                        duration: const Duration(milliseconds: 300));
                  },
                  controller: pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildProvinceList(),
                    _buildCityList(),
                    _buildDistrictList(),
                    _buildStreetList(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  //省
  Widget _buildProvinceList() {
    return ListView.builder(
      itemCount: provinceList.length,
      itemBuilder: (buildContext, index) {
        return _buildItem(index, provinceList[index], PICKER_TYPE.PROVINCE);
      },
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      controller: ScrollController(),
    );
  }

  //市
  Widget _buildCityList() {
    return ListView.builder(
      itemCount: cityList.length,
      itemBuilder: (buildContext, index) {
        return _buildItem(index, cityList[index], PICKER_TYPE.CITY);
      },
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      controller: ScrollController(),
    );
  }

  //区
  Widget _buildDistrictList() {
    return ListView.builder(
      itemCount: districtList.length,
      itemBuilder: (buildContext, index) {
        return _buildItem(index, districtList[index], PICKER_TYPE.DISTRICT);
      },
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      controller: ScrollController(),
    );
  }

  //街道
  Widget _buildStreetList() {
    return ListView.builder(
      itemCount: streetList.length,
      itemBuilder: (buildContext, index) {
        return _buildItem(index, streetList[index], PICKER_TYPE.STREET);
      },
      padding: const EdgeInsets.all(0),
      shrinkWrap: true,
      controller: ScrollController(),
    );
  }

  //
  Widget _buildItem(int index, CityItem item, PICKER_TYPE type) {
    return GestureDetector(
      onTap: () {
        resetChecked(type);
        item.isChecked = true;
        //省
        if (type == PICKER_TYPE.PROVINCE) {
          choosedMap['province'] = item.name;
          cityList = item.child;
          tabs = [choosedMap['province'], "请选择", "", ""];

          if (cityList.isNotEmpty) {
            currentPageIndex = 1;
            tabController.animateTo(1,
                duration: const Duration(milliseconds: 300));
            pageController.animateToPage(1,
                duration: const Duration(milliseconds: 200),
                curve: Curves.linear);
          } else {
            choosedMap.remove("city");
            choosedMap.remove("district");
            choosedMap.remove("street");
            widget.callback?.call(choosedMap);
            Navigator.pop(context);
          }
        }
        //市
        else if (type == PICKER_TYPE.CITY) {
          choosedMap['city'] = item.name;
          districtList = item.child;
          tabs = [choosedMap['province'], choosedMap['city'], "请选择", ""];

          if (districtList.isNotEmpty) {
            currentPageIndex = 2;
            tabController.animateTo(2,
                duration: const Duration(milliseconds: 300));
            pageController.animateToPage(2,
                duration: const Duration(milliseconds: 200),
                curve: Curves.linear);
          } else {
            choosedMap.remove("district");
            choosedMap.remove("street");
            widget.callback?.call(choosedMap);
            Navigator.pop(context);
          }
        }
        //区
        else if (type == PICKER_TYPE.DISTRICT) {
          choosedMap['district'] = item.name;
          streetList = item.child;
          tabs = [
            choosedMap['province'],
            choosedMap['city'],
            choosedMap['district'],
            "请选择"
          ];

          if (streetList.isNotEmpty) {
            currentPageIndex = 3;
            tabController.animateTo(3,
                duration: const Duration(milliseconds: 300));
            pageController.animateToPage(3,
                duration: const Duration(milliseconds: 200),
                curve: Curves.linear);
          } else {
            choosedMap.remove("street");
            widget.callback?.call(choosedMap);
            Navigator.pop(context);
          }
        }
        //街道
        else if (type == PICKER_TYPE.STREET) {
          choosedMap['street'] = item.name;
          tabs = [
            choosedMap['province'],
            choosedMap['city'],
            choosedMap['district'],
            choosedMap['street']
          ];
          widget.callback?.call(choosedMap);
          Navigator.pop(context);
        }
        setState(() {});
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: ScreenUtil().setHeight(30),
            vertical: ScreenUtil().setWidth(20)),
        child: Text(
          item.name ?? "",
          style: TextStyle(
              color: item.isChecked
                  ? const Color(0xFF2288FC)
                  : const Color(0xFF333333),
              fontSize: ScreenUtil().setSp(28)),
        ),
      ),
    );
  }

  //重置选中状态
  void resetChecked(PICKER_TYPE type) {
    switch (type) {
      case PICKER_TYPE.PROVINCE:
        for (var element in provinceList) {
          element.isChecked = false;
        }
        break;
      case PICKER_TYPE.CITY:
        for (var element in cityList) {
          element.isChecked = false;
        }
        break;
      case PICKER_TYPE.DISTRICT:
        for (var element in districtList) {
          element.isChecked = false;
        }
        break;
      case PICKER_TYPE.STREET:
        for (var element in streetList) {
          element.isChecked = false;
        }
        break;
    }
  }

  ///获取数据文件
  static Future<String> loadAssetJSON() async {
    return await rootBundle.loadString('assets/city.json');
  }

  @override
  void dispose() {
    pageController.dispose();
    tabController.dispose();
    super.dispose();
  }
}

class CityItem {
  int? id;
  String? name;
  List<CityItem> child = [];
  bool isChecked = false; //是否选中

  static CityItem fromMap(Map<String, dynamic> map) {
    CityItem item = CityItem();
    item.child =
        (map['child'] as List?)?.map((o) => CityItem.fromMap(o)).toList() ?? [];
    item.id = map['id'];
    item.name = map['name'];
    return item;
  }

  Map toJson() => {"child": child, "id": id, "name": name};
}
