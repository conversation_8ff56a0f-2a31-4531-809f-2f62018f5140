import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:scan_gun/scan_monitor_widget.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../plugin/scanner_plugin.dart';
import '../bill/bill/channel/widget/base_common_dialog.dart';
import '../bill/tool/scan_tool.dart';
import 'halo_pos_label.dart';

Future<String?> showScannerDialog(BuildContext context) async {
  String? scanResult = await DialogUtil.showAlertDialog(context,
      child: const ScannerDialog(), dismissOnTouchOutside: false);
  if (scanResult?.endsWith("\n") == true) {
    scanResult = scanResult!.substring(0, scanResult.lastIndexOf("\n"));
  }
  return scanResult;
}

class ScannerDialog extends StatefulWidget {
  const ScannerDialog({Key? key}) : super(key: key);

  @override
  State<ScannerDialog> createState() => _ScannerDialogState();
}

class _ScannerDialogState extends BaseCommonDialogState<ScannerDialog> {
  String scanResult = "";
  int timestamp = 0;
  bool stopScan = false;
  final FocusNode _focusNode = FocusNode();
  int popTimes = 0;

  @override
  void initState() {
    super.initState();
    ScannerPlugin.callback = (value) {
      stopScan = true;
      _pop(value);
    };
  }

  @override
  void dispose() {
    ScannerPlugin.callback = null;
    super.dispose();
  }

  @override
  buildTitle(BuildContext context) {
    return Container();
  }

  @override
  Widget buildContent(BuildContext context) {
    return ScanMonitorWidget(
      childBuilder: (context) {
        return HaloContainer(
          margin: EdgeInsets.only(top: 60.w),
          color: Colors.white,
          direction: Axis.vertical,
          children: [
            Image.asset(
              'assets/images/saomiao.png',
              width: 250.w,
              height: 250.w,
            ),
            Padding(
              padding: EdgeInsets.all(5.w),
              child: HaloPosLabel(
                "等待扫码支付...",
                textStyle: TextStyle(
                    color: Colors.black,
                    fontSize: 30.sp,
                    fontWeight: FontWeight.bold),
              ),
            ),
            RichText(
              text: TextSpan(
                style: DefaultTextStyle.of(context).style,
                children: <InlineSpan>[
                  TextSpan(
                      text: '平板设备请',
                      style: TextStyle(color: Colors.grey, fontSize: 28.sp)),
                  TextSpan(
                    text: '点击扫码',
                    style: TextStyle(color: Colors.blueAccent, fontSize: 30.sp),
                    recognizer: TapGestureRecognizer()..onTap = doScan,
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 10.w, bottom: 50.w),
              child: HaloPosLabel("为避免扫码异常，扫码过程中请勿使用键盘",
                  textStyle: TextStyle(
                    color: Colors.grey,
                    fontSize: 22.sp,
                  )),
            ),
            SizedBox(
                width: 250.w,
                height: 80.w,
                child: OutlinedButton(
                    style: ButtonStyle(
                        side: MaterialStateProperty.all(
                            BorderSide(color: Colors.blueAccent, width: 1.w))),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text("取消",
                        style: TextStyle(
                            color: Colors.blueAccent,
                            fontSize: 30.sp,
                            fontWeight: FontWeight.bold))))
          ],
        );
      },
      onSubmit: (String result) {
        if (result.isNotEmpty) {
          stopScan = true;
          _pop(result); //接收到扫码结果
        }
      },
    );
  }

  ///扫码
  doScan() async {
    if (Platform.isAndroid) {
      ScanTool.openQRScanPage().then((value) {
        scanResult = value;
        _pop(scanResult);
      });
    }
  }

  void _pop(String result) {
    if (mounted && popTimes == 0) {
      popTimes = 1;
      Navigator.pop(context, result);
    }
  }

  @override
  // TODO: implement height
  double get height => 650.w;

  @override
  // TODO: implement title
  String get title => "";

  @override
  // TODO: implement width
  double get width => 650.w;
}
