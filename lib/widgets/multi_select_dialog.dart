import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../bill/widget/ptype/base_goods_dialog.dart';
import '../../../common/standard.dart';
import '../../../entity/select_wrapper_bean.dart';
import '../../../iconfont/icon_font.dart';

///编辑会员中多选弹窗（标签、门店、权益卡）
class MultiSelectDialog<T> extends StatefulWidget {
  ///标题
  final String title;

  ///数据源
  final AsyncValueGetter<List<SelectWrapperBean<T>>> listGetter;

  ///获取item展示的名称
  final ResultFunction<T, String> nameGetter;

  const MultiSelectDialog(
      {Key? key,
      required this.title,
      required this.listGetter,
      required this.nameGetter})
      : super(key: key);

  @override
  State<MultiSelectDialog> createState() => _MultiSelectDialogState<T>();
}

class _MultiSelectDialogState<T>
    extends BaseGoodsDialogState<MultiSelectDialog<T>>
    with ListAndListTitleMixin {
  @override
  final double height = 720.h;

  @override
  final double width = 696.w;

  ///数据源
  List<SelectWrapperBean<T>>? data;

  @override
  List<String> get listTitles => [];

  ///列表各列比例
  @override
  List<int> get listColumnFlex => [];

  @override
  String get title => widget.title;

  @override
  int get itemCount => data?.length ?? 0;

  @override
  final EdgeInsets dialogPadding = EdgeInsets.only(left: 32.w, right: 30.w);

  @override
  void initState() {
    super.initState();
    widget.listGetter().then((value) => setState(() => data = value));
  }

  @override
  Widget buildContent(BuildContext context) {
    return Column(children: [
      Expanded(child: super.buildContent(context)),
      //底部分割线及确定按钮
      _buildBottom(context)
    ]);
  }

  @override
  Widget buildListItem(BuildContext context, int index) {
    SelectWrapperBean<T> item = data![index];
    String name = widget.nameGetter.call(item.data);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 76.h,
          padding: listPadding,
          child: Row(
            children: [
              Expanded(
                child: Text(
                  name,
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 22.sp,
                      overflow: TextOverflow.ellipsis),
                  maxLines: 1,
                ),
              ),
              GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    if (item.enable) {
                      setState(() => item.selected = !item.selected);
                    }
                  },
                  child: Container(
                    alignment: Alignment.center,
                    padding: EdgeInsets.all(20.h),
                    child: IconFont(
                        item.selected
                            ? IconNames.xuanzhong
                            : IconNames.weixuanzhong,
                        size: 30.w),
                  ))
            ],
          ),
        ),
        divider,
      ],
    );
  }

  ///构建底部分割线和确定按钮
  _buildBottom(BuildContext context) {
    return Column(
      children: [
        divider,
        Container(
          height: 122.h,
          padding: dialogPadding,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildButton(context, "全选",
                  onTap: () => setState(() =>
                      data?.forEach((element) => element.selected = true))),
              _buildButton(context, "取消", onTap: () => Navigator.pop(context)),
              _buildButton(
                context,
                "确定",
                textColor: Colors.white,
                background: const Color(0xFF4679FC),
                borderColor: null,
                onTap: () => Navigator.pop(
                    context,
                    data
                        ?.where((element) => element.selected)
                        .map((e) => e.data)
                        .toList()),
              ),
            ],
          ),
        ),
      ],
    );
  }

  ///底部按钮
  Widget _buildButton(
    BuildContext context,
    String content, {
    Color textColor = const Color(0xFF333333),
    Color background = Colors.white,
    Color? borderColor = const Color(0xFF999999),
    VoidCallback? onTap,
  }) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: 66.h,
          width: 196.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: background,
            borderRadius: BorderRadius.circular(4.w),
            border: borderColor?.let(
                (borderColor) => Border.all(color: borderColor, width: 2.w)),
          ),
          child: Text(content,
              style: TextStyle(color: textColor, fontSize: 26.sp)),
        ));
  }
}
