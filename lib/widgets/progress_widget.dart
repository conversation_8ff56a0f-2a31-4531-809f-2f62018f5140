import 'package:flutter/material.dart';

///
///@ClassName: progress_widget
///@Description:
///@Author: tanglan
///@Date: 2023/12/25
///虚线控件
class ProgressWidget extends StatelessWidget {
  ///进度
  final double? value;

  ///最小高度
  final double? minHeight;

  final Color? color;
  final Color? backgroundColor;
  final Animation<Color?>? valueColor;

  const ProgressWidget(
      {Key? key,
      required this.value,
      this.backgroundColor,
      this.color,
      this.valueColor,
      this.minHeight})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LinearProgressIndicator(
        value: value ?? 0,
        minHeight: minHeight,
        color: color,
        valueColor: valueColor,
        backgroundColor: backgroundColor);
  }
}
