import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';

///
///@ClassName: focus_text_field
///@Description: 类作用描述
///@Author: tanglan
///@Date: 7/5/21 4:56 PM
class FocusTextField extends StatefulWidget {
  final String? title;
  String? value;
  final Widget? rightWidget;
  final bool? obscureText;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final TextInputType? keyboardType;
  final Border? border;
  final Widget? labelWidget;
  final BorderRadius? borderRadius;
  final Function(String? value)? onChange;
  final List<TextInputFormatter>? inputFormatter;
  final TextStyle? textStyle;
  final TextStyle? hintTextStyle;

  FocusTextField(
      {Key? key,
      this.title,
      this.value,
      this.obscureText,
      this.rightWidget,
      this.margin,
      this.padding,
      this.keyboardType,
      this.border,
      this.labelWidget,
      this.borderRadius,
      this.textStyle,
      this.hintTextStyle,
      this.inputFormatter,
      this.onChange})
      : super(key: key);

  @override
  _FocusTextFieldState createState() {
    return _FocusTextFieldState();
  }
}

class _FocusTextFieldState extends State<FocusTextField> {
  FocusNode focusNode = FocusNode();

  bool isFocus = false;

  @override
  void initState() {
    super.initState();
    focusNode.addListener(() {
      setState(() {
        isFocus = focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      margin: widget.margin,
      padding: widget.padding,
      color: Colors.white,
      borderRadius: widget.borderRadius,
      border: widget.border ??
          Border(
              bottom: BorderSide(
                  width: 2.w,
                  color: isFocus
                      ? ColorUtil.stringColor("#2288FC")
                      : ColorUtil.stringColor("#e9e9e9"))),
      children: getContentWidget(),
    );
  }

  List<Widget> getContentWidget() {
    List<Widget> widgets = [];
    widgets.add(Expanded(
        child: TextField(
      focusNode: focusNode,
      obscureText: widget.obscureText ?? false,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatter,
      controller: TextEditingController.fromValue(
        TextEditingValue(
            text: widget.value ?? "",
            selection: TextSelection.fromPosition(TextPosition(
                affinity: TextAffinity.downstream,
                offset: widget.value?.length ?? 0))),
      ),
      onChanged: (value) {
        widget.value = value;
        if (widget.onChange != null) {
          widget.onChange!(widget.value);
        }
      },
      decoration: InputDecoration(
          hintText: "请输入${widget.title}",
          hintStyle: widget.hintTextStyle,
          icon: widget.labelWidget ??
              Container(
                  padding: EdgeInsets.only(right: 10.w),
                  width: 120.w,
                  child: Text(
                    widget.title ?? "",
                    style: TextStyle(
                        fontSize: 32.sp,
                        fontWeight: FontWeight.w500,
                        color: ColorUtil.stringColor("#666666")),
                  )),
          border: InputBorder.none),
      style: widget.textStyle ??
          TextStyle(
              fontSize: 32.sp,
              fontWeight: FontWeight.w500,
              color: ColorUtil.stringColor("#333333")),
    )));
    if (null != widget.rightWidget) {
      widgets.add(widget.rightWidget!);
    }
    return widgets;
  }
}
