import 'package:flutter/cupertino.dart';

/**
 * @ClassName:      halo_base_stateful_widget
 * @Author:         tlan
 * @CreateDate:     2020/4/27 11:27
 * @Description:     java类作用描述
 */

abstract class HaloBaseStatefulWidget extends StatefulWidget {
  final bool enable;
  final bool visible;
  final bool require;
  final bool requirePosition; //require隐藏时是否保留位置
  final String requireTips;
  final bool isPrint;

  const HaloBaseStatefulWidget(
      {Key? key,
      this.enable = true,
      this.visible = true,
      this.require = false,
      this.requirePosition = true,
      this.isPrint = false,
      this.requireTips = ""})
      : super(key: key);

  @override
  HaloBaseStatefulWidgetState createState();

  bool isValid() {
    return true;
  }

  String buildPrintTitle() {
    return "";
  }

  String buildPrintValue() {
    return "";
  }
}

abstract class HaloBaseStatefulWidgetState<T extends HaloBaseStatefulWidget>
    extends State<T> {
  @override
  Widget build(BuildContext context);
}
