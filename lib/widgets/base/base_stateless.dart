import 'package:flutter/material.dart';

///
///@ClassName: base_stateless
///@Description:
///@Author: tanglan
///@Date: 7/29/21 3:27 PM
abstract class BaseStatelessPage extends StatelessWidget {
  final Color? bgColor;
  final _defaultAppTitleFontSize = 34;

  const BaseStatelessPage({Key? key, this.bgColor}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return _buildBody(context);
  }

  String getTitle(BuildContext context);

  List<Widget>? buildAppBarActions(BuildContext context) {
    return null;
  }

  ///返回按钮监听
  Future<bool> backWillPop() {
    return _backWillPop();
  }

  Future<bool> _backWillPop() {
    return Future.value(true); //true 返回/ false 取消返回
  }

  Widget buildBody(BuildContext context);

  _buildBody(BuildContext context) {
    Widget bodyWidget = Container(
        padding: EdgeInsets.only(
            bottom: MediaQuery
                .of(context)
                .viewInsets
                .bottom > 0 ? 45 : 0),
        child: buildBody(context));
    if (bodyWidget == null) {
      bodyWidget = Text("NO Content");
    }

    return bodyWidget;
  }
}
