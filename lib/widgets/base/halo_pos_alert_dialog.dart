import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/halo_dialog.dart';

import '../../common/style/app_color_helper.dart';
import '../../common/style/app_colors.dart';
import '../halo_pos_label.dart';

///
///@ClassName: alert_dialog
///@Description:  警告弹框组件
///@Author: tanglan
///@Date: 2024/5/9
class HaloPosAlertDialog extends StatelessWidget {
  ///标题 默认为请确认
  final String? title;

  ///是否展示标题栏，默认为true
  final bool showTitle;

  ///展示文字内容
  final String? content;

  final TextStyle? contentTextStyle;

  ///是否展示取消按钮，默认为true
  final bool showCancel;

  ///取消按钮
  final Function? onCancelCallBack;

  ///确定按钮
  final Function? onSubmitCallBack;

  final double? width;

  final double? height;
  final String? button1Text;
  final String? button2Text;

  ///确认后（onSubmitCallBack）是否自动退出
  final bool autoAfterSubmit;

  ///点击取消 是否自动退出
  final bool autoAfterCancel;

  ///内容是否区中
  final bool isContentCenter;

  const HaloPosAlertDialog({Key? key,
    this.showTitle = true,
    this.title,
    this.content,
    this.onSubmitCallBack,
    this.showCancel = true,
    this.onCancelCallBack,
    this.width,
    this.height,
    this.contentTextStyle,
    this.button1Text,
    this.isContentCenter = false,
    this.autoAfterSubmit = true,
    this.autoAfterCancel = true,
    this.button2Text})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: HaloContainer(
            width: width ?? 560.w,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            direction: Axis.vertical,
            color: Colors.white,
            children: [
              HaloContainer(
                visible: showTitle,
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                height: 48.h,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                border: Border(
                    bottom: BorderSide(
                        color: AppColors.dividerColor, width: 1.w)),
                children: [
                  HaloPosLabel(
                    title ?? "请确认",
                    textStyle: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColorHelper(context).getTitleTextColor()),
                  )
                ],
              ),
              HaloContainer(
                  mainAxisSize:
                  isContentCenter ? MainAxisSize.min : MainAxisSize.max,
                  mainAxisAlignment: isContentCenter
                      ? MainAxisAlignment.center
                      : MainAxisAlignment.start,
                  padding: EdgeInsets.symmetric(
                      horizontal: 24.w, vertical: 12.h),
                  constraints: BoxConstraints(minHeight: 120.h),
                  children: [
                    Expanded(
                        child: HaloPosLabel(
                          content ?? "",
                          maxLines: 100,
                          textStyle: contentTextStyle ??
                              TextStyle(
                                  fontWeight: FontWeight.w500,
                                  decoration: TextDecoration.none,
                                  color: AppColors.secondTextColor,
                                  fontSize: 22.sp),
                        ))
                  ]),
              HaloContainer(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                height: 60.h,
                margin: EdgeInsets.only(top: 12.h),
                padding: EdgeInsets.only(
                  right: 36.w,
                  bottom: 12.h,
                ),
                children: [
                  HaloButton(
                    text: button1Text ?? "确定",
                    height: 46.h,
                    fontSize: 28.sp,
                    outLineWidth: 1.h,
                    onPressed: () {
                      if (null != onSubmitCallBack) {
                        onSubmitCallBack!();
                      }
                      if (autoAfterSubmit) {
                        NavigateUtil.pop(context);
                      }
                    },
                  ),
                  HaloContainer(
                    visible: showCancel,
                    margin: EdgeInsets.only(left: 40.w),
                    children: [
                      HaloButton(
                        text: button2Text ?? "取消",
                        buttonType: HaloButtonType.outlinedButton,
                        height: 46.h,
                        fontSize: 28.sp,
                        outLineWidth: 1.h,
                        onPressed: () {
                          if (null != onCancelCallBack) {
                            onCancelCallBack!();
                          }
                          if (autoAfterCancel) {
                            NavigateUtil.pop(context);
                          };
                        },
                      ),
                    ],
                  )
                ],
              )
            ]));
  }

  static Future showAlertDialog(BuildContext context,
      {String? title,
        bool showTitle = true,
        String? content,
        TextStyle? contentTextStyle,
        bool showCancel = true,
        bool dismissOnTouchOutside = true,
        bool dismissOnBackKeyPress = true,
        bool autoAfterSubmit = true,
        String? button1Text,
        String? button2Text,
        Function? onCancelCallBack,
        Function? onSubmitCallBack}) {
    return HaloDialog(context,
        dismissOnTouchOutside: dismissOnTouchOutside,
        dismissOnBackKeyPress: dismissOnBackKeyPress,
        child: HaloPosAlertDialog(
          title: title,
          showTitle: showTitle,
          content: content,
          contentTextStyle: contentTextStyle,
          showCancel: showCancel,
          autoAfterSubmit: autoAfterSubmit,
          onCancelCallBack: onCancelCallBack,
          onSubmitCallBack: onSubmitCallBack,
          button1Text: button1Text,
          button2Text: button2Text,
        )).show();
  }
}
