import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../common/style/app_color_helper.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../common/style/style_res.dart';
import '../../../iconfont/icon_font.dart';
import 'package:haloui/utils/color_util.dart';

import '../../application.dart';
import '../../common/style/app_colors.dart';
import 'halo_pos_alert_dialog.dart';

///
///@ClassName: base_stateful
///@Description: 基础类
///@Author: tanglan
abstract class BaseStatefulPage extends StatefulWidget {
  final double rightFlex; //右组件的占比
  final double appBarHeight; //appbar高度
  final double titleTextSize; //标题字号
  final Color appBarColor; //appbar背景色
  final bool showAppBar;
  final bool showEndDrawer;

  const BaseStatefulPage({
    Key? key,
    this.rightFlex = 0,
    this.appBarHeight = 80,
    this.showAppBar = true,
    this.showEndDrawer = false,
    this.appBarColor = Colors.grey,
    this.titleTextSize = 28,
  }) : super(key: key);

  @override
  BaseStatefulPageState createState();
}

abstract class BaseStatefulPageState<T extends BaseStatefulPage>
    extends State<T>
    with AutomaticKeepAliveClientMixin {
  bool isPageLoading = false;
  bool isErrorPage = false;
  bool isShowErrorDetail = false;
  String? errorMsg;
  String? errorDetailsMsg;

  //初始化状态
  Future<void>? onInitState();

  ///返回按钮监听
  Future<bool> backWillPop() {
    return _backWillPop();
  }

  Future<bool> _backWillPop() {
    return Future.value(true); //true 返回/ false 取消返回
  }

  @override
  void initState() {
    super.initState();
    _loadChildInitState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        Visibility(visible: isPageLoading, child: _buildFullPage(Container())),
        //这里使用builder避免找不到scaffold
        _buildFullPage(Builder(builder: (context) => _buildBody(context))),
      ],
    );
  }

  //region 对外方法

  //标题
  String getActionBarTitle();

  List<Widget>? appBarActions(BuildContext context) {
    return null;
  }

  Widget buildLeftBody(BuildContext context);

  Widget buildRightBody(BuildContext context) {
    return Container();
  }

  Widget buildTopBody(BuildContext context) {
    return Container();
  }

  Widget buildBottomBody(BuildContext context) {
    return Container();
  }

  @override
  bool get wantKeepAlive => true;

  //endregion

  //region 私有内部方法

  _loadChildInitState() {
    //初始化状态
    _resetInitState();
    Future<void>? childState = onInitState();
    if (childState == null) {
      setState(() {
        isPageLoading = false;
      });
      return;
    }
    childState
        .whenComplete(() {
          setState(() {
            isPageLoading = false;
          });
        })
        .catchError((e, stack) {
          _causeException(e, stackTrace: stack);
          if (Application.isDebug) {
            throw e;
          } else {
            //TODO bugly上传
            // BuglyHelper.flutterError(e.toString() + stack.toString());
          }
        });
  }

  _resetInitState() {
    setState(() {
      errorMsg = "";
      errorDetailsMsg = "";
      isErrorPage = false;
      isPageLoading = true;
      isShowErrorDetail = false;
    });
  }

  _buildFullPage(Widget body) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        if (await backWillPop()) {
          if (mounted) {
            HaloPosAlertDialog.showAlertDialog(
              context,
              content: "请确认是否退出，再次点击退出",
              onSubmitCallBack: () {
                SystemNavigator.pop();
              },
            );
          }

        } else {
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF1F3F3),
        //有tabBar的页面通过控制buildAppBarBottom的preferredSize为0控制不显示titleBar
        drawerScrimColor: Colors.transparent,
        endDrawerEnableOpenDragGesture: false,
        appBar: widget.showAppBar ? buildAppBar() : null,
        endDrawer: widget.showEndDrawer ? buildEndDrawer() : null,
        resizeToAvoidBottomInset: resizeToAvoidBottomPadding(),
        body: body,
      ),
    );
  }

  ///软键盘是否改变布局
  bool resizeToAvoidBottomPadding() {
    return false;
  }

  buildEndDrawer() {
    return Container();
  }

  buildAppBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(80.h),
      child: AppBar(
        backgroundColor: AppColorHelper(context).getAppBarColor(),
        titleSpacing: 0.0,
        automaticallyImplyLeading: false,
        toolbarHeight: 80.h,
        title: SizedBox(
          height: 80.h,
          child: Row(
            children: [
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => NavigateUtil.pop(context),
                child: Container(
                  padding: EdgeInsets.zero,
                  width: 80.w,
                  height: double.infinity,
                  alignment: Alignment.center,
                  child: IconFont(
                    IconNames.ngp_left_back,
                    size: 32.w,
                    color: ColorUtil.color2String(
                      AppColorHelper(context).getTitleBoldTextColor(),
                    ),
                  ),
                ),
              ),
              Text(
                getActionBarTitle() ?? "",
                style: TextStyle(
                  color: AppColors.normalTextColor,
                  fontSize: widget.titleTextSize.sp,
                ),
              ),
            ],
          ),
        ),
        centerTitle: false,
        actions: appBarActions(context),
      ),
    );
  }

  _buildBody(BuildContext context) {
    if (isErrorPage) {
      return _buildErrorBody();
    }

    Widget bodyWidget = _buildFullBody(context);
    bodyWidget ??= const Text("NO Content");
    return GestureDetector(
      onTap: () {
        // 键盘回收
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: bodyWidget,
    );
  }

  Widget _buildFullBody(BuildContext context) {
    return Column(
      children: [
        buildTopBody(context),
        Expanded(
          child: Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: getLeftFlex(), child: buildLeftBody(context)),
              Expanded(flex: getRightFlex(), child: buildRightBody(context)),
            ],
          ),
        ),
        buildBottomBody(context),
      ],
    );
  }

  int getLeftFlex() {
    List<String> rightFlexList = widget.rightFlex.toString().split(".");
    if (widget.rightFlex == 0 || rightFlexList.length == 1) {
      return 1;
    }
    int leftFlex = 1;
    for (int i = 0; i < rightFlexList[1].length; i++) {
      leftFlex = leftFlex * 10;
    }
    return leftFlex;
  }

  int getRightFlex() {
    if (widget.rightFlex == 0) {
      return 0;
    }
    List<String> rightFlexList = widget.rightFlex.toString().split(".");
    if (rightFlexList.length == 1) {
      return widget.rightFlex.toInt();
    }

    double leftFlex = widget.rightFlex;
    for (int i = 0; i < rightFlexList[1].length; i++) {
      leftFlex = leftFlex * 10;
    }
    return leftFlex.toInt();
  }

  //构建加载错误界面
  Widget _buildErrorBody() {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(top: 140.w),
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            HaloImage(
              const AssetImage("assets/images/<EMAIL>"),
              width: 190.w,
              height: 135.w,
            ),
            Container(
              padding: EdgeInsets.only(top: 28.w, left: 20.w),
              child: Text(errorMsg ?? "", style: StyleRes.getErrorTextStyle()),
            ),
            Visibility(
              visible: StringUtil.isNotEmpty(errorDetailsMsg),
              child: HaloExpandedWidget(
                isExpand: isShowErrorDetail,
                expandWidget: [
                  Container(
                    padding: EdgeInsets.only(top: 28.w, left: 20.w),
                    child: Text(
                      errorDetailsMsg ?? "",
                      style: StyleRes.getErrorTextStyle(),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 74.w, bottom: 60.w),
              width: 270.w,
              child: HaloButton(
                text: "刷新",
                onPressed: () => _loadChildInitState(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  //处理异常信息
  void _causeException(dynamic error, {StackTrace? stackTrace}) {
    if (error != null) {
      errorMsg =
          StringUtil.isEmpty(error.toString()) ? "请求失败" : error.toString();
      try {
        errorDetailsMsg = error.respone.message;
      } catch (e) {
        errorDetailsMsg = "";
      }
    } else {
      errorMsg = error == null ? "请求失败" : error.toString();
      errorDetailsMsg = stackTrace == null ? "" : stackTrace.toString();
    }

    errorMsg = errorMsg?.replaceFirst("Exception: ", "");
    isErrorPage = true;
    isPageLoading = false;
    setState(() {});
  }

  //endregion
}

///@Date: 8/18/21 1:24 PM
