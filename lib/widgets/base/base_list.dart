import 'package:flutter/material.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/base/base_stateful_page.dart';
import 'package:halo_utils/halo_utils.dart' hide DateUtil;
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/halo_empty.dart';
import 'package:haloui/widget/halo_textfield.dart';
import 'package:haloui/widget/list/halo_list.dart';

import '../../common/style/app_colors.dart';

enum LoadState {
  AllFinish, //所有数据加载完成
  LoadFail, //加载失败
  LoadFinish, //加载完成
  LoadMore //加载更多
}

abstract class BaseListPage extends BaseStatefulPage {
  final bool isShowfilter; //是否展示筛选
  final bool isPullUpRefresh; //是否支持上拉
  final bool isPullDownRefresh; //是否支持下拉
  final Color itemBgColor; //item的背景色
  final Color bgColor; //列表背景色
  final EdgeInsetsGeometry? padding;
  final bool scrollWidthKeyboard; //是否跟随键盘滚动

  const BaseListPage(
      {Key? key,
      this.isShowfilter = false,
      this.isPullUpRefresh = true,
      this.isPullDownRefresh = true,
      this.itemBgColor = Colors.white,
      this.bgColor = AppColors.pageBackgroundColor,
      this.padding,
      bool showAppBar = false,
      double rightFlex = 0,
      this.scrollWidthKeyboard = false})
      : super(key: key, rightFlex: rightFlex, showAppBar: showAppBar);
}

abstract class BaseListPageState<T extends BaseListPage>
    extends BaseStatefulPageState<T> {
  int pageIndex = 1;
  final int pageSize = 20;
  bool isHasMoreData = true;
  Map<String, dynamic> filterParams = {
    "endTime": DateUtil.getDateStrByDateTime(DateTime(DateTime.now().year,
        DateTime.now().month, DateTime.now().day, 23, 59, 59))!,
    "starTime": DateUtil.getDateStrByDateTime(DateTime(
        DateTime.now().add(const Duration(days: -30)).year,
        DateTime.now().add(const Duration(days: -30)).month,
        DateTime.now().add(const Duration(days: -30)).day,
        00,
        00,
        00))!,
  }; //查询过滤参数
  List<dynamic> dataSource = []; //列表数据源
  ScrollController scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();
  bool isBack = false;

  ///单据编号textC
  TextEditingController textBillNumberController = TextEditingController();

  @override
  Future<void> onInitState() {
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus && !isBack) {
        ///失去焦点 进行筛选
        // filterValue();
      }
    });

    return onLoadData();
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      color: widget.bgColor,
      mainAxisSize: MainAxisSize.max,
      children: [
        buildTopWidget(),
        _buildFilter(context),
        Expanded(child: _buildContentList())
      ],
    );
  }

  ///顶部组件
  Widget buildTopWidget() {
    return Container(
      color: Colors.white,
    );
  }

  ///构建筛选组建
  Widget _buildFilter(BuildContext context) {
    return Visibility(
      visible: widget.isShowfilter,
      child: Column(
        children: [
          HaloContainer(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            color: AppColors.pageBackgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(6.w)),
            children: [
              IconFont(IconNames.ngp_search,
                  color: ColorUtil.color2String(AppColors.tipsFontColor)),
              SizedBox(
                width: ScreenUtil().setWidth(13),
              ),
              Expanded(
                  child: HaloTextField(
                controller: textBillNumberController,
                textInputAction: TextInputAction.done,
                contentPadding: 0,
                backgroundColor: Colors.transparent,
                focusNode: _focusNode,
                hintText: "输入单据编号",
                textColor: AppColors.normalFontColor,
                hintStyle: TextStyle(
                    color: AppColors.tipsFontColor,
                    fontSize: AppPosSize.totalFontSize.sp),
                fontSize: AppPosSize.totalFontSize.sp,
                onSubmitted: (newV) {
                  filterValue();
                },
              ))
            ],
          ),
        ],
      ),
    );
  }

  ///构建ListView
  _buildContentList() {
    if (dataSource.isEmpty) {
      return HaloEmptyContainer(
        gravity: EmptyGravity.CENTER,
        image: Image.asset('assets/images/nodata.png'),
        title: "暂无数据",
        centerGravityPadding: EdgeInsets.only(bottom: 10.w),
        titleStyle: TextStyle(
            decoration: TextDecoration.none,
            fontSize: ScreenUtil().setSp(25),
            color: Colors.grey),
      );
    }
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        /// 页面点击响应
        _closeKeyboard(context);
      },
      onPanDown: (_) {
        /// 列表滑动响应
        _closeKeyboard(context);
      },
      child: HaloList(
        dataSource,
        scrollController: this.scrollController,
        hasMoreData: this.isHasMoreData,
        scrollWidthKeyboard: widget.scrollWidthKeyboard,
        buildItemContent: (context, index) {
          return GestureDetector(
            onTap: () {
              onItemClick(index, dataSource[index]);
            },
            child: buildItemView(index, dataSource[index]),
          );
        },
        onRefresh: widget.isPullDownRefresh
            ? () {
                pageIndex = 1;
                isHasMoreData = true;
                return onLoadData();
              }
            : null,
        onLoadMore: widget.isPullUpRefresh
            ? () async {
                return onLoadMore().then((value) {
                  if (value == LoadState.AllFinish) {
                    isHasMoreData = false;
                  }
                });
              }
            : null,
      ),
    );
  }

  ///筛选条件回调方法
  filterValue() {
    filterParams["billNumber"] = textBillNumberController.text;
    pageIndex = 1;
    onLoadData();
  }

  ///点击回调方法
  onItemClick(int index, dynamic item);

  ///行内容回调方法
  Widget buildItemView(int index, dynamic item);

  ///网络请求
  Future<List<dynamic>> onRequestData();

  ///网络请求
  Future<LoadState> onLoadData() async {
    return onRequestData().then((result) {
      if (null == result) {
        return LoadState.LoadFail;
      }
      if (pageIndex <= 1) {
        dataSource.clear();
      }
      setState(() {
        dataSource.addAll(result);
      });
      if (result.length < pageSize) {
        setState(() {
          isHasMoreData = false;
        });
        return LoadState.AllFinish;
      }
      setState(() {
        isHasMoreData = true;
      });
      return LoadState.LoadFinish;
    }).catchError((msg, stack) {
      return LoadState.LoadFinish;
    });
  }

  ///加载更多
  Future<LoadState> onLoadMore() async {
    pageIndex++;
    List<dynamic> result = await onRequestData();
    if (null == result) {
      return LoadState.LoadFail;
    }
    if (pageIndex <= 1) {
      dataSource.clear();
    }
    setState(() {
      dataSource.addAll(result);
    });

    if (result.length < pageSize) {
      setState(() {
        isHasMoreData = false;
      });
      return LoadState.AllFinish;
    }
    return LoadState.LoadFinish;
  }

  void _closeKeyboard(BuildContext context) {
    FocusScopeNode currentFocus = FocusScope.of(context);

    /// 键盘是否是弹起状态
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus!.unfocus();
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(() {});
    super.dispose();
  }
}
