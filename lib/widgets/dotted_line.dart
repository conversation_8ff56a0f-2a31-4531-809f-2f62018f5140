import 'package:flutter/material.dart';
import 'package:path_drawing/path_drawing.dart';

///虚线控件
class DottedLine extends StatelessWidget {
  ///虚线宽度
  final double strokeWidth;

  ///虚线颜色
  final Color color;

  ///虚线参数，例如[8,4],则意味着一小截实线长度为8，然后紧跟着长度为4的空白。
  final List<double> dashPattern;

  ///虚线方向
  final Axis axis;

  const DottedLine(
      {Key? key,
      this.strokeWidth = 1,
      this.color = Colors.black87,
      this.dashPattern = const <double>[3, 1],
      this.axis = Axis.horizontal})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: axis == Axis.horizontal ? double.infinity : strokeWidth,
      height: axis == Axis.vertical ? double.infinity : strokeWidth,
      child: CustomPaint(
        painter: DottedLinePainter(
            strokeWidth: strokeWidth,
            color: color,
            dashPattern: dashPattern,
            axis: axis),
      ),
    );
  }
}

///虚线painter，用来绘制
class DottedLinePainter extends CustomPainter {
  ///虚线宽度
  final double strokeWidth;

  ///虚线颜色
  final Color color;

  ///虚线参数，例如[8,4],则意味着一小截实线长度为8，然后紧跟着长度为4的空白。
  final List<double> dashPattern;

  ///虚线方向
  final Axis axis;

  DottedLinePainter(
      {required this.strokeWidth,
      required this.color,
      required this.dashPattern,
      required this.axis});

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..strokeWidth = strokeWidth
      ..color = color
      ..style = PaintingStyle.stroke;
    Path path = Path();
    path.moveTo(0, 0);
    if (axis == Axis.horizontal) {
      path.lineTo(size.width, 0);
    } else {
      path.lineTo(0, size.height);
    }
    canvas.drawPath(
        dashPath(path, dashArray: CircularIntervalList(dashPattern)), paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
