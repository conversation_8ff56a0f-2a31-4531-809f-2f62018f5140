import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import "package:flutter/foundation.dart" as WidgetKey show Key;
import '../../../common/tool/text_input_util.dart';

typedef CustomCallback = void Function(Key key, String content);

///带键盘的价格界面
class PriceKeyBoard extends StatefulWidget {
  ///按键二维列表
  final List<List<Key>> keyList;

  ///水平方向上的分割线
  final Widget dividerHorizontal;

  ///竖直方向上分割线
  final Widget dividerVertical;

  ///按键的高度
  final double keyHeight;

  ///数字变动的回调
  final ValueChanged<String>? numberChangeCallback;

  ///自定义按键的点击回调
  final CustomCallback? customCallback;

  ///是否允许为空，若不允许，则回调的返回值最小为0
  final bool allowEmpty;

  ///是否是电话号码
  final bool isPhone;

  ///最大值，小于0时无效
  final num max;
  final int scale;

  final TextEditingController controller;

  ///构造函数
  ///其中[key]为必传，通过此key，父控件可以拿到state，进而拿到输入的金额
  PriceKeyBoard(
      {WidgetKey.Key? key,
      this.keyList = const [
        [Key.one, Key.two, Key.three],
        [Key.four, Key.five, Key.six],
        [Key.seven, Key.eight, Key.nine],
        [Key.back, Key.dot, Key.zero]
      ],
      Widget? dividerHorizontal,
      Widget? dividerVertical,
      double? keyHeight,
      this.max = -1,
      this.scale = 2,
      this.allowEmpty = false,
      this.isPhone = false,
      required this.controller,
      this.numberChangeCallback,
      this.customCallback})
      : dividerVertical = dividerVertical ??
            VerticalDivider(width: 10.w, color: Colors.white),
        dividerHorizontal =
            dividerHorizontal ?? Divider(height: 10.h, color: Colors.white),
        keyHeight = keyHeight ?? 74.h,
        super(key: key);

  @override
  State<StatefulWidget> createState() => PriceKeyBoardWidgetState();
}

class PriceKeyBoardWidgetState extends State<PriceKeyBoard> {
  ///金额字符串
  // String priceStr = "";

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    for (int i = 0; i < widget.keyList.length; i++) {
      children.add(buildRow(context, widget.keyList[i]));
      //添加分割线
      if (i != widget.keyList.length - 1) {
        children.add(widget.dividerHorizontal);
      }
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  ///构建一行
  Widget buildRow(BuildContext context, List<Key> keyList) {
    List<Widget> children = [];
    for (int i = 0; i < keyList.length; i++) {
      children.add(Expanded(child: buildKey(context, keyList[i])));
      //添加分割线
      if (i != keyList.length - 1) {
        children.add(widget.dividerVertical);
      }
    }
    return SizedBox(
      height: widget.keyHeight,
      child: Row(
        children: children,
      ),
    );
  }

  ///构建按键
  Widget buildKey(BuildContext context, Key key) {
    final isBack = key.type == KeyType.back;
    final isClear = key.type == KeyType.clear;
    final isCustom = key.type == KeyType.custom;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (isClear) {
          _onClearPressed();
        } else if (isBack) {
          _onBackPressed();
        } else if (isCustom) {
          widget.customCallback?.call(key, widget.controller.text);
        } else {
          _onNumPressed(key);
        }
      },
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: key.background ?? const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(4.w)),
        child: isBack
            ? Icon(
                Icons.backspace_outlined,
                color: const Color(0xff333333),
                size: 40.w,
              )
            : Text(key.value,
                style: TextStyle(
                    color: key.textColor ?? const Color(0xff333333),
                    fontWeight: FontWeight.bold,
                    fontSize: 30.sp)),
      ),
    );
  }

  ///点击了清空
  void _onClearPressed() {
    clean(widget.controller);
    _checkPriceEmpty();
    widget.numberChangeCallback?.call(widget.controller.text);
  }

  ///点击了回退
  void _onBackPressed() {
    delete(widget.controller);
    _checkPriceEmpty();
    widget.numberChangeCallback?.call(widget.controller.text);
  }

  ///检查输入的值是否为空
  void _checkPriceEmpty() {
    if (widget.allowEmpty) {
      return;
    }
    //这里先要求，若字符串为空，则展示一个0
    if (widget.controller.text.isEmpty) {
      insert(widget.controller, "0");
    }
  }

  ///点击了数字和小数点
  void _onNumPressed(Key key) {
    String text = widget.controller.text;
    //点击了小数点，需要先判断是否已经包含小数点
    if (key.type == KeyType.dot) {
      //已经包含小数点，不响应
      if (text.contains(".")) {
        return;
      }
    }
    //按照光标位置插入
    TextSelection selection = widget.controller.selection;
    int start = selection.start;
    int end = selection.end;

    text = text.replaceRange(start, end, key.value);
    start = start + key.value.length;

    if (widget.isPhone != true) {
      Characters characters;
      //排除字符串第一位为0，第二位不为小数点的情况
      while ((characters = text.characters).length > 1 &&
          characters.characterAt(0).string == "0" &&
          characters.characterAt(1).string != ".") {
        start--;
        text = text.substring(1);
      }
    }
    if (widget.max >= 0 && (num.tryParse(text) ?? 0) > widget.max) {
      text = widget.max.toString();
      start = text.length;
    }
    if (_checkNumberLength(text)) {
      widget.controller.value = TextEditingValue(
          text: text, selection: TextSelection.collapsed(offset: start));
      widget.numberChangeCallback?.call(text);
    }
  }

  ///规定整数部分最大长度为12位，小数部分读取配置
  bool _checkNumberLength(String text) {
    final split = text.split(".");
    var integer = split[0];
    if (integer.length > 12) {
      return false;
    }
    if (split.length >= 2) {
      //小数位数
      var float = split[1];
      if (float.length > widget.scale) {
        return false;
      }
    }
    return true;
  }
}

///按键
class Key {
  static const zero = Key("0");
  static const one = Key("1");
  static const two = Key("2");
  static const three = Key("3");
  static const four = Key("4");
  static const five = Key("5");
  static const six = Key("6");
  static const seven = Key("7");
  static const eight = Key("8");
  static const nine = Key("9");
  static const doubleZero = Key("00");
  static const dot = Key(".", type: KeyType.dot);
  static const clear = Key("清空", type: KeyType.clear);
  static const back = Key("", type: KeyType.back);

  ///文字内容
  final String value;

  ///按键类型
  final KeyType type;

  ///文字颜色
  final Color? textColor;

  ///背景颜色
  final Color? background;

  const Key(this.value,
      {this.type = KeyType.number, this.textColor, this.background});
}

enum KeyType {
  ///数字
  number,

  ///.
  dot,

  ///回退
  back,

  ///清空
  clear,

  ///自定义按键
  custom
}
