import 'package:flutter/material.dart';
import '../../../common/standard.dart';
import '../../../iconfont/icon_font.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

///选择会员等级弹窗
class SingleSelectDialog<T> extends StatelessWidget {
  final EdgeInsets dialogPadding = EdgeInsets.only(left: 32.w, right: 30.w);

  final divider = Divider(height: 2.h, color: const Color(0xFFD0D0D0));

  ///列表
  final List<T> list;

  ///标题
  final String title;

  ///获取内容的方法
  final ResultFunction<T, String> contentGetter;

  SingleSelectDialog(
      {Key? key,
      required this.title,
      required this.list,
      required this.contentGetter})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 696.w,
        height: 720.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6.w),
        ),
        child: Column(
          children: [
            buildTitle(context),
            Expanded(
              child: ListView.separated(
                  itemBuilder: buildItem,
                  separatorBuilder: (context, index) => divider,
                  itemCount: list.length),
            )
          ],
        ),
      ),
    );
  }

  Widget buildItem(BuildContext context, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => Navigator.pop(context, list[index]),
      child: Container(
        padding: dialogPadding.copyWith(top: 20.h, bottom: 20.h),
        child: Text(
          contentGetter(list[index]),
          style: TextStyle(
              color: const Color(0xFF333333),
              fontSize: 22.sp,
              overflow: TextOverflow.ellipsis),
          maxLines: 1,
        ),
      ),
    );
  }

  ///标题栏和关闭按钮
  Widget buildTitle(BuildContext context) {
    return Column(children: [
      HaloContainer(
          height: 72.h,
          padding: dialogPadding,
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title,
                style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF333333),
                    fontSize: 26.sp)),
            GestureDetector(
                child: IconFont(IconNames.close, size: 20.w),
                onTap: () => Navigator.pop(context))
          ]),
      //分割线
      divider
    ]);
  }
}
