import 'package:flutter/material.dart';
import 'package:halo_pos/bill/settlement/entity/score_configuration.dart';
import 'package:halo_pos/login/tool/login_util.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/haloui.dart';

import '../bill/entity/bill_promotion_info_dto.dart';
import '../bill/model/bill_model.dart';
import '../bill/model/promotion_model.dart';
import '../bill/tool/promotion/promotion.dart';
import '../common/ptype_sync.dart';
import '../common/string_res.dart';
import '../common/style/app_colors.dart';
import '../common/tool/performance_capture_util.dart';
import '../common/tool/sp_tool.dart';
import '../entity/print/print_config_info.dart';
import '../entity/system/permission_config.dart';
import '../enum/sync_info_type.dart';
import '../login/entity/store/store_info.dart';
import '../login/model/store_model.dart';
import '../login/tool/shop_config_model.dart';

///
///@ClassName: info_sync_dialog
///@Description:
///@Author: tanglan
///@Date: 2023/12/21
class InfoSyncDialog extends StatefulWidget {
  final List<SyncInfoType> sycInfoList;
  final Function()? afterSync;
  final bool isAutoClose;
  final BuildContext context;

  const InfoSyncDialog(
    this.context, {
    Key? key,
    required this.sycInfoList,
    this.afterSync,
    this.isAutoClose = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _InfoSyncDialogState();
  }
}

class _InfoSyncDialogState extends State<InfoSyncDialog> {
  double _progressValue = 0.0;
  String progressMsg = "";
  final List<SyncInfoType> _syncFailInfo = [];
  late StateSetter _innerState;
  bool _isNeedRepeat = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await startSyncInfo(widget.sycInfoList);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: HaloContainer(
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          color: AppColors.cardColor,
          width: 900.w,
          height: 800.h,
          direction: Axis.vertical,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              child: Text(
                StringRes.messageSync.getText(context),
                style: TextStyle(
                  color: AppColors.normalTextColor,
                  fontSize: 24.sp,
                  decoration: TextDecoration.none,
                ),
              ),
            ),
            Expanded(
              child: StatefulBuilder(
                builder: (context, setInnerState) {
                  _innerState = setInnerState;
                  return HaloContainer(
                    direction: Axis.vertical,
                    mainAxisSize: MainAxisSize.max,
                    padding: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 12.w,
                    ),
                    crossAxisAlignment: CrossAxisAlignment.start,
                    border: Border(
                      top: BorderSide(
                        color: AppColors.dividerColor,
                        width: 2.w,
                      ),
                    ),
                    children: [
                      LinearProgressIndicator(
                        value: _progressValue,
                        minHeight: 20.h,
                        color: Colors.blue,
                        backgroundColor: Colors.grey,
                      ),
                      Expanded(
                        child: HaloContainer(
                          width: 900.w,
                          direction: Axis.vertical,
                          margin: EdgeInsets.only(top: 24.h, bottom: 24.h),
                          padding: EdgeInsets.all(12.w),
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.max,
                          border: Border.all(
                            color: AppColors.borderColor,
                            width: 1,
                          ),
                          children: [
                            Expanded(
                              child: SingleChildScrollView(
                                child: Container(
                                  alignment: Alignment.topLeft,
                                  child: Text(
                                    progressMsg,
                                    style: TextStyle(
                                      color: AppColors.normalTextColor,
                                      fontSize: 18.sp,
                                    ),
                                    softWrap: true,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      HaloContainer(
                        margin: EdgeInsets.only(bottom: 24.h),
                        mainAxisSize: MainAxisSize.max,
                        visible:
                            _syncFailInfo.isNotEmpty || !widget.isAutoClose,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          HaloButton(
                            height: 50.h,
                            visible:
                                _isNeedRepeat ||
                                (_syncFailInfo.isNotEmpty &&
                                    !widget.isAutoClose),
                            text: "重新同步失败数据",
                            onPressed: () {
                              startSyncInfo(_syncFailInfo);
                            },
                          ),
                          HaloContainer(
                            visible: !_isNeedRepeat && widget.isAutoClose,
                            margin: EdgeInsets.only(left: 48.w),
                            children: [
                              HaloButton(
                                height: 50.h,
                                text: "后续手动同步",
                                onPressed: () {
                                  closeDialog();
                                },
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.only(left: 48.w),
                            child: HaloButton(
                              buttonType: HaloButtonType.outlinedButton,
                              borderColor: AppColors.btnBorderColor,
                              outLineWidth: 1,
                              backgroundColor: Colors.white,
                              textColor: AppColors.secondTextColor,
                              height: 50.h,
                              text: "返回登录",
                              onPressed: () {
                                SpTool.saveAutoLogin(false);
                                LoginUtil.loginOut(context);
                              },
                            ),
                          ),
                          HaloContainer(
                            visible: !widget.isAutoClose,
                            margin: EdgeInsets.only(left: 48.w),
                            children: [
                              HaloButton(
                                height: 50.h,
                                text: "关闭",
                                onPressed: () {
                                  closeDialog();
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  startSyncInfo(List<SyncInfoType> syncList) async {
    for (int i = 0; i < syncList.length; i++) {
      switch (syncList[i]) {
        case SyncInfoType.pType:
          await doRefreshPTypeData();
          break;
        case SyncInfoType.storeInfo:
          await doSyncStoreInfo(SyncInfoType.storeInfo);
          break;
        case SyncInfoType.promotion:
          await doSyncPromotion(SyncInfoType.promotion);
          break;
        case SyncInfoType.permissionConfig:
          await doSyncPermissionAndMenu(SyncInfoType.permissionConfig);
          break;
        case SyncInfoType.scoreConfiguration:
          await doSyncScoreConfiguration();
          break;
        case SyncInfoType.printConfiguration:
          await doSyncPrintInfo();
          break;
        default:
          break;
      }
    }
    if (_syncFailInfo.isNotEmpty) {
      _innerState(() {
        _isNeedRepeat =
            _syncFailInfo.contains(SyncInfoType.permissionConfig) ||
            _syncFailInfo.contains(SyncInfoType.promotion) ||
            _syncFailInfo.contains(SyncInfoType.storeInfo);
      });
    }
    if (mounted) {
      Future.delayed(const Duration(seconds: 3), () {
        if (widget.isAutoClose && _syncFailInfo.isEmpty) {
          closeDialog();
        }
      });
    }
  }

  closeDialog() {
    NavigateUtil.pop(context);
    if (null != widget.afterSync) {
      widget.afterSync!();
    }
  }

  ///同步商品信息
  Future<void> doRefreshPTypeData() async {
    String baseMessage = progressMsg;
    PerformanceCaptureUtil.start(PerformanceTimeName.pTypeSync);
    try {
      await PtypeSyncUtil.startSync(context, (progress, message) {
        if (mounted) {
          _innerState(() {
            _progressValue = progress;
            progressMsg = baseMessage + message;
          });
        }
        if (_syncFailInfo.contains(SyncInfoType.pType)) {
          _syncFailInfo.remove(SyncInfoType.pType);
        }
      });
      PerformanceCaptureUtil.end(PerformanceTimeName.pTypeSync);
    } catch (e) {
      PerformanceCaptureUtil.end(PerformanceTimeName.pTypeSync);
      if (mounted) {
        _innerState(() {
          _progressValue = 0;
          if (!_syncFailInfo.contains(SyncInfoType.pType)) {
            _syncFailInfo.add(SyncInfoType.pType);
          }
          progressMsg += "商品信息同步失败 \n";
        });
      }
      rethrow;
    }
  }

  void failSyncStore(String tipMsg) {
    SpTool.saveAutoLogin(false);
    NavigateUtil.pop(context);
    LoginUtil.tipsLoginOut(widget.context, tipMsg);
  }

  ///同步门店信息
  doSyncStoreInfo(SyncInfoType type) async {
    StoreInfo storeInfo = SpTool.getStoreInfo()!;
    PerformanceCaptureUtil.start(PerformanceTimeName.storeSync);
    await doSyncInfo<StoreInfo?>(
      type,
      postRequest: () async {
        ResponseModel response = await StoreModel.getStoreInfoById(
          context,
          storeInfo.otypeId,
          isLoading: false,
          errorCallBack: (error) {
            if (error.data["message"]?.contains("删除") ?? false) {
              failSyncStore("当前门店已被删除，请重新登录");
            }
          },
        );
        if (response.code != 200) {
          return null;
        }

        StoreInfo storeFullInfo = StoreInfo.fromMap(response.data);
        if (storeFullInfo.stoped ?? false) {
          failSyncStore("当前门店已被停用，请重新登录");

          return null;
        }
        if (!(storeFullInfo.allowPosLogin ?? false)) {
          failSyncStore("当前门店非登录门店，请重新登录");

          return null;
        }
        return storeFullInfo;
      },
      afterRequest: (StoreInfo? data) async {
        if (null == data) {
          return;
        }
        SpTool.saveStoreInfo(data);
      },
    );
    PerformanceCaptureUtil.end(PerformanceTimeName.storeSync);
  }

  ///
  ////同步打印信配置息
  Future<void> doSyncPrintInfo() async {
    await doSyncInfo<List<PrintConfigInfo>?>(
      SyncInfoType.printConfiguration,
      //打印配置允许为空
      showErrorWhenNull: false,
      postRequest: () async {
        return await StoreModel.getPrintConfigList(context, isLoading: false);
      },
      afterRequest: (List<PrintConfigInfo>? data) async {
        SpTool.savePrintFieldInfo(data ?? []);
      },
    );
  }

  ///同步积分策略
  Future<void> doSyncScoreConfiguration() async {
    await doSyncInfo<ScoreConfiguration?>(
      SyncInfoType.scoreConfiguration,
      //积分策略允许为空
      showErrorWhenNull: false,
      postRequest: () async {
        return await BillModel.getScoreConfiguration(
          context,
          showLoading: false,
        );
      },
      afterRequest: (ScoreConfiguration? data) async {
        SpTool.saveScoreConfiguration(data);
      },
    );
  }

  ///同步促销信息
  doSyncPromotion(SyncInfoType type) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.promotionSync);
    await doSyncInfo<List<BillPromotionInfoDto>?>(
      type,
      postRequest: () async {
        await BillModel.getPromotionAutomation(
          context,
        ).then((value) => autoChoosePromotionGift = value);
        if (context.mounted) {
          return await PromotionModel.getFullPromotionList(
            context,
            isLoading: false,
          );
        }
      },
      afterRequest: (List<BillPromotionInfoDto>? data) async {
        SpTool.savePromotionList(data);
      },
    );
    PerformanceCaptureUtil.end(PerformanceTimeName.promotionSync);
  }

  ///同步配置信息
  doSyncPermissionAndMenu(SyncInfoType type) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.configSync);
    await doSyncInfo<PermissionConfig?>(
      type,
      postRequest: () async {
        return await ShopConfigModel.getSysConfigAndPermission(
          context,
          isLoading: false,
        );
      },
      afterRequest: (PermissionConfig? permissionConfig) async {
        await SpTool.savePermission(permissionConfig?.permissions);
        await SpTool.saveQiNiuConfig(permissionConfig?.qiniuConfigDTO);
        await ShopConfigModel.saveAllSystemConfig(
          permissionConfig?.systemConfig,
        );
      },
    );
    PerformanceCaptureUtil.end(PerformanceTimeName.configSync);
  }

  ///执行同步信息
  doSyncInfo<T>(
    SyncInfoType syncInfoType, {
    required Future Function() postRequest,
    required Future Function(T data) afterRequest,
    bool showErrorWhenNull = true,
  }) async {
    _innerState(() {
      progressMsg += "开始同步${syncInfoMsgData[syncInfoType]}... \n";
      _progressValue = 0;
    });
    try {
      T data = await postRequest();
      if (null == data && showErrorWhenNull) {
        _innerState(() {
          if (!_syncFailInfo.contains(syncInfoType)) {
            _syncFailInfo.add(syncInfoType);
          }
          progressMsg += "${syncInfoMsgData[syncInfoType]}同步失败。 \n\n";
        });
        return;
      }
      await afterRequest(data);
      _innerState(() {
        _progressValue = 1;
        if (_syncFailInfo.contains(syncInfoType)) {
          _syncFailInfo.remove(syncInfoType);
        }
        progressMsg += "${syncInfoMsgData[syncInfoType]}同步完成。 \n\n";
        return;
      });
    } catch (e) {
      _innerState(() {
        if (!_syncFailInfo.contains(syncInfoType)) {
          _syncFailInfo.add(syncInfoType);
        }
        progressMsg += "${syncInfoMsgData[syncInfoType]}同步失败。$e \n\n";
      });
    }
  }

  ///执行同步信息
  doSyncListInfo(
    SyncInfoType syncInfoType, {
    required Future<Map<dynamic, dynamic>?> Function(
      int pageIndex,
      int pageSize,
    )
    postRequest,
    required Future Function(List list) afterRequest,
    int pageSize = 1000,
  }) async {
    String sourceMsg = "$progressMsg开始同步${syncInfoMsgData[syncInfoType]}.. \n";

    _innerState(
      () => _innerState(() {
        _progressValue = 0;
        progressMsg = "$sourceMsg${syncInfoMsgData[syncInfoType]}同步0% \n";
      }),
    );
    Map? data = await postRequest(1, pageSize);
    if (null == data || null == data["data"]) {
      _innerState(() {
        _progressValue = 1;
        if (!_syncFailInfo.contains(syncInfoType)) {
          _syncFailInfo.add(syncInfoType);
        }
        progressMsg += "${syncInfoMsgData[syncInfoType]}同步失败。 \n\n";
      });
      return;
    }
    int totalCount = int.parse(data["total"]);
    int pages = (totalCount / (pageSize)).ceil();
    await afterRequest(data["list"]);
    _innerState(() {
      _progressValue += 1 / pages;
      progressMsg =
          "$sourceMsg${syncInfoMsgData[syncInfoType]}同步${_progressValue * 100}% \n";
      if (_progressValue == 1) {
        if (_syncFailInfo.contains(syncInfoType)) {
          _syncFailInfo.remove(syncInfoType);
        }
        progressMsg += "${syncInfoMsgData[syncInfoType]}同步完成。 \n\n";
        return;
      }
    });

    for (int i = 2; i <= pages; i++) {
      data = await postRequest(i, pageSize);
      if (null == data || null == data["data"]) {
        _innerState(() {
          _progressValue = 1;
          if (!_syncFailInfo.contains(syncInfoType)) {
            _syncFailInfo.add(syncInfoType);
          }
          progressMsg += "${syncInfoMsgData[syncInfoType]}同步失败。 \n\n";
        });
        return;
      }
      await afterRequest(data["list"]);
      _innerState(() {
        _progressValue += 1 / pages;
        progressMsg =
            "$sourceMsg${syncInfoMsgData[syncInfoType]}同步${_progressValue * 100}% \n";
        if (_progressValue == 1) {
          if (_syncFailInfo.contains(syncInfoType)) {
            _syncFailInfo.remove(syncInfoType);
          }
          progressMsg += "${syncInfoMsgData[syncInfoType]}同步完成。 \n\n";
          return;
        }
      });
    }
  }
}
