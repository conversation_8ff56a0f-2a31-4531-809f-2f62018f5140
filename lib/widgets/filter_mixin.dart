import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart' show SizeExtension;
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/widget/datepicker/pduration.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_pop_window.dart';
import 'package:haloui/widget/halo_textfield.dart';
import 'package:haloui/widget/halo_toast.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';

import '../common/standard.dart';

import '../common/style/app_color_helper.dart';
import '../common/style/app_colors.dart';
import '../common/widget/datetime_filter.dart';
import '../iconfont/icon_font.dart';
import 'base/base_stateful_page.dart';
import 'halo_pos_label.dart';

mixin FilterMixin<T extends BaseStatefulPage>
    on BaseStatefulPageState<T>, DateTimeFilterMixin<T> {
  final DateTime initialTime = DateTime.now()
      .add(const Duration(days: 1))
      .let((tomorrow) => DateTime(tomorrow.year, tomorrow.month, tomorrow.day));

  ///输入框Controller
  final TextEditingController filterValueController = TextEditingController();

  ///搜索框前面标题
  final String searchTitle = "单据编号:";

  ///单据编号
  String filterValue = "";

  @override
  Future<void>? onInitState() async {
    //记录进入的当前时间 避免每次获取当前时间 导致限制最大最小时间不准确
  }

  ///搜索框
  Widget buildFilterValueQuery(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        left: 12.w,
        top: 14.h,
        bottom: 14.h,
      ),
      padding: EdgeInsets.only(
        left: 15.w,
        right: 15.w,
      ),
      height: 56.h,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFF6F6F6), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 3),
            child: HaloPosLabel(
              searchTitle,
              textAlign: TextAlign.center,
              textStyle: TextStyle(
                  fontSize: 24.sp,
                  color: AppColorHelper(context).getTitleBoldTextColor()),
            ),
          ),
          Expanded(
            child: HaloTextField(
              controller: filterValueController,
              fontSize: 24.sp,
              contentPadding: 0,
              maxLines: 1,
              onSubmitted: (_) => onSearchPressed(),
            ),
          )
        ],
      ),
    );
  }

  ///构建下拉弹窗筛选条件
  Widget buildPopupFilterWidget(
    BuildContext context, {
    required GlobalKey key,
    required String title,
    required String value,
    required List<String> source,
    required Function(String) onItemClick,
  }) {
    TextStyle style = TextStyle(
        fontSize: 24.sp,
        color: AppColorHelper(context).getTitleBoldTextColor());
    return Container(
      height: 60.w,
      color: Colors.white,
      margin: EdgeInsets.only(
        left: 12.w,
        top: 14.h,
        bottom: 14.h,
      ),
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(title, style: style),
          GestureDetector(
            onTap: () => HaloPopWindow().show(key,
                gravity: PopWindowGravity.bottom,
                backgroundColor: Colors.transparent,
                child: buildPopupList(
                    context: context,
                    width: 100.w,
                    source: source,
                    onItemClick: onItemClick)),
            child: Container(
              key: key,
              width: 100.w,
              height: 60.w,
              alignment: Alignment.center,
              child: HaloPosLabel(
                value,
                textAlign: TextAlign.center,
                textStyle: style,
              ),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget buildTopBody(BuildContext context) {
    return Container(
      color: const Color(0xFFF6F6F6),
      height: 84.w,
      child: Row(
        children: [
          buildDateTimeQuery(context),
          buildOtherFilter(context),
          Flexible(
            child: buildFilterValueQuery(context),
          ),
          Container(
            padding: EdgeInsets.only(left: 12.w, right: 17.w),
            height: 54.w,
            child: HaloButton(
              borderRadius: 5.sp,
              text: "查询",
              fontSize: 25.sp,
              backgroundColor: AppColors.accentColor,
              onPressed: onSearchPressed,
            ),
          ),
        ],
      ),
    );
  }

  onSearchPressed() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
    filterValue = filterValueController.text;
    onSearch();
  }

  // bool _checkStartTime(DateTime dateTime) {
  //   if (dateTime.millisecondsSinceEpoch -
  //           DateUtil.getDateTime(endTime)!.millisecondsSinceEpoch >
  //       0) {
  //     HaloToast.showError(context, msg: "开始时间不可以大于结束时间，请重新选择!");
  //     return false;
  //   }
  //   return true;
  // }
  //
  // bool _checkEndTime(DateTime dateTime) {
  //   if (dateTime.millisecondsSinceEpoch -
  //           DateUtil.getDateTime(startTime)!.millisecondsSinceEpoch <
  //       0) {
  //     HaloToast.showError(context, msg: "结束时间不可以小于开始时间，请重新选择!");
  //     return false;
  //   }
  //   return true;
  // }

  ///构建除了日期、单据编号的其他筛选条件
  Widget buildOtherFilter(BuildContext context);

  ///点击查询，发起请求
  void onSearch();

  ///构建筛选条件PopupWindow中的ListView
  Widget buildPopupList({
    required BuildContext context,
    required double width,
    required List<String> source,
    required ValueSetter<String> onItemClick,
  }) {
    return Container(
      width: width,
      decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: AppColors.dividerColor),
          borderRadius: BorderRadius.all(Radius.circular(8.w))),
      child: ListView.separated(
          itemCount: source.length,
          shrinkWrap: true,
          separatorBuilder: (context, index) =>
              Divider(height: 2.h, color: AppColors.dividerColor),
          itemBuilder: (buildContext, index) {
            String item = source[index];
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                onItemClick(item);
                HaloPopWindow().disMiss();
              },
              child: Container(
                height: 60.w,
                alignment: Alignment.center,
                child: Text(
                  item,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      fontSize: 24.sp,
                      color: AppColorHelper(context).getTitleBoldTextColor()),
                ),
              ),
            );
          }),
    );
  }
}
