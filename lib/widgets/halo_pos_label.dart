import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/haloui.dart';

///
///@ClassName: HaloPosLabel
///@Description: 类作用描述
///@Author: tanglan
///@Date: 9/16/21 2:13 PM

class HaloPosLabel extends HaloLabel {
  HaloPosLabel(String text,
      {TextAlign textAlign = TextAlign.start,
      TextStyle? textStyle,
      int maxLines = 1,
      TextDirection textDirection = TextDirection.ltr,
      TextOverflow overflow = TextOverflow.ellipsis,
      bool visible = true,
      bool showFullText = true,
      bool replace = false,
      String replaceChar = "*****"})
      : super(text,
            textAlign: textAlign,
            textStyle: textStyle,
            maxLines: maxLines,
            textDirection: textDirection,
            overflow: overflow,
            visible: visible,
            showFullText: showFullText,
            replace: replace,
            dialogMarginHorizontal: 600.w,
            dialogActionHeight: 100.w);
}
