import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/svg.dart';

enum IconNames {
  move, squarecheck, guolvxiala, square, ngp_right_back, ngp_left_back, close, ngp_search, jianpan, squarecheckfill, add, jiantou_1, wenhao_1, gengduo_2, shai<PERSON>uan, ji<PERSON><PERSON><PERSON><PERSON>, ji<PERSON><PERSON><PERSON>, xiaopiao_1, x<PERSON><PERSON><PERSON><PERSON><PERSON>, ji<PERSON><PERSON>, di<PERSON><PERSON><PERSON><PERSON><PERSON>, shang<PERSON><PERSON><PERSON><PERSON>, xito<PERSON><PERSON><PERSON><PERSON>, a_yijingtijiao_1, gouxuan, saoma, shuaxin, jianpan_1, duigou_1, taotaogu, chuzhi, morentouxiang, jifendixian, duigou, touxiang, saomiao, dayinshezhi, manjianzeng, tuige, zhi<PERSON><PERSON>, weixin, yin<PERSON><PERSON>, juhe, xianjin, shanchu_1, shanchu_2, banji<PERSON>, cu<PERSON><PERSON><PERSON><PERSON>, tejia, manjian, qingkong, guadan, cuxia<PERSON>, shou<PERSON><PERSON>, lins<PERSON><PERSON>, tux<PERSON><PERSON><PERSON><PERSON>, yanjing_kai, yanjing_guan, yanzhen<PERSON>, shou<PERSON><PERSON>_copy, mima, gong<PERSON><PERSON>, yong<PERSON><PERSON>, shezhi, guan, kai_1, danx<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, danxuankuangweixuanzhong, rili, xiaopiao, xiaoshoudingdan, shangchuan, fup<PERSON>hezhi, tongyongshezhi, bunengbianji, weixuanzhong, xuanzhong, zengpin, huiyuan, shanchu, caidan, youhuiquan, xia<PERSON>i, sousuo, dayinshangdan, zhuanshulaoshi, lianjie_copy, gengduo, zaixiankefu_1, yujing
}

extension parseString on IconNames {
  String serialize() => this.toString().split('.').last;
}

/// A class includes all icons which you provided from https://iconfont.cn
///
/// How to use it:
/// ```dart
/// IconFont(IconNames.xxx);
/// IconFont(IconNames.xxx, color: '#f00');
/// IconFont(IconNames.xxx, colors: ['#f00', 'blue']);
/// IconFont(IconNames.xxx, size: 30, color: '#000');
/// ```
///
/// The name is dynamic to against server interface.
/// Feel free to input string literal.
/// ```dart
/// IconFont('xxx');
/// ```
class IconFont extends StatelessWidget {
  IconNames? name;
  final String? color;
  final List<String>? colors;
  final double size;

  IconFont(dynamic iconName, { this.size = 15, this.color, this.colors }) {
    this.name = getIconNames(iconName);
  }

  static IconNames getIconNames(dynamic iconName) {
    switch (iconName) {
      case 'move':
        iconName = IconNames.move;
        break;
      case 'squarecheck':
        iconName = IconNames.squarecheck;
        break;
      case 'guolvxiala':
        iconName = IconNames.guolvxiala;
        break;
      case 'square':
        iconName = IconNames.square;
        break;
      case 'ngp_right_back':
        iconName = IconNames.ngp_right_back;
        break;
      case 'ngp_left_back':
        iconName = IconNames.ngp_left_back;
        break;
      case 'close':
        iconName = IconNames.close;
        break;
      case 'ngp_search':
        iconName = IconNames.ngp_search;
        break;
      case 'jianpan':
        iconName = IconNames.jianpan;
        break;
      case 'squarecheckfill':
        iconName = IconNames.squarecheckfill;
        break;
      case 'add':
        iconName = IconNames.add;
        break;
      case 'jiantou_1':
        iconName = IconNames.jiantou_1;
        break;
      case 'wenhao_1':
        iconName = IconNames.wenhao_1;
        break;
      case 'gengduo_2':
        iconName = IconNames.gengduo_2;
        break;
      case 'shaixuan':
        iconName = IconNames.shaixuan;
        break;
      case 'jifenbumanzu':
        iconName = IconNames.jifenbumanzu;
        break;
      case 'jiantoushang':
        iconName = IconNames.jiantoushang;
        break;
      case 'xiaopiao_1':
        iconName = IconNames.xiaopiao_1;
        break;
      case 'xuanzhonggou':
        iconName = IconNames.xuanzhonggou;
        break;
      case 'jiantou':
        iconName = IconNames.jiantou;
        break;
      case 'dianpuerweima':
        iconName = IconNames.dianpuerweima;
        break;
      case 'shangchuantupian':
        iconName = IconNames.shangchuantupian;
        break;
      case 'xitonggengxin':
        iconName = IconNames.xitonggengxin;
        break;
      case 'a_yijingtijiao_1':
        iconName = IconNames.a_yijingtijiao_1;
        break;
      case 'gouxuan':
        iconName = IconNames.gouxuan;
        break;
      case 'saoma':
        iconName = IconNames.saoma;
        break;
      case 'shuaxin':
        iconName = IconNames.shuaxin;
        break;
      case 'jianpan_1':
        iconName = IconNames.jianpan_1;
        break;
      case 'duigou_1':
        iconName = IconNames.duigou_1;
        break;
      case 'taotaogu':
        iconName = IconNames.taotaogu;
        break;
      case 'chuzhi':
        iconName = IconNames.chuzhi;
        break;
      case 'morentouxiang':
        iconName = IconNames.morentouxiang;
        break;
      case 'jifendixian':
        iconName = IconNames.jifendixian;
        break;
      case 'duigou':
        iconName = IconNames.duigou;
        break;
      case 'touxiang':
        iconName = IconNames.touxiang;
        break;
      case 'saomiao':
        iconName = IconNames.saomiao;
        break;
      case 'dayinshezhi':
        iconName = IconNames.dayinshezhi;
        break;
      case 'manjianzeng':
        iconName = IconNames.manjianzeng;
        break;
      case 'tuige':
        iconName = IconNames.tuige;
        break;
      case 'zhifubao':
        iconName = IconNames.zhifubao;
        break;
      case 'weixin':
        iconName = IconNames.weixin;
        break;
      case 'yinhangka':
        iconName = IconNames.yinhangka;
        break;
      case 'juhe':
        iconName = IconNames.juhe;
        break;
      case 'xianjin':
        iconName = IconNames.xianjin;
        break;
      case 'shanchu_1':
        iconName = IconNames.shanchu_1;
        break;
      case 'shanchu_2':
        iconName = IconNames.shanchu_2;
        break;
      case 'banjia':
        iconName = IconNames.banjia;
        break;
      case 'cuxiaojia':
        iconName = IconNames.cuxiaojia;
        break;
      case 'tejia':
        iconName = IconNames.tejia;
        break;
      case 'manjian':
        iconName = IconNames.manjian;
        break;
      case 'qingkong':
        iconName = IconNames.qingkong;
        break;
      case 'guadan':
        iconName = IconNames.guadan;
        break;
      case 'cuxiao':
        iconName = IconNames.cuxiao;
        break;
      case 'shouyingyuan':
        iconName = IconNames.shouyingyuan;
        break;
      case 'linshilogo':
        iconName = IconNames.linshilogo;
        break;
      case 'tuxingyanzhengma':
        iconName = IconNames.tuxingyanzhengma;
        break;
      case 'yanjing_kai':
        iconName = IconNames.yanjing_kai;
        break;
      case 'yanjing_guan':
        iconName = IconNames.yanjing_guan;
        break;
      case 'yanzhengma':
        iconName = IconNames.yanzhengma;
        break;
      case 'shoujihao_copy':
        iconName = IconNames.shoujihao_copy;
        break;
      case 'mima':
        iconName = IconNames.mima;
        break;
      case 'gongsiming':
        iconName = IconNames.gongsiming;
        break;
      case 'yonghuming':
        iconName = IconNames.yonghuming;
        break;
      case 'shezhi':
        iconName = IconNames.shezhi;
        break;
      case 'guan':
        iconName = IconNames.guan;
        break;
      case 'kai_1':
        iconName = IconNames.kai_1;
        break;
      case 'danxuankuangxuanzhong':
        iconName = IconNames.danxuankuangxuanzhong;
        break;
      case 'danxuankuangweixuanzhong':
        iconName = IconNames.danxuankuangweixuanzhong;
        break;
      case 'rili':
        iconName = IconNames.rili;
        break;
      case 'xiaopiao':
        iconName = IconNames.xiaopiao;
        break;
      case 'xiaoshoudingdan':
        iconName = IconNames.xiaoshoudingdan;
        break;
      case 'shangchuan':
        iconName = IconNames.shangchuan;
        break;
      case 'fupingshezhi':
        iconName = IconNames.fupingshezhi;
        break;
      case 'tongyongshezhi':
        iconName = IconNames.tongyongshezhi;
        break;
      case 'bunengbianji':
        iconName = IconNames.bunengbianji;
        break;
      case 'weixuanzhong':
        iconName = IconNames.weixuanzhong;
        break;
      case 'xuanzhong':
        iconName = IconNames.xuanzhong;
        break;
      case 'zengpin':
        iconName = IconNames.zengpin;
        break;
      case 'huiyuan':
        iconName = IconNames.huiyuan;
        break;
      case 'shanchu':
        iconName = IconNames.shanchu;
        break;
      case 'caidan':
        iconName = IconNames.caidan;
        break;
      case 'youhuiquan':
        iconName = IconNames.youhuiquan;
        break;
      case 'xiaoxi':
        iconName = IconNames.xiaoxi;
        break;
      case 'sousuo':
        iconName = IconNames.sousuo;
        break;
      case 'dayinshangdan':
        iconName = IconNames.dayinshangdan;
        break;
      case 'zhuanshulaoshi':
        iconName = IconNames.zhuanshulaoshi;
        break;
      case 'lianjie_copy':
        iconName = IconNames.lianjie_copy;
        break;
      case 'gengduo':
        iconName = IconNames.gengduo;
        break;
      case 'zaixiankefu_1':
        iconName = IconNames.zaixiankefu_1;
        break;
      case 'yujing':
        iconName = IconNames.yujing;
        break;

    }
    return iconName;
  }

  static String getColor(int arrayIndex, String? color, List<String>? colors, String defaultColor) {
    if (color != null && color.isNotEmpty) {
      return color;
    }

    if (colors != null && colors.isNotEmpty && colors.length > arrayIndex) {
      return colors.elementAt(arrayIndex);
    }

    return defaultColor;
  }

  @override
  Widget build(BuildContext context) {
    String svgXml;

    switch (this.name!) {
      case IconNames.move:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M141.21142578 479.64931898m32.35068102 0l676.8757864 0q32.35068102 0 32.35068102 32.35068102l0 0q0 32.35068102-32.35068102 32.35068102l-676.8757864 0q-32.35068102 0-32.35068102-32.35068102l0 0q0-32.35068102 32.35068102-32.35068102Z"
              fill="''' + getColor(0, color, colors, '#8a8a8a') + '''"
            />
            <path
              d="M544.35068102 141.21142578m0 32.35068102l0 676.8757864q0 32.35068102-32.35068102 32.35068102l0 0q-32.35068102 0-32.35068102-32.35068102l0-676.8757864q0-32.35068102 32.35068102-32.35068102l0 0q32.35068102 0 32.35068102 32.35068102Z"
              fill="''' + getColor(1, color, colors, '#8a8a8a') + '''"
              fill-opacity="0"
            />
          </svg>
        ''';
        break;
      case IconNames.squarecheck:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M827.75920252 90.125c57.17829748 0 103.52760755 46.34931006 103.52760754 103.52760755v634.10659497c0 57.17829748-46.34931006 103.52760755-103.52760754 103.52760754H193.65260755c-57.17829748 0-103.52760755-46.34931006-103.52760755-103.52760754V193.65260755C90.125 136.47431006 136.47431006 90.125 193.65260755 90.125h634.10659497z m0 62.11656436H193.65260755a41.41104319 41.41104319 0 0 0-41.39033753 40.11694821L152.24156436 193.65260755v634.10659497a41.41104319 41.41104319 0 0 0 40.11694822 41.39033751l1.29409497 0.02070567h634.10659497a41.41104319 41.41104319 0 0 0 41.39033751-40.11694822l0.02070567-1.29409496V193.65260755a41.41104319 41.41104319 0 0 0-40.11694822-41.39033753L827.75920252 152.24156436z m-97.74041446 221.17120368l0.83857377 0.80751571a31.0582826 31.0582826 0 0 1 0.80751573 43.08301373l-0.80751573 0.83857379-263.53987755 263.53987757a31.0582826 31.0582826 0 0 1-43.08301372 0.80751488l-0.83857298-0.80751488L287.96625745 546.25210871a31.0582826 31.0582826 0 0 1 43.08301372-44.72910242l0.8385738 0.80751489 113.46625745 113.46625827 241.58167189-241.5764957a31.0582826 31.0582826 0 0 1 43.08301375-0.80751571z"
              fill="''' + getColor(0, color, colors, '#000000') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.guolvxiala:
        svgXml = '''
          <svg viewBox="0 0 1826 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M846.183784 992.947892l-813.757117-836.358919C-3.108901 120.066306-2.306306 61.661405 34.21636 26.135063A92.252252 92.252252 0 0 1 98.543856 0h1627.514234c50.950919 0 92.252252 41.301333 92.252252 92.252252a92.252252 92.252252 0 0 1-26.135063 64.336721l-813.757117 836.358919c-35.526342 36.513441-93.931243 37.316036-130.444685 1.780468l-1.789693-1.780468z"
              fill="''' + getColor(0, color, colors, '#BCBCBC') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.square:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M827.75920252 90.125c57.17829748 0 103.52760755 46.34931006 103.52760754 103.52760755v634.10659497c0 57.17829748-46.34931006 103.52760755-103.52760754 103.52760754H193.65260755c-57.17829748 0-103.52760755-46.34931006-103.52760755-103.52760754V193.65260755C90.125 136.47431006 136.47431006 90.125 193.65260755 90.125h634.10659497z m0 62.11656436H193.65260755a41.41104319 41.41104319 0 0 0-41.39033753 40.11694821L152.24156436 193.65260755v634.10659497a41.41104319 41.41104319 0 0 0 40.11694822 41.39033751l1.29409497 0.02070567h634.10659497a41.41104319 41.41104319 0 0 0 41.39033751-40.11694822l0.02070567-1.29409496V193.65260755a41.41104319 41.41104319 0 0 0-40.11694822-41.39033753L827.75920252 152.24156436z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.ngp_right_back:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M227.80869594 903.05913031L618.86260906 512l-391.06434844-391.05913031L316.34782625 32.40173937 795.95130406 512 316.34782625 991.60347781z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.ngp_left_back:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M796.19130406 120.94086969L405.13739094 512l391.06434844 391.05913031L707.65217375 991.59826063 228.04869594 512 707.65217375 32.39652219z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.close:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M823.54930591 200.45069409a33.89239312 33.89239312 0 0 1 0 47.92905807L559.93427229 512l263.62546205 263.62024784a33.89239312 33.89239312 0 1 1-47.93427227 47.93427229L512 559.91862964l-263.61503362 263.63067627a33.89239312 33.89239312 0 1 1-47.93427229-47.93427229L464.06572771 512 200.44547987 248.37975216a33.89239312 33.89239312 0 1 1 47.93427229-47.93427229L512 464.07094192l263.62024784-263.62546205a33.89239312 33.89239312 0 0 1 47.93427229 0z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.ngp_search:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M482.787118 3.040492c250.454646 0 453.490215 203.035569 453.490215 453.490216 0 121.719467-47.954708 232.237949-126.004512 313.695836l0.073517 0.073518 172.662154 172.662153a44.635897 44.635897 0 0 1-62.038646 64.170667l-1.081764-1.045005-172.662154-172.662154a44.977231 44.977231 0 0 1-4.510851-5.256533c-73.596718 51.583344-163.2256 81.851733-259.927959 81.851733-250.454646 0-453.490215-203.035569-453.490215-453.490215S232.332472 3.040492 482.787118 3.040492z m0 89.271795c-201.150359 0-364.218421 163.068062-364.218421 364.218421 0 201.150359 163.068062 364.218421 364.218421 364.21842 201.150359 0 364.218421-163.068062 364.21842-364.21842 0-201.150359-163.068062-364.218421-364.21842-364.218421z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.jianpan:
        svgXml = '''
          <svg viewBox="0 0 1435 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1167.64403707 141.21142578c40.1542213 0 72.70364211 32.54942079 72.70364209 72.70364211v596.16986422c0 40.1542213-32.54942079 72.70364211-72.70364209 72.70364211H273.38924073c-40.1542213 0-72.70364211-32.54942079-72.70364211-72.70364211V213.91506789C200.68559862 173.76084658 233.23501942 141.21142578 273.38924073 141.21142578h894.25479634z m0 43.62218513H273.38924073a29.08145698 29.08145698 0 0 0-29.04510524 27.62738424L244.30778375 213.91506789v596.16986422a29.08145698 29.08145698 0 0 0 27.62738422 29.04510523L273.38924073 839.16638909h894.25479634a29.08145698 29.08145698 0 0 0 29.04510521-27.62738423L1196.72549405 810.08493211V213.91506789a29.08145698 29.08145698 0 0 0-27.62738424-29.04510523L1167.64403707 184.83361091z m-58.16291327 479.844037v43.62218584H331.55215396v-43.62218584h777.92896984zM440.60761749 475.64817931v109.0554628H331.55215396V475.64817931h109.05546352z m167.21837604 0v109.0554628H498.77053072V475.64817931h109.05546281z m167.21837675 0v109.0554628H665.98890749V475.64817931h109.05546279z m167.21837676 0v109.0554628H833.20728425V475.64817931h109.05546279z m167.21837676 0v109.0554628h-109.05546351V475.64817931h109.05546351zM440.60761749 322.97053068v109.05546278H331.55215396V322.97053068h109.05546352z m167.21837604 0v109.05546278H498.77053072V322.97053068h109.05546281z m167.21837675 0v109.05546278H665.98890749V322.97053068h109.05546279z m167.21837676 0v109.05546278H833.20728425V322.97053068h109.05546279z m167.21837676 0v109.05546278h-109.05546351V322.97053068h109.05546351z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.squarecheckfill:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M829.38281286 116.4921875c43.14941414 0 78.12499963 34.97558627 78.12499964 78.12499963v634.76562574c0 43.14941414-34.97558627 78.12499963-78.12499964 78.12499963H194.61718714c-43.14941414 0-78.12499963-34.97558627-78.12499964-78.12499963V194.61718714C116.4921875 151.46777377 151.46777377 116.4921875 194.61718714 116.4921875h634.76562573z m-98.30566448 247.07519509a29.29687505 29.29687505 0 0 0-40.63964825 0.76171868L453.35742165 601.40429692 332.5224611 480.55468788l-0.79101562-0.75683664a29.29687505 29.29687505 0 0 0-40.64453107 42.19238334l141.56249994 141.55761712 0.79101562 0.76660151a29.29687505 29.29687505 0 0 0 40.63964825-0.76660151l257.78808578-257.78320296 0.76171868-0.79101562a29.29687505 29.29687505 0 0 0-0.76171868-40.64453184z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.add:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 32A41.87919469 41.87919469 0 0 1 553.87919469 73.87919469V470.12080531h396.24161062a41.87919469 41.87919469 0 1 1 0 83.75838938H553.87275125L553.87919469 950.12080531a41.87919469 41.87919469 0 1 1-83.75838938 0L470.11436281 553.87919469H73.87919469a41.87919469 41.87919469 0 1 1 0-83.75838938H470.12080531V73.87919469A41.87919469 41.87919469 0 0 1 512 32z"
              fill="''' + getColor(0, color, colors, '#000000') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.jiantou_1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M513.6896 38.4c33.1264 0 60.416 25.1904 63.6416 57.4464L577.6896 102.4v662.8352l197.9392-197.8368a64 64 0 0 1 85.2992-4.6592l5.2224 4.608a64 64 0 0 1 4.608 85.3504l-4.608 5.2224-307.2 307.2a64 64 0 0 1-85.3504 4.608l-5.1712-4.608-307.2-307.2a64 64 0 0 1 85.2992-95.232l5.2224 4.7104 197.9392 197.9392V102.4c0-35.328 28.672-64 64-64z"
              fill="''' + getColor(0, color, colors, '#979797') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.wenhao_1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0z m0 85.333333a426.666667 426.666667 0 1 0 0 853.333334A426.666667 426.666667 0 0 0 512 85.333333z m6.144 586.069334a60.586667 60.586667 0 1 1 0 121.173333 60.586667 60.586667 0 0 1 0-121.173333z m112.64-381.696c51.626667 58.026667 47.786667 129.536 2.218667 190.293333a195.584 195.584 0 0 1-15.701334 18.517333l-13.909333 13.653334-11.178667 10.496c-25.6 23.722667-34.474667 34.986667-36.181333 47.872l-0.256 4.949333v45.397333h-85.333333v-45.397333c0-42.666667 15.189333-68.437333 52.053333-104.362667l26.965333-25.514666 8.874667-9.045334 6.4-7.850666c23.04-30.634667 24.490667-57.429333 2.304-82.346667-24.746667-27.904-78.677333-31.232-106.410667-6.570667-18.346667 16.298667-28.074667 33.706667-30.464 53.76l-0.597333 10.24h-85.333333c0-49.493333 20.565333-92.928 59.733333-127.744 63.061333-56.064 170.752-49.493333 226.816 13.653334z"
              fill="''' + getColor(0, color, colors, '#979797') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.gengduo_2:
        svgXml = '''
          <svg viewBox="0 0 4534 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 0a512 512 0 1 1 0 1024 512 512 0 0 1 0-1024z m1755.428571 0a512 512 0 1 1 0 1024 512 512 0 0 1 0-1024z m1755.428572 0a512 512 0 1 1 0 1024 512 512 0 0 1 0-1024z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shaixuan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M842.581333 42.666667H221.525333a110.933333 110.933333 0 0 0-82.773333 37.034666l-7.509333 9.386667A110.933333 110.933333 0 0 0 147.626667 236.373333l216.917333 193.962667v372.992c0 30.72 15.104 59.562667 40.362667 77.141333l150.101333 104.192a110.933333 110.933333 0 0 0 63.232 19.797334l11.349333-0.512a110.933333 110.933333 0 0 0 99.584-110.421334V428.544l192.768-197.461333A110.933333 110.933333 0 0 0 842.581333 42.666667z m-621.056 85.333333h621.056a25.6 25.6 0 0 1 18.261334 43.52l-204.8 209.834667a42.666667 42.666667 0 0 0-12.202667 29.866666v482.218667a25.6 25.6 0 0 1-40.106667 21.077333l-150.186666-104.192a8.533333 8.533333 0 0 1-3.584-7.082666V411.136a42.666667 42.666667 0 0 0-14.250667-31.829333l-231.253333-206.592a25.6 25.6 0 0 1 17.066666-44.714667z"
              fill="''' + getColor(0, color, colors, '#555555') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.jifenbumanzu:
        svgXml = '''
          <svg viewBox="0 0 1067 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M814.873239 519.211268C954.267042 519.211268 1067.267606 632.211831 1067.267606 771.605634S954.267042 1024 814.873239 1024 562.478873 910.999437 562.478873 771.605634 675.479437 519.211268 814.873239 519.211268z m0 43.267605C699.377577 562.478873 605.746479 656.109972 605.746479 771.605634S699.377577 980.732394 814.873239 980.732394 1024 887.101296 1024 771.605634 930.368901 562.478873 814.873239 562.478873zM490.366197 0c56.074817 0 109.813183 5.00462 159.527662 14.16293l13.470648 2.596056C832.540845 50.983662 951.887324 133.898817 951.887324 230.760563c0 39.113915-19.470423 75.94907-53.796056 108.226705 48.502986 39.661972 75.429859 86.679437 75.429859 136.956394a21.633803 21.633803 0 0 1-43.267606 0c0-38.06107-22.960676-75.804845-66.891718-109.265127-50.450028 34.556394-119.591662 61.814986-199.997296 78.083606l-13.470648 2.596056C600.17938 456.516507 546.441014 461.521127 490.366197 461.521127s-109.813183-5.00462-159.527662-14.16293l-13.470648-2.596056c-80.405634-16.26862-149.56169-43.541634-199.997295-78.069183C73.453972 400.109972 50.478873 437.868169 50.478873 475.943662c0 70.194479 77.564394 134.951662 196.088789 173.257915l11.393803 3.562367C325.257014 673.099718 404.811718 685.070423 490.366197 685.070423a21.633803 21.633803 0 0 1 0 43.267605c-159.657465 0-302.108845-39.445634-390.273803-102.111549C67.382085 656.009014 50.478873 688.430873 50.478873 721.126761c0 70.194479 77.564394 134.951662 196.088789 173.257915l11.393803 3.562366C325.257014 918.282817 404.811718 930.253521 490.366197 930.253521a21.633803 21.633803 0 0 1 0 43.267606C225.063662 973.521127 7.211268 864.602141 7.211268 721.126761c0-44.277183 20.912676-86.102535 59.132394-122.59155C28.700845 562.623099 7.211268 520.999662 7.211268 475.943662c0-50.305803 26.941296-97.308845 75.429859-136.956394C48.315493 306.709634 28.84507 269.874479 28.84507 230.760563 28.84507 133.898817 148.191549 50.983662 317.367887 16.758986l13.470648-2.596056C380.553014 5.00462 434.29138 0 490.366197 0z m324.507042 850.929577a21.633803 21.633803 0 1 1 0 43.267606 21.633803 21.633803 0 0 1 0-43.267606z m0-216.338028a31.729577 31.729577 0 0 1 31.469972 35.767888l-17.927211 139.797633a13.658141 13.658141 0 0 1-27.085521 0l-17.927211-139.797633A31.729577 31.729577 0 0 1 814.873239 634.591549zM490.366197 43.267606a852.371831 852.371831 0 0 0-127.09138 9.374648l-12.518761 1.990309c-6.216113 1.038423-12.374535 2.148958-18.446422 3.317183l-12.042817 2.437409-11.826479 2.596056c-17.609915 4.009465-34.513127 8.566986-50.594253 13.614874l-10.614986 3.432563-10.355381 3.562366-10.095774 3.706592A458.780845 458.780845 0 0 0 171.772394 112.063099l-8.163155 4.514253-7.860281 4.615211C103.077859 153.080789 72.112676 191.646648 72.112676 230.760563c0 39.113915 30.965183 77.665352 83.636282 109.553578l7.860281 4.615211 8.163155 4.514254a458.780845 458.780845 0 0 0 54.993127 24.777915l10.110197 3.706592 10.355381 3.562366 10.614986 3.432563c16.081127 5.047887 32.984338 9.605408 50.57983 13.614873l11.840902 2.596057 12.042817 2.437408c6.071887 1.168225 12.23031 2.278761 18.446422 3.317183l12.518761 2.004733A852.371831 852.371831 0 0 0 490.366197 418.253521a852.371831 852.371831 0 0 0 127.09138-9.374648l12.518761-1.99031c6.216113-1.038423 12.374535-2.148958 18.446423-3.317183l12.042816-2.437408 11.826479-2.596057a661.590535 661.590535 0 0 0 50.594254-13.614873l10.614986-3.432563 10.35538-3.562366 10.095775-3.706592a458.780845 458.780845 0 0 0 55.007549-24.777915l8.163155-4.514254 7.860282-4.615211C877.654535 308.440338 908.619718 269.874479 908.619718 230.760563c0-39.113915-30.965183-77.665352-83.636281-109.553577l-7.860282-4.615211-8.163155-4.514254a458.780845 458.780845 0 0 0-54.993127-24.777915l-10.110197-3.706592-10.35538-3.562366-10.614986-3.432563a661.590535 661.590535 0 0 0-50.579831-13.614874l-11.840902-2.596056-12.042816-2.437409a762.649239 762.649239 0 0 0-18.446423-3.317183l-12.518761-2.004732a849.905577 849.905577 0 0 0-120.082028-9.33138L490.366197 43.267606z"
              fill="''' + getColor(0, color, colors, '#666666') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.jiantoushang:
        svgXml = '''
          <svg viewBox="0 0 1732 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1649.609627 925.787386a118.13867 118.13867 0 0 1-158.620854 7.639634l-8.427225-7.639634-640.469109-640.469109-640.39035 640.469109a118.13867 118.13867 0 0 1-158.699613 7.639634l-8.427225-7.639634a118.13867 118.13867 0 0 1-7.639634-158.620854l7.639634-8.427225L758.607779 34.706778a118.13867 118.13867 0 0 1 158.620854-7.639634l8.427225 7.639634 723.953769 724.032529a118.13867 118.13867 0 0 1 0 167.048079z"
              fill="''' + getColor(0, color, colors, '#979797') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.xiaopiao_1:
        svgXml = '''
          <svg viewBox="0 0 1097 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M990.244571 0a73.142857 73.142857 0 0 1 73.142858 73.142857v247.588572a73.142857 73.142857 0 0 1-73.142858 73.142857l-5.632-0.036572V230.692571a73.142857 73.142857 0 0 0-67.657142-72.96l-5.485715-0.182857H151.881143a73.142857 73.142857 0 0 0-72.923429 67.657143l-0.182857 5.485714-0.036571 163.145143H73.142857a73.142857 73.142857 0 0 1-73.142857-73.142857V73.142857a73.142857 73.142857 0 0 1 73.142857-73.142857h917.101714z"
              fill="''' + getColor(0, color, colors, '#7BAAFF') + '''"
            />
            <path
              d="M827.318857 236.324571a73.142857 73.142857 0 0 1 73.142857 73.142858v645.741714a35.84 35.84 0 0 1-56.393143 29.403428l-23.990857-16.786285a73.142857 73.142857 0 0 0-69.046857-7.936l-28.854857 11.556571a67.474286 67.474286 0 0 1-62.500571-6.509714 67.474286 67.474286 0 0 0-62.500572-6.509714l-38.326857 15.323428a73.142857 73.142857 0 0 1-54.308571 0l-44.141715-17.664a73.142857 73.142857 0 0 0-54.345143 0l-38.326857 15.36a67.474286 67.474286 0 0 1-62.500571-6.509714 52.004571 52.004571 0 0 0-58.624 0.621714l-27.282286 19.053714a35.84 35.84 0 0 1-56.393143-29.403428V309.467429a73.142857 73.142857 0 0 1 73.142857-73.142858h591.250286z m-216.868571 396.653715H295.387429l-4.278858 0.219428a36.571429 36.571429 0 0 0 4.278858 72.923429h315.062857l4.278857-0.256a36.571429 36.571429 0 0 0-4.278857-72.886857z m-118.125715-196.937143H295.387429l-4.278858 0.256a36.571429 36.571429 0 0 0 4.278858 72.886857h196.937142l4.242286-0.256a36.571429 36.571429 0 0 0-4.242286-72.886857z"
              fill="''' + getColor(1, color, colors, '#4386FF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.xuanzhonggou:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1024 0v1024H0L1024 0z m-174.996211 575.488l-191.973052 192-109.67579-109.675789-43.92421 43.870315 153.6 153.6 235.897263-235.897263-43.924211-43.897263z"
              fill="''' + getColor(0, color, colors, '#4679FC') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.jiantou:
        svgXml = '''
          <svg viewBox="0 0 1811 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M98.222813 34.682814A118.150938 118.150938 0 0 1 256.860139 26.884852l8.4281 7.640427L905.823857 675.139664 1546.359474 34.682814a118.150938 118.150938 0 0 1 158.637326-7.640427l8.4281 7.640427a118.150938 118.150938 0 0 1 7.640428 158.716093l-7.640428 8.4281-724.028947 724.028947a118.150938 118.150938 0 0 1-158.716093 7.640428l-8.4281-7.640428-724.028947-724.028947a118.150938 118.150938 0 0 1 0-167.144193z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.dianpuerweima:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 0m9.061947 0l1005.876106 0q9.061947 0 9.061947 9.061947l0 1005.876106q0 9.061947-9.061947 9.061947l-1005.876106 0q-9.061947 0-9.061947-9.061947l0-1005.876106q0-9.061947 9.061947-9.061947Z"
              fill="''' + getColor(0, color, colors, '#F7F7F7') + '''"
            />
            <path
              d="M449.264142 335.989805h27.88361v27.883611h-27.88361z"
              fill="''' + getColor(1, color, colors, '#C4C4C4') + '''"
            />
            <path
              d="M449.264142 433.577912h27.88361v27.88361h-27.88361zM523.617416 405.694301h27.883611v27.883611h-27.883611zM579.384637 405.694301h27.883611v27.883611h-27.883611zM551.501027 433.577912h27.88361v27.88361h-27.88361zM523.617416 461.461522h83.650832V489.345133h-83.650832z"
              fill="''' + getColor(2, color, colors, '#C4C4C4') + '''"
            />
            <path
              d="M551.501027 335.989805h27.88361v27.883611h-27.88361z"
              fill="''' + getColor(3, color, colors, '#C4C4C4') + '''"
            />
            <path
              d="M241.876956 694.566513c8.848991-11.114478 13.629168-27.715965 14.336-49.80446v-35.577203h86.228956v-7.856708h-40.833133a155.353487 155.353487 0 0 0-8.019823-13.275753l-7.535009 3.715399c2.084248 3.189805 3.982726 6.37508 5.681841 9.560354h-44.335576v41.52184c-0.072496 19.537558-3.932885 34.371965-11.576637 44.49416 2.405947 2.618903 4.42223 5.029381 6.053381 7.222371z m32.917522-0.851823v-6.900672H323.013097v6.900672h8.495576v-45.554407h-31.535576v-15.079079h39.818195v-7.648284h-39.822726v-13.275752h-8.708531v36.003115h-24.956601v45.554407h8.495575z m48.214088-14.336H274.80354v-23.574655h48.209557v23.574655z m70.089629 14.127575v-24.743646h17.838442v23.574655h7.861239v-23.574655h19.433345v10.724815c0 3.67915-1.839575 5.523257-5.523256 5.523256-2.618903 0-6.089628-0.212956-10.407646-0.638867 0.706832 3.112779 1.241487 5.840425 1.594902 8.178407 3.896637 0.135929 7.643752 0.212956 11.254938 0.212956 7.294867-0.072496 10.93777-3.756177 10.93777-11.046513v-59.999151H418.797876v-10.090478h32.174443v-7.643752h-14.761912l4.993133-3.506973a230.989027 230.989027 0 0 0-9.560354-11.68085l-6.583505 4.567221c2.97685 3.53869 5.663717 7.081912 8.074195 10.620602H418.797876v-14.227257h-7.861239v14.227257h-28.989168v7.643752h28.989168v10.090478h-25.699681v71.788743h7.861239z m-42.264921-63.188955c3.044814-4.952354 5.980885-10.761062 8.812744-17.412531h19.542088v-7.331115h-16.673982a182.734159 182.734159 0 0 0 4.245522-13.697133l-7.96092-1.807858a96.564106 96.564106 0 0 1-13.488708 33.135008c2.125027 2.546407 3.964602 4.920637 5.523256 7.113629z m10.829027 62.867256c2.478442-2.265487 4.354265-3.855858 5.632-4.775646 5.944637-4.250053 10.439363-7.331115 13.484177-9.243185a132.884389 132.884389 0 0 1-1.699115-8.386832 380.778478 380.778478 0 0 1-11.893805 8.178407v-24.956602h13.910088v-7.326584h-13.910088v-13.062797h10.829026v-7.326584h-24.104779v7.326584h5.736213v13.062797h-12.849841v7.326584h12.849841v23.891823c0 3.611186-1.205239 6.37508-3.611186 8.282619l5.627469 7.009416z m76.56892-51.716531h-19.437876v-12.319716h19.437876v12.324247z m-27.294584 0h-17.838442v-12.319716h17.838442v12.324247z m27.294584 19.859257h-19.437876v-12.423929h19.437876v12.423929z m-27.294584 0h-17.838442v-12.423929h17.838442v12.423929z m139.862089-49.274336v-9.134443h-86.659399v9.134443h86.659399z m7.539539 69.346548v-8.921486h-102.055646v8.921486h102.055646z m8.173877-19.968c7.149876-1.268673 17.204106-2.650619 30.158159-4.141309 0.212956-1.626619 0.498407-4.173027 0.851823-7.643753-8.069664 0.919788-15.468743 1.76708-22.192708 2.546408 6.157593-8.916956 14.191009-22.192708 24.104779-39.822726l-7.648283-3.39823a558.804956 558.804956 0 0 1-7.91108 15.559363c-5.237805 0.104212-9.111788 0.158584-11.626478 0.158584 5.523257-9.274903 11.254938-20.24892 17.204106-32.922053l-8.282619-2.972319c-5.169841 13.524956-11.150726 25.699681-17.947186 36.533239l2.016283 6.687717c3.964602-0.353416 9.043823-0.74308 15.237664-1.168991-6.370549 11.436177-11.553982 19.011965-15.554832 22.727362l1.590372 7.856708z m47.575221 32.074761v-7.222371h53.94577v-7.222372h-24.530691v-16.461026h22.410195v-7.217841h-22.410195v-16.039646h22.410195v-7.217841h-22.410195v-16.039646h23.257487v-7.217841h-49.750088a442.73954 442.73954 0 0 0 8.336991-17.204106l-8.178407-2.65515c-6.162124 16.143858-13.842124 30.547823-23.044531 43.225487 2.192991 2.473912 3.964602 4.811894 5.310301 7.004884 2.125027-3.081062 4.318018-6.497416 6.583504-10.249061v64.51653h8.069664z m27.398796-85.381663l7.009416-4.463009c-2.618903-4.458478-5.980885-9.270372-10.090478-14.440212l-6.583504 4.141309c3.896637 5.310301 7.113628 10.230938 9.664566 14.761912z m-6.05338 24.000566h-21.340885v-16.039646h21.340885v16.039646z m0 23.257487h-21.340885v-16.039646h21.340885v16.039646z m0 23.678867h-21.340885v-16.461026h21.340885v16.461026z m-69.554974 7.326584c8.916956-1.76708 20.421097-4.14131 34.512425-7.113628 0-2.550938 0.104212-5.274053 0.317168-8.178407a935.102301 935.102301 0 0 1-36.850407 7.326584l2.020814 7.965451z m191.043965 5.097345c9.415363 0 14.440212-5.274053 15.07908-15.822159 0.566372-10.548106 1.132743-23.008283 1.699115-37.380531h-11.259469l4.463008-42.582088H718.793628v7.643752h39.183859l-3.556814 34.938336h-25.010974l2.763894-31.114195h-8.069664l-3.294017 38.762478h44.281203a626.085381 626.085381 0 0 1-1.268673 28.350301c-0.430442 6.021664-3.366513 9.03023-8.817274 9.03023-4.603469 0-9.768779-0.317168-15.504991-0.956035 0.566372 3.253239 1.060248 6.193841 1.486159 8.812743a444.805664 444.805664 0 0 0 15.930903 0.317168z m-66.904354-0.530124v-8.708531h13.275752v7.326585h7.53954v-57.556956h-20.547965a150.654867 150.654867 0 0 0 8.495576-24.847859h15.876531v-7.856708h-39.718514v7.856708h15.663576c-3.860389 15.36-10.104071 29.025416-18.744638 40.991717 2.052531 2.831858 3.611186 5.237805 4.671434 7.222372 2.088779-2.763894 4.073345-5.650124 5.949168-8.654159v44.226831h7.53954z m13.275752-15.717946h-13.275752v-35.998585h13.275752v35.998585z m56.60092-7.326585v-7.643752h-44.811327v7.643752h44.811327z"
              fill="''' + getColor(4, color, colors, '#B8B8B8') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shangchuantupian:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M698.181818 0l279.272727 279.272727v651.636364a93.090909 93.090909 0 0 1-93.090909 93.090909H139.636364a93.090909 93.090909 0 0 1-93.090909-93.090909V93.090909a93.090909 93.090909 0 0 1 93.090909-93.090909h558.545454z m-180.363636 325.818182a23.272727 23.272727 0 0 0-23.272727 23.272727v162.909091h-162.909091l-2.722909 0.162909A23.272727 23.272727 0 0 0 331.636364 558.545455h162.909091v162.90909l0.162909 2.72291A23.272727 23.272727 0 0 0 541.090909 721.454545v-162.90909h162.909091l2.722909-0.16291A23.272727 23.272727 0 0 0 704 512h-162.909091v-162.909091l-0.162909-2.722909A23.272727 23.272727 0 0 0 517.818182 325.818182z"
              fill="''' + getColor(0, color, colors, '#2769FF') + '''"
            />
            <path
              d="M698.181818 0l279.272727 279.272727H744.727273a46.545455 46.545455 0 0 1-46.545455-46.545454V0z"
              fill="''' + getColor(1, color, colors, '#B7CBFD') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.xitonggengxin:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 0c282.766222 0 512 229.233778 512 512s-229.233778 512-512 512S0 794.766222 0 512 229.233778 0 512 0z m0 56.888889C260.636444 56.888889 56.888889 260.636444 56.888889 512s203.747556 455.111111 455.111111 455.111111 455.111111-203.747556 455.111111-455.111111S763.363556 56.888889 512 56.888889z m98.304 677.489778a22.471111 22.471111 0 0 1 0 44.999111h-192.910222a22.471111 22.471111 0 0 1 0-44.999111h192.910222z m-77.710222-464.213334l172.003555 167.992889a28.444444 28.444444 0 0 1-19.882666 48.782222h-53.873778v185.258667a28.444444 28.444444 0 0 1-28.444445 28.444445h-179.313777a28.444444 28.444444 0 0 1-28.444445-28.444445v-185.258667h-53.902222a28.444444 28.444444 0 0 1-19.882667-48.782222l172.003556-167.992889a28.444444 28.444444 0 0 1 39.736889 0z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.a_yijingtijiao_1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1024 512c0 282.766-229.234 512-512 512S0 794.766 0 512 229.234 0 512 0s512 229.234 512 512z m-56.889 0q0-86.812-31.957-167.524-34.702-87.637-101.348-154.282-66.645-66.66-154.282-101.348Q598.812 56.89 512 56.89T344.476 88.846q-87.637 34.702-154.282 101.348-66.66 66.645-101.348 154.282Q56.89 425.188 56.89 512t31.957 167.524q34.702 87.637 101.348 154.282 66.645 66.66 154.282 101.348Q425.188 967.11 512 967.11t167.524-31.957q87.637-34.702 154.282-101.348 66.66-66.645 101.348-154.282Q967.11 598.812 967.11 512z"
              fill="''' + getColor(0, color, colors, '#29C4A8') + '''"
            />
            <path
              d="M240.043 471.111a33.337 33.337 0 0 0-45.554 0.797 31.545 31.545 0 0 0-0.797 44.515l218.624 213.76c12.8 12.501 33.536 12.501 46.336 0l371.656-363.378a31.545 31.545 0 0 0-0.797-44.515 33.337 33.337 0 0 0-45.554-0.797L435.484 662.158l-195.441-191.09v0.043z"
              fill="''' + getColor(1, color, colors, '#29C4A8') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.gouxuan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1024 0v1024L0 0h1024z m-174.979879 163.219394l-191.984485 191.984485-109.692121-109.692121-43.907879 43.876848 153.6 153.6 235.892364-235.892364L848.989091 163.219394z"
              fill="''' + getColor(0, color, colors, '#4679FC') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.saoma:
        svgXml = '''
          <svg viewBox="0 0 1060 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1046.125714 681.691429V1024h-342.308571v-109.714286h232.557714v-232.594285h109.714286z m-918.125714 0v232.557714L360.594286 914.285714v109.714286H18.285714v-342.308571h109.714286z m851.748571-224.548572v109.714286H139.52v-109.714286h840.301714zM360.594286 18.285714v109.714286H128.036571L128 360.594286h-109.714286V18.285714h342.308572z m685.531428 0v342.308572h-109.714285V128h-232.594286v-109.714286h342.308571z"
              fill="''' + getColor(0, color, colors, '#666666') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shuaxin:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1022.421333 402.304v-256l-83.498666 83.498667A511.616 511.616 0 0 0 512 0C229.248 0 0 229.205333 0 512s229.248 512 512 512a512 512 0 0 0 474.496-319.402667 43.861333 43.861333 0 1 0-81.28-33.024A424.362667 424.362667 0 0 1 512 936.234667c-234.282667 0-424.234667-189.952-424.234667-424.234667S277.717333 87.765333 512 87.765333a424.021333 424.021333 0 0 1 363.434667 205.525334l-108.970667 109.013333h256z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.jianpan_1:
        svgXml = '''
          <svg viewBox="0 0 1203 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M809.1648 861.44l-210.7648 150.528-210.7648-150.528h421.5296zM1103.6928 18.4064a76.8 76.8 0 0 1 76.8 76.8v639.232a76.8 76.8 0 0 1-76.8 76.8H83.0464a76.8 76.8 0 0 1-76.8-76.8V95.232a76.8 76.8 0 0 1 76.8-76.8h1020.672z m0 76.8H83.0464v639.232h1020.672V95.232z m-320.128 465.92v99.328H413.2352v-99.328h370.3296z m-424.5248-198.7328v162.56H196.4544v-162.56h162.56z m213.7856 0v162.56h-162.56v-162.56h162.56z m213.8112 0v162.56h-162.5856v-162.56h162.5856z m213.8112 0v162.56h-162.5856v-162.56h162.56zM359.04 163.6608v162.5856H196.4544v-162.56h162.56z m213.76 0v162.5856h-162.56v-162.56h162.56z m213.76 0v162.5856h-162.56v-162.56h162.56z m213.7856 0v162.5856h-162.56v-162.56h162.56z"
              fill="''' + getColor(0, color, colors, '#979797') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.duigou_1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 0c282.770286 0 512 229.229714 512 512s-229.229714 512-512 512S0 794.770286 0 512 229.229714 0 512 0z m0 79.792208c-238.705039 0-432.207792 193.502753-432.207792 432.207792s193.502753 432.207792 432.207792 432.207792 432.207792-193.502753 432.207792-432.207792-193.502753-432.207792-432.207792-432.207792z m271.625974 254.590337l1.077195 1.037299a39.896104 39.896104 0 0 1 1.037299 55.342546l-1.037299 1.077194-319.720727 319.720728a39.896104 39.896104 0 0 1-55.342546 1.037298l-1.077195-1.037298-169.26587-169.26587a39.896104 39.896104 0 0 1 55.342546-57.457039l1.077194 1.037298 141.052676 141.059325 291.507532-291.514182a39.896104 39.896104 0 0 1 55.349195-1.037299z"
              fill="''' + getColor(0, color, colors, '#1678FF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.taotaogu:
        svgXml = '''
          <svg viewBox="0 0 2852 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1858.925714 0v56.685714c0 3.437714 2.706286 6.144 5.997715 6.290286h14.482285a6.290286 6.290286 0 0 0 6.290286-5.924571V0h924.818286v1024H1885.769143v-59.830857a6.290286 6.290286 0 0 0-5.997714-6.363429h-14.482286a6.290286 6.290286 0 0 0-6.363429 5.997715V1024H953.124571v-59.830857a6.290286 6.290286 0 0 0-5.997714-6.363429h-14.482286a6.290286 6.290286 0 0 0-4.754285 2.194286V1024H0V0c164.864 13.165714 333.750857 46.738286 506.514286 100.790857 172.763429 54.125714 313.197714 117.394286 421.376 189.878857v9.654857l0.219428 0.292572a6.290286 6.290286 0 0 0 4.169143 1.828571h14.555429a6.290286 6.290286 0 0 0 6.290285-5.924571V245.76a6.290286 6.290286 0 0 0-5.997714-6.290286h-14.482286a6.290286 6.290286 0 0 0-4.754285 2.194286v-61.074286a6.290286 6.290286 0 0 0 4.388571 2.194286h14.555429a6.290286 6.290286 0 0 0 6.290285-5.997714v-50.761143a6.290286 6.290286 0 0 0-5.997714-6.290286h-14.482286a6.290286 6.290286 0 0 0-4.754285 2.194286V60.781714a6.290286 6.290286 0 0 0 4.388571 2.194286h14.555429A6.290286 6.290286 0 0 0 953.051429 57.051429V0h905.801142z m-912.091428 838.070857h-14.189715a6.290286 6.290286 0 0 0-4.754285 2.194286v58.733714l0.219428 0.219429a6.290286 6.290286 0 0 0 4.169143 1.901714h14.555429a6.290286 6.290286 0 0 0 6.290285-5.997714v-50.761143a6.290286 6.290286 0 0 0-5.997714-6.217143h-0.292571z m932.571428 0h-14.116571a6.290286 6.290286 0 0 0-6.363429 5.997714v50.761143c0 3.364571 2.706286 6.144 5.997715 6.290286h14.482285a6.290286 6.290286 0 0 0 6.290286-5.997714v-50.761143a6.290286 6.290286 0 0 0-6.290286-6.290286zM478.134857 171.739429h-144.091428v99.986285H214.162286V398.628571H327.68v290.669715c5.266286 78.774857 33.865143 130.779429 85.869714 155.940571 62.390857 30.281143 124.196571 32.914286 176.713143 31.085714l3.949714-0.146285 1.974858-0.146286 3.803428-0.146286 3.876572-0.146285 3.803428-0.219429 7.460572-0.365714 12.653714-0.731429a503.734857 503.734857 0 0 0 118.564571-22.089143 6.290286 6.290286 0 0 0 4.388572-6.802285l-15.872-123.904a6.290286 6.290286 0 0 0-6.363429-5.485715l-180.516571 2.56a37.814857 37.814857 0 0 1-38.326857-36.644571V398.555429h215.771428V280.429714l-97.645714 40.96-44.105143-49.664H508.050286v-70.070857a29.988571 29.988571 0 0 0-29.988572-29.988571z m967.314286-17.334858h-174.08v115.785143h-119.734857v126.829715h113.371428v290.669714c5.266286 78.701714 33.865143 130.706286 85.869715 155.940571 62.390857 30.208 124.269714 32.914286 176.786285 31.012572l3.876572-0.146286 1.974857-0.073143 3.876571-0.146286 3.803429-0.219428 3.803428-0.146286 7.460572-0.438857 12.653714-0.731429a503.734857 503.734857 0 0 0 118.637714-22.016 6.290286 6.290286 0 0 0 4.388572-6.875428l-15.945143-123.830857a6.290286 6.290286 0 0 0-6.363429-5.485715l-180.516571 2.486858a37.814857 37.814857 0 0 1-38.326857-36.571429V396.946286h215.771428V270.189714h-217.307428V154.331429z m1141.979428 16.091429c-185.051429-41.618286-316.269714-28.818286-393.728 38.253714-117.321143 101.668571-140.946286 393.069714 52.004572 459.190857 62.390857 16.676571 119.661714 12.726857 171.739428-11.849142l1.536-0.731429V696.685714l-0.073142 1.170286-0.073143 1.243429-0.073143 1.243428-0.146286 1.243429-0.146286 1.316571c-2.121143 18.139429-12.214857 39.862857-54.637714 39.862857H2356.370286l-3.584-0.146285-3.657143-0.146286h-2.56l-2.633143-0.146286-4.096-0.219428-4.315429-0.219429-4.388571-0.292571-3.072-0.146286-3.145143-0.219429-4.900571-0.292571-4.973715-0.365714-7.021714-0.585143-5.412571-0.365715-5.632-0.512-7.753143-0.658285-5.997714-0.512-8.338286-0.731429-6.363429-0.658286-6.582857-0.658285-9.142857-0.877715-7.021714-0.731428-7.168-0.731429-9.801143-1.024-12.8-1.389714-10.532572-1.170286-10.825142-1.243428-8.338286-0.950857-11.410286-1.316572-8.777143-1.024-29.110857 117.394286 4.754286 1.243428c155.648 39.204571 270.116571 40.374857 343.405714 3.510858 84.699429-39.497143 120.832-120.246857 108.251429-242.102858l-0.365715-3.657142v-430.08l-5.632-1.316572z m-1640.594285 547.84h-14.189715a6.290286 6.290286 0 0 0-4.754285 2.194286v58.733714l0.219428 0.219429a6.290286 6.290286 0 0 0 4.169143 1.901714h14.555429a6.290286 6.290286 0 0 0 6.290285-5.997714v-50.761143a6.290286 6.290286 0 0 0-5.997714-6.217143h-0.292571z m932.571428 0h-14.116571a6.290286 6.290286 0 0 0-6.363429 5.997714v50.761143c0 3.364571 2.706286 6.144 5.997715 6.290286h14.482285a6.290286 6.290286 0 0 0 6.290286-5.997714v-50.761143a6.290286 6.290286 0 0 0-6.290286-6.217143z m-932.571428-119.661714h-14.189715a6.290286 6.290286 0 0 0-4.754285 2.121143v58.733714l0.219428 0.219428a6.290286 6.290286 0 0 0 4.169143 1.901715h14.555429a6.290286 6.290286 0 0 0 6.290285-5.997715V604.891429a6.290286 6.290286 0 0 0-5.997714-6.217143h-0.292571z m932.571428 0h-14.116571a6.290286 6.290286 0 0 0-6.363429 5.924571v50.761143c0 3.364571 2.706286 6.144 5.997715 6.290286h14.482285a6.290286 6.290286 0 0 0 6.290286-5.997715V604.891429a6.290286 6.290286 0 0 0-6.290286-6.217143z m534.893715-319.853715a9.508571 9.508571 0 0 1 9.435428 9.142858v255.634285a3.145143 3.145143 0 0 1-2.194286 2.998857c-40.448 13.970286-74.166857 13.750857-100.937142-0.731428-35.254857-19.017143-41.984-61.805714-40.155429-131.072l0.146286-4.827429 0.073143-2.486857 0.219428-5.046857 0.073143-2.486857 0.219429-5.12 0.219428-5.266286 0.292572-5.266286 0.219428-5.412571c4.681143-89.673143 58.806857-99.84 85.138286-100.059429h47.250286zM946.834286 478.939429h-14.189715a6.290286 6.290286 0 0 0-4.754285 2.121142v58.733715l0.219428 0.292571a6.290286 6.290286 0 0 0 4.169143 1.828572h14.555429a6.290286 6.290286 0 0 0 6.290285-5.997715v-50.761143a6.290286 6.290286 0 0 0-5.997714-6.217142h-0.292571z m932.571428 0h-14.116571a6.290286 6.290286 0 0 0-6.363429 5.997714v50.761143c0 3.291429 2.706286 6.070857 5.997715 6.217143h14.482285a6.290286 6.290286 0 0 0 6.290286-5.997715v-50.761143a6.290286 6.290286 0 0 0-6.290286-6.217142zM946.834286 359.131429h-14.189715a6.290286 6.290286 0 0 0-4.754285 2.121142v58.733715l0.219428 0.292571a6.290286 6.290286 0 0 0 4.169143 1.828572h14.555429a6.290286 6.290286 0 0 0 6.290285-5.997715v-50.761143a6.290286 6.290286 0 0 0-5.997714-6.217142h-0.292571z m932.571428 0h-14.116571a6.290286 6.290286 0 0 0-6.363429 5.997714v50.761143c0 3.291429 2.706286 6.070857 5.997715 6.217143h14.482285a6.290286 6.290286 0 0 0 6.290286-5.997715v-50.761143a6.290286 6.290286 0 0 0-6.290286-6.217142z m0-119.734858h-14.116571a6.290286 6.290286 0 0 0-6.363429 5.997715v50.761143c0 3.291429 2.706286 6.070857 5.997715 6.217142h14.482285a6.290286 6.290286 0 0 0 6.290286-5.924571V245.76a6.290286 6.290286 0 0 0-6.290286-6.290286z m0-119.734857h-14.116571a6.290286 6.290286 0 0 0-6.363429 5.997715v50.761142c0 3.291429 2.706286 6.070857 5.997715 6.217143h14.482285a6.290286 6.290286 0 0 0 6.290286-5.924571v-50.761143a6.290286 6.290286 0 0 0-6.290286-6.290286z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.chuzhi:
        svgXml = '''
          <svg viewBox="0 0 1293 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 248.293053v82.782315h1287.437474V248.293053H0zM1136.316632 0c40.798316 0.538947 79.602526 17.677474 107.627789 47.427368 28.456421 30.127158 44.032 70.170947 43.493053 111.669895v674.977684c0.538947 41.498947-15.090526 81.542737-43.493053 111.72379-28.025263 29.696-66.829474 46.780632-107.627789 47.373474H152.899368a150.797474 150.797474 0 0 1-107.627789-47.373474 159.690105 159.690105 0 0 1-43.493053-111.72379V159.097263A155.216842 155.216842 0 0 1 151.983158 0h984.333474z m-204.638316 485.052632h-81.542737l91.405474 169.229473h-69.308632v48.289684h84.506947v41.606737h-84.506947v48.23579h84.506947V862.315789h77.392843v-69.901473h81.81221v-48.23579h-81.81221v-41.606737h81.81221v-48.235789h-67.799579L1142.245053 485.052632h-77.392842l-55.296 127.137684a627.334737 627.334737 0 0 0-10.078316 24.576h-0.970106L931.678316 485.052632z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.morentouxiang:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z"
              fill="''' + getColor(0, color, colors, '#DADBDF') + '''"
            />
            <path
              d="M504.1152 283.562667a126.037333 126.037333 0 0 1 41.762133 244.992c91.818667 21.418667 160.085333 101.0176 160.085334 196.130133v12.8H286.5152v-12.8c0-98.594133 73.386667-180.514133 170.257067-198.280533a126.037333 126.037333 0 0 1 47.36-242.8416z m-7.867733 264.874666c-96.802133 0-175.9744 71.441067-183.534934 162.030934l-0.1024 1.416533h367.2576l-0.546133-6.075733c-9.659733-86.818133-85.230933-154.965333-178.090667-157.320534z m7.867733-239.274666a100.437333 100.437333 0 1 0 0 200.874666 100.437333 100.437333 0 0 0 0-200.874666z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.jifendixian:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 0m198.193548 0l627.612904 0q198.193548 0 198.193548 198.193548l0 627.612904q0 198.193548-198.193548 198.193548l-627.612904 0q-198.193548 0-198.193548-198.193548l0-627.612904q0-198.193548 198.193548-198.193548Z"
              fill="''' + getColor(0, color, colors, '#F4CA24') + '''"
            />
            <path
              d="M794.987355 328.340645c0-35.410581-126.183226-64.082581-281.89729-64.08258C357.442065 264.258065 231.225806 292.930065 231.225806 328.340645v76.866065c34.155355 42.71071 128.132129 64.082581 281.864259 64.08258 153.765161 0 247.741935-21.371871 281.89729-64.08258V328.307613zM231.225806 484.847484v80.565677c0 0.990968 0.462452 1.915871 1.222194 2.543484 51.596387 42.050065 149.404903 63.058581 293.458581 63.058581 144.152774 0 233.538065-21.041548 268.288-63.091613a3.237161 3.237161 0 0 0 0.792774-2.114065v-78.319483a3.303226 3.303226 0 0 0-5.84671-2.147097c-31.710968 37.326452-119.477677 55.989677-263.234064 55.989677-147.489032 0-243.910194-19.654194-289.19742-58.96258a3.303226 3.303226 0 0 0-5.483355 2.477419zM231.225806 646.606452v80.565677c0 0.990968 0.462452 1.915871 1.222194 2.543484C284.077419 771.765677 381.852903 792.774194 525.906581 792.774194c144.152774 0 233.538065-21.041548 268.288-63.091613a3.237161 3.237161 0 0 0 0.792774-2.114065v-78.319484a3.303226 3.303226 0 0 0-5.84671-2.147097c-31.710968 37.326452-119.477677 55.989677-263.234064 55.989678-147.489032 0-243.910194-19.654194-289.19742-58.962581a3.303226 3.303226 0 0 0-5.483355 2.47742z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.duigou:
        svgXml = '''
          <svg viewBox="0 0 1389 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1206.272 29.037714l155.136 155.136-807.497143 807.497143L43.739429 481.645714l155.136-155.136 354.962285 354.889143z"
              fill="''' + getColor(0, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.touxiang:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 511.6416a512 511.6416 0 1 0 1024 0 512 511.6416 0 1 0-1024 0Z"
              fill="''' + getColor(0, color, colors, '#E9E9E9') + '''"
            />
            <path
              d="M431.1552 610.2528h152.2176v200.8704H431.1552z"
              fill="''' + getColor(1, color, colors, '#F3DDD4') + '''"
            />
            <path
              d="M428.2496 732.8896C321.408 761.984 253.9392 784.7936 225.7792 801.28c-28.16 16.512-48.0768 44.6464-59.7248 84.4288C278.6944 977.9072 393.28 1024 509.8112 1024c116.5312 0 230.272-44.032 341.2096-132.096-14.7968-42.9312-41.1392-73.1392-79.0144-90.6112-37.8752-17.4592-96.6272-40.2688-176.256-68.4032l-80.1152 65.4976-87.3856-65.4976z"
              fill="''' + getColor(2, color, colors, '#B3B8C3') + '''"
            />
            <path
              d="M377.2672 739.8016l58.7392 130.9952 79.6288-80.0512-87.3856-65.4976z"
              fill="''' + getColor(3, color, colors, '#FFFFFF') + '''"
            />
            <path
              d="M632.896 739.8016L574.1568 870.784l-79.6416-80.0512 87.3984-65.4976z"
              fill="''' + getColor(4, color, colors, '#FFFFFF') + '''"
            />
            <path
              d="M632.896 520.7296a36.416 36.3904 0 1 0 72.832 0 36.416 36.3904 0 1 0-72.832 0Z"
              fill="''' + getColor(5, color, colors, '#F8E5DE') + '''"
            />
            <path
              d="M297.8816 520.7296a36.416 36.3904 0 1 0 72.832 0 36.416 36.3904 0 1 0-72.832 0Z"
              fill="''' + getColor(6, color, colors, '#F8E5DE') + '''"
            />
            <path
              d="M500.352 685.2096c71.3728-2.9056 153.0752-94.336 183.5264-200.8704 14.5664-50.944 44.2368-72.448 29.1328-101.888-30.9248-60.288-146.176-98.9824-212.6592-98.9824-89.28 0-174.2848 68.6976-187.904 164.48-1.28 8.96 5.2224 26.1632 7.2832 36.3904 0.32 1.6256 1.4464-1.4848 1.4464 0 0 110.9376 107.8016 203.776 179.1744 200.8704z"
              fill="''' + getColor(7, color, colors, '#F8E5DE') + '''"
            />
            <path
              d="M319.7312 484.352c33.024-81.5232 66.9952-117.9136 101.9648-109.184 52.4288 13.1072 119.4368 13.1072 138.368 0 18.944-13.0944 23.3088-5.8112 58.2656 50.944 23.3088 37.8496 45.1584 62.1184 65.5488 72.7936 42.1632-78.8608 52.352-140.48 30.592-184.8704-6.9376-14.144-29.2736-32.576-67.008-55.296 6.7968-38.8224-33.4976-58.24-120.896-58.24-131.0976 0-215.5776 74.24-228.6848 138.2912-8.7424 42.688-1.4592 91.2128 21.8496 145.5488z"
              fill="''' + getColor(8, color, colors, '#665454') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.saomiao:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1009.664 683.385263v160.498526a154.947368 154.947368 0 0 1-149.018947 154.839579l-5.928421 0.10779h-160.525474v-94.31579h160.525474a60.631579 60.631579 0 0 0 60.496842-56.481684l0.134737-4.149895V683.385263h94.315789z m-895.137684 0v160.498526A60.631579 60.631579 0 0 0 175.157895 904.515368h160.525473v94.31579H175.157895a154.947368 154.947368 0 0 1-154.947369-154.947369V683.385263h94.31579z m681.903158-231.262316v94.31579H249.263158v-94.31579h547.166316zM335.683368 6.736842v94.31579H175.157895a60.631579 60.631579 0 0 0-60.496842 56.481684L114.526316 161.684211v160.525473H20.210526V161.684211A154.947368 154.947368 0 0 1 169.229474 6.844632L175.157895 6.736842h160.525473z m519.033264 0A154.947368 154.947368 0 0 1 1009.664 161.684211v160.525473h-94.315789V161.684211a60.631579 60.631579 0 0 0-60.631579-60.631579h-160.525474V6.736842h160.525474z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.dayinshezhi:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 0m170.666667 0l682.666666 0q170.666667 0 170.666667 170.666667l0 682.666666q0 170.666667-170.666667 170.666667l-682.666666 0q-170.666667 0-170.666667-170.666667l0-682.666666q0-170.666667 170.666667-170.666667Z"
              fill="''' + getColor(0, color, colors, '#007EFE') + '''"
            />
            <path
              d="M768 319.431111a56.888889 56.888889 0 0 1 56.888889 56.888889v323.84a28.444444 28.444444 0 0 1-28.444445 28.444444h-67.84V540.444444a28.444444 28.444444 0 0 0-25.116444-28.245333L700.16 512H323.84a28.444444 28.444444 0 0 0-28.444444 28.444444l-0.028445 188.16H227.555556a28.444444 28.444444 0 0 1-28.444445-28.444444V376.348444a56.888889 56.888889 0 0 1 56.888889-56.888888h512z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
            <path
              d="M319.459556 536.064m56.888888 0l271.303112 0q56.888889 0 56.888888 56.888889l0 175.047111q0 56.888889-56.888888 56.888889l-271.303112 0q-56.888889 0-56.888888-56.888889l0-175.047111q0-56.888889 56.888888-56.888889Z"
              fill="''' + getColor(2, color, colors, '#D8EBFF') + '''"
            />
            <path
              d="M371.968 199.111111h284.444444a28.444444 28.444444 0 0 1 28.444445 28.444445v67.84h-341.333333V227.555556a28.444444 28.444444 0 0 1 28.444444-28.444445z"
              fill="''' + getColor(3, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.manjianzeng:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M156.20339 0h711.59322a104.135593 104.135593 0 0 1 104.135593 104.135593v121.491526H52.067797V104.135593a104.135593 104.135593 0 0 1 104.135593-104.135593z"
              fill="''' + getColor(0, color, colors, '#FFAA44') + '''"
            />
            <path
              d="M867.79661 121.491525a104.135593 104.135593 0 0 1 104.135593 104.135594v708.469152a86.779661 86.779661 0 0 1-116.892203 81.381966l-312.927458-115.764068a86.779661 86.779661 0 0 0-60.225084 0l-312.927458 115.764068A86.779661 86.779661 0 0 1 52.067797 934.078915V225.627119a104.135593 104.135593 0 0 1 104.135593-104.135594h711.59322z m-151.586712 406.840407c-12.982237 1.596746-26.259525 2.533966-39.866576 2.846373h-135.25478c-12.669831 0-25.634712-0.93722-38.912-2.846373 1.891797 12.669831 2.846373 25.634712 2.846373 38.929356v116.26739c0 10.760678-0.954576 21.347797-2.846373 31.796068 7.272136-0.954576 14.700475-1.423186 22.302373-1.423187 7.584542 0 15.186441 0.46861 22.770983 1.423187-1.266983-7.289492-2.204203-14.405424-2.846373-21.347797h129.093424a169.567458 169.567458 0 0 1-2.377763 21.347797c7.289492-0.954576 14.717831-1.423186 22.302373-1.423187 7.601898 0 15.186441 0.46861 22.788339 1.423187a261.848949 261.848949 0 0 1-2.846373-31.327458v-116.736c0-12.982237 0.93722-25.947119 2.846373-38.929356z m-255.323118-239.164746c-13.607051 1.562034-27.214102 2.51661-40.821153 2.829017H345.55661c-12.964881 0-26.259525-0.93722-39.866576-2.846372 1.909153 6.959729 2.846373 14.075661 2.846373 21.347796v257.214915c0 9.823458-0.93722 19.785763-2.846373 29.921627a165.054915 165.054915 0 0 1 43.198915 0 244.683932 244.683932 0 0 1-2.863729-29.904271V329.485017h74.509017v238.244881c0 8.851525-0.781017 17.87661-2.360406 27.057899-6.959729-6.647322-14.405424-13.138441-22.302373-19.473356 2.846373-18.657627 4.269559-37.957424 4.269559-57.89939v-125.275119c0-5.692746 2.048-9.806102 6.161356-12.340068 3.471186-2.533966 5.20678-5.380339 5.20678-8.539118 0-3.818305-3.297627-5.692746-9.94495-5.692746h-1.423186c-11.073085 0-23.725559-0.642169-37.97478-1.909153 1.57939 9.806102 2.377763 22.302373 2.377763 37.488814v116.284746c0 35.423458-5.553898 66.438508-16.609627 93.01044-12.669831 31.639864-32.108475 56.944814-58.368 75.932204 11.385492 2.204203 20.39322 5.067932 27.040542 8.539118 5.692746 3.15878 12.183864 9.025085 19.456 17.564204 28.168678-40.508746 45.715525-74.66522 52.675254-102.504136 22.788339 24.350373 41.445966 48.874305 56.007594 73.554441a73.571797 73.571797 0 0 1 17.564203-14.717831 85.599458 85.599458 0 0 1 19.438644-9.493694c-15.481492-19.612203-35.579661-40.178983-60.259796-61.682984a106.652203 106.652203 0 0 1 17.564203-1.423186c7.289492 0 14.544271 0.46861 21.833763 1.423186a244.683932 244.683932 0 0 1-2.846373-29.904271V315.253153c0-8.539119 0.93722-17.234441 2.846373-26.103322z m213.078779 340.263051v30.841492h-129.562034v-30.841492h129.562034z m0-64.564068v30.858848h-129.562034v-30.841492h129.562034z m-26.571932-303.242847c-1.266983 8.851525-3.176136 16.29722-5.692746 22.302373-4.113356 13.607051-10.292068 29.904271-18.518779 48.891661H567.191864c2.533966-1.909153 5.050576-3.471186 7.584543-4.755525a171.823729 171.823729 0 0 1 18.98739-9.025085 340.037424 340.037424 0 0 0-20.410577-30.372882c-8.226712-11.368136-14.700475-20.237017-19.438644-26.554576a147.612203 147.612203 0 0 1-16.141017 11.385492c-5.067932 3.15878-11.090441 6.803525-18.050169 10.916881 7.601898 8.226712 13.607051 15.655051 18.050169 22.302373 4.425763 6.005153 9.163932 14.717831 14.231865 26.103322h-16.609627a581.597288 581.597288 0 0 1-56.96217-2.846373c1.909153 19.299797 2.846373 38.599593 2.846373 57.89939v63.592135c0 19.299797-0.93722 38.599593-2.846373 57.89939a685.455186 685.455186 0 0 1 56.96217-2.377762h147.108881c18.98739 0 37.97478 0.798373 56.944814 2.377762a1213.457356 1213.457356 0 0 1-2.360407-57.89939v-63.592135c0-19.612203 0.781017-38.929356 2.360407-57.89939a836.729492 836.729492 0 0 1-56.927458 2.846373h-22.319729c5.380339-12.027661 10.916881-23.08339 16.609627-33.219254 3.471186-4.425763 7.601898-7.758102 12.340068-9.979661 4.425763-1.891797 6.647322-4.894373 6.647322-9.007729 0-3.800949-5.553898-6.959729-16.609627-9.493695a389.831593 389.831593 0 0 1-31.796068-9.493695z m-59.808542 106.791051v102.972746h-68.798916v-102.972746h68.798916z m112.015186 0v102.972746h-74.509017v-102.972746h74.509017z m-43.667525 18.970034a379.279186 379.279186 0 0 1-9.493695 33.219254c-2.533966 7.289492-7.758102 16.314576-15.655051 27.057898 6.647322 0.312407 12.652475 1.266983 18.032814 2.846373 5.380339 1.57939 10.587119 4.113356 15.65505 7.601899 4.425763-13.294644 8.070508-23.430508 10.934238-30.372882 2.829017-7.601898 7.410983-17.87661 13.745898-30.858847-5.380339-0.624814-10.760678-1.423186-16.141017-2.377763a67.341017 67.341017 0 0 1-17.078237-7.115932z m-96.325424 0.485966c-4.130712 2.204203-8.712678 4.425763-13.78061 6.629966-5.050576 1.596746-10.760678 2.690169-17.078237 3.332339 8.226712 17.390644 13.763254 35.440814 16.609627 54.098441a90.250847 90.250847 0 0 1 17.078237-5.692746c5.380339-1.57939 10.760678-2.690169 16.141017-3.314983a6836.328136 6836.328136 0 0 1-18.98739-55.053017z"
              fill="''' + getColor(1, color, colors, '#FF7744') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.tuige:
        svgXml = '''
          <svg viewBox="0 0 1554 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1554.962963 0v1024H429.018074L0 512 429.018074 0H1554.962963z m-94.814815 94.814815H473.27763l-349.56326 417.185185 349.56326 417.185185H1460.148148v-834.37037z m-355.896889 175.066074l67.053037 67.053037-175.104 175.066074 175.104 175.066074-67.053037 67.053037-175.066074-175.104-175.066074 175.104-67.053037-67.053037 175.104-175.066074-175.104-175.066074 67.053037-67.053037 175.066074 175.104 175.066074-175.104z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.zhifubao:
        svgXml = '''
          <svg viewBox="0 0 1280 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M815.4112 635.904c87.552-158.5664 118.8864-309.6576 118.8864-309.6576h-293.9904V214.7328h349.3376V166.912h-349.3376V0H480.9216v166.8096h-317.952v47.872h317.952v111.5136H210.944v47.872h548.352c0 8.2432 0 8.2432-8.2432 15.7184 0 55.296-39.68 134.6048-71.8848 199.0144-404.6336-159.3856-524.3904-63.5904-555.7248-47.9232-270.08 190.7712-15.7184 429.4144 23.9104 421.1712 285.696 63.5904 469.0944-55.296 596.224-206.4384 8.2944 8.2432 15.7184 8.2432 23.9616 8.2432 87.552 47.9232 508.7232 246.1184 508.7232 246.1184v-238.6944c-63.5904-0.8192-293.9904-80.0768-460.8-135.424z m-206.4896 71.0144c-199.0144 254.3616-436.8384 175.104-476.4672 158.5664-94.976-23.9616-127.1808-199.0144-8.2432-254.3616 199.0144-63.5904 373.248 8.2944 500.4288 71.8336a98.6624 98.6624 0 0 1-15.7184 23.9616z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.weixin:
        svgXml = '''
          <svg viewBox="0 0 1170 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M216.746667 1024.048762l127.463619-73.630476c9.606095-5.510095 19.797333-8.97219 31.012571-8.972191 5.851429 0 11.654095 0.975238 17.066667 2.633143 59.489524 17.066667 123.66019 26.575238 190.073905 26.575238 321.682286 0 582.363429-217.33181 582.363428-485.278476 0-81.237333-24.088381-157.647238-66.267428-224.938667L427.934476 647.655619l-4.291047 2.389333a38.765714 38.765714 0 0 1-51.395048-15.701333l-2.535619-5.558857-106.008381-232.594286a19.407238 19.407238 0 0 1 29.159619-23.747047l125.123048 89.039238a58.12419 58.12419 0 0 0 51.687619 5.90019L1058.133333 205.531429C952.661333 81.237333 778.922667 0 582.363429 0 260.778667 0 0 217.33181 0 485.376c0 146.18819 78.457905 277.845333 201.191619 366.738286a38.863238 38.863238 0 0 1 16.384 31.695238 46.470095 46.470095 0 0 1-2.048 12.336762c-9.801143 36.571429-25.551238 95.134476-26.233905 97.962666-1.219048 4.534857-3.169524 9.264762-3.169524 14.140953"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.yinhangka:
        svgXml = '''
          <svg viewBox="0 0 1293 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 248.293053v82.782315h1287.437474V248.293053H0z m707.206737 439.565473h-72.434526l-77.770106 152.629895h74.213053l75.991579-152.629895z m160.929684 0h-72.434526l-74.213053 152.629895h74.213053l72.434526-152.629895z m300.409263 0H953.936842l-71.518316 152.629895h210.997895l75.129263-152.629895zM1136.316632 0c40.798316 0.538947 79.602526 17.677474 107.627789 47.427368 28.456421 30.127158 44.032 70.170947 43.493053 111.669895v674.977684c0.538947 41.498947-15.090526 81.542737-43.493053 111.72379-28.025263 29.696-66.829474 46.780632-107.627789 47.373474H152.899368a150.797474 150.797474 0 0 1-107.627789-47.373474 159.690105 159.690105 0 0 1-43.493053-111.72379V159.097263A155.216842 155.216842 0 0 1 151.983158 0h984.333474z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.juhe:
        svgXml = '''
          <svg viewBox="0 0 1170 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M422.765714 0a97.52381 97.52381 0 0 1 88.551619 56.661333l20.333715 44.080762a48.761905 48.761905 0 0 0 44.275809 28.379429H1072.761905a97.52381 97.52381 0 0 1 97.523809 97.523809v691.004953a97.52381 97.52381 0 0 1-97.523809 97.523809H97.52381a97.52381 97.52381 0 0 1-97.52381-97.523809V97.52381a97.52381 97.52381 0 0 1 97.52381-97.52381h325.241904z m75.093334 298.666667H383.609905l128.048762 239.225904H414.573714v68.169143h118.393905v58.855619H414.573714v68.169143h118.393905v98.791619h108.446476v-98.791619h114.639238v-68.169143h-114.590476V606.110476h114.590476v-68.169143h-94.98819l131.803428-239.225904h-108.397714L606.98819 478.354286c-6.436571 15.11619-11.117714 26.721524-14.140952 34.767238h-1.316571l-93.622857-214.454857z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.xianjin:
        svgXml = '''
          <svg viewBox="0 0 1724 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M936.495638 48.662261l205.512348 355.951304c1.335652 2.31513 2.537739 4.630261 3.606261 6.989913h47.415652c45.412174 0 82.231652 36.819478 82.231652 82.187131v411.024695c0 45.412174-36.819478 82.231652-82.231652 82.231653H288.793377c-6.277565 0-12.466087-0.712348-18.342956-2.092522l8.904348-5.342609a82.098087 82.098087 0 0 1-62.820174-40.648348L11.022247 582.967652a82.18713 82.18713 0 0 1 30.096696-112.283826L824.211812 18.565565a82.18713 82.18713 0 0 1 112.283826 30.096696z m175.415652 468.23513l-0.890434 0.445218 0.178087 2.982956a61.662609 61.662609 0 0 0 81.92 52.224v253.640348a61.662609 61.662609 0 0 0-78.714435 78.714435H439.811117l-71.012174 40.96 824.230956 0.044522a41.093565 41.093565 0 0 0 40.826435-36.285218l0.311652-4.808348v-411.024695a41.093565 41.093565 0 0 0-36.329739-40.826435l-4.808348-0.267131h-40.292174a82.098087 82.098087 0 0 1-40.826435 64.200348zM849.054943 52.001391l-4.274087 2.181566-783.137392 452.11826a41.093565 41.093565 0 0 0-17.185391 51.867826l2.181565 4.274087 205.512348 355.951305c10.50713 18.253913 32.946087 25.377391 51.823304 17.185391l4.318609-2.137043L1091.386769 481.28a41.004522 41.004522 0 0 0 19.945739-28.627478h-0.489739l0.044521-16.11687a40.870957 40.870957 0 0 0-2.31513-7.123478l-2.137043-4.274087-205.512348-355.951304a41.093565 41.093565 0 0 0-51.867826-17.229913z m-108.054261 811.76487a164.418783 164.418783 0 0 0 154.134261-221.718261l-83.033044 47.905391 0.044522 18.120348h-31.387826l-15.849739 9.126957v15.36h47.237565v28.404869h-47.193044v41.093565H720.20903v-41.093565l-31.031653-0.044522-74.885565 43.275131a164.062609 164.062609 0 0 0 126.664348 59.570087zM797.231638 129.113043a61.662609 61.662609 0 0 0 107.52 28.850087l126.842435 219.670261a61.662609 61.662609 0 0 0-28.805565 107.52L355.976682 858.557217a61.662609 61.662609 0 0 0-107.564522-28.805565l-126.842435-219.670261a61.662609 61.662609 0 0 0 28.805565-107.564521L797.231638 129.113043z m251.993044 488.047305a61.662609 61.662609 0 1 0 0 123.325217 61.662609 61.662609 0 0 0 0-123.325217z m-780.777739-40.381218a61.662609 61.662609 0 1 0 61.662608 106.807653 61.662609 61.662609 0 0 0-61.662608-106.807653z m225.947826-225.28a164.418783 164.418783 0 1 0 164.418782 284.761044 164.418783 164.418783 0 0 0-164.418782-284.761044z m115.845565 27.959653l-15.760696 81.341217 33.881044-19.589565 14.246956 24.620522-40.915478 23.596521 12.243478 21.23687 40.870957-23.596522 14.246956 24.576-40.915478 23.596522 20.569044 35.617391-38.733914 22.305391-20.524521-35.617391-42.251131 24.397913-14.157913-24.576 42.206609-24.397913-12.243478-21.192348-42.251131 24.353392-14.157913-24.531479 34.593392-20.034782-76.978087-27.826087 40.781913-23.507478 59.570087 26.000695 0.445217-0.311652a13.089391 13.089391 0 0 1 0-2.137043l0.311652-4.541218c0.667826-8.370087 2.404174-24.442435 5.164522-48.172522l1.068522-9.260521 38.689391-22.349913z m192.155826-110.948174A61.662609 61.662609 0 1 0 864.014247 375.318261a61.662609 61.662609 0 0 0-61.618087-106.807652z"
              fill="''' + getColor(0, color, colors, '#2c2c2c') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shanchu_1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 0c282.770286 0 512 229.229714 512 512s-229.229714 512-512 512S0 794.770286 0 512 229.229714 0 512 0z m0 73.142857C269.641143 73.142857 73.142857 269.641143 73.142857 512s196.498286 438.857143 438.857143 438.857143 438.857143-196.498286 438.857143-438.857143S754.358857 73.142857 512 73.142857z m127.451429 256l51.712 51.712-129.28 129.316572 129.28 129.28-51.712 51.712-129.28-129.28-129.316572 129.28L329.142857 639.451429l129.316572-129.28L329.142857 380.854857 380.854857 329.142857l129.316572 129.316572L639.451429 329.142857z"
              fill="''' + getColor(0, color, colors, '#979797') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shanchu_2:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z"
              fill="''' + getColor(0, color, colors, '#5C5C5C') + '''"
              opacity=".983"
            />
            <path
              d="M342.488615 291.643077l169.472 169.472 169.55077-169.472 50.845538 50.806154L562.845538 512l169.511385 169.511385-50.806154 50.845538-169.590154-169.472-169.472 169.472-50.845538-50.806154L461.075692 512 291.643077 342.488615l50.806154-50.845538z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.banjia:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M156.20339 0h711.59322a104.135593 104.135593 0 0 1 104.135593 104.135593v138.847458H52.067797V104.135593a104.135593 104.135593 0 0 1 104.135593-104.135593z"
              fill="''' + getColor(0, color, colors, '#9CD2FF') + '''"
            />
            <path
              d="M867.79661 121.491525a104.135593 104.135593 0 0 1 104.135593 104.135594v708.469152a86.779661 86.779661 0 0 1-116.892203 81.381966l-312.927458-115.764068a86.779661 86.779661 0 0 0-60.225084 0l-312.927458 115.764068A86.779661 86.779661 0 0 1 52.067797 934.078915V225.627119a104.135593 104.135593 0 0 1 104.135593-104.135594h711.59322z m-370.844203 143.464136c1.57939 10.760678 2.377763 24.506576 2.377762 41.289763v130.030644h-123.869288a330.109831 330.109831 0 0 1-42.712949-2.846373c0.954576 6.942373 1.423186 14.058305 1.423187 21.347797 0 6.959729-0.46861 14.075661-1.423187 21.347796 14.231864-1.562034 28.463729-2.360407 42.695593-2.360407h123.886644v70.708068h-154.242169a330.109831 330.109831 0 0 1-42.712949-2.846373c0.954576 6.959729 1.423186 14.075661 1.423186 21.347797 0 6.977085-0.46861 14.093017-1.423186 21.365152 14.231864-1.57939 28.463729-2.377763 42.695593-2.377762h154.259525v89.209491c0 16.47078-0.954576 32.924203-2.863728 49.377627 7.289492-0.954576 14.717831-1.423186 22.319728-1.423186 7.584542 0 15.186441 0.46861 22.770984 1.423186a664.142102 664.142102 0 0 1-2.846373-49.360271v-89.209491h159.466305c14.231864 0 28.463729 0.781017 42.695593 2.360406-1.249627-7.289492-1.891797-14.405424-1.891797-21.347796 0-7.289492 0.642169-14.405424 1.909153-21.365153-13.936814 1.57939-28.168678 2.533966-42.712949 2.846373h-159.466305v-70.708068h127.184271c14.231864 0 28.463729 0.781017 42.712949 2.377763-1.266983-7.289492-1.909153-14.405424-1.909153-21.347797 0-7.289492 0.642169-14.405424 1.909153-21.365152-13.919458 1.562034-28.151322 2.51661-42.695593 2.846373h-127.201627V296.265763c0-6.317559 2.221559-10.760678 6.647322-13.277288 4.425763-2.533966 6.491119-5.866305 6.161356-9.979661 0-4.425763-3.644746-6.473763-10.916882-6.161356h-1.891796c-12.027661 0-25.947119-0.624814-41.758373-1.909153z m143.325288 28.949695c-1.596746 7.896949-4.269559 16.141017-8.070509 24.66278-3.176136 8.226712-10.135864 20.722983-20.879186 37.488813-11.715254 18.362576-22.146169 32.282034-31.327458 41.775729 8.539119 0 16.141017 1.423186 22.770983 4.269559 6.647322 2.221559 12.340068 6.005153 17.095594 11.385492 21.521356-30.372881 38.44339-57.89939 50.783457-82.579526 3.471186-4.425763 7.584542-7.896949 12.322712-10.430915 4.443119-1.909153 6.647322-4.911729 6.647322-9.025085-0.312407-3.783593-6.005153-6.803525-17.078237-9.025084-10.760678-1.891797-21.521356-4.738169-32.282034-8.521763z m-251.053559 0.93722c-5.067932 4.113356-10.587119 8.382915-16.609628 12.826034a133.987797 133.987797 0 0 1-18.518779 12.322712c13.294644 12.982237 23.569356 24.367729 30.858847 34.191186 9.493695 11.697898 20.080814 29.244746 31.796068 52.657899a144.036881 144.036881 0 0 1 19.438644-14.231865c6.334915-3.800949 12.84339-7.289492 19.473356-10.430915a380.077559 380.077559 0 0 0-34.17383-46.513898 6368.794034 6368.794034 0 0 0-32.282034-40.821153z"
              fill="''' + getColor(1, color, colors, '#359EF4') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.cuxiaojia:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M156.20339 0h711.59322a104.135593 104.135593 0 0 1 104.135593 104.135593v138.847458H52.067797V104.135593a104.135593 104.135593 0 0 1 104.135593-104.135593z"
              fill="''' + getColor(0, color, colors, '#98E196') + '''"
            />
            <path
              d="M867.79661 121.491525a104.135593 104.135593 0 0 1 104.135593 104.135594v708.469152a86.779661 86.779661 0 0 1-116.892203 81.381966l-312.927458-115.764068a86.779661 86.779661 0 0 0-60.225084 0l-312.927458 115.764068A86.779661 86.779661 0 0 1 52.067797 934.078915V225.627119a104.135593 104.135593 0 0 1 104.135593-104.135594h711.59322z m-475.726102 145.355933l-9.962305 30.841491c-6.959729 20.254373-17.87661 45.889085-32.750644 76.88678a469.026712 469.026712 0 0 1-59.322576 92.07322c6.334915 2.533966 11.871458 7.272136 16.609627 14.231865 5.380339 6.005153 9.493695 12.808678 12.322712 20.410576a948.224 948.224 0 0 0 36.082983-56.007593v224.950237c0 14.561627-0.954576 29.591864-2.846373 45.090712 7.289492-0.954576 14.717831-1.423186 22.302373-1.423187 7.601898 0 15.186441 0.46861 22.788339 1.423187a547.440814 547.440814 0 0 1-2.846373-45.090712V366.991186a2998.757966 2998.757966 0 0 0 25.148746-57.899389c3.800949-4.443119 8.382915-7.601898 13.763254-9.493695 4.738169-1.57939 7.428339-4.425763 8.070509-8.539119 0-4.443119-5.553898-8.226712-16.609627-11.402847-10.760678-3.471186-21.694915-7.740746-32.750645-12.808678z m303.260204 20.410576c-13.607051 1.562034-27.526508 2.533966-41.775729 2.846373h-140.930169c-13.624407 0-27.231458-0.954576-40.821153-2.846373 1.891797 13.607051 2.846373 27.196746 2.846373 40.803797v87.803661c0 13.607051-0.954576 27.370305-2.846373 41.289762a361.402576 361.402576 0 0 1 40.803797-2.377762h53.16122v199.801491a117.360814 117.360814 0 0 1-32.750644-25.166102c-9.806102-11.055729-20.56678-29.886915-32.282034-56.458847 4.443119-13.294644 9.025085-28.168678 13.78061-44.604746 2.51661-5.380339 5.848949-9.979661 9.962305-13.78061 3.800949-2.846373 5.20678-6.317559 4.26956-10.430915-1.266983-4.425763-7.758102-6.178712-19.456-5.20678-11.715254 0.295051-23.430508 0.138847-35.128407-0.485966 0.642169 8.226712 0 17.234441-1.909153 27.040542-5.03322 25.322305-13.277288 52.206644-24.645423 80.687729a252.928 252.928 0 0 1-46.999865 79.247187c7.601898 1.266983 15.186441 3.15878 22.788339 5.692745 8.539119 2.533966 16.609627 5.866305 24.19417 9.979661a3299.640407 3299.640407 0 0 0 33.219254-69.770847c1.57939-3.800949 3.15878-7.914305 4.755525-12.322712 9.476339 17.390644 18.34522 30.372881 26.554577 38.894644a143.325288 143.325288 0 0 0 55.538983 34.642441c26.571932 9.181288 63.904542 14.717831 111.99783 16.609627a1322.695593 1322.695593 0 0 0 38.912 0.485966c2.533966-9.181288 5.692746-18.189017 9.493695-27.057898 3.800949-9.181288 9.493695-17.703051 17.095593-25.617356-26.259525 4.113356-44.934508 6.629966-56.007593 7.584542-33.531661 2.221559-61.544136 2.221559-84.002712 0v-103.441356h64.546712c14.231864 0 28.463729 0.781017 42.695593 2.360407a518.248136 518.248136 0 0 1-1.40583-21.347797c0-7.289492 0.46861-14.405424 1.423186-21.365152-13.919458 1.57939-28.168678 2.533966-42.712949 2.846373h-64.546712v-68.816271h48.423051c13.902102 0 27.821559 0.798373 41.758373 2.377762a460.348746 460.348746 0 0 1-2.863729-41.307118v-87.786305c0-13.919458 0.954576-27.526508 2.846373-40.803797z m-42.244339 40.335186v89.695458h-139.055729v-89.695458h139.055729z"
              fill="''' + getColor(1, color, colors, '#77D175') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.tejia:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M156.20339 0h711.59322a104.135593 104.135593 0 0 1 104.135593 104.135593v138.847458H52.067797V104.135593a104.135593 104.135593 0 0 1 104.135593-104.135593z"
              fill="''' + getColor(0, color, colors, '#FFAA44') + '''"
            />
            <path
              d="M867.79661 121.491525a104.135593 104.135593 0 0 1 104.135593 104.135594v708.469152a86.779661 86.779661 0 0 1-116.892203 81.381966l-312.927458-115.764068a86.779661 86.779661 0 0 0-60.225084 0l-312.927458 115.764068A86.779661 86.779661 0 0 1 52.067797 934.078915V225.627119a104.135593 104.135593 0 0 1 104.135593-104.135594h711.59322z m-484.751186 147.247729c1.57939 10.760678 2.377763 24.523932 2.377762 41.307119v69.267525h-34.17383c1.57939-9.163932 2.690169-18.032814 3.332339-26.554576 2.533966-4.755525 5.848949-8.712678 9.962305-11.871458 3.471186-2.533966 4.894373-5.692746 4.269559-9.493695-0.624814-3.471186-6.334915-5.380339-17.078237-5.692745a622.401085 622.401085 0 0 1-31.796068-2.846373c0 9.806102-0.329763 19.612203-0.954576 29.418305-0.642169 12.652475-3.002576 29.418305-7.115932 50.297491a268.530983 268.530983 0 0 1-20.879187 63.123526c6.629966 1.266983 12.964881 4.113356 18.970034 8.539119 6.334915 4.113356 11.871458 9.337492 16.609627 15.672406 8.226712-21.521356 14.561627-45.403119 18.98739-71.68v-1.40583h39.866576v96.325424l-59.791186 20.410576c-8.226712 3.15878-18.831186 5.067932-31.796068 5.692746 6.317559 14.874034 11.854102 28.637288 16.609627 41.307118 1.909153 5.03322 5.20678 6.942373 9.962305 5.67539a15.030237 15.030237 0 0 0 8.070509-7.115932c1.562034-3.800949 5.050576-6.647322 10.430915-8.539119l46.513898-17.078237v115.79878c0 16.436068-0.954576 32.889492-2.846372 49.342915 7.289492-0.93722 14.717831-1.423186 22.302372-1.423187 7.601898 0 15.186441 0.485966 22.788339 1.423187a664.142102 664.142102 0 0 1-2.846372-49.342915V539.248814l24.662779-9.025085a139.021017 139.021017 0 0 1 25.166102-6.161356l-3.818305-22.770983a74.595797 74.595797 0 0 1 0-23.725559c-12.635119 7.896949-23.864407 13.763254-33.670509 17.546847l-12.340067 4.269559v-82.562169h13.277288c14.231864 0 28.463729 0.781017 42.712949 2.360407a518.248136 518.248136 0 0 1-1.423187-21.347797c0-7.289492 0.46861-14.405424 1.423187-21.347797-13.919458 1.562034-28.151322 2.51661-42.695593 2.829017h-13.294644V300.084068c0-6.334915 2.204203-10.760678 6.629966-13.294644 4.443119-2.51661 6.491119-5.848949 6.178712-9.962305 0-4.425763-3.644746-6.473763-10.934238-6.161356h-1.87444c-12.027661 0-25.947119-0.642169-41.775729-1.909153z m189.839186-3.783593c1.57939 10.760678 2.377763 24.506576 2.377763 41.289763v25.617356H534.909831a330.109831 330.109831 0 0 1-42.695594-2.846373c0.93722 6.959729 1.405831 14.075661 1.405831 21.347796 0 6.977085-0.46861 14.093017-1.423187 21.365153 14.231864-1.57939 28.481085-2.377763 42.71295-2.377763h40.352542v55.538983h-57.448136a330.109831 330.109831 0 0 1-42.695593-2.863729c0.954576 6.959729 1.423186 14.093017 1.423187 21.347797 0 6.977085-0.46861 14.093017-1.423187 21.365153 14.231864-1.562034 28.463729-2.360407 42.695593-2.360407h116.284746v49.811525h-110.557288a330.109831 330.109831 0 0 1-42.730305-2.829017c0.954576 6.942373 1.423186 14.058305 1.423186 21.347797 0 6.959729-0.46861 14.075661-1.423186 21.347797 14.231864-1.562034 28.463729-2.360407 42.712949-2.360407h110.574644v93.027796c0 8.851525-2.846373 15.169085-8.539119 18.970034-6.005153 3.15878-15.655051 4.911729-28.949695 5.20678-8.851525 0-20.56678-0.295051-35.11105-0.93722 9.163932 7.914305 15.186441 15.498847 18.032813 22.788339 3.471186 7.272136 6.473763 15.967458 9.025085 26.103322 36.048271 0 58.83661-4.113356 68.330305-12.340068 11.073085-9.198644 16.609627-21.521356 16.609627-37.020204v-115.798779h24.680136c14.231864 0 28.463729 0.781017 42.695593 2.377762-1.249627-7.289492-1.891797-14.405424-1.891797-21.347796 0-7.289492 0.642169-14.405424 1.909153-21.365153-13.936814 1.562034-28.168678 2.51661-42.712949 2.846373h-24.680136V462.362034h26.554576c14.24922 0 28.498441 0.781017 42.730306 2.360407-1.266983-7.272136-1.909153-14.405424-1.909153-21.347797 0-7.289492 0.642169-14.405424 1.909153-21.347797-13.919458 1.562034-28.168678 2.51661-42.71295 2.829017h-85.425898v-55.521627h53.16122c14.231864 0 28.463729 0.798373 42.695594 2.377763-1.249627-7.289492-1.891797-14.405424-1.891797-21.347797 0-7.289492 0.642169-14.405424 1.909153-21.365152-13.936814 1.57939-28.168678 2.533966-42.71295 2.846373h-53.16122v-35.579661c0-6.334915 2.221559-10.760678 6.647322-13.294644 4.425763-2.533966 6.491119-5.866305 6.161356-9.979661 0-4.425763-3.644746-6.473763-10.916881-6.161356h-1.891797c-12.027661 0-25.947119-0.624814-41.758373-1.909153zM541.557153 569.621695c-6.005153 3.176136-12.652475 6.491119-19.924611 9.979661-6.022508 2.51661-13.138441 5.380339-21.347796 8.539119 6.629966 10.118508 11.697898 18.970034 15.169085 26.571932 4.425763 8.851525 8.851525 21.677559 13.294644 38.44339a135.202712 135.202712 0 0 1 22.770983-9.962305c7.601898-2.533966 15.186441-4.755525 22.788339-6.647323a240.587932 240.587932 0 0 0-16.609628-35.597016 1074.366915 1074.366915 0 0 0-16.141016-31.327458z"
              fill="''' + getColor(1, color, colors, '#FF7744') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.manjian:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M156.20339 0h711.59322a104.135593 104.135593 0 0 1 104.135593 104.135593v156.20339H52.067797V104.135593a104.135593 104.135593 0 0 1 104.135593-104.135593z"
              fill="''' + getColor(0, color, colors, '#FFD06C') + '''"
            />
            <path
              d="M867.79661 121.491525a104.135593 104.135593 0 0 1 104.135593 104.135594v708.469152a86.779661 86.779661 0 0 1-116.892203 81.381966l-312.927458-115.764068a86.779661 86.779661 0 0 0-60.225084 0l-312.927458 115.764068A86.779661 86.779661 0 0 1 52.067797 934.078915V225.627119a104.135593 104.135593 0 0 1 104.135593-104.135594h711.59322z m-291.111051 145.355933c1.562034 10.760678 2.360407 24.523932 2.360407 41.289762v33.219255h-115.79878c-19.612203 0-39.536814-0.954576-59.791186-2.846373 1.909153 20.56678 2.846373 41.602169 2.846373 63.123525v66.438509c0 47.763525-3.957153 89.053288-11.854102 123.851932-6.334915 30.702644-22.146169 62.342508-47.468474 94.936949 9.493695 0.295051 18.050169 2.048 25.634711 5.20678 7.584542 3.15878 13.919458 8.382915 18.970034 15.65505 25.634712-52.831458 41.133559-95.232 46.513899-127.184271 5.380339-32.906847 7.914305-70.395661 7.601898-112.46644v-89.209492h133.345627c1.57939 90.771525 7.115932 153.912407 16.609627 189.35322 3.471186 11.697898 7.115932 23.256949 10.934238 34.625085-4.130712 4.113356-8.244068 8.712678-12.357424 13.763254-19.612203 20.56678-52.206644 41.602169-97.765966 63.123526 10.448271 5.380339 17.564203 10.292068 21.347796 14.71783 5.380339 6.005153 9.511051 12.496271 12.357424 19.438644 37.957424-26.242169 68.816271-50.766102 92.541831-73.537084 6.647322 12.652475 14.075661 24.194169 22.302372 34.64244 20.56678 25.304949 36.378034 38.113627 47.451119 38.426034h10.934237c11.385492 0 21.03539-5.692746 28.932339-17.078237 8.226712-12.027661 15.030237-33.063051 20.410577-63.123525a145.928678 145.928678 0 0 1-23.72556-6.629967c-7.289492-3.488542-15.82861-9.823458-25.634712-18.987389 0 15.811254-1.266983 30.199322-3.783593 43.181559-1.266983 7.289492-4.599322 11.073085-9.979661 11.385491-4.113356 0-11.854102-8.695322-23.256949-26.103322a223.197288 223.197288 0 0 1-14.700475-28.949695c10.118508-15.186441 19.299797-33.219254 27.526509-54.09844 10.118508-24.350373 19.143593-57.101017 27.040542-98.234576 0.954576-5.380339 3.644746-9.025085 8.070509-10.916882 4.425763-2.533966 5.848949-5.536542 4.269559-9.025085-1.266983-4.425763-6.317559-6.786169-15.186441-7.115932a185.448136 185.448136 0 0 1-36.065627-5.692746 1465.048949 1465.048949 0 0 0-2.846373 37.488814c-2.533966 26.901695-9.493695 53.629831-20.879186 80.219119-2.221559 5.380339-4.911729 10.916881-8.070509 16.609627a231.285153 231.285153 0 0 1-3.314983-14.231865c-6.959729-30.060475-11.385492-84.488678-13.294644-163.267254h60.277153c14.231864 0 28.463729 0.798373 42.695593 2.377763a518.248136 518.248136 0 0 1-1.40583-21.347797c0-7.289492 0.46861-14.405424 1.423186-21.365152-13.919458 1.57939-28.168678 2.533966-42.712949 2.846373h-60.745763V298.174915c0-6.334915 2.204203-10.760678 6.647322-13.294644 4.425763-2.533966 6.473763-5.848949 6.161356-9.962305 0-4.425763-3.644746-6.491119-10.916881-6.178712h-1.909153c-12.010305 0-25.929763-0.624814-41.741017-1.891796zM341.738305 460.001627a576.216949 576.216949 0 0 1-7.115932 41.289763c-2.204203 15.498847-7.428339 36.864-15.655051 64.060746-6.005153 20.254373-12.183864 37.662373-18.501424 52.206644-3.176136 6.959729-7.445695 14.717831-12.826034 23.256949a227.189153 227.189153 0 0 1 35.597017 20.410576c4.738169 3.15878 8.851525 3.15878 12.322712 0a16.488136 16.488136 0 0 0 2.863729-12.340068 132.824949 132.824949 0 0 1-0.46861-19.941966l36.534237-115.798779 6.647322-22.770984a39.450034 39.450034 0 0 0-9.025085-3.332339c-10.760678-3.783593-20.879186-12.808678-30.372881-27.040542z m228.282576 20.879187c-11.715254 1.57939-23.725559 2.533966-36.065627 2.846372H497.421017c-11.697898 0-23.413153-0.954576-35.111051-2.846372 1.909153 11.715254 2.846373 23.413153 2.846373 35.11105v65.032678c0 11.697898-0.954576 23.569356-2.846373 35.579661a269.155797 269.155797 0 0 1 35.111051-2.360406h36.551593c12.010305 0 24.037966 0.781017 36.065627 2.360406a343.97722 343.97722 0 0 1-2.846373-35.579661V515.991864c0-12.010305 0.93722-23.725559 2.846373-35.11105z m-34.64244 36.534237v63.123525h-38.44339v-63.106169h38.44339z m34.64244-100.126373c-12.027661 1.57939-24.367729 2.533966-37.020203 2.846373h-32.282034a249.404746 249.404746 0 0 1-37.002847-2.846373 186.020881 186.020881 0 0 1 0 37.020203c12.322712-1.57939 24.680136-2.377763 37.020203-2.377762h32.264678c12.322712 0 24.680136 0.798373 37.020203 2.377762-0.642169-6.334915-1.11078-12.496271-1.423186-18.518779 0-6.317559 0.46861-12.496271 1.423186-18.501424z m-233.020745-98.720542c-6.005153 4.443119-12.183864 8.556475-18.501424 12.340067-7.289492 3.471186-15.516203 6.178712-24.680136 8.070509a285.886915 285.886915 0 0 1 30.841492 39.866576c9.493695 14.561627 18.189017 30.216678 26.103322 46.982509a113.681356 113.681356 0 0 1 20.879186-13.277289c7.289492-3.488542 14.717831-5.710102 22.302373-6.647322a808.231051 808.231051 0 0 0-28.949695-46.513898c-11.697898-18.032814-21.03539-31.639864-27.995118-40.821152z m334.570305-50.297492a64.650847 64.650847 0 0 1-15.186441 10.448271c-6.005153 3.15878-12.652475 5.380339-19.92461 6.629966a202.769356 202.769356 0 0 1 24.680135 55.538983 75.671864 75.671864 0 0 1 19.438644-9.979661 102.764475 102.764475 0 0 1 19.004746-6.161356 1314.191186 1314.191186 0 0 1-28.012474-56.476203z"
              fill="''' + getColor(1, color, colors, '#F8AC1E') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.qingkong:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M783.061333 0l-0.085333 159.445333h188.544v87.04l-72.533333-0.085333v637.994667a130.304 130.304 0 0 1-118.570667 129.92l-11.861333 0.597333H246.528a130.346667 130.346667 0 0 1-129.877333-118.613333l-0.597334-11.818667V246.314667H43.52V159.445333l188.458667-0.085333V0h551.04z m28.928 246.4H203.008v637.994667c0 21.418667 15.445333 39.125333 35.754667 42.837333l7.765333 0.682667h522.026667a43.52 43.52 0 0 0 42.752-35.669334l0.682666-7.850666V246.4z m-376.917333 87.04V797.44h-87.04V333.482667h87.04z m231.978667 0V797.44h-87.04V333.482667h87.04z m28.928-246.485333H319.146667l-0.085334 72.490666h376.96V86.954667z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.guadan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M661.333333 0a85.333333 85.333333 0 0 1 85.12 78.933333L746.666667 85.333333h128a85.333333 85.333333 0 0 1 85.333333 85.333334v768a85.333333 85.333333 0 0 1-85.333333 85.333333H149.333333a85.333333 85.333333 0 0 1-85.333333-85.333333V170.666667a85.333333 85.333333 0 0 1 85.333333-85.333334h128a85.333333 85.333333 0 0 1 85.333334-85.333333h298.666666zM277.333333 170.666667H149.333333v768h725.333334V170.666667h-128a85.333333 85.333333 0 0 1-85.333334 85.333333H362.666667a85.333333 85.333333 0 0 1-85.333334-85.333333z m111.488 222.293333l4.010667 3.541333 119.168 119.125334 119.168-119.125334a42.666667 42.666667 0 0 1 56.32-3.541333l4.010667 3.541333a42.666667 42.666667 0 0 1 0 60.330667L572.373333 576l119.125334 119.168a42.666667 42.666667 0 0 1-56.32 63.872l-4.010667-3.541333-119.168-119.125334-119.168 119.125334a42.666667 42.666667 0 0 1-56.32 3.541333l-4.010667-3.541333a42.666667 42.666667 0 0 1 0-60.330667L451.626667 576l-119.125334-119.168a42.666667 42.666667 0 0 1 56.32-63.872zM661.333333 85.333333H362.666667v85.333334h298.666666V85.333333z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.cuxiao:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M652.48 28.896l292.64 0.192a64 64 0 0 1 63.936 63.968l0.192 292.64a64 64 0 0 1-18.752 45.28L431.36 990.144a64 64 0 0 1-90.496 0L48 697.28a64 64 0 0 1 0-90.528L607.168 47.68a64 64 0 0 1 45.28-18.752z m-0.064 64L93.28 652.064l292.8 292.8L945.28 385.728l-0.192-292.64-292.64-0.192zM320.512 578.88l30.656 30.624c-17.28 3.296-32.8 11.84-46.56 25.6-8.064 8.064-13.024 15.968-14.88 23.68-1.888 7.776-0.352 14.08 4.576 19.04a21.6 21.6 0 0 0 15.872 5.984c6.624 0.032 19.264-3.584 37.92-10.912 21.76-8.704 39.104-12.288 52.064-10.72 12.96 1.536 24.448 7.328 34.464 17.344 14.72 14.72 20.704 31.104 17.984 49.248-2.688 18.112-13.632 36.768-32.832 55.936-17.536 17.536-34.72 29.088-51.616 34.624l-32.704-32.736a90.56 90.56 0 0 0 57.344-26.688c8.896-8.896 14.048-17.12 15.456-24.672 1.44-7.552-0.256-13.728-5.088-18.56-4.16-4.16-9.856-6.272-17.088-6.4-7.264-0.128-21.664 4.256-43.2 13.12-34.08 13.792-60.8 11.008-80.192-8.384-14.24-14.24-19.872-30.72-16.928-49.44 2.976-18.72 13.376-36.992 31.264-54.88 14.944-14.944 29.44-25.568 43.52-31.808z m126.88-133.44l208.128 98.208-35.584 35.584-46.592-23.616-55.68 55.68 23.872 46.336-35.424 35.392-97.088-209.184 38.4-38.4z m6.816 45.984l-0.896 0.896c4.096 4.896 7.52 10.08 10.304 15.616l35.968 71.008 40.864-40.832-70.496-35.712a83.36 83.36 0 0 1-15.744-10.976z m100.16-152.96l126.464 126.464 57.248-57.248 26.688 26.72-89.856 89.856-153.152-153.152 32.64-32.64z m166.208-171.328l26.688 26.688L692.8 248.32l36.096 36.096 50.592-50.624 26.624 26.56-50.624 50.656 37.056 37.056 58.048-58.016 26.688 26.688-90.656 90.656-153.152-153.152 87.104-87.104z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shouyingyuan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 0m151.703704 0l720.592592 0q151.703704 0 151.703704 151.703704l0 720.592592q0 151.703704-151.703704 151.703704l-720.592592 0q-151.703704 0-151.703704-151.703704l0-720.592592q0-151.703704 151.703704-151.703704Z"
              fill="''' + getColor(0, color, colors, '#F1F4F9') + '''"
            />
            <path
              d="M827.031704 834.199704H184.111407C155.856593 834.199704 132.740741 811.093333 132.740741 782.829037V377.419852C132.740741 349.184 155.856593 326.049185 184.111407 326.049185h642.920297c28.254815 0 51.370667 23.134815 51.370666 51.370667V782.829037c0 28.254815-23.10637 51.370667-51.370666 51.370667"
              fill="''' + getColor(1, color, colors, '#EC6B52') + '''"
            />
            <path
              d="M810.922667 799.222519H206.203259a26.804148 26.804148 0 0 1-26.728296-26.728297V434.090667a26.804148 26.804148 0 0 1 26.737778-26.737778h604.709926a26.804148 26.804148 0 0 1 26.737777 26.737778v338.403555a26.804148 26.804148 0 0 1-26.737777 26.737778"
              fill="''' + getColor(2, color, colors, '#FFFFFF') + '''"
            />
            <path
              d="M464.459852 724.100741H249.618963a25.201778 25.201778 0 0 1-25.144889-25.125926V476.416a25.220741 25.220741 0 0 1 25.144889-25.144889h214.840889a25.220741 25.220741 0 0 1 25.125926 25.144889V698.974815a25.201778 25.201778 0 0 1-25.125926 25.125926"
              fill="''' + getColor(3, color, colors, '#F4D4D3') + '''"
            />
            <path
              d="M406.518519 675.375407h-19.664593a0.768 0.768 0 0 1-0.758519-0.768v-6.637037c0-1.185185 0.967111-2.152296 2.152297-2.152296h16.877037c1.204148 0 2.171259 0.967111 2.171259 2.152296v6.637037a0.768 0.768 0 0 1-0.777481 0.768M339.844741 629.238519l-5.764741 5.072592 13.814519 18.650074a2.247111 2.247111 0 0 0 3.546074 0.075852l7.613629-9.424593-19.209481-14.373925z"
              fill="''' + getColor(4, color, colors, '#FFFFFF') + '''"
            />
            <path
              d="M378.197333 629.200593l5.783704 5.063111-13.842963 18.678518a2.23763 2.23763 0 0 1-3.527111 0.066371l-7.623111-9.434074 19.209481-14.373926z"
              fill="''' + getColor(5, color, colors, '#FFFFFF') + '''"
            />
            <path
              d="M427.956148 668.814222c-2.522074-13.226667-25.780148-25.874963-46.629926-32.768-1.024-0.341333-7.376593-3.204741-3.39437-15.255703 10.382222-10.647704 18.270815-27.799704 18.270815-44.686223 0-25.941333-17.275259-39.547259-37.366519-39.547259-20.081778 0-37.319111 13.596444-37.319111 39.547259 0 16.943407 7.888593 34.152296 18.280296 44.781037 4.077037 10.638222-3.185778 14.601481-4.712296 15.122963-19.844741 7.149037-42.903704 19.844741-45.444741 32.777482-35.223704-38.267259-32.749037-97.848889 5.49926-133.072593 38.276741-35.223704 97.848889-32.749037 133.072592 5.518222a94.245926 94.245926 0 0 1 24.898371 63.715556 93.525333 93.525333 0 0 1-25.154371 63.867259"
              fill="''' + getColor(6, color, colors, '#EC6B52') + '''"
            />
            <path
              d="M524.439704 496.877037h279.978666v-27.420444H524.439704zM525.416296 572.444444h280.045037V551.632593H525.416296zM525.416296 640.455111h280.045037v-21.048889H525.416296zM525.416296 713.102222h168.941037v-20.385185H525.416296z"
              fill="''' + getColor(7, color, colors, '#CDD2D2') + '''"
            />
            <path
              d="M613.622519 372.726519H397.520593a14.411852 14.411852 0 0 1-14.364445-14.373926v-3.802074c0-7.907556 6.447407-14.373926 14.364445-14.373926h216.101926a14.411852 14.411852 0 0 1 14.364444 14.373926v3.802074a14.411852 14.411852 0 0 1-14.364444 14.373926"
              fill="''' + getColor(8, color, colors, '#CBCED7') + '''"
            />
            <path
              d="M604.700444 355.574519H403.418074L480.749037 157.392593A21.807407 21.807407 0 0 1 501.077333 143.502222h118.509037a21.816889 21.816889 0 0 1 21.418667 25.998222l-36.304593 186.074075z"
              fill="''' + getColor(9, color, colors, '#CED4DD') + '''"
            />
            <path
              d="M603.401481 354.294519H402.10963L377.742222 166.874074A21.82637 21.82637 0 0 1 399.378963 142.222222H556.657778c10.960593 0 20.224 8.144593 21.636741 19.010371l25.106962 193.061926z"
              fill="''' + getColor(10, color, colors, '#E5E8ED') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.linshilogo:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 0m190.511628 0l642.976744 0q190.511628 0 190.511628 190.511628l0 642.976744q0 190.511628-190.511628 190.511628l-642.976744 0q-190.511628 0-190.511628-190.511628l0-642.976744q0-190.511628 190.511628-190.511628Z"
              fill="''' + getColor(0, color, colors, '#2976FF') + '''"
            />
            <path
              d="M531.352806 278.940775L229.820527 418.427039V370.148217L530.733643 223.184372l263.453768 145.733457v61.590822L531.352806 278.940775zM865.24031 326.798884L532.749891 142.883721 158.75969 325.55262V833.488372h71.060837V496.941643L527.772775 359.114419l264.827039 152.726821 1.579659-2.746542v247.808h-289.180775c-55.565891 0-100.764775-45.365581-100.764776-101.114047s45.198884-101.106109 100.756838-101.106108h44.532093v128.793798h71.060837V483.375628H504.99076c-94.747783 0-171.817674 77.347721-171.817675 172.413023 0 95.07324 77.077829 172.420961 171.817675 172.420961h303.580279v-0.66679H865.24031V326.806822z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.tuxingyanzhengma:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M879.502222 101.902222H144.497778c-74.524444 0-135.111111 60.586667-135.111111 135.111111v550.044445c0 74.524444 60.586667 135.111111 135.111111 135.111111h735.075555c74.524444 0 135.111111-60.586667 135.111111-135.111111V237.013333c-0.071111-74.524444-60.657778-135.111111-135.182222-135.111111zM108.942222 786.986667v-94.577778l151.182222-72.248889c14.862222-7.111111 32.853333-2.773333 42.808889 10.311111l39.822223 52.124445a134.030222 134.030222 0 0 0 129.706666 50.915555l138.595556-23.893333c13.084444-2.275556 26.453333 3.2 34.204444 13.937778l71.68 99.128888h-572.444444c-19.626667-0.142222-35.555556-16.071111-35.555556-35.697777z m806.115556 0c0 19.626667-15.928889 35.555556-35.555556 35.555555h-39.822222l-113.848889-157.44c-29.866667-41.315556-81.635556-62.364444-131.84-53.688889l-138.595555 23.964445c-12.8 2.133333-25.742222-2.844444-33.635556-13.226667l-39.822222-52.124444c-38.4-50.275556-107.733333-66.986667-164.835556-39.68l-108.231111 51.768889V237.013333c0-19.626667 15.928889-35.555556 35.555556-35.555555h735.075555c19.626667 0 35.555556 15.928889 35.555556 35.555555v549.973334z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
            <path
              d="M704.071111 266.524444c-77.013333 0-139.733333 62.648889-139.733333 139.733334 0 77.013333 62.648889 139.733333 139.733333 139.733333 77.013333 0 139.733333-62.648889 139.733333-139.733333 0-77.013333-62.648889-139.733333-139.733333-139.733334z m0 179.911112c-22.115556 0-40.177778-17.991111-40.177778-40.177778a40.177778 40.177778 0 1 1 40.177778 40.177778z"
              fill="''' + getColor(1, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.yanjing_kai:
        svgXml = '''
          <svg viewBox="0 0 1536 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M768 0c282.752 0 538.752 170.666667 768 512-229.248 341.333333-485.248 512-768 512-282.752 0-538.752-170.666667-768-512C229.248 170.666667 485.248 0 768 0z m0 106.666667C541.184 106.666667 329.216 238.08 130.090667 512c199.125333 273.92 411.093333 405.333333 637.909333 405.333333 226.816 0 438.784-131.413333 637.909333-405.333333C1206.784 238.08 994.816 106.666667 768 106.666667zM768 256a256 256 0 1 1 0 512 256 256 0 0 1 0-512z m0 106.666667a149.333333 149.333333 0 1 0 0 298.666666 149.333333 149.333333 0 0 0 0-298.666666z"
              fill="''' + getColor(0, color, colors, '#979797') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.yanjing_guan:
        svgXml = '''
          <svg viewBox="0 0 2368 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M2254.528 1.152l98.944 125.696a1855.232 1855.232 0 0 1-206.272 140.992l202.112 350.016-138.624 80-204.8-354.88a1877.12 1877.12 0 0 1-257.472 100.544l163.584 404.736-148.352 59.904L1592.32 484.48a1920.832 1920.832 0 0 1-328.32 41.92v449.664h-160V526.336a1921.728 1921.728 0 0 1-313.792-38.848L620.16 908.16l-148.352-59.904L633.6 447.808a1877.696 1877.696 0 0 1-271.488-104.832l-204.8 354.88-138.624-80 202.176-350.08A1855.68 1855.68 0 0 1 41.856 148.032l-27.328-21.12L113.472 1.152C413.056 236.928 787.904 367.936 1184 367.936c396.16 0 770.944-131.008 1070.528-366.784z"
              fill="''' + getColor(0, color, colors, '#979797') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.yanzhengma:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M470.357333 546.787556l-111.047111-121.856a47.502222 47.502222 0 0 0-70.172444 63.943111l143.36 157.354666a47.445333 47.445333 0 0 0 67.441778 2.759111l263.537777-245.504a47.473778 47.473778 0 0 0-64.711111-69.489777l-228.408889 212.792889z"
              fill="''' + getColor(0, color, colors, '#120718') + '''"
            />
            <path
              d="M865.934222 549.518222c0 207.018667-292.266667 335.075556-363.235555 363.093334-71.281778-27.619556-365.084444-154.140444-365.084445-363.093334V224.426667c44.657778-31.374222 96.256-58.965333 153.656889-82.204445 140.430222-56.775111 301.056-53.816889 440.689778 8.135111 63.857778 28.330667 110.307556 57.770667 133.973333 74.126223v325.034666z m77.169778-386.190222c-2.616889-2.133333-65.706667-52.337778-172.629333-99.754667C607.431111-8.732444 419.811556-12.145778 255.687111 54.215111 182.243556 83.911111 116.878222 120.376889 61.411556 162.588444l-18.744889 14.222223v372.736c0 304.64 426.808889 452.579556 444.984889 458.723555l15.388444 5.205334 15.36-5.319112c18.062222-6.257778 442.453333-156.643556 442.453333-458.609777V177.578667l-17.749333-14.250667z"
              fill="''' + getColor(1, color, colors, '#120718') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shoujihao_copy:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M731.107556 923.648H288.284444a21.048889 21.048889 0 0 1-20.764444-20.764444v-185.344h484.323556v185.344a21.048889 21.048889 0 0 1-20.764445 20.764444M288.312889 96.853333h442.823111a21.048889 21.048889 0 0 1 20.764444 20.764445V620.657778H267.491556V117.617778a21.048889 21.048889 0 0 1 20.764444-20.764445M731.107556 0H288.284444A117.76 117.76 0 0 0 170.666667 117.617778v785.265778a117.76 117.76 0 0 0 117.617777 117.617777h442.823112a117.76 117.76 0 0 0 117.617777-117.617777V117.617778A117.76 117.76 0 0 0 731.107556 0"
              fill="''' + getColor(0, color, colors, '#120718') + '''"
            />
            <path
              d="M585.102222 779.719111H434.289778a48.440889 48.440889 0 1 0 0 96.853333h150.812444a48.440889 48.440889 0 1 0 0-96.853333"
              fill="''' + getColor(1, color, colors, '#120718') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.mima:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M889.258667 877.767111c0 19.143111-16.184889 35.328-35.299556 35.328H148.394667a35.356444 35.356444 0 0 1-35.299556-35.328V503.495111c0-19.484444 15.843556-35.328 35.299556-35.328h705.564444c19.114667 0 35.271111 16.184889 35.271111 35.328v374.272z m-608.113778-551.822222c0-121.315556 98.702222-220.017778 220.017778-220.017778 121.344 0 220.017778 98.702222 220.017777 220.017778v43.349333H281.144889v-43.349333z m572.814222 43.349333H820.053333v-43.349333C820.053333 150.101333 677.006222 7.054222 501.191111 7.054222c-175.843556 0-318.890667 143.075556-318.890667 318.862222v43.377778H148.394667A134.314667 134.314667 0 0 0 14.222222 503.495111v374.272a134.314667 134.314667 0 0 0 134.172445 134.172445h705.564444a134.314667 134.314667 0 0 0 134.172445-134.172445V503.495111a134.314667 134.314667 0 0 0-134.172445-134.200889z"
              fill="''' + getColor(0, color, colors, '#120718') + '''"
            />
            <path
              d="M501.191111 572.188444c-27.306667 0-49.464889 22.129778-49.464889 49.436445v153.912889a49.436444 49.436444 0 1 0 98.872889 0v-153.884445c0-27.306667-22.129778-49.464889-49.436444-49.464889"
              fill="''' + getColor(1, color, colors, '#120718') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.gongsiming:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M886.158364 895.544889a21.816889 21.816889 0 0 1-21.788444 21.788444h-245.020444V410.737778l261.774222 131.640889a9.102222 9.102222 0 0 1 5.034666 8.163555v345.031111z m-389.461333 21.788444H137.671253a21.816889 21.816889 0 0 1-21.788444-21.788444V317.44c0-8.163556 4.494222-15.559111 11.747555-19.342222L485.831253 111.559111a21.617778 21.617778 0 0 1 10.040889-2.588444 21.902222 21.902222 0 0 1 11.292445 3.271111c3.925333 2.389333 10.524444 7.964444 10.524444 18.631111v786.488889h-20.992z m430.136889-465.777777l-307.484444-154.652445V130.844444c0-43.52-22.215111-83.000889-59.448889-105.557333A122.510222 122.510222 0 0 0 438.869476 21.333333L80.668587 207.957333A123.050667 123.050667 0 0 0 14.222364 317.44v578.104889a123.591111 123.591111 0 0 0 123.448889 123.448889H864.36992a123.591111 123.591111 0 0 0 123.448889-123.448889V550.542222a110.279111 110.279111 0 0 0-60.984889-98.986666z"
              fill="''' + getColor(0, color, colors, '#120718') + '''"
            />
            <path
              d="M377.657031 433.208889h-121.742222a50.830222 50.830222 0 1 0 0 101.660444h121.742222a50.830222 50.830222 0 1 0 0-101.660444M377.657031 647.509333h-121.742222a50.830222 50.830222 0 1 0 0 101.688889h121.742222a50.830222 50.830222 0 1 0 0-101.688889"
              fill="''' + getColor(1, color, colors, '#120718') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.yonghuming:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M499.633778 98.872889a169.045333 169.045333 0 0 1 168.874666 168.846222 169.045333 169.045333 0 0 1-168.874666 168.846222 169.045333 169.045333 0 0 1-168.846222-168.846222 169.045333 169.045333 0 0 1 168.846222-168.846222m0 436.565333c147.626667 0 267.747556-120.092444 267.747555-267.719111S647.260444 0 499.633778 0 231.914667 120.092444 231.914667 267.719111s120.120889 267.719111 267.719111 267.719111"
              fill="''' + getColor(0, color, colors, '#120718') + '''"
            />
            <path
              d="M100.529778 920.433778C124.622222 771.612444 253.617778 660.821333 405.624889 660.821333h188.074667c152.007111 0 281.031111 110.762667 305.095111 259.612445H100.529778z m898.133333 0.398222C973.859556 716.231111 799.779556 561.976889 593.642667 561.976889H405.624889C199.516444 561.976889 25.436444 716.231111 0.632889 920.860444a87.893333 87.893333 0 0 0 87.352889 98.417778h823.324444a87.893333 87.893333 0 0 0 87.324445-98.446222z"
              fill="''' + getColor(1, color, colors, '#120718') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shezhi:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M580.906667 35.584l36.309333 8.106667c53.930667 12.074667 105.088 33.365333 151.466667 62.762666l31.402666 19.925334-15.402666 33.834666c-8.362667 18.261333-2.773333 44.117333 16.128 62.976 18.858667 18.901333 44.714667 24.490667 62.933333 16.213334l33.792-15.402667 19.925333 31.317333a475.733333 475.733333 0 0 1 62.848 151.552l8.149334 36.394667-34.986667 12.928c-18.688 6.912-33.066667 29.098667-33.066667 55.808s14.378667 48.981333 33.109334 55.978667l34.858666 12.970666-8.106666 36.266667a476.970667 476.970667 0 0 1-62.72 151.466667l-19.925334 31.445333-33.834666-15.488c-18.218667-8.32-44.117333-2.730667-62.976 16.170667-18.901333 18.858667-24.490667 44.757333-16.170667 62.976l15.445333 33.792-31.402666 19.925333a476.885333 476.885333 0 0 1-151.466667 62.805333l-36.352 8.106667-12.970667-34.901333c-6.954667-18.773333-29.184-33.109333-55.893333-33.109334s-48.981333 14.378667-55.978667 33.152l-12.970666 34.858667-36.309334-8.106667a477.013333 477.013333 0 0 1-151.466666-62.805333l-31.445334-19.925333 15.488-33.877334c8.362667-18.176 2.773333-44.032-16.128-62.890666-18.858667-18.901333-44.757333-24.490667-62.976-16.170667l-33.792 15.445333-19.925333-31.402666a476.8 476.8 0 0 1-62.805333-151.466667l-8.106667-36.352 34.901333-12.970667c18.773333-6.954667 33.109333-29.184 33.109334-55.893333s-14.378667-48.981333-33.152-55.978667l-34.816-12.970666 8.106666-36.266667a476.928 476.928 0 0 1 62.72-151.466667l19.968-31.488 33.877334 15.530667c18.176 8.32 44.032 2.730667 62.890666-16.170667 18.901333-18.858667 24.490667-44.757333 16.213334-62.976l-15.488-33.792 31.402666-19.925333a476.885333 476.885333 0 0 1 151.509334-62.805333l36.394666-8.106667 12.928 34.901333c6.954667 18.773333 29.141333 33.109333 55.893334 33.109334 24.405333 0 45.312-12.032 53.248-27.221334l1.578666-3.114666 14.037334-37.674667z m47.402666 99.285333c-26.666667 33.621333-69.717333 54.058667-116.309333 54.058667-46.762667 0-89.728-20.608-116.266667-54.058667a391.424 391.424 0 0 0-68.138666 28.245334c4.864 42.410667-11.008 87.381333-44.032 120.448-33.066667 33.024-78.037333 48.853333-120.448 43.989333a391.381333 391.381333 0 0 0-28.245334 68.138667c33.450667 26.581333 54.058667 69.546667 54.058667 116.309333s-20.608 89.728-54.058667 116.309333c7.253333 23.594667 16.725333 46.378667 28.245334 68.096 42.410667-4.821333 87.381333 11.008 120.448 44.074667 33.066667 33.024 48.896 77.994667 43.989333 120.405333 21.76 11.52 44.544 20.992 68.138667 28.245334 26.538667-33.450667 69.546667-54.058667 116.309333-54.058667s89.770667 20.608 116.309333 54.058667a391.381333 391.381333 0 0 0 68.096-28.245334c-4.821333-42.410667 11.008-87.381333 44.074667-120.405333 33.024-33.066667 77.994667-48.896 120.405333-44.032 11.52-21.76 20.992-44.544 28.245334-68.138667-33.450667-26.581333-54.058667-69.546667-54.058667-116.309333s20.608-89.770667 54.101333-116.266667a390.229333 390.229333 0 0 0-28.288-68.138666c-42.453333 4.821333-87.381333-11.008-120.405333-44.032-33.066667-33.066667-48.896-77.994667-44.032-120.448a391.808 391.808 0 0 0-68.138667-28.245334zM512 338.176a173.824 173.824 0 1 1 0 347.648 173.824 173.824 0 0 1 0-347.648z m0 85.333333a88.490667 88.490667 0 1 0 0 176.981334 88.490667 88.490667 0 0 0 0-176.981334z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.guan:
        svgXml = '''
          <svg viewBox="0 0 1894 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M507.913394 0h863.466863c280.519633 0 507.913394 227.393761 507.913394 507.913394s-227.393761 507.922789-507.913394 507.922789H507.913394C227.393761 1015.826789 0 788.442422 0 507.913394 0 227.393761 227.393761 0 507.913394 0z"
              fill="''' + getColor(0, color, colors, '#D8D8D8') + '''"
            />
            <path
              d="M75.766606 507.913394a432.146789 432.146789 0 1 0 864.293577 0c0-238.667156-193.470239-432.146789-432.146789-432.146788-238.667156 0-432.146789 193.479633-432.146788 432.146788z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.kai_1:
        svgXml = '''
          <svg viewBox="0 0 1894 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M507.913394 0h863.466863c280.519633 0 507.913394 227.393761 507.913394 507.913394s-227.393761 507.922789-507.913394 507.922789H507.913394C227.393761 1015.826789 0 788.442422 0 507.913394 0 227.393761 227.393761 0 507.913394 0z"
              fill="''' + getColor(0, color, colors, '#2769FF') + '''"
            />
            <path
              d="M939.233468 507.913394c0 238.67655 193.479633 432.146789 432.146789 432.146789s432.146789-193.470239 432.146789-432.146789a432.146789 432.146789 0 0 0-864.293578 0z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.danxuankuangxuanzhong:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 0c282.7776 0 512 229.2224 512 512s-229.2224 512-512 512S0 794.7776 0 512 229.2224 0 512 0z m213.3248 332.8L486.4 560.8704l-136.5248-130.304L281.6 495.6928l136.5248 130.304L486.4 691.2l68.2752-65.152L793.6 397.952 725.3248 332.8z"
              fill="''' + getColor(0, color, colors, '#2769FF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.danxuankuangweixuanzhong:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 1024c282.7776 0 512-229.2224 512-512S794.7776 0 512 0 0 229.2224 0 512s229.2224 512 512 512z m0-51.2c-254.4896 0-460.8-206.3104-460.8-460.8S257.5104 51.2 512 51.2s460.8 206.3104 460.8 460.8-206.3104 460.8-460.8 460.8z"
              fill="''' + getColor(0, color, colors, '#BFBFC1') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.rili:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M365.714286 18.285714v73.142857H676.571429v-73.142857h91.428571v73.142857H932.571429a73.142857 73.142857 0 0 1 73.142857 73.142858v768a73.142857 73.142857 0 0 1-73.142857 73.142857H91.428571a73.142857 73.142857 0 0 1-73.142857-73.142857v-768a73.142857 73.142857 0 0 1 73.142857-73.142858h182.857143v-73.142857h91.428572z m-256 429.677715V914.285714h804.571428V448l-804.571428-0.036571zM274.285714 182.857143H109.714286v173.677714l804.571428 0.036572V182.857143h-146.285714v54.857143H676.571429V182.857143H365.714286v54.857143H274.285714V182.857143z"
              fill="''' + getColor(0, color, colors, '#9A9A9A') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.xiaopiao:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M208.641656 1024L194.069858 1008.103493V16.356181L208.48799 0 222.908771 16.356181 237.325578 0l14.418132 16.356181L266.163167 0l14.418132 16.356181L294.999431 0l14.419456 16.356181L323.837019 0l14.419457 16.356181L352.674608 0l14.418132 16.356181L381.512197 0l14.418132 16.356181L410.348461 0l14.419456 16.356181L439.186049 0l14.418132 16.356181L468.023638 0l14.418132 16.356181L496.859902 0l14.419456 16.356181L525.69749 0l14.418132 16.356181L554.536404 0l14.418132 16.356181L583.372668 0l14.418131 16.356181L612.208931 0l14.419457 16.356181L641.04652 0l14.418132 16.356181h1.442608L671.325392 0l14.418132 16.356181L700.162981 0l14.418132 16.356181L729.000569 0l14.418132 16.356181L757.836833 0l14.419457 16.356181L786.674422 0l14.418132 16.356181L815.51201 0 829.930142 16.356181V1008.103493l-14.571798 15.896507-14.571798-15.896507-14.571798 15.896507-13.909444-15.174541L758.39586 1024l-14.571798-15.896507-14.571798 15.896507-14.571798-15.896507-14.571798 15.896507-14.571799-15.896507-14.571798 15.896507-13.909444-15.174541L643.146184 1024l-14.571798-15.896507-14.571799 15.896507-14.571798-15.896507-14.571798 15.896507-14.571798-15.896507-14.571798 15.896507-14.571799-15.896507-14.571798 15.896507-13.909444-15.174541L498.752911 1024l-14.571798-15.896507-14.571799 15.896507-14.571798-15.896507-14.571798 15.896507-14.571798-15.896507-14.571798 15.896507-13.909444-15.174541L383.503234 1024l-14.571798-15.896507h-1.324709l-14.571798 15.896507-14.571798-15.896507-14.571799 15.896507-14.571798-15.896507-14.571798 15.896507-14.571798-15.896507-14.571798 15.896507-13.909444-15.174541L237.785252 1024l-14.571798-15.896507z"
              fill="''' + getColor(0, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.xiaoshoudingdan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M689.230769 0a98.461538 98.461538 0 0 1 96.492308 78.769231h48.521846c59.076923 0 107.283692 46.395077 110.788923 104.763077l0.196923 6.813538v722.077539a111.340308 111.340308 0 0 1-104.211692 111.379692l-6.774154 0.196923H189.755077a111.261538 111.261538 0 0 1-110.788923-104.763077L78.769231 912.384V190.345846a111.340308 111.340308 0 0 1 104.211692-111.379692L189.755077 78.769231H238.276923A98.461538 98.461538 0 0 1 334.769231 0h354.461538zM787.692308 177.230769A98.461538 98.461538 0 0 1 689.230769 275.692308h-354.461538A98.461538 98.461538 0 0 1 236.307692 177.230769V157.538462H189.755077a32.452923 32.452923 0 0 0-31.980308 28.672L157.538462 190.345846v722.077539c0 16.777846 12.366769 30.523077 28.199384 32.571077l4.017231 0.236307h644.489846a32.452923 32.452923 0 0 0 31.980308-28.672l0.236307-4.135384V190.345846a32.610462 32.610462 0 0 0-28.199384-32.571077L834.244923 157.538462H787.692308v19.692307zM472.615385 708.923077a39.384615 39.384615 0 0 1 0 78.769231H315.076923a39.384615 39.384615 0 0 1 0-78.769231h157.538462z m236.307692-157.538462a39.384615 39.384615 0 0 1 0 78.769231H315.076923a39.384615 39.384615 0 0 1 0-78.769231h393.846154z m0-157.538461a39.384615 39.384615 0 0 1 0 78.769231H315.076923a39.384615 39.384615 0 0 1 0-78.769231h393.846154z m-19.692308-315.076923h-354.461538a19.692308 19.692308 0 0 0-19.692308 19.692307v78.769231a19.692308 19.692308 0 0 0 19.692308 19.692308h354.461538a19.692308 19.692308 0 0 0 19.692308-19.692308v-78.769231a19.692308 19.692308 0 0 0-19.692308-19.692307z"
              fill="''' + getColor(0, color, colors, '#A0A8B3') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shangchuan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 0A70.892308 70.892308 0 0 1 582.892308 70.892308V441.107692h370.215384a70.892308 70.892308 0 1 1 0 141.784616H582.876554L582.892308 953.107692a70.892308 70.892308 0 1 1-141.784616 0L441.091938 582.892308H70.892308a70.892308 70.892308 0 1 1 0-141.784616H441.107692V70.892308A70.892308 70.892308 0 0 1 512 0z"
              fill="''' + getColor(0, color, colors, '#2769FF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.fupingshezhi:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 0m170.666667 0l682.666666 0q170.666667 0 170.666667 170.666667l0 682.666666q0 170.666667-170.666667 170.666667l-682.666666 0q-170.666667 0-170.666667-170.666667l0-682.666666q0-170.666667 170.666667-170.666667Z"
              fill="''' + getColor(0, color, colors, '#FE8900') + '''"
            />
            <path
              d="M227.555556 256m56.888888 0l483.555556 0q56.888889 0 56.888889 56.888889l0 341.333333q0 56.888889-56.888889 56.888889l-483.555556 0q-56.888889 0-56.888888-56.888889l0-341.333333q0-56.888889 56.888888-56.888889Z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
            <path
              d="M537.6 555.605333l184.888889 246.528a14.222222 14.222222 0 0 1-11.377778 22.755556H341.333333a14.222222 14.222222 0 0 1-11.377777-22.755556l184.888888-246.528a14.222222 14.222222 0 0 1 22.755556 0z"
              fill="''' + getColor(2, color, colors, '#FFFFFF') + '''"
              opacity=".722"
            />
          </svg>
        ''';
        break;
      case IconNames.tongyongshezhi:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 0m170.666667 0l682.666666 0q170.666667 0 170.666667 170.666667l0 682.666666q0 170.666667-170.666667 170.666667l-682.666666 0q-170.666667 0-170.666667-170.666667l0-682.666666q0-170.666667 170.666667-170.666667Z"
              fill="''' + getColor(0, color, colors, '#2F6DFB') + '''"
            />
            <path
              d="M580.750222 199.111111c14.449778 0 26.168889 11.548444 26.168889 25.799111v69.290667a242.887111 242.887111 0 0 1 49.976889 27.335111l59.960889-34.076444a26.396444 26.396444 0 0 1 35.783111 9.443555l68.721778 117.304889a25.6 25.6 0 0 1-9.585778 35.271111l-56.604444 32.227556a237.283556 237.283556 0 0 1 0.768 61.041777l55.836444 31.772445a25.6 25.6 0 0 1 9.585778 35.271111l-68.721778 117.304889a26.396444 26.396444 0 0 1-35.783111 9.443555l-54.983111-31.288888a242.915556 242.915556 0 0 1-54.926222 31.004444v62.833778a25.998222 25.998222 0 0 1-26.197334 25.799111h-137.500444a25.998222 25.998222 0 0 1-26.168889-25.799111v-65.592889a242.915556 242.915556 0 0 1-51.2-30.350222l-58.737778 33.393777a26.396444 26.396444 0 0 1-35.783111-9.443555l-68.721778-117.304889a25.6 25.6 0 0 1 9.585778-35.271111l62.008889-35.271111a238.023111 238.023111 0 0 1 0.682667-54.101334l-62.691556-35.669333a25.6 25.6 0 0 1-9.585778-35.271111l68.721778-117.304889a26.396444 26.396444 0 0 1 35.783111-9.443555l63.630222 36.181333a242.915556 242.915556 0 0 1 46.279111-26.680889V224.910222c0-14.222222 11.747556-25.799111 26.197334-25.799111h137.500444z"
              fill="''' + getColor(1, color, colors, '#FFFFFF') + '''"
            />
            <path
              d="M512 512m-113.777778 0a113.777778 113.777778 0 1 0 227.555556 0 113.777778 113.777778 0 1 0-227.555556 0Z"
              fill="''' + getColor(2, color, colors, '#A7C2FF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.bunengbianji:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M118.153846 0h787.692308a118.153846 118.153846 0 0 1 118.153846 118.153846v787.692308a118.153846 118.153846 0 0 1-118.153846 118.153846H118.153846a118.153846 118.153846 0 0 1-118.153846-118.153846V118.153846a118.153846 118.153846 0 0 1 118.153846-118.153846z m0 59.076923A59.076923 59.076923 0 0 0 59.076923 118.153846v787.692308A59.076923 59.076923 0 0 0 118.153846 964.923077h787.692308a59.076923 59.076923 0 0 0 59.076923-59.076923V118.153846A59.076923 59.076923 0 0 0 905.846154 59.076923H118.153846z"
              fill="''' + getColor(0, color, colors, '#E6E6E6') + '''"
            />
            <path
              d="M726.567385 292.588308l77.981538 77.981538-351.074461 351.113846-234.06277-234.062769L297.432615 409.6l156.002462 156.041846 273.132308-273.053538z"
              fill="''' + getColor(1, color, colors, '#BFBFBF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.weixuanzhong:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M118.153846 0h787.692308a118.153846 118.153846 0 0 1 118.153846 118.153846v787.692308a118.153846 118.153846 0 0 1-118.153846 118.153846H118.153846a118.153846 118.153846 0 0 1-118.153846-118.153846V118.153846a118.153846 118.153846 0 0 1 118.153846-118.153846z m0 59.076923A59.076923 59.076923 0 0 0 59.076923 118.153846v787.692308A59.076923 59.076923 0 0 0 118.153846 964.923077h787.692308a59.076923 59.076923 0 0 0 59.076923-59.076923V118.153846A59.076923 59.076923 0 0 0 905.846154 59.076923H118.153846z"
              fill="''' + getColor(0, color, colors, '#BFBFC1') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.xuanzhong:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M905.846154 0a118.153846 118.153846 0 0 1 118.153846 118.153846v787.692308a118.153846 118.153846 0 0 1-118.153846 118.153846H118.153846a118.153846 118.153846 0 0 1-118.153846-118.153846V118.153846a118.153846 118.153846 0 0 1 118.153846-118.153846h787.692308z m-179.278769 292.588308l-273.132308 273.053538-155.963077-156.041846-78.060308 78.020923 234.06277 234.062769L804.627692 370.609231l-78.020923-78.020923z"
              fill="''' + getColor(0, color, colors, '#2C68FF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.zengpin:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M420.864 60.371478c43.853913 26.35687 78.892522 61.350957 101.509565 99.238957 22.706087-37.62087 57.566609-72.347826 101.153392-98.526609 106.852174-64.200348 234.585043-58.234435 285.072695 25.778087 26.579478 44.254609 24.041739 98.214957-1.113043 148.925217H912.695652a89.043478 89.043478 0 0 1 89.043478 89.043479v195.005217a89.043478 89.043478 0 0 1-77.913043 88.375652v288.189218c0 61.840696-49.107478 111.927652-110.547478 115.712l-7.435131 0.222608H218.156522c-62.374957 0-113.886609-47.727304-117.76-108.588521l-0.222609-7.346087V608.166957A89.043478 89.043478 0 0 1 22.26087 519.880348V324.830609a89.043478 89.043478 0 0 1 89.043478-89.043479h25.911652C111.749565 184.854261 109.078261 130.626783 135.791304 86.149565 186.234435 2.137043 313.878261-3.873391 420.864 60.371478zM467.478261 608.879304H189.217391v287.521392c0 13.222957 10.462609 24.620522 24.620522 26.579478l4.318609 0.311652H467.478261v-314.368z m367.304348 0H556.521739v314.412522h249.321739c14.781217 0 26.624-10.195478 28.627479-22.973217l0.311652-3.917913v-287.47687zM467.478261 324.87513H111.304348v195.005218h356.173913v-195.005218z m445.217391 0h-356.173913v195.005218h356.173913v-195.005218zM212.101565 132.006957c-14.024348 23.329391-3.739826 64.111304 32.189218 102.711652l-1.202087 1.068521h216.108521c-8.45913-32.634435-37.798957-71.190261-84.190608-99.105391-69.765565-41.850435-142.60313-38.466783-162.905044-4.674782z m457.238261 5.431652c-46.08 27.692522-75.330783 65.892174-84.012522 98.348521h215.129044l-0.400696-0.356173c33.658435-36.196174 44.833391-74.306783 34.54887-98.125914l-2.359652-4.585739c-20.301913-33.747478-93.139478-37.175652-162.860522 4.719305z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.huiyuan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M515.564468 0a290.891852 290.891852 0 0 1 162.057482 532.404148A496.526222 496.526222 0 0 1 1005.036468 1000.25837h-85.143704c0-227.328-182.689185-411.45837-407.893333-411.45837-225.204148 0-407.893333 184.13037-407.893333 411.496296H18.962394c0-217.505185 138.808889-402.394074 332.117333-469.560889A290.891852 290.891852 0 0 1 515.526542 0z m0 85.143704a205.710222 205.710222 0 1 0 0 411.45837 205.710222 205.710222 0 0 0 0-411.496296z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.shanchu:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M781.979826 0l-0.089043 158.809043h187.792695v86.728348l-72.258782-0.089043v635.458782a129.825391 129.825391 0 0 1-118.071653 129.380174l-11.842782 0.578783H247.585391a129.825391 129.825391 0 0 1-129.380174-118.116174l-0.578782-11.753739V245.314783H45.412174V158.809043l187.703652-0.089043V0h548.864z m28.805565 245.448348H204.221217v635.458782c0 21.370435 15.404522 39.001043 35.617392 42.696348l7.746782 0.667826H767.554783c20.925217 0 38.867478-15.003826 42.607304-35.572869l0.667826-7.791305V245.448348zM435.422609 332.132174v462.135652H348.649739V332.132174H435.422609z m231.067826 0v462.135652h-86.728348V332.132174h86.728348z m28.805565-245.49287H319.844174l-0.089044 72.125218h375.496348v-72.125218z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.caidan:
        svgXml = '''
          <svg viewBox="0 0 1246 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1246.608696 896v116.869565H0v-116.869565h1246.608696z m0-467.478261v116.869565H0V428.521739h1246.608696zM1246.608696 0v116.869565H0V0h1246.608696z"
              fill="''' + getColor(0, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.youhuiquan:
        svgXml = '''
          <svg viewBox="0 0 1210 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1210.181818 0v360.122182a146.664727 146.664727 0 1 0 0 291.933091v360.075636H0V652.101818a146.664727 146.664727 0 1 0 0-291.933091V0h1210.181818z m-87.970909 87.970909H87.970909v195.072a234.821818 234.821818 0 0 1 0 446.045091v195.025455h1034.24v-195.025455a234.821818 234.821818 0 0 1 0-446.045091v-195.025454z m-271.406545 513.442909v88.017455h-484.072728v-87.970909h484.072728z m0-300.683636v87.970909h-484.072728v-87.970909h484.072728z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.xiaoxi:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M682.678857 910.226286V1024H341.321143v-113.773714h341.357714zM501.540571 0.146286L512 0c216.868571 0 392.557714 178.176 398.08 399.250286l0.146286 10.752-0.036572 272.64H1024v113.810285H0v-113.773714l113.773714-0.036571v-272.64C113.773714 187.574857 286.025143 5.851429 501.540571 0.146286L512 0z m19.309715 113.773714L512 113.773714c-153.709714 0-279.625143 127.268571-284.306286 286.976l-0.146285 9.252572v272.64h568.868571v-272.64c0-160.914286-122.989714-291.218286-275.565714-296.082286L512 113.773714z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.sousuo:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M193.8432 153.31328c164.16768-164.16768 430.32576-164.16768 594.49344 0 164.16768 164.16768 164.16768 430.32576 0 594.49344l6.02112-6.18496 189.11232 189.11232-57.91744 57.91744-193.57696-193.49504c-163.96288 114.93376-391.65952 99.1232-538.13248-47.34976-164.16768-164.16768-164.16768-430.32576 0-594.49344z m536.576 57.91744A338.45248 338.45248 0 1 0 251.76064 689.88928 338.45248 338.45248 0 0 0 730.4192 211.23072z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.dayinshangdan:
        svgXml = '''
          <svg viewBox="0 0 1066 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M865.024 0c28.757333 0 52.053333 23.296 52.053333 52.053333v234.112h62.933334c46.976 0 85.546667 37.034667 86.613333 83.541334l0.042667 2.005333v401.28c0 47.445333-38.997333 85.546667-86.656 85.546667h-95.445334v78.08a52.053333 52.053333 0 0 1-52.053333 52.010666H234.154667a52.053333 52.053333 0 0 1-52.053334-52.053333v-78.037333H86.656c-46.976 0-85.546667-37.034667-86.613333-83.541334L0 772.992V371.712C0 324.266667 38.997333 286.165333 86.656 286.165333h62.933333V52.053333c0-28.757333 23.296-52.053333 52.053334-52.053333h663.381333z m-58.538667 689.450667H260.181333v221.098666h546.304V689.493333z m173.525334-325.205334H86.613333c-4.949333 0-8.618667 3.541333-8.618666 7.466667v401.28c0 3.925333 3.669333 7.509333 8.618666 7.509333h95.445334v-117.077333c0-28.757333 23.296-52.053333 52.053333-52.053333h598.357333c28.757333 0 52.053333 23.296 52.053334 52.053333v117.077333h95.445333c4.650667 0 8.106667-3.157333 8.533333-6.784l0.085334-0.725333V371.712c0-3.925333-3.669333-7.466667-8.618667-7.466667zM871.125333 426.666667a53.333333 53.333333 0 1 1 0 106.666666 53.333333 53.333333 0 0 1 0-106.666666z m-32.085333-348.586667H227.626667v208.085333h611.413333V78.08z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.zhuanshulaoshi:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 0c186.272 0 336.768 123.968 390.336 309.44A102.816 102.816 0 0 1 992 411.424v205.728a102.848 102.848 0 0 1-102.848 102.848h-24.864c-61.376 122.72-165.44 211.008-288.256 234.048a64.032 64.032 0 0 1-59.456 40.224h-43.424a64 64 0 0 1-64-64v-9.12a64 64 0 0 1 64-64h43.424c22.08 0 41.536 11.168 53.024 28.16 90.176-19.168 168.928-82.656 220.704-172.352a68.576 68.576 0 0 1-38.304-61.536v-274.272c0-37.888 30.72-68.576 68.576-68.576l9.696-0.032c-49.888-146.048-170.816-240-318.272-240s-268.384 93.952-318.272 240h9.696c37.888 0 68.576 30.72 68.576 68.608v274.272c0 37.888-30.72 68.576-68.576 68.576H134.848A102.848 102.848 0 0 1 32 617.152v-205.728A102.88 102.88 0 0 1 121.664 309.44C175.232 123.968 325.728 0 512 0zM203.424 377.152H134.848c-18.912 0-34.272 15.36-34.272 34.272v205.728c0 18.912 15.36 34.272 34.272 34.272h68.576v-274.272z m685.728 0h-68.576v274.272h68.576c18.912 0 34.272-15.36 34.272-34.272v-205.728c0-18.944-15.36-34.272-34.272-34.272z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.lianjie_copy:
        svgXml = '''
          <svg viewBox="0 0 1365 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M643.73696825 774.13149551a87.3770575 87.3770575 0 0 1 71.54601122 37.07882483L643.73696825 882.78857422l-71.5460112-71.57825388a87.3770575 87.3770575 0 0 1 71.57825388-37.07882483z m0-200.67705082a287.40925919 287.40925919 0 0 1 214.08991252 95.27645968l-67.12879461 67.06430999a193.03558784 193.03558784 0 0 0-146.96111791-67.64467448 193.03558784 193.03558784 0 0 0-146.96111717 67.64467448L429.67929841 668.69866171a287.40925919 287.40925919 0 0 1 214.08991252-95.24421701z m2.57939651-221.27998021c145.34899435 0 276.41458134 61.19618249 368.85370457 159.21325134l-62.06672888 62.03448693a418.0234509 418.0234509 0 0 0-306.78697569-133.54825619 418.05569358 418.05569358 0 0 0-309.23740149 136.19213733l-62.06672962-62.03448695a505.46499304 505.46499304 0 0 1 371.30413111-161.85713246z m0-210.96239417C849.9274786 141.21207032 1033.70948211 226.00973203 1164.35591652 362.16962596l-62.87279067 62.90503333A626.79335776 626.79335776 0 0 0 653.99006968 237.9394409a626.98681236 626.98681236 0 0 0-457.52046033 197.51728906L125.76190185 364.78126515A715.78253807 715.78253807 0 0 1 646.31636476 141.21207032z"
              fill="''' + getColor(0, color, colors, '#FFFFFF') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.gengduo:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M99.555556 71.111111c-48.355556 0-85.333333 36.977778-85.333334 85.333333s36.977778 85.333333 85.333334 85.333334 85.333333-36.977778 85.333333-85.333334-36.977778-85.333333-85.333333-85.333333zM99.555556 426.666667c-48.355556 0-85.333333 36.977778-85.333334 85.333333s36.977778 85.333333 85.333334 85.333333 85.333333-36.977778 85.333333-85.333333-36.977778-85.333333-85.333333-85.333333zM99.555556 782.222222c-48.355556 0-85.333333 36.977778-85.333334 85.333334s36.977778 85.333333 85.333334 85.333333 85.333333-36.977778 85.333333-85.333333-36.977778-85.333333-85.333333-85.333334zM327.111111 213.333333h625.777778c31.288889 0 56.888889-25.6 56.888889-56.888889s-25.6-56.888889-56.888889-56.888888h-625.777778c-31.288889 0-56.888889 25.6-56.888889 56.888888s25.6 56.888889 56.888889 56.888889zM952.888889 455.111111h-625.777778c-31.288889 0-56.888889 25.6-56.888889 56.888889s25.6 56.888889 56.888889 56.888889h625.777778c31.288889 0 56.888889-25.6 56.888889-56.888889s-25.6-56.888889-56.888889-56.888889zM952.888889 810.666667h-625.777778c-31.288889 0-56.888889 25.6-56.888889 56.888889s25.6 56.888889 56.888889 56.888888h625.777778c31.288889 0 56.888889-25.6 56.888889-56.888888s-25.6-56.888889-56.888889-56.888889z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.zaixiankefu_1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M955.485091 451.956364C952.630303 216.064 760.676848 25.755152 524.039758 25.755152 287.340606 25.724121 95.418182 216.157091 92.563394 451.956364a172.373333 172.373333 0 0 0-65.070546 134.919757 172.683636 172.683636 0 0 0 282.344728 133.430303L228.38303 416.705939c-9.18497-1.458424-18.618182-2.482424-28.237575-2.482424-6.361212 0-12.598303 0.310303-18.711273 1.024 20.759273-170.728727 166.136242-303.166061 342.574545-303.16606 176.407273 0 321.784242 132.437333 342.543515 303.259151a169.363394 169.363394 0 0 0-18.711272-1.024c-9.619394 0-19.052606 1.024-28.237576 2.482424l-81.299394 303.476364a172.00097 172.00097 0 0 0 81.640727 36.988121 345.274182 345.274182 0 0 1-151.148606 95.107879l-7.136969 7.13697a107.582061 107.582061 0 0 0-94.456243-56.661334 107.892364 107.892364 0 1 0 0 215.846788 107.364848 107.364848 0 0 0 102.958546-77.451636 432.438303 432.438303 0 0 0 276.200727-212.774788 172.218182 172.218182 0 0 0 74.069333-141.591273 172.094061 172.094061 0 0 0-64.977454-134.919757z"
              fill="''' + getColor(0, color, colors, '#333333') + '''"
            />
          </svg>
        ''';
        break;
      case IconNames.yujing:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M925.013333 743.537778l-50.631111-104.675556a48.298667 48.298667 0 0 1-5.12-21.617778V448.284444c0-142.222222-89.884444-263.964444-215.608889-311.751111A142.222222 142.222222 0 0 0 512 1.706667c-75.662222 0-137.671111 59.733333-142.222222 134.257777-125.724444 48.355556-215.608889 169.528889-215.608889 312.32v168.96c0 7.395556-1.706667 14.791111-5.12 21.617778l-50.631111 104.675556c-13.084444 27.875556-11.377778 59.733333 4.551111 85.902222s44.373333 41.528889 75.662222 41.528889h148.48c17.066667 85.902222 93.297778 151.324444 184.32 151.324444 91.022222 0 167.253333-64.853333 184.32-151.324444h148.48c30.72 0 59.164444-15.36 75.662222-41.528889 16.497778-26.168889 18.773333-58.595556 5.12-85.902222zM512 87.04c21.048889 0 38.115556 11.946667 48.355556 28.444444-7.964444-0.568889-15.928889-1.137778-24.462223-1.137777h-47.786666c-8.533333 0-16.497778 0.568889-24.462223 1.137777 10.24-16.497778 27.306667-28.444444 48.355556-28.444444z m0 849.92c-43.804444 0-80.782222-27.306667-95.573333-65.991111h191.715555c-15.36 38.684444-52.337778 65.991111-96.142222 65.991111z m332.8-151.324444H179.2l-3.413333-5.12 50.631111-104.675556c8.533333-18.204444 13.653333-38.684444 13.653333-59.164444V448.284444c0-137.102222 111.502222-248.604444 248.604445-248.604444h47.786666c137.102222 0 248.604444 111.502222 248.604445 248.604444v168.96c0 20.48 4.551111 40.96 13.653333 59.164445l50.631111 104.675555-4.551111 4.551112z"
              fill="''' + getColor(0, color, colors, '#020202') + '''"
            />
          </svg>
        ''';
        break;

    }

    return SvgPicture.string(svgXml, width: this.size, height: this.size);
  }
}
