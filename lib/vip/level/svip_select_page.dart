import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import '../../common/num_extension.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/haloui.dart';

import '../../bill/tool/decimal_display_helper.dart';
import '../../bill/tool/promotion/equity_value_type.dart';
import '../../common/tool/sp_tool.dart';
import '../../iconfont/icon_font.dart';
import '../../plugin/secondary_screen_windows.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../add/vip_settlement.dart';
import '../entity/card_coupon_card_template.dart';
import '../entity/get_vip_level_score_rights_card_response.dart';
import '../entity/vip_level_dto.dart';
import '../model/vip_model.dart';
import '../utils/svip_util.dart';

///付费会员等级选择界面
class SVipLevelSelectPage extends BaseStatefulPage {
  ///会员id
  final String vipId;

  const SVipLevelSelectPage({Key? key, required this.vipId}) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _SVipLevelSelectPageState();
}

class _SVipLevelSelectPageState
    extends BaseStatefulPageState<SVipLevelSelectPage> {
  ///等级列表
  final List<VipLevelDto> _levelList = [];

  ///会员信息
  VipWithLevelAssertsRightsCardDTO? vipInfo;

  final SVipSelectController controller = SVipSelectController();

  @override
  String getActionBarTitle() => "付费会员";

  @override
  Future<void>? onInitState() async {
    showSVipForWin();
    await Future.wait([_getVipInfo(context), _getVipLevelList(context)]);
    //若当前会员是付费的永久会员，则排除该等级，无需续费该等级
    if (vipInfo?.level?.vipType == true && isForever(vipInfo?.vip?.validDate)) {
      _levelList
          .removeWhere((element) => vipInfo!.level!.levelId == element.id);
    }
    controller._init(_levelList, vipInfo);
  }

  @override
  void dispose() {
    hideSVipForWin();
    super.dispose();
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return SVipLevelSelectSubPage(controller: controller);
  }

  ///获取会员等级列表，新增会员用
  Future<void> _getVipLevelList(BuildContext context) async {
    final list = await VipModel.getVipLevelList(context, vipType: 1);
    _levelList.clear();
    _levelList.addAll(list ?? []);
  }

  Future<void> _getVipInfo(BuildContext context) async {
    vipInfo = await VipModel.getVipWithLevelScoreRightsCardById(
        context, widget.vipId, SpTool.getStoreInfo()?.otypeId ?? "");
  }
}

class SVipLevelSelectSubPage extends BaseStatefulPage {
  final SVipSelectController controller;

  final bool isSecondaryScreen;

  const SVipLevelSelectSubPage(
      {Key? key, required this.controller, this.isSecondaryScreen = false})
      : super(key: key, showAppBar: false);

  @override
  BaseStatefulPageState<SVipLevelSelectSubPage> createState() =>
      SVipLevelSelectSubPageState();
}

class SVipLevelSelectSubPageState
    extends BaseStatefulPageState<SVipLevelSelectSubPage> {
  ///会员信息
  VipWithLevelAssertsRightsCardDTO? vipInfo;

  ///等级列表
  final List<VipLevelDto> _levelList = [];

  ///权益map，key为会员等级id
  final Map<String, CardCouponCardTemplate?> rightsMap = {};

  ///当前选中等级的权益
  CardCouponCardTemplate? currentRights;

  ///选中的等级
  VipLevelDto? currentLevel;

  ///选中的规则
  VipLevelRule? currentRule;

  ///应付金额
  num shouldPayMoney = 0;

  @override
  String getActionBarTitle() => "";

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F6F8),
      padding: EdgeInsets.only(top: 20.h, left: 20.w, right: 20.w),
      child: Row(
        children: [
          Expanded(flex: 3, child: _buildLevelList(context)),
          SizedBox(width: 20.w),
          Expanded(flex: 2, child: _buildRightsList(context)),
          SizedBox(width: 20.w),
          Expanded(flex: 3, child: _buildRuleList(context)),
        ],
      ),
    );
  }

  ///选中一个付费会员等级
  Future<void> _onLevelSelect(VipLevelDto level) async {
    if (currentLevel == level) return;
    currentLevel = level;
    currentRights = await getLevelRights(level.id ?? "");
    _onRuleSelect(null);
    //按照需求，默认不勾选
    // final ruleList = level.levelRule;
    // VipLevelRule? rule;
    // if (ruleList?.isNotEmpty == true) {
    //   rule = ruleList!.first;
    // }
    // _onRuleSelect(rule);
  }

  ///获取会员的权益
  Future<CardCouponCardTemplate?> getLevelRights(String levelId) async {
    var rights = rightsMap[levelId];
    if (rights == null) {
      rights = await VipModel.getLevelRightsByLevelId(context, levelId);
      rightsMap[levelId] = rights;
    }
    return rights;
  }

  ///选中一个付费规则
  void _onRuleSelect(VipLevelRule? rule) {
    currentRule = rule;
    shouldPayMoney = currentRule?.levelPrice ?? 0;
    if (mounted) {
      setState(() {});
    }
    showSVipForWin(
        levelList: _levelList,
        currentLevel: currentLevel,
        currentRule: currentRule,
        currentRights: currentRights,
        shouldPayMoney: shouldPayMoney);
  }

  ///会员等级列表
  Widget _buildLevelList(BuildContext context) {
    return _buildListContainer(
        context,
        "请选择要充值的会员等级",
        ListView.builder(
            itemCount: _levelList.length,
            itemBuilder: (context, index) {
              final item = _levelList[index];
              TextStyle style = const TextStyle(
                  color: Color(0xFF333333), overflow: TextOverflow.ellipsis);
              num? minPrice;
              if (item.levelRule?.isNotEmpty == true) {
                for (var rule in item.levelRule!) {
                  num levelPrice = (rule.levelPrice ?? 0);
                  if (minPrice == null || minPrice > levelPrice) {
                    minPrice = levelPrice;
                  }
                }
              }
              return Padding(
                padding: EdgeInsets.only(top: 24.h),
                child: _buildSelectedItemContainer(
                  selected: currentLevel?.id == item.id,
                  height: 160.h,
                  onTap: () {
                    if (widget.isSecondaryScreen) return;
                    _onLevelSelect(item);
                  },
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 26.h, horizontal: 26.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                item.levelName ?? "",
                                maxLines: 1,
                                style: style.copyWith(
                                    color: const Color(0xFF683202),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 24.sp),
                              ),
                            ),
                            Text(
                              "SVIP${item.vipLevel}",
                              style: style.copyWith(fontSize: 22.sp),
                            )
                          ],
                        ),
                        Expanded(child: Container()),
                        RichText(
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          text: TextSpan(children: [
                            TextSpan(
                                text: "￥",
                                style: style.copyWith(
                                  fontSize: 20.sp,
                                  fontWeight: FontWeight.bold,
                                )),
                            TextSpan(
                                text: (minPrice ?? 0).toString(),
                                style: style.copyWith(
                                  fontSize: 30.sp,
                                  fontWeight: FontWeight.bold,
                                )),
                            TextSpan(
                                text: " 起",
                                style: style.copyWith(fontSize: 24.sp)),
                          ]),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }));
  }

  ///等级权益列表
  Widget _buildRightsList(BuildContext context) {
    return _buildListContainer(
        context,
        "会员权益",
        Padding(
          padding: EdgeInsets.only(top: 40.h),
          child: ListView.separated(
            itemCount: currentRights?.memberEquityValues?.length ?? 0,
            itemBuilder: (context, index) {
              final item = currentRights!.memberEquityValues![index];
              String fullname = item.fullName ?? "";
              if (item.detailList?.isNotEmpty == true) {
                final detail = item.detailList!.first;
                if (item.valueType == EquityValueType.scoreMultiple) {
                  num valueCondition = detail.valueCondition ?? 0;
                  num valueDetail = detail.valueDetail ?? 0;
                  fullname =
                      "每消费${valueCondition.getIntWhenInteger}元，累计${valueDetail.getIntWhenInteger}积分";
                } else if (item.valueType == EquityValueType.discount) {
                  String range = "";
                  switch (detail.ptypeRang) {
                    case 0:
                      range = "全部商品";
                      break;
                    case 1:
                      range = "部分商品";
                      break;
                    case 3:
                      range = "除部分商品外";
                      break;
                  }
                  num discount =
                      (Decimal.parse((detail.valueDetail ?? 0).toString()) *
                              Decimal.fromInt(10))
                          .toDouble();
                  if (!discount.isInteger) {
                    discount = (Decimal.parse(discount.toString()) *
                            Decimal.fromInt(10))
                        .toDouble();
                  }
                  discount = discount.toInt();
                  fullname = "$range消费$discount折";
                }
              }
              TextStyle style = TextStyle(
                  color: const Color(0xFF666666), height: 1.5, fontSize: 22.sp);
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 33.sp,
                    alignment: Alignment.center,
                    child: ClipOval(
                      child: Container(
                        height: 5,
                        width: 5,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ),
                  SizedBox(width: 20.w),
                  Expanded(child: Text(fullname, style: style)),
                ],
              );
            },
            separatorBuilder: (context, index) => SizedBox(height: 30.h),
          ),
        ));
  }

  ///付费规则列表
  Widget _buildRuleList(BuildContext context) {
    return _buildListContainer(
      context,
      "请选择会员卡套餐",
      ListView.builder(
        itemCount: currentLevel?.levelRule?.length ?? 0,
        itemBuilder: (context, index) {
          final item = currentLevel!.levelRule![index];
          bool selected = currentRule?.id == item.id;
          final style = TextStyle(
              color:
                  selected ? const Color(0xFFF2A700) : const Color(0xFF333333),
              fontSize: 26.sp);
          return Padding(
            padding: EdgeInsets.only(top: 24.h),
            child: _buildSelectedItemContainer(
              selected: selected,
              height: 128.h,
              background: Colors.white,
              unselectedBorderColor: const Color(0xFFCACACA),
              onTap: () {
                if (widget.isSecondaryScreen) return;
                if (item.id == currentRule?.id) {
                  return;
                }
                _onRuleSelect(item);
              },
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 40.w),
                child: Row(
                  children: [
                    Text(
                      _getValidity(item),
                      style: TextStyle(
                          color: const Color(0xFF333333), fontSize: 26.sp),
                    ),
                    Expanded(
                      child: RichText(
                        textAlign: TextAlign.right,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        text: TextSpan(
                          children: [
                            TextSpan(text: "￥ ", style: style),
                            TextSpan(
                                text: "${item.levelPrice ?? 0}",
                                style: style.copyWith(fontSize: 30.sp)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  ///构建列表容器
  Widget _buildListContainer(
      BuildContext context, String title, Widget content) {
    return Container(
      color: Colors.white,
      height: double.infinity,
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 24.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
                color: const Color(0xFF333333),
                fontSize: 26.sp,
                fontWeight: FontWeight.bold),
          ),
          Expanded(child: content),
        ],
      ),
    );
  }

  ///选择容器外边框
  Widget _buildSelectedItemContainer({
    bool selected = false,
    required double height,
    required Widget child,
    Color background = const Color(0xFFFDF6E8),
    Color unselectedBorderColor = Colors.transparent,
    VoidCallback? onTap,
  }) {
    Color borderColor =
        selected ? const Color(0xFFFFB207) : unselectedBorderColor;
    if (selected) {
      child = Stack(
        children: [
          Positioned(top: 0, left: 0, bottom: 0, right: 0, child: child),
          Positioned(
            right: 0,
            bottom: 0,
            width: 50.w,
            height: 50.w,
            child: CustomPaint(painter: _YellowTriangle()),
          ),
          Positioned(
            bottom: 5.h,
            right: 5.w,
            child: IconFont(IconNames.duigou, size: 15.w),
          ),
        ],
      );
    }
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: height,
        decoration: BoxDecoration(
          color: background,
          borderRadius: BorderRadius.circular(4.w),
          border: Border.all(color: borderColor, width: 2.w),
        ),
        child: child,
      ),
    );
  }

  @override
  Widget buildBottomBody(BuildContext context) {
    TextStyle style = const TextStyle(
        color: Color(0xFF333333), overflow: TextOverflow.ellipsis);
    return Container(
      height: 110.h,
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            offset: const Offset(0, 14),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          Text("应收  ￥", style: style.copyWith(fontSize: 24.sp)),
          Expanded(
            child: Text(
                DecimalDisplayHelper.getPriceFixed(shouldPayMoney.toString()),
                style: style.copyWith(
                    fontSize: 34.sp, fontWeight: FontWeight.bold)),
          ),
          if (!widget.isSecondaryScreen)
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                if (widget.isSecondaryScreen) return;
                //跳转到收银页面
                if (vipInfo?.vip?.id == null) {
                  HaloToast.show(context, msg: "会员信息异常");
                }
                if (currentLevel == null) {
                  HaloToast.show(context, msg: "未选择会员等级");
                }
                if (currentRule == null) {
                  HaloToast.show(context, msg: "未选择付费规则");
                }
                NavigateUtil.navigateTo(
                        context,
                        VipUpgradeSettlementPage(
                            vipInfo: vipInfo!,
                            level: currentLevel!,
                            levelRule: currentRule!))
                    .then((value) {
                  if (value != null) {
                    Navigator.pop(context);
                  }
                });
              },
              child: Container(
                width: 326.w,
                height: 80.h,
                decoration: BoxDecoration(
                  color: const Color(0xFF4679FC),
                  borderRadius: BorderRadius.circular(8.w),
                ),
                alignment: Alignment.center,
                child: Text(
                  "收银",
                  style: TextStyle(color: Colors.white, fontSize: 36.sp),
                ),
              ),
            )
        ],
      ),
    );
  }

  @override
  Future<void>? onInitState() async {
    widget.controller.state = this;
  }

  @override
  void dispose() {
    widget.controller.state = null;
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant SVipLevelSelectSubPage oldWidget) {
    if (oldWidget.controller != widget.controller) {
      oldWidget.controller.state = null;
    }
    widget.controller.state = this;
    super.didUpdateWidget(oldWidget);
  }

  void _setState(VoidCallback fn) {
    setState(fn);
  }
}

///由外部控制界面展示
class SVipSelectController {
  SVipLevelSelectSubPageState? state;

  ///选中的等级
  VipLevelDto? currentLevel;

  ///选中的规则
  VipLevelRule? currentRule;

  void _init(
      List<VipLevelDto> levelList, VipWithLevelAssertsRightsCardDTO? vipInfo) {
    setState(() {
      state
        ?..vipInfo = vipInfo
        .._levelList.clear()
        .._levelList.addAll(levelList);
      if (levelList.isNotEmpty) {
        state?._onLevelSelect(levelList.first);
      }
    });
  }

  void onSecondaryScreenRefresh({
    VipLevelDto? currentLevel,
    VipLevelRule? currentRule,
    CardCouponCardTemplate? currentRights,
    List<VipLevelDto> levelList = const [],
    num shouldPayMoney = 0,
  }) {
    setState(() {
      state
        ?.._levelList.clear()
        .._levelList.addAll(levelList)
        ..currentLevel = currentLevel
        ..currentRule = currentRule
        ..currentRights = currentRights
        ..shouldPayMoney = shouldPayMoney;
    });
  }

  void setState(VoidCallback fn) {
    if (state != null && state!.mounted) {
      state?._setState(fn);
    }
  }
}

///优惠券选中的黄色三角形
class _YellowTriangle extends CustomPainter {
  final Paint _paint = Paint()
    ..style = PaintingStyle.fill
    ..color = const Color(0xFFFFB207)
    ..isAntiAlias = true;

  final Path _path = Path();

  @override
  void paint(Canvas canvas, Size size) {
    _path.moveTo(0, size.height);
    _path.lineTo(size.width, 0);
    _path.lineTo(size.width, size.height);
    _path.lineTo(0, size.height);
    canvas.drawPath(_path, _paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

///构建付费会员定价说明文字
String _getValidity(VipLevelRule? rule) {
  if (rule == null) {
    return "";
  } else {
    int validity = rule.validity ?? 0;
    String period;
    if (validity <= 0) {
      period = "一直有效";
    } else if (validity == 12) {
      period = "1年";
    } else if (validity == 36) {
      period = "3年";
    } else {
      period = "$validity个月";
    }
    return period;
  }
}
