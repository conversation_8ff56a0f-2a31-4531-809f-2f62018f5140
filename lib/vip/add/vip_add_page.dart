import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_pos/entity/system/system_config_dto.dart';
import 'package:halo_utils/halo_utils.dart' hide DateUtil;
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';

import '../../bill/model/base_info_model.dart';
import '../../bill/widget/mixin/select_vip_mixin.dart';
import '../../common/standard.dart';
import '../../common/tool/decimal_scale_input_formatter.dart';
import '../../common/tool/sp_tool.dart';
import '../../entity/otype.dart';
import '../../entity/select_wrapper_bean.dart';
import '../../entity/system/permission_dto.dart';
import '../../iconfont/icon_font.dart';
import '../../login/entity/store/store_info.dart';
import '../../settting/widget/checkbox.dart';
import '../../vip/add/vip_settlement.dart';
import '../../vip/detail/vip_detail_page.dart';
import '../../vip/entity/add_or_edit_vip_request.dart';
import '../../vip/entity/card_dto.dart';
import '../../vip/entity/card_template.dart';
import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../vip/entity/vip_level_dto.dart';
import '../../vip/entity/vip_simple.dart' as vip_simple;
import '../../vip/entity/vip_tag.dart';
import '../../vip/model/vip_model.dart';
import '../../vip/widget/mixin/vip_info_row_mixin.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/citypicker.dart';
import '../../widgets/multi_select_dialog.dart';
import '../../widgets/single_select_dialog.dart';

///会员新增和编辑页面
class VipAddPage extends BaseStatefulPage {
  /// 会员id，当不为空，会根据此id查询会员信息，编辑
  final String? vipId;

  /// 从手机号搜索-》搜索不到会员-》新增会员时，传入的手机号，默认填入
  final String? phone;

  final String? name;

  const VipAddPage({Key? key, this.vipId, this.phone, this.name})
      : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() => _VipAddPageState();
}

class _VipAddPageState extends BaseStatefulPageState<VipAddPage>
    with VipInfoRowMixin, SelectVipMixin {
  ///左边距
  final _paddingLeft = 300.w;

  ///右边距
  final _paddingRight = 218.w;

  ///权限
  final PermissionDto _permissionDto = SpTool.getPermission();

  ///系统配置
  final SystemConfigDto _systemConfigDto = SpTool.getSystemConfig();

  ///需要提交的会员信息
  final AddOrEditVipDTO _request = AddOrEditVipDTO();

  ///是否是添加会员
  bool get _isAdd => widget.vipId == null;

  ///是否可以编辑基本信息
  bool get _editable => true;

  ///权益卡名称(多个)
  String? _rightCardNames;

  ///门店名称(多个)
  String? _otypeNames;

  ///会员标签
  List<VipTag>? _tagList;

  ///新增会员时的当前会员等级
  VipLevelDto? _currentLevel;

  ///新增付费会员时选中的付费规则
  VipLevelRule? _currentLevelRule;

  ///会员类型
  String vipType = "免费会员";

  bool get _isPaidLevel => vipType == "付费会员";

  ///会员等级列表
  List<VipLevelDto>? get _levelList =>
      _isPaidLevel ? _paidLevelList : _freeLevelList;

  ///付费会员等级列表
  List<VipLevelDto>? _paidLevelList;

  ///免费会员等级列表
  List<VipLevelDto>? _freeLevelList;

  ///当前门店
  late StoreInfo currentStoreInfo;

  ///密码是否可见
  bool _pwdObscureText = true;

  @override
  Future<void> onInitState() async {
    currentStoreInfo = SpTool.getStoreInfo()!;
    _request.id = widget.vipId;
    //编辑会员，先根据会员id查询会员信息
    if (!_isAdd) {
      VipWithLevelAssertsRightsCardDTO? vipInfo =
          await VipModel.getVipWithLevelScoreRightsCardById(
              context, widget.vipId!, currentStoreInfo.otypeId ?? "");
      if (vipInfo != null) {
        vipInfo.vip?.let((vip) => _request
          ..vipLevelId = vip.vipLevelId ?? ""
          ..name = vip.name ?? ""
          ..phone = vip.phone ?? ""
          ..sex = vip.sex ?? 1
          ..birthday = vip.birthday ?? ""
          ..idCode = vip.idCode ?? ""
          ..email = vip.email ?? ""
          ..address = vip.address ?? ""
          ..province = vip.province ?? ""
          ..city = vip.city ?? ""
          ..district = vip.district ?? ""
          ..street = vip.street ?? ""
          ..score = vipInfo.asserts?.score ?? 0
          ..store = vipInfo.asserts?.chargeTotal ?? 0
          ..applyStoreType = vip.applyStoreType);
        //权益卡
        _setRightsCardsOrOtypes<CardDto>(
            vipInfo.rightCardList ?? [],
            (value) => value.cardName ?? "",
            (value) =>
                //这里表单上传的是权益卡的模板id
                value.cardTemplateId ?? "");
        //门店
        _setRightsCardsOrOtypes<vip_simple.Otype>(vipInfo.otypeList ?? [],
            (value) => value.otypeName ?? "", (value) => value.otypeId ?? "",
            isRightsCard: false);
        //标签
        _setTagList(vipInfo.tagList ?? []);
      }
    }
    //新增会员
    else {
      _request.phone = widget.phone ?? "";
      _request.name = widget.name ?? "";
      //默认选中当前门店
      _request.otypeIds = [currentStoreInfo.otypeId ?? ""];
      _otypeNames = currentStoreInfo.fullname ?? "";
      getDefaultLevel(context).then((value) {
        if (value != null) {
          setState(() => _currentLevel = value);
        }
      });
    }
  }

  ///遍历权益卡(或门店)列表，拼接名称(用于展示)和id(用于提交)
  ///[isRightsCard]当为true，说明是权益卡，否则是门店
  void _setRightsCardsOrOtypes<T>(List<T> source,
      ResultFunction<T, String> nameGetter, ResultFunction<T, String> idGetter,
      {bool isRightsCard = true}) {
    List<String> ids = [];
    StringBuffer buffer = StringBuffer();
    for (int i = 0; i < source.length; i++) {
      T value = source[i];
      String name = nameGetter(value);
      String id = idGetter(value);
      ids.add(id);
      if (i != 0) {
        buffer.write("、");
      }
      buffer.write(name);
    }
    if (isRightsCard == true) {
      _rightCardNames = buffer.toString();
      _request.rightsCardModelIds = ids;
    } else {
      _otypeNames = buffer.toString();
      _request.otypeIds = ids;
    }
  }

  ///设置会员标签，当编辑会员第一次获取会员信息，或更改了会员标签时调用
  void _setTagList(List<VipTag> tagList) {
    _tagList = tagList;
    _request.vipTag =
        tagList.where((tag) => tag.id != null).map((tag) => tag.id!).toList();
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container(
      color: Colors.white,
      height: double.infinity,
      padding: EdgeInsets.only(left: _paddingLeft, right: _paddingRight),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            //会员姓名和电话行
            _buildNameAndPhoneRow(context),
            buildDivider(),
            //性别和生日行
            _buildSexAndBirthdayRow(context),
            buildDivider(),
            //地址
            ..._buildAddressAreaRow(context),
            //身份证和邮编行
            _buildIdCodeAndEmailRow(),
            buildDivider(),
            //等级和成长值，只有新增会员有
            if (_isAdd) ...[
              _buildVipTypeRow(context),
              buildDivider(),
              _buildLevelRow(context),
              buildDivider(),
              if (_isPaidLevel) _buildLevelRuleRow(context),
              if (!_isPaidLevel) _buildGrowthRow(context),
              buildDivider(),
            ],
            //会员储值和积分，只有新增才展示
            if (_isAdd && _permissionDto.memberVipGiveScore == true) ...[
              _buildScoreAndStoreRow(context),
              buildDivider()
            ],
            //会员标签选择行
            if (_permissionDto.memberVipAddTags == true) ...[
              _buildVipTagRow(context),
              buildDivider()
            ],
            //权益卡选择行
            //编辑会员时不允许编辑权益卡
            if (_isAdd && _permissionDto.memberVipBindRightsCard == true) ...[
              _buildRightsCardRow(),
              buildDivider()
            ],
            //门店选择行
            _buildOtypeRow(),
            buildDivider(),
            //密码
            if (_isAdd) _buildPasswordRow(),
            buildDivider(),
          ],
        ),
      ),
    );
  }

  ///底部的按钮
  @override
  Widget buildBottomBody(BuildContext context) {
    return Column(
      children: [
        buildDivider(),
        Container(
            color: Colors.white,
            padding: EdgeInsets.only(
                left: _paddingLeft,
                right: _paddingRight,
                top: 34.h,
                bottom: 46.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildButton(context, "取消",
                    onTap: () => Navigator.pop(context)),
                _buildButton(
                  context,
                  "保存",
                  textColor: Colors.white,
                  background: const Color(0xFF4679FC),
                  borderColor: null,
                  onTap: () async {
                    if (checkRequest(context)) {
                      String? vipId;
                      bool isAdd = _isAdd;
                      if (_request.vipType == 1 && isAdd) {
                        vipId = await Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    VipAddSettlementPage(request: _request)));
                      } else {
                        vipId = await VipModel.addOrEditVip(context, _request);
                      }
                      if (vipId != null && mounted) {
                        HaloToast.show(context, msg: isAdd ? "添加成功" : "保存成功");
                        Navigator.pop(context, vipId);
                        //新增会员之后需要跳转到会员详情
                        if (isAdd &&
                            (SpTool.getPermission().memberVipView ?? false)) {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      VipDetailPage(vipId: vipId!)));
                        }
                      }
                    }
                  },
                )
              ],
            ))
      ],
    );
  }

  ///提交之前先检查提交的数据是否合法
  bool checkRequest(BuildContext context) {
    //电话必填
    if (TextUtil.isEmpty(_request.phone)) {
      HaloToast.show(context, msg: "电话号码不能为空");
      return false;
    }
    if (_systemConfigDto.posVipNameRequired &&
        TextUtil.isEmpty(_request.name)) {
      HaloToast.show(context, msg: "会员名称不能为空");
      return false;
    }
    if (_isAdd) {
      if (_request.store < 0 || _request.store > 99999) {
        HaloToast.show(context, msg: "初始储值为0-99999");
        return false;
      }
      if (_request.score < 0 || _request.score > 99999999) {
        HaloToast.show(context, msg: "初始积分为0-99999999");
        return false;
      }
      //新增会员需要会员等级
      if (_currentLevel == null) {
        HaloToast.show(context, msg: "请选择会员等级");
        return false;
      }
      if (_request.growthValue < 0 || _request.growthValue > 99999) {
        HaloToast.show(context, msg: "初始成长值为0-99999");
        return false;
      }
      //付费会员
      if (_currentLevel!.vipType == 1) {
        if (_currentLevelRule == null) {
          HaloToast.show(context, msg: "请选择付费会员定价规则");
          return false;
        }
        _request.levelRuleId = _currentLevelRule!.id ?? "";
        _request.vipType = 1;
        _request.levelName = _currentLevel!.levelName ?? "";
        _request.priceStr = _getValidity(_currentLevelRule);
        _request.levelPrice = _currentLevelRule!.levelPrice ?? 0;
        _request.validity = _currentLevelRule!.validity ?? 0;
      }
      _request.vipLevelId = _currentLevel!.id ?? "";
    }
    if (_request.applyStoreType != 0) {
      //门店必须包含当前门店
      String? otypeId = currentStoreInfo.otypeId;
      if (!_request.otypeIds.contains(otypeId)) {
        HaloToast.show(context, msg: "必须包含此门店");
        return false;
        // _request.otypeIds.add(otypeId!);
      }
    }
    return true;
  }

  @override
  String getActionBarTitle() => widget.vipId == null ? "新增会员" : "编辑会员信息";

  ///获取默认等级（新增会员用）
  Future<VipLevelDto?> getDefaultLevel(BuildContext context) async {
    await _getVipLevelList(context);
    return (_freeLevelList?.isNotEmpty == true) ? _freeLevelList![0] : null;
  }

  ///获取会员等级列表，新增会员用
  Future<void> _getVipLevelList(BuildContext context) async {
    var list = await VipModel.getVipLevelList(context);
    list ??= [];
    _paidLevelList = [];
    _freeLevelList = [];
    for (var level in list) {
      if (level.vipType == 1) {
        _paidLevelList!.add(level);
      } else {
        _freeLevelList!.add(level);
      }
    }
  }

  ///构建文本输入行
  ///[title] 左边标题
  ///[content] 文本输入框默认值
  ///[maxLength] 输入字符串最大长度
  ///[decimal] 限制输入的小数位数，当不为空，则只能输入数字
  ///[isNumber] 限制只能输入数字
  ///[required] 标题是否有*号
  ///[enable] 是否允许输入
  Widget buildTextInputRow(
      {required String title,
      String? content,
      int? maxLength,
      int? decimal,
      bool? isNumber,
      bool required = false,
      bool? enable,
      bool obscureText = false,
      Widget? suffixIcon,
      ValueChanged<String>? callback}) {
    TextInputType? inputType;
    if (decimal != null || isNumber == true) {
      inputType = TextInputType.numberWithOptions(decimal: (decimal ?? 0) > 0);
    }
    return buildRow(
      title: title,
      required: required,
      content: TextField(
        controller: TextEditingController(text: content),
        maxLines: 1,
        enabled: enable,
        obscureText: obscureText,
        textAlignVertical: TextAlignVertical.center,
        decoration: InputDecoration(
          isCollapsed: true,
          contentPadding: EdgeInsets.zero,
          border: const OutlineInputBorder(borderSide: BorderSide.none),
          suffixIcon: suffixIcon,
        ),
        inputFormatters: <TextInputFormatter>[].also((list) {
          if (maxLength != null && maxLength > 0) {
            list.add(LengthLimitingTextInputFormatter(maxLength));
          }
          if (decimal != null) {
            list.add(DecimalScaleInputFormatter(scale: decimal));
          } else if (isNumber == true) {
            list.add(FilteringTextInputFormatter.digitsOnly);
          }
        }),
        onChanged: (text) => callback?.call(text),
        keyboardType: inputType,
        style: contentStyle,
      ),
    );
  }

  ///底部按钮
  Widget _buildButton(
    BuildContext context,
    String content, {
    Color textColor = const Color(0xFF333333),
    Color background = Colors.white,
    Color? borderColor = const Color(0xFF999999),
    VoidCallback? onTap,
  }) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: 80.h,
          width: 225.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: background,
            borderRadius: BorderRadius.circular(8.w),
            border: borderColor?.let(
                (borderColor) => Border.all(color: borderColor, width: 2.w)),
          ),
          child: Text(content,
              style: TextStyle(color: textColor, fontSize: 34.sp)),
        ));
  }

  ///性别选择的单选按钮
  Widget _buildSwitchItem(
          String name, bool value, ValueChanged<bool> onChanged) =>
      Row(children: [
        HaloPosCheckBox(
            value: value, width: 30.w, height: 30.w, onChanged: onChanged),
        Text(name, style: contentStyle),
      ]);

  ///构建可点击弹出选择框的行
  ///生日日期选择，标签、权益卡、消费范围
  Widget _buildSelectableRow(
      {required String title,
      required Widget content,
      bool required = false,
      Widget? rightIcon,
      VoidCallback? onTap}) {
    List<Widget> children = [Expanded(child: content)];
    rightIcon?.let((rightIcon) => children.add(rightIcon));
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        child: buildRow(
          title: title,
          required: required,
          content: Row(children: children),
        ));
  }

  ///构建会员标签选择行
  Widget _buildVipTagRow(BuildContext context) {
    return _buildSelectableRow(
      title: "标签",
      content: SizedBox(
        height: 56.h,
        child: ListView.separated(
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) =>
                buildTagItem(context, _tagList![index],
                    onDelete: (tag) => setState(() {
                          _tagList!.remove(tag);
                          _setTagList(_tagList!);
                        })),
            separatorBuilder: (context, index) => SizedBox(width: 12.w),
            itemCount: _tagList?.length ?? 0),
      ),
      rightIcon: IconFont(IconNames.guolvxiala, size: 15.w),
      onTap: () => showDialog<List<VipTag>>(
          context: context,
          builder: (context) => MultiSelectDialog<VipTag>(
                title: "标签",
                nameGetter: (tag) => tag.text ?? "",
                listGetter: () async =>
                    (await VipModel.getVipTagList(context))
                        ?.map((tag) => SelectWrapperBean(
                            data: tag,
                            selected: _request.vipTag.contains(tag.id)))
                        .toList() ??
                    [],
              )).then((tagList) =>
          tagList?.let((tagList) => setState(() => _setTagList(tagList)))),
    );
  }

  ///构建权益卡选择行
  Widget _buildRightsCardRow() {
    return _buildSelectableRow(
      title: "权益卡",
      content: buildStringContent(_rightCardNames ?? ""),
      rightIcon: IconFont(IconNames.guolvxiala, size: 15.w),
      onTap: () => showDialog<List<CardTemplate>>(
          context: context,
          builder: (context) => MultiSelectDialog<CardTemplate>(
                title: "权益卡",
                nameGetter: (card) => card.fullname ?? "",
                listGetter: () async =>
                    (await VipModel.getRightsCardTemplateList(
                            context, 1, 99999))
                        ?.map((template) => SelectWrapperBean(
                            data: template,
                            selected: _request.rightsCardModelIds
                                .contains(template.id)))
                        .toList() ??
                    [],
              )).then((templateList) => setState(() =>
          _setRightsCardsOrOtypes<CardTemplate>(templateList ?? [],
              (value) => value.fullname ?? "", (value) => value.id ?? ""))),
    );
  }

  ///构建门店选择行
  Widget _buildOtypeRow() {
    bool isPartOtype = _request.applyStoreType != 0;
    return buildRow(
        title: "可用门店",
        required: true,
        content: Row(
          children: [
            _buildSwitchItem("全部", !isPartOtype, (value) {
              if (_editable) {
                setState(() => _request.applyStoreType = 0);
              }
            }),
            SizedBox(width: 90.w),
            _buildSwitchItem("部分", isPartOtype, (value) {
              if (_editable) {
                setState(() => _request.applyStoreType = 1);
              }
            }),
            if (isPartOtype)
              Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => showDialog<List<Otype>>(
                    context: context,
                    builder: (context) => MultiSelectDialog<Otype>(
                      title: "选择门店",
                      nameGetter: (otype) => otype.fullname ?? "",
                      listGetter: () async =>
                          (await BaseInfoModel.getOtypeList(context) ?? [])
                              .map<SelectWrapperBean<Otype>>(
                                (otype) => SelectWrapperBean<Otype>(
                                    data: otype,
                                    selected: currentStoreInfo.otypeId ==
                                            otype.id ||
                                        _request.otypeIds.contains(otype.id),
                                    //当前门店不可取消选择
                                    enable:
                                        currentStoreInfo.otypeId != otype.id),
                              )
                              .toList(),
                    ),
                  ).then((otypeList) => setState(() =>
                      _setRightsCardsOrOtypes<Otype>(
                          otypeList ?? [],
                          (value) => value.fullname ?? "",
                          (value) => value.id ?? "",
                          isRightsCard: false))),
                  child: Row(
                    children: [
                      SizedBox(width: 20.w),
                      Expanded(child: buildStringContent(_otypeNames ?? "")),
                      IconFont(IconNames.guolvxiala, size: 15.w),
                    ],
                  ),
                ),
              ),
          ],
        ));
  }

  ///构建姓名和电话行
  Widget _buildNameAndPhoneRow(BuildContext context) {
    return Row(
      children: [
        Expanded(
            child: buildTextInputRow(
                title: "姓名",
                required: _systemConfigDto.posVipNameRequired,
                content: _request.name,
                maxLength: 20,
                enable: _editable,
                callback: (text) => _request.name = text.trim())),
        Expanded(
            child: buildTextInputRow(
                title: "手机号码",
                content: _request.phone,
                isNumber: true,
                required: true,
                maxLength: 12,
                enable: _editable,
                callback: (text) => _request.phone = text.trim()))
      ],
    );
  }

  ///地址
  List<Widget> _buildAddressAreaRow(BuildContext context) {
    return [
      _buildSelectableRow(
          title: "省市区",
          content: buildStringContent(
              "${_request.province}${_request.city}${_request.district}${_request.street}"),
          rightIcon: IconFont(IconNames.guolvxiala, size: 15.w),
          onTap: () => _showAreaPicker(context)),
      buildDivider(),
      buildTextInputRow(
          title: "详细地址",
          enable: _editable,
          content: _request.address,
          maxLength: 200,
          callback: (text) => _request.address = text.trim()),
      buildDivider(),
    ];
  }

  ///展示省市区选择弹框
  void _showAreaPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return CityPicker(
          callback: (map) {
            if ('请选择' != map['province']) {
              _request.province = map['province'];
              _request.city = map['city'];
              _request.district = map['district'];
              _request.street = map['street'] ?? '';
            } else {
              _request.province = '';
              _request.city = '';
              _request.district = '';
              _request.street = '';
            }
            setState(() {});
          },
          addressMap: {
            "province": _request.province,
            "city": _request.city,
            "district": _request.district,
            "street": _request.street,
          },
        );
      },
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(25),
      ),
    );
  }

  ///构建性别和生日行
  Widget _buildSexAndBirthdayRow(BuildContext context) {
    DateTime now = DateTime.now();
    return Row(
      children: [
        Expanded(
            child: buildRow(
                title: "性别",
                content: Row(
                  children: [
                    _buildSwitchItem("男", _request.sex == 1, (value) {
                      if (_editable) {
                        setState(() => _request.sex = 1);
                      }
                    }),
                    SizedBox(width: 90.w),
                    _buildSwitchItem("女", _request.sex != 1, (value) {
                      if (_editable) {
                        setState(() => _request.sex = 2);
                      }
                    }),
                  ],
                ))),
        Expanded(
            child: _buildSelectableRow(
                title: "生日",
                content: buildStringContent(formatDateStringToLocal(
                    _request.birthday,
                    format: DateFormats.y_mo_d)),
                rightIcon: IconFont(IconNames.rili, size: 30.w),
                onTap: () {
                  if (_editable) {
                    showDialog(
                        context: context,
                        builder: (context) => DatePickerDialog(
                              initialDate: DateUtil.getDateTime(
                                      _request.birthday,
                                      isUtc: false) ??
                                  now,
                              firstDate: DateTime(1900, 1, 1),
                              lastDate: now,
                            )).then((date) {
                      if (mounted && date != null) {
                        setState(() => _request.birthday =
                            formatDateStringToUtc(DateUtil.getFilterDate(
                                date.toLocal(),
                                isBegin: true)));
                      }
                    });
                    // DatePicker.showDatePicker(
                    //   context,
                    //   title: "生日",
                    //   maximumDate: now,
                    //   initialDateTime:
                    //       DateUtil.getDateTime(_request.birthday) ?? now,
                    //   listener: (DateTime date) => setState(() =>
                    //       _request.birthday = DateUtil.formatDate(date,
                    //           format: DataFormats.y_mo_d)),
                    // );
                  }
                }))
      ],
    );
  }

  ///构建身份证和邮编行
  Widget _buildIdCodeAndEmailRow() {
    return Row(
      children: [
        //todo 这里先把身份证停了
        Visibility(
          visible: false,
          child: Expanded(
              child: buildTextInputRow(
                  title: "身份证号",
                  content: _request.idCode,
                  maxLength: 20,
                  callback: (text) => _request.idCode = text.trim())),
        ),
        Expanded(
            child: buildTextInputRow(
                title: "邮箱",
                enable: _editable,
                content: _request.email,
                maxLength: 32,
                callback: (text) => _request.email = text.trim()))
      ],
    );
  }

  ///构建会员储值和积分行
  Widget _buildScoreAndStoreRow(BuildContext context) {
    return Row(
      children: [
        //todo 由于充值功能暂时关闭，先隐藏初始储值
        Visibility(
          visible: false,
          child: Expanded(
              child: buildTextInputRow(
                  title: "初始储值",
                  content: _request.store.toString(),
                  decimal: 0,
                  maxLength: 7,
                  callback: (text) =>
                      _request.store = double.tryParse(text) ?? 0)),
        ),
        Expanded(
            child: buildTextInputRow(
                title: "初始积分",
                content: _request.score.toString(),
                maxLength: 8,
                decimal: 0,
                callback: (text) => _request.score = int.tryParse(text) ?? 0))
      ],
    );
  }

  ///成长值
  Widget _buildGrowthRow(BuildContext context) {
    return buildTextInputRow(
        title: "成长值",
        content: _request.growthValue.toString(),
        decimal: 0,
        callback: (text) => _request.growthValue = int.tryParse(text) ?? 0);
  }

  ///会员类型
  Widget _buildVipTypeRow(BuildContext context) {
    return _buildSelectableRow(
        required: true,
        title: "会员类型",
        content: buildStringContent(vipType),
        rightIcon: IconFont(IconNames.guolvxiala, size: 15.w),
        onTap: () => _showVipTypeSelectDialog(context));
  }

  ///会员等级
  Widget _buildLevelRow(BuildContext context) {
    return _buildSelectableRow(
        required: _isPaidLevel,
        title: "等级",
        content: buildStringContent(_currentLevel?.levelName ?? ""),
        rightIcon: IconFont(IconNames.guolvxiala, size: 15.w),
        onTap: () => _showVipSelectDialog(context));
  }

  ///付费会员定价
  Widget _buildLevelRuleRow(BuildContext context) {
    return _buildSelectableRow(
        required: true,
        title: "定价",
        content: buildStringContent(_getValidity(_currentLevelRule)),
        rightIcon: IconFont(IconNames.guolvxiala, size: 15.w),
        onTap: () => _showLevelRuleSelectDialog(context));
  }

  ///密码
  Widget _buildPasswordRow() {
    return buildTextInputRow(
      title: "支付密码",
      isNumber: true,
      content: _request.password,
      maxLength: 6,
      obscureText: _pwdObscureText,
      callback: (text) => _request.password = text.trim(),
      suffixIcon: Container(
        alignment: Alignment.centerRight,
        width: 40.w,
        height: 40.w,
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => setState(() => _pwdObscureText = !_pwdObscureText),
          child: Icon(
            _pwdObscureText ? Icons.visibility_off : Icons.visibility,
            size: 30.w,
          ),
        ),
      ),
    );
  }

  ///会员等级列表弹窗
  void _showVipSelectDialog(BuildContext context) async {
    if (_levelList?.isNotEmpty != true) {
      await _getVipLevelList(context);
    }
    if (context.mounted) {
      var level = await showDialog<VipLevelDto>(
          context: context,
          builder: (context) => SingleSelectDialog<VipLevelDto>(
                title: "会员",
                list: _levelList ?? [],
                contentGetter: (level) => level.levelName ?? "",
              ));
      if (level != null) {
        setState(() => _onLevelChange(level));
      }
    }
  }

  ///会员类型弹窗
  void _showVipTypeSelectDialog(BuildContext context) async {
    String? type = await showDialog<String>(
        context: context,
        builder: (context) => SingleSelectDialog<String>(
            title: "会员类型",
            list: const ["免费会员", "付费会员"],
            contentGetter: (text) => text));
    if (type != null && vipType != type) {
      setState(() {
        vipType = type;
        var levelList = _levelList;
        VipLevelDto? level;
        if (levelList?.isNotEmpty == true) {
          level = levelList!.first;
        }
        _onLevelChange(level);
      });
    }
  }

  ///付费会员规则弹窗
  void _showLevelRuleSelectDialog(BuildContext context) async {
    VipLevelRule? rule = await showDialog<VipLevelRule>(
        context: context,
        builder: (context) => SingleSelectDialog<VipLevelRule>(
            title: "定价",
            list: _currentLevel?.levelRule ?? [],
            contentGetter: (rule) => _getValidity(rule)));
    if (rule != null && _currentLevelRule != rule) {
      setState(() {
        _currentLevelRule = rule;
      });
    }
  }

  ///更改会员等级
  void _onLevelChange(VipLevelDto? level) {
    _currentLevel = level;
    VipLevelRule? levelRule;
    if (_currentLevel?.vipType == 1) {
      //默认选中第一个规则
      List<VipLevelRule>? ruleList = _currentLevel!.levelRule;
      if (ruleList?.isNotEmpty == true) {
        levelRule = ruleList!.first;
      }
      _request.growthValue = 0;
    }
    _currentLevelRule = levelRule;
  }

  ///构建付费会员定价说明文字
  String _getValidity(VipLevelRule? rule) {
    if (rule == null) {
      return "";
    } else {
      int validity = rule.validity ?? 0;
      String period;
      if (validity <= 0) {
        period = "一直有效";
      } else if (validity == 12) {
        period = "1年";
      } else if (validity == 36) {
        period = "3年";
      } else {
        period = "$validity个月";
      }
      return "￥${rule.levelPrice ?? 0}/$period";
    }
  }
}
