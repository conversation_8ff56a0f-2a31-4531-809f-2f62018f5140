import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/bill/model/bill_model.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../../bill/settlement/entity/atype_info_bean.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/dotted_line.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../bill/entity/delete_bill_request.dart';
import '../../bill/entity/payment_dto.dart';
import '../../bill/entity/pay_result_dto.dart';
import '../entity/pay_result_info.dart';
import '../../bill/settlement/settlement_mixin.dart';
import '../../common/tool/UUID.dart';
import '../../common/tool/performance_capture_util.dart';
import '../../enum/payment_enum.dart';
import '../../common/tool/unified_payment_util.dart';
import '../entity/add_or_edit_vip_request.dart';
import '../entity/get_vip_level_score_rights_card_response.dart';
import '../entity/vip_fee_record.dart';
import '../entity/vip_level_dto.dart';
import '../model/vip_model.dart';
import '../utils/svip_util.dart';
import '../widget/mixin/vip_settlement_mixin.dart';

///新增会员结算页面
class VipAddSettlementPage extends BaseStatefulPage {
  final AddOrEditVipDTO request;

  const VipAddSettlementPage({Key? key, required this.request})
    : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _VipAddSettlementPageState();
}

class _VipAddSettlementPageState
    extends BaseStatefulPageState<VipAddSettlementPage>
    with SettlementMixin, VipSettlementMixin, _VipSettlementMixin {
  /// 聚合支付结果，用于获取vipId和vchcode
  PayResult? _aggregatePayResult;

  ///应收金额
  @override
  num get moneyReceived => widget.request.levelPrice;

  @override
  String get levelName => widget.request.levelName;

  @override
  String get priceStr => widget.request.priceStr;

  @override
  int get validity => widget.request.validity;

  @override
  String get vipName => widget.request.name;

  @override
  String get vipPhone => widget.request.phone;

  @override
  Future<void> onInitState() {
    //计算到期时间
    if (validity == 0) {
      expireTime = "一直有效";
    } else {
      DateTime now = DateTime.now();
      expireTime = DateUtil.formatDate(
        getValidDate(validity, now),
        format: DateFormats.zh_y_mo_d,
      );
    }
    return super.onInitState();
  }

  @override
  Future<bool> generateDraftBill(BuildContext context, String vchcode) async {
    // 新增会员不需要生成草稿单据
    return true;
  }

  /// 重写支付方法，支持会员开通的聚合支付
  @override
  void pay(
    BuildContext context,
    VoidCallback successCallBack, {
    VoidCallback? cancelCallBack,
    required String vchcode,
    String? scanOutNo,
  }) async {
    // 检查是否有聚合支付
    if (checkPay()) {
      // 有聚合支付，使用统一的聚合支付处理
      widget.request.etypeId = etype?.etypeId;
      widget.request.efullname = etype?.etypeName;
      widget.request.memo = memoController.text;
      widget.request.btypeId = SpTool.getStoreInfo()?.btypeId;
      widget.request.otypeId = SpTool.getStoreInfo()?.otypeId;

      // 设置payment字段
      List<PaymentDto> paymentDtos =
          paymentList
              .map(
                (e) =>
                    PaymentDto()
                      ..atypeId = e.storePayway.atypeId
                      ..paywayId = e.storePayway.paywayId
                      ..currencyAtypeTotal = e.total
                      ..outNo = e.payOutNo,
              )
              .toList();
      widget.request.payment = paymentDtos;

      UnifiedPaymentUtil.processAggregatePayment(
        context,
        paymentType: PaymentType.vipCreate,
        paymentList: paymentList,
        vchcode: vchcode,
        vipCreateInfo: widget.request,
        scanOutNo: scanOutNo,
        onResult: (payResult) {
          // 保存支付结果，用于后续获取vipId和vchcode
          _aggregatePayResult = payResult;
        },
        onSuccess: (resultType) async {
          // 支付成功，会员已创建
          if (mounted && resultType == PayResultType.SUCCEEDED) {
            successCallBack();
          }
        },
        onAction: (action) {
          // 处理用户操作
          if (action == 0 && mounted || action == 2) {
            // 手动确认收款成功
            if (action == 0) {
              memoController.text = "手工确认收款";
            }
            successCallBack();
          } else {
            // 取消支付
            if (cancelCallBack != null) {
              cancelCallBack();
            }
          }
        },
      );
    } else {
      // 没有聚合支付，直接调用成功回调
      successCallBack();
    }
  }

  ///提交
  @override
  Future<void> submit() async {
    List<AtypeInfoBean> paymentList = getEnablePaymentSubtractPettyCash(
      removeZero: true,
    );
    if (!BillModel.checkPaymentList(context, paymentList)) {
      return;
    }
    final storeInfo = SpTool.getStoreInfo();

    String vchcode = UUID.getVchcode();

    if (!mounted) return;

    pay(
      context,
      () async {
        // 如果没有聚合支付，需要调用原有的addOrEditVip接口
        if (!checkPay()) {
          widget.request.etypeId = etype?.etypeId;
          widget.request.efullname = etype?.etypeName;
          widget.request.memo = memoController.text;
          List<PaymentDto> paymentDtos =
              paymentList
                  .map(
                    (e) =>
                        PaymentDto()
                          ..atypeId = e.storePayway.atypeId
                          ..paywayId = e.storePayway.paywayId
                          ..currencyAtypeTotal = e.total
                          ..outNo = e.payOutNo,
                  )
                  .toList();
          widget.request.payment = paymentDtos;
          widget.request.btypeId = storeInfo?.btypeId;
          widget.request.otypeId = storeInfo?.otypeId;
          String? vipId = await VipModel.addOrEditVip(context, widget.request);
          if (vipId != null && mounted) {
            await super.submit();
            if (context.mounted) {
              Navigator.pop(context, vipId);
            }
          }
        } else {
          // 聚合支付已经完成会员创建，直接完成流程
          await super.submit();
          if (context.mounted) {
            // 聚合支付成功，从支付结果中获取vipId并返回
            String? vipId;
            if (_aggregatePayResult is PayResultInfo) {
              vipId = (_aggregatePayResult as PayResultInfo).vipId;
            } else if (_aggregatePayResult?.data != null) {
              // 如果是手工确认收款，从data中获取vipId
              vipId = _aggregatePayResult!.data!['manualConfirmVipId'];
            }
            Navigator.pop(context, vipId ?? "success");
          }
        }
      },
      cancelCallBack: () {
        // 取消支付，删除草稿单据
        // 使用从支付结果中获取的vchcode，因为后端会重新生成
        String? deleteVchcode =
            _aggregatePayResult?.resultDTO?.vchcode ?? vchcode;

        DeleteBillRequest deleteBillRequest = DeleteBillRequest();
        deleteBillRequest.vchcode = deleteVchcode;
        deleteBillRequest.vchtype = "OtherIncome";
        deleteBillRequest.billPostState = 0;
        deleteBillRequest.businessType = "None";
        deleteBillRequest.accountBill = true;
        BillModel.deleteBill(context, deleteBillRequest).then((value) {
          if (value?.success ?? false) {
            // 删除成功
          } else {
            if (context.mounted) {
              HaloToast.show(context, msg: value?.errorDetail?.first.message);
            }
          }
        });
      },
      vchcode: vchcode,
    );
  }
}

///会员升级或续费界面
class VipUpgradeSettlementPage extends BaseStatefulPage {
  ///会员信息
  final VipWithLevelAssertsRightsCardDTO vipInfo;

  final VipLevelDto level;

  final VipLevelRule levelRule;

  const VipUpgradeSettlementPage({
    Key? key,
    required this.vipInfo,
    required this.level,
    required this.levelRule,
  }) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _VipUpgradeSettlementPageState();
}

class _VipUpgradeSettlementPageState
    extends BaseStatefulPageState<VipUpgradeSettlementPage>
    with SettlementMixin, VipSettlementMixin, _VipSettlementMixin {
  @override
  VipWithLevelAssertsRightsCardDTO? get vipInfo => widget.vipInfo;

  @override
  String get levelName => widget.level.levelName ?? "";

  @override
  num get moneyReceived => _moneyReceived;

  @override
  String get priceStr => _getValidity(widget.levelRule);

  @override
  int get validity => widget.levelRule.validity ?? 0;

  @override
  String get vipName => widget.vipInfo.vip?.name ?? "";

  @override
  String get vipPhone => widget.vipInfo.vip?.phone ?? "";

  num _moneyReceived = 0;

  ///抵扣金额
  num discountMoney = 0;

  @override
  Future<void> init() async {
    VipLevelRule rule = widget.levelRule;
    _moneyReceived = rule.levelPrice ?? 0;
    DateTime now = DateTime.now();
    //会员开始时间
    DateTime startDate = now;
    //当前已经是付费会员且未过期,计算抵扣
    if (widget.vipInfo.level?.vipType == true &&
        !isVipExpired(widget.vipInfo.vip!.validDate)) {
      // 同等级之间的续费，且并不是续费到永久会员，
      // 无需计算抵扣
      // 会员开始时间为之前的过期时间
      if (widget.vipInfo.level!.levelId == widget.level.id && validity > 0) {
        startDate = DateTime.parse(widget.vipInfo.vip!.validDate!);
      } else {
        //获取会员等级费用记录
        List<VipFeeRecord> list = await VipModel.selectFeeRecordByVipId(
          context,
          vipId: widget.vipInfo.vip!.id!,
        );
        list.removeWhere((element) => isVipExpired(element.endDate));
        if (list.isNotEmpty) {
          VipFeeRecord? foreverRecord = list.cast<VipFeeRecord?>().firstWhere(
            (element) => isForever(element!.endDate),
            orElse: () => null,
          );
          Decimal levelPrice = Decimal.parse(rule.levelPrice.toString());
          //抵扣的钱
          Decimal discountTotal = Decimal.zero;
          //如果之前就是永久会员，则抵扣金额就是该等级支付的钱
          if (foreverRecord != null) {
            discountTotal = Decimal.parse(
              (foreverRecord.total ?? 0).toString(),
            );
          }
          //不是永久会员，则需要根据天数计算抵扣
          else {
            for (var record in list) {
              num total = record.total ?? 0;
              if (total != 0) {
                Decimal discount;
                DateTime start = DateTime.parse(record.startDate!);
                DateTime end = DateTime.parse(record.endDate!);
                //该有效期已经开始
                if (start.isBefore(now)) {
                  //该记录一共多少天
                  int period = end.difference(start).inDays;
                  //剩余多少天
                  int remainDays = end.difference(now).inDays;
                  discount = (Decimal.fromInt(remainDays) *
                          Decimal.parse(total.toString()) /
                          Decimal.fromInt(period))
                      .toDecimal(scaleOnInfinitePrecision: 8)
                      .round(scale: 2);
                }
                //未开始，全额抵扣
                else {
                  discount = Decimal.parse(total.toString());
                }
                discountTotal += discount;
              }
            }
          }
          discountTotal = discountTotal.round(scale: 2);
          if (levelPrice <= discountTotal) {
            discountTotal = levelPrice;
            _moneyReceived = 0;
          } else {
            _moneyReceived =
                (levelPrice - discountTotal).round(scale: 2).toDouble();
          }
          discountMoney = discountTotal.toDouble();
        }
      }
    }
    //计算到期时间
    if (validity == 0) {
      expireTime = "一直有效";
    } else {
      expireTime = DateUtil.formatDate(
        getValidDate(validity, startDate),
        format: DateFormats.zh_y_mo_d,
      );
    }
    return super.init();
  }

  @override
  List<Widget> buildSettlementInfoRows() {
    if (discountMoney > 0) {
      return super.buildSettlementInfoRows()
        ..add(buildNormalRow(title: "未到期会费折算：￥$discountMoney", content: ""));
    }
    return super.buildSettlementInfoRows();
  }

  @override
  Future<void> submit() async {
    List<AtypeInfoBean> paymentList = getEnablePaymentSubtractPettyCash(
      removeZero: true,
    );
    if (!BillModel.checkPaymentList(context, paymentList)) {
      return;
    }
    final storeInfo = SpTool.getStoreInfo();

    String vchcode = UUID.getVchcode();

    if (!mounted) return;

    PerformanceCaptureUtil.start(PerformanceTimeName.vipRecharge);

    // 使用新的统一聚合支付处理
    await VipModel.renewOrUpgradeSVIPWithAggregatePayment(
      context,
      vchcode: vchcode,
      vipId: widget.vipInfo.vip?.id ?? "",
      levelId: widget.level.id ?? "",
      levelRuleId: widget.levelRule.id ?? "",
      payment: paymentList,
      btypeId: storeInfo?.btypeId ?? "",
      etypeId: etype?.etypeId,
      otypeId: storeInfo?.otypeId,
      memo: memoController.text,
      onSuccess: (PayResultType resultType) async {
        PerformanceCaptureUtil.end(PerformanceTimeName.vipRecharge);
        if (resultType == PayResultType.SUCCEEDED && mounted) {
          await super.submit();
          if (mounted) {
            Navigator.pop(context, true);
          }
        }
      },
      onAction: (int index) {
        if (index == 0 && mounted) {
          // 手工确认收款
          PerformanceCaptureUtil.end(PerformanceTimeName.vipRecharge);
          super.submit().then((_) {
            if (mounted) {
              Navigator.pop(context, true);
            }
          });
        } else {
          // 取消支付，删除草稿单据
          DeleteBillRequest deleteBillRequest = DeleteBillRequest();
          deleteBillRequest.vchcode = vchcode;
          deleteBillRequest.vchtype = "OtherIncome";
          deleteBillRequest.billPostState = 0;
          deleteBillRequest.businessType = "None";
          deleteBillRequest.accountBill = true;
          BillModel.deleteBill(context, deleteBillRequest).then((value) {
            if (value?.success ?? false) {
            } else {
              if (context.mounted) {
                HaloToast.show(context, msg: value?.errorDetail?.first.message);
              }
            }
          });
        }
      },
    );
  }

  ///构建付费会员定价说明文字
  String _getValidity(VipLevelRule? rule) {
    if (rule == null) {
      return "";
    } else {
      int validity = rule.validity ?? 0;
      String period;
      if (validity <= 0) {
        period = "一直有效";
      } else if (validity == 12) {
        period = "1年";
      } else if (validity == 36) {
        period = "3年";
      } else {
        period = "$validity个月";
      }
      return "￥${rule.levelPrice ?? 0}/$period";
    }
  }

  @override
  Future<bool> generateDraftBill(BuildContext context, String vchcode) async {
    final storeInfo = SpTool.getStoreInfo();
    List<AtypeInfoBean> paymentList = getEnablePaymentSubtractPettyCash(
      removeZero: true,
    );
    return VipModel.submitVipLevelBill(
      context,
      vchcode: vchcode,
      vipId: widget.vipInfo.vip?.id ?? "",
      levelId: widget.level.id ?? "",
      levelRuleId: widget.levelRule.id ?? "",
      etypeId: etype?.etypeId,
      btypeId: storeInfo?.btypeId ?? "",
      otypeId: storeInfo?.otypeId,
      payment: paymentList,
    );
  }
}

mixin _VipSettlementMixin<T extends BaseStatefulPage> on VipSettlementMixin<T> {
  String? expireTime;

  ///定价
  String get priceStr;

  String get vipName;

  String get vipPhone;

  String get levelName;

  int get validity;

  ///备注
  @override
  final TextEditingController memoController = TextEditingController();

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container(
      color: const Color(0xFFF0F0F0),
      padding: EdgeInsets.all(14.w),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.h)),
        ),
        padding: EdgeInsets.only(left: 38.w, right: 30.w),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(top: 30.h, bottom: 15.h),
              child: Row(
                children: [
                  buildAvatar(context),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(left: 16.w),
                      child: buildVipInfo(context, enableClearVip: false),
                    ),
                  ),
                ],
              ),
            ),
            Divider(height: 2.h, color: const Color(0xFFD5D5D5)),
            ...buildSettlementInfoRows(),
            Expanded(child: Container()),
            DottedLine(dashPattern: [8.w, 8.w], color: const Color(0xFFA5A5A5)),
            Container(
              padding: EdgeInsets.only(top: 24.h, bottom: 24.h),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.only(right: 8.w),
                    child: Text(
                      "应收金额",
                      style: TextStyle(
                        fontSize: 30.sp,
                        color: const Color(0xFF333333),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: HaloPosLabel(
                      "￥$moneyReceived",
                      textAlign: TextAlign.end,
                      textStyle: TextStyle(
                        fontSize: 30.sp,
                        color: const Color(0xFF333333),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> buildSettlementInfoRows() {
    return [
      buildNormalRow(
        title: "会员定价：$priceStr",
        content: "",
        padding: EdgeInsets.only(top: 15.h),
      ),
      buildNormalRow(title: "会员到期时间:$expireTime", content: ""),
    ];
  }

  ///头像
  Widget buildAvatar(BuildContext context) {
    return Container(
      height: 76.w,
      width: 76.w,
      alignment: Alignment.center,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: Color(0xFFFFB207),
      ),
      child: IconFont(IconNames.huiyuan, size: 36.w, color: "#ffffff"),
    );
  }

  Widget buildVipInfo(BuildContext context, {bool enableClearVip = true}) {
    return SizedBox(
      width: 286.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  "会员姓名：$vipName",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(fontSize: 20.sp),
                ),
              ),
              Expanded(
                child: Text(
                  "会员电话：$vipPhone",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(fontSize: 20.sp),
                ),
              ),
            ],
          ),
          SizedBox(height: 3.h),
          //余额和积分
          Text(
            "会员等级：$levelName",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(fontSize: 20.sp),
          ),
        ],
      ),
    );
  }
}
