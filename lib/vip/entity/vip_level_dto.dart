/// id : ""
/// levelName : ""

///会员等级
class VipLevelDto {
  VipLevelDto({
    this.id,
    this.levelName,
    this.vipLevel,
    this.deleted,
    this.stoped,
    this.createTime,
    this.updateTime,
    this.cardTemplateId,
    this.upgradeValue,
    this.vipPersonCount,
    this.vipType,
    this.levelRule,
  });

  VipLevelDto.fromJson(dynamic json) {
    id = json['id'];
    levelName = json['vipName'];
    vipLevel = json['vipLevel'];
    deleted = json['deleted'];
    stoped = json['stoped'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    cardTemplateId = json['cardTemplateId'];
    upgradeValue = json['upgradeValue'];
    vipPersonCount = json['vipPersonCount'];
    vipType = json['vipType'];
    levelRule = (json['levelRule'] as List?)
        ?.map((e) => VipLevelRule.fromJson(e))
        .toList();
  }

  ///会员等级id
  String? id;

  ///会员等级名称
  String? levelName;

  ///等级
  int? vipLevel;

  ///是否删除
  bool? deleted;

  ///是否停用
  bool? stoped;

  ///创建时间
  String? createTime;

  ///更新时间
  String? updateTime;

  ///模板id
  String? cardTemplateId;

  ///升级所需成长值
  int? upgradeValue;

  ///会员数量
  int? vipPersonCount;

  ///0:免费会员 1:付费会员
  int? vipType;

  ///付费会员等级收费规则
  List<VipLevelRule>? levelRule;

  VipLevelDto copyWith({
    String? id,
    String? levelName,
    int? vipLevel,
    bool? deleted,
    bool? stoped,
    String? createTime,
    String? updateTime,
    String? cardTemplateId,
    int? upgradeValue,
    int? vipPersonCount,
    int? vipType,
    List<VipLevelRule>? levelRule,
  }) =>
      VipLevelDto(
        id: id ?? this.id,
        levelName: levelName ?? this.levelName,
        vipLevel: vipLevel ?? this.vipLevel,
        deleted: deleted ?? this.deleted,
        stoped: stoped ?? this.stoped,
        createTime: createTime ?? this.createTime,
        updateTime: updateTime ?? this.updateTime,
        cardTemplateId: cardTemplateId ?? this.cardTemplateId,
        upgradeValue: upgradeValue ?? this.upgradeValue,
        vipPersonCount: vipPersonCount ?? this.vipPersonCount,
        vipType: vipType ?? this.vipType,
        levelRule: levelRule ?? this.levelRule,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['vipName'] = levelName;
    map['vipLevel'] = vipLevel;
    map['deleted'] = deleted;
    map['stoped'] = stoped;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['cardTemplateId'] = cardTemplateId;
    map['upgradeValue'] = upgradeValue;
    map['vipPersonCount'] = vipPersonCount;
    map['vipType'] = vipType;
    map['levelRule'] = levelRule?.map((e) => e.toJson()).toList();
    return map;
  }
}

///付费会员等级规则
class VipLevelRule {
  VipLevelRule({
    this.id,
    this.stoped,
    this.deleted,
    this.createTime,
    this.updateTime,
    this.vipLevelId,
    this.levelPrice,
    this.validity,
  });

  VipLevelRule.fromJson(dynamic json) {
    id = json['id'];
    stoped = json['stoped'];
    deleted = json['deleted'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    vipLevelId = json['vipLevelId'];
    levelPrice = json['levelPrice'];
    validity = json['validity'];
  }

  ///id
  String? id;

  ///是否停用
  int? stoped;

  ///是否删除
  int? deleted;

  ///创建时间
  String? createTime;

  ///更新时间
  String? updateTime;

  ///会员等级id
  String? vipLevelId;

  ///价格
  num? levelPrice;

  ///有效期，月的数量
  int? validity;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['stoped'] = stoped;
    map['deleted'] = deleted;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['vipLevelId'] = vipLevelId;
    map['levelPrice'] = levelPrice;
    map['validity'] = validity;
    return map;
  }
}
