/// 对应接口"/vip/getVipByCodeNew"的请求实体类
class GetVipByCodeDTO {
  /// 查询参数（手机号、会员名称、微信code）其一
  String? code;

  /// 门店id
  String? otypeId;

  /// 会员id
  String? vipId;

  /// 门店类型： 4、直营店
  int? type;

  /// 是否模糊查询手机号
  bool? fuzzyQuery;

  /// 页码，从1开始
  int? pageIndex;

  /// 每页大小
  int? pageSize;

  GetVipByCodeDTO({
    this.code,
    this.otypeId,
    this.vipId,
    this.type = 4,
    this.fuzzyQuery = true,
    this.pageIndex = 1,
    this.pageSize = 20,
  });

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'otypeId': otypeId,
      'vipId': vipId,
      'type': type,
      'fuzzyQuery': fuzzyQuery,
      'pageIndex': pageIndex,
      'pageSize': pageSize,
    };
  }

  factory GetVipByCodeDTO.fromJson(Map<String, dynamic> json) {
    return GetVipByCodeDTO(
      code: json['code'],
      otypeId: json['otypeId'],
      vipId: json['vipId'],
      type: json['type'],
      fuzzyQuery: json['fuzzyQuery'],
      pageIndex: json['pageIndex'],
      pageSize: json['pageSize'],
    );
  }
}
