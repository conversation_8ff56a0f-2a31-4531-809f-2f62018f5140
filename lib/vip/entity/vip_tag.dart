///会员标签
class VipTag {
  ///标签id
  String? id;

  ///账套id
  String? profileId;

  ///文本
  String? text;

  ///颜色
  String? color;

  String? operate;

  VipTag({this.id, this.profileId, this.text, this.color, this.operate});

  VipTag.fromJson(dynamic json) {
    id = json["id"];
    profileId = json["profileId"];
    text = json["text"];
    color = json["colour"];
    operate = json["operate"];
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "profileId": profileId,
        "text": text,
        "colour": color,
        "operate": operate,
      };

  VipTag copyWith(
          {String? id,
          String? profileId,
          String? text,
          String? color,
          String? operate}) =>
      VipTag(
        id: id ?? this.id,
        profileId: profileId ?? this.profileId,
        text: text ?? this.text,
        color: color ?? this.color,
        operate: id ?? this.operate,
      );
}
