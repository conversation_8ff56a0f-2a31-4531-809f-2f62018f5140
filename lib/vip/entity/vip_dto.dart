///会员
class Vip {
  ///会员id
  String? id;

  ///手机号码
  String? phone;

  ///姓名
  String? name;

  ///会员等级id
  String? vipLevelId;

  ///会员开始时间
  String? startTime;

  ///会员截止时间
  String? endTime;

  ///会员成长值
  int? growthValue;

  ///累计消费金额
  double? payMoneyCount;

  ///累计消费次数
  int? payTimeCount;

  ///身份证号码
  String? idCode;

  ///性别 2=》女 1=》男
  int? sex;

  ///生日
  String? birthday;

  ///邮箱
  String? email;

  ///会员卡类型
  int? type;

  ///会员标签，用,分隔
  String? tags;

  ///会员储值消费密码
  String? password;

  ///付费会员过期时间
  String? validDate;

  ///详细地址
  String? address;

  ///会员地址-省
  String? province;

  ///会员地址-市
  String? city;

  ///会员地址-区
  String? district;

  ///会员地址-街道
  String? street;

  ///会员地址-完整地址
  String? fullAddress;

  ///付费会员是否过期
  bool? expired;

  ///会员门店类型: 0 全部门店 1 部分门店
  int applyStoreType = 0;

  Vip.fromJson(Map<String, dynamic> json) {
    id = json["id"]?.toString();
    password = json["password"];
    phone = json["phone"]?.toString();
    name = json["name"];
    vipLevelId = json["vipLevelId"]?.toString();
    startTime = json["startTime"];
    endTime = json["endTime"];
    growthValue = json["growthValue"];
    idCode = json["idCode"];
    birthday = json["birthday"];
    email = json["email"];
    sex = json["sex"];
    payMoneyCount = json["payMoneyCount"];
    payTimeCount = json["payTimeCount"];
    type = json["type"];
    tags = json["tags"];
    validDate = json["validDate"];
    address = json["address"];
    province = json["province"];
    city = json["city"];
    district = json["district"];
    street = json["street"];
    fullAddress = json["fullAddress"];
    expired = json["expired"];
    applyStoreType = json["applyStoreType"] ?? 0;
  }

  Map<String, dynamic> toJson() => {
    "password": password,
    "id": id,
    "phone": phone,
    "name": name,
    "vipLevelId": vipLevelId,
    "startTime": startTime,
    "endTime": endTime,
    "growthValue": growthValue,
    "payMoneyCount": payMoneyCount,
    "payTimeCount": payTimeCount,
    "idCode": idCode,
    "birthday": birthday,
    "email": email,
    "type": type,
    "tags": tags,
    "validDate": validDate,
    "address": address,
    "province": province,
    "city": city,
    "district": district,
    "street": street,
    "fullAddress": fullAddress,
    "expired": expired,
    "applyStoreType": applyStoreType,
  };
}
