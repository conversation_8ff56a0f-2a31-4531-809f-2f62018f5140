class CardCouponCardTemplate {
  String? id;
  String? profileId;
  String? createTime;
  String? updateTime;
  int? cardType;
  String? fullname;
  int? validType;
  String? validValue;
  dynamic expiryString;
  String? comment;
  int? superposition;
  bool? deleted;
  int? stoped;
  List<String>? otypeIds;
  String? ofullNames;
  int? page;
  int? pageSize;
  dynamic orderField;
  int? createdCount;
  dynamic validFullName;
  int? userCount;
  dynamic checkDate;
  dynamic enablePaging;
  dynamic vipId;
  List<MemberEquityValuesBean>? memberEquityValues;
  dynamic cardTypes;
  dynamic rights;
  dynamic cardTemplateDetails;

  CardCouponCardTemplate();

  CardCouponCardTemplate.fromJson(Map<String, dynamic> map) {
    id = map['id'];
    profileId = map['profileId'];
    createTime = map['createTime'];
    updateTime = map['updateTime'];
    cardType = map['cardType'];
    fullname = map['fullname'];
    validType = map['validType'];
    validValue = map['validValue'];
    expiryString = map['expiryString'];
    comment = map['comment'];
    superposition = map['superposition'];
    deleted = map['deleted'];
    stoped = map['stoped'];
    otypeIds = map['otypeIds']?.cast<String>();
    ofullNames = map['ofullNames'];
    page = map['page'];
    pageSize = map['pageSize'];
    orderField = map['orderField'];
    createdCount = map['createdCount'];
    validFullName = map['validFullName'];
    userCount = map['userCount'];
    checkDate = map['checkDate'];
    enablePaging = map['enablePaging'];
    vipId = map['vipId'];
    memberEquityValues = (map['memberEquityValues'] as List?)
        ?.map<MemberEquityValuesBean>(
            (e) => MemberEquityValuesBean.fromMap(e.cast<String, dynamic>()))
        .toList();
    cardTypes = map['cardTypes'];
    rights = map['rights'];
    cardTemplateDetails = map['cardTemplateDetails'];
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "profileId": profileId,
        "createTime": createTime,
        "updateTime": updateTime,
        "cardType": cardType,
        "fullname": fullname,
        "validType": validType,
        "validValue": validValue,
        "expiryString": expiryString,
        "comment": comment,
        "superposition": superposition,
        "deleted": deleted,
        "stoped": stoped,
        "otypeIds": otypeIds,
        "ofullNames": ofullNames,
        "page": page,
        "pageSize": pageSize,
        "orderField": orderField,
        "createdCount": createdCount,
        "validFullName": validFullName,
        "userCount": userCount,
        "checkDate": checkDate,
        "enablePaging": enablePaging,
        "vipId": vipId,
        "memberEquityValues":
            memberEquityValues?.map((e) => e.toJson()).toList(),
        "cardTypes": cardTypes,
        "rights": rights,
        "cardTemplateDetails": cardTemplateDetails,
      };
}

class MemberEquityValuesBean {
  String? id;
  String? valueType;
  String? fullName;
  String? rightsDesc;
  String? codes;
  String? profileId;
  int? deleted;
  String? createTime;
  String? updateTime;
  bool? stoped;
  dynamic cardTemplateId;
  dynamic cardDeleted;
  bool? check;
  List<DetailListBean>? detailList;

  MemberEquityValuesBean.fromMap(Map<String, dynamic> map) {
    id = map['id'];
    valueType = map['valueType'];
    fullName = map['fullName'];
    rightsDesc = map['rightsDesc'];
    codes = map['codes'];
    profileId = map['profileId'];
    deleted = map['deleted'];
    createTime = map['createTime'];
    updateTime = map['updateTime'];
    stoped = map['stoped'];
    cardTemplateId = map['cardTemplateId'];
    cardDeleted = map['cardDeleted'];
    check = map['check'];
    detailList = (map['detailList'] as List?)
        ?.map<DetailListBean>(
            (o) => DetailListBean.fromMap(o.cast<String, dynamic>()))
        .toList();
  }

  Map toJson() => {
        "id": id,
        "valueType": valueType,
        "fullName": fullName,
        "rightsDesc": rightsDesc,
        "codes": codes,
        "profileId": profileId,
        "deleted": deleted,
        "createTime": createTime,
        "updateTime": updateTime,
        "stoped": stoped,
        "cardTemplateId": cardTemplateId,
        "cardDeleted": cardDeleted,
        "check": check,
        "detailList": detailList?.map((e) => e.toJson()).toList(),
      };
}

class DetailListBean {
  String? id;
  String? createTime;
  String? updateTime;
  bool? deleted;
  bool? stoped;
  String? equityValueId;
  double? valueCondition;
  double? valueDetail;
  int? priority;
  String? profileId;
  int? miniCostType;
  int? ptypeRang;
  List<PtypeListBean>? ptypeList;

  DetailListBean.fromMap(Map<String, dynamic> map) {
    id = map['id'];
    createTime = map['createTime'];
    updateTime = map['updateTime'];
    deleted = map['deleted'];
    stoped = map['stoped'];
    equityValueId = map['equityValueId'];
    valueCondition = map['valueCondition'];
    valueDetail = map['valueDetail'];
    priority = map['priority'];
    profileId = map['profileId'];
    miniCostType = map['miniCostType'];
    ptypeRang = map['ptypeRang'];
    ptypeList = (map['ptypeList'] as List?)
        ?.map<PtypeListBean>((o) => PtypeListBean.fromMap(o))
        .toList();
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleted": deleted,
        "stoped": stoped,
        "equityValueId": equityValueId,
        "valueCondition": valueCondition,
        "valueDetail": valueDetail,
        "priority": priority,
        "profileId": profileId,
        "miniCostType": miniCostType,
        "ptypeRang": ptypeRang,
        "ptypeList": ptypeList?.map((e) => e.toJson()).toList(),
      };
}

class PtypeListBean {
  String? id;
  String? profileId;
  String? createTime;
  String? updateTime;
  String? ptypeId;
  String? skuId;
  String? unitId;
  bool? deleted;
  String? pid;
  int? rowindex;
  String? equityValueDetailId;
  int? ptypeRang;
  String? ptypeName;
  String? usercode;
  String? propertyName;
  double? retailPrice;
  String? unitName;
  String? pcategory;
  bool? batchEnabled;
  int? snEnabled;

  PtypeListBean.fromMap(Map<String, dynamic> map) {
    id = map['id'];
    profileId = map['profileId'];
    createTime = map['createTime'];
    updateTime = map['updateTime'];
    ptypeId = map['ptypeId'];
    skuId = map['skuId'];
    unitId = map['unitId'];
    deleted = map['deleted'];
    pid = map['pid'];
    rowindex = map['rowindex'];
    equityValueDetailId = map['equityValueDetailId'];
    ptypeRang = map['ptypeRang'];
    ptypeName = map['ptypeName'];
    usercode = map['usercode'];
    propertyName = map['propertyName'];
    retailPrice = map['retailPrice'];
    unitName = map['unitName'];
    pcategory = map['pcategory'];
    batchEnabled = map['batchEnabled'];
    snEnabled = map['snEnabled'];
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "createTime": createTime,
        "updateTime": updateTime,
        "ptypeId": ptypeId,
        "skuId": skuId,
        "unitId": unitId,
        "deleted": deleted,
        "pid": pid,
        "rowindex": rowindex,
        "equityValueDetailId": equityValueDetailId,
        "ptypeRang": ptypeRang,
        "ptypeName": ptypeName,
        "usercode": usercode,
        "propertyName": propertyName,
        "retailPrice": retailPrice,
        "unitName": unitName,
        "pcategory": pcategory,
        "batchEnabled": batchEnabled,
        "snEnabled": snEnabled,
      };
}
