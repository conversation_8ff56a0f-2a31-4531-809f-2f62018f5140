/// 会员开通聚合支付请求模型

import 'add_or_edit_vip_request.dart';
import 'vip_level_payment_request.dart';

/// 会员开通聚合支付请求
class VipCreatePaymentRequest {
  /// 会员信息
  AddOrEditVipDTO? vipInfo;

  /// 聚合支付信息
  AggregatePaymentRequest? payInfo;

  VipCreatePaymentRequest({this.vipInfo, this.payInfo});

  Map<String, dynamic> toJson() {
    return {'vipInfo': vipInfo?.toJson(), 'payInfo': payInfo?.toJson()};
  }

  factory VipCreatePaymentRequest.fromJson(Map<String, dynamic> json) {
    return VipCreatePaymentRequest(
      vipInfo:
          json['vipInfo'] != null
              ? AddOrEditVipDTO.fromJson(json['vipInfo'])
              : null,
      payInfo:
          json['payInfo'] != null
              ? AggregatePaymentRequest.fromJson(json['payInfo'])
              : null,
    );
  }

  @override
  String toString() {
    return 'VipCreatePaymentRequest{vipInfo: $vipInfo, payInfo: $payInfo}';
  }
}
