/// 会员升级聚合支付请求模型

/// 会员升级聚合支付请求
class VipLevelPaymentRequest {
  /// 会员升级信息
  VipUpgradeInfo? upgradeInfo;

  /// 聚合支付信息
  AggregatePaymentRequest? payInfo;

  VipLevelPaymentRequest({this.upgradeInfo, this.payInfo});

  Map<String, dynamic> toJson() {
    return {'upgradeInfo': upgradeInfo?.toJson(), 'payInfo': payInfo?.toJson()};
  }

  factory VipLevelPaymentRequest.fromJson(Map<String, dynamic> json) {
    return VipLevelPaymentRequest(
      upgradeInfo:
          json['upgradeInfo'] != null
              ? VipUpgradeInfo.fromJson(json['upgradeInfo'])
              : null,
      payInfo:
          json['payInfo'] != null
              ? AggregatePaymentRequest.fromJson(json['payInfo'])
              : null,
    );
  }

  @override
  String toString() {
    return 'VipLevelPaymentRequest{upgradeInfo: $upgradeInfo, payInfo: $payInfo}';
  }
}

/// 会员充值聚合支付请求
class VipRechargePaymentRequest {
  /// 会员充值信息
  VipRechargeInfo? rechargeInfo;

  /// 聚合支付信息
  AggregatePaymentRequest? payInfo;

  VipRechargePaymentRequest({this.rechargeInfo, this.payInfo});

  Map<String, dynamic> toJson() {
    return {
      'rechargeInfo': rechargeInfo?.toJson(),
      'payInfo': payInfo?.toJson(),
    };
  }

  factory VipRechargePaymentRequest.fromJson(Map<String, dynamic> json) {
    return VipRechargePaymentRequest(
      rechargeInfo:
          json['rechargeInfo'] != null
              ? VipRechargeInfo.fromJson(json['rechargeInfo'])
              : null,
      payInfo:
          json['payInfo'] != null
              ? AggregatePaymentRequest.fromJson(json['payInfo'])
              : null,
    );
  }

  @override
  String toString() {
    return 'VipRechargePaymentRequest{rechargeInfo: $rechargeInfo, payInfo: $payInfo}';
  }
}

/// 聚合支付请求信息
class AggregatePaymentRequest {
  String? atypeId;
  String? authCode;
  num? originalAmount;
  num? tradeAmount;
  num? ignoreAmount;
  String? vchcode;
  String? paywayId;
  String? outNo;
  num? billTotal;

  AggregatePaymentRequest({
    this.atypeId,
    this.authCode,
    this.originalAmount,
    this.tradeAmount,
    this.ignoreAmount,
    this.vchcode,
    this.paywayId,
    this.outNo,
    this.billTotal,
  });

  Map<String, dynamic> toJson() {
    return {
      'atypeId': atypeId,
      'authCode': authCode,
      'originalAmount': originalAmount,
      'tradeAmount': tradeAmount,
      'ignoreAmount': ignoreAmount,
      'vchcode': vchcode,
      'paywayId': paywayId,
      'outNo': outNo,
      'billTotal': billTotal,
    };
  }

  factory AggregatePaymentRequest.fromJson(Map<String, dynamic> json) {
    return AggregatePaymentRequest(
      atypeId: json['atypeId'],
      authCode: json['authCode'],
      originalAmount: json['originalAmount'],
      tradeAmount: json['tradeAmount'],
      ignoreAmount: json['ignoreAmount'],
      vchcode: json['vchcode'],
      paywayId: json['paywayId'],
      outNo: json['outNo'],
      billTotal: json['billTotal'],
    );
  }

  @override
  String toString() {
    return 'AggregatePaymentRequest{atypeId: $atypeId, authCode: $authCode, originalAmount: $originalAmount, tradeAmount: $tradeAmount, ignoreAmount: $ignoreAmount, vchcode: $vchcode, paywayId: $paywayId, outNo: $outNo, billTotal: $billTotal}';
  }
}

/// 会员升级信息（别名）
typedef RenewOrUpgradeSVIPDTO = VipUpgradeInfo;

/// 会员充值信息（别名）
typedef VipRechargeDTO = VipRechargeInfo;

/// 会员升级信息
class VipUpgradeInfo {
  String? vchcode;
  String? vipId;
  String? levelId;
  String? levelRuleId;
  String? levelName;
  num? levelPrice;
  String? etypeId;
  String? efullname;
  String? btypeId;
  String? otypeId;
  String? memo;
  List<dynamic>? payment;

  VipUpgradeInfo({
    this.vchcode,
    this.vipId,
    this.levelId,
    this.levelRuleId,
    this.levelName,
    this.levelPrice,
    this.etypeId,
    this.efullname,
    this.btypeId,
    this.otypeId,
    this.memo,
    this.payment,
  });

  Map<String, dynamic> toJson() {
    return {
      'vchcode': vchcode,
      'vipId': vipId,
      'levelId': levelId,
      'levelRuleId': levelRuleId,
      'levelName': levelName,
      'levelPrice': levelPrice,
      'etypeId': etypeId,
      'efullname': efullname,
      'btypeId': btypeId,
      'otypeId': otypeId,
      'memo': memo,
      'payment': payment,
    };
  }

  factory VipUpgradeInfo.fromJson(Map<String, dynamic> json) {
    return VipUpgradeInfo(
      vchcode: json['vchcode'],
      vipId: json['vipId'],
      levelId: json['levelId'],
      levelRuleId: json['levelRuleId'],
      levelName: json['levelName'],
      levelPrice: json['levelPrice'],
      etypeId: json['etypeId'],
      efullname: json['efullname'],
      btypeId: json['btypeId'],
      otypeId: json['otypeId'],
      memo: json['memo'],
      payment: json['payment'],
    );
  }

  @override
  String toString() {
    return 'VipUpgradeInfo{vchcode: $vchcode, vipId: $vipId, levelId: $levelId, levelRuleId: $levelRuleId, levelName: $levelName, levelPrice: $levelPrice, etypeId: $etypeId, efullname: $efullname, btypeId: $btypeId, otypeId: $otypeId, memo: $memo, payment: $payment}';
  }
}

/// 会员充值信息
class VipRechargeInfo {
  String? vchcode;
  String? vipId;
  String? vipLevelId;
  String? rechargeId;
  num? rechargeMoney;
  num? rechargeAmount;
  String? etypeId;
  String? efullname;
  String? btypeId;
  String? otypeId;
  String? ktypeId;
  String? memo;
  String? summary;
  String? createEtypeId;
  num? giveMoney;
  num? score;
  String? cardId;
  num? cardQty;
  dynamic goodsBill;
  String? cashierId;
  // 添加 payment 字段，统一两个类的功能
  List<dynamic>? payment;

  VipRechargeInfo({
    this.vchcode,
    this.vipId,
    this.vipLevelId,
    this.rechargeId,
    this.rechargeMoney,
    this.rechargeAmount,
    this.etypeId,
    this.efullname,
    this.btypeId,
    this.otypeId,
    this.ktypeId,
    this.memo,
    this.summary,
    this.createEtypeId,
    this.giveMoney,
    this.score,
    this.cardId,
    this.cardQty,
    this.goodsBill,
    this.cashierId,
    this.payment,
  });

  Map<String, dynamic> toJson() {
    return {
      'vchcode': vchcode,
      'vipId': vipId,
      'vipLevelId': vipLevelId,
      'rechargeId': rechargeId,
      'rechargeMoney': rechargeMoney,
      'rechargeAmount': rechargeAmount,
      'etypeId': etypeId,
      'efullname': efullname,
      'btypeId': btypeId,
      'otypeId': otypeId,
      'ktypeId': ktypeId,
      'memo': memo,
      'summary': summary,
      'createEtypeId': createEtypeId,
      'giveMoney': giveMoney,
      'score': score,
      'cardId': cardId,
      'cardQty': cardQty,
      'goodsBill': goodsBill,
      'cashierId': cashierId,
      'payment': payment,
    };
  }

  factory VipRechargeInfo.fromJson(Map<String, dynamic> json) {
    return VipRechargeInfo(
      vchcode: json['vchcode'],
      vipId: json['vipId'],
      vipLevelId: json['vipLevelId'],
      rechargeId: json['rechargeId'],
      rechargeMoney: json['rechargeMoney'],
      rechargeAmount: json['rechargeAmount'],
      etypeId: json['etypeId'],
      efullname: json['efullname'],
      btypeId: json['btypeId'],
      otypeId: json['otypeId'],
      ktypeId: json['ktypeId'],
      memo: json['memo'],
      summary: json['summary'],
      createEtypeId: json['createEtypeId'],
      giveMoney: json['giveMoney'],
      score: json['score'],
      cardId: json['cardId'],
      cardQty: json['cardQty'],
      goodsBill: json['goodsBill'],
      cashierId: json['cashierId'],
      payment: json['payment'],
    );
  }
}
