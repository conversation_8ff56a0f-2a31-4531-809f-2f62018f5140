/// 会员开通支付确认请求模型

import '../../bill/entity/pay_result_dto.dart';
import 'vip_create_payment_request.dart';
import 'vip_level_payment_request.dart';
import 'add_or_edit_vip_request.dart';

/// 会员开通支付确认请求
/// 参数与聚合支付接口保持一致
class VipCreatePaymentConfirmRequest {
  /// 会员信息（与聚合支付请求一致）
  AddOrEditVipDTO? vipInfo;

  /// 聚合支付信息（与聚合支付请求一致）
  AggregatePaymentRequest? payInfo;

  /// 确认类型：1-轮询确认，2-手动确认
  int? confirmType;

  VipCreatePaymentConfirmRequest({
    this.vipInfo,
    this.payInfo,
    this.confirmType,
  });

  /// 从VipCreatePaymentRequest创建确认请求（用于轮询确认）
  factory VipCreatePaymentConfirmRequest.fromPaymentRequest(
    VipCreatePaymentRequest paymentRequest, {
    int confirmType = 1,
  }) {
    return VipCreatePaymentConfirmRequest(
      vipInfo: paymentRequest.vipInfo,
      payInfo: paymentRequest.payInfo,
      confirmType: confirmType,
    );
  }

  /// 从PayResult创建确认请求（用于手动确认）
  factory VipCreatePaymentConfirmRequest.fromPayResult(
    VipCreatePaymentRequest originalRequest,
    PayResult payResult, {
    int confirmType = 1,
  }) {
    // 从PayResult中提取vchcode等信息，更新到vipInfo中
    AddOrEditVipDTO? updatedVipInfo = originalRequest.vipInfo;
    if (updatedVipInfo != null && payResult.resultDTO?.vchcode != null) {
      // 创建新的vipInfo实例，更新vchcode
      updatedVipInfo = AddOrEditVipDTO.fromJson(updatedVipInfo.toJson());
      updatedVipInfo.vchcode = payResult.resultDTO!.vchcode;
    }

    // 从PayResult中提取支付信息，更新到payInfo中
    AggregatePaymentRequest? updatedPayInfo = originalRequest.payInfo;
    if (updatedPayInfo != null) {
      updatedPayInfo = AggregatePaymentRequest(
        atypeId: updatedPayInfo.atypeId,
        authCode: updatedPayInfo.authCode,
        originalAmount: updatedPayInfo.originalAmount,
        tradeAmount: updatedPayInfo.tradeAmount,
        ignoreAmount: updatedPayInfo.ignoreAmount,
        vchcode: payResult.resultDTO?.vchcode ?? updatedPayInfo.vchcode,
        paywayId: updatedPayInfo.paywayId,
        outNo: payResult.outNo ?? updatedPayInfo.outNo,
        billTotal: updatedPayInfo.billTotal,
      );
    }

    return VipCreatePaymentConfirmRequest(
      vipInfo: updatedVipInfo,
      payInfo: updatedPayInfo,
      confirmType: confirmType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'vipInfo': vipInfo?.toJson(),
      'payInfo': payInfo?.toJson(),
      'confirmType': confirmType,
    };
  }

  factory VipCreatePaymentConfirmRequest.fromJson(Map<String, dynamic> json) {
    return VipCreatePaymentConfirmRequest(
      vipInfo:
          json['vipInfo'] != null
              ? AddOrEditVipDTO.fromJson(json['vipInfo'])
              : null,
      payInfo:
          json['payInfo'] != null
              ? AggregatePaymentRequest.fromJson(json['payInfo'])
              : null,
      confirmType: json['confirmType'],
    );
  }

  @override
  String toString() {
    return 'VipCreatePaymentConfirmRequest{vipInfo: $vipInfo, payInfo: $payInfo, confirmType: $confirmType}';
  }
}
