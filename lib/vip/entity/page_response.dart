// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

class PageResponse<T> {
  int pageIndex;
  int pageSize;
  int total;
  List<T> list;

  PageResponse({
    this.pageIndex = 0,
    this.pageSize = 0,
    this.total = 0,
    this.list = const [],
  });

  factory PageResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return PageResponse<T>(
      pageIndex: json['pageIndex'] ?? 0,
      pageSize: json['pageSize'] ?? 0,
      total: int.tryParse(json['total']?.toString() ?? '0') ?? 0,
      list:
          (json['list'] as List<dynamic>?)
              ?.map((item) => fromJsonT(item as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'pageIndex': pageIndex,
      'pageSize': pageSize,
      'total': total,
      'list': list.map((item) => toJsonT(item)).toList(),
    };
  }

  // 计算是否有下一页
  bool get hasNextPage {
    int totalPages = (total / pageSize).ceil();
    return pageIndex <= totalPages - 1;
  }

  @override
  String toString() {
    return 'PageResponse{pageIndex: $pageIndex, pageSize: $pageSize, total: $total, list: $list}';
  }
}
