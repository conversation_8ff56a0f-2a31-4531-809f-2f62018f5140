///卡卷模板下的商品
class CardTemplatePtype {
  CardTemplatePtype({
    this.id,
    this.profileId,
    this.createTime,
    this.updateTime,
    this.ptypeId,
    this.skuId,
    this.unitId,
    this.deleted,
    this.pid,
    this.rowindex,
    this.cardTemplateId,
    this.ptypeRang,
    this.ptypeName,
    this.usercode,
    this.propertyName,
    this.retailPrice,
    this.unitName,
    this.pcategory,
    this.batchEnabled,
    this.snEnabled,
  });

  CardTemplatePtype.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    ptypeId = json['ptypeId'];
    skuId = json['skuId'];
    unitId = json['unitId'];
    deleted = json['deleted'];
    pid = json['pid'];
    rowindex = json['rowindex'];
    cardTemplateId = json['cardTemplateId'];
    ptypeRang = json['ptypeRang'];
    ptypeName = json['ptypeName'];
    usercode = json['usercode'];
    propertyName = json['propertyName'];
    retailPrice = json['retailPrice'];
    unitName = json['unitName'];
    pcategory = json['pcategory'];
    batchEnabled = json['batchEnabled'];
    snEnabled = json['snEnabled'];
  }

  String? id;

  ///账套ID
  String? profileId;

  ///创建时间
  String? createTime;

  ///修改时间
  String? updateTime;

  ///商品 typeId
  String? ptypeId;

  ///skuId
  String? skuId;

  ///单位id
  String? unitId;

  ///是否删除
  bool? deleted;

  ///商品id
  String? pid;

  ///排序
  int? rowindex;

  ///卡券模板id
  String? cardTemplateId;

  ///商品范围
  int? ptypeRang;

  ///商品名称
  String? ptypeName;

  ///商品编号
  String? usercode;

  ///属性名称
  String? propertyName;

  ///零售价
  double? retailPrice;

  ///单位名称
  String? unitName;

  ///商品类型
  String? pcategory;

  ///是否是批次商品
  bool? batchEnabled;

  ///是否是序列号商品
  int? snEnabled;

  CardTemplatePtype copyWith({
    String? id,
    String? profileId,
    String? createTime,
    String? updateTime,
    String? ptypeId,
    String? skuId,
    String? unitId,
    bool? deleted,
    String? pid,
    int? rowindex,
    String? cardTemplateId,
    int? ptypeRang,
    String? ptypeName,
    String? usercode,
    String? propertyName,
    double? retailPrice,
    String? unitName,
    String? pcategory,
    bool? batchEnabled,
    int? snEnabled,
  }) =>
      CardTemplatePtype(
        id: id ?? this.id,
        profileId: profileId ?? this.profileId,
        createTime: createTime ?? this.createTime,
        updateTime: updateTime ?? this.updateTime,
        ptypeId: ptypeId ?? this.ptypeId,
        skuId: skuId ?? this.skuId,
        unitId: unitId ?? this.unitId,
        deleted: deleted ?? this.deleted,
        pid: pid ?? this.pid,
        rowindex: rowindex ?? this.rowindex,
        cardTemplateId: cardTemplateId ?? this.cardTemplateId,
        ptypeRang: ptypeRang ?? this.ptypeRang,
        ptypeName: ptypeName ?? this.ptypeName,
        usercode: usercode ?? this.usercode,
        propertyName: propertyName ?? this.propertyName,
        retailPrice: retailPrice ?? this.retailPrice,
        unitName: unitName ?? this.unitName,
        pcategory: pcategory ?? this.pcategory,
        batchEnabled: batchEnabled ?? this.batchEnabled,
        snEnabled: snEnabled ?? this.snEnabled,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['ptypeId'] = ptypeId;
    map['skuId'] = skuId;
    map['unitId'] = unitId;
    map['deleted'] = deleted;
    map['pid'] = pid;
    map['rowindex'] = rowindex;
    map['cardTemplateId'] = cardTemplateId;
    map['ptypeRang'] = ptypeRang;
    map['ptypeName'] = ptypeName;
    map['usercode'] = usercode;
    map['propertyName'] = propertyName;
    map['retailPrice'] = retailPrice;
    map['unitName'] = unitName;
    map['pcategory'] = pcategory;
    map['batchEnabled'] = batchEnabled;
    map['snEnabled'] = snEnabled;
    return map;
  }
}
