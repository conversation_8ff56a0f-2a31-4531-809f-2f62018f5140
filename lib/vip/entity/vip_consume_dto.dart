class VipConsumeDTO {
  ///id
  String? id;

  ///会员id
  String? vipId;

  ///累积消费金额
  num? payMoneyCount;

  ///累积消费次数
  int? payTimeCount;

  ///累积积分使用
  int? payScoreCount;

  ///累积充值金额
  num? rechargeNum;

  ///累积积累积分
  int? accumulateScoreNum;

  ///累积储值消费
  num? storedPayCount;

  /// 上次消费时间
  String? lastPayTime;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'vipId': vipId,
      'payMoneyCount': payMoneyCount,
      'payTimeCount': payTimeCount,
      'payScoreCount': payScoreCount,
      'rechargeNum': rechargeNum,
      'accumulateScoreNum': accumulateScoreNum,
      'storedPayCount': storedPayCount,
      'lastPayTime': lastPayTime,
    };
  }

  VipConsumeDTO.fromMap(Map<String, dynamic> map)
      : id = map['id'],
        vipId = map['vipId'],
        payMoneyCount = map['payMoneyCount'],
        payTimeCount = map['payTimeCount'],
        payScoreCount = map['payScoreCount'],
        rechargeNum = map['rechargeNum'],
        accumulateScoreNum = map['accumulateScoreNum'],
        storedPayCount = map['storedPayCount'],
        lastPayTime = map['lastPayTime'];
}
