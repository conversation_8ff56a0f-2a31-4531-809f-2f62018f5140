/// 会员充值支付确认请求模型

import '../../bill/entity/pay_result_dto.dart';
import 'vip_recharge_payment_request.dart';
import 'vip_level_payment_request.dart' as level_payment;

/// 会员充值支付确认请求
/// 参数与聚合支付接口保持一致
class VipRechargePaymentConfirmRequest {
  /// 会员充值信息（与聚合支付请求一致）
  level_payment.VipRechargeDTO? rechargeInfo;

  /// 聚合支付信息（与聚合支付请求一致）
  level_payment.AggregatePaymentRequest? payInfo;

  /// 确认类型：1-轮询确认，2-手动确认
  int? confirmType;

  VipRechargePaymentConfirmRequest({
    this.rechargeInfo,
    this.payInfo,
    this.confirmType,
  });

  /// 从VipRechargePaymentRequest创建确认请求（用于轮询确认）
  factory VipRechargePaymentConfirmRequest.fromPaymentRequest(
    VipRechargePaymentRequest paymentRequest, {
    int confirmType = 1,
  }) {
    return VipRechargePaymentConfirmRequest(
      rechargeInfo: paymentRequest.rechargeInfo,
      payInfo: paymentRequest.payInfo,
      confirmType: confirmType,
    );
  }

  /// 从PayResult创建确认请求（用于手动确认）
  factory VipRechargePaymentConfirmRequest.fromPayResult(
    VipRechargePaymentRequest originalRequest,
    PayResult payResult, {
    int confirmType = 1,
  }) {
    // 从PayResult中提取vchcode等信息，更新到rechargeInfo中
    level_payment.VipRechargeDTO? updatedRechargeInfo =
        originalRequest.rechargeInfo;
    if (updatedRechargeInfo != null && payResult.resultDTO?.vchcode != null) {
      updatedRechargeInfo = level_payment.VipRechargeDTO(
        vchcode: payResult.resultDTO!.vchcode,
        vipId: updatedRechargeInfo.vipId,
        vipLevelId: updatedRechargeInfo.vipLevelId,
        rechargeId: updatedRechargeInfo.rechargeId,
        rechargeMoney: updatedRechargeInfo.rechargeMoney,
        otypeId: updatedRechargeInfo.otypeId,
        ktypeId: updatedRechargeInfo.ktypeId,
        btypeId: updatedRechargeInfo.btypeId,
        memo: updatedRechargeInfo.memo,
        summary: updatedRechargeInfo.summary,
        createEtypeId: updatedRechargeInfo.createEtypeId,
        giveMoney: updatedRechargeInfo.giveMoney,
        score: updatedRechargeInfo.score,
        cardId: updatedRechargeInfo.cardId,
        cardQty: updatedRechargeInfo.cardQty,
        goodsBill: updatedRechargeInfo.goodsBill,
        cashierId: updatedRechargeInfo.cashierId,
      );
    }

    // 从PayResult中提取支付信息，更新到payInfo中
    level_payment.AggregatePaymentRequest? updatedPayInfo =
        originalRequest.payInfo;
    if (updatedPayInfo != null) {
      updatedPayInfo = level_payment.AggregatePaymentRequest(
        atypeId: updatedPayInfo.atypeId,
        authCode: updatedPayInfo.authCode,
        originalAmount: updatedPayInfo.originalAmount,
        tradeAmount: updatedPayInfo.tradeAmount,
        ignoreAmount: updatedPayInfo.ignoreAmount,
        vchcode: payResult.resultDTO?.vchcode ?? updatedPayInfo.vchcode,
        paywayId: updatedPayInfo.paywayId,
        outNo: payResult.outNo ?? updatedPayInfo.outNo,
        billTotal: updatedPayInfo.billTotal,
      );
    }

    return VipRechargePaymentConfirmRequest(
      rechargeInfo: updatedRechargeInfo,
      payInfo: updatedPayInfo,
      confirmType: confirmType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rechargeInfo': rechargeInfo?.toJson(),
      'payInfo': payInfo?.toJson(),
      'confirmType': confirmType,
    };
  }

  factory VipRechargePaymentConfirmRequest.fromJson(Map<String, dynamic> json) {
    return VipRechargePaymentConfirmRequest(
      rechargeInfo:
          json['rechargeInfo'] != null
              ? level_payment.VipRechargeDTO.fromJson(json['rechargeInfo'])
              : null,
      payInfo:
          json['payInfo'] != null
              ? level_payment.AggregatePaymentRequest.fromJson(json['payInfo'])
              : null,
      confirmType: json['confirmType'],
    );
  }

  @override
  String toString() {
    return 'VipRechargePaymentConfirmRequest{rechargeInfo: $rechargeInfo, payInfo: $payInfo, confirmType: $confirmType}';
  }
}
