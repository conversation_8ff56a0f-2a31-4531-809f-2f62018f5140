///权益类
class EquityValue {
  EquityValue({
    this.valueType,
    this.qty,
    this.valueCondition,
    this.valueDetail,
    this.cardType,
    this.vType,
    this.miniCostType,
    this.ptypeRang,
  });

  EquityValue.fromJson(dynamic json) {
    valueType = json['valueType'];
    qty = json['qty'];
    valueCondition = json['valueCondition'];
    valueDetail = json['valueDetail'];
    cardType = json['cardType'];
    vType = json['vType'];
    miniCostType = json['miniCostType'];
    ptypeRang = json['ptypeRang'];
  }

  ///权益价值类型
  String? valueType;

  ///离谱兑换可兑换组数量-用
  int? qty;

  ///价值条件
  double? valueCondition;

  ///价值的值
  double? valueDetail;
  int? cardType;

  ///优惠券类型 0：代金券 1：折扣券 2：礼品券
  int? vType;

  ///最低消费限制
  int? miniCostType;

  ///商品范围
  int? ptypeRang;

  EquityValue copyWith({
    String? valueType,
    int? qty,
    double? valueCondition,
    double? valueDetail,
    int? cardType,
    int? vType,
    int? miniCostType,
    int? ptypeRang,
  }) =>
      EquityValue(
        valueType: valueType ?? this.valueType,
        qty: qty ?? this.qty,
        valueCondition: valueCondition ?? this.valueCondition,
        valueDetail: valueDetail ?? this.valueDetail,
        cardType: cardType ?? this.cardType,
        vType: vType ?? this.vType,
        miniCostType: miniCostType ?? this.miniCostType,
        ptypeRang: ptypeRang ?? this.ptypeRang,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['valueType'] = valueType;
    map['qty'] = qty;
    map['valueCondition'] = valueCondition;
    map['valueDetail'] = valueDetail;
    map['cardType'] = cardType;
    map['vType'] = vType;
    map['miniCostType'] = miniCostType;
    map['ptypeRang'] = ptypeRang;
    return map;
  }
}

///查询某个权益卡/优惠券下的权益列表时返回的实体类
class EquityValueResponse {
  EquityValueResponse({
    this.modelId,
    this.eqList,
  });

  EquityValueResponse.fromJson(dynamic json) {
    modelId = json['modelId'];
    eqList =
        (json['eqList'] as List?)?.map((e) => EquityValue.fromJson(e)).toList();
  }

  ///权益卡/优惠券的id
  String? modelId;

  ///查询出来的权益列表
  List<EquityValue>? eqList;

  EquityValueResponse copyWith({
    String? modelId,
    List<EquityValue>? eqList,
  }) =>
      EquityValueResponse(
        modelId: modelId ?? this.modelId,
        eqList: eqList ?? this.eqList,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['modelId'] = modelId;
    map['eqList'] = eqList?.map((v) => v.toJson()).toList();
    return map;
  }
}
