/// 会员储值聚合支付请求模型

import 'vip_level_payment_request.dart' as level_payment;

/// 会员储值聚合支付请求
class VipRechargePaymentRequest {
  /// 会员充值信息
  level_payment.VipRechargeDTO? rechargeInfo;

  /// 聚合支付信息
  level_payment.AggregatePaymentRequest? payInfo;

  /// 包含 payment 字段的充值信息（用于序列化）
  Map<String, dynamic>? rechargeInfoWithPayment;

  VipRechargePaymentRequest({this.rechargeInfo, this.payInfo});

  Map<String, dynamic> toJson() {
    return {
      'rechargeInfo': rechargeInfoWithPayment ?? rechargeInfo?.toJson(),
      'payInfo': payInfo?.toJson(),
    };
  }

  factory VipRechargePaymentRequest.fromJson(Map<String, dynamic> json) {
    return VipRechargePaymentRequest(
      rechargeInfo:
          json['rechargeInfo'] != null
              ? level_payment.VipRechargeDTO.fromJson(json['rechargeInfo'])
              : null,
      payInfo:
          json['payInfo'] != null
              ? level_payment.AggregatePaymentRequest.fromJson(json['payInfo'])
              : null,
    );
  }

  @override
  String toString() {
    return 'VipRechargePaymentRequest{rechargeInfo: $rechargeInfo, payInfo: $payInfo}';
  }
}

// VipRechargeDTO 已删除，统一使用 VipRechargeInfo（通过 typedef 别名）

/// 商品单据信息
class GoodsBillInfo {
  String? billId;
  String? billType;
  String? etypeId;
  String? efullname;
  List<dynamic>? outDetail;
  List<dynamic>? inDetail;

  GoodsBillInfo({
    this.billId,
    this.billType,
    this.etypeId,
    this.efullname,
    this.outDetail,
    this.inDetail,
  });

  Map<String, dynamic> toJson() {
    return {
      'billId': billId,
      'billType': billType,
      'etypeId': etypeId,
      'efullname': efullname,
      'outDetail': outDetail,
      'inDetail': inDetail,
    };
  }

  factory GoodsBillInfo.fromJson(Map<String, dynamic> json) {
    return GoodsBillInfo(
      billId: json['billId'],
      billType: json['billType'],
      etypeId: json['etypeId'],
      efullname: json['efullname'],
      outDetail: json['outDetail'],
      inDetail: json['inDetail'],
    );
  }
}
