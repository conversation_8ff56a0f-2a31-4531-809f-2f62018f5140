/// 付费会员升级支付确认请求模型

import 'vip_level_payment_request.dart';
import '../../bill/entity/pay_result_dto.dart';

/// 付费会员升级支付确认请求
/// 用于POS端轮询或手动确认收款时调用
/// 参数与聚合支付接口保持一致
class VipLevelPaymentConfirmRequest {
  /// 会员升级信息（与聚合支付请求一致）
  RenewOrUpgradeSVIPDTO? upgradeInfo;

  /// 聚合支付信息（与聚合支付请求一致）
  AggregatePaymentRequest? payInfo;

  /// 确认类型：1-轮询确认，2-手动确认
  int? confirmType;

  VipLevelPaymentConfirmRequest({
    this.upgradeInfo,
    this.payInfo,
    this.confirmType,
  });

  /// 从聚合支付请求创建确认请求
  factory VipLevelPaymentConfirmRequest.fromPaymentRequest(
    VipLevelPaymentRequest paymentRequest, {
    int confirmType = 1,
  }) {
    return VipLevelPaymentConfirmRequest(
      upgradeInfo: paymentRequest.upgradeInfo,
      payInfo: paymentRequest.payInfo,
      confirmType: confirmType,
    );
  }

  /// 从PayResult创建确认请求
  factory VipLevelPaymentConfirmRequest.fromPayResult(
    VipLevelPaymentRequest originalRequest,
    PayResult payResult, {
    int confirmType = 1,
  }) {
    // 从PayResult中提取vchcode等信息，更新到upgradeInfo中
    RenewOrUpgradeSVIPDTO? updatedUpgradeInfo = originalRequest.upgradeInfo;
    if (updatedUpgradeInfo != null && payResult.resultDTO?.vchcode != null) {
      updatedUpgradeInfo = RenewOrUpgradeSVIPDTO(
        vchcode: payResult.resultDTO!.vchcode,
        vipId: updatedUpgradeInfo.vipId,
        levelId: updatedUpgradeInfo.levelId,
        levelRuleId: updatedUpgradeInfo.levelRuleId,
        payment: updatedUpgradeInfo.payment,
        btypeId: updatedUpgradeInfo.btypeId,
        etypeId: updatedUpgradeInfo.etypeId,
        otypeId: updatedUpgradeInfo.otypeId,
        memo: updatedUpgradeInfo.memo,
      );
    }

    // 从PayResult中提取支付信息，更新到payInfo中
    AggregatePaymentRequest? updatedPayInfo = originalRequest.payInfo;
    if (updatedPayInfo != null) {
      updatedPayInfo = AggregatePaymentRequest(
        atypeId: updatedPayInfo.atypeId,
        authCode: updatedPayInfo.authCode,
        originalAmount: updatedPayInfo.originalAmount,
        tradeAmount: updatedPayInfo.tradeAmount,
        ignoreAmount: updatedPayInfo.ignoreAmount,
        vchcode: payResult.resultDTO?.vchcode ?? updatedPayInfo.vchcode,
        paywayId: updatedPayInfo.paywayId,
        outNo: payResult.outNo ?? updatedPayInfo.outNo,
      );
    }

    return VipLevelPaymentConfirmRequest(
      upgradeInfo: updatedUpgradeInfo,
      payInfo: updatedPayInfo,
      confirmType: confirmType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'upgradeInfo': upgradeInfo?.toJson(),
      'payInfo': payInfo?.toJson(),
      'confirmType': confirmType,
    };
  }

  factory VipLevelPaymentConfirmRequest.fromJson(Map<String, dynamic> json) {
    return VipLevelPaymentConfirmRequest(
      upgradeInfo:
          json['upgradeInfo'] != null
              ? RenewOrUpgradeSVIPDTO.fromJson(json['upgradeInfo'])
              : null,
      payInfo:
          json['payInfo'] != null
              ? AggregatePaymentRequest.fromJson(json['payInfo'])
              : null,
      confirmType: json['confirmType'],
    );
  }

  @override
  String toString() {
    return 'VipLevelPaymentConfirmRequest{'
        'upgradeInfo: $upgradeInfo, '
        'payInfo: $payInfo, '
        'confirmType: $confirmType'
        '}';
  }
}
