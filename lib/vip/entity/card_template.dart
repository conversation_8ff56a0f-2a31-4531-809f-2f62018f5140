import '../../../common/standard.dart';

import 'card_template_ptype.dart';

class CardTemplate {
  CardTemplate({
    this.id,
    this.profileId,
    this.createTime,
    this.updateTime,
    this.cardType,
    this.fullname,
    this.validType,
    this.validValue,
    this.comment,
    this.superposition,
    this.rangeType,
    this.count,
    this.miniCostType,
    this.minConstValue,
    this.deductionTotal,
    this.deleted,
    this.stoped,
    this.ptypeRang,
    this.takeGoodQty,
    this.ptypeList,
    this.otypeIds,
    this.ofullNames,
    this.orderField,
    this.createdCount,
    this.activeCount,
    this.validFullName,
  });

  CardTemplate.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    cardType = json['cardType'];
    fullname = json['fullname'];
    validType = json['validType'];
    validValue = json['validValue'];
    comment = json['comment'];
    superposition = json['superposition'];
    rangeType = json['rangeType'];
    count = json['count'];
    miniCostType = json['miniCostType'];
    minConstValue = json['minConstValue'];
    deductionTotal = json['deductionTotal'];
    deleted = json['deleted'];
    stoped = json['stoped'];
    ptypeRang = json['ptypeRang'];
    takeGoodQty = json['takeGoodQty'];
    ptypeList = (json['ptypeList'] as List?)?.let((list) =>
            list.map((e) => CardTemplatePtype.fromJson(e)).toList()) ??
        [];
    otypeIds = json['otypeIds'] != null ? json['otypeIds'].cast<String>() : [];
    ofullNames = json['ofullNames'];
    orderField = json['orderField'];
    createdCount = json['createdCount'];
    activeCount = json['activeCount'];
    validFullName = json['validFullName'];
    isSelect = false;
  }

  ///卡卷模板id
  String? id;

  ///账套id
  String? profileId;

  ///创建时间
  String? createTime;

  ///修改时间
  String? updateTime;

  ///卡券类型 0：等级权益卡 1：普通权益卡 2：优惠券
  int? cardType;

  String? cardTypeStr;

  ///卡券名称
  String? fullname;

  ///有效期类型：0：固定日期 1：领券时,2永久
  int? validType;

  ///有效类型值
  String? validValue;

  ///有效类型值
  String? validFullName;

  ///备注
  String? comment;

  ///是否叠加
  int? superposition;

  ///使用范围 0:所有人 1：会员
  int? rangeType;

  ///预计生成张数
  int? count;

  ///最低消费：0 限制 1：不限制
  int? miniCostType;

  ///最低消费值
  double? minConstValue;

  ///抵扣金额
  double? deductionTotal;

  ///是否删除
  bool? deleted;

  ///是否停用
  int? stoped;

  ///商品范围 0:全部 1：部分
  int? ptypeRang;

  ///提货数量
  int? takeGoodQty;

  ///商品列表
  List<CardTemplatePtype>? ptypeList;

  ///门店id组合
  List<String>? otypeIds;

  ///门店名称组合
  String? ofullNames;

  ///
  String? orderField;

  ///已生卡数
  int? createdCount;

  ///已激活数
  int? activeCount;

  // 是否选中
  bool isSelect = false;

  ///卡券类型 0：代金券 1：折扣券 2：礼品券
  String getCardString() {
    switch (this.cardType) {
      case 0:
        return "代金券";
      case 1:
        return "折扣券";
      case 2:
        return "礼品券";
      default:
        return "";
    }
  }

  CardTemplate copyWith({
    String? id,
    String? profileId,
    String? createTime,
    String? updateTime,
    int? cardType,
    String? fullname,
    int? validType,
    String? validValue,
    String? comment,
    int? superposition,
    int? rangeType,
    int? count,
    int? miniCostType,
    double? minConstValue,
    double? deductionTotal,
    bool? deleted,
    int? stoped,
    int? ptypeRang,
    int? takeGoodQty,
    List<CardTemplatePtype>? ptypeList,
    List<String>? otypeIds,
    String? ofullNames,
    String? orderField,
    int? createdCount,
    int? activeCount,
    String? validFullName,
  }) =>
      CardTemplate(
        id: id ?? this.id,
        profileId: profileId ?? this.profileId,
        createTime: createTime ?? this.createTime,
        updateTime: updateTime ?? this.updateTime,
        cardType: cardType ?? this.cardType,
        fullname: fullname ?? this.fullname,
        validType: validType ?? this.validType,
        validValue: validValue ?? this.validValue,
        comment: comment ?? this.comment,
        superposition: superposition ?? this.superposition,
        rangeType: rangeType ?? this.rangeType,
        count: count ?? this.count,
        miniCostType: miniCostType ?? this.miniCostType,
        minConstValue: minConstValue ?? this.minConstValue,
        deductionTotal: deductionTotal ?? this.deductionTotal,
        deleted: deleted ?? this.deleted,
        stoped: stoped ?? this.stoped,
        ptypeRang: ptypeRang ?? this.ptypeRang,
        takeGoodQty: takeGoodQty ?? this.takeGoodQty,
        ptypeList: ptypeList ?? this.ptypeList,
        otypeIds: otypeIds ?? this.otypeIds,
        ofullNames: ofullNames ?? this.ofullNames,
        orderField: orderField ?? this.orderField,
        createdCount: createdCount ?? this.createdCount,
        activeCount: activeCount ?? this.activeCount,
        validFullName: validFullName ?? this.validFullName,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['cardType'] = cardType;
    map['fullname'] = fullname;
    map['validType'] = validType;
    map['validValue'] = validValue;
    map['comment'] = comment;
    map['superposition'] = superposition;
    map['rangeType'] = rangeType;
    map['count'] = count;
    map['miniCostType'] = miniCostType;
    map['minConstValue'] = minConstValue;
    map['deductionTotal'] = deductionTotal;
    map['deleted'] = deleted;
    map['stoped'] = stoped;
    map['ptypeRang'] = ptypeRang;
    map['takeGoodQty'] = takeGoodQty;
    map['ptypeList'] = ptypeList;
    map['otypeIds'] = otypeIds;
    map['ofullNames'] = ofullNames;
    map['orderField'] = orderField;
    map['createdCount'] = createdCount;
    map['activeCount'] = activeCount;
    map['validFullName'] = validFullName;
    return map;
  }
}
