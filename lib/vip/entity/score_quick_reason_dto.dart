class ScoreQuickReasonDto {
  String? id;
  String? content;
  String? profileId;
  String? createTime;
  String? updateTime;

  ScoreQuickReasonDto({
    this.id,
    this.content,
    this.profileId,
    this.createTime,
    this.updateTime,
  });

  // 可选：添加一个工厂构造函数，用于从Map中创建对象
  factory ScoreQuickReasonDto.fromJson(Map<String, dynamic> json) {
    return ScoreQuickReasonDto(
      id: json['id'] as String?,
      content: json['content'] as String?,
      profileId: json['profileId'] as String?,
      createTime: json['createTime'] as String?,
      updateTime: json['updateTime'] as String?,
    );
  }

  // 可选：添加一个方法，用于将对象转换为Map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'profileId': profileId,
      'createTime': createTime,
      'updateTime': updateTime,
    };
  }
}
