import '../../bill/entity/payment_dto.dart';

/// 用于添加会员接口的实体类
class AddOrEditVipDTO {
  /// 会员id
  String? id;

  /// 初始会员等级id
  String vipLevelId = "";

  ///付费会员规则id
  String levelRuleId = "";

  ///成长值
  int growthValue = 0;

  /// 会员名称v
  String name = "";

  /// 电话号码
  String phone = "";

  /// 性别 2为女，1为男
  int sex = 1;

  /// 生日
  String birthday = "";

  /// 身份证号码
  String idCode = "";

  /// 邮箱
  String email = "";

  /// 初始积分
  int score = 0;

  /// 初始储值
  double store = 0;

  /// 权益卡模板id列表
  List<String> rightsCardModelIds = [];

  ///会员门店类型: 0 全部门店 1 部分门店
  int applyStoreType = 0;

  ///[门店id列表] 当门店类型[applyStoreType] 为部分门店时有效
  List<String> otypeIds = [];

  /// 会员标签id列表
  List<String> vipTag = [];

  ///业务员
  String? etypeId;

  ///业务员
  String? efullname;

  ///支付方式
  List<PaymentDto>? payment;

  ///收款单据用，结算单位
  String? btypeId;

  ///收款单门店
  String? otypeId;

  ///会员等级名称，本地用
  String levelName = "";

  ///会员类型，本地用
  int vipType = 0;

  ///付费会员价格说明，本地用
  String priceStr = "";

  ///付费会员价格，本地用
  num levelPrice = 0;

  ///付费会员有效期月份数量，用于计算到期日期，本地用
  int validity = 0;

  ///详细地址
  String address = "";

  ///会员地址-省
  String province = "";

  ///会员地址-市
  String city = "";

  ///会员地址-区
  String district = "";

  ///会员地址-街道
  String street = "";

  ///备注附加说明
  String? memo;

  ///密码(仅新增会员有效)
  String? password;

  ///单据编号(用于聚合支付)
  String? vchcode;

  AddOrEditVipDTO();

  AddOrEditVipDTO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    vipLevelId = json['vipLevelId'] ?? "";
    levelRuleId = json['levelRuleId'] ?? "";
    growthValue = json['growthValue'] ?? 0;
    name = json['name'] ?? "";
    phone = json['phone'] ?? "";
    sex = json['sex'] ?? 1;
    birthday = json['birthday'] ?? "";
    idCode = json['idCode'] ?? "";
    email = json['email'] ?? "";
    score = json['score'] ?? 0;
    store = json['store']?.toDouble() ?? 0;
    rightsCardModelIds = List<String>.from(json['rightsCardModelIds'] ?? []);
    applyStoreType = json['applyStoreType'] ?? 0;
    otypeIds = List<String>.from(json['otypeIds'] ?? []);
    vipTag = List<String>.from(json['vipTag'] ?? []);
    etypeId = json['etypeId'];
    efullname = json['efullname'];
    btypeId = json['btypeId'];
    otypeId = json['otypeId'];
    if (json['payment'] != null) {
      payment =
          (json['payment'] as List).map((e) => PaymentDto.fromMap(e)).toList();
    }
    levelName = json['levelName'] ?? "";
    vipType = json['vipType'] ?? 0;
    priceStr = json['priceStr'] ?? "";
    levelPrice = json['levelPrice'] ?? 0;
    validity = json['validity'] ?? 0;
    address = json['address'] ?? "";
    province = json['province'] ?? "";
    city = json['city'] ?? "";
    district = json['district'] ?? "";
    street = json['street'] ?? "";
    memo = json['memo'];
    password = json['password'];
    vchcode = json['vchcode'];
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "vipLevelId": vipLevelId,
    "levelRuleId": levelRuleId,
    "growthValue": growthValue,
    "name": name,
    "phone": phone,
    "sex": sex,
    "birthday": birthday,
    "idCode": idCode,
    "email": email,
    "score": score,
    "store": store,
    "rightsCardModelIds": rightsCardModelIds,
    "applyStoreType": applyStoreType,
    "otypeIds": otypeIds,
    "vipTag": vipTag,
    "etypeId": etypeId,
    "efullname": efullname,
    "btypeId": btypeId,
    "otypeId": otypeId,
    "payment": payment?.map((e) => e.toJson()).toList(),
    "address": address,
    "province": province,
    "city": city,
    "district": district,
    "street": street,
    "memo": memo,
    "password": password,
    "vchcode": vchcode,
  };
}
