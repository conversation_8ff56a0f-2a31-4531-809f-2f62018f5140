class VipAssertChange {
  VipAssertChange({
    this.assertId,
    this.typed,
    this.cardType,
    this.sourceOperation,
    this.cardName,
    this.qty,
    this.createTime,
  });

  VipAssertChange.fromMap(dynamic json) {
    assertId = json['assertId'];
    typed = json['typed'];
    cardType = json['cardType'];
    sourceOperation = json['sourceOperation'];
    cardName = json['cardName'];
    qty = json['qty'];
    createTime = json['createTime'];
  }

  ///资产id
  String? assertId;

  ///变动种类 0:积分 1：充值金额 2:赠送金额 3:成长值 4:卡券 5：服务商品
  int? typed;

  ///0等级权益卡、1普通权益卡、2代金券、3折扣券、4礼品券
  int? cardType;

  ///操作来源：0=新增会员；1=会员升级；2=普通发放；3=门店零售；4=积分兑换；5=赠送积分；6=会员充值：7=积分过期；8=卡券到期；9=解绑权益卡；10=门店退货；11=解绑优惠券；12=储值退款；13=充值作废
  int? sourceOperation;

  ///卡券名称
  String? cardName;

  ///数量
  num? qty;

  ///创建时间
  String? createTime;

  VipAssertChange copyWith({
    String? assertId,
    int? typed,
    int? cardType,
    int? sourceOperation,
    String? cardName,
    num? qty,
    String? createTime,
  }) =>
      VipAssertChange(
        assertId: assertId ?? this.assertId,
        typed: typed ?? this.typed,
        cardType: cardType ?? this.cardType,
        sourceOperation: sourceOperation ?? this.sourceOperation,
        cardName: cardName ?? this.cardName,
        qty: qty ?? this.qty,
        createTime: createTime ?? this.createTime,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['assertId'] = assertId;
    map['typed'] = typed;
    map['cardType'] = cardType;
    map['sourceOperation'] = sourceOperation;
    map['cardName'] = cardName;
    map['qty'] = qty;
    map['createTime'] = createTime;
    return map;
  }
}
