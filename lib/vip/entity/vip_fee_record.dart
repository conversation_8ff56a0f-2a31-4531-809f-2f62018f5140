///会员等级费用记录
class VipFeeRecord {
  /// id
  String? id;

  /// 会员id
  String? vipId;

  /// 充值金额
  num? total;

  /// 付费会员开始时间
  String? startDate;

  /// 付费会员结束时间
  String? endDate;

  VipFeeRecord.fromMap(Map<String, dynamic> map)
      : id = map['id'],
        vipId = map['vipId'],
        total = map['total'],
        startDate = map['startDate'],
        endDate = map['endDate'];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'vipId': vipId,
      'total': total,
      'startDate': startDate,
      'endDate': endDate,
    };
  }
}
