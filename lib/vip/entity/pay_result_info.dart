/// 会员开通支付结果信息模型

import '../../bill/entity/pay_result_dto.dart';
import '../../bill/entity/bill_save_reslut_dto.dart';

/// 会员开通支付结果信息
/// 继承自 PayResult，并添加会员ID字段
class PayResultInfo extends PayResult {
  /// 支付结果信息
  String? message;

  /// 会员ID（用于会员开通等场景）
  String? vipId;

  PayResultInfo();

  PayResultInfo.fromJson(Map map) : super.fromJson(map) {
    message = map["message"];
    vipId = map["vipId"]?.toString();
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {
      'successed': successed,
      'message': message,
      'errcode': errcode,
      'data': data,
      'payInfo': payInfo,
      'queryType': queryType,
      'status': status,
      'outNo': outNo,
      'orderNo': orderNo,
      'vipId': vipId,
    };
    
    if (tradeResult != null) {
      json['tradeResult'] = tradeResult!.toJson();
    }
    
    if (resultDTO != null) {
      json['resultDTO'] = resultDTO!.toJson();
    }
    
    return json;
  }

  @override
  String toString() {
    return 'PayResultInfo{successed: $successed, message: $message, errcode: $errcode, data: $data, payInfo: $payInfo, queryType: $queryType, status: $status, outNo: $outNo, orderNo: $orderNo, vipId: $vipId, tradeResult: $tradeResult, resultDTO: $resultDTO}';
  }
}
