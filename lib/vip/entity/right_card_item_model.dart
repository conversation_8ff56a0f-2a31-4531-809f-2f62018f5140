class RightCartItemModel {
  /// 主键ID
  String? id;

  /// 卡名称
  String? fullname;

  /// 来源前端显示
  String? cardSourceString;

  /// 过期时间前端显示
  String? expiryString;

  /// 包含权益
  String? rights;

  ///优惠券使用-优惠券类
  int? valueType;

  ///优惠券使用-优惠券类型名称
  String? valueTypeName;

  ///有效期
  String? validFullName;

  /// 是否选中
  bool? isSelect;

  RightCartItemModel.fromMap(Map<String, dynamic> map) {
    id = map['id'];
    fullname = map['fullname'];
    cardSourceString = map['cardSourceString'];
    expiryString = map['expiryString'];
    valueType = map['valueType'];
    valueTypeName = map['valueTypeName'];
    validFullName = map['validFullName'];
    rights = map['rights'] == null ? "" : map['rights'];
    isSelect = false;
  }
}
