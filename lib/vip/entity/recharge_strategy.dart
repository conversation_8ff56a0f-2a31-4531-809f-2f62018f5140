///充值活动
class RechargeStrategy {
  RechargeStrategy({
    this.id,
    this.profileId,
    this.startDate,
    this.startTime,
    this.endDate,
    this.endTime,
    this.createTime,
    this.updateTime,
    this.deleted,
    this.stoped,
    this.firstCharge,
    this.cycleType,
    this.cycleSpace,
    this.spaceType,
    this.frequency,
    this.vipLevelIds,
    this.otypeIds,
    this.activityState,
    this.activityTime,
    this.canStop,
    this.gears,
    this.rechargeType,
  });

  RechargeStrategy.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    startDate = json['startDate'];
    startTime = json['startTime'];
    endDate = json['endDate'];
    endTime = json['endTime'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleted = json['deleted'];
    stoped = json['stoped'];
    firstCharge = json['firstCharge'];
    cycleType = json['cycleType'];
    cycleSpace = json['cycleSpace'];
    spaceType = json['spaceType'];
    frequency = json['frequency'];
    vipLevelIds =
        json['vipLevelIds'] != null ? json['vipLevelIds'].cast<String>() : [];
    otypeIds = json['otypeIds'] != null ? json['otypeIds'].cast<String>() : [];
    activityState = json['activityState'];
    activityTime = json['activityTime'];
    canStop = json['canStop'];
    gears = (json['gears'] as List?)?.map((e) => Gear.fromJson(e)).toList();
    rechargeType = json['rechargeType'];
  }

  ///活动id
  String? id;

  ///账套id
  String? profileId;

  ///活动开始日期
  String? startDate;

  ///活动结束日期
  String? endDate;

  ///活动开始时间
  String? startTime;

  ///活动结束时间
  String? endTime;

  ///创建时间
  String? createTime;

  ///变动时间
  String? updateTime;

  ///是否删除
  int? deleted;

  ///是否开启 0 开启 1停止
  int? stoped;

  ///是否仅限首次充值
  int? firstCharge;

  ///活动周期类型：0=无周期 1=次数 2=固定日期
  int? cycleType;

  ///周期间隔(数量)：例如：每周或者每两周
  int? cycleSpace;

  ///间隔类型：0=无  1=天 2=周 ;3=月;4=年
  int? spaceType;

  ///频次：例如：每周2次(2)或者每周周二和周四重复(2,4)
  String? frequency;

  ///适用会员等级
  List<String>? vipLevelIds;

  ///适用门店
  List<String>? otypeIds;

  ///活动状态 0=未开始,1=进行中,2=已结束
  int? activityState;

  ///活动时间
  String? activityTime;

  ///是否可以停用
  bool? canStop;

  ///充值档位
  List<Gear>? gears;

  ///活动类型 0：充值赠送  1：充值换购
  int? rechargeType;

  RechargeStrategy copyWith({
    String? id,
    String? profileId,
    String? startDate,
    String? startTime,
    String? endDate,
    String? endTime,
    String? createTime,
    String? updateTime,
    int? deleted,
    int? stoped,
    int? firstCharge,
    int? cycleType,
    int? cycleSpace,
    int? spaceType,
    String? frequency,
    List<String>? vipLevelIds,
    List<String>? otypeIds,
    int? activityState,
    String? activityTime,
    bool? canStop,
    List<Gear>? gears,
    int? rechargeType,
  }) =>
      RechargeStrategy(
        id: id ?? this.id,
        profileId: profileId ?? this.profileId,
        startDate: startDate ?? this.startDate,
        startTime: startTime ?? this.startTime,
        endDate: endDate ?? this.endDate,
        endTime: endTime ?? this.endTime,
        createTime: createTime ?? this.createTime,
        updateTime: updateTime ?? this.updateTime,
        deleted: deleted ?? this.deleted,
        stoped: stoped ?? this.stoped,
        firstCharge: firstCharge ?? this.firstCharge,
        cycleType: cycleType ?? this.cycleType,
        cycleSpace: cycleSpace ?? this.cycleSpace,
        spaceType: spaceType ?? this.spaceType,
        frequency: frequency ?? this.frequency,
        vipLevelIds: vipLevelIds ?? this.vipLevelIds,
        otypeIds: otypeIds ?? this.otypeIds,
        activityState: activityState ?? this.activityState,
        activityTime: activityTime ?? this.activityTime,
        canStop: canStop ?? this.canStop,
        gears: gears ?? this.gears,
        rechargeType: rechargeType ?? this.rechargeType,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['startDate'] = startDate;
    map['startTime'] = startTime;
    map['endDate'] = endDate;
    map['endTime'] = endTime;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['deleted'] = deleted;
    map['stoped'] = stoped;
    map['firstCharge'] = firstCharge;
    map['cycleType'] = cycleType;
    map['cycleSpace'] = cycleSpace;
    map['spaceType'] = spaceType;
    map['frequency'] = frequency;
    map['vipLevelIds'] = vipLevelIds;
    map['otypeIds'] = otypeIds;
    map['activityState'] = activityState;
    map['activityTime'] = activityTime;
    map['canStop'] = canStop;
    map['gears'] = gears?.map((v) => v.toJson()).toList();
    map['rechargeType'] = rechargeType;
    return map;
  }
}

///活动内充值档位
class Gear {
  Gear({
    this.id,
    this.profileId,
    this.createTime,
    this.updateTime,
    this.deleted,
    this.rechargeId,
    this.chargeTotal,
    this.rowIndex,
    this.details,
  });

  Gear.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleted = json['deleted'];
    rechargeId = json['rechargeId'];
    chargeTotal = (json['chargeTotal'] as num?)?.toInt();
    rowIndex = json['rowIndex'];
    details = (json['details'] as List?)
        ?.map<Detail>((e) => Detail.fromJson(e))
        .toList();
  }

  ///档位id
  String? id;

  ///账套id
  String? profileId;

  ///创建时间
  String? createTime;

  ///修改时间
  String? updateTime;

  ///是否删除
  int? deleted;

  ///充值活动id
  String? rechargeId;

  ///充值档位金额
  int? chargeTotal;

  ///排序
  int? rowIndex;

  ///充值档位详情
  List<Detail>? details;

  Gear copyWith({
    String? id,
    String? profileId,
    String? createTime,
    String? updateTime,
    int? deleted,
    String? rechargeId,
    int? chargeTotal,
    int? rowIndex,
    List<Detail>? details,
  }) =>
      Gear(
        id: id ?? this.id,
        profileId: profileId ?? this.profileId,
        createTime: createTime ?? this.createTime,
        updateTime: updateTime ?? this.updateTime,
        deleted: deleted ?? this.deleted,
        rechargeId: rechargeId ?? this.rechargeId,
        chargeTotal: chargeTotal ?? this.chargeTotal,
        rowIndex: rowIndex ?? this.rowIndex,
        details: details ?? this.details,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['deleted'] = deleted;
    map['rechargeId'] = rechargeId;
    map['chargeTotal'] = chargeTotal;
    map['rowIndex'] = rowIndex;
    map['details'] = details?.map((v) => v.toJson()).toList();
    return map;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Gear && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

///活动详情
class Detail {
  Detail({
    this.id,
    this.profileId,
    this.createTime,
    this.updateTime,
    this.deleted,
    this.rechargeGearId,
    this.detailType,
    this.giveMode,
    this.rowIndex,
    this.rules,
    this.valueQty,
  });

  Detail.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleted = json['deleted'];
    rechargeGearId = json['rechargeGearId'];
    detailType = json['detailType'];
    rowIndex = json['rowIndex'];
    rules = (json['rules'] as List?)?.map((e) => Rule.fromJson(e)).toList();
    valueQty = json["valueQty"];
    giveMode = json["giveMode"];
  }

  ///详情id
  String? id;

  ///账套id
  String? profileId;

  ///创建时间
  String? createTime;

  ///修改时间
  String? updateTime;

  ///是否删除
  int? deleted;

  ///充值档位id
  String? rechargeGearId;

  ///明细类型:1.赠金2.积分3.权益卡4.优惠券5.商品6.次卡
  int? detailType;

  ///商品范围 赠送类型 0=固定赠送  1=范围自选
  int? giveMode;

  ///排序
  int? rowIndex;

  ///充值档位福利
  List<Rule>? rules;

  ///赠送数量
  num? valueQty;

  Detail copyWith({
    String? id,
    String? profileId,
    String? createTime,
    String? updateTime,
    int? deleted,
    String? rechargeGearId,
    int? detailType,
    int? giveMode,
    int? rowIndex,
    List<Rule>? rules,
    num? valueQty,
  }) =>
      Detail(
        id: id ?? this.id,
        profileId: profileId ?? this.profileId,
        createTime: createTime ?? this.createTime,
        updateTime: updateTime ?? this.updateTime,
        deleted: deleted ?? this.deleted,
        rechargeGearId: rechargeGearId ?? this.rechargeGearId,
        detailType: detailType ?? this.detailType,
        giveMode: giveMode ?? this.giveMode,
        rowIndex: rowIndex ?? this.rowIndex,
        rules: rules ?? this.rules,
        valueQty: valueQty ?? this.valueQty,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['deleted'] = deleted;
    map['rechargeGearId'] = rechargeGearId;
    map['detailType'] = detailType;
    map['giveMode'] = giveMode;
    map['rowIndex'] = rowIndex;
    map['rules'] = rules?.map((e) => e.toJson()).toList();
    map['valueQty'] = valueQty;
    return map;
  }
}

///活动详情赠品
class Rule {
  Rule({
    this.id,
    this.profileId,
    this.createTime,
    this.updateTime,
    this.deleted,
    this.rechargeGearDetailId,
    this.skuId,
    this.unitId,
    this.ptypeId,
    this.cardTemplateId,
    this.valueQty,
    this.cardName,
    this.ptypeName,
    this.propValueId1,
    this.propValueName1,
    this.propValueId2,
    this.propValueName2,
    this.propValueId3,
    this.propValueName3,
    this.propValueId4,
    this.propValueName4,
    this.propValueId5,
    this.propValueName5,
    this.propValueId6,
    this.propValueName6,
    this.unitName,
    this.unitRate,
    this.taxRate,
    this.usercode,
    this.propertyName,
    this.retailPrice,
    this.exchangePrice,
    this.exchangeDiscount,
    this.giveType,
    this.localSelectQty = 0,
  });

  Rule.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleted = json['deleted'];
    rechargeGearDetailId = json['rechargeGearDetailId'];
    skuId = json['skuId'];
    unitId = json['unitId'];
    ptypeId = json['ptypeId'];
    cardTemplateId = json['cardTemplateId'];
    valueQty = (json['valueQty'] as num?)?.toInt();
    cardName = json['cardName'];
    ptypeName = json['ptypeName'];
    propValueId1 = json['propValueId1'];
    propValueName1 = json['propValueName1'];
    propValueId2 = json['propValueId2'];
    propValueName2 = json['propValueName2'];
    propValueId3 = json['propValueId3'];
    propValueName3 = json['propValueName3'];
    propValueId4 = json['propValueId4'];
    propValueName4 = json['propValueName4'];
    propValueId5 = json['propValueId5'];
    propValueName5 = json['propValueName5'];
    propValueId6 = json['propValueId6'];
    propValueName6 = json['propValueName6'];
    unitName = json['unitName'];
    unitRate = json['unitRate'];
    taxRate = json['taxRate'];
    usercode = json['usercode'];
    propertyName = json['propertyName'];
    retailPrice = json['retailPrice'];
    exchangePrice = json['exchangePrice'];
    exchangeDiscount = json['exchangeDiscount'];
    giveType = json['giveType'];
    localSelectQty = json['localSelectQty'] ?? 0;
  }

  ///id
  String? id;

  ///账套id
  String? profileId;

  ///创建时间
  String? createTime;

  ///修改时间
  String? updateTime;

  ///是否删除
  int? deleted;

  ///充值规则档位福利明细id
  String? rechargeGearDetailId;

  ///商品skuid
  String? skuId;

  ///单位id
  String? unitId;

  ///商品id
  String? ptypeId;

  ///卡券模板id
  String? cardTemplateId;

  ///数量：例如：优惠券充值送2张，商品送5个，次卡送3次，赠金200元，积分30分，(权益卡只能一张)
  num? valueQty;

  ///卡券或权益名称
  String? cardName;

  ///商品名称
  String? ptypeName;

  ///属性值id1
  String? propValueId1;

  ///属性值名称1
  String? propValueName1;

  ///属性值id2
  String? propValueId2;

  ///属性值名称2
  String? propValueName2;

  ///属性值id3
  String? propValueId3;

  ///属性值名称3
  String? propValueName3;

  ///属性值id4
  String? propValueId4;

  ///属性值名称4
  String? propValueName4;

  ///属性值id5
  String? propValueId5;

  ///属性值名称5
  String? propValueName5;

  ///属性值id6
  String? propValueId6;

  ///属性值名称6
  String? propValueName6;

  String? unitName;

  num? unitRate;

  ///商品编号
  String? usercode;

  ///属性组合名称
  String? propertyName;

  ///零售价
  num? retailPrice;

  ///换购价
  num? exchangePrice;

  ///换购折扣
  num? exchangeDiscount;

  ///福利类型： 0=赠送 1=换购价格 2=换购折扣
  int? giveType;

  ///本地选择赠品时记录的赠品数量
  num localSelectQty = 0;

  ///税率
  num? taxRate;



  Rule copyWith({
    String? id,
    String? profileId,
    String? createTime,
    String? updateTime,
    int? deleted,
    String? rechargeGearDetailId,
    String? skuId,
    String? unitId,
    String? ptypeId,
    String? cardTemplateId,
    int? valueQty,
    String? cardName,
    String? ptypeName,
    String? propValueId1,
    String? propValueName1,
    String? propValueId2,
    String? propValueName2,
    String? propValueId3,
    String? propValueName3,
    String? propValueId4,
    String? propValueName4,
    String? propValueId5,
    String? propValueName5,
    String? propValueId6,
    String? propValueName6,
    String? unitName,
    num? unitRate,
    num? taxRate,
    String? usercode,
    String? propertyName,
    num? retailPrice,
    num? exchangePrice,
    num? exchangeDiscount,
    int? giveType,
    num? localSelectQty,
  }) =>
      Rule(
        id: id ?? this.id,
        profileId: profileId ?? this.profileId,
        createTime: createTime ?? this.createTime,
        updateTime: updateTime ?? this.updateTime,
        deleted: deleted ?? this.deleted,
        rechargeGearDetailId: rechargeGearDetailId ?? this.rechargeGearDetailId,
        skuId: skuId ?? this.skuId,
        unitId: unitId ?? this.unitId,
        ptypeId: ptypeId ?? this.ptypeId,
        cardTemplateId: cardTemplateId ?? this.cardTemplateId,
        valueQty: valueQty ?? this.valueQty,
        cardName: cardName ?? this.cardName,
        ptypeName: ptypeName ?? this.ptypeName,
        propValueId1: propValueId1 ?? this.propValueId1,
        propValueName1: propValueName1 ?? this.propValueName1,
        propValueId2: propValueId2 ?? this.propValueId2,
        propValueName2: propValueName2 ?? this.propValueName2,
        propValueId3: propValueId3 ?? this.propValueId3,
        propValueName3: propValueName3 ?? this.propValueName3,
        propValueId4: propValueId4 ?? this.propValueId4,
        propValueName4: propValueName4 ?? this.propValueName4,
        propValueId5: propValueId5 ?? this.propValueId5,
        propValueName5: propValueName5 ?? this.propValueName5,
        propValueId6: propValueId6 ?? this.propValueId6,
        propValueName6: propValueName6 ?? this.propValueName6,
        unitName: unitName ?? this.unitName,
        unitRate: unitRate ?? this.unitRate,
        taxRate: taxRate ?? this.taxRate,
        usercode: usercode ?? this.usercode,
        propertyName: propertyName ?? this.propertyName,
        retailPrice: retailPrice ?? this.retailPrice,
        exchangePrice: exchangePrice ?? this.exchangePrice,
        exchangeDiscount: exchangeDiscount ?? this.exchangeDiscount,
        giveType: giveType ?? this.giveType,
        localSelectQty: localSelectQty ?? this.localSelectQty,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['deleted'] = deleted;
    map['rechargeGearDetailId'] = rechargeGearDetailId;
    map['skuId'] = skuId;
    map['unitId'] = unitId;
    map['ptypeId'] = ptypeId;
    map['cardTemplateId'] = cardTemplateId;
    map['valueQty'] = valueQty;
    map['cardName'] = cardName;
    map['ptypeName'] = ptypeName;
    map['propValueId1'] = propValueId1;
    map['propValueName1'] = propValueName1;
    map['propValueId2'] = propValueId2;
    map['propValueName2'] = propValueName2;
    map['propValueId3'] = propValueId3;
    map['propValueName3'] = propValueName3;
    map['propValueId4'] = propValueId4;
    map['propValueName4'] = propValueName4;
    map['propValueId5'] = propValueId5;
    map['propValueName5'] = propValueName5;
    map['propValueId6'] = propValueId6;
    map['propValueName6'] = propValueName6;
    map['unitName'] = unitName;
    map['unitRate'] = unitRate;
    map['taxRate'] = taxRate;
    map['usercode'] = usercode;
    map['propertyName'] = propertyName;
    map['retailPrice'] = retailPrice;
    map['exchangePrice'] = exchangePrice;
    map['exchangeDiscount'] = exchangeDiscount;
    map['giveType'] = giveType;
    map['localSelectQty'] = localSelectQty;
    return map;
  }
}
