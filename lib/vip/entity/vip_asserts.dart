import 'package:haloui/utils/math_util.dart';

///会员资产，替代之前的会员积分表
class VipAsserts {
  VipAsserts(
      {this.id,
      this.vipId,
      this.profileId,
      this.createTime,
      this.updateTime,
      this.deleted,
      this.protectScore,
      this.availableScore,
      this.score,
      this.chargeTotal,
      this.giftTotal,
      this.total});

  VipAsserts.fromJson(dynamic json) {
    id = json['id'];
    vipId = json['vipId'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleted = json['deleted'];
    protectScore = json['protectScore'];
    availableScore = json['availableScore'];
    score = json['score'];
    chargeTotal = json['chargeTotal'];
    giftTotal = json['giftTotal'];
    total = json['total'];
  }

  ///资产id
  String? id;

  ///会员id
  String? vipId;

  ///账套id
  String? profileId;

  ///创建时间
  String? createTime;

  ///修改时间
  String? updateTime;

  ///是否删除
  bool? deleted;

  ///保护积分
  int? protectScore;

  ///可用积分
  int? availableScore;

  ///积分余额
  int? score;

  ///储值余额
  double? chargeTotal;

  ///赠送金额
  double? giftTotal;

  ///4.4新增资产总金额(赠金+本金)
  double? total;

  ///4.4修改表结构之后，资产总金额=充值金额+赠送金额
  double get totalMoney =>
      total ??
      (MathUtil.addDec((chargeTotal ?? 0), (giftTotal ?? 0))).toDouble();

  VipAsserts copyWith({
    String? id,
    String? vipId,
    String? profileId,
    String? createTime,
    String? updateTime,
    bool? deleted,
    int? protectScore,
    int? availableScore,
    int? score,
    double? chargeTotal,
    double? giftTotal,
    double? total,
  }) =>
      VipAsserts(
        id: id ?? this.id,
        vipId: vipId ?? this.vipId,
        profileId: profileId ?? this.profileId,
        createTime: createTime ?? this.createTime,
        updateTime: updateTime ?? this.updateTime,
        deleted: deleted ?? this.deleted,
        protectScore: protectScore ?? this.protectScore,
        availableScore: availableScore ?? this.availableScore,
        score: score ?? this.score,
        chargeTotal: chargeTotal ?? this.chargeTotal,
        giftTotal: giftTotal ?? this.giftTotal,
        total: total ?? this.total,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['vipId'] = vipId;
    map['profileId'] = profileId;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['deleted'] = deleted;
    map['protectScore'] = protectScore;
    map['availableScore'] = availableScore;
    map['score'] = score;
    map['chargeTotal'] = chargeTotal;
    map['giftTotal'] = giftTotal;
    map['total'] = total;
    return map;
  }
}
