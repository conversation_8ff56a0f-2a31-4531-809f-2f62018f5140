///卡卷
class CardDto {
  ///卡劵id
  String? id;

  ///创建时间，现有卡卷创建就会开始生效
  String? createTime;

  ///卡券编号
  String? cardNo;

  ///核销状态 0：未核销 1：已核销
  bool? writeOffState;

  ///会员id
  String? vipId;

  ///说明
  String? comment;

  ///卡券模板id（卡卷包id）
  String? cardTemplateId;

  ///使用范围 0:所有人 1：会员
  int? rangeType;

  ///绑定会员
  String? vipName;

  ///会员手机号
  String? vipPhone;

  ///可用门店
  String? otypeName;

  ///卡券名称
  String? cardName;

  ///卡券类型 0等级权益卡、1普通权益卡、2：代金券 3：折扣券 4：礼品券
  int? cardType;

  ///过期时间
  String? expiryDay;

  ///是否限制最低消费
  int? miniCostType;

  ///最低消费值，为0代表任何消费都可以
  double? minConstValue;

  CardDto({
    this.id,
    this.cardNo,
    this.writeOffState,
    this.vipId,
    this.comment,
    this.cardTemplateId,
    this.rangeType,
    this.vipName,
    this.vipPhone,
    this.otypeName,
    this.cardName,
    this.cardType,
    this.expiryDay,
    this.miniCostType,
    this.minConstValue,
    this.createTime,
  });

  CardDto.fromJson(dynamic json) {
    id = json['id'];
    cardNo = json['cardNo'];
    writeOffState = json['writeOffState'];
    vipId = json['vipId'];
    comment = json['comment'];
    cardTemplateId = json['cardTemplateId'];
    rangeType = json['rangeType'];
    vipName = json['vipName'];
    vipPhone = json['vipPhone'];
    otypeName = json['fullName'];
    cardName = json['cardName'];
    cardType = json['cardType'];
    expiryDay = json['expiryDay'];
    miniCostType = json['miniCostType'];
    minConstValue = json['minConstValue'];
    createTime = json['createTime'];
  }

  CardDto copyWith({
    String? id,
    String? cardNo,
    bool? activteState,
    String? activieTime,
    bool? writeOffState,
    String? writeOffTime,
    String? vipId,
    String? comment,
    String? cardTemplateId,
    int? rangeType,
    String? vipName,
    String? vipPhone,
    String? otypeName,
    String? cardName,
    int? cardType,
    int? valueType,
    int? miniCostType,
    double? minConstValue,
    double? deductionTotal,
    String? createTime,
    int? qty,
  }) =>
      CardDto(
        id: id ?? this.id,
        cardNo: cardNo ?? this.cardNo,
        writeOffState: writeOffState ?? this.writeOffState,
        vipId: vipId ?? this.vipId,
        comment: comment ?? this.comment,
        cardTemplateId: cardTemplateId ?? this.cardTemplateId,
        rangeType: rangeType ?? this.rangeType,
        vipName: vipName ?? this.vipName,
        vipPhone: vipPhone ?? this.vipPhone,
        otypeName: otypeName ?? this.otypeName,
        cardName: cardName ?? this.cardName,
        cardType: cardType ?? this.cardType,
        expiryDay: expiryDay ?? this.expiryDay,
        miniCostType: miniCostType ?? this.miniCostType,
        minConstValue: minConstValue ?? this.minConstValue,
        createTime: createTime ?? this.createTime,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['cardNo'] = cardNo;
    map['writeOffState'] = writeOffState;
    map['vipId'] = vipId;
    map['comment'] = comment;
    map['cardTemplateId'] = cardTemplateId;
    map['rangeType'] = rangeType;
    map['vipName'] = vipName;
    map['vipPhone'] = vipPhone;
    map['otypeName'] = otypeName;
    map['cardName'] = cardName;
    map['cardType'] = cardType;
    map['expiryDay'] = expiryDay;
    map['miniCostType'] = miniCostType;
    map['minConstValue'] = minConstValue;
    map['createTime'] = createTime;
    return map;
  }
}
