///会员积分和储值
class Score {
  Score({
    this.vipId,
    this.storeBalance,
    this.scoreBalance,
    this.protectScore,
    this.useableScore,
  });

  Score.fromJson(dynamic json) {
    vipId = json['vipId'];
    storeBalance = json['storeBalance'];
    scoreBalance = json['scoreBalance'];
    protectScore = json['protectScore'];
    useableScore = json['useableScore'];
  }

  ///会员id
  String? vipId;

  ///储值余额
  double? storeBalance;

  ///积分余额
  int? scoreBalance;

  ///保护积分
  int? protectScore;

  ///可用积分
  int? useableScore;

  Score copyWith({
    String? vipId,
    double? storeBalance,
    int? scoreBalance,
    int? protectScore,
    int? useableScore,
  }) =>
      Score(
        vipId: vipId ?? this.vipId,
        storeBalance: storeBalance ?? this.storeBalance,
        scoreBalance: scoreBalance ?? this.scoreBalance,
        protectScore: protectScore ?? this.protectScore,
        useableScore: useableScore ?? this.useableScore,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vipId'] = vipId;
    map['storeBalance'] = storeBalance;
    map['scoreBalance'] = scoreBalance;
    map['protectScore'] = protectScore;
    map['useableScore'] = useableScore;
    return map;
  }
}

///会员等级
class Level {
  Level({
    this.levelName,
    this.levelId,
    this.vipLevel,
    this.vipType
  });

  Level.fromJson(dynamic json) {
    levelName = json['levelName'];
    levelId = json['levelId'];
    vipLevel = json['vipLevel'];
    vipType = json['vipType'];
  }
  ///

  ///等级
  int? vipLevel;

  /// 0免费会员，1付费会员
  bool? vipType;

  ///等级名称
  String? levelName;

  ///等级id
  String? levelId;

  Level copyWith({
    String? levelName,
    String? levelId,
    bool? vipType,
    int? vipLevel,
  }) =>
      Level(
        levelName: levelName ?? this.levelName,
        levelId: levelId ?? this.levelId,
        vipLevel: vipLevel ?? this.vipLevel,
        vipType: vipType ?? this.vipType,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['levelName'] = levelName;
    map['levelId'] = levelId;
    map['vipType'] = vipType;
    map['vipLevel'] = vipLevel;
    return map;
  }
}

///权益卡
class RightsCard {
  RightsCard({
    this.rightsCardId,
    this.rightsCardName,
  });

  RightsCard.fromJson(dynamic json) {
    rightsCardId = json['rightsCardId'];
    rightsCardName = json['rightsCardName'];
  }

  ///权益卡id
  String? rightsCardId;

  ///权益卡名称
  String? rightsCardName;

  RightsCard copyWith({
    String? rightsCardId,
    String? rightsCardName,
  }) =>
      RightsCard(
        rightsCardId: rightsCardId ?? this.rightsCardId,
        rightsCardName: rightsCardName ?? this.rightsCardName,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['rightsCardId'] = rightsCardId;
    map['rightsCardName'] = rightsCardName;
    return map;
  }
}

///门店
class Otype {
  Otype({
    this.otypeId,
    this.otypeName,
  });

  Otype.fromJson(dynamic json) {
    otypeId = json['otypeId'];
    otypeName = json['otypeName'];
  }

  String? otypeId;
  String? otypeName;

  Otype copyWith({
    String? otypeId,
    String? otypeName,
  }) =>
      Otype(
        otypeId: otypeId ?? this.otypeId,
        otypeName: otypeName ?? this.otypeName,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['otypeId'] = otypeId;
    map['otypeName'] = otypeName;
    return map;
  }
}
