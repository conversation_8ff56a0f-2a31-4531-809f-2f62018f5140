
import '../../../common/net/request_method.dart';
import '../entity/vip_asserts.dart';
import '../../../vip/entity/vip_dto.dart';
import '../../../vip/entity/vip_simple.dart';
import '../../../vip/entity/vip_tag.dart';

import 'card_dto.dart';
import 'vip_consume_dto.dart';

///[RequestMethod.POST_GET_VIP_LEVEL_ASSERTS_RIGHTS_CARD_BY_PHONE] 接口返回的实体类
class VipWithLevelAssertsRightsCardDTO {
  ///会员
  Vip? vip;

  ///会员资产
  VipAsserts? asserts;

  ///等级
  Level? level;

  ///权益卡
  List<CardDto>? rightCardList;

  ///会员已激活绑定的卡劵
  List<CardDto>? cardList;

  ///等级权益卡列表（目前大小只会为1）
  List<CardDto>? levelCardList;

  ///会员关联门店
  List<Otype>? otypeList;

  ///会员标签
  List<VipTag>? tagList;

  ///会员消费统计
  VipConsumeDTO? consume;

  VipWithLevelAssertsRightsCardDTO();

  VipWithLevelAssertsRightsCardDTO.fromJson(dynamic json) {
    Map<String, dynamic>? vipJson = json["vip"];
    if (vipJson != null) {
      vip = Vip.fromJson(vipJson);
    }
    Map<String, dynamic>? vipAsserts = json["ssVipAsserts"];
    if (vipAsserts != null) {
      asserts = VipAsserts.fromJson(vipAsserts);
    }
    Map<String, dynamic>? levelJson = json["level"];
    if (levelJson != null) {
      level = Level.fromJson(levelJson);
    }
    List? rightCardList = json["rightCardList"];
    if (rightCardList != null) {
      this.rightCardList =
          rightCardList.map((e) => CardDto.fromJson(e)).toList();
    }
    List? otypeList = json["otypeList"];
    if (otypeList != null) {
      this.otypeList = otypeList.map((e) => Otype.fromJson(e)).toList();
    }
    List? cardList = json["cardList"];
    if (cardList != null) {
      this.cardList = cardList.map((e) => CardDto.fromJson(e)).toList();
    }
    List? levelCardList = json["levelCardList"];
    if (levelCardList != null) {
      this.levelCardList =
          levelCardList.map((e) => CardDto.fromJson(e)).toList();
    }
    List? tagList = json["tagList"];
    if (tagList != null) {
      this.tagList = tagList.map((e) => VipTag.fromJson(e)).toList();
    }
    Map<String, dynamic>? consume = json["consume"];
    if (consume != null) {
      this.consume = VipConsumeDTO.fromMap(consume);
    }
  }
}
