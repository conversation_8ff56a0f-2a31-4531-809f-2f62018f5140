import 'dart:math';

import '../../bill/entity/goods_detail_dto.dart';
import '../entity/get_vip_level_score_rights_card_response.dart';
import 'svip_util.dart';

///当前用户是否是会员
bool checkIsVip(VipWithLevelAssertsRightsCardDTO? vipInfo) =>
    vipInfo?.vip?.id?.isNotEmpty == true;

///判断会员是否有效
bool isValidVip(VipWithLevelAssertsRightsCardDTO? vipInfo) {
  //会员是否有效
  bool isVip = checkIsVip(vipInfo);
  if (isVip) {
    //判断会员是否过期
    isVip = !isVipExpired(vipInfo!.vip!.validDate,
        vipType: vipInfo.level!.vipType == true ? 1 : 0);
  }
  return isVip;
}

///取价逻辑，获取商品单价。
///优先级：会员价>门店零售价>零售价
///当价格本中的价格为0时，不参与取价判断
num getVipPrice(GoodsDetailDto goods,
    {bool? isVip, VipWithLevelAssertsRightsCardDTO? vipInfo}) {
  isVip ??= isValidVip(vipInfo);
  if (isVip) {
    num vipPrice = num.tryParse(goods.saleOtypeVipPrice ?? "0") ?? 0;
    if (vipPrice > 0) {
      return min(vipPrice, goods.currencyPrice);
    }
  }
  return goods.currencyPrice;
}
