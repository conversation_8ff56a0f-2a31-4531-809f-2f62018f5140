
///付费会员零点时间，过期时间早于此时间均为永久有效
final DateTime zeroPointDate = DateTime(2000, 1, 1);

///是否会员过期
bool isVipExpired(String? date, {int? vipType = 1}) {
  if (vipType != 1) return false;
  if (date == null) return false;
  DateTime? validDate = DateTime.tryParse(date);
  if (validDate == null) return false;
  if (isForever(date) || validDate.isAfter(DateTime.now())) {
    return false;
  }
  return true;
}

///是否是永久
bool isForever(String? date) {
  if (date == null) return true;
  DateTime? validDate = DateTime.tryParse(date);
  if (validDate == null) return true;
  return validDate.isBefore(zeroPointDate);
}

///计算过期时间
DateTime getValidDate(int validity, DateTime startDate) {
  return DateTime(
      startDate.year, startDate.month + validity, startDate.day + 1);
  // return getEndDate(DateTime(startDate.year, startDate.month + validity,
  //     startDate.day, startDate.hour, startDate.minute, startDate.second));
}
