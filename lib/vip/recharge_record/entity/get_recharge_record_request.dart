class GetRechargeRecordRequestDto {
  GetRechargeRecordRequestDto({
    this.startTime,
    this.endTime,
    this.phone,
    this.page,
    this.pageSize,
    this.otypeId,
  });

  GetRechargeRecordRequestDto.fromJson(dynamic json) {
    startTime = json['startTime'];
    endTime = json['endTime'];
    phone = json['phone'];
    page = json['page'];
    pageSize = json['pageSize'];
    otypeId = json['otypeId'];
  }

  ///开始时间
  String? startTime;

  ///结束时间
  String? endTime;

  ///会员电话号码
  String? phone;

  ///页码
  int? page;

  ///分页大小
  int? pageSize;

  ///门店id
  String? otypeId;

  GetRechargeRecordRequestDto copyWith({
    String? startTime,
    String? endTime,
    String? phone,
    int? page,
    int? pageSize,
    String? otypeId,
  }) =>
      GetRechargeRecordRequestDto(
        startTime: startTime ?? this.startTime,
        endTime: endTime ?? this.endTime,
        phone: phone ?? this.phone,
        otypeId: otypeId ?? this.otypeId,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['startTime'] = startTime;
    map['endTime'] = endTime;
    map['phone'] = phone;
    map['page'] = page;
    map['pageSize'] = pageSize;
    map['otypeId'] = otypeId;
    return map;
  }
}
