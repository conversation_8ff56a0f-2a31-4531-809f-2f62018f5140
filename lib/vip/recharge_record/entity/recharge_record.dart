import '../../../bill/entity/payment_dto.dart';

///充值记录
class _RechargeRecord {
  _RechargeRecord({
    this.id,
    this.vipId,
    this.vchcode,
    this.chargeType,
    this.createTime,
  });

  _RechargeRecord.fromMap(dynamic json) {
    id = json['id'];
    vipId = json['vipId'];
    vchcode = json['vchcode'];
    chargeType = json['chargeType'];
    createTime = json['createTime'];
  }

  ///充值记录id
  String? id;

  ///会员id
  String? vipId;

  ///充值收款单id
  String? vchcode;

  ///充值类型
  String? chargeType;

  ///创建日期
  String? createTime;

  _RechargeRecord copyWith({
    String? id,
    String? vipId,
    String? vchcode,
    String? chargeType,
    String? createTime,
  }) =>
      _RechargeRecord(
        id: id ?? this.id,
        vipId: vipId ?? this.vipId,
        vchcode: vchcode ?? this.vchcode,
        chargeType: chargeType ?? this.chargeType,
        createTime: createTime ?? this.createTime,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['vipId'] = vipId;
    map['vchcode'] = vchcode;
    map['chargeType'] = chargeType;
    map['createTime'] = createTime;
    return map;
  }
}

///充值记录（列表）
class RechargeRecordDTO extends _RechargeRecord {
  ///会员电话号码
  String? phone;

  ///会员名称
  String? vipName;

  ///充值金额
  num? rechargeTotal;

  ///解密buyerid
  String? buyerId;

  RechargeRecordDTO({
    String? id,
    String? vipId,
    String? vchcode,
    String? chargeType,
    String? createTime,
    this.phone,
    this.vipName,
    this.rechargeTotal,
    this.buyerId
  }) : super(
      id: id,
      vipId: vipId,
      vchcode: vchcode,
      chargeType: chargeType,
      createTime: createTime);

  RechargeRecordDTO.fromMap(dynamic json) : super.fromMap(json) {
    phone = json['phone'];
    vipName = json['vipName'];
    rechargeTotal = json['rechargeTotal'];
    buyerId = json['buyerId'];
  }

  @override
  RechargeRecordDTO copyWith({
    String? id,
    String? vipId,
    String? buyerId,
    String? vchcode,
    String? chargeType,
    String? createTime,
    String? phone,
    String? vipName,
    num? rechargeTotal,
  }) =>
      RechargeRecordDTO(
        id: id ?? this.id,
        vipId: vipId ?? this.vipId,
        buyerId: buyerId ?? this.buyerId,
        vchcode: vchcode ?? this.vchcode,
        chargeType: chargeType ?? this.chargeType,
        createTime: createTime ?? this.createTime,
        phone: phone ?? this.phone,
        vipName: vipName ?? this.vipName,
        rechargeTotal: rechargeTotal ?? this.rechargeTotal,
      );

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      "phone": phone,
      "vipName": vipName,
      "buyerId": buyerId,
      "rechargeTotal": rechargeTotal,
    };
  }
}

///充值记录详情
class RechargeRecordDetailDTO extends _RechargeRecord {
  ///充值赠品
  List<RechargeGift>? giveList;

  ///支付方式名称
  String? paymentName;

  ///充值门店名称
  String? ofullname;

  ///业务员
  String? efullname;

  ///收营员
  String? createEfullname;

  ///备注
  String? memo;


  List<PaymentDto>?atypeInfo;

  RechargeRecordDetailDTO({
    String? id,
    String? vipId,
    String? vchcode,
    String? chargeType,
    String? createTime,
    this.giveList,
    this.paymentName,
    this.ofullname,
    this.efullname,
    this.createEfullname,
    this.memo,this.atypeInfo
  }) : super(
      id: id,
      vipId: vipId,
      vchcode: vchcode,
      chargeType: chargeType,
      createTime: createTime);

  RechargeRecordDetailDTO.fromMap(dynamic json) : super.fromMap(json) {
    paymentName = json['paymentName'];
    ofullname = json['ofullname'];
    efullname = json['efullname'];
    createEfullname = json['createEfullname'];
    memo = json['memo'];
    giveList = (json["giveList"] as List?)
        ?.map((e) => RechargeGift.fromMap(e))
        .toList();
    atypeInfo = (json["atypeInfo"] as List?)
        ?.map((e) => PaymentDto.fromMap(e))
        .toList();
  }

  @override
  RechargeRecordDetailDTO copyWith({
    String? id,
    String? vipId,
    String? vchcode,
    String? chargeType,
    String? createTime,
    List<RechargeGift>? giveList,
    String? paymentName,
    String? ofullname,
    String? efullname,
    String? createEfullname,
    String? memo,List<PaymentDto>?atypeInfo
  }) =>
      RechargeRecordDetailDTO(
        id: id ?? this.id,
        vipId: vipId ?? this.vipId,
        vchcode: vchcode ?? this.vchcode,
        chargeType: chargeType ?? this.chargeType,
        createTime: createTime ?? this.createTime,
        giveList: giveList ?? this.giveList,
        paymentName: paymentName ?? this.paymentName,
        ofullname: ofullname ?? this.ofullname,
        efullname: efullname ?? this.efullname,
        createEfullname: createEfullname ?? this.createEfullname,
        memo: memo ?? this.memo,
        atypeInfo: atypeInfo ?? this.atypeInfo,
      );

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      "giveList": giveList,
      "paymentName": paymentName,
      "ofullname": ofullname,
      "efullname": efullname,
      "createEfullname": createEfullname,
      "memo": memo,
      "atypeInfo": atypeInfo,
    };
  }
}

///充值赠品
class RechargeGift {
  ///id
  String? id;

  ///充值记录id
  String? rechargeId;

  ///赠品类型[RechargeGiftType]
  String? giveType;

  ///赠送内容id（优惠券id/ 商品id）
  String? objectId;

  ///skuId
  String? skuId;

  ///单位id
  String? unitId;

  ///是否删除
  bool? deleted;

  ///赠送数量
  num? giveQty;

  int? cardType;

  String? cardName;

  String? ptypeName;

  String? skuName;

  ///单位名称
  String? unitName;

  String get pFullname {
    String name = ptypeName ?? "";
    if(skuName?.isNotEmpty == true) {
      name = "$name:$skuName";
    }
    return name;
  }

  RechargeGift({
    this.id,
    this.rechargeId,
    this.giveType,
    this.objectId,
    this.skuId,
    this.unitId,
    this.deleted,
    this.giveQty,
    this.cardType,
    this.cardName,
    this.ptypeName,
    this.skuName,
    this.unitName,
  });

  RechargeGift copyWith({
    String? id,
    String? rechargeId,
    String? giveType,
    String? objectId,
    String? skuId,
    String? unitId,
    bool? deleted,
    num? giveQty,
    int? cardType,
    String? cardName,
    String? ptypeName,
    String? skuName,
    String? unitName,
  }) {
    return RechargeGift(
      id: id ?? this.id,
      rechargeId: rechargeId ?? this.rechargeId,
      giveType: giveType ?? this.giveType,
      objectId: objectId ?? this.objectId,
      skuId: skuId ?? this.skuId,
      unitId: unitId ?? this.unitId,
      deleted: deleted ?? this.deleted,
      giveQty: giveQty ?? this.giveQty,
      cardType: cardType ?? this.cardType,
      cardName: cardName ?? this.cardName,
      ptypeName: ptypeName ?? this.ptypeName,
      skuName: skuName ?? this.skuName,
      unitName: unitName ?? this.unitName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rechargeId': rechargeId,
      'giveType': giveType,
      'objectId': objectId,
      'skuId': skuId,
      'unitId': unitId,
      'deleted': deleted,
      'giveQty': giveQty,
      'cardType': cardType,
      'cardName': cardName,
      'ptypeName': ptypeName,
      'skuName': skuName,
      'unitName': unitName,
    };
  }

  RechargeGift.fromMap(Map<String, dynamic> map)
      : id = map['id'],
        rechargeId = map['rechargeId'],
        giveType = map['giveType'],
        objectId = map['objectId'],
        skuId = map['skuId'],
        unitId = map['unitId'],
        deleted = map['deleted'],
        giveQty = map['giveQty'],
        cardType = map['cardType'],
        cardName = map['cardName'],
        ptypeName = map['ptypeName'],
        skuName = map['skuName'],
        unitName = map['unitName'];
}

enum RechargeGiftType {
  ///赠金
  GIVE_MONEY,

  ///积分
  SCORE,

  ///商品
  GOODS,

  ///优惠券
  CARD
}

extension RechargeGiftTypeExtension on RechargeGiftType {
  int get value {
    switch (this) {
      case RechargeGiftType.GIVE_MONEY:
        return 0;
      case RechargeGiftType.SCORE:
        return 1;
      case RechargeGiftType.GOODS:
        return 2;
      case RechargeGiftType.CARD:
        return 3;
    }
  }
}
