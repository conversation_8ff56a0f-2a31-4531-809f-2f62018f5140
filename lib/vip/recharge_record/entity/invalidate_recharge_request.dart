///充值作废请求实体类
class InvalidateRechargeRequest {
  InvalidateRechargeRequest({
    this.recordId,
    this.otypeId,
    this.btypeId,
    this.createEtypeId,
    this.etypeId,
    this.cashierId,
  });

  InvalidateRechargeRequest.fromJson(dynamic json) {
    recordId = json['recordId'];
    otypeId = json['otypeId'];
    btypeId = json['btypeId'];
    createEtypeId = json['createEtypeId'];
    etypeId = json['etypeId'];
    cashierId = json['cashierId'];
  }

  ///充值记录id
  String? recordId;

  ///门店id
  String? otypeId;

  ///往来单位id
  String? btypeId;

  ///收银员id
  String? createEtypeId;

  ///业务员，经手人id
  String? etypeId;

  ///收银机id
  String? cashierId;

  InvalidateRechargeRequest copyWith({
    String? recordId,
    String? otypeId,
    String? btypeId,
    String? createEtypeId,
    String? etypeId,
    String? cashierId,
  }) =>
      InvalidateRechargeRequest(
        recordId: recordId ?? this.recordId,
        otypeId: otypeId ?? this.otypeId,
        btypeId: btypeId ?? this.btypeId,
        createEtypeId: createEtypeId ?? this.createEtypeId,
        etypeId: etypeId ?? this.etypeId,
        cashierId: cashierId ?? this.cashierId,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['recordId'] = recordId;
    map['otypeId'] = otypeId;
    map['btypeId'] = btypeId;
    map['createEtypeId'] = createEtypeId;
    map['etypeId'] = etypeId;
    map['cashierId'] = cashierId;
    return map;
  }
}
