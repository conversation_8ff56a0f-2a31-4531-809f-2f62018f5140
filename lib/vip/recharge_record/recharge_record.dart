import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_utils/halo_utils.dart' show SizeExtension;
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/widget/datepicker/pduration.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_empty.dart';
import 'package:haloui/widget/halo_textfield.dart';
import 'package:haloui/widget/halo_toast.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';

import '../../common/standard.dart';
import '../../common/style/app_color_helper.dart';
import '../../common/style/app_colors.dart';
import '../../common/widget/datetime_filter.dart';
import '../../common/widget/encrypted_ richtext.dart';
import '../../iconfont/icon_font.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/filter_mixin.dart';
import '../../widgets/halo_pos_label.dart';
import '../model/vip_recharge_model.dart';
import 'entity/get_recharge_record_request.dart';
import 'entity/recharge_record.dart';
import 'recharge_record_detail.dart';

///空白占位图
Widget buildEmptyPlaceholder() {
  return HaloEmptyContainer(
    gravity: EmptyGravity.CENTER,
    image: Image.asset('assets/images/nodata.png'),
    title: "暂无数据",
    titleStyle: TextStyle(
        decoration: TextDecoration.none, fontSize: 30.sp, color: Colors.grey),
  );
}

///会员储值记录
class RechargeRecordPage extends BaseStatefulPage {
  const RechargeRecordPage({Key? key}) : super(key: key, rightFlex: 2);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _RechargeRecordPageState();
}

class _RechargeRecordPageState extends BaseStatefulPageState<RechargeRecordPage>
    with DateTimeFilterMixin, FilterMixin {
  static const _pageSize = 40;

  final GetRechargeRecordRequestDto request =
  GetRechargeRecordRequestDto(pageSize: _pageSize);

  ///搜索框前面标题
  @override
  String get searchTitle => "手机号:";

  ///是否有更多数据
  bool _hasMore = true;

  ///充值记录列表
  final List<RechargeRecordDTO> _data = [];

  ///列表当前选中的item的充值记录id
  final ValueNotifier<String?> _currentRecordId = ValueNotifier(null);

  @override
  Future<void>? onInitState() async {
    await super.onInitState();
    onSearch();
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    Widget left;
    if (_data.isEmpty) {
      left = buildEmptyPlaceholder();
    } else {
      left = EasyRefresh(
          header: MaterialHeader(),
          footer: MaterialFooter(),
          onRefresh: () async => onSearch(),
          onLoad: _hasMore ? _request : null,
          child: ListView.separated(
            itemBuilder: _buildItem,
            itemCount: _data.length,
            separatorBuilder: (BuildContext context, int index) => const Divider(height: 1,),
          ));
    }
    return Row(
      children: [
        Expanded(child: left),
        const VerticalDivider(width: 1, color: Color(0xFFE9E9E9)),
      ],
    );
  }

  @override
  Widget buildRightBody(BuildContext context) {
    return RechargeRecordDetailPage(
      currentRecordId: _currentRecordId,
      onInvalidateCallback: (recordId) {
        setState(() {
          for (var value in _data) {
            if (value.id == recordId) {
              value.chargeType = "INVALID";
              break;
            }
          }
        });
      },
    );
  }

  ///构建item
  Widget _buildItem(BuildContext context, int index) {
    final item = _data[index];
    final style = TextStyle(
        color: const Color(0xFF333333),
        fontSize: 24.sp,
        overflow: TextOverflow.ellipsis);
    const encryptedStyle = TextStyle(
      color: Colors.blue,
      decoration: TextDecoration.underline,
      decorationColor: Colors.blue,
      decorationStyle: TextDecorationStyle.solid,
      // 其他文本样式
    );
    Widget content = Row(
      children: [
        Expanded(child: EncryptedRichText(encryptedText: item.phone,encryptedStyle:encryptedStyle,buyerId: item.buyerId,setPhone: (phone) {
          setState(() {
            item.phone = phone;
          });
        },)),
        SizedBox(width: 20.w),
        Expanded(child: Text(item.vipName ?? "", maxLines: 1, style: style)),
        SizedBox(width: 20.w),
        Expanded(
          child: Text(
            "￥${item.rechargeTotal ?? 0}",
            textAlign: TextAlign.end,
            maxLines: 1,
            style: style.copyWith(fontSize: 26.sp, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
    //作废
    if (item.chargeType == "INVALID") {
      content = Stack(
        children: [
          Positioned(
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            child: content,
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF869BB7), Color(0xFFB3C2D6)],
                ),
              ),
              child: Text("已作废",
                  style: TextStyle(fontSize: 20.sp, color: Colors.white)),
            ),
          ),
        ],
      );
    }
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => setState(() => _currentRecordId.value = item.id),
      child: Container(
        height: 100.h,
        color: item.id == _currentRecordId.value
            ? const Color(0xFFF4F7FF)
            : Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: content,
      ),
    );
  }

  @override
  String getActionBarTitle() => "会员储值记录";

  @override
  Widget buildFilterValueQuery(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: 500.w),
      child: super.buildFilterValueQuery(context),
    );
  }

  ///构建除了日期、单据编号的其他筛选条件
  @override
  Widget buildOtherFilter(BuildContext context) => Container();

  ///点击查询，发起请求
  @override
  void onSearch() {
    request.phone = filterValue;
    request.page = 1;
    request.otypeId = SpTool.getStoreInfo()!.otypeId;
    request.startTime = formatDateStringToLocal(textStartTimeController.text);
    request.endTime = formatDateStringToLocal(textEndTimeController.text);
    _request();
  }

  ///发起请求
  Future<void> _request() async {
    List<RechargeRecordDTO> list =
        (await VipRechargeModel.getRechargeRecordList(context, request))
            ?.list ??
            [];
    if (mounted) {
      setState(() {
        if (request.page == 1) {
          _data.clear();
          if (list.isNotEmpty) {
            _currentRecordId.value = list.first.id;
          } else {
            _currentRecordId.value = null;
          }
        }
        _hasMore = list.length >= _pageSize;
        _data.addAll(list);
        request.page = request.page! + 1;
      });
    }
  }
}
