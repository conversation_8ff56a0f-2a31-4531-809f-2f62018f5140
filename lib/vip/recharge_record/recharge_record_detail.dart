import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:halo_pos/bill/entity/payment_dto.dart';
import 'package:halo_utils/http/base_model.dart';
import '../../bill/entity/pay_result_dto.dart';
import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../common/num_extension.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import '../../common/tool/dialog_util.dart';
import '../../common/tool/payment.dart';
import '../../common/tool/sp_tool.dart';
import '../../enum/payment_enum.dart';
import '../model/vip_recharge_model.dart';
import 'entity/invalidate_recharge_request.dart';
import 'entity/recharge_record.dart';
import 'recharge_record.dart';

///充值记录详情
class RechargeRecordDetailPage extends StatefulWidget {
  final ValueNotifier<String?> currentRecordId;

  final void Function(String recordId)? onInvalidateCallback;

  const RechargeRecordDetailPage(
      {Key? key, required this.currentRecordId, this.onInvalidateCallback})
      : super(key: key);

  @override
  State<RechargeRecordDetailPage> createState() =>
      _RechargeRecordDetailPageState();
}

class _RechargeRecordDetailPageState extends State<RechargeRecordDetailPage> {
  final invalidatePermission =
      SpTool.getPermission().shopsalerechargeRecordinvalidate;

  RechargeRecordDetailDTO? _data;

  @override
  void initState() {
    super.initState();
    widget.currentRecordId.addListener(onRecordChange);
  }

  @override
  void didUpdateWidget(covariant RechargeRecordDetailPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentRecordId != widget.currentRecordId) {
      oldWidget.currentRecordId.removeListener(onRecordChange);
      widget.currentRecordId.addListener(onRecordChange);
    }
  }

  @override
  void dispose() {
    super.dispose();
    widget.currentRecordId.removeListener(onRecordChange);
  }

  void onRecordChange() {
    if (widget.currentRecordId.value?.isNotEmpty == true) {
      _request();
    }
    if (mounted) {
      setState(() {});
    }
  }

  ///内容成对出现
  Widget buildContent(
      {required String title, String? contentText, Widget? contentWidget}) {
    TextStyle titleStyle =
        TextStyle(color: const Color(0xFF808080), fontSize: 22.sp, height: 1);
    contentWidget ??= Text(contentText ?? "",
        style: titleStyle.copyWith(color: const Color(0xFF333333)));
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120.w,
          child: Text(
            title,
            textAlign: TextAlign.end,
            style: titleStyle,
          ),
        ),
        SizedBox(
          width: 40.w,
        ),
        Expanded(child: contentWidget),
      ],
    );
  }

  ///赠送项目
  Widget buildGift() {
    TextStyle style =
        TextStyle(fontSize: 22.sp, color: const Color(0xFFEA6404));
    final data = _data!;
    String goods = "";
    List<RechargeGift> goodsList = [];
    List<RechargeGift> otherGiftList = [];
    if (data.giveList != null) {
      for (var element in data.giveList!) {
        if ((element.giveQty ?? 0) <= 0) continue;
        if (element.giveType == RechargeGiftType.GOODS.name) {
          goodsList.add(element);
        } else {
          otherGiftList.add(element);
        }
      }
    }
    if (goodsList.isNotEmpty) {
      goods = goodsList.map((e) {
        final unitQty = (e.giveQty ?? 0).getIntWhenInteger;
        return "[${e.pFullname}]x$unitQty${e.unitName ?? ""}";
      }).join(" ");
    }
    return Container(
      color: const Color(0xFFFFFCF4),
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 14.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (otherGiftList.isNotEmpty)
            ...otherGiftList.map<Widget>((e) {
              final qty = (e.giveQty ?? 0).getIntWhenInteger;
              if (e.giveType == RechargeGiftType.SCORE.name) {
                return Text("积分 $qty", style: style);
              } else if (e.giveType == RechargeGiftType.GIVE_MONEY.name) {
                return Text("赠金 ￥$qty", style: style);
              } else if (e.giveType == RechargeGiftType.CARD.name) {
                String cardType = "优惠券";
                if (e.cardType == 0 || e.cardType == 1) {
                  cardType = "权益卡";
                }
                return Text("$cardType [${e.cardName ?? ""}]x$qty",
                    style: style);
              }
              return Container();
            }).toList(),
          if (goodsList.isNotEmpty == true) Text("商品 $goods", style: style),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.currentRecordId.value?.isEmpty != false || _data == null) {
      return Center(child: buildEmptyPlaceholder());
    }
    Widget content = Container(
      width: double.infinity,
      height: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 130.w),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 36.h),
            buildContent(
                title: "充值时间:", contentText: _parseDate(_data?.createTime)),
            SizedBox(height: 36.h),
            buildContent(title: "赠送项目:", contentWidget: buildGift()),
            SizedBox(height: 36.h),
            buildContent(title: "支付方式:", contentText: _data?.paymentName),
            SizedBox(height: 36.h),
            buildContent(title: "充值门店:", contentText: _data?.ofullname),
            SizedBox(height: 36.h),
            buildContent(title: "收银员:", contentText: _data?.createEfullname),
            SizedBox(height: 36.h),
            buildContent(title: "业务员:", contentText: _data?.efullname),
            SizedBox(height: 36.h),
            buildContent(title: "备注:", contentText: _data?.memo),
          ],
        ),
      ),
    );
    //已作废
    if (_data!.chargeType == "INVALID") {
      content = Stack(
        children: [
          content,
          Positioned(
            right: 36.w,
            bottom: 50.h,
            child: SvgPicture.asset(
              "assets/images/yizuofei.svg",
              width: 236.w,
              height: 236.w,
            ),
          )
        ],
      );
    } else {
      //底部作废按钮
      if (invalidatePermission == true) {
        content = Column(
          children: [
            Expanded(child: content),
            const Divider(color: Color(0xFFE1E1E1)),
            Container(
              color: Colors.white,
              width: double.infinity,
              height: 110.h,
              alignment: Alignment.centerRight,
              padding: EdgeInsets.only(right: 36.w),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: _invalidate,
                child: Container(
                  width: 196.w,
                  height: 66.h,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: Border.all(color: const Color(0xFF2769FF)),
                    borderRadius: BorderRadius.circular(4.w),
                  ),
                  child: Text(
                    "作废",
                    style: TextStyle(
                        color: const Color(0xFF2769FF), fontSize: 30.sp),
                  ),
                ),
              ),
            ),
          ],
        );
      }
    }

    return content;
  }

  ///获取储值记录
  Future<void> _request() async {
    if (!mounted) return;
    _data = await VipRechargeModel.getRechargeRecordDetail(
        context, widget.currentRecordId.value!);
    setState(() {});
  }

  ///储值记录作废
  Future<void> _invalidate() async {
    if (!mounted || _data?.id?.isEmpty != false) return;

    final paymentList = _data!.atypeInfo!;

    if (paymentList.any((element) => element.paywayType == 2)) {
      ///弹窗
      DialogUtil.showConfirmDialog(context,
          title: "提示",
          content: "支付类型为聚合支付，作废时会自动通过聚合支付账户退款，确认要继续作废吗？",
          actionLabels: ["取消", "确定"], confirmCallback: () {
            invalidateRechargeRecord();
      }, cancelCallback: () {});
    } else {
      invalidateRechargeRecord();
    }
  }

  ///作废
  void invalidateRechargeRecord() {
    final storeInfo = SpTool.getStoreInfo();
    final loginUser = SpTool.getLoginUser();
    final recordId = _data!.id!;

    VipRechargeModel.invalidateRechargeRecord(
        context,
        InvalidateRechargeRequest(
          recordId: recordId,
          otypeId: storeInfo?.otypeId,
          btypeId: storeInfo?.btypeId,
          etypeId: loginUser.employeeId,
          createEtypeId: loginUser.employeeId,
          cashierId: SpTool.getCashierInfo().id,
        )).then((value) {
      if (mounted) {
        widget.onInvalidateCallback?.call(recordId);
        if (_data?.id == recordId) {
          setState(() => _data!.chargeType = "INVALID");
        }
        HaloToast.show(context, msg: "作废成功");
      }
    });
  }

  ///聚合支付退款
  void refundPayment(
      BuildContext context, PaymentDto payment, String vchcode, VoidCallback voidCallback) async {
    String newOutNo =
        vchcode + DateTime.now().millisecondsSinceEpoch.toString();
    Map params = {
      "tradeAmount": num.parse(payment.currencyAtypeTotal!).abs().toString(),
      "outNo": payment.outNo,
      "orderNo": payment.payOrderNo,
      "paywayId": payment.paywayId,
      "refundOutNo": newOutNo,
      "goodsTitle": "会员储值",
      "goodsDesc": "会员储值作废"
    };
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_PAYREFUND, data: params);
    if (response.code == 200) {
      PayResult payResult = PayResult.fromJson(response.data);
      payResult.payInfo = params;
      if (mounted) {
        PaymentUtil.processingPayResult(context, payResult,
            result: (PayResultType resultType) {
          if (resultType == PayResultType.SUCCEEDED) {
            voidCallback();
          }
        }, onActionListener: (int index) {
          if (index == 0) {
            voidCallback();
          }
        });
      }
    } else {
      if (context.mounted) {
        HaloToast.show(context, msg: "聚合支付退款失败");
      }
    }
  }

  String _parseDate(String? date) {
    if (date?.isNotEmpty == true) {
      //Z代表utc时间,0时区，所以只需将其转换为本地时区即可
      DateTime? dateTime = DateTime.tryParse(date!)?.toLocal();
      if (dateTime == null) {
        return "";
      }
      return "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} "
          "${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}";
    }
    return "";
  }
}
