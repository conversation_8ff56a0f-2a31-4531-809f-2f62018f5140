import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../iconfont/icon_font.dart';
import '../../../vip/entity/right_card_item_model.dart';
import '../../../widgets/base/base_list.dart';
import '../../../vip/model/vip_model.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_checkbox.dart';

///会员详情-发放优惠券
class CouponsPages extends StatefulWidget {
  final List<String> vipIds;
  final VoidCallback voidCallback;

  const CouponsPages({
    Key? key,
    required this.vipIds,
    required this.voidCallback,
  }) : super(key: key);

  @override
  State<CouponsPages> createState() => _CouponsPagesState();
}

class _CouponsPagesState extends State<CouponsPages> {
  final rowHeight = 61.h;
  final List<RightCartItemModel> dataSource = [];
  var isSelectAll = false;
  final pageSize = 20;
  var pageIndex = 1;
  bool isHasMoreData = true;
  ScrollController scrollController = new ScrollController();

  @override
  void initState() {
    super.initState();
    onRequestData().then((value) {
      if (pageIndex == 1) {
        dataSource.clear();
        this.isSelectAll = false;
      }
      dataSource.addAll(value ?? []);
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Color.fromRGBO(0, 0, 0, 0),
        body: Container(
          alignment: Alignment.topCenter,
          margin: EdgeInsets.symmetric(vertical: 80.w, horizontal: 230.w),
          color: Colors.white,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: double.infinity,
            ),
            child: HaloContainer(direction: Axis.vertical, children: [
              // 顶部标题
              HaloContainer(
                padding: EdgeInsets.only(left: 12.w, right: 12.w),
                color: Colors.white,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                height: 55.h,
                children: [
                  Text(
                    "发放优惠券",
                    style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 28.sp,
                        color: AppColorHelper(context).getTitleBoldTextColor()),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      color: Colors.transparent,
                      alignment: Alignment.centerRight,
                      width: 100,
                      height: 30,
                      child: IconFont(
                        IconNames.close,
                        size: 30,
                      ),
                    ),
                  ),
                ],
              ),
              //线条
              Divider(
                color: AppColors.lineColor,
                height: 1,
              ),
              // 顶部固定第一行
              Container(child: buildTopItemView()),
              // 无数据状态
              Visibility(
                  visible: false,
                  child: Expanded(
                    child: HaloEmptyContainer(
                      gravity: EmptyGravity.CENTER,
                      image: Image.asset('assets/images/nodata.png'),
                      title: "暂无数据",
                      titleStyle: TextStyle(
                          decoration: TextDecoration.none,
                          fontSize: ScreenUtil().setSp(30),
                          color: Colors.grey),
                    ),
                  )),
              // 有数据状态
              Visibility(
                  visible: true,
                  child: Expanded(
                      child: Container(
                          child: HaloList(
                    dataSource,
                    scrollController: this.scrollController,
                    hasMoreData: this.isHasMoreData,
                    scrollWidthKeyboard: false,
                    buildItemContent: (context, index) {
                      return GestureDetector(
                        onTap: () {
                          // onItemClick(index, dataSource[index]);
                        },
                        child: buildItemView(index, dataSource[index]),
                      );
                    },
                    onRefresh: () {
                      pageIndex = 1;
                      isHasMoreData = true;
                      this.isSelectAll = false;
                      return onLoadData();
                    },
                    onLoadMore: () async {
                      return onLoadMore().then((value) {
                        this.isSelectAll = false;

                        if (value == LoadState.AllFinish) {
                          isHasMoreData = false;
                        }
                      });
                    },
                  )))),
              Divider(
                color: Color(0xFFDEDEDE),
                height: 1.5,
              ),
              HaloContainer(
                height: 120.w,
                padding: EdgeInsets.only(right: 25.w, bottom: 4.w),
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  HaloButton(
                    buttonType: HaloButtonType.outlinedButton,
                    borderColor: Color(0xFF999999),
                    borderRadius: 4.w,
                    outLineWidth: 2,
                    width: 196.w,
                    height: 66.w,
                    child: Text(
                      '取消',
                      style:
                          TextStyle(color: Color(0xFF333333), fontSize: 26.sp),
                    ),
                    onPressed: () {
                      // isSelectAll = false;
                      // for (CouponsEntity item in dataSource) {
                      //   item.isSelect = isSelectAll;
                      // }
                      // setState(() {});
                      Navigator.pop(context);
                    },
                  ),
                  SizedBox(
                    width: 30.w,
                  ),
                  HaloButton(
                    buttonType: HaloButtonType.elevatedButton,
                    borderRadius: 4.w,
                    width: 196.w,
                    height: 66.w,
                    disabledBackgroundColor: Color(0xFF4679FC),
                    child: Text(
                      '确定',
                      style:
                          TextStyle(color: Color(0xFFFFFFFF), fontSize: 26.sp),
                    ),
                    onPressed: () {
                      List<String> rightsIds = dataSource
                          .where((element) =>
                              element.isSelect == true &&
                              element.id?.isNotEmpty == true)
                          .map((e) => e.id!)
                          .toList();
                      if (rightsIds.length <= 0) {
                        HaloToast.show(context, msg: '请至少选择一张优惠券');
                        return;
                      }
                      onRequestGiveRightsCard(rightsIds)
                          .then((value) => Navigator.of(context).pop())
                          .onError((error, stackTrace) => {});
                    },
                  )
                ],
              ),
            ]),
          ),
        ));
  }

  Widget buildTopItemView() {
    var textStyle = TextStyle(
        color: Color(0xFF666666),
        fontSize: 22.sp,
        fontFamily: 'PingFangSC-Medium',
        fontWeight: FontWeight.w500);
    return Container(
      padding: EdgeInsets.only(left: 40.w, right: 32.w),
      decoration: BoxDecoration(
          color: Color(0xFFF5F5F5),
          border:
              Border(bottom: BorderSide(color: Color(0xFFE1E1E1), width: 1))),
      child: HaloContainer(
          mainAxisSize: MainAxisSize.max,
          height: rowHeight,
          children: [
            Expanded(
              flex: 1,
              child: Container(
                alignment: Alignment.centerLeft,
                height: rowHeight,
                child: HaloCheckBox(
                  value: isSelectAll,
                  onChanged: (bool value) {
                    // 选中所有
                    isSelectAll = !value;
                    for (RightCartItemModel item in dataSource) {
                      item.isSelect = !value;
                    }
                    setState(() {});
                  },
                  defaultImage: IconFont(
                    IconNames.weixuanzhong,
                    size: 32.w,
                    color: '#FFBFBFC1',
                  ),
                  selectedImage: IconFont(
                    IconNames.xuanzhong,
                    size: 32.w,
                    color: '#FF2C68FF',
                  ),
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                alignment: Alignment.centerLeft,
                height: rowHeight,
                child: HaloPosLabel(
                  '优惠券名称',
                  textStyle: textStyle,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                alignment: Alignment.centerLeft,
                height: rowHeight,
                child: HaloPosLabel(
                  '有效期',
                  textStyle: textStyle,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                alignment: Alignment.centerLeft,
                height: rowHeight,
                child: HaloPosLabel(
                  '优惠券类型',
                  textStyle: textStyle,
                ),
              ),
            ),
            Expanded(
              flex: 4,
              child: Container(
                alignment: Alignment.centerLeft,
                height: rowHeight,
                child: HaloPosLabel(
                  '包含权益',
                  textStyle: textStyle,
                ),
              ),
            ),
          ]),
    );
  }

  ///行内容回调方法
  Widget buildItemView(int index, RightCartItemModel itemModel) {
    var textStyle = TextStyle(
        color: Color(0xFF333333),
        fontSize: 22.sp,
        fontFamily: 'PingFangSC-Medium',
        fontWeight: FontWeight.w500);
    return Container(
        padding: EdgeInsets.only(left: 40.w, right: 32.w),
        decoration: BoxDecoration(
            color: Color(0xFFFFFFFF),
            border:
                Border(bottom: BorderSide(color: Color(0xFFE1E1E1), width: 1))),
        child: HaloContainer(
            mainAxisSize: MainAxisSize.max,
            height: rowHeight,
            // constraints: BoxConstraints(
            //     color: Color(0xFFFFFFFF),
            //     border:
            //     Border(bottom: BorderSide(color: Color(0xFFE1E1E1), width: 1))),
            children: [
              Expanded(
                flex: 1,
                child: Container(
                  alignment: Alignment.centerLeft,
                  child: HaloCheckBox(
                    value: itemModel.isSelect ?? false,
                    onChanged: (bool value) {
                      // 选中当前
                      itemModel.isSelect = !value;
                      var isAll = true;
                      for (RightCartItemModel item in dataSource) {
                        if (item.isSelect == false) {
                          isAll = false;
                          break;
                        }
                      }
                      isSelectAll = isAll;
                      setState(() {});
                    },
                    defaultImage: IconFont(
                      IconNames.weixuanzhong,
                      size: 32.w,
                      color: '#FFBFBFC1',
                    ),
                    selectedImage: IconFont(
                      IconNames.xuanzhong,
                      size: 32.w,
                      color: '#FF2C68FF',
                    ),
                  ),
                ),
              ),
              Expanded(
                  flex: 3,
                  child: Container(
                    alignment: Alignment.centerLeft,
                    // color: Colors.blue,
                    child: HaloPosLabel(
                      '${itemModel.fullname}',
                      textStyle: textStyle,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  )),
              Expanded(
                  flex: 3,
                  child: Container(
                    alignment: Alignment.centerLeft,
                    // color: Colors.green,
                    child: HaloPosLabel(
                      itemModel.validFullName ?? "",
                      textStyle: textStyle,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  )),
              Expanded(
                  flex: 3,
                  child: Container(
                    alignment: Alignment.centerLeft,
                    // color: Colors.green,
                    child: HaloPosLabel(
                      itemModel.valueTypeName ?? "",
                      textStyle: textStyle,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  )),
              Expanded(
                  flex: 4,
                  child: Container(
                    alignment: Alignment.centerLeft,
                    // color: Colors.yellow,
                    child: HaloPosLabel(
                      itemModel.rights ?? "",
                      textStyle: textStyle,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  )),
            ]));
  }

  ///网络请求
  Future<List<RightCartItemModel>?> onRequestData() async {
    return VipModel.getCouponsList(context, pageIndex, pageSize).then((value) {
      if (value != null && value.length < this.pageSize) {
        isHasMoreData = false;
      }
      return value;
    });
  }

  ///网络请求
  Future<LoadState> onLoadData() async {
    return onRequestData().then((result) {
      if (null == result) {
        return LoadState.LoadFail;
      }
      if (pageIndex <= 1) {
        dataSource.clear();
      }
      setState(() {
        dataSource.addAll(result);
      });
      if (result.length < this.pageSize) {
        setState(() {
          isHasMoreData = false;
        });
        return LoadState.AllFinish;
      }
      setState(() {
        isHasMoreData = true;
      });
      return LoadState.LoadFinish;
    }).catchError((msg, stack) {
      print(msg + stack);
      return LoadState.LoadFinish;
    });
  }

  ///加载更多
  Future<LoadState> onLoadMore() async {
    pageIndex++;
    List<RightCartItemModel>? result = await onRequestData();
    if (null == result) {
      return LoadState.LoadFail;
    }
    if (pageIndex <= 1) {
      dataSource.clear();
    }
    setState(() {
      dataSource.addAll(result);
    });

    if (result.length < this.pageSize) {
      setState(() {
        isHasMoreData = false;
      });
      return LoadState.AllFinish;
    }
    return LoadState.LoadFinish;
  }

  ///网络请求
  Future<bool> onRequestGiveRightsCard(List<String> rightsIds) async {
    return VipModel.sendCouponsList(context, {
      "vipIds": widget.vipIds,
      "cardIds": rightsIds,
      "comment": "pos发放优惠券",
      "sourceOperation": "pos发放优惠券"
    }).then((value) {
      bool isSuccess = value;
      if (isSuccess) {
        HaloToast.showSuccess(context, msg: "优惠券发放成功");
        widget.voidCallback();
      }
      return isSuccess;
    });
  }
}
