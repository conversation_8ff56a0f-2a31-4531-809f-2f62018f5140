import 'package:flutter/material.dart' hide Key;
import 'package:flutter/foundation.dart' as Foundation show Key;
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../bill/widget/ptype/base_goods_dialog.dart';
import '../../../iconfont/icon_font.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../vip/entity/vip_dto.dart';
import '../../../widgets/price_keyboard.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;

import '../../common/keyboard_hidden.dart';
import '../../common/style/app_colors.dart';
import '../../common/tool/decimal_scale_input_formatter.dart';

///搜索会员页面
class SearchVipDialog extends StatefulWidget {
  const SearchVipDialog({Foundation.Key? key}) : super(key: key);

  @override
  State<SearchVipDialog> createState() => _SearchVipDialogState();
}

class _SearchVipDialogState extends BaseGoodsDialogState<SearchVipDialog> {
  @override
  double get height => 300.h;

  @override
  String get title => "选择会员";

  @override
  double get width => 720.w;

  @override
  EdgeInsets get dialogPadding => EdgeInsets.only(left: 38.w, right: 42.w);

  final TextEditingController controller = TextEditingController();

  final KeyboardHiddenFocusNode searchFocusNode = KeyboardHiddenFocusNode(
    keyboardHidden: false,
  );

  @override
  Widget buildContent(BuildContext context) {
    return Padding(
      padding: dialogPadding,
      child: Column(
        children: [
          Expanded(child: Align(child: _buildPhone(context))),
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            margin: EdgeInsets.only(bottom: 20.w),
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              HaloButton(
                height: 48.w,
                text: "确定",
                onPressed: () {
                  _clearFocus();
                  _onSubmit();
                },
              ),
              SizedBox(width: 28.w),
              HaloButton(
                buttonType: HaloButtonType.outlinedButton,
                outLineWidth: 2.w,
                foregroundColor: AppColors.normalTextColor,
                borderColor: AppColors.btnBorderColor,
                height: 48.w,
                text: "取消",
                onPressed: () {
                  _clearFocus();
                  Navigator.pop(context);
                },
              ),
            ],
          ),

          // Padding(
          //   padding: EdgeInsets.only(bottom: 56.h),
          //   child: PriceKeyBoard(
          //     controller: controller,
          //     keyList: const [
          //       [Key.one, Key.two, Key.three],
          //       [Key.four, Key.five, Key.six],
          //       [Key.seven, Key.eight, Key.nine],
          //       [
          //         Key.back,
          //         Key.zero,
          //         Key("确定",
          //             type: KeyType.custom,
          //             textColor: Colors.white,
          //             background: Color(0xFF4679FC))
          //       ]
          //     ],
          //     isPhone: true,
          //     allowEmpty: true,
          //     numberChangeCallback: (str) => setState(() {}),
          //     customCallback: (key, str) => _onSubmit(),
          //   ),
          // )
        ],
      ),
    );
  }

  _clearFocus() {
    if (FocusScope.of(context).hasFocus) {
      FocusScope.of(context).unfocus();
    }
  }

  void _onSubmit() {
    if (StringUtil.isEmpty(controller.text)) {
      HaloToast.show(context, msg: "请输入手机号或会员姓名");
      return;
    }
    Navigator.pop(context, controller.text);
  }

  HaloContainer _buildPhone(BuildContext context) {
    return HaloContainer(
      padding: EdgeInsets.symmetric(horizontal: 22.w),
      height: 80.h,
      mainAxisSize: MainAxisSize.max,
      borderRadius: BorderRadius.circular(6.w),
      border: Border.all(color: const Color(0xFFCFCFCF), width: 2.w),
      children: [
        IconFont(IconNames.sousuo, size: 36.w),
        SizedBox(width: 12.w),
        Expanded(
          child: KeyboardHiddenTextField(
            focusNode: searchFocusNode,
            controller: controller,
            style: TextStyle(color: const Color(0xFF333333), fontSize: 26.sp),
            keyboardType: TextInputType.text,
            cleanTextWhenSearch: false, // 确保搜索时不清空文本
            onSubmitted: (_) => _onSubmit(),
            hint: "请输入手机号或姓名选择会员",
            onTapBefore: () => true,
          ),
        ),
      ],
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }
}

///搜索出来多个会员时，选择会员的弹窗
class SelectVipDialog extends StatefulWidget {
  final List<Vip> vipList;

  const SelectVipDialog({Foundation.Key? key, required this.vipList})
    : super(key: key);

  @override
  State<SelectVipDialog> createState() => _SelectVipDialogState();
}

class _SelectVipDialogState extends BaseGoodsDialogState<SelectVipDialog>
    with ListAndListTitleMixin {
  @override
  final double height = 600.h;

  @override
  final double width = 564.w;

  @override
  final String title = "选择会员";

  @override
  final EdgeInsets dialogPadding = EdgeInsets.only(left: 32.w, right: 30.w);

  ///表头各列标题
  @override
  final List<String> listTitles = ["手机号"];

  ///列表各列比例
  @override
  final List<int> listColumnFlex = [1];

  @override
  int get itemCount => widget.vipList.length;

  @override
  Widget buildListItem(BuildContext context, int index) {
    final Vip vip = widget.vipList[index];
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => Navigator.pop(context, vip),
          child: Container(
            width: double.infinity,
            height: 76.h,
            alignment: Alignment.centerLeft,
            padding: listPadding,
            child: Text(
              "${vip.name ?? ""}(${vip.phone ?? ""})",
              style: TextStyle(
                color: const Color(0xFF333333),
                fontSize: 22.sp,
                overflow: TextOverflow.ellipsis,
              ),
              maxLines: 1,
            ),
          ),
        ),
        Divider(height: 2.h, color: const Color(0xFFCFCFCF)),
      ],
    );
  }
}
