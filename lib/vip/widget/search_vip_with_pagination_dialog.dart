import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../../common/keyboard_hidden.dart' as local;
import '../../common/tool/sp_tool.dart';
import '../../iconfont/icon_font.dart';

import '../entity/page_response.dart';
import '../entity/vip_dto.dart';
import '../model/vip_model.dart';

/// 会员搜索分页弹窗 - 根据UI设计图精确实现
class SearchVipWithPaginationDialog extends StatefulWidget {
  const SearchVipWithPaginationDialog({Key? key}) : super(key: key);

  @override
  State<SearchVipWithPaginationDialog> createState() =>
      _SearchVipWithPaginationDialogUIState();
}

class _SearchVipWithPaginationDialogUIState
    extends State<SearchVipWithPaginationDialog> {
  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();

  // 会员列表
  List<Vip> _vipList = [];

  // 是否已经执行过搜索（用于区分"还没搜索"和"搜索了但没结果"）
  bool _hasSearched = false;

  // 分页相关
  int _currentPage = 1;
  final int _pageSize = 10;
  bool _hasMore = false;
  bool _isLoadingMore = false;

  // 选中的会员
  Vip? _selectedVip;

  @override
  void initState() {
    super.initState();
    // 监听搜索框文本变化，实时更新UI
    _searchController.addListener(() {
      setState(() {
        // 如果搜索框被清空，清除搜索结果
        if (_searchController.text.isEmpty && _hasSearched) {
          _vipList.clear();
          _selectedVip = null;
          _hasSearched = false;
        }
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 674.w,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8, // 限制最大高度为屏幕的80%
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.w),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildDivider(),
            _buildSearchArea(),
            Flexible(child: _buildContent()), // 使用Flexible包装内容区域
          ],
        ),
      ),
    );
  }

  /// 构建标题栏
  Widget _buildHeader() {
    return Container(
      width: 619.w,
      height: 50.w,
      margin: EdgeInsets.only(left: 24.w, top: 22.h, right: 31.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "选择会员",
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
              height: 33.h / 24.sp,
            ),
          ),
          GestureDetector(
            onTap: () {
              // 先隐藏键盘
              FocusScope.of(context).unfocus();
              // 然后关闭弹窗
              Navigator.pop(context);
            },
            child: Container(
              width: 30.w,
              height: 30.w,
              margin: EdgeInsets.only(top: 4.h),
              child: IconFont(IconNames.close, size: 30.w),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分隔线
  Widget _buildDivider() {
    return Container(
      width: 674.w,
      height: 1.h,
      margin: EdgeInsets.only(top: 15.h),
      color: const Color(0xFFE5E5E5),
    );
  }

  /// 构建搜索区域
  Widget _buildSearchArea() {
    return Container(
      width: 626.w,
      height: 70.h,
      margin: EdgeInsets.only(left: 24.w, top: 25.h, right: 24.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [_buildSearchInput(), _buildConfirmButton()],
      ),
    );
  }

  /// 构建搜索输入框
  Widget _buildSearchInput() {
    // 判断是否显示激活状态：搜索框有内容
    bool isActive = _searchController.text.isNotEmpty;

    return Container(
      width: 461.w,
      height: 70.h,
      padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 0.h),
      decoration: BoxDecoration(
        color: isActive ? Colors.white : const Color(0xFFF1F3F5),
        border:
            isActive
                ? Border.all(color: const Color(0xFF4679FC), width: 1.5.w)
                : Border.all(color: const Color(0xFFF1F3F5)),
        borderRadius: BorderRadius.circular(6.w),
      ),
      child: Row(
        children: [
          // 搜索图标
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.w),
            child: IconFont(IconNames.sousuo, color: "#67686A", size: 24.w),
          ),
          // 输入框
          Expanded(
            child: local.KeyboardHiddenTextField(
              controller: _searchController,
              hint: "输入手机号或姓名搜索",
              style: TextStyle(color: const Color(0xFF333333), fontSize: 24.sp),
              onSubmitted: _onSearch,
              onTapBefore: () => true,
              cleanTextWhenSearch: false, // 禁用搜索时清空文本
            ),
          ),
          // 清除按钮
          if (_searchController.text.isNotEmpty) _buildClearButton(),
        ],
      ),
    );
  }

  /// 构建高亮文本
  Widget _buildHighlightText(
    String text,
    String searchText,
    TextStyle baseStyle,
  ) {
    if (searchText.isEmpty || text.isEmpty) {
      return Text(text, style: baseStyle);
    }

    // 不区分大小写搜索
    String lowerText = text.toLowerCase();
    String lowerSearchText = searchText.toLowerCase();

    List<TextSpan> spans = [];
    int index = -1;

    // 判断是否为手机号（纯数字且长度大于等于7位）
    bool isPhoneNumber = RegExp(r'^\d{7,}$').hasMatch(text);

    if (isPhoneNumber) {
      // 手机号优先匹配后四位，如果没有则匹配全号码，最后匹配任意位置
      if (text.length >= 4 && text.endsWith(searchText)) {
        // 后四位匹配
        index = text.length - searchText.length;
      } else if (text == searchText) {
        // 全号码匹配
        index = 0;
      } else {
        // 如果后四位和全号码都不匹配，则匹配任意位置
        index = lowerText.indexOf(lowerSearchText);
      }
    } else {
      // 非手机号，匹配第一个出现的位置
      index = lowerText.indexOf(lowerSearchText);
    }

    if (index == -1) {
      // 没有找到匹配，返回原文本
      spans.add(TextSpan(text: text, style: baseStyle));
    } else {
      // 添加匹配前的文本
      if (index > 0) {
        spans.add(TextSpan(text: text.substring(0, index), style: baseStyle));
      }

      // 添加匹配的文本（蓝色高亮）
      spans.add(
        TextSpan(
          text: text.substring(index, index + searchText.length),
          style: baseStyle.copyWith(
            color: const Color(0xFF4679FC), // 蓝色高亮
          ),
        ),
      );

      // 添加匹配后的文本
      if (index + searchText.length < text.length) {
        spans.add(
          TextSpan(
            text: text.substring(index + searchText.length),
            style: baseStyle,
          ),
        );
      }
    }

    return RichText(text: TextSpan(children: spans));
  }

  /// 构建清除按钮
  Widget _buildClearButton() {
    return GestureDetector(
      onTap: _onClear,
      child: Container(
        width: 30.w,
        height: 30.w,
        margin: EdgeInsets.only(right: 20.w, top: 20.h, bottom: 20.h),
        child: Center(child: IconFont(IconNames.shanchu_2, size: 30.w)),
      ),
    );
  }

  /// 构建确定按钮
  Widget _buildConfirmButton() {
    return GestureDetector(
      onTap: _onConfirm,
      child: Container(
        width: 145.w,
        height: 70.h,
        decoration: BoxDecoration(
          color: const Color(0xFF4679FC),
          borderRadius: BorderRadius.circular(8.w),
        ),
        child: Center(
          child: Text(
            "确定",
            style: TextStyle(
              fontSize: 24.sp,
              color: Colors.white,
              height: 33.h / 24.sp,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    // 1. 搜索框为空：不展示列表
    if (_searchController.text.isEmpty) {
      return SizedBox(height: 74.h);
    }

    // 2. 搜索框不为空并且有数据：展示列表
    if (_vipList.isNotEmpty) {
      return _buildResultContent();
    }

    // 3. 搜索框有数据但还没执行搜索：显示空白
    if (!_hasSearched) {
      return SizedBox(height: 74.h);
    }

    // 4. 搜索框有数据且已执行搜索但无结果：展示未找到的UI和新增按钮
    return _buildNoResultContent();
  }

  /// 构建无结果内容
  Widget _buildNoResultContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: 48.h),
          // 无结果图标
          Image.asset('assets/images/icon_no_data.png'),
          SizedBox(height: 21.h),
          // 提示文字
          Text(
            "未找到手机号或姓名为【${_searchController.text}】的会员",
            style: TextStyle(
              fontSize: 24.sp,
              color: const Color(0xFF999999),
              height: 33.h / 24.sp,
            ),
          ),
          SizedBox(height: 27.h),
          // 新增按钮
          GestureDetector(
            onTap: _onAddVip,
            child: Container(
              width: 189.w,
              height: 70.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.w),
                border: Border.all(color: const Color(0xFF4679FC), width: 2.w),
              ),
              child: Center(
                child: Text(
                  "新增",
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: const Color(0xFF4679FC),
                    height: 33.h / 24.sp,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 63.h),
        ],
      ),
    );
  }

  /// 构建结果内容
  Widget _buildResultContent() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: 500.h, // 限制列表最大高度，让弹窗更紧凑
      ),
      child: Column(
        children: [
          SizedBox(height: 15.w),
          // 会员列表 - 使用Expanded让列表可以滚动
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(10.w),
                bottomRight: Radius.circular(10.w),
              ),
              child: NotificationListener<ScrollNotification>(
                onNotification: _onScrollNotification,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _vipList.length + (_hasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _vipList.length) {
                      return _buildLoadMoreItem();
                    }
                    return _buildVipItem(_vipList[index], index);
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建会员项
  Widget _buildVipItem(Vip vip, int index) {
    bool isSelected = _selectedVip?.id == vip.id;
    bool isLastItem = index == _vipList.length - 1 && !_hasMore;

    return GestureDetector(
      onTap: () => _onSelectVip(vip),
      child: Container(
        width: 674.w,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFEAF0FF) : Colors.white,
          borderRadius:
              isLastItem
                  ? BorderRadius.only(
                    bottomLeft: Radius.circular(10.w),
                    bottomRight: Radius.circular(10.w),
                  )
                  : null,
        ),
        child: Column(
          children: [
            // 会员信息行
            SizedBox(
              height: 80.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
                children: [
                  // 会员姓名
                  Container(
                    margin: EdgeInsets.only(left: 34.w),
                    child: _buildHighlightText(
                      vip.name ?? "",
                      _searchController.text,
                      TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF333333),
                        height: 33.h / 24.sp,
                      ),
                    ),
                  ),
                  // 会员手机号
                  Container(
                    margin: EdgeInsets.only(right: 34.w),
                    child: _buildHighlightText(
                      vip.phone ?? "",
                      _searchController.text,
                      TextStyle(
                        fontSize: 24.sp,
                        color: const Color(0xFF333333),
                        height: 33.h / 24.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 分割线（最后一项不显示）
            if (!isLastItem)
              Container(
                height: 1.h,
                margin: EdgeInsets.symmetric(horizontal: 34.w),
                color: const Color(0xFFebebeb),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建加载更多项
  Widget _buildLoadMoreItem() {
    return InkWell(
      onTap: _isLoadingMore ? null : _loadMore,
      child: Container(
        width: double.infinity, // 确保占满整个宽度
        height: 60.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10.w),
            bottomRight: Radius.circular(10.w),
          ),
        ),
        child: Center(
          child:
              _isLoadingMore
                  ? const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Color(0xFF4679FC),
                    ),
                  )
                  : Text(
                    "加载更多",
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
        ),
      ),
    );
  }

  // 事件处理方法
  void _onSearch(String value) {
    if (value.isNotEmpty) {
      _performSearch(value);
    }
  }

  void _onClear() {
    setState(() {
      _searchController.clear();
      _vipList.clear();
      _selectedVip = null;
      _hasSearched = false; // 重置搜索状态
    });
  }

  void _onConfirm() {
    String searchText = _searchController.text.trim();
    if (searchText.isNotEmpty) {
      _performSearch(searchText);
    } else if (_selectedVip != null) {
      Navigator.pop(context, _selectedVip);
    }
    // 如果搜索框为空且没有选中会员，不做任何操作
  }

  void _onSelectVip(Vip vip) {
    setState(() {
      _selectedVip = vip;
    });
    // 先隐藏键盘
    FocusScope.of(context).unfocus();
    // 直接返回选中的会员
    Navigator.pop(context, vip);
  }

  void _onAddVip() async {
    bool hasPermission = SpTool.getPermission().memberVipAdd ?? false;
    if (!hasPermission) {
      HaloToast.show(context, msg: "没有新增会员的权限！");
      return;
    }

    // 先隐藏键盘
    FocusScope.of(context).unfocus();
    // 关闭当前弹窗，并返回特殊标识和搜索内容
    Navigator.pop(context, {
      'action': 'add_vip',
      'searchText': _searchController.text,
    });
  }

  /// 执行搜索
  void _performSearch(String searchText) async {
    setState(() {
      _hasSearched = true; // 标记已执行搜索
      _currentPage = 1;
      _vipList.clear();
      _selectedVip = null;
    });

    try {
      PageResponse<Vip>? pageResponse = await VipModel.getVipByCodeNewPage(
        context,
        searchText,
        pageIndex: _currentPage,
        pageSize: _pageSize,
      );

      if (!mounted) return;

      setState(() {
        if (pageResponse != null && pageResponse.list.isNotEmpty) {
          _vipList = pageResponse.list;
          _hasMore = pageResponse.hasNextPage;

          if (_vipList.length == 1) {
            // 只有一个会员，直接选择
            Navigator.pop(context, _vipList.first);
          }
          // 多个会员会自动显示列表（通过_buildContent逻辑判断）
        }
        // 无结果会自动显示无结果UI（通过_buildContent逻辑判断）
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          // 搜索失败，保持_hasSearched=true，_vipList为空，会显示无结果UI
        });
        HaloToast.show(context, msg: "搜索失败，请重试");
      }
    }
  }

  /// 滚动监听
  bool _onScrollNotification(ScrollNotification notification) {
    // 当滚动到底部时自动加载更多
    if (notification is ScrollEndNotification) {
      final metrics = notification.metrics;
      if (metrics.pixels >= metrics.maxScrollExtent - 50 && // 距离底部50像素时触发
          _hasMore &&
          !_isLoadingMore) {
        _loadMore();
      }
    }
    return false;
  }

  /// 加载更多
  void _loadMore() async {
    if (_isLoadingMore || !_hasMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      PageResponse<Vip>? pageInfo = await VipModel.getVipByCodeNewPage(
        context,
        _searchController.text,
        pageIndex: _currentPage + 1,
        pageSize: _pageSize,
      );

      if (!mounted) return;

      setState(() {
        _isLoadingMore = false;
        if (pageInfo != null && pageInfo.list.isNotEmpty) {
          _vipList.addAll(pageInfo.list);
          _currentPage++;
          _hasMore = pageInfo.hasNextPage;
        } else {
          _hasMore = false;
        }
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
          _hasMore = false;
        });
      }
    }
  }
}
