import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/vip/widget/score_quick_reason.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../iconfont/icon_font.dart';
import '../../../vip/model/vip_model.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../common/tool/dialog_util.dart';

class VipScoreDialog extends StatefulWidget {
  final String vipId;
  final VoidCallback voidCallback;

  const VipScoreDialog({required this.vipId, required this.voidCallback})
    : super();

  @override
  _VipScoreDialogState createState() => _VipScoreDialogState();
}

class _VipScoreDialogState extends State<VipScoreDialog> {
  TextEditingController textGivingScore = TextEditingController();
  TextEditingController textMemo = TextEditingController();
  var maxLength = 5;

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      child: SizedBox(
        child: Material(
          borderRadius: BorderRadius.circular(10.w),
          child: HaloContainer(
            width: 720.w,
            height: 850.w,
            color: Colors.white,
            direction: Axis.vertical,
            borderRadius: BorderRadius.circular(10.w),
            children: [
              HaloContainer(
                padding: EdgeInsets.only(left: 19.w, right: 15),
                color: Colors.white24,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                height: 77.w,
                children: [
                  Text(
                    "积分调整",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 24.sp,
                      color: AppColorHelper(context).getTitleBoldTextColor(),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      color: Colors.transparent,
                      alignment: Alignment.centerRight,
                      width: 100,
                      height: 30,
                      child: IconFont(IconNames.close, size: 30),
                    ),
                  ),
                ],
              ),
              const Divider(color: AppColors.lineColor, height: 1),
              HaloContainer(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                padding: EdgeInsets.only(left: 45.w, top: 35.w),
                children: [
                  HaloPosLabel(
                    "数量",
                    textStyle: TextStyle(
                      fontSize: 30.sp,
                      color: ColorUtil.stringColor("#555555"),
                    ),
                  ),
                  HaloPosLabel(
                    "*",
                    textStyle: TextStyle(fontSize: 30.sp, color: Colors.red),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(left: 45.w, top: 30.w, right: 45.w),
                child: TextField(
                  controller: this.textGivingScore,
                  keyboardType: TextInputType.number,
                  //完成类型
                  textInputAction: TextInputAction.done,
                  //输入类型数字
                  inputFormatters: <TextInputFormatter>[
                    LengthLimitingTextInputFormatter(maxLength),
                    FilteringTextInputFormatter.allow(RegExp("[0-9-]")),
                  ],
                  onChanged: (value) {
                    maxLength = 5;
                    for (int i = 0; i < value.length; i++) {
                      //非0-9之外的数字 最大长度+1
                      if (value.codeUnitAt(i) < 48 ||
                          value.codeUnitAt(i) > 57) {
                        maxLength++;
                      }
                    }
                    setState(() {});
                  },
                  //只允许输⼊数字
                  maxLines: 1,
                  decoration: InputDecoration(
                    hintText: "请输入要加减的积分值（负数表示减少）",
                    contentPadding: EdgeInsets.only(left: 30.w),
                    focusedBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: Color(0xFFCFCFCF)),
                      borderRadius: BorderRadius.circular(6.w),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: const BorderSide(color: Color(0xFFCFCFCF)),
                      borderRadius: BorderRadius.circular(6.w),
                    ),
                  ),
                ),
              ),
              HaloContainer(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                padding: EdgeInsets.only(left: 45.w, top: 50.w),
                children: [
                  HaloPosLabel(
                    "备注",
                    textStyle: TextStyle(
                      fontSize: 30.sp,
                      color: ColorUtil.stringColor("#555555"),
                    ),
                  ),
                  HaloPosLabel(
                    "*",
                    textStyle: TextStyle(fontSize: 30.sp, color: Colors.red),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.only(left: 45.w, top: 30.w, right: 45.w),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: 220.w),
                  child: TextField(
                    controller: this.textMemo,
                    maxLines: 4,
                    keyboardType: TextInputType.text,
                    inputFormatters: <TextInputFormatter>[
                      LengthLimitingTextInputFormatter(190),
                    ],
                    //输入类型数字
                    textInputAction: TextInputAction.done,
                    //完成类型
                    decoration: InputDecoration(
                      isCollapsed: true,
                      hintText: "备注原因",
                      contentPadding: EdgeInsets.symmetric(
                        vertical: 10.w,
                        horizontal: 30.w,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xFFCFCFCF)),
                        borderRadius: BorderRadius.circular(6.w),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: const BorderSide(color: Color(0xFFCFCFCF)),
                        borderRadius: BorderRadius.circular(6.w),
                      ),
                    ),
                  ),
                ),
              ),
              HaloContainer(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.start,
                padding: EdgeInsets.only(left: 45.w),
                children: [
                  HaloButton(
                    foregroundColor: AppColors.accentColor,
                    fontSize: 25.sp,
                    height: 50.w,
                    buttonType: HaloButtonType.textButton,
                    text: "快捷录入",
                    borderColor: AppColors.accentColor,
                    onPressed: () {
                      DialogUtil.showAlertDialog(
                        context,
                        child: ScoreQuickReason(
                          callback: (reason) {
                            setState(() {
                              textMemo.text = reason;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ],
              ),
              HaloContainer(
                children: [
                  HaloButton(
                    foregroundColor: Colors.black,
                    width: 310.w,
                    height: 80.w,
                    buttonType: HaloButtonType.outlinedButton,
                    text: "取消",
                    outLineWidth: 2.w,
                    borderColor: ColorUtil.stringColor("#999999"),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  SizedBox(width: 20.w),
                  HaloButton(
                    width: 310.w,
                    height: 80.w,
                    buttonType: HaloButtonType.elevatedButton,
                    text: "确定",
                    outLineWidth: 2.w,
                    onPressed: () {
                      if (int.parse(textGivingScore.text) == 0) {
                        HaloToast.showError(context, msg: "请填写正确的积分");
                      }

                      Map parmas = {
                        "vipId": widget.vipId,
                        "sourceOperation": 5,
                        "memo": textMemo.text,
                        "vipAsserts": [
                          {
                            "typed": 0,
                            "memo": '积分',
                            "qty": textGivingScore.text,
                            "changeType": 13,
                          },
                        ],
                      };

                      VipModel.givingScore(context, parmas).then((value) {
                        if (value) {
                          HaloToast.showSuccess(context, msg: "积分调整成功");
                          Navigator.pop(context);
                          widget.voidCallback();
                        }
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
