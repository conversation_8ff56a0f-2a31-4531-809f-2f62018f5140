import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/bill/settlement/entity/atype_info_bean.dart';
import 'package:halo_pos/enum/payment_enum.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:haloui/widget/halo_dialog.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../../bill/entity/delete_bill_request.dart';
import '../../../bill/entity/pay_result_dto.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/settlement/settlement_mixin.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../cashierbox/entity/cash_box_payment_request_dto.dart';
import '../../../common/login/login_center.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/tool/UUID.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/payment.dart';
import '../../../common/tool/performance_capture_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../common/tool/tts_util.dart';
import '../../../iconfont/icon_font.dart';
import '../../../plugin/notify_voice_plugin.dart';
import '../../../print/tool/print_tool.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../../widgets/memo_dialog.dart';
import '../../../widgets/scanner_dialog.dart';
import '../../../widgets/selector/etype_selector.dart';
import '../../entity/get_vip_level_score_rights_card_response.dart';

mixin VipSettlementMixin<T extends BaseStatefulPage>
    on BaseStatefulPageState<T>, SettlementMixin<T> {
  @override
  bool get showChooseEtype => false;

  @override
  VipWithLevelAssertsRightsCardDTO? get vipInfo;

  ///会员升级/充值不能使用储值
  @override
  bool get enableStore => false;

  ///是否正在扫码支付
  bool isPaying = false;

  Map payInfo = {};

  ///备注
  TextEditingController get memoController;

  /// 生成草稿单据的方法，由具体实现类提供
  Future<bool> generateDraftBill(BuildContext context, String vchcode) async {
    return true;
  }

  @override
  Widget buildRightBody(BuildContext context) {
    return buildSettlementWidget();
  }

  @override
  String getActionBarTitle() => "收银机";

  @override
  Future<void> onInitState() async {
    return init();
  }

  @override
  void initEtype() {
    //会员使用的经手人和单据不同
  }

  @override
  Widget buildAppBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(80.h),
      child: AppBar(
        titleSpacing: 0.0,
        automaticallyImplyLeading: false,
        toolbarHeight: 80.h,
        backgroundColor: AppColorHelper(context).getAppBarColor(),
        title: SizedBox(
          width: double.infinity,
          height: 80.h,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Positioned(
                left: 0,
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: IconFont(
                      IconNames.ngp_left_back,
                      size: 32.w,
                      color: ColorUtil.color2String(
                        AppColorHelper(context).getTitleBoldTextColor(),
                      ),
                    ),
                  ),
                ),
              ),
              Text(
                getActionBarTitle(),
                style: TextStyle(
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  fontSize: ScreenUtil().setSp(widget.titleTextSize),
                ),
              ),
              Positioned(right: 0, child: _buildEtype()),
            ],
          ),
        ),
        centerTitle: false,
      ),
    );
  }

  ///构建右上角业务员
  Widget _buildEtype() {
    TextStyle style = TextStyle(
      fontSize: 26.sp,
      fontWeight: FontWeight.bold,
      color: AppColorHelper(context).getTitleBoldTextColor(),
    );
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap:
          () =>
              HaloDialog(
                context,
                child: EtypeSelector(
                  onItemClick: (buildContext, selectEtype) async {
                    setState(() {
                      etype = selectEtype;
                    });
                    Navigator.pop(context);
                  },
                ),
              ).show(),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 400.w,
          maxHeight: 80.h,
          minHeight: 80.h,
        ),
        padding: EdgeInsets.only(right: 10.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text("业务员:", style: style),
            Flexible(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                child: HaloPosLabel(
                  TextUtil.isEmpty(etype?.etypeName) ? "无" : etype!.etypeName!,
                  textStyle: style,
                ),
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: AppColorHelper(context).getTitleBoldTextColor(),
            ),
          ],
        ),
      ),
    );
  }

  @override
  int getLeftFlex() => 4;

  @override
  int getRightFlex() => 7;

  Widget buildNormalRow({
    required String title,
    required String content,
    double? textSize,
    Color titleColor = const Color(0xFF666666),
    Color? contentColor,
    EdgeInsets? padding,
    FontWeight fontWeight = FontWeight.normal,
  }) {
    padding ??= EdgeInsets.only(top: 30.h);
    contentColor ??= titleColor;
    textSize ??= 28.sp;
    return Container(
      padding: padding,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.only(right: 8.w),
            child: HaloPosLabel(
              title,
              textStyle: TextStyle(
                fontSize: textSize,
                color: titleColor,
                fontWeight: fontWeight,
              ),
            ),
          ),
          Expanded(
            child: HaloPosLabel(
              content,
              textAlign: TextAlign.end,
              textStyle: TextStyle(
                fontSize: textSize,
                color: contentColor,
                fontWeight: fontWeight,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void startPrint() {}

  @override
  void onTipsClick() {
    showDialog(
      context: context,
      builder: (context) => MemoInputDialog(controller: memoController),
    );
  }

  ///提交
  @override
  Future<void> submit() async {
    if (this.print) {
      startPrint();
    }
    if (mounted) {
      //弹出钱箱，记录
      await checkCash();
    }
  }

  @override
  bool checkNext() {
    if (!super.checkNext()) {
      return false;
    }
    return true;
  }

  ///检查是否有聚合支付，如果有，则先进行聚合支付
  bool checkPay() {
    var unionPayment = paymentList.firstWhereOrNull(
      (element) => element.enable && element.storePayway.paywayType == 2,
    );
    if (unionPayment != null) {
      if (unionPayment.total == "0") {
        // HaloToast.show(context, msg: "聚合支付金额不能为0，请重新输入！");
        return false;
      }
      return true;
    }
    return false;
  }

  // ///支付
  // void pay(BuildContext context, VoidCallback successCallBack,
  //     {VoidCallback? cancelCallBack,
  //       required String vchcode,
  //       String? scanOutNo}) async {
  //   ///没有聚合支付 直接回调成功
  //   if (checkPay()) {
  //     var unionPayment = paymentList.firstWhereOrNull(
  //             (element) => element.enable && element.storePayway.paywayType == 2);
  //
  //     if (!mounted) {
  //       isPaying = false;
  //       return;
  //     }
  //
  //     Map? params =
  //     await buildScanInfo(context, unionPayment!, scanOutNo);
  //
  //     if (!mounted) {
  //       isPaying = false;
  //       return;
  //     }
  //
  //     if (params == null) {
  //       isPaying = false;
  //       return;
  //     }
  //
  //     // 扫码成功后再生成草稿单据
  //     bool draftSuccess = await generateDraftBill(context, vchcode);
  //     if (!draftSuccess) {
  //       isPaying = false;
  //       return;
  //     }
  //
  //     // 添加vchcode到支付参数中
  //     params["vchcode"] = vchcode;
  //
  //     try {
  //       PayResult payResult = await BillModel.payOrder(context, params);
  //       if (!mounted) {
  //         isPaying = false;
  //         return;
  //       }
  //
  //       payResult.payInfo = params;
  //       PaymentUtil.processingPayResult(context, payResult,
  //           result: (PayResultType resultType) {
  //             if (resultType == PayResultType.SUCCEEDED && mounted) {
  //               isUnionPaySuccess = true;
  //               unionPayment.payOrderNo = payResult.orderNo;
  //               successCallBack();
  //             } else if (resultType == PayResultType.PENDING ||
  //                 resultType == PayResultType.WAITING_PASSWORD) {
  //               unionPayment.payOrderNo = payResult.orderNo;
  //             }
  //           }, onActionListener: (int index) {
  //             if (index == 0 && mounted||index == 2) {
  //               isUnionPaySuccess = true;
  //               if(index == 0){
  //                 memoController.text = "手工确认收款";
  //               }
  //               successCallBack();
  //             } else {
  //               if (cancelCallBack != null) {
  //                 cancelCallBack();
  //               }
  //             }
  //           });
  //     } catch (error, stackTrace) {
  //       if (!mounted) {
  //         isPaying = false;
  //         return;
  //       }
  //
  //       NotifyVoicePlugin.failure();
  //       PerformanceCaptureUtil.end(PerformanceTimeName.vipRecharge);
  //
  //       PaymentUtil.instance.showErrorInfo(context, "支付失败-未知错误",
  //           onActionListener: (int index) {
  //             if (index == 0 && mounted) {
  //               isUnionPaySuccess = true;
  //               memoController.text = "手工确认收款";
  //               successCallBack();
  //             } else {
  //               if (cancelCallBack != null) {
  //                 cancelCallBack();
  //               }
  //             }
  //           });
  //
  //       PerformanceCaptureUtil.end(PerformanceTimeName.vipRecharge);
  //       TTSUtil()
  //         ..initTTS()
  //         ..speak("扫码支付失败，请稍后再试");
  //     } finally {
  //       if (mounted) {
  //         isPaying = false;
  //       }
  //     }
  //   } else {
  //     successCallBack();
  //   }
  // }

  ///扫码，构建扫码支付信息
  Future<Map?> buildScanInfo(
    BuildContext context,
    AtypeInfoBean unionPayment,
    String? newOutNo, {
    VoidCallback? cancelCallBack,
  }) async {
    String? scanResult = await showScannerDialog(context);
    if (scanResult == null || scanResult.isEmpty || isPaying) {
      return Future(() => null);
    }
    isPaying = true;
    //去掉\n
    var reducedString = scanResult;
    if (scanResult.contains("\n")) {
      var indexOfSlashN = scanResult.indexOf("\n");
      reducedString = scanResult.substring(0, indexOfSlashN);
    }

    ///获取paymentList所有enable=true的total
    var enabledTotals =
        paymentList
            .where((element) => element.enable)
            .map((e) => e.total.toString())
            .toList();
    String billTotal =
        enabledTotals.isEmpty
            ? "0"
            : DecimalDisplayHelper.getTotalFixed(
              enabledTotals.reduce(
                (value, element) => MathUtil.add(value, element).toString(),
              ),
            );

    Map params = {
      "paywayId": unionPayment.storePayway.paywayId,
      "tradeAmount": unionPayment.total,
      "authCode": reducedString,
      "enablePollingQuery": false,
      "outNo": newOutNo,
      "billTotal": billTotal,
    };
    if (StringUtil.isNotEmpty(newOutNo)) {
      unionPayment.payOutNo = newOutNo;
    }
    return Future(() => params);
  }

  ///检查是否有现金支付,有的话需要上报现金
  Future<void> checkCash() async {
    var cashPayment = paymentList.firstWhereOrNull(
      (element) => element.enable && element.storePayway.paywayType == 0,
    );
    if (cashPayment != null) {
      //如果选择了现金支付，则打开钱箱
      String cashierId = SpTool.getCashierInfo().id ?? "";
      String otypeId = SpTool.getStoreInfo()!.otypeId ?? "";
      String etypeId = LoginCenter.getLoginUser().employeeId ?? "";
      double num =
          MathUtil.subtraction(
            cashPayment.total,
            pettyCash.toString(),
          ).toDouble();
      //3为销售存入，4为退货取出
      int paymentType = 2;
      if (StringUtil.isNotEmpty(cashierId) &&
          StringUtil.isNotEmpty(otypeId) &&
          StringUtil.isNotEmpty(etypeId)) {
        bool value = await BillModel.uploadCashBoxPayment(
          context,
          CashBoxPaymentRequestDto(
            num,
            paymentType,
            cashierId,
            etypeId,
            otypeId,
          ),
        );
        if (!value && mounted) {
          HaloToast.showError(context, msg: "新增钱箱记录失败");
        }
        if (context.mounted) {
          PrintTool.openCashBox(context);
        }
      }
    }
  }

  ///支付
  void pay(
    BuildContext context,
    VoidCallback successCallBack, {
    VoidCallback? cancelCallBack,
    required String vchcode,
    String? scanOutNo,
  }) async {
    ///没有聚合支付 直接回调成功
    if (checkPay()) {
      var unionPayment = paymentList.firstWhereOrNull(
        (element) => element.enable && element.storePayway.paywayType == 2,
      );

      if (!mounted) {
        isPaying = false;
        return;
      }

      Map? params = await buildScanInfo(context, unionPayment!, scanOutNo);

      if (!mounted) {
        isPaying = false;
        return;
      }

      if (params == null) {
        isPaying = false;
        return;
      }

      // 扫码成功后再生成草稿单据
      bool draftSuccess = await generateDraftBill(context, vchcode);
      if (!draftSuccess) {
        isPaying = false;
        return;
      }

      // 添加vchcode到支付参数中
      params["vchcode"] = vchcode;

      try {
        PayResult payResult = await BillModel.payOrder(context, params);
        if (!mounted) {
          isPaying = false;
          return;
        }

        payResult.payInfo = params;
        PaymentUtil.processingPayResult(
          context,
          payResult,
          result: (PayResultType resultType) {
            if (resultType == PayResultType.SUCCEEDED && mounted) {
              unionPayment.payOrderNo = payResult.orderNo;
              successCallBack();
            } else if (resultType == PayResultType.PENDING ||
                resultType == PayResultType.WAITING_PASSWORD) {
              unionPayment.payOrderNo = payResult.orderNo;
            }
          },
          onActionListener: (int index) {
            if (index == 0 && mounted || index == 2) {
              if (index == 0) {
                memoController.text = "手工确认收款";
              }
              successCallBack();
            } else {
              if (cancelCallBack != null) {
                cancelCallBack();
              }
            }
          },
        );
      } catch (error, stackTrace) {
        if (!mounted) {
          isPaying = false;
          return;
        }

        NotifyVoicePlugin.failure();

        PaymentUtil.instance.showErrorInfo(
          context,
          "支付失败-未知错误",
          onActionListener: (int index) {
            if (index == 0 && mounted) {
              memoController.text = "手工确认收款";
              successCallBack();
            } else {
              if (cancelCallBack != null) {
                cancelCallBack();
              }
            }
          },
        );

        TTSUtil()
          ..initTTS()
          ..speak("扫码支付失败，请稍后再试");
      } finally {
        if (mounted) {
          isPaying = false;
        }
      }
    } else {
      successCallBack();
    }
  }
}
