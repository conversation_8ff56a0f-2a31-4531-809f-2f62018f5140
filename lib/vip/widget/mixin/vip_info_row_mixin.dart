import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../common/standard.dart';
import '../../../iconfont/icon_font.dart';
import '../../../vip/entity/vip_tag.dart';
import 'package:halo_utils/utils/color_util.dart';

///会员新增/编辑/详情页中左边标题，右边内容的行控件
mixin VipInfoRowMixin {
  ///标题文字样式
  final TextStyle titleStyle = TextStyle(
    color: const Color(0xFF666666),
    fontSize: 30.sp,
    overflow: TextOverflow.ellipsis,
  );

  ///内容文本样式
  final TextStyle contentStyle = TextStyle(
    color: const Color(0xFF333333),
    fontSize: 30.sp,
    overflow: TextOverflow.ellipsis,
  );

  ///构建一行数据，左边标题，右边内容
  ///[title] 标题文字
  ///[content] 内容控件，填充满除开标题的空间
  ///[required] 是否必填，如果是，标题会有一个红点
  Widget buildRow({
    required String title,
    required Widget content,
    bool required = false,
  }) {
    return SizedBox(
      height: 100.h,
      child: Row(
          children: [buildTitle(title, required), Expanded(child: content)]),
    );
  }

  ///构建文字标题和内容
  Widget buildStringRow(String title, String content, {TextAlign? textAlign}) =>
      buildRow(
          title: title,
          content: buildStringContent(content, textAlign: textAlign));

  ///构建文字内容
  Widget buildStringContent(String content, {TextAlign? textAlign}) =>
      Text(content, textAlign: textAlign, style: contentStyle, maxLines: 1);

  ///构建标题
  Widget buildTitle(String title, bool required) {
    return SizedBox(
        width: 188.w,
        child: RichText(
          text: TextSpan(
              style: titleStyle,
              children: [TextSpan(text: title)].also((list) {
                if (required == true) {
                  list.add(TextSpan(
                      text: "*",
                      style:
                          titleStyle.copyWith(color: const Color(0xFFFF4141))));
                }
                list.add(TextSpan(text: "："));
              })),
        ));
  }

  ///构建分割线
  Widget buildDivider([bool isHorizontal = true]) {
    if (isHorizontal) {
      return Divider(height: 2.h, color: const Color(0xFFCFCFCF));
    }
    return VerticalDivider(width: 1.w, color: const Color(0xFFC6C6C6));
  }

  ///构建会员标签
  Widget buildTagItem(BuildContext context, VipTag tag,
      {ValueChanged<VipTag>? onDelete}) {
    return Container(
      constraints:
          BoxConstraints(maxHeight: 56.h, minHeight: 56.h, maxWidth: 250.w),
      decoration: BoxDecoration(
          color: tag.color?.let((color) => ColorUtil.stringColor(color)) ??
              const Color(0xFFFFF4EE),
          borderRadius: BorderRadius.circular(6.w)),
      padding:
          EdgeInsets.only(left: 12.w, right: (onDelete != null) ? 0 : 12.w),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Flexible(
            child: Text(
              tag.text ?? "",
              style: TextStyle(color: Colors.white, fontSize: 28.sp),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ].also((children) {
          if (onDelete != null) {
            children.add(
              GestureDetector(
                child: Padding(
                  padding: EdgeInsets.all(14.w),
                  child: IconFont(
                    IconNames.close,
                    size: 25.w,
                    color: "#F98200",
                  ),
                ),
                behavior: HitTestBehavior.opaque,
                onTap: () => onDelete(tag),
              ),
            );
          }
        }),
      ),
    );
  }
}
