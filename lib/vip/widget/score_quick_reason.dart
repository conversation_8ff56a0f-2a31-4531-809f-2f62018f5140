import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../../bill/bill/channel/bill_channel_scan.dart';
import '../../bill/bill/channel/widget/base_common_dialog.dart';
import '../entity/score_quick_reason_dto.dart';
import '../model/vip_model.dart';
typedef StringCallback = void Function(String);

class ScoreQuickReason extends StatefulWidget {
  final StringCallback callback;
  const ScoreQuickReason({Key? key, required this.callback}) : super(key: key);

  @override
  State<ScoreQuickReason> createState() => _ScoreQuickReasonState();
}

class _ScoreQuickReasonState extends BaseCommonDialogState<ScoreQuickReason> {
  List<ScoreQuickReasonDto> scoreQuickReasonList = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    VipModel.getScoreQuickReasonList(context).then((value) {
      setState(() {
        scoreQuickReasonList = value;
      });
    });
  }

  @override
  Widget buildContent(BuildContext context) {
    // TODO: implement buildContent
    ///列表
    return Container(
      color: Colors.white,
      width: double.infinity,
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: scoreQuickReasonList.length,
              itemBuilder: (context, index) {
                ScoreQuickReasonDto quickReasonDto = scoreQuickReasonList[index];
                return GestureDetector(
                  onTap: (){
                    onButtonClick(context,quickReasonDto.content!);
                  },
                  child: HaloContainer(
                    direction: Axis.vertical,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: HaloPosLabel("${quickReasonDto.content}",maxLines: 7,),
                      ),
                      _buildDivider(),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }


  void onButtonClick(BuildContext context,String reason) {
    // TODO: implement onCloseButtonClick
    widget.callback(reason);
    super.onCloseButtonClick(context);
  }

  ///分割线
  Widget _buildDivider() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 0.w),
      height: 1.w,
      color: const Color(0xFF979797),
    );
  }
  @override
  // TODO: implement height
  double get height => 550.w;

  @override
  // TODO: implement title
  String get title => "积分调整原因";

  @override
  // TODO: implement width
  double get width => 650.w;
}
