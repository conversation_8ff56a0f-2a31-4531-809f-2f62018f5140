import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/common/num_extension.dart';
import 'package:halo_pos/common/standard.dart';
import 'package:halo_pos/widgets/custom_table.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_button.dart';

import '../../bill/widget/ptype/base_goods_dialog.dart';
import '../../bill/widget/sale/select_gift.dart';
import '../../settting/widget/checkbox.dart';
import '../entity/recharge_strategy.dart';
import 'vip_recharge_page.dart';

//region 更改赠品弹窗
///更改赠品弹窗
class RechargeGoodsChangeDialog extends StatefulWidget {
  final List<Map<String, dynamic>> list;

  const RechargeGoodsChangeDialog(this.list, {Key? key}) : super(key: key);

  @override
  State<RechargeGoodsChangeDialog> createState() =>
      _RechargeGoodsChangeDialogState();
}

class _RechargeGoodsChangeDialogState
    extends BaseGoodsDialogState<RechargeGoodsChangeDialog> {
  @override
  final height = 600.h;

  @override
  final width = 574.w;

  @override
  final title = "选择赠品";

  @override
  final dialogPadding = EdgeInsets.symmetric(horizontal: 20.w);

  Map<String, dynamic>? _current;

  @override
  void initState() {
    super.initState();
    _current =
        widget.list.firstWhereOrNull((element) => element["isCurrent"] == true);
  }

  @override
  Widget buildContent(BuildContext context) {
    return Container(
      color: Colors.white,
      width: double.infinity,
      padding: EdgeInsets.only(top: 20.h),
      child: Column(
        children: [
          Expanded(
            child: Padding(
                padding: dialogPadding,
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    var item = widget.list[index];
                    return Row(
                      children: [
                        HaloPosCheckBox(
                          value: _current?.let((value) =>
                                  value["gearId"] == item["gearId"]) ??
                              false,
                          width: 36.w,
                          height: 36.w,
                          onChanged: (value) {
                            setState(() {
                              _current = item;
                            });
                          },
                        ),
                        SizedBox(width: 10.w),
                        Expanded(
                          child: Container(
                            height: 60.h,
                            alignment: Alignment.centerLeft,
                            padding: EdgeInsets.symmetric(horizontal: 22.w),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4.w),
                              border: Border.all(
                                  color: const Color(0xFFDADADA), width: 1.w),
                            ),
                            child: Text(
                              formatGoodsName(item["details"]),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style:
                                  rechargeTextStyle.copyWith(fontSize: 22.sp),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  itemCount: widget.list.length,
                )),
          ),
          divider,
          Container(
            height: 100.h,
            padding: dialogPadding,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildButton(context, "取消",
                    onTap: () => Navigator.pop(context)),
                SizedBox(width: 30.w),
                _buildButton(
                  context,
                  "确定",
                  textColor: Colors.white,
                  background: const Color(0xFF4679FC),
                  borderColor: null,
                  onTap: () => Navigator.pop(context, _current),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  ///底部按钮
  Widget _buildButton(
    BuildContext context,
    String content, {
    Color textColor = const Color(0xFF333333),
    Color background = Colors.white,
    Color? borderColor = const Color(0xFF999999),
    VoidCallback? onTap,
  }) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: 66.h,
          width: 196.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: background,
            borderRadius: BorderRadius.circular(4.w),
            border: borderColor?.let(
                (borderColor) => Border.all(color: borderColor, width: 2.w)),
          ),
          child: Text(content,
              style: TextStyle(color: textColor, fontSize: 26.sp)),
        ));
  }
}
//endregion 更改赠品弹窗

//region 选择商品弹窗
///选择商品弹窗
class RechargeGoodsSelectDialog extends StatefulWidget {
  final List<Detail> list;

  final bool isBuy;

  const RechargeGoodsSelectDialog(
      {Key? key, required this.list, this.isBuy = false})
      : super(key: key);

  @override
  State<RechargeGoodsSelectDialog> createState() =>
      _RechargeGoodsSelectDialogState();
}

class _RechargeGoodsSelectDialogState
    extends BaseGoodsDialogState<RechargeGoodsSelectDialog>
    with SingleTickerProviderStateMixin {
  @override
  @override
  double get height => 900.h;

  @override
  String get title => widget.isBuy ? "选择换购商品" : "选择赠品";

  @override
  double get width => 1600.w;

  late final TabController _controller;

  ///标题
  final List<Widget> tabTitleList = [];

  ///子页面
  final List<_SubPage> _tabPageList = [];

  ///子页面对应的赠品列表
  final List<List<GiftWrapper<Rule>>> allSubGiftList = [];

  num currentRemainCount = 0;

  StateSetter? remainStateSetter;

  @override
  void initState() {
    super.initState();
    for (var i = 0; i < widget.list.length; i++) {
      Detail detail = widget.list[i];
      List<GiftWrapper<Rule>> giftList = detail.rules
              ?.map((e) => GiftWrapper<Rule>(e, e.localSelectQty))
              .toList() ??
          [];
      //计算加载页面的剩余数量
      if (i == 0) {
        currentRemainCount =
            SelectTabPageState.getRemainCount(giftList, detail.valueQty ?? 0);
      }
      allSubGiftList.add(giftList);
      tabTitleList.add(Text("指定范围${i + 1}",
          style: TextStyle(fontSize: 24.sp, color: Colors.black)));
      _tabPageList.add(_SubPage(
          giftList: giftList,
          maxCount: detail.valueQty ?? 0,
          isBuy: widget.isBuy,
          onRemainCountChange: onRemainCountChange));
    }
    _controller = TabController(length: widget.list.length, vsync: this);
    //当页面切换，计算并更新剩余数量
    _controller.addListener(() {
      onRemainCountChange(SelectTabPageState.getRemainCount(
          allSubGiftList[_controller.index],
          widget.list[_controller.index].valueQty ?? 0));
    });
  }

  @override
  Widget buildContent(BuildContext context) {
    return Column(
      children: [
        if (_tabPageList.isEmpty) Expanded(child: Container()),
        if (_tabPageList.length > 1)
          Container(
            height: 60.h,
            alignment: Alignment.centerLeft,
            child: TabBar(
              tabs: tabTitleList,
              controller: _controller,
              isScrollable: true,
              indicatorSize: TabBarIndicatorSize.label,
              indicatorColor: const Color(0xFF4679FC),
            ),
          ),
        if (_tabPageList.isNotEmpty)
          Expanded(
              child: TabBarView(
            controller: _controller,
            children: _tabPageList,
          )),
        divider,
        Padding(
          padding: EdgeInsets.symmetric(
              vertical: 10.h, horizontal: dialogPadding.left),
          child: Row(
            children: [
              Expanded(child: StatefulBuilder(
                builder: (BuildContext context,
                    void Function(void Function()) setState) {
                  remainStateSetter = setState;
                  return RichText(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                            text: "当前指定范围内可选数量:",
                            style: TextStyle(
                                fontSize: 24.sp,
                                color: const Color(0xFF333333))),
                        TextSpan(
                          text: currentRemainCount.getIntWhenInteger.toString(),
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: 28.sp,
                              fontWeight: FontWeight.bold),
                        )
                      ],
                    ),
                  );
                },
              )),
              buildBottomButton(
                "取消",
                buttonType: HaloButtonType.outlinedButton,
                textColor: const Color(0xFF333333),
                borderColor: const Color(0xFF999999),
                color: Colors.white,
                onTap: () => Navigator.pop(context),
              ),
              buildBottomButton("确定", color: const Color(0xFF4679FC), margin: 0,
                  onTap: () {
                for (var list in allSubGiftList) {
                  for (var item in list) {
                    item.gift.localSelectQty = item.count;
                  }
                }
                Navigator.pop(context);
              }),
            ],
          ),
        )
      ],
    );
  }

  void onRemainCountChange(num count) {
    remainStateSetter?.call(() => currentRemainCount = count);
  }
}

///选择商品弹窗子页面
class _SubPage extends StatefulWidget {
  final num maxCount;
  final List<GiftWrapper<Rule>> giftList;
  final bool isBuy;
  final ValueChanged<num> onRemainCountChange;

  const _SubPage(
      {required this.giftList,
      required this.maxCount,
      required this.isBuy,
      required this.onRemainCountChange});

  @override
  State<_SubPage> createState() => _SubPageState();
}

class _SubPageState extends SelectTabPageState<Rule, _SubPage> {
  @override
  List<GiftWrapper<Rule>> initGiftList() => widget.giftList;

  @override
  num get maxCount => widget.maxCount;

  final TextStyle cellTextStyle = TextStyle(
    fontSize: 24.sp,
    color: const Color(0xFF4e4e4e),
    overflow: TextOverflow.ellipsis,
  );

  @override
  void initState() {
    super.initState();
    remainCount = SelectTabPageState.getRemainCount(allGiftList, maxCount);
  }

  @override
  Widget build(BuildContext context) {
    return buildList();
  }

  @override
  Widget buildList() {
    Map<int, num> columnConfig;
    if (widget.isBuy) {
      columnConfig = _buyColumnConfig;
    } else {
      columnConfig = _presentationColumnConfig;
    }
    return CustomColumnTableByList<GiftWrapper<Rule>>(
      columnConfig: columnConfig,
      columnTitle: _columnTitle,
      data: showGiftList,
      scrollable: true,
      titleRowDecoration: const BoxDecoration(color: Color(0xFFf3f4f7)),
      divider: const Divider(height: 1, color: Color(0xFFe1e1e1)),
      columnTitleBuilder: buildTitleCell,
      cellBuilder: buildCell,
    );
  }

  ///构建标题单元格
  Widget buildTitleCell(String title, int columnType) {
    Widget titleColumn = Text(
      title,
      maxLines: 1,
      style: TextStyle(
          color: const Color(0xFF333333),
          fontSize: 26.sp,
          fontWeight: FontWeight.bold,
          overflow: TextOverflow.ellipsis),
    );
    titleColumn = Container(
      height: 80.h,
      padding: EdgeInsets.symmetric(horizontal: 6.w),
      alignment: Alignment.center,
      child: titleColumn,
    );
    return titleColumn;
  }

  ///构建单元格
  Widget buildCell(GiftWrapper<Rule> item, int columnType) {
    Widget? cell;
    switch (columnType) {
      case _columnTypeQty:
        cell = buildCountStepper(item);
        break;
      case _columnTypeName:
        cell = buildContentText(item.gift.ptypeName ?? "");
        break;
      case _columnTypeCode:
        cell = buildContentText(item.gift.usercode ?? "");
        break;
      case _columnTypeProperty:
        cell = buildContentText(item.gift.propertyName ?? "");
        break;
      case _columnTypeUnit:
        cell = buildContentText(item.gift.unitName ?? "");
        break;
      case _columnTypeRetailPrice:
        cell = buildContentText(item.gift.retailPrice?.toString() ?? "");
        break;
      case _columnTypePrice:
        num? exchangePrice = item.gift.exchangePrice;
        if (exchangePrice != null && exchangePrice <= 0) {
          exchangePrice = null;
        }
        cell = buildContentText(exchangePrice?.toString() ?? "");
        break;
      case _columnTypeDiscount:
        num? exchangeDiscount = item.gift.exchangeDiscount;
        if (exchangeDiscount != null && exchangeDiscount <= 0) {
          exchangeDiscount = null;
        }
        cell = buildContentText(exchangeDiscount?.toString() ?? "");
        break;
    }
    return Container(height: 80.h, alignment: Alignment.center, child: cell);
  }

  Widget buildContentText(String text) {
    return Text(text, style: cellTextStyle, maxLines: 1);
  }

  @override
  Widget buildItem(BuildContext context, int index) => Container();

  @override
  void onGiftSelect() {
    widget.onRemainCountChange.call(remainCount);
  }
}

//选择商品弹窗列类型
///数量
const int _columnTypeQty = 0;

///名称
const int _columnTypeName = 1;

///编号
const int _columnTypeCode = 2;

///属性
const int _columnTypeProperty = 3;

///单位
const int _columnTypeUnit = 4;

///零售价
const int _columnTypeRetailPrice = 5;

///换购价
const int _columnTypePrice = 6;

///换购折扣
const int _columnTypeDiscount = 7;

///选择商品弹窗列标题
const Map<int, String> _columnTitle = {
  _columnTypeQty: "数量",
  _columnTypeName: "商品名称",
  _columnTypeCode: "商品编号",
  _columnTypeProperty: "属性组合",
  _columnTypeUnit: "单位",
  _columnTypeRetailPrice: "零售价",
  _columnTypePrice: "换购价",
  _columnTypeDiscount: "换购折扣",
};

///选择商品列配置
const Map<int, num> _presentationColumnConfig = {
  _columnTypeQty: 1,
  _columnTypeName: 2,
  _columnTypeCode: 1,
  _columnTypeProperty: 1,
  _columnTypeUnit: 1,
};

///换购列配置
const Map<int, num> _buyColumnConfig = {
  _columnTypeQty: 2,
  _columnTypeName: 3,
  _columnTypeCode: 1,
  _columnTypeProperty: 1,
  _columnTypeUnit: 1,
  _columnTypeRetailPrice: 1,
  _columnTypePrice: 1,
  _columnTypeDiscount: 1,
};

//endregion 选择商品弹窗
