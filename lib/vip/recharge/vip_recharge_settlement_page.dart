import 'package:flutter/material.dart';
import 'package:halo_pos/bill/tool/bill_tool.dart';
import 'package:halo_pos/bill/tool/promotion/price.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/ptype/ptype_prop_dto.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/settlement/entity/atype_info_bean.dart';
import '../../../bill/tool/sale_event_buds.dart';
import '../../../bill/widget/mixin/select_vip_mixin.dart';
import '../../../common/login/login_center.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_post_state.dart';
import '../../../enum/bill_type.dart';
import '../../../login/entity/store/store_info.dart';
import '../../../print/tool/print_tool.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../vip/entity/recharge_strategy.dart';
import '../../../vip/model/vip_recharge_model.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/dotted_line.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../bill/entity/delete_bill_request.dart';
import '../../bill/entity/payment_dto.dart';
import '../../bill/model/ptype_model.dart';
import '../../enum/payment_enum.dart';
import '../../bill/settlement/settlement_mixin.dart';
import '../../common/tool/UUID.dart';
import '../../common/tool/performance_capture_util.dart';
import '../../login/model/login_user_model.dart';
import '../../common/tool/performance_capture_util.dart';
import '../widget/mixin/vip_settlement_mixin.dart';

///会员充值结算页面
class VipRechargeSettlementPage extends BaseStatefulPage {
  ///会员信息
  final VipWithLevelAssertsRightsCardDTO vipInfo;

  ///充值策略
  final Gear? strategy;

  ///充值金额
  final int rechargeMoney;

  ///应付金额
  final num shouldPayMoney;

  ///赠送金额
  final int presentationMoney;

  ///备注
  final TextEditingController memoController;

  ///赠送的商品
  final List<Detail>? goods;

  ///赠送的积分
  final Detail? score;

  ///赠送的优惠券
  final Detail? coupon;

  ///是否是换购
  final bool isExchange;

  const VipRechargeSettlementPage({
    Key? key,
    required this.vipInfo,
    required this.rechargeMoney,
    required this.shouldPayMoney,
    required this.presentationMoney,
    required this.memoController,
    this.isExchange = false,
    this.strategy,
    this.goods,
    this.score,
    this.coupon,
  }) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _VipRechargeSettlementPageState();
}

class _VipRechargeSettlementPageState
    extends BaseStatefulPageState<VipRechargeSettlementPage>
    with SelectVipMixin, SettlementMixin, VipSettlementMixin {
  @override
  TextEditingController get memoController => widget.memoController;

  ///出库单据
  GoodsBillDto? _goodsBillDto;

  @override
  num get moneyReceived => widget.shouldPayMoney;

  int get presentationMoney => widget.presentationMoney;

  @override
  VipWithLevelAssertsRightsCardDTO get vipInfo => widget.vipInfo;

  LoginUserModel? loginUser;

  StoreInfo? storeInfo;

  ///充值结算不使用组合支付
  @override
  bool get combinationPay => false;

  @override
  bool get showCombinationPay => false;

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container(
      color: const Color(0xFFF0F0F0),
      padding: EdgeInsets.all(14.w),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.h)),
        ),
        padding: EdgeInsets.only(left: 38.w, right: 30.w),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(top: 30.h, bottom: 15.h),
              child: Row(
                children: [
                  buildAvatar(context),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(left: 16.w),
                      child: buildVipInfo(context, enableClearVip: false),
                    ),
                  ),
                ],
              ),
            ),
            Divider(height: 2.h, color: const Color(0xFFD5D5D5)),
            buildNormalRow(
              title: "充值金额",
              content: "￥${widget.rechargeMoney}",
              padding: EdgeInsets.only(top: 15.h),
            ),
            buildNormalRow(title: "赠送金额", content: "￥$presentationMoney"),
            buildNormalRow(
              title: "共计储值",
              content: "￥${widget.rechargeMoney + presentationMoney}",
            ),
            buildNormalRow(
              title: "充值后余额",
              content:
                  "￥${MathUtil.addDec(widget.rechargeMoney + presentationMoney, vipInfo.asserts?.totalMoney ?? 0)}",
            ),
            Expanded(child: Container()),
            DottedLine(dashPattern: [8.w, 8.w], color: const Color(0xFFA5A5A5)),
            Container(
              padding: EdgeInsets.only(top: 24.h, bottom: 24.h),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.only(right: 8.w),
                    child: Text(
                      "应收金额",
                      style: TextStyle(
                        fontSize: 30.sp,
                        color: const Color(0xFF333333),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: HaloPosLabel(
                      "￥$moneyReceived",
                      textAlign: TextAlign.end,
                      textStyle: TextStyle(
                        fontSize: 30.sp,
                        color: const Color(0xFF333333),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Future<void> init() async {
    await super.init();
    loginUser = LoginCenter.getLoginUser();
    storeInfo = SpTool.getStoreInfo();
    _goodsBillDto = await _getGoodsBill();
  }

  Future<GoodsBillDto?> _getGoodsBill() async {
    if (widget.goods?.isNotEmpty != true) {
      return null;
    }
    List<GoodsDetailDto> goodsList =
        widget.goods
            ?.expand<GoodsDetailDto>(
              (detail) =>
                  detail.rules
                      ?.where((e) {
                        if (detail.giveMode == 1) return e.localSelectQty > 0;
                        return (e.valueQty ?? 0) > 0;
                      })
                      .map((e) => buildGoods(e, detail))
                      .toList() ??
                  [],
            )
            .toList() ??
        [];
    if (goodsList.isEmpty) return null;

    ///拉取一张新单据
    GoodsBillDto? goodsBillDto = await BillModel.getGoodsBill(
      context,
      "", //vchcode
      BillTypeData[BillType.SaleBill], //vchtype
      BillBusinessTypeString[BillBusinessType.SaleNormal]!, //  WholeSaleOffline
    );
    if (goodsBillDto == null || goodsBillDto.vchcode == null) {
      if (mounted) {
        HaloToast.show(context, msg: "获取单据失败");
      }
      return null;
    }
    //拉去好的单据填入基本信息
    goodsBillDto.createEtypeId = loginUser?.employeeId;
    goodsBillDto.vipCardId = vipInfo.vip?.id;
    goodsBillDto.createEfullname = loginUser?.etypeName;
    goodsBillDto.createUserCode = loginUser?.userCode;
    goodsBillDto.ktypeId = storeInfo?.ktypeId;
    goodsBillDto.kfullname = storeInfo?.ktypeName;
    goodsBillDto.btypeId = storeInfo?.btypeId;
    goodsBillDto.bfullname = storeInfo?.btypeName;
    goodsBillDto.otypeId = storeInfo?.otypeId;
    goodsBillDto.ofullname = storeInfo?.otypeFullname;
    goodsBillDto.etypeId = etype?.etypeId ?? "";
    goodsBillDto.efullname = etype?.etypeName ?? "";
    //这一坨不知道干嘛的
    goodsBillDto.freightAtypeName = "";
    goodsBillDto.freightBillNo = "";
    goodsBillDto.freightBtypeId = "0";
    goodsBillDto.freightBtypeName = "";
    goodsBillDto.freightaTypeTotal = "0";
    goodsBillDto.shareType = 0;
    goodsBillDto.dfullname = "";
    goodsBillDto.billType = "goodsBill";
    goodsBillDto.postState =
        BillPostStateString[BillPostState.PROCESS_COMPLETED];
    goodsBillDto.confirm = true; //避免各种奇奇怪怪的单据异常导致需要再次确认
    goodsBillDto.outDetail = goodsList;
    //汇总单据
    BillTool.reCalcStatistic(goodsBillDto, billType: BillType.SaleBill);
    return goodsBillDto;
  }

  ///将充值活动中的商品转换为单据中的商品
  GoodsDetailDto buildGoods(Rule rule, Detail detail) {
    final goods =
        GoodsDetailDto()
          ..prop = transformPtypeProp(rule)
          ..kfullname = storeInfo?.ktypeName
          ..ktypeId = storeInfo?.ktypeId
          ..pFullName = rule.ptypeName ?? ""
          ..ptypeId = rule.ptypeId ?? ""
          ..skuId = rule.skuId ?? ""
          ..unitId = rule.unitId ?? ""
          ..unitQty = (rule.valueQty ?? 1)
          ..unitName = rule.unitName ?? ""
          ..unitRate = (rule.unitRate ?? 1)
          ..taxRate = (rule.taxRate ?? 0)
          ..currencyPrice = (rule.retailPrice ?? 0);
    if (detail.giveMode == 1) {
      goods.unitQty = rule.localSelectQty;
    }
    //换购价格
    if (rule.giveType == 1) {
      goods.discountPrice = rule.exchangePrice ?? 0;
      GoodsQtyUtil.onQtyChange(goods, goods.unitQty, keepDiscount: false);
    }
    //换购折扣
    else if (rule.giveType == 2) {
      goods.discount = rule.exchangeDiscount ?? 0;
      GoodsQtyUtil.onQtyChange(goods, goods.unitQty, keepDiscount: true);
    }
    //赠送
    else {
      goods.gift = true;
      goods.currencyPrice = 0;
      GoodsQtyUtil.onQtyChange(goods, goods.unitQty);
    }
    TaxUtil.calculateDisedPriceAndTotal(goods);
    return goods;
  }

  List<PtypePropDto> transformPtypeProp(Rule rule) {
    List<PtypePropDto> prop = [];
    if (!TextUtil.isEmpty(rule.propValueId1) && rule.propValueId1 != "0") {
      prop.add(
        PtypePropDto()
          ..propvalueId = rule.propValueId1
          ..propvalueName = rule.propValueName1,
      );
    }
    if (!TextUtil.isEmpty(rule.propValueId2) && rule.propValueId2 != "0") {
      prop.add(
        PtypePropDto()
          ..propvalueId = rule.propValueId2
          ..propvalueName = rule.propValueName2,
      );
    }
    if (!TextUtil.isEmpty(rule.propValueId3) && rule.propValueId3 != "0") {
      prop.add(
        PtypePropDto()
          ..propvalueId = rule.propValueId3
          ..propvalueName = rule.propValueName3,
      );
    }
    if (!TextUtil.isEmpty(rule.propValueId4) && rule.propValueId4 != "0") {
      prop.add(
        PtypePropDto()
          ..propvalueId = rule.propValueId4
          ..propvalueName = rule.propValueName4,
      );
    }
    if (!TextUtil.isEmpty(rule.propValueId5) && rule.propValueId5 != "0") {
      prop.add(
        PtypePropDto()
          ..propvalueId = rule.propValueId5
          ..propvalueName = rule.propValueName5,
      );
    }
    if (!TextUtil.isEmpty(rule.propValueId6) && rule.propValueId6 != "0") {
      prop.add(
        PtypePropDto()
          ..propvalueId = rule.propValueId6
          ..propvalueName = rule.propValueName6,
      );
    }
    return prop;
  }

  ///提交
  @override
  Future<void> submit() async {
    List<AtypeInfoBean> paymentList = getEnablePaymentSubtractPettyCash(
      removeZero: true,
    );

    ///验证支付方式有效性（非储值的支付方式 必须绑定现金科目）
    if (!BillModel.checkPaymentList(context, paymentList)) {
      return;
    }

    String scanOutNo = DateTime.now().microsecondsSinceEpoch.toString();
    String vchcode = UUID.getVchcode();

    if (!mounted) return;

    ///检查充值权限
    if (!(SpTool.getPermission().shopsaleRechargeView ?? false)) {
      HaloToast.showError(context, msg: "没有会员充值权限");
      return;
    }

    ///支付
    StoreInfo storeInfo = SpTool.getStoreInfo()!;
    //如果之前拉取单据失败，则重新拉
    _goodsBillDto ??= await _getGoodsBill();
    _goodsBillDto?.etypeId = etype?.etypeId;
    _goodsBillDto?.efullname = etype?.etypeName;
    String summary =
        "顾客[${vipInfo.vip?.phone ?? ""}]的会员卡在[${storeInfo.fullname}]充值${widget.rechargeMoney}元";
    if (widget.isExchange) {
      summary = "$summary;充值换购";
    }
    String memo = widget.memoController.text;
    if (memo.length > 200) {
      memo = memo.substring(0, 200);
    }
    _goodsBillDto?.memo = memo;
    _goodsBillDto?.summary = summary;

    _goodsBillDto?.date = DateUtil.formatDate(DateTime.now());
    _goodsBillDto?.storeManagerId = storeInfo.ownerId ?? "";

    paymentList = getEnablePaymentSubtractPettyCash(removeZero: true);

    //拆分支付方式
    _setGoodsBillPayment(paymentList, _goodsBillDto);
    if (!mounted) return;

    PerformanceCaptureUtil.start(PerformanceTimeName.vipRecharge);

    // 使用新的统一聚合支付处理
    await VipRechargeModel.rechargeWithAggregatePayment(
      context,
      vchcode: vchcode,
      vipId: widget.vipInfo.vip?.id ?? "",
      vipLevelId: widget.vipInfo.level?.levelId ?? "",
      rechargeId: widget.strategy?.rechargeId ?? "",
      rechargeMoney: widget.rechargeMoney,
      payment: paymentList,
      ktypeId: storeInfo.ktypeId ?? "",
      otypeId: storeInfo.otypeId ?? "",
      btypeId: storeInfo.btypeId ?? "",
      memo: memo,
      summary: summary,
      createEtypeId: loginUser?.employeeId ?? "",
      cashierId: SpTool.getCashierInfo().id ?? "",
      giveMoney: presentationMoney,
      score: widget.score?.valueQty?.toInt(),
      cardId: widget.coupon?.rules?.first.cardTemplateId,
      cardQty: widget.coupon?.valueQty?.toInt(),
      goodsBill: _goodsBillDto,
      etypeId: etype?.etypeId,
      onSuccess: (PayResultType resultType) async {
        PerformanceCaptureUtil.end(PerformanceTimeName.vipRecharge);
        if (resultType == PayResultType.SUCCEEDED && mounted) {
          PtypeModel.changePtypeStockQtyByGoodsBill(
            outDetail: _goodsBillDto?.outDetail ?? [],
          );
          SaleEventBus.getInstance().fire(SaleEventBus.updateVip);
          await super.submit();
          if (mounted) {
            Navigator.pop(context, true);
          }
        }
      },
      onAction: (int index) {
        if (index == 0 && mounted) {
          // 手工确认收款
          PerformanceCaptureUtil.end(PerformanceTimeName.vipRecharge);
          PtypeModel.changePtypeStockQtyByGoodsBill(
            outDetail: _goodsBillDto?.outDetail ?? [],
          );
          SaleEventBus.getInstance().fire(SaleEventBus.updateVip);
          super.submit().then((_) {
            if (mounted) {
              Navigator.pop(context, true);
            }
          });
        } else {
          // 取消支付，删除草稿单据
          DeleteBillRequest deleteBillRequest = DeleteBillRequest();
          deleteBillRequest.vchcode = vchcode;
          deleteBillRequest.vchtype = "Receiving";
          deleteBillRequest.businessType = "VipValue";
          deleteBillRequest.billPostState = 0;
          deleteBillRequest.accountBill = true;
          BillModel.deleteBill(context, deleteBillRequest).then((value) {
            if (value?.success ?? false) {
            } else {
              if (context.mounted) {
                HaloToast.show(context, msg: value?.errorDetail?.first.message);
              }
            }
          });
        }
      },
    );
  }

  ///将支付方式拆分成充值和出库单的
  void _setGoodsBillPayment(
    List<AtypeInfoBean> paymentList,
    GoodsBillDto? goodsBill,
  ) {
    if (goodsBill == null ||
        paymentList.isEmpty ||
        (num.tryParse(goodsBill.currencyBillTotal) ?? 0) <= 0)
      return;
    final payment = paymentList.first;
    Map atype = BillTool.getAtypeMessage(goodsBill, BillType.SaleBill);
    goodsBill.payment = [
      PaymentDto()
        ..afullnameCation = atype["afullnameCation"]
        ..atypeTotalCation = atype["atypeTotalCation"]
        ..paywayFullname = payment.storePayway.paywayName
        ..atypeId = payment.storePayway.atypeId
        ..atypeFullName = payment.storePayway.atypeFullname
        ..paywayId = payment.storePayway.paywayId
        ..paywayType = payment.storePayway.paywayType
        ..currencyAtypeTotal = goodsBill.currencyBillTotal
        ..posCurrencyAtypeTotal = goodsBill.currencyBillTotal,
    ];
    payment.total =
        MathUtil.subtraction(
          payment.total,
          goodsBill.currencyBillTotal,
        ).toString();
  }

  @override
  void startPrint() {
    if (mounted) {
      PrintTool.printRecharge(
        context,
        vipName: vipInfo.vip?.name ?? "",
        phone: vipInfo.vip?.phone ?? "",
        rechargeMoney: widget.rechargeMoney.toString(),
        giveMoney: presentationMoney.toString(),
        beforeMoney: vipInfo.asserts?.totalMoney.toString() ?? "",
        afterMoney:
            MathUtil.addDec(
              widget.rechargeMoney + presentationMoney,
              vipInfo.asserts?.totalMoney ?? 0,
            ).toString(),
        payMoney: moneyReceived.toString(),
        atypeNames: paymentList
            .where(
              (element) =>
                  element.enable && StringUtil.isNotZeroOrEmpty(element.total),
            )
            .map((e) => e.storePayway.paywayName)
            .join("、"),
      );
    }
  }

  @override
  bool checkVipPwd() {
    //会员储值需要验证是否设置了密码
    if (!checkVipPwdSet()) {
      return false;
    }
    return true;
  }

  @override
  Future<bool> generateDraftBill(BuildContext context, String vchcode) async {
    List<AtypeInfoBean> paymentList = getEnablePaymentSubtractPettyCash(
      removeZero: true,
    );
    return VipRechargeModel.submitRechargeBill(
      context,
      vipId: widget.vipInfo.vip?.id ?? "",
      vipLevelId: widget.vipInfo.level?.levelId ?? "",
      rechargeId: widget.strategy?.rechargeId ?? "",
      rechargeMoney: widget.rechargeMoney,
      payment: paymentList,
      ktypeId: storeInfo?.ktypeId ?? "",
      otypeId: storeInfo?.otypeId ?? "",
      btypeId: storeInfo?.btypeId ?? "",
      etypeId: etype?.etypeId,
      memo: "会员充值草稿单",
      createEtypeId: loginUser?.employeeId ?? "",
      vchcode: vchcode,
      summary: '会员充值草稿单',
    );
  }
}
