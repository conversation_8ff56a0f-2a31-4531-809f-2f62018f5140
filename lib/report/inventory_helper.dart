import 'package:get/get.dart';

import '../../../bill/entity/ktype_list_dto.dart';

import 'entity/stock_statistics_dto.dart';

///需要对PC的数据做处理
class InventoryHelper {
  static List<StockStatisticsDto> reconsitutionDataList(
      List<StockStatisticsDto> list, String ktypeId) {
    List<StockStatisticsDto> resultList = [];
    list.forEach((stockStatistics) {
        StockStatisticsDto newObj =
            StockStatisticsDto.fromMap(stockStatistics.toJson());
        newObj.ktypeId = ktypeId;
        newObj.stockQty = stockStatistics.beforeData!["${ktypeId}_inventoryQty"];
        resultList.add(newObj);
    });
    return resultList;
  }

  static List<StockStatisticsDto> reconsitutionDataListForKtype(
      StockStatisticsDto statisticsDto,List<KtypeItemDto>ktypeList) {
    List<StockStatisticsDto> resultList = [];
    statisticsDto.beforeData!.keys.forEach((element) {
      if (element.contains("_inventoryQty")) {
        StockStatisticsDto newObj = StockStatisticsDto();
        String ktypeId = element.split("_")[0];
        newObj.ktypeId = ktypeId;
        KtypeItemDto? ktypeItemDto =ktypeList.firstWhereOrNull((element) => element.id! == ktypeId);
        if (ktypeItemDto != null) {
          newObj.kFullname = ktypeItemDto.fullname;
        }else{
          return;
        }
        newObj.stockQty = statisticsDto.beforeData!["${ktypeId}_inventoryQty"];
        resultList.add(newObj);
      }
    });
    return resultList;
  }
}
