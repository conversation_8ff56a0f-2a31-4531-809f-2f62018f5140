import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/bill/model/statistics_model.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_pos/shiftchange/widget/PieChartMixin.dart';
import 'package:halo_pos/widgets/base/base_stateful_page.dart';
import 'package:halo_pos/widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';
import 'package:info_popup/info_popup.dart';

import '../common/tool/sp_tool.dart';
import '../common/widget/datetime_filter.dart';
import 'entity/store_statistics_dto.dart';

enum TimeSelectorType {
  None,
  Today,
  Last7Days,
  Last30Days,
}

class StoreRevenueStatistics extends BaseStatefulPage {
  const StoreRevenueStatistics({Key? key}) : super(key: key);

  @override
  BaseStatefulPageState<StoreRevenueStatistics> createState() =>
      _StoreRevenueStatisticsState();
}

class _StoreRevenueStatisticsState
    extends BaseStatefulPageState<StoreRevenueStatistics>
    with PieChartState, DateTimeFilterMixin {
  TimeSelectorType selectorType = TimeSelectorType.Last7Days;
  static double height = ScreenUtil().scaleHeight * 150;
  StoreStatisticsDto statisticsDto = StoreStatisticsDto.fromJson({});

  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        HaloContainer(
          margin: EdgeInsets.only(bottom: 24.w),
          padding: EdgeInsets.only(left: 10.w, right: 10.w),
          mainAxisSize: MainAxisSize.max,
          color: Colors.white,
          children: [
            buildDateTimeQuery(context, width: 840.w),
            buildTimeSelector(),
            _buildBtnQuery()
          ],
        ),
        buildOverall(),
        buildSecond(),
        buildThree()
      ],
    );
  }

  @override
  String getActionBarTitle() {
    return "门店收入统计";
  }

  @override
  void initState() {
    super.initState();
    _initTimeData();
    _initData();
    textStartTimeController.addListener(() {
      setQuickDateSel();
    });
    textEndTimeController.addListener(() {
      setQuickDateSel();
    });
  }

  setQuickDateSel() {
    DateTime now = DateTime.now();
    int beforeTime = DateUtil.getDateTime(textStartTimeController.text)!
        .millisecondsSinceEpoch;
    DateTime endDateTime = DateUtil.getDateTime(textEndTimeController.text)!;
    int endTime = endDateTime.millisecondsSinceEpoch;

    DateTime endDate = DateTime(
        endDateTime.year, endDateTime.month, endDateTime.day, 00, 00, 00);
    DateTime todayDate = DateTime(now.year, now.month, now.day, 00, 00, 00);
    if (endDate == todayDate) {
      var difference =
          (endTime - beforeTime) / 86400000; // 86400000 = 24*60*60*1000
      switch (difference.ceil()) {
        case 1:
          selectorType = TimeSelectorType.Today;
          break;
        case 7:
          selectorType = TimeSelectorType.Last7Days;
          break;
        case 30:
          selectorType = TimeSelectorType.Last30Days;
          break;
        default:
          selectorType = TimeSelectorType.None;
      }
    } else {
      selectorType = TimeSelectorType.None;
    }
    setState(() {});
  }

  _initData() {
    removeOverlay();
    StatisticsModel.getStoreIncomeStatisticsListForPOS(
            context,
            formatDateStringToUtc(textStartTimeController.text),
            formatDateStringToUtc(textEndTimeController.text))
        .then((value) {
      setState(() {
        statisticsDto = value;
        incomePayWays = statisticsDto.incomePayWayDtoList;
      });
    });
  }

  _initTimeData() {
    DateTime now = DateTime.now();
    // 创建一个新的DateTime对象，设置时间为23:59:59
    //默认为当前时间的23:59:59
    DateTime afterTime = DateTime(now.year, now.month, now.day, 23, 59, 59);
    //起始日期默认为起始日的前一个月
    DateTime nowDateTime = DateTime(now.year, now.month, now.day, 00, 00, 00);
    int day = selectorType == TimeSelectorType.Today
        ? 0
        : selectorType == TimeSelectorType.Last7Days
            ? -6
            : -29;
    DateTime beforeTime = nowDateTime.add(Duration(days: day));

    textStartTimeController.text = DateUtil.getDateStrByDateTime(beforeTime)!;
    textEndTimeController.text = DateUtil.getDateStrByDateTime(afterTime)!;
  }

  ///构建时间选择 今天 近7天 近30天
  buildTimeSelector() {
    return Row(children: [
      HaloContainer(
        padding: EdgeInsets.only(left: 15.w, right: 15.w),
        height: 40,
        children: [
          _buildTimeText("今日", selectorType == TimeSelectorType.Today, () {
            setState(() {
              selectorType = TimeSelectorType.Today;
              _initTimeData();
              _initData();
            });
          }),
          _buildTimeText("近7天", selectorType == TimeSelectorType.Last7Days, () {
            setState(() {
              selectorType = TimeSelectorType.Last7Days;
              _initTimeData();
              _initData();
            });
          }),
          _buildTimeText("近30天", selectorType == TimeSelectorType.Last30Days,
              () {
            setState(() {
              selectorType = TimeSelectorType.Last30Days;
              _initTimeData();
              _initData();
            });
          })
        ],
      )
    ]);
  }

  _buildTimeText(String text, bool selected, VoidCallback onChanged) {
    return GestureDetector(
      onTap: onChanged,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: HaloPosLabel(
          text,
          textStyle: TextStyle(
            color: selected ? Colors.blue : Colors.black,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  _buildBtnQuery() {
    return HaloButton(
      height: 25.w,
      borderRadius: 5.sp,
      text: "查询",
      buttonType: HaloButtonType.outlinedButton,
      outLineWidth: 1.0,
      fontSize: 25.sp,
      onPressed: () {
        FocusScopeNode currentFocus = FocusScope.of(context);
        if (!currentFocus.hasPrimaryFocus &&
            currentFocus.focusedChild != null) {
          FocusManager.instance.primaryFocus?.unfocus();
        }
        _initData();
      },
    );
  }

  ///营业概况
  buildOverall() {
    return HaloContainer(
      color: Colors.white,
      direction: Axis.vertical,
      margin: EdgeInsets.symmetric(horizontal: 25.w),
      borderRadius: BorderRadius.all(Radius.circular(10.w)),
      children: [
        buildTitle("营业概况"),
        HaloContainer(
          height: height,
          padding:
              EdgeInsets.only(left: 25.w, right: 25.w, top: 10.w, bottom: 10.w),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: buildCardItem("销售收入(元)", "￥${statisticsDto.income}",
                    tips: "销售收入=销售金额-退货金额+换货金额")),
            Expanded(child: buildCardItem("销售金额(元)", "￥${statisticsDto.sale}")),
            Expanded(
                child:
                    buildCardItem("销售笔数", statisticsDto.saleCount.toString())),
            Expanded(
                child: buildCardItem("平均客单价(元)", "￥${statisticsDto.avgPrice}",
                    tips: "销售金额/销售笔数")),
            Expanded(
                child: buildCardItem("连单率", "${statisticsDto.connectRate}%",
                    tips: "商品数量大于1的销售单数量/销售笔数")),
            Expanded(
                child: buildCardItem("连带率", "${statisticsDto.dependRate}",
                    tips: "销售商品数量/销售笔数"))
          ],
        ),
      ],
    );
  }

  buildSecond() {
    return HaloContainer(
        margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 24.w),
        children: [
          Expanded(
            child: buildReturnData(),
            flex: 2,
          ),
          SizedBox(
            width: 24.w,
          ),
          Expanded(
            child: buildStoredValueData(),
            flex: 1,
          ),
        ]);
  }

  ///退货数据
  buildReturnData() {
    return HaloContainer(
      color: Colors.white,
      direction: Axis.vertical,
      borderRadius: BorderRadius.all(Radius.circular(10.w)),
      children: [
        buildTitle("退换货数据"),
        HaloContainer(
          height: height,
          padding:
              EdgeInsets.only(left: 25.w, right: 25.w, top: 10.w, bottom: 10.w),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child:
                    buildCardItem("退货金额(元)", "￥${statisticsDto.returnSale}")),
            Expanded(
                child:
                    buildCardItem("退货笔数", "${statisticsDto.returnSaleCount}")),
            Expanded(
                child:
                    buildCardItem("换货金额(元)", "￥${statisticsDto.exchangeSale}")),
            Expanded(
                child: buildCardItem(
                    "换货笔数", "${statisticsDto.exchangeSaleCount}")),
          ],
        ),
      ],
    );
  }

  ///储值数据
  buildStoredValueData() {
    return HaloContainer(
      color: Colors.white,
      direction: Axis.vertical,
      borderRadius: BorderRadius.all(Radius.circular(10.w)),
      children: [
        buildTitle("储值数据"),
        HaloContainer(
          height: height,
          padding:
              EdgeInsets.only(left: 25.w, right: 25.w, top: 10.w, bottom: 10.w),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: buildCardItem("储值金额(元)", "￥${statisticsDto.recharge}")),
            Expanded(
                child: buildCardItem("充值笔数", "${statisticsDto.rechargeCount}",
                    tips: "充值笔数不含退款的笔数")),
          ],
        ),
      ],
    );
  }

  buildThree() {
    return Expanded(
      child: HaloContainer(
          margin: EdgeInsets.only(left: 25.w, right: 25.w, bottom: 16.w),
          children: [
            Expanded(
              child: buildVipCard(),
              flex: 1,
            ),
            SizedBox(
              width: 24.w,
            ),
            Expanded(
              child: buildPieChartView(),
              flex: 2,
            ),
          ]),
    );
  }

  buildVipCard() {
    return HaloContainer(
      color: Colors.white,
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      borderRadius: BorderRadius.all(Radius.circular(10.w)),
      children: [
        buildTitle("会员数据"),
        HaloContainer(
          height: height,
          padding:
              EdgeInsets.only(left: 25.w, right: 25.w, top: 10.w, bottom: 10.w),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: buildCardItem("会员消费金额(元)", "￥${statisticsDto.vipSale}")),
            Expanded(
                child:
                    buildCardItem("会员消费笔数", "${statisticsDto.vipSaleCount}")),
          ],
        ),
        HaloContainer(
          height: height,
          padding:
              EdgeInsets.only(left: 25.w, right: 25.w, top: 10.w, bottom: 10.w),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: buildCardItem(
                    "会员消费金额占比", "${statisticsDto.vipSalePercent}%",
                    tips: "会员消费金额/销售金额")),
            Expanded(
                child: buildCardItem(
                    "会员客单价(元)",
                    statisticsDto.vipSaleCount == 0
                        ? "0"
                        : (statisticsDto.vipSale / statisticsDto.vipSaleCount)
                            .toStringAsFixed(
                                SpTool.getSystemConfig().sysDigitalTotal),
                    tips: "会员消费金额/会员消费笔数")),
          ],
        ),
      ],
    );
  }

  buildPieChartView() {
    return HaloContainer(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      color: Colors.white,
      direction: Axis.vertical,
      borderRadius: BorderRadius.all(Radius.circular(10.w)),
      children: [
        buildTitle("支付方式占比"),
        Expanded(child: buildPieChart(context)),
        Padding(
          padding: EdgeInsets.only(left: 160 * ScreenUtil().scaleHeight,bottom: 5.w),
          child: HaloPosLabel(
            "合计收入=销售收入+储值金额",
            textStyle: TextStyle(fontSize: 25.sp, color: Colors.grey),
          ),
        )
      ],
    );
  }

  buildCardItem(String title, String subTitle, {String? tips}) {
    return HaloContainer(
      padding: EdgeInsets.all(10.w),
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
            child: Row(
          children: [
            Flexible(
                child: HaloPosLabel(
              title,
              textStyle:
                  TextStyle(fontSize: 25.sp, color: const Color(0xFF555555)),
            )),
            Visibility(
                visible: tips != null && tips.isNotEmpty,
                child: Padding(
                    padding: const EdgeInsets.only(left: 5, top: 2),
                    child: SizedBox(
                      width: 15,
                      height: 15,
                      child: InfoPopupWidget(
                        arrowTheme: const InfoPopupArrowTheme(
                          arrowDirection: ArrowDirection.down,
                          color: Colors.white,
                        ),
                        contentTitle: tips,
                        child: Container(
                          width: 15,
                          height: 15,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.grey,
                              width: 1,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "?",
                              style: TextStyle(
                                  fontSize: 15.sp, color: Colors.grey),
                            ),
                          ),
                        ),
                      ),
                    )))
          ],
        )),
        Expanded(
            child: HaloPosLabel(subTitle,
                textStyle: TextStyle(
                    fontSize: 30.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.bold))),
      ],
    );
  }

  buildTitle(String title) {
    return HaloContainer(
      padding: EdgeInsets.only(left: 30.w, top: 22.w),
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        HaloPosLabel(title,
            textStyle: TextStyle(
                fontSize: 24.sp,
                color: Colors.black,
                fontWeight: FontWeight.bold)),
      ],
    );
  }

  void showBubble(BuildContext context, String text) {
    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final position = renderBox.localToGlobal(Offset.zero);

    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx,
        top: position.dy + size.height,
        child: Material(
          elevation: 4.0,
          color: Colors.red,
          child: Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(8.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Text(
              text,
              style: const TextStyle(fontSize: 16.0),
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);
  }

  @override
  Future<void>? onInitState() {
    // radius = 50 * ScreenUtil().scaleHeight;
    return null;
  }
}
