class StoreStatisticsDto {
  String storeName;
  double income;
  double recharge;
  int rechargeCount;
  double sale;
  int saleCount;
  double returnSale;
  int returnSaleCount;
  double exchangeSale;
  int exchangeSaleCount;
  double avgPrice;
  double connectRate;
  double dependRate;
  double vipSale;
  double vipSalePercent;
  int vipSaleCount;
  int connectNum;
  double qty;
  String otypeId;
  List<IncomePayWay> incomePayWayDtoList;
  String? profileId;
  String? startTime;
  String? endTime;
  String? otypeIds;
  int? page;
  int? pageSize;

  StoreStatisticsDto({
    required this.storeName,
    required this.income,
    required this.recharge,
    required this.rechargeCount,
    required this.sale,
    required this.saleCount,
    required this.returnSale,
    required this.returnSaleCount,
    required this.exchangeSale,
    required this.exchangeSaleCount,
    required this.avgPrice,
    required this.connectRate,
    required this.dependRate,
    required this.vipSale,
    required this.vipSalePercent,
    required this.vipSaleCount,
    required this.connectNum,
    required this.qty,
    required this.otypeId,
    required this.incomePayWayDtoList,
    this.profileId,
    this.startTime,
    this.endTime,
    this.otypeIds,
    this.page,
    this.pageSize,
  });

  factory StoreStatisticsDto.fromJson(Map<String, dynamic> json) {

    return StoreStatisticsDto(
      storeName: json['storeName']??"",
      income: (json['income']??0).toDouble(),
      recharge: (json['recharge']??0).toDouble(),
      rechargeCount: json['rechargeCount']??0,
      sale: (json['sale']??0).toDouble(),
      saleCount: json['saleCount']??0,
      returnSale: (json['returnSale']??0).toDouble(),
      returnSaleCount: json['returnSaleCount']??0,
      exchangeSale: (json['exchangeSale']??0).toDouble(),
      exchangeSaleCount: json['exchangeSaleCount']??0,
      avgPrice: (json['avgPrice']??0).toDouble(),
      connectRate: (json['connectRate']??0).toDouble(),
      dependRate: (json['dependRate']??0).toDouble(),
      vipSale: (json['vipSale']??0).toDouble(),
      vipSalePercent: (json['vipSalePercent']??0).toDouble(),
      vipSaleCount: json['vipSaleCount']??0,
      connectNum: json['connectNum']??0,
      qty: (json['qty']??0).toDouble(),
      otypeId: json['otypeId']??"0",
      incomePayWayDtoList: json['incomePayWayDtoList']!=null? (json['incomePayWayDtoList'] as List)
          .map((e) => IncomePayWay.fromJson(e))
          .toList():[],
      profileId: json['profileId']??"0",
      startTime: json['startTime']??"0",
      endTime: json['endTime']??"0",
      otypeIds: json['otypeIds']??"0",
      page: json['page']??0,
      pageSize: json['pageSize']??0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'storeName': storeName,
      'income': income,
      'recharge': recharge,
      'rechargeCount': rechargeCount,
      'sale': sale,
      'saleCount': saleCount,
      'returnSale': returnSale,
      'returnSaleCount': returnSaleCount,
      'exchangeSale': exchangeSale,
      'exchangeSaleCount': exchangeSaleCount,
      'avgPrice': avgPrice,
      'connectRate': connectRate,
      'dependRate': dependRate,
      'vipSale': vipSale,
      'vipSalePercent': vipSalePercent,
      'vipSaleCount': vipSaleCount,
      'connectNum': connectNum,
      'qty': qty,
      'otypeId': otypeId,
      'incomePayWayDtoList': incomePayWayDtoList.map((e) => e.toJson()).toList(),
      'profileId': profileId,
      'startTime': startTime,
      'endTime': endTime,
      'otypeIds': otypeIds,
      'page': page,
      'pageSize': pageSize,
    };
  }
}

class IncomePayWay {
  double income;
  String payWayName;
  String payWayId;

  IncomePayWay({
    required this.income,
    required this.payWayName,
    required this.payWayId,
  });

  factory IncomePayWay.fromJson(Map<String, dynamic> json) {
    return IncomePayWay(
      income: json['income'].toDouble(),
      payWayName: json['payWayName'],
      payWayId: json['payWayId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'income': income,
      'payWayName': payWayName,
      'payWayId': payWayId,
    };
  }
}
