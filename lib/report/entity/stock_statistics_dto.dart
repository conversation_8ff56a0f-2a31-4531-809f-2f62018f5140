class StockStatisticsDto {
  String batchno = ""; //
  String batchId = "";
  String brand = ""; //商品品牌
  String brandName = ""; //商品品牌
  dynamic costPrice; //参考成本价
  dynamic estimatedCostPrice; //预估成本
  dynamic estimatedCostTotal; //预估金额
  String expireDate = ""; //
  String? expireToDays; //
  String fullBarCode = ""; //商品条码
  String? fullbarcode = ""; //商品条码
  String? ktypeId; //仓库id
  String? kFullname; //仓库id
  String memo = ""; //
  String pFullname = ""; //
  String pFullName = ""; //
  String? pUrl; //
  String? picUrl; //商品图片url
  String? price; //
  String produceDate = ""; //
  int shelfLife = 0; //保质期
  String propNames = ""; //属性格式
  String propValues = ""; //属性值组合
  String? ptypeArea; //
  String? ptypeId; //
  String ptypeMemo = ""; //
  String? ptypeType; //
  String? ptypeTypeId; //商品typeid
  dynamic saleStockQty; //可销库存
  String sellByDates = ""; //
  String shortName = ""; //
  String shortname = ""; //
  String? skuId; //
  String? ptypeSkuId; //
  String? sn1; //
  String? sn2; //
  String snMemo = ""; //
  String snno = ""; //
  String standard = ""; //
  String? status; //
  int? stockAmount; //
  dynamic stockQty; //
  dynamic totalWeight; //商品总重量
  String? unit; //
  String? unit2; //辅助单位1
  int? unit2Qty; //辅助单位1数量
  String? unit3; //辅助单位2
  int? unit3Qty; //辅助单位2数量
  String unitName = ""; //
  String? unitRelation; //单位关系
  String? unitRelationQty; //
  String? userCode; //商品编号
  String? usercode; //
  dynamic weight; //商品重量
  int? snenabled;
  bool propenabled = false;
  bool batchenabled = false;
  bool batchEnabled = false;

  String? qty; //成本明细库存数量
  String? total; //成本明细库存金额
  String? ktypeName;
  dynamic retailPrice; //零售价
  Map? beforeData;

  static StockStatisticsDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return StockStatisticsDto();
    StockStatisticsDto stockStatisticsDtoBean = StockStatisticsDto();
    stockStatisticsDtoBean.batchno = map['batchno'] ?? "";
    stockStatisticsDtoBean.brand = map['brand'] ?? "";
    stockStatisticsDtoBean.brandName = map['brandName'] ?? "无";
    stockStatisticsDtoBean.costPrice = map['costPrice'];
    stockStatisticsDtoBean.estimatedCostPrice = map['estimatedCostPrice'];
    stockStatisticsDtoBean.estimatedCostTotal = map['estimatedCostTotal'];
    // stockStatisticsDtoBean.expireDate = DateUtil.formatDateStr(
    //     map['expireDate'] ?? "",
    //     format: DataFormats.y_mo_d);
    stockStatisticsDtoBean.expireToDays = map['expireToDays'] ?? "";
    stockStatisticsDtoBean.fullBarCode = map['fullBarCode'] ?? "";
    stockStatisticsDtoBean.fullbarcode = map['fullbarcode'] ?? "";
    stockStatisticsDtoBean.ktypeId = map['ktypeId'];
    stockStatisticsDtoBean.memo = map['memo'] ?? "无";
    stockStatisticsDtoBean.pFullname = map['pFullname'] ?? "";
    stockStatisticsDtoBean.pFullName = map['pFullName'] ?? "";
    stockStatisticsDtoBean.pUrl = map['pUrl'];
    stockStatisticsDtoBean.picUrl = map['picUrl'];
    stockStatisticsDtoBean.price = map['price'];
    // stockStatisticsDtoBean.produceDate = DateUtil.formatDateStr(
    //     map['produceDate'] ?? "",
    //     format: DataFormats.y_mo_d);
    stockStatisticsDtoBean.propNames = map['propNames'] ?? "";
    stockStatisticsDtoBean.propValues = map['propValues'] ?? "";
    stockStatisticsDtoBean.ptypeArea = map['ptypeArea'];
    stockStatisticsDtoBean.ptypeId = map['ptypeId'];
    stockStatisticsDtoBean.ptypeMemo = map['ptypeMemo'] ?? "无";
    stockStatisticsDtoBean.ptypeType = map['ptypeType'];
    stockStatisticsDtoBean.ptypeTypeId = map['ptypeTypeId'];
    stockStatisticsDtoBean.saleStockQty = map['saleStockQty'];
    stockStatisticsDtoBean.sellByDates = map['sellByDates'] ?? "";
    stockStatisticsDtoBean.shortName = map['shortName'] ?? "";
    stockStatisticsDtoBean.shortname = map['shortname'] ?? "";
    stockStatisticsDtoBean.skuId = map['skuId'];
    stockStatisticsDtoBean.ptypeSkuId = map['ptypeSkuId'];
    stockStatisticsDtoBean.sn1 = map['sn1'];
    stockStatisticsDtoBean.sn2 = map['sn2'];
    stockStatisticsDtoBean.snMemo = map['sn_memo'] ?? "无";
    stockStatisticsDtoBean.snno = map['snno'] ?? "";
    stockStatisticsDtoBean.standard = map['standard'] ?? "无";
    stockStatisticsDtoBean.status = map['status'].toString();
    stockStatisticsDtoBean.stockAmount = map['stockAmount'];
    stockStatisticsDtoBean.stockQty = map['stockQty'];
    stockStatisticsDtoBean.totalWeight = map['totalWeight'];
    stockStatisticsDtoBean.unit = map['unit'];
    stockStatisticsDtoBean.unit2 = map['unit2'];
    stockStatisticsDtoBean.unit2Qty = map['unit2Qty'];
    stockStatisticsDtoBean.unit3 = map['unit3'];
    stockStatisticsDtoBean.unit3Qty = map['unit3Qty'];
    stockStatisticsDtoBean.unitName = map['unitName'] ?? "";
    stockStatisticsDtoBean.unitRelation = map['unitRelation'];
    stockStatisticsDtoBean.unitRelationQty = map['unitRelationQty'];
    stockStatisticsDtoBean.userCode = map['userCode'];
    stockStatisticsDtoBean.usercode = map['usercode'];
    stockStatisticsDtoBean.weight = map['weight'];

    stockStatisticsDtoBean.snenabled = map['snenabled'] ?? 0;
    stockStatisticsDtoBean.propenabled = map['propenabled'] ?? false;
    stockStatisticsDtoBean.batchenabled = map['batchenabled'] ?? false;
    stockStatisticsDtoBean.batchEnabled = map['batchEnabled'] ?? false;
    stockStatisticsDtoBean.shelfLife = map["shelfLife"] ?? 0;
    stockStatisticsDtoBean.qty = map["qty"] ?? "0";
    stockStatisticsDtoBean.total = map["total"];
    stockStatisticsDtoBean.ktypeName = map["ktypeName"];
    stockStatisticsDtoBean.batchId = map["batchId"] ?? "";
    stockStatisticsDtoBean.retailPrice = map["retailPrice"];
    return stockStatisticsDtoBean;
  }

  Map<String, dynamic> toJson() => {
        "batchno": batchno,
        "brand": brand,
        "brandName": brandName,
        "costPrice": costPrice,
        "estimatedCostPrice": estimatedCostPrice,
        "estimatedCostTotal": estimatedCostTotal,
        "expireDate": expireDate,
        "expireToDays": expireToDays,
        "fullBarCode": fullBarCode,
        "fullbarcode": fullbarcode,
        "ktypeId": ktypeId,
        "memo": memo,
        "pFullname": pFullname,
        "pFullName": pFullName,
        "pUrl": pUrl,
        "picUrl": picUrl,
        "price": price,
        "produceDate": produceDate,
        "propNames": propNames,
        "propValues": propValues,
        "ptypeArea": ptypeArea,
        "ptypeId": ptypeId,
        "ptypeMemo": ptypeMemo,
        "ptypeType": ptypeType,
        "ptypeTypeId": ptypeTypeId,
        "saleStockQty": saleStockQty,
        "sellByDates": sellByDates,
        "shortName": shortName,
        "shortname": shortname,
        "skuId": skuId,
    "ptypeSkuId": ptypeSkuId,


        "sn1": sn1,
        "sn2": sn2,
        "sn_memo": snMemo,
        "snno": snno,
        "standard": standard,
        "status": status,
        "stockAmount": stockAmount,
        "stockQty": stockQty,
        "totalWeight": totalWeight,
        "unit": unit,
        "unit2": unit2,
        "unit2Qty": unit2Qty,
        "unit3": unit3,
        "unit3Qty": unit3Qty,
        "unitName": unitName,
        "unitRelation": unitRelation,
        "unitRelationQty": unitRelationQty,
        "userCode": userCode,
        "usercode": usercode,
        "weight": weight,
        "snenabled": snenabled,
        "propenabled": propenabled,
        "batchenabled": batchenabled,
    "batchEnabled": batchEnabled,
    "shelfLife": shelfLife,
        "qty": qty,
        "total": total,
        "ktypeName": ktypeName,
        "batchId": batchId,
        "retailPrice": retailPrice
      };
}
