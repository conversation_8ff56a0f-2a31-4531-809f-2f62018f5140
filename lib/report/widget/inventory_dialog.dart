import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../bill/bill/channel/widget/base_common_dialog.dart';
import '../../bill/model/bill_model.dart';
import '../../widgets/halo_pos_label.dart';
import '../entity/stock_statistics_dto.dart';
import '../inventory_helper.dart';

/**
 * 库存分布查询弹窗
 */
class InventoryDialog extends StatefulWidget {
  final StockStatisticsDto statisticsDto;
  const InventoryDialog({required this.statisticsDto, Key? key}) : super(key: key);

  @override
  State<InventoryDialog> createState() => _InventoryDialogState();
}

class _InventoryDialogState extends BaseCommonDialogState<InventoryDialog> {

  @override
  // TODO: implement height
  double get height => 600.w;

  @override
  // TODO: implement title
  String get title => "库存分布";

  @override
  // TODO: implement width
  double get width => 600.w;

  List<StockStatisticsDto> stockStatisticsDtoList = [];

  @override
  void initState() {
    // TODO: implement initState
    BillModel.getKtypeList(context, {
      "queryParams": {"stoped": false}
    }).then((ktypeList) {
      //获取数据
      BillModel.getNventoryGoodsDistList(context,"",(widget.statisticsDto.ptypeSkuId??"")).then((value) {
        List<StockStatisticsDto> list =
        InventoryHelper.reconsitutionDataListForKtype(value.first,ktypeList);
        setState(() {
          stockStatisticsDtoList = list;
        });
      });
    });

    super.initState();
  }

  @override
  Widget buildContent(BuildContext context) {
    // TODO: implement buildContent
    return HaloContainer(
      direction: Axis.vertical,
      children: [
        HaloContainer(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(padding: EdgeInsets.all(10.w),child: HaloImage(
              widget.statisticsDto.pUrl,
              fit: BoxFit.fill,
              width: 128.w,
              height: 128.w,
            ),),
            Expanded(
              child: HaloContainer(
                height: 150.w,
                padding: EdgeInsets.all(10.w),
                direction: Axis.vertical,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize:MainAxisSize.max,
                children: [
                  HaloPosLabel(
                    widget.statisticsDto.pFullName,
                    textStyle: TextStyle(
                        fontSize: 28.w,
                        color: ColorUtil.stringColor("#333333"),
                        fontWeight: FontWeight.w600),
                    maxLines: 2,
                  ),
                  Expanded(child: Container()),
                  HaloPosLabel(widget.statisticsDto.propValues, textStyle: TextStyle(
                      fontSize: 28.w,
                      color: ColorUtil.stringColor("#555555")),)
                ],
              ),
            )
          ],
        ),
        HaloContainer(
          margin: EdgeInsets.only(left: 25.w,right: 25.w,top: 25.w),
          padding: EdgeInsets.only(left: 10.w,right: 10.w),
          height: 60.w,
          color: ColorUtil.stringColor("#F5F6F7"),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            HaloPosLabel("仓库",textStyle: TextStyle(fontSize: 22.sp,color: ColorUtil.stringColor("#333333"),fontWeight: FontWeight.w600),),
            HaloPosLabel("数量",textStyle: TextStyle(fontSize: 22.sp,color: ColorUtil.stringColor("#333333"),fontWeight: FontWeight.w600)),
          ],
        ),
        Container(
          margin: EdgeInsets.only(left: 25.w,right: 25.w,),
          padding: EdgeInsets.only(left: 10.w,right: 10.w),
          height: 250.w,
          child: ListView.builder(
              itemCount: stockStatisticsDtoList.length,
              itemBuilder: (item, index) {
                return _buildContentItem(
                    stockStatisticsDtoList[index]
                    );
              }),
        ),
      ],
    );
  }

  _buildContentItem(StockStatisticsDto statisticsDto){
    return Column(
      children: [
        HaloContainer(
          height: 60.w,
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            HaloPosLabel(statisticsDto.kFullname??""),
            HaloPosLabel(statisticsDto.stockQty.toString()),
          ],
        ),
        Divider(
          color: ColorUtil.stringColor("#D5D6D7"),
          height: 1.w,
        ),
      ],
    );
  }
}
