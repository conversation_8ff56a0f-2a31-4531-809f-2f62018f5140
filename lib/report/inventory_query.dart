import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import '../../../bill/entity/ktype_list_dto.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../report/inventory_helper.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_search.dart';
import '../../../report/entity/stock_statistics_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../bill/model/bill_model.dart';
import '../bill/entity/goods_detail_dto.dart';
import '../common/tool/dialog_util.dart';
import '../common/widget/ptype_note_richtext.dart';
import '../widgets/selector/scan_select.dart';
import 'widget/inventory_dialog.dart';

class InventoryQuery extends StatefulWidget {
  const InventoryQuery({Key? key}) : super(key: key);

  @override
  State createState() => _InventoryQueryState();
}

class _InventoryQueryState extends State<InventoryQuery> {
  List dataSoure = [];
  String filterValue = "";
  List<KtypeItemDto> ktypeList = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color.fromRGBO(0, 0, 0, 0),
      body: Container(
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 80.w, horizontal: 300.w),
              decoration: const BoxDecoration(
                color: Colors.white,
                //设置四周圆角 角度
                borderRadius: BorderRadius.all(Radius.circular(5.0)),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: 830.h,
                  // maxWidth: ScreenUtil().screenWidth - 900.w,
                ),
                child: Column(
                  children: [
                    HaloContainer(
                      padding: EdgeInsets.only(left: 19.w, right: 15),
                      color: Colors.white24,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      height: 72.h,
                      children: [
                        Text(
                          "库存查询",
                          style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 28.sp,
                              color: AppColorHelper(context)
                                  .getTitleBoldTextColor()),
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            color: Colors.transparent,
                            alignment: Alignment.centerRight,
                            width: 100,
                            height: 30,
                            child: IconFont(
                              IconNames.close,
                              size: 30,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Divider(
                      color: AppColors.lineColor,
                      height: 1,
                    ),
                    _buildSearch(),
                    // const Divider(
                    //   color: AppColors.lineColor,
                    //   height: 1,
                    // ),
                    _buildTop(),
                    // const Divider(
                    //   color: AppColors.lineColor,
                    //   height: 1,
                    // ),
                    Expanded(
                      child: ListView.separated(
                        itemBuilder: (itemData, int index) {
                          return _buildItem(dataSoure[index]);
                        },
                        separatorBuilder: (item, index) => Divider(
                          height: 1.w,
                          color: AppColors.lineColor,
                        ),
                        itemCount: dataSoure.length,
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTop() {
    return HaloContainer(
      padding: EdgeInsets.only(left: 19.w, right: 19.w),
      // mainAxisSize: MainAxisSize.max,
      color: ColorUtil.stringColor("#F5F5F5"),
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      height: 60.h,
      children: [
        SizedBox(
          width: 100.w,
          child: Padding(
            padding: EdgeInsets.only(left: 5.w),
            child: Text(
              "图片",
              textAlign: TextAlign.start,
              style: TextStyle(
                  color: AppColorHelper(context).getTitleTextColor(),
                  fontSize: 22.sp),
            ),
          ),
        ),
        Expanded(
            flex: 3,
            child: Text(
              "商品名称/编号",
              style: TextStyle(
                  color: AppColorHelper(context).getTitleTextColor(),
                  fontSize: 22.sp),
            )),
        SizedBox(
          width: 250.w,
          child: Text(
            "条码",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 350.w,
          child: Text(
            "属性组合",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 100.w,
          child: Text(
            "单位",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 150.w,
          child: Text(
            "库存",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 100.w,
          child: Text(
            "库存分布",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleTextColor(),
                fontSize: 22.sp),
          ),
        ),
      ],
    );
  }

  Widget _buildItem(StockStatisticsDto statisticsDto) {
    return HaloContainer(
      padding: EdgeInsets.symmetric(horizontal: 19.w, vertical: 16.h),
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SizedBox(
          width: 100.w,
          child: HaloImage(
            statisticsDto.pUrl,
            fit: BoxFit.fill,
            width: 60.w,
            height: 60.w,
          ),
        ),
        Expanded(
            flex: 3,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PtypeNoteRichText(goodsDetailDto:conversionGoodsDetail(statisticsDto)),
                Visibility(
                  visible: statisticsDto.userCode?.isNotEmpty ?? false,
                  child: Text(
                    statisticsDto.userCode ?? "",
                    style: TextStyle(
                        color: AppColorHelper(context).getTitleBoldTextColor(),
                        fontSize: 22.sp,
                        fontWeight: FontWeight.w600),
                  ),
                )
              ],
            )),
        SizedBox(
          width: 250.w,
          child: Text(
            statisticsDto.fullbarcode ?? '',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 350.w,
          child: Text(
            statisticsDto.propValues ?? '',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 100.w,
          child: Text(
            statisticsDto.unit ?? '',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 150.w,
          child: Text(
            statisticsDto.stockQty != null
                ? Decimal.parse(statisticsDto.stockQty.toString()).toString()
                : "0",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: ColorUtil.stringColor("#4679FC"), fontSize: 22.sp),
          ),
        ),
        GestureDetector(
          onTap: () {
            DialogUtil.showAlertDialog(context,
                child: InventoryDialog(
                  statisticsDto: statisticsDto,
                ));
          },
          child: SizedBox(
            width: 100.w,
            child: Text(
              "查看",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.lightBlueAccent, fontSize: 22.sp),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSearch() {
    return ScanSelectWidget(
        hint: "输入商品名称/商品编号/条码查询",
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border.all(color: Colors.transparent),
          borderRadius: BorderRadius.all(Radius.circular(4.w)),
        ),
        onSubmitted: (text) {
          filterValue = text;
          _requsetData(text);
        });
  }

  conversionGoodsDetail(StockStatisticsDto statisticsDto){
    GoodsDetailDto goodsDetailDto = GoodsDetailDto();
    goodsDetailDto.pFullName = statisticsDto.pFullName;
    // goodsDetailDto.comboRow = statisticsDto.comboRow;
    goodsDetailDto.batchenabled = statisticsDto.batchEnabled;
    goodsDetailDto.snenabled = statisticsDto.snenabled??0;
    goodsDetailDto.propenabled = statisticsDto.propenabled;
    // goodsDetailDto.pcategory = statisticsDto.pcategory;
    return goodsDetailDto;
  }

  _requsetData(String filterValue) {
    // BillModel.getStockStatistics(context, 1, filterValue).then((value) {
    //   setState(() {
    //     dataSoure.clear();
    //     dataSoure.addAll(value);
    //   });
    // });

    BillModel.getNventoryGoodsDistList(context, filterValue, "",
        ktypeIds: [SpTool.getStoreInfo()!.ktypeId!]).then((value) {
      List<StockStatisticsDto> list = InventoryHelper.reconsitutionDataList(
          value, SpTool.getStoreInfo()!.ktypeId!);
      setState(() {
        dataSoure.clear();
        dataSoure.addAll(list);
      });
    });
  }
}
