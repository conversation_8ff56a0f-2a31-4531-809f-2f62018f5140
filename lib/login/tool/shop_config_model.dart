import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../common/net/http_util.dart';
import '../../../common/net/request_method.dart';
import '../../../common/tool/sp_tool.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:halo_utils/http/base_model.dart';

import '../../entity/system/permission_config.dart';
import '../../entity/system/module_version.dart';

///
///@ClassName: ShopShop
///@Description: 门店信息
///@Author: tanglan
///@Date: 8/2/21 1:44 PM
class ShopConfigModel {
  // static void getShopConfig(BuildContext context, {Function onSuccess}) {
  //   HttpUtil.request(context,
  //           method: RequestMethod.GET_STORE_LIST, requestModel: NetMethod.GET)
  //       .then((model) {
  //     SpTool.saveShopConfig(model.data);
  //     onSuccess();
  //   });
  // }

  ///保存系统配置数据到本地
  static Future<void> saveAllSystemConfig(
      Map<String, dynamic>? systemConfig) async {
    if (null == systemConfig) {
      return;
    }
    await SpTool.saveAllSystemConfig(systemConfig);

    ///重新登录单例不刷新
    DecimalDisplayHelper.instance.configDto = SpTool.getSystemConfig();
  }

  ///保存权限
  static Future<PermissionConfig?> getSysConfigAndPermission(
      BuildContext context,
      {isLoading = true}) async {
    ResponseModel responseModel = await HttpUtil.request(context,
        method: RequestMethod.GET_PERMISSION,
        requestModel: NetMethod.GET,
        isLoading: isLoading);
    return PermissionConfig.fromMap(responseModel.data);
  }

  ///保存权限
  static Future<void> savePermission(BuildContext context) async {
    return await HttpUtil.request(context,
            method: RequestMethod.GET_PERMISSION, requestModel: NetMethod.GET)
        .then((onValue) async {
      SpTool.savePermission(onValue.data["permissions"]);
    });
  }

//保存模块开关以及升级验证
  static Future<ModuleVersion?> getModelSwitchAndVersion(
      BuildContext context) async {
    ResponseModel responseModel = await HttpUtil.request(context,
        method: RequestMethod.getModelSwitchAndVersion,
        requestModel: NetMethod.GET);
    return ModuleVersion.fromMap(responseModel.data);
  }

  ///获取自动登录地址
  static Future<ResponseModel> registerLoginUsing(BuildContext context) async {
    ResponseModel responseModel = await HttpUtil.request(context,
        method: RequestMethod.GET_AUTO_LOGINUSING, requestModel: NetMethod.GET);
    return responseModel;
  }

  ///保存日志到服务器上
  static Future<ResponseModel?> addPosErrorLog(
      BuildContext? context, String message) async {
    if (null == context) {
      return Future.value();
    }
    ResponseModel responseModel = await HttpUtil.request(context,
        method: RequestMethod.addPosErrorLog,
        requestModel: NetMethod.POST,
        data: message,
        isLoading: false);
    return responseModel;
  }
}
