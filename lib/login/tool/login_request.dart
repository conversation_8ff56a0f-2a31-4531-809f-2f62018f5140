import 'package:flutter/material.dart';
import '../../../application.dart';
import '../../../common/net/request_url.dart';
import '../../../common/string_res.dart';
import 'package:haloui/widget/halo_toast.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:halo_utils/http/dio_manager.dart';
import '../../common/tool/performance_capture_util.dart';
import '../model/company_dto.dart';
import 'package:halo_utils/http/base_model.dart';

///
///@ClassName: login-request
///@Description: 登录网络请求类
///@Author: tanglan
///@Date: 7/21/21 11:03 PM

class LoginRequest {
  ///账号密码登录
  static loginByAccount(
    BuildContext context, {
    Map<String, dynamic>? params,
    Function(Map<String, dynamic>)? loginSuccess,
    bool Function(Map<String, dynamic>)? loginFail,
  }) async {
    await LoginRequest._request(
      context,
      params: params,
      method: "pos/ngplogin",
      requestSuccess: (data) {
        if (data.data["changePassword"] == true) {
          HaloToast.show(
            context,
            msg: StringRes.PASSWORD_VALID.getText(context),
          );
          return;
        }
        if (null != loginSuccess) {
          loginSuccess(data.data);
        }
      },
      requestFail: (data) {
        ///友好提示，当API返回空消息时，特殊处理
        if (StringUtil.isEmpty(data["message"])) {
          HaloToast.show(context, msg: StringRes.LOGIN_FAIL.getText(context));
          return true;
        }
        if (null != loginFail) {
          return loginFail(data);
        }
        return false;
      },
    );
  }

  ///获取公司信息
  static Future<void> getCompanyList(
    BuildContext context, {
    Map<String, dynamic>? params,
    Function(List<CompanyDto>)? requestSuccess,
  }) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.companyList);
    await _request(
      context,
      method: "api/getLoginUser",
      params: params,
      requestSuccess: (data) {
        PerformanceCaptureUtil.end(PerformanceTimeName.companyList);
        var list = data.data["data"] as List;
        if (null != requestSuccess) {
          List<CompanyDto> companyList =
              []..addAll(
                list.map((o) {
                  return CompanyDto.fromMap(o);
                }),
              );
          requestSuccess(companyList);
        }
      },
    );
  }

  //loginType 0：pc 1：app 2：h5
  static Future<void> loginByProfileId(
    BuildContext context, {
    Map<String, dynamic>? params,
    Function(Map<String, dynamic>)? loginSuccess,
    bool Function(Map<String, dynamic>)? loginFail,
  }) async {
    return _request(
      context,
      method: "api/loginByProfileId",
      params: params,
      requestSuccess: (data) {
        if (null != loginSuccess) {
          loginSuccess(data.data);
        }
      },
      requestFail: (responseData) {
        if (null != loginFail) {
          return loginFail(responseData);
        }
        return false;
      },
    );
  }

  static Future<void> sendCode(
    BuildContext context,
    String phone, {
    Function? success,
  }) async {
    await _request(
      context,
      requestWork: NetMethod.GET,
      method: "app/sendCode/$phone",
      requestSuccess: (data) {
        if (null != success) {
          success();
        }
      },
    );
  }

  static Future<void> _request(BuildContext context,
      {NetMethod requestWork = NetMethod.POST,
      params,
      String? baseUrl,
      String? method,
      bool showProgressBar = true,
      Function(ResponseModel)? requestSuccess,
      bool Function(Map<String, dynamic>)? requestFail}) async {
    ///登录单独的请求 没有走httputil 所以单独写一套
    setProxy();
    dynamic responseModel = await DioManager().request("/$method", context,
        data: params,
        baseUrl: StringUtil.isNotEmpty(baseUrl)
            ? baseUrl
            : (Application.isDebug
                ? RequestURL.LOGIN_URL_DEBUG
                : RequestURL.LOGIN_URL),
        method: requestWork,
        isLoading: showProgressBar, onErrorCallBack: (data) {
      if (null != requestFail) {
        return requestFail(data.data);
      }
      return false;
    }).catchError((error) {
      HaloToast.show(context, msg: error.message);
    });
    if (null != responseModel) {
      requestSuccess!(responseModel);
    }
  }
  //设置代理
  static void setProxy() {
    String proxyAddress = "";
    bool useProxy = false;
    if (Application.isDebug &&
        StringUtil.isNotEmpty(Application.proxyAddress)) {
      useProxy = true;
      proxyAddress = Application.proxyAddress!;
    }

    if (useProxy && StringUtil.isNotEmpty(proxyAddress)) {
      //请求代理
      DioManager().setProxy(proxyAddress);
    }
  }
}
