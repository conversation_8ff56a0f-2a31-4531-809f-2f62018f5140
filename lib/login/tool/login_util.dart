import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import '../../../common/string_res.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../login/model/company_dto.dart';
import '../../../login/model/login_user_model.dart';
import '../../../login/tool/login_request.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/widget/custom_dialog.dart';
import 'package:haloui/widget/halo_dialog.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../application.dart';
import '../../common/login/login_center.dart';
import '../../common/tool/dialog_util.dart';
import '../../common/tool/performance_capture_util.dart';
import '../login_page.dart';

///
///@ClassName: login_util
///@Description:
///@Author: tanglan
///@Date: 2023/3/15

class LoginUtil {
  static const int SUCCESS_CODE = 200;
  static const int EXPIRE_CODE = 101; //未开通APP的code
  static const int UN_BUY_CODE = 102; //账号资产过期的code
  static const int ERROR_PASSWORD = 1; //账号密码错误
  static const String LOGIN_ASSET = "ispos"; //登录资产

  /// 账号密码登录操作
  ///   ////loginAsset: h5,iswlb，ispos,iswms
  static Future<void> accountLogin(BuildContext context,
      {String? company,
      String? userName,
      String? pwd,
      Function? loginSuccess,
      Function(int code)? loginFail}) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.loginByPwd);
    await LoginRequest.loginByAccount(context, params: {
      "company": company,
      "user": userName,
      "pwd": pwd,
      "showNotice": "true",
      "loginAsset": LOGIN_ASSET
    }, loginSuccess: (data) {
      PerformanceCaptureUtil.end(PerformanceTimeName.loginByPwd);
      _afterLoginSuccess(context,
          loginResponse: data,
          company: company,
          pwd: pwd,
          userName: userName,
          loginSuccess: loginSuccess);
    }, loginFail: (responseData) {
      //登录失败
      PerformanceCaptureUtil.end(PerformanceTimeName.loginByPwd);

      ///账号过期的联系客户
      if (responseData["code"] == EXPIRE_CODE ||
          responseData["code"] == UN_BUY_CODE) {
        _handleOutDateAccount(context,
            responseData: responseData, company: company);
        return true;
      }

      ///账号密码错误三次以上，录入图形验证码
      if (responseData["message"] ==
          StringRes.LOGIN_FAIL_WITH_PASSWORD.getText(context)) {
        HaloToast.show(context, msg: responseData["message"]);
        if (null != loginFail) {
          loginFail(ERROR_PASSWORD);
        }
        return true;
      }
      return false;
    });
  }

  ///手机账号登录
  ///  ////loginAsset: h5,iswlb，ispos,iswms
  static Future<void> phoneLogin(BuildContext context,
      {String? phone,
      String? smsCode,
      Function(List<CompanyDto> companyList)? requestSuccess}) async {
    //获取公司 loginType 1：pc 2：重置密码 3：App登录 4：风险检查
    LoginRequest.getCompanyList(context,
        params: {
          "mobile": phone?.trim(),
          "validateCode": smsCode?.trim(),
          "loginType": 3,
          "productIdList": [66, 88],
          "loginAsset": LOGIN_ASSET
        },
        requestSuccess: requestSuccess);
  }

// ///登录成功
  static void _afterLoginSuccess(
    BuildContext context, {
    Map<String, dynamic>? loginResponse,
    String? company,
    String? userName,
    String? pwd,
    Function? loginSuccess,
  }) async {
    LoginUserModel loginUser =
        SpTool.decodeJWtAndUpdateUser(loginResponse!["authorization"]);
    loginUser.productId = loginResponse["productId"];
    loginUser.authorization = loginResponse["authorization"];
    loginUser.company = company;
    loginUser.isPhoneLogin = false;
    loginUser.user = userName;
    loginUser.pwd = pwd;
    loginUser.requestUrl =
        StringUtil.trim(loginResponse["appLoginUrl"], trimStr: "/");
    if (Application.isDebug) {
      loginUser.pwd = pwd;
    }
    await SpTool.saveLoginUser(loginUser);
    if (null != loginSuccess) {
      loginSuccess();
    }
  }

//
  static void _handleOutDateAccount(BuildContext context,
      {Map<String, dynamic>? responseData, String? company}) async {
    LoginUserModel loginUser = LoginUserModel();
    loginUser.productId = responseData?["productId"];
    loginUser.company = company;
    await SpTool.saveLoginUser(loginUser);
    _onlineService(context, responseData?["messsage"]);
  }

//
  ///联系客服
  static void _onlineService(BuildContext context, String message) {
    HaloDialog(context, title: message, actionModels: [
      CustomButtonModel(textString: "立即咨询"),
      CustomButtonModel(textString: "取消", fontWeight: FontWeight.bold),
    ], cancelActionListener: () {
//TODO 加载客服
// try {
//   SystemConfigUtil.getOnlineService(context).then((value) {
//     Map<String, dynamic> dataJson = jsonDecode(value);
//     if (0 == dataJson["code"]) {
//       HaloToast.show(context, msg: dataJson["msg"]);
//       return;
//     }
//     OnlineServicePlugin _onlineServicePlugin = OnlineServicePlugin();
//     _onlineServicePlugin.start(context, value);
//   });
// } catch (e) {
//   HaloToast.show(context, msg: "加载在线客服失败！");
// }
    });
  }

//loginType 0：pc 1：app 2：h5
////loginAsset: h5,iswlb，ispos,iswms
  static Future<void> loginByProfileId(BuildContext context,
      {CompanyDto? companyDto, String? phone, Function()? loginSuccess}) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.loginBySMS);
    await LoginRequest.loginByProfileId(context, params: {
      "companyName": companyDto?.companyName,
      "token": companyDto?.sign,
      "loginType": "2",
      "loginAsset": LOGIN_ASSET
    }, loginSuccess: (data) async {
      String authorization = data["arguments"]["ngp-authorization"];
      LoginUserModel loginUser = SpTool.decodeJWtAndUpdateUser(authorization);
      loginUser.productId = data["productId"];
      loginUser.company = companyDto?.companyName;
      loginUser.user = companyDto?.loginUserName;
      loginUser.mobile = phone;
      loginUser.isPhoneLogin = true;
      loginUser.requestUrl = StringUtil.trim(data["appLoginUrl"], trimStr: "/");
      loginUser.authorization = authorization;

      await SpTool.saveLoginUser(loginUser);
      PerformanceCaptureUtil.start(PerformanceTimeName.loginBySMS);
      if (null != loginSuccess) {
        loginSuccess();
      }
    }, loginFail: (responseResult) {
      if (responseResult["code"] == UN_BUY_CODE ||
          responseResult["code"] == EXPIRE_CODE) {
        PerformanceCaptureUtil.start(PerformanceTimeName.loginBySMS);
        _onlineService(context, responseResult["message"]);
        return true;
      }
      return false;
    });
  }

  static Future<void> doSendCode(BuildContext context,
      {String? phone, Function()? afterSend}) async {
    await LoginRequest.sendCode(context, phone ?? "", success: () {
      if (null != afterSend) {
        afterSend();
      }
    });
  }

  static void loginOut(BuildContext context) {
    debugPrint("_loginOut_begin");
    debugPrint("_loginOut_ing+++++${Application.loginOut}");
    if (null != Application.loginOut) {
      debugPrint("_loginOut_ing++++111+${Application.loginOut}");
      Application.loginOut!(context);
      return;
    }
    debugPrint("_loginOut_end");
    //这里重新打开登录页之前，需要将GetX重置，否则会抛异常，脏widget泄漏(LoginPage)
    Get.reset();
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false);
  }

  static void tipsLoginOut(BuildContext context, String tipsMsg) {
    DialogUtil.showAlertDialog(context,
        title: "重新登录",
        dismissOnTouchOutside: false,
        content: tipsMsg,
        actionLabels: ["确定"], onActionListener: (index) {
      LoginCenter.loginOut(context, loginOutSuccess: () {}).whenComplete(() {
        SpTool.saveShiftChangesInfo(0);
        SpTool.clearPromotionList();
        SpTool.cleanTenderManageConfig();
        SpTool.cleanTransferConfig();
        SpTool.saveAutoLogin(false);
        SpTool.saveEtypeInfo("", "");
        Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const LoginPage()),
            (route) => false);
      });
    });
  }
}
