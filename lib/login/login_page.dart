import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../common/stock_sync.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_checkbox.dart';
import 'package:haloui/widget/halo_web_widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/string_res.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../common/tool/version_check.dart';
import '../../../entity/system/system_config_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../login/model/store_model.dart';
import '../../../login/tool/login_util.dart';
import '../../../login/tool/shop_config_model.dart';
import '../../../login/widget/company_selector.dart';
import '../../../login/widget/halo_check_code.dart';
import '../../../login/widget/login_select_tab.dart';
import '../../../login/widget/safe_tips.dart';
import '../../../login/widget/store_selector.dart';
import '../../../offline/offline_tool.dart';
import '../../../plugin/secondary_screen_plugin.dart';
import '../../../widgets/focus_text_field.dart';
import '../application.dart';
import '../common/login/login_center.dart';
import '../common/net/request_url.dart';
import '../common/tool/performance_capture_util.dart';
import '../common/tool/sp_custom_util.dart';
import '../db/database_config.dart';
import '../db/login_user_db_mannager.dart';
import '../entity/system/module_version.dart';
import '../enum/sync_info_type.dart';
import '../home/<USER>';
import '../settting/widget/switch.dart';
import '../widgets/info_sync_dialog.dart';
import '../widgets/base/halo_pos_alert_dialog.dart';
import '../widgets/halo_pos_label.dart';
import 'entity/login_record_dto.dart';
import 'entity/store/store_info.dart';
import 'model/login_user_model.dart';

///
///@ClassName: login_page
///@Description: 登录
///@Author: tanglanc
///@Date: 7/27/21 1:21 PM
class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() {
    return _LoginPageState();
  }
}

class _LoginPageState extends State<LoginPage> with WidgetsBindingObserver {
  final _LoginInputStateController _stateController = Get.put(
    _LoginInputStateController(),
  );

  String versionNumber = "";

  // bool privacyChecked = true;
  bool firstInstall = true;
  _LoginInputEntity loginInputData = _LoginInputEntity();
  Timer? _timer; // 倒计时的计时器。
  int _seconds = 0; // 倒计时时间，设置60秒
  String? ip;
  String? proxyAddress;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this); // 监听应用生命周期
    SecondaryScreenPlugin.showGoodsList(goods: [], total: "0", discount: "0");
    VersionCheck.getLocalVersionName().then(
      (value) => setState(() => versionNumber = value),
    );
    LoginUserModel? loginUser = LoginCenter.getLoginUser();

    if (!StringUtil.isEmpty(loginUser.company)) {
      loginInputData.company = loginUser.company;
      loginInputData.user = loginUser.user;
      loginInputData.pwd = loginUser.pwd;
      loginInputData.mobile = loginUser.mobile;
      loginInputData.isPhoneLogin = loginUser.isPhoneLogin;
      setLoginButtonEnable();
    } else {
      loginInputData.isPhoneLogin = true;
    }
    _stateController.setIsPhoneSelect(loginInputData.isPhoneLogin);
    bool firstInstall = LoginCenter.isFirstInstall() ?? true;

    _stateController.setOffLine(OffLineTool().isOfflineLogin);
    if (firstInstall) {
      WidgetsBinding.instance.addPostFrameCallback((callback) {
        HaloDialog(
          context,
          dismissOnTouchOutside: false,
          child: SafeTipWidget(),
        ).show();
      });
    }
    _stateController.setPrivacyCheck(!firstInstall);
    setLoginButtonEnable();
    bool isUnAutoLogin =
        StringUtil.isEmpty(loginUser.authorization) ||
        null == SpTool.getStoreInfo()?.otypeId ||
        null == SpTool.getCashierInfo().id;

    if (!isUnAutoLogin && SpTool.getAutoLogin()!) {
      autoLogin();
      return;
    }
  }

  @override
  void dispose() {
    if (null != _timer) {
      _timer?.cancel();
    }
    _seconds = 0;
    Get.reset();
    WidgetsBinding.instance.removeObserver(this); // 移除生命周期监听
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.detached) {
      // 应用终止时释放资源
      PerformanceCaptureUtil.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    Application.init(context);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        SystemNavigator.pop();
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: const Color(0xFFF9FAFB),
        bottomNavigationBar: Container(
          height: 80.h,
          alignment: Alignment.center,
          child: Text(
            "成都章鱼侠科技股份有限公司 版本 V$versionNumber",
            style: TextStyle(color: const Color(0xFF7E7E7E), fontSize: 22.w),
          ),
        ),
        body: HaloContainer(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          direction: Axis.vertical,
          padding: EdgeInsets.only(left: 60.w, right: 60.w, top: 52.w),
          children: [
            HaloContainer(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: ColorUtil.stringColor("#DDEDFF"),
                        offset: Offset(2.w, 4.w),
                        blurRadius: 6,
                      ),
                    ],
                  ),
                  child: Image(
                    image: const AssetImage("assets/images/logo.png"),
                    width: 66.w,
                    height: 66.w,
                  ),
                ),
                SizedBox(width: 20.w),
                Text(
                  "网上管家婆云零售",
                  style: TextStyle(
                    color: AppColorHelper(context).getAccentColor(),
                    fontSize: 38.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            Expanded(
              child: HaloContainer(
                direction: Axis.vertical,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Obx(
                    () => LoginSelectTab(
                      isPhoneSelect: _stateController.isPhoneSelect.value,
                      selectStyle: TextStyle(
                        fontSize: 32.sp,
                        color: AppColorHelper(context).getAccentColor(),
                      ),
                      unSelectStyle: TextStyle(
                        fontSize: 28.sp,
                        color: const Color(0xFF333333),
                      ),
                      selectedChange: (bool? isPhone) {
                        loginInputData.isPhoneLogin = isPhone;
                        _stateController.setIsPhoneSelect(isPhone);
                        setLoginButtonEnable();
                      },
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: HaloContainer(
                        direction: Axis.vertical,
                        children: [
                          buildAccountLogin(context),
                          buildPhoneLogin(context),
                          SizedBox(height: 56.w),
                          _buildLoginBtnView(),
                          _buildPrivacyWidget(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginBtnView() {
    return Obx(
      () => HaloContainer(
        mainAxisAlignment: MainAxisAlignment.center,
        margin: EdgeInsets.only(
          left: _stateController.isPhoneSelect.value ? 0 : 240.w,
        ),
        children: [
          HaloButton(
            width: 606.w,
            enabled: _stateController.loginEnable.value,
            buttonType: HaloButtonType.elevatedButton,
            backgroundColor: AppColorHelper(context).getAccentColor(),
            foregroundColor: const Color(0xFFFFFFFF),
            disabledBackgroundColor: const Color(0xffB9DBFD),
            disabledForegroundColor: const Color(0xFFFFFFFF),
            fontWeight: FontWeight.w500,
            fontSize: 30.sp,
            height: 80.w,
            text: "登录",
            onPressed: () {
              doLoginClick(context);
            },
          ),
          _buildOffLineView(),
        ],
      ),
    );
  }

  Widget _buildOffLineView() {
    return Obx(
      () => HaloContainer(
        padding: EdgeInsets.only(left: 20.w),
        direction: Axis.horizontal,
        width: 240.w,
        visible: !_stateController.isPhoneSelect.value,
        children: [
          HaloPosLabel(
            "离线登录",
            textStyle: TextStyle(fontSize: 24.sp, color: Colors.black),
          ),
          SizedBox(width: 10.w),
          HaloPosSwitch(
            value: _stateController.offline.value,
            width: 80.w,
            height: 60.h,
            onChanged: (value) {
              OffLineTool().isOfflineLogin = value;
              _stateController.setOffLine(value);
              _stateController.setOffLine(value);
            },
          ),
        ],
      ),
    );
  }

  Widget buildAccountLogin(BuildContext context) {
    return Obx(
      () => HaloContainer(
        direction: Axis.vertical,
        visible: !_stateController.isPhoneSelect.value,
        width: 606.w,
        children: [
          _buildRowInput(
            context,
            title: "公司名",
            iconName: IconNames.gongsiming,
            inputFormatter: [LengthLimitingTextInputFormatter(100)],
            value: loginInputData.company ?? "",
            onChange: (value) {
              loginInputData.company = value;
              setLoginButtonEnable();
            },
          ),

          _buildRowInput(
            context,
            title: "账号",
            iconName: IconNames.yonghuming,
            inputFormatter: [LengthLimitingTextInputFormatter(200)],
            value: loginInputData.user ?? "",
            onChange: (value) {
              loginInputData.user = value;

              setLoginButtonEnable();
            },
          ),

              ///ip
              _buildPasswordWidget(context),
              Visibility(
                visible: Application.isDebug,
                child: _buildRowInput(
                  context,
                  title: "IP地址",
                  value: ip ?? "",
                  iconName: IconNames.mima,
                  inputFormatter: [LengthLimitingTextInputFormatter(200)],
                  obscureText: false,
                  onChange: (value) {
                    ip = value;
                    if (null != ip && ip!.isNotEmpty) {
                      Application.debugAPIURL = "http://${ip!}:10041/sale/";
                    } else {
                      Application.debugAPIURL = "";
                    }
                  },
                ),
              ),
              Visibility(
                visible: Application.isDebug,
                child: _buildRowInput(
                  context,
                  title: "代理地址",
                  value: proxyAddress ?? "",
                  iconName: IconNames.mima,
                  inputFormatter: [LengthLimitingTextInputFormatter(200)],
                  obscureText: false,
                  onChange: (value) {
                    proxyAddress = value;
                    if (null != proxyAddress && proxyAddress!.isNotEmpty) {
                      Application.proxyAddress = proxyAddress;
                    } else {
                      Application.proxyAddress = "";
                    }
                  },
                ),
              ),

          ///图形验证码 密码录入错误3次录入
          _buildCheckCodeWidget(context),
        ],
      ),
    );
  }

  Widget buildPhoneLogin(BuildContext context) {
    return Obx(
      () => HaloContainer(
        direction: Axis.vertical,
        width: 606.w,
        visible: _stateController.isPhoneSelect.value,
        children: [
          _buildRowInput(
            context,
            title: "手机号",
            keyboardType: TextInputType.phone,
            iconName: IconNames.shoujihao_copy,
            inputFormatter: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(11),
            ],
            value: loginInputData.mobile ?? "",
            onChange: (value) {
              loginInputData.mobile = value;
              setLoginButtonEnable();
            },
          ),
          _buildRowInput(
            context,
            title: "验证码",
            value: loginInputData.smCode,
            iconName: IconNames.yanzhengma,
            keyboardType: TextInputType.number,
            inputFormatter: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(6),
            ],
            onChange: (value) {
              loginInputData.smCode = value;
              setLoginButtonEnable();
            },
            moreWidget: buildSmsCodeWidget(context),
          ),
        ],
      ),
    );
  }

  ///登录点击
  void doLoginClick(BuildContext context) async {
    FocusScope.of(context).requestFocus(FocusNode());

    // InfoSyn
    bool isValid = checkValid(context);

    if (!isValid) {
      return;
    }
    if (!_stateController.isPrivacyCheck.value) {
      HaloToast.show(context, msg: "请勾选《服务协议》与《隐私条款》");
      return;
    }
    if (OffLineTool().isOfflineLogin) {
      loginOffLine();
    } else {
      if (loginInputData.isPhoneLogin ?? false) {
        await LoginUtil.phoneLogin(
          context,
          phone: loginInputData.mobile ?? "",
          smsCode: loginInputData.smCode ?? "",
          requestSuccess: (companyList) async {
            if (companyList.length == 1) {
              await LoginUtil.loginByProfileId(
                context,
                companyDto: companyList.first,
                phone: loginInputData.mobile,
                loginSuccess: loginSuccess,
              );
              return;
            }
            if (companyList.length > 1) {
              HaloDialog(
                context,
                title: "选择登录公司",
                child: CompanySelect(companyList, (selectItem) async {
                  await LoginUtil.loginByProfileId(
                    context,
                    companyDto: selectItem,
                    phone: loginInputData.mobile,
                    loginSuccess: loginSuccess,
                  );
                }),
              ).show();
            }
          },
        );
      } else {
        await LoginUtil.accountLogin(
          context,
          company: loginInputData.company,
          userName: loginInputData.user,
          pwd: loginInputData.pwd,
          loginSuccess: loginSuccess,
          loginFail: (code) {
            //密码错误
            if (code == 1) {
              loginInputData.photoCode = "";
              _stateController.errorPwdCountAdd();
            }
          },
        );
      }
    }
  }

  ///离线登录
  void loginOffLine() async {
    List<LoginUserModel> logins = await OffLineTool().offlineLogin(
      context,
      company: loginInputData.company ?? "",
      user: loginInputData.user ?? "",
      pwd: loginInputData.pwd ?? "",
    );

    if (logins.isNotEmpty) {
      LoginUserModel loginUserModel = logins.first;
      await SpTool.saveLoginTime(DateTime.now().millisecondsSinceEpoch);
      SpTool.saveEtypeInfo("", "");
      SpTool.saveAutoLogin(false);

      if (!(SpTool.getModule()["storeFunc"] ?? false)) {
        if (context.mounted) {
          HaloToast.show(context, msg: "未开通云零售模块，请联系管理员!");
        }
        return;
      }
      SpTool.savePromotionList([]);
      await SpTool.saveLoginUser(loginUserModel);
      if (context.mounted) {
        NavigateUtil.navigateTo(context, const Root(), replace: true);
      }
    } else {
      if (context.mounted) {
        HaloToast.showMsg(
          context,
          msg: "无此用户离线数据或账号密码错误",
          gravity: ToastGravity.CENTER,
        );
      }
    }
  }

  bool checkValid(BuildContext context) {
    if (loginInputData.isPhoneLogin ?? false) {
      if (StringUtil.isEmpty(loginInputData.mobile)) {
        HaloToast.show(context, msg: StringRes.PHONE_INPUT.getText(context));
        return false;
      }
      if (StringUtil.isEmpty(loginInputData.smCode)) {
        HaloToast.show(context, msg: StringRes.SMS_CODE_INPUT.getText(context));
        return false;
      }
      return true;
    } else {
      if (StringUtil.isEmpty(loginInputData.company)) {
        HaloToast.show(context, msg: StringRes.COMPANY_INPUT.getText(context));
        return false;
      }
      if (StringUtil.isEmpty(loginInputData.user)) {
        HaloToast.show(context, msg: StringRes.ACCOUNT_INPUT.getText(context));
        return false;
      }
      if (StringUtil.isEmpty(loginInputData.pwd)) {
        HaloToast.show(context, msg: StringRes.PASSWORD_INPUT.getText(context));
        return false;
      }
      if (_stateController.errorPwdCount > 3) {
        if (StringUtil.trim(loginInputData.checkCode) !=
            loginInputData.photoCode) {
          HaloToast.show(
            context,
            msg: StringRes.ERROR_CHECK_CODE.getText(context),
          );
          return false;
        }
      }
      return true;
    }
  }

  ///登录成功
  Future<void> loginSuccess() async {
    await checkModuleAndVersion(
      afterCheck: () {
        selectStore();
      },
    );
  }

  ///验证是否开启云零售资产以及升级
  checkModuleAndVersion({Function? afterCheck}) async {
    ModuleVersion? moduleVersion =
        await ShopConfigModel.getModelSwitchAndVersion(context);
    if (!(moduleVersion?.module?["storeFunc"] ?? false)) {
      if (context.mounted) {
        HaloToast.show(context, msg: "未开通云零售模块，请联系管理员!");
      }
      return;
    }
    SpTool.saveModule(moduleVersion?.module);
    await SpTool.saveVersionInfo(moduleVersion?.versionInfo);
    PerformanceCaptureUtil.initialize(); // 初始化子线程和数据队列
    LoginUserModel loginUserModel = SpTool.getLoginUser();
    loginUserModel.userCode = moduleVersion?.employeeInfo?.usercode ?? "";
    SpTool.saveLoginUser(loginUserModel);
    if (context.mounted) {
      try {
        Platform.resolvedExecutable;
        await VersionCheck.checkVersionUpdate(
          context,
          moduleVersion?.versionInfo,
          onlyForce: true,
        );
      } catch (e) {
        HaloToast.show(context, msg: "升级验证失败，请联系管理员！");
      }
    }
    if (null != afterCheck) {
      afterCheck();
    }
  }

  ///选择门店
  Future<void> selectStore() async {
    List<StoreInfo> storeList = [];
    PerformanceCaptureUtil.start(PerformanceTimeName.storeSelect);
    try {
      storeList = await StoreModel.getStoreList(context);
    } on UnsupportedError catch (error) {
      HaloToast.show(context, msg: error.message);
      return;
    }
    PerformanceCaptureUtil.end(PerformanceTimeName.storeSelect);
    if (storeList.isEmpty) {
      if (context.mounted) {
        HaloPosAlertDialog.showAlertDialog(
          context,
          content: StringRes.TIP_NO_STORE.getText(context),
          showCancel: false,
        );
      }
      return;
    }
    if (context.mounted) {
      HaloDialog(
        context,
        child: StoreSelector(
          storeList: storeList,
          onSelected: () {
            showSyncDialog();
          },
        ),
      ).show();
    }
  }

  ///设置登录按钮是否可用
  void setLoginButtonEnable() {
    // if (!_stateController.isPrivacyCheck.value) {
    //   _stateController.setLoginEnable(false);
    //   return;
    // }
    if (loginInputData.isPhoneLogin ?? false) {
      bool isEnable =
          StringUtil.isNotEmpty(loginInputData.mobile) &&
          StringUtil.isNotEmpty(loginInputData.smCode);
      _stateController.setLoginEnable(isEnable);
      return;
    }

    bool isEnable =
        StringUtil.isNotEmpty(loginInputData.company) &&
        StringUtil.isNotEmpty(loginInputData.user) &&
        StringUtil.isNotEmpty(loginInputData.pwd);
    _stateController.setLoginEnable(isEnable);
  }

  ///构造图形验证码
  Widget _buildCheckCodeWidget(BuildContext context) {
    if (_stateController.errorPwdCount.value > 3 &&
        StringUtil.isEmpty(loginInputData.photoCode)) {
      loginInputData.photoCode = StringUtil.buildRandom(4);
      _stateController.setPhotoCode(loginInputData.photoCode!);
    }
    return Obx(
      () => Visibility(
        visible: _stateController.errorPwdCount.value > 3,
        child: _buildRowInput(
          context,
          iconName: IconNames.tuxingyanzhengma,
          title: StringRes.SMS_CODE_INPUT.getText(context),
          inputFormatter: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(4),
          ],
          value: loginInputData.checkCode,
          onChange: (value) {
            loginInputData.checkCode = value;
            // widget.onChange(loginInputData);
            setLoginButtonEnable();
          },
          moreWidget: GestureDetector(
            onTap: () {
              loginInputData.photoCode = StringUtil.buildRandom(4);
              _stateController.setPhotoCode(loginInputData.photoCode ?? "");
            },
            child: HaloCheckCode(code: _stateController.photoCode.value),
          ),
        ),
      ),
    );
  }

  ///构建发送验证码组件
  Widget buildSmsCodeWidget(BuildContext context) {
    return Obx(
      () => HaloButton(
        buttonType: HaloButtonType.textButton,
        text: _stateController.smsCode.value,
        height: 48.w,
        enabled: _stateController.smsEnable.value,
        fontSize: 28.sp,
        foregroundColor: AppColorHelper(context).getAccentColor(),
        onPressed: () {
          if (loginInputData.mobile != null) {
            LoginUtil.doSendCode(
              context,
              phone: loginInputData.mobile,
              afterSend: () {
                _startTimer();
              },
            );
          } else {
            HaloToast.showError(context, msg: "请输入手机号");
          }
        },
      ),
    );
  }

  /// 启动倒计时的计时器。
  void _startTimer() {
    // 计时器（`Timer`）组件的定期（`periodic`）构造函数，创建一个新的重复计时器。
    _seconds = 60;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_seconds == 0) {
        timer.cancel();
        _stateController.setSmsEnable(true);
        _stateController.setSmsCode('重新发送');
      } else {
        _seconds--;
        _stateController.setSmsEnable(false);
        _stateController.setSmsCode('已发送${_seconds}s');
      }
    });
  }

  ///构造行组件
  Widget _buildRowInput(
    BuildContext context, {
    String? title,
    String? value,
    Widget? moreWidget,
    bool obscureText = false,
    TextInputType? keyboardType,
    IconNames? iconName,
    List<TextInputFormatter>? inputFormatter,
    Function(String?)? onChange,
  }) {
    return FocusTextField(
      title: title,
      value: value ?? "",
      textStyle: TextStyle(color: const Color(0xFF333333), fontSize: 28.sp),
      padding: EdgeInsets.symmetric(horizontal: 26.w, vertical: 12.w),
      borderRadius: const BorderRadius.all(Radius.circular(4)),
      border: Border.all(color: const Color(0xFFC5C9CA), width: 1),
      keyboardType: keyboardType,
      obscureText: obscureText,
      inputFormatter: inputFormatter,
      labelWidget: IconFont(iconName, size: 30.w, color: "#6C6C6C"),
      margin: EdgeInsets.symmetric(vertical: 12.w),
      onChange: (value) {
        if (null != onChange) {
          onChange(value);
        }
      },
      rightWidget: moreWidget,
    );
  }

  ///构造密码组件
  Widget _buildPasswordWidget(BuildContext context) {
    return _buildRowInput(
      context,
      title: "密码",
      value: loginInputData.pwd,
      iconName: IconNames.mima,
      inputFormatter: [LengthLimitingTextInputFormatter(200)],
      obscureText: _stateController.obscureText.value,
      onChange: (value) {
        loginInputData.pwd = value;

        // widget.onChange(loginInputData);
        setLoginButtonEnable();
        _stateController.setShowObscure(StringUtil.isNotEmpty(value));
      },
      moreWidget: Visibility(
        visible: _stateController.showObscureText.value,
        child: GestureDetector(
          onTap: () {
            _stateController.setObscureText(
              !_stateController.obscureText.value,
            );
          },
          child: IconFont(
            _stateController.obscureText.value
                ? IconNames.yanjing_guan
                : IconNames.yanjing_kai,
            size: 20.w,
          ),
        ),
      ),
    );
  }

  //构造隐私协议
  Widget _buildPrivacyWidget() {
    return HaloContainer(
      width: 606.w,
      direction: Axis.horizontal,
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      margin: EdgeInsets.only(top: 27.w),
      children: [
        Obx(
          () => HaloCheckBox(
            defaultImage: IconFont(
              IconNames.square,
              color: "#D5D6D7",
              size: 36.w,
            ),
            selectedImage: IconFont(
              IconNames.squarecheck,
              size: 36.w,
              color: "#2288FC",
            ),
            value: _stateController.isPrivacyCheck.value,
            onChanged: onPrivacyChange,
          ),
        ),
        SizedBox(width: 14.w),
        RichText(
          text: TextSpan(
            text: "我已阅读并同意",
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w500,
              color: AppColorHelper(context).getSubTitleTextColor(),
            ),
            recognizer:
                TapGestureRecognizer()
                  ..onTap = () {
                    onPrivacyChange(_stateController.isPrivacyCheck.value);
                  },
            children: [
              TextSpan(
                text: "《服务协议》",
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                ),
                recognizer:
                    TapGestureRecognizer()
                      ..onTap = () {
                        if (Platform.isWindows) {
                          launchUrl(Uri.parse(RequestURL.HALO_APP_SERVICE_URL));
                          return;
                        }
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            settings: const RouteSettings(name: "1"),
                            builder: (context) {
                              return HaloWebViewWidget(
                                url: RequestURL.HALO_APP_SERVICE_URL,
                                title: "服务协议",
                              );
                            },
                          ),
                        );
                      },
              ),
              TextSpan(
                text: "与",
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColorHelper(context).getSubTitleTextColor(),
                ),
              ),
              TextSpan(
                text: "《隐私条款》",
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                ),
                recognizer:
                    TapGestureRecognizer()
                      ..onTap = () {
                        if (Platform.isWindows) {
                          launchUrl(Uri.parse(RequestURL.HALO_APP_PRIVACY_URL));
                          return;
                        }
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            settings: const RouteSettings(name: "1"),
                            builder: (context) {
                              return HaloWebViewWidget(
                                url: RequestURL.HALO_APP_PRIVACY_URL,
                                title: "隐私政策",
                              );
                            },
                          ),
                        );
                      },
              ),
            ],
          ),
        ),
      ],
    );
  }

  void onPrivacyChange(bool check) {
    _stateController.setPrivacyCheck(!check);
    setLoginButtonEnable();
  }

  ///自动登录
  void autoLogin() async {
    await checkModuleAndVersion(
      afterCheck: () {
        showSyncDialog(isAutoLogin: true);
      },
    );
  }

  ///开启定时同步库存任务
  void startSyncStockTimer() {
    final config = SpTool.getDatabaseConfig();
    if (config.enableLocalPtype && config.autoSyncStock) {
      StockSyncTimer.start();
    }
  }

  ///弹出更新信息弹出
  void showSyncDialog({bool isAutoLogin = false}) {
    List<SyncInfoType> syncList = [
      SyncInfoType.storeInfo,
      SyncInfoType.permissionConfig,
      SyncInfoType.promotion,
      SyncInfoType.scoreConfiguration,
      SyncInfoType.printConfiguration,
    ];
    DatabaseConfig setting = SpTool.getDatabaseConfig();
    if (setting.enableLocalPtype && setting.loginSync && !isAutoLogin) {
      syncList.add(SyncInfoType.pType);
    }
    HaloDialog(
      context,
      child: InfoSyncDialog(
        context,
        sycInfoList: syncList,
        isAutoClose: true,
        afterSync: () async {
          await afterSyncMessage(isAutoLogin: isAutoLogin);
        },
      ),
    ).show();
  }

  ///信息更新完成执行操作
  afterSyncMessage({bool isAutoLogin = false}) async {
    if (!isAutoLogin) {
      await SpTool.saveAutoLogin(true);
      SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
      LoginUserModel? loginUser = SpTool.getLoginUser();
      loginUser.etypeName = systemConfigDto.userInfo!.fullname;
      await SpTool.saveLoginUser(loginUser);
      LoginUserDBManager.insertLoginUser(
        company: loginUser.company ?? "",
        user: loginUser.user ?? "",
        pwd: loginInputData.pwd ?? "",
        json: jsonEncode(loginUser.toJson()),
      );
    }
    if (context.mounted) {
      //保存登陆时间
      ///feat 31098
      int loginTime = DateTime.now().millisecondsSinceEpoch;
      LoginRecordDto? dto = await StoreModel.getLoginRecord(context);
      if (dto != null) {
        loginTime = DateUtil.getDateTime(dto.loginTime)!.millisecondsSinceEpoch;
      }
      await SpTool.saveLoginTime(loginTime);
      SpTool.saveEtypeInfo("", "");
      if (context.mounted) {
        startSyncStockTimer();
        SpCustomUtil.backupPreferences();
        NavigateUtil.navigateTo(
          context,
          VersionCheck.getVersionRootWidget(),
          replace: true,
        );
      }
    }
  }
}

///GetX状态管理控制显示
class _LoginInputStateController extends GetxController {
  final obscureText = true.obs; //密文显示
  final showObscureText = false.obs; //是否显示控制密文明文操作

  var photoCode = "".obs; //图形验证码
  var isPhoneSelect = true.obs;
  var smsCode = "获取验证码".obs;
  var smsEnable = true.obs;
  var errorPwdCount = 0.obs; //密码错误次数
  var loginEnable = false.obs;
  var isPrivacyCheck = false.obs;
  var offline = false.obs;

  setObscureText(bool isObscureText) {
    obscureText.value = isObscureText;
  }

  setIsPhoneSelect(bool? phoneSelect) {
    isPhoneSelect.value = phoneSelect ?? true;
  }

  setShowObscure(bool showObscure) {
    showObscureText.value = showObscure;
  }

  setPhotoCode(String code) {
    photoCode.value = code;
  }

  setSmsCode(String msg) {
    smsCode.value = msg;
  }

  setSmsEnable(bool enable) {
    smsEnable.value = enable;
  }

  setLoginEnable(bool enable) {
    loginEnable.value = enable;
  }

  setPrivacyCheck(bool check) {
    isPrivacyCheck.value = check;
  }

  errorPwdCountAdd() => errorPwdCount++;

  setOffLine(bool enable) {
    offline.value = enable;
  }
}

class _LoginInputEntity extends LoginUserModel {
  String? checkCode;
  String? photoCode;
  String? smCode;
}
