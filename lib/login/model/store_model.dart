import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/utils/String_util.dart';

import '../../../common/net/http_util.dart';
import '../../../common/net/request_method.dart';
import '../../../login/entity/store/store_info.dart';
import '../../common/login/login_center.dart';
import '../../common/tool/sp_tool.dart';
import '../../entity/print/print_config_info.dart';
import '../../print/tool/print_config.dart';
import '../entity/login_record_dto.dart';
import '../tool/login_util.dart';

///
///@ClassName: store_model
///@Description: 类作用描述
///@Author: tanglan
///@Date: 11/18/21 3:27 PM
class StoreModel {
  static Future<List<StoreInfo>> getStoreList(BuildContext context) async {
    String url =
        "${RequestMethod.GET_STORE_LIST}/${LoginCenter.getLoginUser()?.employeeId}";
    ResponseModel response = await HttpUtil.request(
      context,
      method: url,
      requestModel: NetMethod.GET,
    );
    if (response.code! < 0 && null == response.data ||
        0 == response.data.length) {
      return [];
    }
    List? data = response.data;
    return data?.map((o) => StoreInfo.fromMap(o)).toList() ?? [];
  }

  static Future<StoreInfo> getStoreInfo(
    BuildContext context,
    String? otypeId, {
    bool isLoading = true,
  }) async {
    ResponseModel response = await HttpUtil.request(
      context,
      isLoading: isLoading,
      method: RequestMethod.getUsingFullStoreInfo,
      data: otypeId,
    );

    return StoreInfo.fromMap(response.data);
  }

  ///获取门店信息（不排除非登录门店，已停用门店，用于弹出提示展示）
  static Future<ResponseModel> getStoreInfoById(
    BuildContext context,
    String? otypeId, {
    bool isLoading = true,
    Function(dynamic error)? errorCallBack,
  }) async {
    ResponseModel response = await HttpUtil.request(
      context,
      isLoading: isLoading,
      method: RequestMethod.getFullStoreInfo,
      data: otypeId,
      onErrorCallBack: (error) {
        if (null != errorCallBack) {
          errorCallBack(error);
        }
        return false;
      },
    );

    return response;
  }

  ///获取门店信息（不排除非登录门店，已停用门店，用于弹出提示展示）
  static Future<StoreInfo> getStoreInfoContainsStopAndUnLogin(
    BuildContext context,
    String? otypeId, {
    bool isLoading = true,
  }) async {
    ResponseModel response = await HttpUtil.request(
      context,
      isLoading: isLoading,
      method: RequestMethod.getFullStoreInfo,
      data: otypeId,
    );

    return StoreInfo.fromMap(response.data);
  }

  static Future<LoginRecordDto?> getLoginRecord(BuildContext context) async {
    LoginRecordDto loginRecordDto = LoginRecordDto();
    loginRecordDto.otypeId = SpTool.getStoreInfo()!.otypeId!;
    loginRecordDto.cashierId = SpTool.getCashierInfo().id!;
    loginRecordDto.clientType = Platform.isWindows ? 1 : 0;
    loginRecordDto.shiftChanged = 0;
    loginRecordDto.loginTime = DateUtil.getNowDateStr();
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.insertLoginRecord,
      data: loginRecordDto.toJson(),
    );
    return LoginRecordDto.fromJson(response.data);
  }

  static Future<bool> updateLoginRecord(BuildContext context) async {
    LoginRecordDto loginRecordDto = LoginRecordDto();
    loginRecordDto.otypeId = SpTool.getStoreInfo()!.otypeId!;
    loginRecordDto.cashierId = SpTool.getCashierInfo().id!;
    loginRecordDto.loginTime = "";
    loginRecordDto.clientType = Platform.isWindows ? 1 : 0;
    loginRecordDto.shiftChanged = 0;
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.updateAllLoginRecordShiftChanged,
      data: loginRecordDto.toJson(),
    );
    return response.data;
  }

  static Future<void> savePrintConfig(
    BuildContext context,
    List<PrintConfigInfo> printConfigInfoList,
  ) async {
    clearPrintImageContent(printConfigInfoList);
    await HttpUtil.request(
      context,
      method: RequestMethod.savePrintConfig,
      data: printConfigInfoList.map((e) => e.toJson()).toList(),
    );
  }

  ///清除配置项中前端用于存储图片内容的数据，减少接口数据量大问题，目前仅处理票头/票尾图片
  static void clearPrintImageContent(List<PrintConfigInfo> printFields) {
    for (PrintConfigInfo item in printFields) {
      ///当前仅票头/票尾会处理图片
      if (StringUtil.isNotEmpty(item.imgBytesContent)) {
        item.imgBytesContent = "";
        return;
      }
    }
  }

  static Future<List<PrintConfigInfo>> getPrintConfigList(
    BuildContext context, {
    bool isLoading = true,
  }) async {
    String? cashierId = SpTool.getCashierInfo().id;
    ResponseModel response = await HttpUtil.request(
      context,
      isLoading: isLoading,
      method: RequestMethod.getPrintConfig,
      data: cashierId,
    );

    List? data = response.data;
    return data?.map((o) => PrintConfigInfo.fromMap(o)).toList() ?? [];
  }
}
