///
///@ClassName: company_dto
///@Description: 
///@Author: tanglan
///@Date: 2023/3/15
class CompanyDto {
  String? companyName;
  bool? expire;
  String? loginUserName;
  dynamic password;
  String? profileId;
  int? productId;
  String? etypeId;
  String? registerdate;
  String? sign;
  dynamic loginPhone;

  static CompanyDto fromMap(Map<String, dynamic> map) {
    CompanyDto companyDtoBean = CompanyDto();
    companyDtoBean.companyName = map['companyName'];
    companyDtoBean.expire = map['expire'];
    companyDtoBean.loginUserName = map['loginUserName'];
    companyDtoBean.password = map['password'];
    companyDtoBean.profileId = map['profileId'];
    companyDtoBean.productId = map['productId'];
    companyDtoBean.etypeId = map['etypeId'];
    companyDtoBean.registerdate = map['registerdate'];
    companyDtoBean.sign = map['sign'];
    companyDtoBean.loginPhone = map['loginPhone'];
    return companyDtoBean;
  }

  Map toJson() => {
    "companyName": companyName,
    "expire": expire,
    "loginUserName": loginUserName,
    "password": password,
    "profileId": profileId,
    "productId": productId,
    "etypeId": etypeId,
    "registerdate": registerdate,
    "sign": sign,
    "loginPhone": loginPhone,
  };
}