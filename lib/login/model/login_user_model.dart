///
///@ClassName: login_user_model
///@Description: 登录用户
///@Author: tanglan
///@Date: 2023/3/15//
///
///
class LoginUserModel {
  String? company;
  String? user;
  String? loginName;
  String? etypeName;
  String? userCode;
  String? pwd;
  String? mobile;
  bool? isPhoneLogin;
  int? productId;
  String? authorization;
  String? requestUrl;
  String? profileId;
  String? employeeId;
  String? deploy;
  bool? redisWrited;
  String? serverId;

  String? redisKey;
  bool? adminStatus;
  String? ngpSessionId;
  String? loginType;
  bool? greyFlag;
  bool? testFlag;
  bool? demoFlag;
  String? startTime;
  String? endTime;
  String? startDate;
  String? endDate;
  int? weekDays;

  static LoginUserModel fromMap(Map<String?, dynamic> map) {
    LoginUserModel loginUser = LoginUserModel();
    loginUser.company = map['company'];
    loginUser.user = map['user'];
    loginUser.loginName = map['loginName'];
    loginUser.etypeName = map['etypeName'];
    loginUser.pwd = map['pwd'];
    loginUser.mobile = map['mobile'];
    loginUser.isPhoneLogin = map['isPhoneLogin'];
    loginUser.productId = map['productId'];
    loginUser.authorization = map['authorization'];
    loginUser.requestUrl = map['requestUrl'];
    loginUser.profileId = map['profileId'];
    loginUser.employeeId = map['employeeId'];
    loginUser.deploy = map['deploy'];
    loginUser.redisWrited = map['redisWrited'];
    loginUser.serverId = map['serverId'];
    loginUser.redisKey = map['redisKey'];
    loginUser.adminStatus = map['adminStatus'];
    loginUser.ngpSessionId = map['ngpSessionId'];
    loginUser.loginType = map['loginType'];
    loginUser.greyFlag = map['greyFlag'];
    loginUser.testFlag = map['testFlag'];
    loginUser.demoFlag = map['demoFlag'];
    loginUser.startTime = map['startTime'];
    loginUser.endTime = map['endTime'];
    loginUser.endDate = map['endDate'];
    loginUser.weekDays = map['weekDays'];
    loginUser.userCode = map['userCode'];
    return loginUser;
  }

  Map toJson() => {
        "company": company,
        "user": user,
        "loginName": loginName,
        "pwd": pwd,
        "mobile": mobile,
        "isPhoneLogin": isPhoneLogin,
        "productId": productId,
        "authorization": authorization,
        "requestUrl": requestUrl,
        "profileId": profileId,
        "employeeId": employeeId,
        "deploy": deploy,
        "redisWrited": redisWrited,
        "serverId": serverId,
        "redisKey": redisKey,
        "adminStatus": adminStatus,
        "ngpSessionId": ngpSessionId,
        "loginType": loginType,
        "greyFlag": greyFlag,
        "testFlag": testFlag,
        "demoFlag": demoFlag,
        "startTime": startTime,
        "endTime": endTime,
        "endDate": endDate,
        "weekDays": weekDays,
        "etypeName": etypeName,
        "userCode": userCode
      };
}
