
class LoginRecordDto{
  late String otypeId;
  String etypeId = "0";
  late String cashierId;
  ///登录时间
  late String loginTime;
  ///0=未交接班  1=交接班
  late int shiftChanged;
  ///"0=安卓 1=windows"
  late int clientType;

  LoginRecordDto._();

  factory LoginRecordDto(){
    return LoginRecordDto._();
  }

  LoginRecordDto.fromJson(Map<String, dynamic> map){
    otypeId = map["otypeId"];
    etypeId = map["etypeId"]??0;
    cashierId = map["cashierId"];
    loginTime = map["loginTime"];
    shiftChanged = map["shiftChanged"]??0;
    clientType = map["clientType"]??0;
  }
  Map toJson() => {
    "otypeId": otypeId,
    "etypeId": etypeId,
    "cashierId": cashierId,
    "loginTime": loginTime,
    "shiftChanged": shiftChanged,
    "clientType": clientType,
  };
}