///
///@ClassName: store_cashier
///@Description: 门店收银台
///@Author: tanglan
///@Date: 11/19/21 11:33 AM

/// createTime : "2021-11-19T03:30:57.049Z"
/// deleted : true
/// fullname : "string"
/// id : 0
/// modifyTime : "2021-11-19T03:30:57.049Z"
/// otypeId : 0
/// profileId : 0
/// stoped : true
/// usercode : "string"

class StoreCashier {
  String? createTime;
  bool? deleted;
  String? fullname;
  String? id;
  String? modifyTime;
  String? otypeId;
  String? profileId;
  bool? stoped;
  String? usercode;

  static StoreCashier fromMap(Map<String, dynamic> map) {
    StoreCashier cashiersBean = StoreCashier();
    cashiersBean.createTime = map['createTime'];
    cashiersBean.deleted = map['deleted'];
    cashiersBean.fullname = map['fullname'];
    cashiersBean.id = map['id'];
    cashiersBean.modifyTime = map['modifyTime'];
    cashiersBean.otypeId = map['otypeId'];
    cashiersBean.profileId = map['profileId'];
    cashiersBean.stoped = map['stoped'];
    cashiersBean.usercode = map['usercode'];
    return cashiersBean;
  }

  Map toJson() => {
    "createTime": createTime,
    "deleted": deleted,
    "fullname": fullname,
    "id": id,
    "modifyTime": modifyTime,
    "otypeId": otypeId,
    "profileId": profileId,
    "stoped": stoped,
    "usercode": usercode,
  };
}