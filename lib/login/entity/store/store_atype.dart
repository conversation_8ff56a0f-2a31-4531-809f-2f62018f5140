///
///@ClassName: store_atype
///@Description: 门店账户列表
///@Author: tanglan
///@Date: 11/19/21 11:32 AM


/// atypeId : 0
/// atypeName : "string"
/// id : 0
/// otypeId : 0
/// profileId : 0

class StoreAtype {
  String? atypeId;
  String? atypeName;
  String? id;
  String? otypeId;
  String? profileId;

  static StoreAtype fromMap(Map<String, dynamic> map) {
    StoreAtype multiAtypesBean = StoreAtype();
    multiAtypesBean.atypeId = map['atypeId'];
    multiAtypesBean.atypeName = map['atypeName'];
    multiAtypesBean.id = map['id'];
    multiAtypesBean.otypeId = map['otypeId'];
    multiAtypesBean.profileId = map['profileId'];
    return multiAtypesBean;
  }

  Map toJson() => {
    "atypeId": atypeId,
    "atypeName": atypeName,
    "id": id,
    "otypeId": otypeId,
    "profileId": profileId,
  };
}
