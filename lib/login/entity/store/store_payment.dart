///
///@ClassName: store_payment
///@Description:
///@Author: tanglan
///@Date: 2022/12/16
class StorePayway {
  String? id;
  String? profileId;
  String? otypeId;
  String? paywayId;
  String? paywayName; //支付方式名称"
  int? _paywayType; //"支付方式类型：0=现金，1=银行，2=淘淘谷，3=预存款，5=通企付，6=淘淘谷，7=汇付"
  String? atypeId; //收款账户id
  String? atypeFullname; //收款账户名称
  int? payType; //聚合支付支付类型：1 淘淘谷
  int? product; //支付产品，如淘淘谷扫码支付
  int? platformPaytype; //支付平台：1=淘淘谷，4=汇付
  String? content; //聚合支付支付配置内容

  /// 获取支付方式类型，将新的聚合支付类型(5,6,7)统一转换为2|| platformPaytype! >= 0
  /// 这样保持与现有代码的兼容性
  int? get paywayType {
    if ( _paywayType == 5 || _paywayType == 6 || _paywayType == 7 ) {
      return 2; // 统一返回2，保持兼容性
    }
    return _paywayType;
  }

  /// 设置支付方式类型
  set paywayType(int? value) {
    _paywayType = value;
  }

  /// 获取原始的支付方式类型（如果将来需要区分不同的聚合支付类型）
  int? get originalPaywayType => _paywayType;

  static StorePayway fromMap(Map<String, dynamic> map) {
    StorePayway storePayment = StorePayway();
    storePayment.id = map['id'];
    storePayment.profileId = map['profileId'];
    storePayment.otypeId = map['otypeId'];
    storePayment.paywayId = map['paywayId'];
    storePayment.paywayName = map['paywayName'];
    storePayment._paywayType = map['paywayType']; // 直接设置私有字段
    storePayment.atypeId = map['atypeId'];
    storePayment.atypeFullname = map['atypeFullname'];
    storePayment.payType = map['payType'];
    storePayment.product = map['product'];
    storePayment.content = map['content'];
    storePayment.platformPaytype = map['platformPaytype']??0;
    return storePayment;
  }

  Map toJson() => {
    "id": id,
    "profileId": profileId,
    "otypeId": otypeId,
    "paywayId": paywayId,
    "paywayName": paywayName,
    "paywayType": _paywayType, // 序列化时使用原始值
    "atypeId": atypeId,
    "atypeFullname": atypeFullname,
    "payType": payType,
    "product": product,
    "content": content,
    "platformPaytype": platformPaytype,
  };
}
