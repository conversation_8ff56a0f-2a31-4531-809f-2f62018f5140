///
///@ClassName: barcode_scale_type_enum
///@Description:
///@Author: tanglan
///@Date: 2024/4/22
enum BarcodeScaleTypeEnum {
  ///条码输出格式（0=18位重量+金额码；1=18位金额+重量码；2=13位重量码;3=13位金额码；）
  // int outputFormatType = 0;
  ///0=18位重量+金额码
  weightAndTotal,

  ///1=18位金额+重量码
  totalAndWeight,

  ///2=13位重量码
  onlyWeight,

  ///3=13位金额码
  onlyTotal
}

class BarcodeScaleTypeTool {
  ///条码扫码成 条码配置18位
  static bool isBarcodeScale18(int? config) {
    if (null == config) {
      return false;
    }
    return config == BarcodeScaleTypeEnum.weightAndTotal.index ||
        config == BarcodeScaleTypeEnum.totalAndWeight.index;
  }

  ///条码扫码成 条码配置13位
  static bool isBarcodeScale13(int? config) {
    if (null == config) {
      return false;
    }
    return config == BarcodeScaleTypeEnum.onlyWeight.index ||
        config == BarcodeScaleTypeEnum.onlyTotal.index;
  }

  ///优先金额位
  static bool isFirstTotal(int? config) {
    if (null == config) {
      return false;
    }
    return config == BarcodeScaleTypeEnum.onlyTotal.index ||
        config == BarcodeScaleTypeEnum.totalAndWeight.index;
  }

  ///优先重量位
  static bool isFirstWeight(int? config) {
    if (null == config) {
      return false;
    }
    return config == BarcodeScaleTypeEnum.onlyWeight.index ||
        config == BarcodeScaleTypeEnum.weightAndTotal.index;
  }
}
