///
///@ClassName: store_etype
///@Description: 
///@Author: tanglan
///@Date: 2022/1/13

class StoreEtype {
  String? etypeId;
  String? etypeName;
  String? id;
  String? otypeId;
  String? profileId;
  String? usercode;

  static StoreEtype fromMap(Map<String, dynamic> map) {
    StoreEtype storeEtype = StoreEtype();
    storeEtype.etypeId = map['etypeId'];
    storeEtype.etypeName = map['etypeName'];
    storeEtype.id = map['id'];
    storeEtype.otypeId = map['otypeId'];
    storeEtype.profileId = map['profileId'];
    storeEtype.usercode=map['usercode'];
    return storeEtype;
  }

  Map toJson() => {
    "etypeId": etypeId,
    "etypeName": etypeName,
    "id": id,
    "otypeId": otypeId,
    "profileId": profileId,
    "usercode":usercode
  };
}