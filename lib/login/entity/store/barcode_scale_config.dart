///
///@ClassName: BarcodeScaleConfig
///@Description: 门店条码称配置
///@Author: tangLan
///@Date: 2024/4/18
class BarcodeScaleConfig {
  String? id;

  ///门店id
  String? otypeId;

  ///条码输出格式（0=18位重量+金额码；2=18位金额+重量码；3=13位重量码;4=13位金额码；）
  int outputFormatType = 0;

  ///标识位数(支持1 位/2 位)
  int markDigit = 1;

  ///金额小数位数(支持1 位/2 位)
  int totalDigit = 2;

  ///重量小数位数(支持1 位/2 位)
  int weightDigit = 2;

  String? profileId;
  int? deleted;
  String? createTime;
  String? updateTime;

  static BarcodeScaleConfig? fromMap(Map<String, dynamic>? map) {
    if (null == map) {
      return null;
    }
    BarcodeScaleConfig cashiersBean = BarcodeScaleConfig();
    cashiersBean.createTime = map['createTime'];
    cashiersBean.outputFormatType = map['outputFormatType'];
    cashiersBean.id = map['id'];

    cashiersBean.updateTime = map['updateTime'];
    cashiersBean.otypeId = map['otypeId'];
    cashiersBean.profileId = map['profileId'];
    cashiersBean.markDigit = map['markDigit'];
    cashiersBean.totalDigit = map['totalDigit'];
    cashiersBean.weightDigit = map['weightDigit'];
    return cashiersBean;
  }

  Map toJson() => {
        "createTime": createTime,
        "outputFormatType": outputFormatType,
        "id": id,
        "updateTime": updateTime,
        "otypeId": otypeId,
        "profileId": profileId,
        "markDigit": markDigit,
        "totalDigit": totalDigit,
        "weightDigit": weightDigit,
      };
}
