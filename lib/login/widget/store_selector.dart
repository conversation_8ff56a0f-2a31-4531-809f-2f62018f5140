import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import '../../../common/login/login_center.dart';
import '../../../common/string_res.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../login/entity/store/store_cashier.dart';
import '../../../login/entity/store/store_info.dart';
import '../../../login/model/store_model.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/utils/color_util.dart';

///
///@ClassName: store_selector
///@Description: 类作用描述
///@Author: tanglan
///@Date: 11/18/21 5:22 PM
///

class StoreSelector extends StatefulWidget {
  final List<StoreInfo>? storeList;
  final Function? onSelected;

  const StoreSelector({Key? key, this.storeList, this.onSelected})
    : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _StoreSelectorState();
  }
}

class _StoreSelectorState extends State<StoreSelector> {
  final _StoreSelectController _storeSelectController = Get.put(
    _StoreSelectController(),
  );

  final GlobalKey _storeGlobalKey = GlobalKey(); //门店选择框key
  final GlobalKey _cashierGlobalKey = GlobalKey(); //收银机选择框key

  StoreCashier? _selectStoreCashier;

  StoreInfo? _selectStoreInfo;

  @override
  void initState() {
    _selectStoreInfo = SpTool.getStoreInfo();
    _selectStoreCashier = SpTool.getCashierInfo();
    if (null == _selectStoreInfo ||
        null == _selectStoreInfo!.otypeId ||
        !widget.storeList!.any(
          (item) => item.otypeId == _selectStoreInfo!.otypeId,
        )) {
      _selectStoreInfo = widget.storeList!.first;
    }
    _loadFullStoreInfo(_selectStoreInfo!.otypeId);
    super.initState();
  }

  @override
  void dispose() {
    HaloPopWindow().disMiss();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.transparent,
      body: HaloContainer(
        mainAxisSize: MainAxisSize.max,
        margin: EdgeInsets.only(top: 200.h),
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          HaloContainer(
            width: 700.w,
            height: 550.h,
            color: Colors.white,
            direction: Axis.vertical,
            padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 32.h),
            borderRadius: BorderRadius.all(Radius.circular(10.w)),
            children: [
              Text(
                StringRes.STORE_SELECTOR.getText(context),
                style: TextStyle(
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  fontWeight: FontWeight.w500,
                  fontSize: 34.sp,
                ),
              ),
              HaloContainer(
                direction: Axis.vertical,
                crossAxisAlignment: CrossAxisAlignment.start,
                margin: EdgeInsets.only(top: 24.h),
                children: [
                  HaloContainer(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text(
                        "门店    ",
                        style: TextStyle(
                          color: AppColors.normalFontColor,
                          fontSize: AppPosSize.firstTitleFontSize.sp,
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            _showStorePopWindow(context);
                          },
                          behavior: HitTestBehavior.opaque,
                          child: HaloContainer(
                            key: _storeGlobalKey,
                            mainAxisSize: MainAxisSize.max,
                            padding: EdgeInsets.symmetric(
                              horizontal: 24.w,
                              vertical: 16.h,
                            ),
                            margin: EdgeInsets.only(left: 16.w),
                            borderRadius: BorderRadius.all(
                              Radius.circular(6.w),
                            ),
                            border: Border.all(color: AppColors.borderColor),
                            children: [
                              Expanded(
                                child: Obx(
                                  () => Text(
                                    _storeSelectController.storeName.value,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color:
                                          AppColorHelper(
                                            context,
                                          ).getTitleBoldTextColor(),
                                      fontWeight: FontWeight.w500,
                                      fontSize:
                                          AppPosSize.firstTitleFontSize.sp,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 16.w),
                              IconFont(IconNames.guolvxiala, size: 12.w),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 32.h),
                  HaloContainer(
                    mainAxisSize: MainAxisSize.max,
                    margin: EdgeInsets.only(top: 24.h),
                    children: [
                      Text(
                        "收银机",
                        style: TextStyle(
                          color: AppColors.normalFontColor,
                          fontSize: AppPosSize.firstTitleFontSize.sp,
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            _showCashierPopWindow(context);
                          },
                          behavior: HitTestBehavior.opaque,
                          child: HaloContainer(
                            key: _cashierGlobalKey,
                            mainAxisSize: MainAxisSize.max,
                            padding: EdgeInsets.symmetric(
                              horizontal: 24.w,
                              vertical: 16.h,
                            ),
                            margin: EdgeInsets.only(left: 16.w),
                            borderRadius: BorderRadius.all(
                              Radius.circular(6.w),
                            ),
                            border: Border.all(color: AppColors.borderColor),
                            children: [
                              Expanded(
                                child: Obx(
                                  () => Text(
                                    _storeSelectController.cashierName.value,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color:
                                          AppColorHelper(
                                            context,
                                          ).getTitleBoldTextColor(),
                                      fontWeight: FontWeight.w500,
                                      fontSize: 26.sp,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 16.w),
                              IconFont(IconNames.guolvxiala, size: 12.w),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              Expanded(
                child: HaloContainer(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  margin: EdgeInsets.only(top: 24.h, bottom: 24.h),
                  children: [
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          doSave(context);
                        },
                        child: Container(
                          height: 80.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColorHelper(context).getAccentColor(),
                            borderRadius: BorderRadius.all(
                              Radius.circular(6.w),
                            ),
                          ),
                          child: Text(
                            "确定",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 28.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 28.w),
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          LoginCenter.loginOut(context);
                          NavigateUtil.pop(context);
                        },
                        child: Container(
                          height: 80.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(
                              Radius.circular(6.w),
                            ),
                            border: Border.all(
                              color: AppColors.btnBorderColor,
                              width: 1.w,
                            ),
                          ),
                          child: Text(
                            "取消",
                            style: TextStyle(
                              color:
                                  AppColorHelper(
                                    context,
                                  ).getTitleBoldTextColor(),
                              fontSize: 26.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  //门店选择列表
  _showStorePopWindow(BuildContext context) {
    HaloPopWindow().show(
      _storeGlobalKey,
      gravity: PopWindowGravity.bottom,
      intervalLeft: 16.w,
      backgroundColor: Colors.transparent,
      child: StorePopWindow(
        widget.storeList ?? [],
        onSelectItem: (int? selectIndex, StoreInfo selectItem) {
          _storeSelectController.setStoreName(selectItem.fullname ?? "");
          _storeSelectController.setCashierName("");
          _selectStoreCashier = null;
          _loadFullStoreInfo(selectItem.otypeId);
          HaloPopWindow().disMiss();
        },
      ),
    );
  }

  _loadFullStoreInfo(String? otypeId) async {
    _selectStoreInfo = await StoreModel.getStoreInfo(context, otypeId);
    _storeSelectController.setStoreName(_selectStoreInfo?.fullname ?? "");
    if (null == _selectStoreInfo!.cashiers ||
        _selectStoreInfo!.cashiers!.isEmpty) {
      if (context.mounted) {
        HaloToast.show(
          context,
          msg: "门店【${_selectStoreInfo!.fullname}】不存在可用收银机，请在pc端进行添加",
        );
      }
      _selectStoreCashier = null;
      return;
    }

    if (null == _selectStoreCashier ||
        StringUtil.isEmpty(_selectStoreCashier!.id)) {
      _selectStoreCashier = _selectStoreInfo!.cashiers!.first;
    } else {
      _selectStoreCashier = _selectStoreInfo!.cashiers!.firstWhere(
        (element) => element.id == _selectStoreCashier!.id,
        orElse: () => _selectStoreInfo!.cashiers!.first,
      );
    }
    _storeSelectController.setCashierName(_selectStoreCashier!.fullname ?? "");
  }

  //收银机选择列表
  _showCashierPopWindow(BuildContext context) {
    if (null == _selectStoreInfo) {
      HaloToast.show(
        context,
        msg: StringRes.TIP_STORE_SELECTOR.getText(context),
      );
      return;
    }
    if (_selectStoreInfo!.cashiers!.isEmpty) {
      HaloToast.show(
        context,
        msg: StringRes.TIP_STORE_CASHIER.getText(context),
      );
      return;
    }

    HaloPopWindow().show(
      _cashierGlobalKey,
      gravity: PopWindowGravity.bottom,
      backgroundColor: Colors.transparent,
      intervalLeft: 16.w,
      child: Container(
        width: 530.w,
        height: _selectStoreInfo!.cashiers!.length * 60.w,
        constraints: BoxConstraints(maxHeight: 300.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.w)),
          border: Border.all(color: AppColors.dividerColor),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.16),
              offset: Offset(3.h, 3.w),
              blurRadius: 16.w,
              spreadRadius: 0,
            ),
          ],
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(0),
          itemCount: _selectStoreInfo!.cashiers!.length,
          itemBuilder: (buildContext, index) {
            StoreCashier item = _selectStoreInfo!.cashiers![index];
            return _ItemWidget(
              name: item.fullname,
              index: index,
              onSelectItem: (selectIndex) {
                _selectStoreCashier =
                    _selectStoreInfo!.cashiers![selectIndex ?? 0];
                _storeSelectController.setCashierName(
                  _selectStoreCashier!.fullname ?? "",
                );
                HaloPopWindow().disMiss();
              },
            );
          },
        ),
      ),
    );
  }

  doSave(BuildContext context) {
    if (null == _selectStoreInfo ||
        StringUtil.isEmpty(_selectStoreInfo!.otypeId)) {
      HaloToast.show(
        context,
        msg: StringRes.TIP_STORE_SELECTOR.getText(context),
      );
      return;
    }
    if (null == _selectStoreCashier ||
        StringUtil.isEmpty(_selectStoreCashier!.id)) {
      HaloToast.show(
        context,
        msg: StringRes.TIP_CASHIER_SELECTOR.getText(context),
      );
      return;
    }
    SpTool.saveCashierInfo(_selectStoreCashier);
    SpTool.saveStoreInfo(_selectStoreInfo!);
    NavigateUtil.pop(context);
    if (null != widget.onSelected) {
      widget.onSelected!();
    }
  }
}

class StorePopWindow extends StatefulWidget {
  final List<StoreInfo> storeList;
  final Function(int?, StoreInfo selectItem)? onSelectItem;

  const StorePopWindow(this.storeList, {Key? key, this.onSelectItem})
    : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return StorePopWindowState();
  }
}

class StorePopWindowState extends State<StorePopWindow> {
  TextEditingController filterTextController = TextEditingController();
  List<StoreInfo> showStoreList = [];

  @override
  void initState() {
    showStoreList = widget.storeList;
    filterTextController.text = "";
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      width: 530.w,
      direction: Axis.vertical,
      height: 300.h,
      color: Colors.white,
      border: Border.all(color: AppColors.dividerColor),
      borderRadius: BorderRadius.all(Radius.circular(10.w)),
      borderShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.16),
          offset: Offset(3.h, 3.w),
          blurRadius: 16.w,
          spreadRadius: 0,
        ),
      ],
      children: [
        HaloContainer(
          height: 60.h,
          padding: EdgeInsets.symmetric(horizontal: 18.w),
          mainAxisSize: MainAxisSize.max,
          border: const Border(
            bottom: BorderSide(color: AppColors.dividerColor),
          ),
          children: [
            IconFont(
              IconNames.sousuo,
              size: 28.w,
              color: ColorUtil.color2String(AppColors.hintColor),
            ),
            SizedBox(width: 8.w),
            HaloTextField(
              contentPadding: 0,
              controller: filterTextController,
              onChanged: (value) {
                setState(() {
                  showStoreList =
                      StringUtil.isEmpty(filterTextController.text)
                          ? widget.storeList ?? []
                          : widget.storeList
                                  ?.where(
                                    (element) => element.fullname!.contains(
                                      filterTextController.text,
                                    ),
                                  )
                                  .toList() ??
                              [];
                });
              },
              fontSize: AppPosSize.secondaryTitleFontSize.sp,
              textColor: AppColors.normalFontColor,
              hintText: "输入门店名称查询",
              hintStyle: const TextStyle(color: AppColors.hintColor),
            ),
          ],
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(0),
            itemCount: showStoreList.length,
            itemBuilder: (buildContext, index) {
              return _ItemWidget(
                name: showStoreList[index].fullname,
                index: index,
                onSelectItem: (selectIndex) {
                  if (null != widget.onSelectItem) {
                    widget.onSelectItem!(
                      selectIndex,
                      showStoreList[selectIndex ?? 0],
                    );
                  }
                },
              );
            },
          ),
        ),
      ],
    );
  }
}

class _ItemWidget extends StatelessWidget {
  final Function(int?)? onSelectItem;
  final String? name;
  final int? index;

  const _ItemWidget({this.name, this.index, this.onSelectItem});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (null != onSelectItem) {
          onSelectItem!(index);
        }
      },
      child: Container(
        height: 60.w,
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(color: AppColors.dividerColor)),
        ),
        child: Text(
          name ?? "",
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 24.sp,
            color: AppColorHelper(context).getTitleBoldTextColor(),
          ),
        ),
      ),
    );
  }
}

class _StoreSelectController extends GetxController {
  var storeName = "".obs;
  var cashierName = "".obs;

  void setStoreName(String name) {
    storeName.value = name;
  }

  void setCashierName(String name) {
    cashierName.value = name;
  }
}
