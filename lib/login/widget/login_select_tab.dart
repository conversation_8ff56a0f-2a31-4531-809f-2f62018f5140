import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';

///
///@ClassName: select_tab
///@Description:  选择tab切换
///@Author: tanglan
///@Date: 7/5/21 9:57 AM
class LoginSelectTab extends StatefulWidget {
  bool? isPhoneSelect;
  TextStyle? selectStyle;
  TextStyle? unSelectStyle;

  Function(bool? isPhoneSelect)? selectedChange;

  LoginSelectTab(
      {Key? key,
      this.isPhoneSelect = true,
      this.selectedChange,
      this.selectStyle,
      this.unSelectStyle})
      : super(key: key);

  @override
  _LoginSelectTabState createState() {
    return _LoginSelectTabState();
  }
}

class _LoginSelectTabState extends State<LoginSelectTab> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      children: [
        selectItem("验证码登录", isPhoneTab: true),
        SizedBox(
          width: 50.w,
        ),
        selectItem("密码登录", isPhoneTab: false)
      ],
    );
  }

  Widget selectItem(String title, {bool? isPhoneTab}) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          setState(() {
            widget.isPhoneSelect = isPhoneTab;
            if (null != widget.selectedChange) {
              widget.selectedChange!(isPhoneTab);
            }
          });
        },
        child: Container(
            margin: EdgeInsets.only(bottom: 4.w),
            child: Text(
              title,
              style: getTextStyleBySelect((widget.isPhoneSelect ?? false) &&
                      (isPhoneTab ?? false) ||
                  (!(widget.isPhoneSelect ?? false) && !(isPhoneTab ?? false))),
            )));
  }

  TextStyle getTextStyleBySelect(bool isSelect) {
    if (isSelect) {
      return widget.selectStyle ??
          TextStyle(fontSize: 38.sp, color: ColorUtil.stringColor("#2288FC"));
    } else {
      return widget.unSelectStyle ??
          TextStyle(fontSize: 32.sp, color: ColorUtil.stringColor("#999999"));
    }
  }
}
