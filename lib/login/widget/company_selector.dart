import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../login/model/company_dto.dart';
import 'package:haloui/iconfont/icon_font.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_checkbox.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/halo_toast.dart';

///
///@ClassName: halo_pos_company_selector
///@Description: 类作用描述
///@Author: tanglan
///@Date: 9/2/21 10:35 AM

class CompanySelect extends StatefulWidget {
  final List<CompanyDto>? companyList;
  final Function(CompanyDto companyDto)? onSelectedClick;

  CompanySelect(this.companyList, this.onSelectedClick);

  @override
  State<StatefulWidget> createState() {
    return _HaloCompanySelectDialogState();
  }
}

class _HaloCompanySelectDialogState extends State<CompanySelect> {
  int selectIndex = 0;

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
        direction: Axis.vertical,
        borderRadius: BorderRadius.all(Radius.circular(10.w)),
        padding: EdgeInsets.only(top: 46.w, left: 40.w, right: 40.w),
        margin: EdgeInsets.symmetric(horizontal: 600.w, vertical: 150.w),
        color: Colors.white,
        children: [
          Text(
            "选择登录公司",
            style: TextStyle(
                fontSize: 41.sp,
                color: ColorUtil.stringColor("#333333"),
                fontWeight: FontWeight.w600,
                decoration: TextDecoration.none),
          ),
          SizedBox(
            height: 34.w,
          ),
          Expanded(
              child: ListView.builder(
            shrinkWrap: true,
            itemCount: widget.companyList?.length,
            itemBuilder: (context, index) {
              return _companyItem(context, index);
            },
          )),
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            margin: EdgeInsets.only(top: 44.w, bottom: 30.w),
            children: [
              Expanded(
                  child: HaloButton(
                text: "确定",
                fontWeight: FontWeight.w600,
                height: 80.w,
                onPressed: () {
                  if (widget.companyList![selectIndex].expire ?? false) {
                    HaloToast.show(context,
                        msg:
                            "【${widget.companyList![selectIndex].companyName}】公司已过期，请重新选择");
                    return;
                  }
                  if (null != widget.onSelectedClick) {
                    widget.onSelectedClick!(widget.companyList![selectIndex]);
                  }
                  Navigator.pop(context);
                },
              ))
            ],
          )
        ]);
  }

  Widget _companyItem(BuildContext context, int index) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (widget.companyList![index].expire ?? false) {
          return;
        }
        setState(() {
          selectIndex = index;
        });
      },
      child: HaloContainer(
          direction: Axis.vertical,
          crossAxisAlignment: CrossAxisAlignment.end,
          borderRadius: BorderRadius.all(Radius.circular(10.w)),
          margin: EdgeInsets.only(top: 16.w),
          border: Border.all(
              width: selectIndex == index ? 2.w : 1.w,
              color: selectIndex == index
                  ? ColorUtil.stringColor("#2288FC")
                  : ColorUtil.stringColor("#C5C5C5")),
          children: [
            Stack(
              children: [
                Positioned(
                    right: 0,
                    child: Visibility(
                      visible: widget.companyList![index].expire ?? false,
                      child: HaloIconFont(
                        HaloIconNames.yiguoqi,
                        size: 32.w,
                      ),
                    )),
                HaloContainer(
                    padding:
                        EdgeInsets.symmetric(vertical: 20.w, horizontal: 16.w),
                    children: [
                      HaloCheckBox(
                          enabled:
                              !(widget.companyList![index].expire ?? false),
                          imageSize: 36.w,
                          defaultImage: HaloIconFont(
                            HaloIconNames.roundcheckfill,
                            size: 36.w,
                            color: "#DEDEDE",
                          ),
                          selectedImage: HaloIconFont(
                            HaloIconNames.roundcheckfill,
                            size: 36.w,
                            color: ColorUtil.color2String(
                                ColorUtil.stringColor("#2288FC")),
                          ),
                          value: selectIndex == index,
                          onChanged: (value) {
                            if (widget.companyList![index].expire ?? false) {
                              return;
                            }
                            setState(() {
                              selectIndex = index;
                            });
                          }),
                      SizedBox(
                        width: 27.w,
                      ),
                      Expanded(
                          child: Text(
                        widget.companyList![index].companyName ?? "",
                        style: TextStyle(
                            color: ColorUtil.stringColor("#333333"),
                            fontWeight: selectIndex == index
                                ? FontWeight.w500
                                : FontWeight.w400,
                            fontSize: 30.sp,
                            decoration: TextDecoration.none),
                      ))
                    ]),
              ],
            )
          ]),
    );
  }
}
