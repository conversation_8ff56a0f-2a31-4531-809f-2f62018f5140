import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/halo_web_widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/login/login_center.dart';
import '../../common/net/request_url.dart';

///
///@ClassName: halo_pos_save_tips
///@Description: 类作用描述
///@Author: tanglan
///@Date: 9/2/21 9:37 AM

class SafeTipWidget extends StatelessWidget {
  SafeTipWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        child: Halo<PERSON>ontainer(
            direction: Axis.vertical,
            width: 700.w,
            borderRadius: BorderRadius.all(Radius.circular(10)),
            padding: EdgeInsets.only(top: 40.w, left: 40.w, right: 40.w),
            margin: EdgeInsets.symmetric(horizontal: 54.w),
            color: Colors.white,
            children: [
              Text(
                "温馨提示",
                style: TextStyle(
                    fontSize: 41.sp,
                    color: ColorUtil.stringColor("#333333"),
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.none),
              ),
              SizedBox(
                height: 36.w,
              ),
              RichText(
                  strutStyle: StrutStyle(leading: 0.8),
                  text: TextSpan(
                      text:
                          "亲爱的用户，感谢您信任并使用网上管家婆云零售.成都章鱼侠科技股份有限公司（以下简称“我们”）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。请仔细阅读网上管家婆",
                      style: TextStyle(
                          decoration: TextDecoration.none,
                          fontSize: 28.sp,
                          fontWeight: FontWeight.w400,
                          color: ColorUtil.stringColor("#666666")),
                      children: [
                        TextSpan(
                            text: "《服务协议》",
                            style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: ColorUtil.stringColor("#2288FC")),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                if (Platform.isWindows) {
                                  launchUrl(Uri.parse(
                                      RequestURL.HALO_APP_SERVICE_URL));
                                  return;
                                }
                                Navigator.of(context).push(MaterialPageRoute(
                                    settings: RouteSettings(name: "1"),
                                    builder: (context) {
                                      return HaloWebViewWidget(
                                          url: RequestURL.HALO_APP_SERVICE_URL,
                                          title: "服务协议");
                                    }));
                              }),
                        TextSpan(
                            text: "与",
                            style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: ColorUtil.stringColor("#999999"))),
                        TextSpan(
                            text: "《隐私条款》",
                            style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: ColorUtil.stringColor("#2288FC")),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                if (Platform.isWindows) {
                                  launchUrl(Uri.parse(
                                      RequestURL.HALO_APP_PRIVACY_URL));
                                  return;
                                }
                                Navigator.of(context).push(MaterialPageRoute(
                                    settings: RouteSettings(name: "1"),
                                    builder: (context) {
                                      return HaloWebViewWidget(
                                          url: RequestURL.HALO_APP_PRIVACY_URL,
                                          title: "隐私条款");
                                    }));
                              }),
                        TextSpan(
                          text: " 。阅读过程中，如您有任何疑问，可联系我们的客服热线咨询028-85980000",
                          style: TextStyle(
                              decoration: TextDecoration.none,
                              fontSize: 28.sp,
                              fontWeight: FontWeight.w400,
                              color: ColorUtil.stringColor("#666666")),
                        )
                      ])),
              HaloContainer(
                mainAxisSize: MainAxisSize.max,
                height: 110.w,
                margin: EdgeInsets.only(top: 60.w),
                children: [
                  Expanded(
                      child: HaloButton(
                    text: "同意",
                    // padding: MaterialStateProperty.all(EdgeInsets.all(4)),
                    height: 80.w,
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w600,
                    onPressed: () {
                      LoginCenter.setFirstInstall(false);
                      Navigator.of(context).pop();
                    },
                  ))
                ],
              ),
              HaloContainer(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                height: 110.w,
                children: [
                  HaloButton(
                    text: "不同意并退出",
                    foregroundColor: ColorUtil.stringColor("#999999"),
                    fontSize: 32.sp,
                    height: 80.w,
                    buttonType: HaloButtonType.textButton,
                    fontWeight: FontWeight.w600,
                    onPressed: () {
                      SystemNavigator.pop();
                    },
                  )
                ],
              )
            ]));
  }
}
