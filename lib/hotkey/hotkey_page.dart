import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_toast.dart';
import 'package:hotkey_manager/hotkey_manager.dart';

import '../common/standard.dart';
import '../bill/widget/ptype/base_goods_dialog.dart';
import '../common/style/app_colors.dart';
import '../common/tool/dialog_util.dart';
import '../common/tool/sp_tool.dart';
import 'hotkey.dart';
import 'hotkey_manager.dart';

Future<dynamic> showHotkeyManagerPage(BuildContext context) {
  return showDialog(
    context: context,
    builder: (context) => const HotkeyManagerPage(),
  );
}

///快捷键设置页面
class HotkeyManagerPage extends StatefulWidget {
  const HotkeyManagerPage({Key? key}) : super(key: key);

  @override
  State<HotkeyManagerPage> createState() => _HotkeyManagerPageState();
}

class _HotkeyManagerPageState extends BaseGoodsDialogState<HotkeyManagerPage> {
  HotkeyType? _currentType;

  @override
  double get height => 700.h;

  @override
  String get title => "快捷键设置";

  @override
  double get width => 1000.w;

  final Map<HotkeyType, HotKey?> _keyMap = {};

  @override
  void initState() {
    super.initState();
    _keyMap.addAll(SpTool.getSetting().hotkeyMap);
  }

  @override
  Widget buildContent(BuildContext context) {
    return Column(
      children: [
        Expanded(child: _buildKeyList(context)),
        Container(
          padding: dialogPadding.copyWith(bottom: 10.h),
          width: double.infinity,
          child: const Text(
            "快捷键设置支持ALT/CTRL+字母/数字",
            style: TextStyle(color: Colors.red),
          ),
        ),
        divider,
        _buildBottomButtons(),
      ],
    );
  }

  ///底部按钮
  Widget _buildButton(
    BuildContext context,
    String content, {
    Color textColor = const Color(0xFF333333),
    Color background = Colors.white,
    Color? borderColor = const Color(0xFF999999),
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 60.h,
        width: 120.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: background,
          borderRadius: BorderRadius.circular(4.w),
          border: borderColor?.let(
            (borderColor) => Border.all(color: borderColor, width: 2.w),
          ),
        ),
        child: Text(
          content,
          style: TextStyle(color: textColor, fontSize: 26.sp),
        ),
      ),
    );
  }

  ///底部确定和取消按钮
  Widget _buildBottomButtons() {
    return Container(
      color: Colors.white,
      height: 80.h,
      padding: EdgeInsets.only(right: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          _buildButton(context, "取消", onTap: () => Navigator.pop(context)),
          SizedBox(width: 30.w),
          _buildButton(
            context,
            "确定",
            textColor: Colors.white,
            background: const Color(0xFF4679FC),
            borderColor: null,
            onTap: () {
              final setting = SpTool.getSetting();
              setting.hotkeyMap = _keyMap;
              SpTool.saveSetting(setting);
              HotkeyManager.registerAllHotkey();
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  ///构建热键列表
  Widget _buildKeyList(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisExtent: 80.h,
      ),
      itemBuilder: _buildHotkeyWidget,
      itemCount: HotkeyType.values.length,
    );
  }

  ///构建热键widget
  Widget _buildHotkeyWidget(BuildContext context, int index) {
    HotkeyType keyType = HotkeyType.values[index];
    HotKey? hotKey = _keyMap[keyType];
    bool isCurrent = _currentType == keyType;
    Color background = isCurrent ? Colors.white : AppColors.dividerColor;
    Widget child = Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: background,
        borderRadius: BorderRadius.circular(4.w),
        border: isCurrent ? Border.all(color: const Color(0xFF4679FC)) : null,
      ),
      alignment: Alignment.center,
      child: hotKey != null ? Text(_getKeyLabel(hotKey)) : null,
    );
    if (isCurrent) {
      child = Stack(
        children: [
          HotKeyRecorder(
            initalHotKey: hotKey,
            onHotKeyRecorded: (key) => _onHotkeyChange(keyType, key),
          ),
          child,
        ],
      );
    }
    MainAxisAlignment alignment;
    int remainder = index % 3;
    if (remainder == 0) {
      alignment = MainAxisAlignment.start;
    } else if (remainder == 1) {
      alignment = MainAxisAlignment.center;
    } else {
      alignment = MainAxisAlignment.end;
    }
    return Container(
      padding: dialogPadding,
      child: Row(
        mainAxisAlignment: alignment,
        children: [
          Container(
            width: 80.w,
            alignment: Alignment.centerRight,
            child: Text(
              keyType.describe,
              style: TextStyle(
                color: const Color(0xFF333333),
                fontWeight: FontWeight.bold,
                fontSize: 18.sp,
              ),
            ),
          ),
          SizedBox(width: 20.w),
          GestureDetector(
            onTap: () {
              if (!isCurrent) {
                setState(() => _currentType = keyType);
              } else {
                setState(() => _currentType = null);
              }
            },
            behavior: HitTestBehavior.opaque,
            child: SizedBox(width: 150.w, height: 46.h, child: child),
          ),
        ],
      ),
    );
  }

  ///更改了热键，需要判断热键是否符合要求
  Future<void> _onHotkeyChange(HotkeyType type, HotKey newKey) async {
    debugPrint("${type.name}:${newKey.logicalKey.keyLabel}");
    if (newKey.modifiers?.length == 1 &&
        modifiers.contains(newKey.modifiers!.first) &&
        keyList.contains(newKey.physicalKey)) {
      final sameKey = _keyMap.entries
          .where((e) => e.key != type && e.value != null)
          .cast<MapEntry<HotkeyType, HotKey?>?>()
          .firstWhere(
            (e) =>
                e!.value!.identifier == newKey.identifier ||
                ((e.value!.physicalKey == newKey.physicalKey ||
                        e.value!.physicalKey.isSameNumberKey(
                          newKey.physicalKey,
                        )) &&
                    e.value!.modifiers?.first == newKey.modifiers?.first),
            orElse: () => null,
          );
      if (sameKey != null) {
        final index = await DialogUtil.showConfirmDialog(
          context,
          title: "快捷键冲突",
          content:
              "快捷键[${_getKeyLabel(newKey)}]已被[${sameKey.key.describe}]占用,覆盖将清空[${sameKey.key.describe}]的快捷键,取消将取消本次设置",
          actionLabels: ["覆盖", "取消"],
        );
        if (!mounted) return;
        if (index == 0) {
          _keyMap[type] = newKey;
          _keyMap[sameKey.key] = null;
        }
      } else {
        _keyMap[type] = newKey;
      }
      setState(() {
        _currentType = null;
      });
    }
    // else {
    //   HaloToast.show(context, msg: "录入的快捷键不合法");
    // }
  }

  String _getKeyLabel(HotKey key) {
    HotKeyModifier modifier = key.modifiers!.first;
    String modifierStr;
    switch (modifier) {
      case HotKeyModifier.control:
        modifierStr = "CTRL";
        break;
      case HotKeyModifier.alt:
        modifierStr = "ALT";
        break;
      default:
        modifierStr = "";
        break;
    }
    return "$modifierStr+${key.key.keyLabel}";
  }
}
