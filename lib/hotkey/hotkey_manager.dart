import 'dart:io';

import 'package:halo_pos/plugin/hotkey_plugin.dart';

import '../common/standard.dart';
import 'package:hotkey_manager/hotkey_manager.dart';

import '../common/tool/sp_tool.dart';
import 'hotkey.dart';

typedef HotkeyCallback = Function(HotkeyType type);

///热键管理和注册
class HotkeyManager {
  ///回调map，根据type区分要打开哪个页面
  static final Map<HotkeyType, HotkeyCallback> _callbackMap = {};

  ///记录下热键对应的类型
  static final Map<HotKey, HotkeyType> _hotKeyMap = {};

  ///触发时间间隔
  static const int _period = 500;

  ///时间戳，避免短时间内重复触发
  static int _timeStamp = 0;

  HotkeyManager._();

  ///注册热键
  static Future<void> registerAllHotkey() async {
    if (!Platform.isWindows && !Platform.isAndroid) return;
    await unregisterAllHotkey();
    final hotkeyMap = SpTool.getSetting().hotkeyMap;
    for (var entry in hotkeyMap.entries) {
      final hotkey = entry.value;
      if (hotkey == null) continue;
      final type = entry.key;
      _registerHotkey(hotkey, type);
      //数字按键需要两种都注册
      final sameNumberKey = hotkey.physicalKey.sameNumberKey?.let(
        (it) =>
            HotKey(key: it, modifiers: hotkey.modifiers, scope: hotkey.scope),
      );
      if (sameNumberKey != null) {
        _registerHotkey(sameNumberKey, type);
      }
    }
  }

  ///取消注册全部热键
  static Future<void> unregisterAllHotkey() async {
    if (Platform.isWindows) {
      await hotKeyManager.unregisterAll();
    } else if (Platform.isAndroid) {
      HotkeyPlugin.instance.unregisterAllHotkey();
    }
    _hotKeyMap.clear();
  }

  ///注册一个回调
  static void registerCallback(HotkeyType type, HotkeyCallback callback) {
    if (!Platform.isWindows && !Platform.isAndroid) return;
    _callbackMap[type] = callback;
  }

  ///反注册一个回调
  static void unregisterCallback(HotkeyType type) {
    if (!Platform.isWindows && !Platform.isAndroid) return;
    _callbackMap.remove(type);
  }

  static void unregisterAllCallback() {
    if (!Platform.isWindows && !Platform.isAndroid) return;
    _callbackMap.clear();
  }

  ///注册单个热键
  static Future<void> _registerHotkey(
    HotKey hotkey,
    HotkeyType hotkeyType,
  ) async {
    if (Platform.isWindows) {
      await hotKeyManager.register(hotkey, keyDownHandler: _onKeyHandler);
      _hotKeyMap[hotkey] = hotkeyType;
    } else if (Platform.isAndroid) {
      HotkeyPlugin.instance.registerHotkey(
        keyLabel: hotkey.key.keyLabel,
        modifier: hotkey.modifiers!.first.name,
        hotkeyType: hotkeyType,
      );
    }
  }

  ///热键触发
  static void _onKeyHandler(HotKey hotkey) {
    if (!Platform.isWindows) return;
    final type = _hotKeyMap[hotkey];
    if (type == null) return;
    onKeyHandlerByHotKeyType(type);
  }

  ///热键触发，通过热键类型触发
  static void onKeyHandlerByHotKeyType(HotkeyType type) {
    final callback = _callbackMap[type];
    if (callback == null) return;
    int timeStamp = DateTime.now().millisecondsSinceEpoch;
    if (timeStamp - _timeStamp >= _period) {
      _timeStamp = timeStamp;
      callback.call(type);
    }
  }
}
