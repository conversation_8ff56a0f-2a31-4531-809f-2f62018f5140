import 'package:flutter/services.dart';
import 'package:hotkey_manager/hotkey_manager.dart';

const hotkeyVersion = 1;

///默认热键
final defaultHotkeyMap = {
  HotkeyType.selectGoods: HotKey(
    key: LogicalKeyboardKey.keyS,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.saveDraft: HotKey(
    key: PhysicalKeyboardKey.keyG,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.openCashBox: HotKey(
    key: PhysicalKeyboardKey.keyD,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.shiftChange: HotKey(
    key: PhysicalKeyboardKey.keyJ,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.selectVip: HotKey(
    key: PhysicalKeyboardKey.keyH,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.stockQuery: HotKey(
    key: PhysicalKeyboardKey.keyK,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.orderPreferential: HotKey(
    key: PhysicalKeyboardKey.keyY,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.orderMemo: HotKey(
    key: PhysicalKeyboardKey.keyB,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.channel: HotKey(
    key: PhysicalKeyboardKey.keyX,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.scanPay: HotKey(
    key: PhysicalKeyboardKey.keyF,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.settlement: HotKey(
    key: PhysicalKeyboardKey.keyS,
    modifiers: [HotKeyModifier.control],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.backBill: HotKey(
    key: PhysicalKeyboardKey.keyT,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.changeBill: HotKey(
    key: PhysicalKeyboardKey.keyH,
    modifiers: [HotKeyModifier.control],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.printPreBill: HotKey(
    key: PhysicalKeyboardKey.keyY,
    modifiers: [HotKeyModifier.control],
    scope: HotKeyScope.inapp,
  ),
  HotkeyType.orderDiscount: HotKey(
    key: PhysicalKeyboardKey.keyZ,
    modifiers: [HotKeyModifier.alt],
    scope: HotKeyScope.inapp,
  ),
};

///修饰键
const List<HotKeyModifier> modifiers = [
  HotKeyModifier.alt,
  HotKeyModifier.control,
];

///普通按键
const List<PhysicalKeyboardKey> keyList = [...letterList, ...numberList];

///字母按键
const List<PhysicalKeyboardKey> letterList = [
  PhysicalKeyboardKey.keyA,
  PhysicalKeyboardKey.keyB,
  PhysicalKeyboardKey.keyC,
  PhysicalKeyboardKey.keyD,
  PhysicalKeyboardKey.keyE,
  PhysicalKeyboardKey.keyF,
  PhysicalKeyboardKey.keyG,
  PhysicalKeyboardKey.keyH,
  PhysicalKeyboardKey.keyI,
  PhysicalKeyboardKey.keyJ,
  PhysicalKeyboardKey.keyK,
  PhysicalKeyboardKey.keyL,
  PhysicalKeyboardKey.keyM,
  PhysicalKeyboardKey.keyN,
  PhysicalKeyboardKey.keyO,
  PhysicalKeyboardKey.keyP,
  PhysicalKeyboardKey.keyQ,
  PhysicalKeyboardKey.keyR,
  PhysicalKeyboardKey.keyS,
  PhysicalKeyboardKey.keyT,
  PhysicalKeyboardKey.keyU,
  PhysicalKeyboardKey.keyV,
  PhysicalKeyboardKey.keyW,
  PhysicalKeyboardKey.keyX,
  PhysicalKeyboardKey.keyY,
  PhysicalKeyboardKey.keyZ,
];

const List<PhysicalKeyboardKey> numberList = [
  PhysicalKeyboardKey.digit0,
  PhysicalKeyboardKey.digit1,
  PhysicalKeyboardKey.digit2,
  PhysicalKeyboardKey.digit3,
  PhysicalKeyboardKey.digit4,
  PhysicalKeyboardKey.digit5,
  PhysicalKeyboardKey.digit6,
  PhysicalKeyboardKey.digit7,
  PhysicalKeyboardKey.digit8,
  PhysicalKeyboardKey.digit9,
  PhysicalKeyboardKey.numpad0,
  PhysicalKeyboardKey.numpad1,
  PhysicalKeyboardKey.numpad2,
  PhysicalKeyboardKey.numpad3,
  PhysicalKeyboardKey.numpad4,
  PhysicalKeyboardKey.numpad5,
  PhysicalKeyboardKey.numpad6,
  PhysicalKeyboardKey.numpad7,
  PhysicalKeyboardKey.numpad8,
  PhysicalKeyboardKey.numpad9,
];

extension PosKeyCodeExtension on PhysicalKeyboardKey {
  ///判断两个数字按键是否相同
  bool isSameNumberKey(PhysicalKeyboardKey other) {
    if (numberList.contains(this)) {
      if (this == other) return true;
      if (numberList.contains(other)) {
        String keyLabelThis = keyLabel;
        String keyLabelOther = other.keyLabel;
        return keyLabelThis[keyLabelThis.length - 1] ==
            keyLabelOther[keyLabelOther.length - 1];
      }
    }
    return false;
  }

  ///找出和自己相同的另外一个数字按键
  PhysicalKeyboardKey? get sameNumberKey {
    if (!numberList.contains(this)) return null;
    for (var key in numberList.where((element) => element != this)) {
      if (isSameNumberKey(key)) return key;
    }
    return null;
  }
}

enum HotkeyType {
  ///商品选择
  selectGoods("商品选择"),

  ///挂单
  saveDraft("挂/取单"),

  ///打开钱箱
  openCashBox("打开钱箱"),

  ///交接班
  shiftChange("交接班"),

  ///选择会员
  selectVip("选择会员"),

  ///库存查询
  stockQuery("库存查询"),

  ///整单优惠
  orderPreferential("总额优惠"),

  ///备注
  orderMemo("备注"),

  ///自提
  channel("自提核销"),

  ///扫码支付
  scanPay("扫码支付"),

  ///收银
  settlement("收银"),

  ///退货
  backBill("退货"),

  ///换货
  changeBill("换货"),

  ///打印预结单
  printPreBill("打印预结单"),

  ///整单折扣
  orderDiscount("整单折扣");

  final String describe;

  const HotkeyType(this.describe);
}
