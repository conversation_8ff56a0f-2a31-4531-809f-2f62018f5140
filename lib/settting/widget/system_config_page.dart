import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/haloui.dart';

import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/permission_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../settting/widget/checkbox.dart';
import '../../../settting/widget/print_setting_page.dart';
import '../../../settting/widget/screen_setting_page.dart';
import '../../../settting/widget/switch.dart';
import '../../../widgets/base/base_stateful_page.dart';
import 'normal_setting_page.dart';

///系统配置，左侧设置类型列表对应的枚举
enum _ConfigType {
  NORMAL,
  PRINT,
  SCREEN,
}

///系统配置页面，分为左侧的设置类型列表，以及选中左侧后，右侧展示的具体设置页面
class SystemConfigPage extends BaseStatefulPage {
  const SystemConfigPage({Key? key})
      : super(
          key: key,
          rightFlex: 2.5,
        );

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _SystemConfigPageState();
}

class _SystemConfigPageState extends BaseStatefulPageState<SystemConfigPage> {
  ///当前左侧选中的分类按钮，默认为通用配置
  _ConfigType _currentType = _ConfigType.NORMAL;
  final PermissionDto _permissionDto = SpTool.getPermission();

  ///根据枚举类型获取枚举对应的名称
  static String _parseTypeNameByConfigType(_ConfigType type) {
    switch (type) {
      case _ConfigType.NORMAL:
        return "通用设置";
      case _ConfigType.SCREEN:
        return "副屏设置";
      case _ConfigType.PRINT:
        return "打印设置";
      default:
        return "";
    }
  }

  ///根据枚举类型获取枚举对应的图片
  static IconNames _parseTypeImageByConfigType(_ConfigType type) {
    switch (type) {
      case _ConfigType.NORMAL:
        return IconNames.tongyongshezhi;
      case _ConfigType.SCREEN:
        return IconNames.fupingshezhi;
      case _ConfigType.PRINT:
        return IconNames.dayinshezhi;
      default:
        return IconNames.tongyongshezhi;
    }
  }

  ///构建左边的分类按钮
  Widget _buildTypeItem(BuildContext context, int index) {
    final type = _ConfigType.values[index];
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => setState(() {
        _currentType = type;
        getPermission();
      }),
      child: Container(
        color:
            _currentType == type ? const Color(0xFFF4F7FF) : Colors.transparent,
        height: 110.h,
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 39.w),
        child: Row(
          children: [
            IconFont(_parseTypeImageByConfigType(type), size: 35.w),
            Flexible(
                child: Padding(
                    padding: EdgeInsets.only(left: 15.w),
                    child: Text(
                      _parseTypeNameByConfigType(type),
                      style: TextStyle(
                        color: _currentType == type
                            ? const Color(0xff2769ff)
                            : const Color(0xff333333),
                        fontWeight: FontWeight.bold,
                        fontSize: 28.sp,
                      ),
                    )))
          ],
        ),
      ),
    );
  }

  ///构建右边的页面
  Widget _buildSubPage(BuildContext context) {
    switch (_currentType) {
      case _ConfigType.NORMAL:
        return _permissionDto.shopsalesettingedit == true
            ? NormalSettingPage(
              )
            : Container();
      case _ConfigType.SCREEN:
        return _permissionDto.shopsalescreenedit == true
            ? const ScreenSettingPage()
            : Container();
      case _ConfigType.PRINT:
        return _permissionDto.shopsaleprintedit == true
            ? const PrintSettingPage()
            : Container();
      default:
        return Container();
    }
  }

  bool getPermission() {
    switch (_currentType) {
      case _ConfigType.NORMAL:
        if (_permissionDto.shopsalesettingedit != true) {
          HaloToast.showError(context, msg: "没有通用设置权限");
          return false;
        }
        break;
      case _ConfigType.SCREEN:
        if (_permissionDto.shopsalescreenedit != true) {
          HaloToast.showError(context, msg: "没有副屏设置权限");
          return false;
        }
        break;
      case _ConfigType.PRINT:
        if (_permissionDto.shopsaleprintedit != true) {
          HaloToast.showError(context, msg: "没有打印设置权限");
          return false;
        }
        break;
      default:
        return true;
    }
    return true;
  }

  @override
  String getActionBarTitle() {
    return "系统配置";
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return MediaQuery.removePadding(
        context: context,
        removeTop: true,
        removeBottom: true,
        child: Container(
            color: Colors.white,
            child: ListView.builder(
                itemBuilder: _buildTypeItem,
                itemCount: _ConfigType.values.length
                // itemCount: Platform.isWindows
                //     ? (_ConfigType.values.length - 1) //windows隐藏副屏
                //     : _ConfigType.values.length
                )));
  }

  @override
  Widget buildRightBody(BuildContext context) {
    return Container(
      height: double.infinity,
      // color: Color(0xfff6f6f6),
      padding:
          EdgeInsets.only(left: 46.w, right: 40.w, top: 38.h, bottom: 38.h),
      child: SingleChildScrollView(
        child: _buildSubPage(context),
      ),
    );
  }

  @override
  Future<void> onInitState()  {

    return Future.value(null);
  }
}

///各种分类设置模块中对应的白色圆角背景容器
Widget buildPageContainer(Widget child) {
  return Container(
      margin: EdgeInsets.only(top: 36.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.w),
      ),
      child: child);
}

///分割线
Widget buildDivider() {
  return Padding(
      padding: EdgeInsets.symmetric(horizontal: 38.w),
      child: Divider(height: 2.h, color: const Color(0xffe8e8ea)));
}

///标题及复选框的容器，具有相同的高度和内边距,并且均首尾分布
Widget buildRow(List<Widget> children, {double? height, EdgeInsets? padding}) {
  return Container(
    height: height ?? 91.h,
    padding: EdgeInsets.only(
        left: padding == null ? 51.w : padding.left,
        right: padding == null ? 39.w : padding.right),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: children,
    ),
  );
}

///标题文字
Text buildTitleText(String name, {TextStyle? style}) => //标题文字
    Text(
      name,
      style: style ??
          TextStyle(
              fontSize: 30.sp,
              color: const Color(0xff333333),
              fontWeight: FontWeight.bold),
    );

///构建右侧设置详情页面，各种设置的标题
Widget buildSettingTitle(String name,
    {bool? value,
    TextStyle? style,
    EdgeInsets? textPadding,
    ValueChanged<bool>? onChanged,
    hasDivider = true}) {
  final List<Widget> children = [
    Padding(
        padding: textPadding ?? EdgeInsets.zero,
        child: buildTitleText(name, style: style))
  ];
  //标题右边的复选框
  if (value != null && onChanged != null) {
    children.add(HaloPosSwitch(
        width: 73.w, height: 40.h, value: value, onChanged: onChanged));
  }

  if (hasDivider) {
    return Column(
      children: [buildRow(children), buildDivider()],
    );
  } else {
    return buildRow(children);
  }
}
///富文本 子标题
Widget buildSettingRichTitle(
    {required String title, String? subTitle,bool? value,
      TextStyle? style,
      TextStyle? subStyle,
      EdgeInsets? textPadding,
      ValueChanged<bool>? onChanged,
      hasDivider = true}) {
  final List<Widget> children = [
    Row(mainAxisAlignment: MainAxisAlignment.start, children: [
      Padding(
        padding: EdgeInsets.zero,
        child:RichText(
          text:  TextSpan(
            children: [
              TextSpan(text: title, style: style??TextStyle(
                  fontSize: 35.sp,
                  color: const Color(0xff333333),
                  fontWeight: FontWeight.bold)),
            ],
          ),
        ),),
      Padding(
        padding: textPadding ?? EdgeInsets.only(left: 15.w),
        child:RichText(
          text:  TextSpan(
            children: [
              TextSpan(
                text: subTitle,
                style:subStyle??TextStyle(
                    fontSize: 22.sp,
                    color: const Color(0xff7F7F7F),
                    fontWeight: FontWeight.w300),
              ),
            ],
          ),
        ),)
    ],)
  ];
  //标题右边的复选框
  if (value != null && onChanged != null) {
    children.add(HaloPosSwitch(
        width: 73.w, height: 40.h, value: value, onChanged: onChanged));
  }

  if (hasDivider) {
    return Column(
      children: [buildRow(children), buildDivider()],
    );
  } else {
    return buildRow(children);
  }
}

///构建带文字的复选框
Widget buildCheckBoxWithName(
        String name, bool value, ValueChanged<bool> onChanged,
        {double? fontSize,
        double? height,
        EdgeInsets? padding,
        FontWeight? fontWeight}) =>
    buildRow([
      Text(
        name,
        style: TextStyle(
            fontSize: fontSize ?? 30.sp,
            color: const Color(0xff333333),
            fontWeight: fontWeight ?? FontWeight.bold),
      ),
      HaloPosCheckBox(
          value: value, width: 40.w, height: 40.w, onChanged: onChanged),
    ], height: height, padding: padding);
