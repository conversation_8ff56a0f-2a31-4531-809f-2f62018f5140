import 'package:flutter/material.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/column_config.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_checkbox.dart';

enum ColumnPagesType {
  ColumnPagesBillingSale, //开单列配置
  ColumnPagesBillingSaleBack, //开单列配置
  ColumnPagesBills, //单据列配置
  ColumnPagesTenderRequest, //要货申请列配置
  ColumnPagesTenderManage, //调拨管理列配置
  ColumnPagesStockBillTenderManage, //库存调拨列配置
}

class ColumnConfigPages extends StatefulWidget {
  final ColumnPagesType columnPagesType;
  final ValueChanged? changed;

  const ColumnConfigPages(
      {Key? key,
      this.columnPagesType = ColumnPagesType.ColumnPagesBillingSale,
      this.changed})
      : super(key: key);

  @override
  _ColumnConfigState createState() => _ColumnConfigState();
}

class _ColumnConfigState extends State<ColumnConfigPages> {
  List<dynamic> config = [];
  bool isTenderConfig = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      isTenderConfig =
          widget.columnPagesType == ColumnPagesType.ColumnPagesTenderRequest ||
          widget.columnPagesType == ColumnPagesType.ColumnPagesTenderManage ||
          widget.columnPagesType ==
              ColumnPagesType.ColumnPagesStockBillTenderManage;

      switch (widget.columnPagesType) {
        case ColumnPagesType.ColumnPagesBillingSale:
          config = SpTool.getBillingColumnConfigSale();
          break;
        case ColumnPagesType.ColumnPagesBillingSaleBack:
          config = SpTool.getBillingColumnConfigSaleBack();
          break;
        case ColumnPagesType.ColumnPagesBills:
          config = SpTool.getBillsColumnConfig();
          break;
        case ColumnPagesType.ColumnPagesTenderRequest:
          config = SpTool.getTenderRequestColumnConfig();
          break;
        case ColumnPagesType.ColumnPagesTenderManage:
          config = SpTool.getTenderManageColumnConfig();
          break;
        case ColumnPagesType.ColumnPagesStockBillTenderManage:
          config = SpTool.getStockBillTenderManageColumnConfig();
          break;
        default:
          config = SpTool.getBillingColumnConfigSale();
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      color: Colors.white,
      margin: EdgeInsets.only(
          left: ScreenUtil().screenWidth - 400.w,
          bottom: MediaQuery.of(context).padding.bottom),
      direction: Axis.vertical,
      children: [
        HaloContainer(
          height: 85.w,
          padding: EdgeInsets.only(left: 22.w, right: 0.w),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            HaloPosLabel(
              "选择列表中显示的字段",
              textStyle: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColorHelper(context).getTitleBoldTextColor()),
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Container(
                width: 60,
                alignment: Alignment.center,
                child: IconFont(IconNames.close),
              ),
            ),
          ],
        ),
        Divider(color: AppColors.lineColor, height: 1),
        Expanded(
          child: ListView.builder(
            itemBuilder: (context, index) {
              return isTenderConfig
                  ? _buildTenderListItem(config[index] as TenderColumnConfig)
                  : _buildListItem(config[index] as ColumnConfig);
            },
            itemCount: config.length,
          ),
        ),
      ],
    );
  }

  Widget _buildListItem(ColumnConfig config) {
    return HaloContainer(
      height: 60.w,
      padding: EdgeInsets.only(left: 29.w),
      children: [
        HaloCheckBox(
          enabled: !config.isRequired,
          value: config.type == ColumnType.all ? getIsAllShow : config.isShow,
          selectedImage:
              config.isRequired
                  ? IconFont(IconNames.bunengbianji)
                  : IconFont(IconNames.xuanzhong),
          defaultImage:
              config.isRequired
                  ? IconFont(IconNames.bunengbianji)
                  : IconFont(IconNames.weixuanzhong),
          onChanged: (newV) {
            setState(() {
              if (config.title == "全部") {
                this.config =
                    (this.config as List<ColumnConfig>).map((e) {
                      if (!e.isRequired) {
                        e.isShow = !newV;
                      }
                      return e;
                    }).toList();
              } else {
                config.isShow = !newV;
              }
            });
            callback() => widget.changed?.call(this.config);
            switch (widget.columnPagesType) {
              case ColumnPagesType.ColumnPagesBillingSale:
                SpTool.saveBillingColumnConfigSale(
                  this.config as List<ColumnConfig>,
                ).whenComplete(callback);
                break;
              case ColumnPagesType.ColumnPagesBillingSaleBack:
                SpTool.saveBillingColumnConfigSaleBack(
                  this.config as List<ColumnConfig>,
                ).whenComplete(callback);
                break;
              case ColumnPagesType.ColumnPagesBills:
                SpTool.saveBillsColumnConfig(
                  this.config as List<ColumnConfig>,
                ).whenComplete(callback);
                break;
              default:
                SpTool.saveBillingColumnConfigSale(
                  this.config as List<ColumnConfig>,
                ).whenComplete(callback);
                break;
            }
          },
        ),
        SizedBox(width: 28.w),
        HaloPosLabel(
          config.title,
          textStyle: TextStyle(
            fontSize: 22.sp,
            color: AppColorHelper(context).getTitleBoldTextColor(),
          ),
        ),
      ],
    );
  }

  Widget _buildTenderListItem(TenderColumnConfig config) {
    return HaloContainer(
      height: 60.w,
      padding: EdgeInsets.only(left: 29.w),
      children: [
        HaloCheckBox(
          enabled: !config.isRequired,
          value:
              config.type == TenderColumnType.all
                  ? getTenderIsAllShow
                  : config.isShow,
          selectedImage:
              config.isRequired
                  ? IconFont(IconNames.bunengbianji)
                  : IconFont(IconNames.xuanzhong),
          defaultImage:
              config.isRequired
                  ? IconFont(IconNames.bunengbianji)
                  : IconFont(IconNames.weixuanzhong),
          onChanged: (newV) {
            setState(() {
              if (config.title == "全部") {
                this.config =
                    (this.config as List<TenderColumnConfig>).map((e) {
                      if (!e.isRequired) {
                        e.isShow = !newV;
                      }
                      return e;
                    }).toList();
              } else {
                config.isShow = !newV;
              }
            });
            callback() => widget.changed?.call(this.config);
            switch (widget.columnPagesType) {
              case ColumnPagesType.ColumnPagesTenderRequest:
                SpTool.saveTenderRequestColumnConfig(
                  this.config as List<TenderColumnConfig>,
                ).whenComplete(callback);
                break;
              case ColumnPagesType.ColumnPagesTenderManage:
                SpTool.saveTenderManageColumnConfig(
                  this.config as List<TenderColumnConfig>,
                ).whenComplete(callback);
                break;
              case ColumnPagesType.ColumnPagesStockBillTenderManage:
                SpTool.saveStockBillTenderManageColumnConfig(
                  this.config as List<TenderColumnConfig>,
                ).whenComplete(callback);
                break;
              default:
                break;
            }
          },
        ),
        SizedBox(width: 28.w),
        HaloPosLabel(
          config.title,
          textStyle: TextStyle(
            fontSize: 22.sp,
            color: AppColorHelper(context).getTitleBoldTextColor(),
          ),
        ),
      ],
    );
  }

  bool get getIsAllShow {
    return (config as List<ColumnConfig>).every(
      (ColumnConfig config) => config.isShow,
    );
  }

  bool get getTenderIsAllShow {
    return (config as List<TenderColumnConfig>).every(
      (TenderColumnConfig config) => config.isShow,
    );
  }
}
