import 'package:flutter/material.dart';
import '../../../iconfont/icon_font.dart';

class HaloPosSwitch extends StatelessWidget {
  final bool value;

  final ValueChanged<bool>? onChanged;

  final double width;

  final double height;

  const HaloPosSwitch({
    Key? key,
    required this.value,
    required this.width,
    required this.height,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onChanged?.call(!value),
      child: SizedBox(
        width: width,
        height: height,
        child: IconFont(value ? IconNames.kai_1 : IconNames.guan),
      ),
    );
  }
}
