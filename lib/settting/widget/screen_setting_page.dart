import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_utils/utils/permisson_util.dart';
import 'package:haloui/widget/photo/entity.dart';
import '../../../bill/widget/ptype/base_goods_dialog.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../plugin/secondary_screen_plugin.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/photo/ui/options.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';

import '../../plugin/secondary_screen_windows.dart';
import '../entity/setting_dto.dart';
import 'switch.dart';
import 'system_config_page.dart';

///副屏设置页面
class ScreenSettingPage extends StatefulWidget {
  const ScreenSettingPage({Key? key}) : super(key: key);

  @override
  State<ScreenSettingPage> createState() => _ScreenSettingPageState();
}

class _ScreenSettingPageState extends State<ScreenSettingPage> {
  ///展示的图片缩略图
  List<String>? _secondaryScreenThumbnailUrls;

  ///展示的图片
  List<String>? _secondaryScreenPictureUrls;

  ///展示的视频
  String? _secondaryVideoUrl;

  ///视频是否轮播
  bool? _isCarousel;

  ///图片切换时间
  int? _bannerTime;

  ///图片张数
  int get _imageCount => _secondaryScreenPictureUrls?.length ?? 0;

  ///列表中图片宽度
  final double _itemWidth = 116.w;

  ///左边标题宽度
  final double _titleWidth = 210.w;

  SettingDto _setting = SpTool.getSetting();

  @override
  void initState() {
    super.initState();
    _initFromStore();
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      children: [
        buildPageContainer(
          HaloContainer(
              visible: Platform.isWindows,
              padding: EdgeInsets.only(right: 29.w),
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                buildSettingRichTitle(title: "启用副屏"),
                HaloPosSwitch(
                    width: 73.w,
                    height: 40.h,
                    value: _setting.enableSecondScreen,
                    onChanged: (value) {
                      setState(() {
                        _setting.enableSecondScreen = value;
                      });
                      if (_setting.enableSecondScreen) {
                        showSecondaryWindow();
                      } else {
                        hideSecondaryWindow();
                      }
                      _saveSettingToStore();
                    })
              ]),
        ),
        HaloContainer(
          visible: _setting.enableSecondScreen || !Platform.isWindows,
          direction: Axis.vertical,
          children: [
            //图片
            _buildImageSettingWidget(context),
            //视频
            Padding(
              padding: EdgeInsets.only(top: 36.h, bottom: 30.h),
              child: _buildVideoSettingWidget(context),
            ),
            //特别说明
            Container(
                width: double.infinity,
                padding: EdgeInsets.only(left: 50.w),
                child: Text("特别说明：有图片也有视频的情况下，先滚动图片，所有图片都展示完后再播放视频，依次交替展示",
                    style: TextStyle(
                        color: const Color(0xFF666666), fontSize: 24.sp)))
          ],
        )
      ],
    );
  }

  ///构建最外层包含标题的容器
  Widget _buildContainer(BuildContext context,
      {required String title, List<Widget> content = const []}) {
    return buildPageContainer(Column(
      mainAxisSize: MainAxisSize.min,
      children: content..insert(0, buildSettingTitle(title)),
    ));
  }

  ///构建提示文字，富文本
  ///[source]中key代表文字内容，value代表是否是红色字体
  Widget _buildHint(Map<String, bool> source) {
    final textStyle =
        TextStyle(color: const Color(0xFF979797), fontSize: 22.sp);
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(left: _titleWidth, top: 16.h),
      child: RichText(
        text: TextSpan(
            style: textStyle,
            children: source.entries
                .map((entry) => TextSpan(
                      style: entry.value
                          ? textStyle.copyWith(color: const Color(0xFFE11111))
                          : null,
                      text: entry.key,
                    ))
                .toList()),
      ),
    );
  }

  ///构建左边的标题
  Widget _buildTitle(String title) {
    return Container(
      width: _titleWidth,
      padding: EdgeInsets.only(left: 50.w),
      child: Text(
        title,
        style: TextStyle(color: const Color(0xFF333333), fontSize: 28.sp),
      ),
    );
  }

  ///构建上传图片
  Widget _buildImageSettingWidget(BuildContext context) {
    return _buildContainer(context, title: "图片广告", content: [
      //图片列表
      _buildImageList(context),
      //提示文字
      _buildHint({
        "建议图片尺寸为": false,
        "686*600": true,
        "；支持JPG、JPEG、BMP、PNG格式，大小不超过5MB，限": false,
        "5": true,
        "张": false,
      }),
      //滚动时长
      _buildSetDelayWidget(context)
    ]);
  }

  ///构建滚动时长
  Widget _buildSetDelayWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 16.h, bottom: 36.h),
      child: Row(
        children: [
          _buildTitle("滚动时长"),
          //时长输入框
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              showDialog<int>(
                context: context,
                builder: (context) =>
                    _SwitchPeriodDialog(period: _bannerTime ?? 0),
              ).then((value) {
                if (value == null || value == _bannerTime) {
                  return;
                }
                setState(() {
                  _bannerTime = value;
                });
                _saveSettingToStore();
                _showOnSecondaryScreen();
              });
            },
            child: Container(
              width: 50.w,
              height: 72.h,
              margin: EdgeInsets.only(right: 10.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.w),
                  border:
                      Border.all(color: const Color(0xFFD5D5D5), width: 2.w)),
              child: Text(
                _bannerTime?.toString() ?? "",
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style:
                    TextStyle(color: const Color(0xFF333333), fontSize: 26.sp),
              ),
            ),
          ),
          Text("秒播放下一张图片",
              style:
                  TextStyle(color: const Color(0xFF333333), fontSize: 28.sp)),
          Text(" （最长60s，不滚动请设置0s）",
              style:
                  TextStyle(color: const Color(0xFF666666), fontSize: 24.sp)),
        ],
      ),
    );
  }

  ///构建图片上传列表
  Widget _buildImageList(BuildContext context) {
    //图片最多五张，如果已经选择了五张图片，则不展示上传图片的按钮
    int itemCount = _imageCount + 1;
    if (itemCount > 5) {
      itemCount = 5;
    }
    return Padding(
      padding: EdgeInsets.only(top: 22.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle("上传图片"),
          Expanded(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxHeight: _itemWidth),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: itemCount,
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, index) {
                  //图片总张数小于5张，则最后一张图片后展示上传图片的按钮
                  if (_imageCount < 5 && index == itemCount - 1) {
                    return _buildAdd();
                  }
                  return _buildReviewItem(
                      _secondaryScreenPictureUrls?[index] ?? "",
                      thumbnail: _secondaryScreenThumbnailUrls?[index]);
                },
              ),
            ),
          )
        ],
      ),
    );
  }

  ///构建图片和视频item
  Widget _buildReviewItem(String url,
      {String? thumbnail, bool isVideo = false}) {
    Widget preview;
    if (isVideo) {
      preview = Container(
          color: const Color(0xFFF4F7FF),
          alignment: Alignment.center,
          child: const Icon(Icons.play_circle_outline));
    } else {
      preview = Image.file(File(thumbnail ?? url));
    }
    return Container(
        margin: EdgeInsets.only(right: 24.w),
        child: SizedBox(
          width: _itemWidth,
          height: _itemWidth,
          child: Stack(
            fit: StackFit.expand,
            clipBehavior: Clip.none,
            children: [
              //预览图片
              ClipRRect(
                borderRadius: BorderRadius.circular(5.w),
                child: preview,
              ),
              //删除图片/视频按钮
              Positioned(
                  top: -10.w,
                  right: -8.w,
                  width: 26.w,
                  height: 26.w,
                  child: GestureDetector(
                    onTap: () {
                      //点击删除视频和图片
                      setState(() {
                        if (isVideo) {
                          _secondaryVideoUrl = null;
                        } else {
                          _secondaryScreenPictureUrls?.remove(url);
                          _secondaryScreenThumbnailUrls?.remove(thumbnail);
                        }
                      });
                      _saveSettingToStore();
                      //同步到副屏
                      _showOnSecondaryScreen().then((value) async {
                        if (Platform.isAndroid) {
                          //同步之后，删除图片
                          File file = File(url);
                          if (await file.exists()) {
                            file.delete();
                          }
                        }
                      });
                    },
                    child: IconFont(IconNames.shanchu_2, size: 26.w),
                  ))
            ],
          ),
        ));
  }

  ///构建图片和视频上传按钮
  Widget _buildAdd([bool isVideo = false]) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => _pickGallery(isVideo),
      child: SizedBox(
          width: _itemWidth,
          height: _itemWidth,
          child: DottedBorder(
              color: const Color(0xffbbbbbb),
              strokeWidth: 2.w,
              borderType: BorderType.RRect,
              dashPattern: [8.w, 4.w, 8.w, 4.w],
              radius: Radius.circular(5.w),
              child: SizedBox(
                width: double.infinity,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconFont(IconNames.shangchuan, size: 22.w),
                    Text("点击上传",
                        style: TextStyle(
                            fontSize: 22.sp, color: const Color(0xFF2769FF)))
                  ],
                ),
              ))),
    );
  }

  ///构建视频设置部分
  Widget _buildVideoSettingWidget(BuildContext context) {
    return _buildContainer(context, title: "视频广告", content: [
      //视频
      _buildVideo(context),
      //提示文字
      Padding(
        padding: EdgeInsets.only(bottom: 42.h),
        child: _buildHint({
          "视频不超过": false,
          "60s": true,
          "；支持AVI、MP4、3GP、WMV格式，大小不超过5MB，建议尺寸1920*1080，限": false,
          "1": true,
          "个": false,
        }),
      )
    ]);
  }

  ///视频上传和删除
  Widget _buildVideo(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 22.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle("上传视频"),
          StringUtil.isEmpty(_secondaryVideoUrl)
              ? _buildAdd(true)
              : _buildReviewItem(_secondaryVideoUrl ?? "", isVideo: true)
        ],
      ),
    );
  }

  ///打开相册选择图片或视频
  ///图片最多5张
  _pickGallery([bool isVideo = false]) async {
    List<String>? mediaList;
    if (Platform.isWindows) {
      mediaList = await _pickWindows(context, isVideo);
    } else if (Platform.isAndroid) {
      mediaList = await _pickAndroid(context, isVideo);
    }
    if (mediaList?.isNotEmpty == true && mounted) {
      if (isVideo) {
        _onVideoPickResult(mediaList!);
      } else {
        _onImagePickResult(mediaList!);
      }
    }

    // List<AssetEntity>? resultList = await PhotoPicker.pickAsset(
    //   context: context,
    //   maxSelected: isVideo ? 1 : 5 - _imageCount,
    //   rowCount: 5,
    //   pickType: isVideo ? PickType.onlyVideo : PickType.onlyImage,
    // );
    // if (!mounted) return;
    // if (null != resultList && resultList.isNotEmpty) {
    //   if (isVideo) {
    //     File file = await resultList.first.file;
    //     if ((await file.length()) > 5 * 1024 * 1024) {
    //       HaloToast.show(context, msg: "选取的视频过大");
    //       return;
    //     }
    //   }
    //   // Loading.showLoading(context); todo 上传图片时的Loading
    //   List<String> mediaList = await Stream.fromFutures(resultList.map(
    //           (e) async =>
    //               await _copyFile((await e.file).path, isVideo: isVideo) ?? ""))
    //       .where((event) => !TextUtil.isEmpty(event))
    //       .toList();
    //   if (mediaList.isNotEmpty && mounted) {
    //     if (isVideo) {
    //       _onVideoPickResult(mediaList);
    //     } else {
    //       _onImagePickResult(mediaList);
    //     }
    //   }
    // Loading.hideLoading(context);
  }

  Future<List<String>> _pickAndroid(BuildContext context, bool isVideo) async {


    List<AssetEntity>? resultList = await PhotoPicker.pickAsset(
      context: context,
      maxSelected: isVideo ? 1 : 5 - _imageCount,
      rowCount: 5,
      pickType: isVideo ? PickType.onlyVideo : PickType.onlyImage, onPermissionFailed:(var permission){

    }
    );
    if (!mounted) return [];
    if (null != resultList && resultList.isNotEmpty) {
      if (isVideo) {
        File file = await resultList.first.file;
        if ((await file.length()) > 5 * 1024 * 1024) {
          HaloToast.show(context, msg: "选取的视频过大");
          return [];
        }
      }

      List<String> mediaList = await Stream.fromFutures(resultList.map(
              (e) async =>
                  await _copyFile((await e.file).path, isVideo: isVideo) ?? ""))
          .where((event) => !TextUtil.isEmpty(event))
          .toList();
      return mediaList;
    }
    return [];
  }


  Future<List<String>> _pickWindows(BuildContext context, bool isVideo) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: isVideo ? FileType.video : FileType.image,
      allowMultiple: !isVideo,
      allowedExtensions: isVideo ? ['mp4'] : ['jpg', 'png'],
    );
    if (result != null) {
      if (isVideo) {
        if (result.files.single.size > 5 * 1024 * 1024) {
          HaloToast.show(context, msg: "选取的视频过大");
          return [];
        }
      }
      return result.paths
          .where((e) => !TextUtil.isEmpty(e))
          .cast<String>()
          .toList();
    }
    return [];
  }

  ///拷贝文件
  Future<String?> _copyFile(String filePath, {isVideo = false}) async {
    // "/storage/emulated/0/Android/data/packname/files"
    Directory? appDir = await getExternalStorageDirectory();
    if (appDir != null) {
      String otypeId = SpTool.getStoreInfo()!.otypeId ?? "default";
      Directory dir = Directory(
          "${appDir.path}/secondaryScreen/$otypeId/${isVideo ? "video" : "image"}");
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
      File source = File(filePath);
      File result = await source.copy(dir.path +
          source.path
              .substring(source.path.lastIndexOf("/"), source.path.length));
      return result.path;
    }
    return null;
  }

  ///图片上传成功的回调，返回图片的地址，然后将该图片地址保存在本地，并套用到副屏幕
  _onImagePickResult(List<String> imgList) {
    setState(() {
      List<String> thumbnails = [];
      List<String> images = [];
      String img;
      for (img in imgList) {
        String url = img;
        String thumbnail = img;
        thumbnails.add(thumbnail);
        images.add(url);
      }
      //刷新
      _secondaryScreenThumbnailUrls ??= [];
      _secondaryScreenThumbnailUrls!.addAll(thumbnails);
      _secondaryScreenPictureUrls ??= [];
      _secondaryScreenPictureUrls!.addAll(images);
      //保存图片地址到本地
      _saveSettingToStore();
      //将图片展示到副屏
      _showOnSecondaryScreen();
    });
  }

  ///视频上传成功后的回调
  _onVideoPickResult(List<String> videoList) {
    setState(() {
      _secondaryVideoUrl = videoList.first;
      //保存视频地址到本地
      _saveSettingToStore();
      //播放视频
      _showOnSecondaryScreen();
    });
  }

  ///图片视频等配置信息
  void _saveSettingToStore() async {
    SettingDto setting = SpTool.getSetting();
    setting.secondaryScreenPictureUrls = _secondaryScreenPictureUrls ?? [];
    setting.secondaryScreenThumbnailUrls = _secondaryScreenThumbnailUrls ?? [];
    setting.secondaryVideoUrl = _secondaryVideoUrl;
    setting.bannerTime = _bannerTime ?? 0;
    setting.isCarousel = _isCarousel ?? false;
    setting.enableSecondScreen = _setting.enableSecondScreen;
    SpTool.saveSetting(setting);
  }

  ///同步到副屏
  Future<void> _showOnSecondaryScreen() {
    return SecondaryScreenPlugin.showImageAndVideo(
        videoUrl: _secondaryVideoUrl ?? "",
        imageUrls: _secondaryScreenPictureUrls ?? [],
        imageSwitchPeriod: _bannerTime ?? 0);
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    //从本地存储获取，副屏展示图片是否开启
    setState(() {
      _setting = SpTool.getSetting();
      _secondaryScreenThumbnailUrls = _setting.secondaryScreenThumbnailUrls;
      _secondaryScreenPictureUrls = _setting.secondaryScreenPictureUrls;
      _secondaryVideoUrl = _setting.secondaryVideoUrl;
      _isCarousel = _setting.isCarousel;
      _bannerTime = _setting.bannerTime;
    });
  }
}

///滚动时长设置弹窗
class _SwitchPeriodDialog extends StatefulWidget {
  final int period;

  const _SwitchPeriodDialog({Key? key, required this.period}) : super(key: key);

  @override
  _SwitchPeriodDialogState createState() => _SwitchPeriodDialogState();
}

class _SwitchPeriodDialogState
    extends BaseGoodsDialogState<_SwitchPeriodDialog> {
  @override
  String get title => "滚动时长";

  @override
  double get height => 360.h;

  @override
  double get width => 530.w;

  final TextEditingController _controller = TextEditingController();

  late int _period;

  _SwitchPeriodDialogState();

  @override
  void initState() {
    super.initState();
    _period = widget.period;
    _controller.text = _period.toString();
    _controller.addListener(() {
      final text = _controller.text;
      int time = int.tryParse(_controller.text) ?? 0;
      _controller.selection =
          TextSelection(baseOffset: text.length, extentOffset: text.length);
      //0 <= time <= 60
      if (time < 0) {
        time = 0;
      }
      if (time > 60) {
        time = 60;
      }
      setState(() => _period = time);
    });
  }

  @override
  Widget buildContent(BuildContext context) {
    return Column(mainAxisSize: MainAxisSize.min, children: [
      Padding(
        padding: EdgeInsets.only(top: 30.h),
        child: _buildTextField(context),
      ),
      _buildConfirmButton(context)
    ]);
  }

  Widget _buildTextField(BuildContext context) {
    _controller.text = _period.toString();
    return Container(
      width: 200.w,
      height: 72.h,
      margin: EdgeInsets.only(right: 10.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.w),
          border: Border.all(color: const Color(0xFFD5D5D5), width: 2.w)),
      child: TextField(
          textAlign: TextAlign.center,
          textAlignVertical: TextAlignVertical.center,
          style: TextStyle(color: const Color(0xFF333333), fontSize: 26.sp),
          decoration: const InputDecoration(
              border: OutlineInputBorder(borderSide: BorderSide.none)),
          controller: _controller,
          keyboardType: const TextInputType.numberWithOptions()),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      width: 194.w,
      height: 66.h,
      margin: EdgeInsets.only(top: 50.h),
      decoration: BoxDecoration(
        color: const Color(0xFF4679FC),
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: GestureDetector(
        child: HaloPosLabel(
          "确定",
          textAlign: TextAlign.center,
          maxLines: 1,
          textStyle: TextStyle(
              color: Colors.white,
              decoration: TextDecoration.none,
              fontSize: 32.sp,
              fontWeight: FontWeight.bold),
        ),
        onTap: () => Navigator.pop(context, _period),
      ),
    );
  }
}
