import 'dart:io';

import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/halo_dialog.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../../../bill/tool/sale_event_buds.dart';
import '../../../../common/login/login_center.dart';
import '../../../../common/tool/sp_tool.dart';
import '../../../../common/tool/version_check.dart';
import '../../../../enum/setting/bill_page_type.dart';
import '../../../../enum/setting/rounding_type.dart';
import '../../../../login/entity/store/store_info.dart';
import '../../../../login/model/login_user_model.dart';
import '../../bill/widget/ptype/goods_bind_batch_page.dart';
import '../../common/stock_sync.dart';
import '../../common/tool/dialog_util.dart';
import '../../db/database_config.dart';
import '../../entity/system/column_config.dart';
import '../../entity/system/system_config_dto.dart';
import '../../enum/sync_info_type.dart';
import '../../iconfont/icon_font.dart';
import '../../plugin/third_party_scale_plugin.dart';
import '../../widgets/info_sync_dialog.dart';
import '../../widgets/single_select_dialog.dart';
import '../entity/setting_dto.dart';
import 'switch.dart';
import 'system_config_page.dart';
import 'update/newest_version_dialog.dart';

///通用设置页面
class NormalSettingPage extends StatelessWidget {
  NormalSettingPage({Key? key}) : super(key: key);
  final StoreInfo shopConfigEntity = SpTool.getStoreInfo()!;
  final LoginUserModel? loginUser = LoginCenter.getLoginUser();
  SettingDto setting = SpTool.getSetting();

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      children: [
        // 开单方式选择
        const _BillTypeSettingPage(),
        // 开单展示库存
        const _StockSettingPage(),

        ///商品离线配置
        Padding(
          padding: EdgeInsets.only(top: 24.h),
          child: const _LocalPtypeConfigPage(),
        ),

        ///收银播报提示
        const _TTSSettingPage(),

        ///条码称启用配置
        const _BarcodeScalePagePage(),

        ///电子秤配置
        const _ScaleSettingPage(),
        //会员储值密码
        // Padding(
        //   padding: EdgeInsets.only(top: 36.h),
        //   child: const _VipPwdSetPage(),
        // ),
        //每次登录是否自动同步商品const _LocalPtypeConfigPage(),

        //检查更新
        const _VersionSettingPage(),

        buildInfoWidget(context)
      ],
    );
  }

  ///账号信息
  Widget buildInfoWidget(BuildContext context) {
    return HaloContainer(
      width: double.infinity,
      margin: EdgeInsets.only(top: 26.h),
      padding: EdgeInsets.only(bottom: 26.h),
      borderRadius: BorderRadius.all(Radius.circular(10.w)),
      color: Colors.white,
      crossAxisAlignment: CrossAxisAlignment.start,
      direction: Axis.vertical,
      children: [
        buildSettingTitle("门店信息", hasDivider: false),
        Container(
          padding: EdgeInsets.only(left: 51.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("公司名：${loginUser?.company ?? ""}",
                  style: TextStyle(fontSize: 28.sp, color: Colors.black)),
              SizedBox(
                height: 10.h,
              ),
              Text("账号：${loginUser?.user ?? ""}",
                  style: TextStyle(fontSize: 28.sp, color: Colors.black)),
              SizedBox(
                height: 10.h,
              ),
              Text("往来单位：${shopConfigEntity.btypeName}",
                  style: TextStyle(fontSize: 28.sp, color: Colors.black)),
              SizedBox(
                height: 10.h,
              ),
              Text("仓库：${shopConfigEntity.ktypeName}",
                  style: TextStyle(fontSize: 28.sp, color: Colors.black)),
              SizedBox(
                height: 10.h,
              ),
            ],
          ),
        )
      ],
    );
  }
}

///配置抹零类型控件
class _RoundingSettingPage extends StatefulWidget {
  const _RoundingSettingPage({Key? key}) : super(key: key);

  @override
  _RoundingSettingPageState createState() => _RoundingSettingPageState();
}

class _RoundingSettingPageState extends State<_RoundingSettingPage> {
  ///配置抹零是否开启
  bool _isOpen = false;

  ///当前选中的配置抹零类型对应的枚举下标
  int _currentTypeIndex = DEFAULT_ROUNDING_TYPE.index;

  @override
  void initState() {
    super.initState();
    //从本地存储读取状态
    _initFromStore();
  }

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[_buildTitle()];
    if (_isOpen) {
      children.add(ListView.separated(
          shrinkWrap: true,
          separatorBuilder: (context, index) => buildDivider(),
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: _buildCheckBox,
          itemCount: RoundingType.values.length));
    }
    return buildPageContainer(HaloContainer(
      direction: Axis.vertical,
      children: children,
    ));
  }

  ///构建标题栏目和开关
  Widget _buildTitle() {
    return buildSettingTitle("配置抹零", value: _isOpen, onChanged: (value) {
      setState(() {
        _isOpen = value;
        _saveIsOpen();
      });
    }, hasDivider: _isOpen);
  }

  ///单选框
  Widget _buildCheckBox(BuildContext context, int index) {
    final bool isChecked = _currentTypeIndex == index;
    String name = RoundingType.values[index].name;
    return buildCheckBoxWithName(name, isChecked, (value) {
      if (value) {
        setState(() {
          _currentTypeIndex = index;
          _saveRoundingTypeIndex();
        });
      }
    });
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    final setting = SpTool.getSetting();
    setState(() {
      //从本地存储获取，配置抹零是否开启
      _isOpen = setting.enableRounding;
      //从本次存储获取，选中的配置抹零类型
      _currentTypeIndex = setting.roundingType;
    });
  }

  ///保存配置抹零是否开启
  void _saveIsOpen() {
    final setting = SpTool.getSetting();
    setting.enableRounding = _isOpen;
    SpTool.saveSetting(setting);
  }

  ///保存选中抹零类型的下标
  void _saveRoundingTypeIndex() async {
    final setting = SpTool.getSetting();
    setting.roundingType = _currentTypeIndex;
    SpTool.saveSetting(setting);
  }
}

///开单方式选择
class _BillTypeSettingPage extends StatefulWidget {
  const _BillTypeSettingPage({Key? key}) : super(key: key);

  @override
  State<_BillTypeSettingPage> createState() => _BillTypeSettingPageState();
}

class _BillTypeSettingPageState extends State<_BillTypeSettingPage> {
  ///当钱开单方式枚举下标
  late int _currentTypeIndex;
  late bool _openFreshWeight;
  late bool _openFreshPriceVisible;

  ///是否按照商品维度搜索
  late bool searchByPtype;

  @override
  void initState() {
    super.initState();
    //从本地存储读取状态
    _initFromStore();
  }

  @override
  Widget build(BuildContext context) {
    return buildPageContainer(
      HaloContainer(
        direction: Axis.vertical,
        children: [
          _buildTitle(),
          ListView.separated(
              shrinkWrap: true,
              separatorBuilder: (context, index) => buildDivider(),
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: _buildCheckBox,
              itemCount: BillPageType.values.length)
        ],
      ),
    );
  }

  ///标题
  Widget _buildTitle() {
    return buildSettingTitle("开单方式选择", hasDivider: true);
  }

  ///单选框
  Widget _buildCheckBox(BuildContext context, int index) {
    final bool isChecked = _currentTypeIndex == index;
    String name = BillPageType.values[index].name;
    if (index == BillPageType.SCAN_AND_SELECT.index) {
      return Column(
        children: [
          buildCheckBoxWithName(name, isChecked, (value) {
            if (value) {
              setState(() {
                _currentTypeIndex = index;
                _saveBillPageTypeIndex();
              });
            }
          }),
          if (isChecked) _buildSearchType(),
          if (isChecked) _buildWeight(),
          if (isChecked) _buildPrice(),
        ],
      );
    } else {
      return buildCheckBoxWithName(name, isChecked, (value) {
        if (value) {
          setState(() {
            _currentTypeIndex = index;
            _saveBillPageTypeIndex();
          });
        }
      });
    }
  }

  _buildPrice() {
    return buildRow([
      RichText(
        text: TextSpan(
            text: "开单展示零售价      ",
            style: TextStyle(
                fontSize: 26.sp,
                color: const Color(0xff333333),
                fontWeight: FontWeight.bold),
            children: [
              TextSpan(
                  text: "开启后显示价格为:",
                  style: TextStyle(
                      fontSize: 22.sp, color: const Color(0xff999999))),
              TextSpan(
                  text: "¥12.9",
                  style: TextStyle(fontSize: 22.sp, color: Colors.black)),
              TextSpan(
                  text: "/13.9，斜杠后的价格为零售价",
                  style: TextStyle(
                      fontSize: 22.sp, color: const Color(0xff999999))),
            ]),
      ),
      HaloPosSwitch(
          width: 73.w,
          height: 40.h,
          value: _openFreshPriceVisible,
          onChanged: (value) {
            setState(() {
              _openFreshPriceVisible = !_openFreshPriceVisible;
              _saveFreshPriceVisible();
            });
          }),
    ], padding: EdgeInsets.only(left: 80.w, right: 39.w));
  }

  _buildWeight() {
    return buildSettingTitle("启用商品称重",
        style: TextStyle(
            fontSize: 26.sp,
            color: const Color(0xff333333),
            fontWeight: FontWeight.bold),
        value: _openFreshWeight, onChanged: (value) {
      setState(() {
        _openFreshWeight = !_openFreshWeight;
        _saveFreshWeight();
      });
    }, hasDivider: false, textPadding: EdgeInsets.only(left: 29.w));
  }

  ///商品展示(搜索)方式
  ///默认是按sku
  ///可选按商品维度
  _buildSearchType() {
    List<Widget> widgets = [
      Padding(
        padding: EdgeInsets.only(left: 80.w),
        child: Text("商品展示方式",
            style: TextStyle(
                fontSize: 26.sp,
                color: const Color(0xff333333),
                fontWeight: FontWeight.bold)),
      ),
      buildCheckBoxWithName("按商品展示", searchByPtype, (value) {
        setState(() {
          searchByPtype = true;
          _saveSearchByPtype();
        });
      },
          fontSize: 24.sp,
          height: 70.h,
          padding: EdgeInsets.only(left: 120.w, right: 39.w),
          fontWeight: FontWeight.normal),
      buildCheckBoxWithName("按SKU展示", !searchByPtype, (value) {
        setState(() {
          searchByPtype = false;
          _saveSearchByPtype();
        });
      },
          fontSize: 24.sp,
          height: 70.h,
          padding: EdgeInsets.only(left: 120.w, right: 39.w),
          fontWeight: FontWeight.normal),
    ];
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start, children: widgets);
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    final setting = SpTool.getSetting();
    setState(() {
      //从本次存储获取，选中的配置抹零类型
      _currentTypeIndex = setting.billPageType;
      _openFreshWeight = setting.openFreshWight;
      _openFreshPriceVisible = setting.openFreshPriceVisible;
      searchByPtype = setting.searchByPtype;
    });
  }

  ///保存开单方式类型的下标
  void _saveBillPageTypeIndex() async {
    final setting = SpTool.getSetting();
    setting.billPageType = _currentTypeIndex;
    SpTool.saveSetting(setting);
    SaleEventBus.getInstance().fire(SaleEventBus.updateSaleBillView);
  }

  ///保存开单方式类型的下标
  void _saveFreshWeight() async {
    final setting = SpTool.getSetting();
    setting.openFreshWight = _openFreshWeight;
    SpTool.saveSetting(setting);
    SaleEventBus.getInstance().fire(SaleEventBus.updateSaleBillView);
  }

  ///保存开单方式类型的下标
  void _saveFreshPriceVisible() async {
    final setting = SpTool.getSetting();
    setting.openFreshPriceVisible = _openFreshPriceVisible;
    SpTool.saveSetting(setting);
    SaleEventBus.getInstance().fire(SaleEventBus.updateSaleBillView);
  }

  ///保存是否按照商品维度搜索
  void _saveSearchByPtype() async {
    final setting = SpTool.getSetting();
    setting.searchByPtype = searchByPtype;
    SpTool.saveSetting(setting);
    SaleEventBus.getInstance().fire(SaleEventBus.updateSaleBillView);
  }
}

///设置密码
class _VipPwdSetPage extends StatefulWidget {
  const _VipPwdSetPage({Key? key}) : super(key: key);

  @override
  State<_VipPwdSetPage> createState() => _VipPwdSetPageState();
}

class _VipPwdSetPageState extends State<_VipPwdSetPage> {
  bool isOpenVipPwd = false;

  @override
  void initState() {
    super.initState();
    //从本地存储读取状态
    _initFromStore();
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    final setting = SpTool.getSetting();
    setState(() {
      //从本次存储获取，是否开启会员消费密码
      isOpenVipPwd = setting.openVipPwd;
    });
  }

  @override
  Widget build(BuildContext context) {
    return buildPageContainer(buildSettingTitle("会员储值消费密码设置",
        value: isOpenVipPwd, onChanged: _onChanged, hasDivider: false));
  }

  _onChanged(bool value) {
    setState(() {
      isOpenVipPwd = !isOpenVipPwd;
      _saveSetting();
    });
  }

  ///保存是否开启会员消费密码
  void _saveSetting() async {
    final setting = SpTool.getSetting();
    // setting.openVipPwd = isOpenVipPwd;
    SpTool.saveSetting(setting);
  }
}

///库存设置
class _StockSettingPage extends StatefulWidget {
  const _StockSettingPage({Key? key}) : super(key: key);

  @override
  State<_StockSettingPage> createState() => _StockSettingPageState();
}

class _StockSettingPageState extends State<_StockSettingPage> {
  late bool openStock;

  @override
  void initState() {
    super.initState();
    //从本地存储读取状态
    _initFromStore();
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    final setting = SpTool.getSetting();
    setState(() {
      //从本次存储获取，是否库存展示
      openStock = setting.openStock;
    });
  }

  @override
  Widget build(BuildContext context) {
    return buildPageContainer(buildSettingTitle("开单时展示商品库存",
        value: openStock, onChanged: _onChanged, hasDivider: false));
  }

  _onChanged(bool value) {
    setState(() {
      openStock = value;
      _saveSetting();
    });
  }

  ///保存是否库存展示
  void _saveSetting() async {
    final setting = SpTool.getSetting();
    setting.openStock = openStock;
    SpTool.saveSetting(setting);
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();

    await SpTool.saveBillingColumnConfigSale(getColumnBillingConfigListSale(
        isOpenStock: setting.openStock,
        isTaxEnable: systemConfigDto.sysGlobalEnabledTax &&
            systemConfigDto.sysGlobalEnabledSaleTax));

    SaleEventBus.getInstance().fire(SaleEventBus.updateSaleBillView);
  }
}

///收银语音播报
class _TTSSettingPage extends StatefulWidget {
  const _TTSSettingPage({Key? key}) : super(key: key);

  @override
  State<_TTSSettingPage> createState() => _TTSSettingPageState();
}

class _TTSSettingPageState extends State<_TTSSettingPage> {
  late bool openTTS;

  @override
  void initState() {
    super.initState();
    //从本地存储读取状态
    _initFromStore();
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    final setting = SpTool.getSetting();
    setState(() {
      //从本次存储获取，是否库存展示
      openTTS = setting.openTTS;
    });
  }

  @override
  Widget build(BuildContext context) {
    return buildPageContainer(buildSettingTitle("收银语音播报",
        value: openTTS, onChanged: _onChanged, hasDivider: false));
  }

  _onChanged(bool value) {
    setState(() {
      openTTS = value;
      _saveSetting();
    });
  }

  ///保存是否库存展示
  void _saveSetting() async {
    final setting = SpTool.getSetting();
    setting.openTTS = openTTS;
    SpTool.saveSetting(setting);
  }
}

///启用条码称配置
class _BarcodeScalePagePage extends StatefulWidget {
  const _BarcodeScalePagePage({Key? key}) : super(key: key);

  @override
  State<_BarcodeScalePagePage> createState() => _BarcodeScalePageState();
}

///启用条码称配置
class _BarcodeScalePageState extends State<_BarcodeScalePagePage> {
  late bool enableBarcodeScale;

  @override
  void initState() {
    super.initState();
    //从本地存储读取状态
    _initFromStore();
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    final setting = SpTool.getSetting();
    setState(() {
      //从本次存储获取，是否库存展示
      enableBarcodeScale = setting.enableBarcodeScale;
    });
  }

  @override
  Widget build(BuildContext context) {
    return buildPageContainer(buildSettingTitle("启用条码秤",
        value: enableBarcodeScale, onChanged: _onChanged, hasDivider: false));
  }

  _onChanged(bool value) {
    setState(() {
      enableBarcodeScale = value;
      _saveSetting();
    });
  }

  ///保存是否库存展示
  void _saveSetting() async {
    final setting = SpTool.getSetting();
    setting.enableBarcodeScale = enableBarcodeScale;
    SpTool.saveSetting(setting);
  }
}

///电子秤设置
class _ScaleSettingPage extends StatefulWidget {
  const _ScaleSettingPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ScaleSettingPageState();
}

class _ScaleSettingPageState extends State<_ScaleSettingPage> {
  ///是否是内置电子秤
  bool _innerScale = Platform.isAndroid;

  ///第三方电子秤
  SimpleUsbDevice? _thirdPartyScale;

  final _style =
      textStyle.copyWith(fontSize: 30.sp, fontWeight: FontWeight.normal);

  @override
  void initState() {
    super.initState();
    //从本地存储读取状态
    _initFromStore();
  }

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[
      _buildTitle(),
      if (Platform.isAndroid) buildDivider(),
      if (Platform.isAndroid) _buildSwitch(),
      if (!_innerScale) buildDivider(),
      if (!_innerScale) _buildDeviceSelector(),
    ];
    return buildPageContainer(HaloContainer(
      direction: Axis.vertical,
      children: children,
    ));
  }

  ///内置电子秤开关
  Widget _buildSwitch() {
    return buildRow([
      buildText("是否使用内置电子秤", style: _style),
      HaloPosSwitch(
        width: 73.w,
        height: 40.h,
        value: _innerScale,
        onChanged: (value) => setState(() {
          _innerScale = value;
          _saveIsOpen();
          if (!_innerScale) {
            connectDevice(_thirdPartyScale);
          }
        }),
      ),
    ]);
  }

  ///构建标题栏
  Widget _buildTitle() {
    return buildRow([
      buildTitleText("电子秤设置"),
      Text(
        _innerScale ? "内置电子秤" : _thirdPartyScale?.productName ?? "",
        style: TextStyle(fontSize: 28.sp, color: const Color(0xff7c7c7c)),
      )
    ]);
  }

  ///电子秤选择
  Widget _buildDeviceSelector() {
    return buildRow([
      Text("选择外接电子秤", style: _style),
      GestureDetector(
        onTap: () async {
          List<SimpleUsbDevice> list =
              await ThirdPartyScalePlugin.instance.getDevices();
          if (context.mounted) {
            SimpleUsbDevice? dto = await showDialog<SimpleUsbDevice>(
              context: context,
              builder: (context) => SingleSelectDialog<SimpleUsbDevice>(
                  title: "请选择外接电子秤",
                  list: list,
                  contentGetter: (device) => device.productName),
            );
            if (dto != null) {
              setState(() {
                _thirdPartyScale = dto;
                _saveScale();
              });
              connectDevice(dto);
            } else {
              connectDevice(_thirdPartyScale);
            }
          }
        },
        child: IconFont(IconNames.shezhi, size: 40.w),
      )
    ]);
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    final setting = SpTool.getSetting();
    setState(() {
      _innerScale = setting.innerScale;
      _thirdPartyScale = setting.thirdPartyScale;
    });
  }

  ///保存电子秤类型
  void _saveIsOpen() {
    final setting = SpTool.getSetting();
    setting.innerScale = _innerScale;
    SpTool.saveSetting(setting);
  }

  ///保存选中的三方电子秤
  void _saveScale() async {
    final setting = SpTool.getSetting();
    setting.thirdPartyScale = _thirdPartyScale;
    SpTool.saveSetting(setting);
  }

  ///连接到电子秤
  void connectDevice(SimpleUsbDevice? scale) {
    if (scale == null) {
      HaloToast.show(context, msg: "还未选择任何外接电子秤");
    } else {
      ThirdPartyScalePlugin.instance
          .connectDevice(context, scale)
          .onError((error, stackTrace) {
        if (mounted) {
          HaloToast.show(context, msg: "连接失败,${error?.toString() ?? ""}");
        }
      });
    }
  }
}

///本地商品设置
class _LocalPtypeConfigPage extends StatefulWidget {
  const _LocalPtypeConfigPage({Key? key}) : super(key: key);

  @override
  State<_LocalPtypeConfigPage> createState() => _LocalPtypeConfigPageState();
}

class _LocalPtypeConfigPageState extends State<_LocalPtypeConfigPage> {
  ///使用缓存商品信息
  bool _enableLocalPtype = true;

  ///登录时自动同步商品
  bool _isAutoSyncPtype = true;

  ///自动同步库存
  bool _autoSyncStock = true;

  @override
  void initState() {
    super.initState();
    //从本地存储读取状态
    _initFromStore();
  }

  ///从本地存储读取，并且初始化
  void _initFromStore() {
    DatabaseConfig setting = SpTool.getDatabaseConfig();
    setState(() {
      _isAutoSyncPtype = setting.loginSync;
      _enableLocalPtype = setting.enableLocalPtype;
      _autoSyncStock = setting.autoSyncStock;
    });
  }

  @override
  Widget build(BuildContext context) {
    final style = TextStyle(
        fontSize: 26.sp,
        color: const Color(0xff333333),
        fontWeight: FontWeight.bold);
    final textPadding = EdgeInsets.only(left: 29.w);

    return buildPageContainer(
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          buildSettingTitle(
            "使用本地缓存商品信息",
            value: _enableLocalPtype,
            onChanged: (data) {
              setState(() => _enableLocalPtype = !_enableLocalPtype);
              DatabaseConfig setting = SpTool.getDatabaseConfig();
              setting.enableLocalPtype = data;
              SpTool.saveDatabaseConfig(setting);
              if (data) {
                DialogUtil.showConfirmDialog(context,
                    content: "是否开始同步商品信息",
                    actionLabels: ["取消", "确定"],
                    confirmCallback: () => _startSyncPtype(context));
              }
              //根据状态开关定时任务
              _onSyncStockChange();
            },
            hasDivider: false,
          ),
          if (_enableLocalPtype)
            buildSettingTitle(
              "登录时自动缓存商品信息",
              value: _isAutoSyncPtype,
              onChanged: (data) {
                setState(() => _isAutoSyncPtype = !_isAutoSyncPtype);
                DatabaseConfig setting = SpTool.getDatabaseConfig();
                setting.loginSync = _isAutoSyncPtype;
                SpTool.saveDatabaseConfig(setting);
              },
              hasDivider: true,
              textPadding: textPadding,
              style: style,
            ),
          if (_enableLocalPtype)
            buildSettingTitle(
              "定时同步商品库存",
              value: _autoSyncStock,
              onChanged: (data) {
                setState(() => _autoSyncStock = !_autoSyncStock);
                DatabaseConfig setting = SpTool.getDatabaseConfig();
                setting.autoSyncStock = data;
                SpTool.saveDatabaseConfig(setting);
                //根据状态开关定时任务
                _onSyncStockChange();
              },
              hasDivider: true,
              textPadding: textPadding,
              style: style,
            ),
          if (_enableLocalPtype)
            Padding(
              padding: textPadding,
              child: buildRow([
                buildTitleText("手动同步商品信息", style: style),
                GestureDetector(
                  onTap: () => _startSyncPtype(context),
                  child: Text("开始同步",
                      style: TextStyle(
                          fontSize: 26.sp, color: const Color(0xFF2769FF))),
                )
              ]),
            ),
          if (_enableLocalPtype) buildDivider(),
        ],
      ),
    );
  }

  ///当监听到需要开关同步库存的定时任务
  void _onSyncStockChange() {
    if (_enableLocalPtype && _autoSyncStock) {
      //开启
      StockSyncTimer.start();
    } else {
      //关闭
      StockSyncTimer.stop();
    }
  }

  ///开始弹窗同步商品信息
  void _startSyncPtype(BuildContext context) {
    HaloDialog(
      context,
      child: InfoSyncDialog(
        context,
        sycInfoList: [
          SyncInfoType.permissionConfig,
          SyncInfoType.pType,
        ],
        isAutoClose: true,
      ),
    ).show();
  }
}

///版本
class _VersionSettingPage extends StatefulWidget {
  const _VersionSettingPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _VersionSettingPageState();
}

class _VersionSettingPageState extends State<_VersionSettingPage> {
  ///本地版本号
  String localVersionName = "";

  ///是否有新版
  bool hasNewVersion = false;

  @override
  void initState() {
    super.initState();
    initDta();
  }

  void initDta() async {
    localVersionName = await VersionCheck.getLocalVersionName();
    hasNewVersion = await VersionCheck.haveNewVersion();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return buildPageContainer(buildRow([
      buildTitleText("系统检查更新(当前版本 V$localVersionName)"),
      GestureDetector(
        onTap: () => checkUpdate(context),
        child: HaloContainer(children: [
          Visibility(
              visible: hasNewVersion,
              child: Container(
                margin: EdgeInsets.only(right: 10.w),
                width: 8.0,
                height: 8.0,
                decoration: const BoxDecoration(
                    shape: BoxShape.circle, //可以设置角度，BoxShape.circle直接圆形
                    color: Colors.red),
              )),
          Text("检查更新",
              style: TextStyle(fontSize: 30.sp, color: const Color(0xFF2769FF)))
        ]),
      )
    ]));
  }

  ///检查更新
  void checkUpdate(BuildContext context) {
    VersionCheck.checkUpdate(context).then((hasNewVersion) {
      if (!hasNewVersion) {
        return HaloDialog(context, child: const NewestVersionDialog()).show();
      }
    });
  }
}
