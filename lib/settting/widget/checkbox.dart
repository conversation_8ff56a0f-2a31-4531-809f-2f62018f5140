import 'package:flutter/material.dart';
import '../../../iconfont/icon_font.dart';

class HaloPosCheckBox extends StatelessWidget {
  final bool value;

  final double width;

  final double height;

  final ValueChanged<bool>? onChanged;

  final IconNames checkImage;
  final IconNames uncheckImage;

  const HaloPosCheckBox({
    Key? key,
    required this.value,
    required this.width,
    required this.height,
    this.checkImage = IconNames.danxuankuangxuanzhong,
    this.uncheckImage = IconNames.danxuankuangweixuanzhong,
    this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget result = Container(
      width: width,
      height: height,
      child: IconFont(value ? checkImage : uncheckImage),
    );
    if (onChanged != null) {
      result = GestureDetector(
        onTap: () => onChanged?.call(!value),
        child: result,
      );
    }
    return result;
  }
}
