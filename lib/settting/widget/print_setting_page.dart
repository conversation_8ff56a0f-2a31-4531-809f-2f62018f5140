import 'dart:async';
import 'dart:io';

import 'package:bluetooth/bluetooth.dart';
import 'package:bluetooth/bluetooth_dto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pos_printer_platform_image_3/flutter_pos_printer_platform_image_3.dart'
    as flutter_pos_printer_platform;
import 'package:flutter_windows_bluetooth/entity/bluetooth_device.dart';
import 'package:flutter_windows_bluetooth/flutter_windows_bluetooth.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/setting/print_width_type.dart';
import '../../../enum/setting/printer_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../print/tool/print_tool.dart';
import '../../../settting/entity/setting_dto.dart';
import '../../../settting/widget/system_config_page.dart';
import '../../../widgets/single_select_dialog.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_container.dart';

/// 打印设置页面
class PrintSettingPage extends StatefulWidget {
  const PrintSettingPage({Key? key}) : super(key: key);

  @override
  State<PrintSettingPage> createState() => _PrintSettingPageState();
}

class _PrintSettingPageState extends State<PrintSettingPage> {
  @override
  Widget build(BuildContext context) {
    return Column(mainAxisSize: MainAxisSize.min, children: [
      Padding(
        padding: EdgeInsets.only(bottom: 36.h),
        child: _PrinterOpenPage(onChange: (value) {
          setState(() {});
        }),
      ),
      // if (Platform.isWindows)
      Visibility(
          visible:true,
          child: Column(
            children: [
              const _PrinterSettingPage(),
              // else
              //   const PrinterBluetoothSettingPage(),
            ],
          ))
    ]);
  }
}

///打印是否开启
class _PrinterOpenPage extends StatefulWidget {
  Function onChange;

  _PrinterOpenPage({Key? key, required this.onChange}) : super(key: key);

  @override
  State<_PrinterOpenPage> createState() => _PrinterOpenPageState();
}

class _PrinterOpenPageState extends State<_PrinterOpenPage> {
  bool _isOpen = SpTool.getPrint();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final style = TextStyle(
        fontSize: 35.sp,
        color: const Color(0xff333333),
        fontWeight: FontWeight.bold);
    final textPadding = EdgeInsets.only(left: 29.w);
    return buildPageContainer(Column(
      children: [
        buildSettingRichTitle(
            title: "收银小票打印",
            subTitle: "开启后收银结算时自动打印小票",
            value: _isOpen,
            style: style,
            onChanged: (value) {
              setState(() {
                _isOpen = value;
                SpTool.savePrint(_isOpen);
              });
            })
      ],
    ));
  }
}

class _PrinterSettingPage extends StatefulWidget {
  const _PrinterSettingPage({Key? key}) : super(key: key);

  @override
  State<_PrinterSettingPage> createState() => _PrinterSettingPageState();
}

class _PrinterSettingPageState extends State<_PrinterSettingPage>
    with WidgetsBindingObserver {
  ///安卓蓝牙打印机
  BluetoothDto? _androidBluetoothPrinter;

  ///windows蓝牙打印机
  BluetoothDevice? _windowsBluetoothPrinter;

  ///usb打印机
  flutter_pos_printer_platform.PrinterDevice? _usbPrinter;

  ///打印机类型
  late PrinterType _printerType;

  ///app是否进入后台
  bool _paused = false;

  @override
  void initState() {
    super.initState();
    SettingDto dto = SpTool.getSetting();
    _printerType = PrinterType.values[dto.printerType];
    _usbPrinter = dto.usbPrinter;
    if (Platform.isWindows) {
      _windowsBluetoothPrinter = dto.windowsBluetoothPrinter;
    } else {
      WidgetsBinding.instance.addObserver(this);
      _getAndroidPrinterBluetoothInfo();
    }
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    debugPrint("lifecycle changed,$state");
    if (state == AppLifecycleState.paused) {
      _paused = true;
    }
    //从后台进入前台，如从设置页面返回此app
    if (state == AppLifecycleState.resumed && _paused) {
      _paused = false;
      _getAndroidPrinterBluetoothInfo();
    }
  }

  @override
  Widget build(BuildContext context) {
    return buildPageContainer(
      Column(
        children: [
          buildRow([
            buildTitleText("小票打印机连接"),
            Text(
              (_printerType == PrinterType.usb
                      ? _usbPrinter?.name
                      : (Platform.isWindows
                          ? _windowsBluetoothPrinter?.name
                          : _androidBluetoothPrinter?.name)) ??
                  "",
              style: TextStyle(fontSize: 28.sp, color: const Color(0xff7c7c7c)),
            )
          ]),
          buildDivider(),
          buildRow([
            Text(
              "打印机类型：${_printerType == PrinterType.usb ? "USB打印机" : "蓝牙打印机"}",
              style: TextStyle(fontSize: 30.sp, color: const Color(0xff333333)),
            ),
            GestureDetector(
              onTap: () async {
                PrinterType? type = await showDialog<PrinterType>(
                  context: context,
                  builder: (context) => SingleSelectDialog<PrinterType>(
                      title: "请选择打印机",
                      list: PrinterType.values,
                      contentGetter: (type) => type.name),
                );
                if (type != null) {
                  setState(() {
                    SettingDto dto = SpTool.getSetting();
                    dto.printerType = type.index;
                    SpTool.saveSetting(dto);
                    _printerType = type;
                  });
                  //安卓平台，切换打印机类型之后，若是蓝牙打印机，需要去获取蓝牙打印机（展示内置打印机）
                  if (Platform.isAndroid) {
                    _getAndroidPrinterBluetoothInfo();
                  }
                }
              },
              child: IconFont(IconNames.shezhi, size: 40.w),
            ),
          ]),
          buildRow([
            Text(
              "选择打印机",
              style: TextStyle(fontSize: 30.sp, color: const Color(0xff333333)),
            ),
            GestureDetector(
              onTap: () async {
                if (context.mounted) {
                  if (_printerType == PrinterType.usb) {
                    flutter_pos_printer_platform.PrinterDevice? usbDevice =
                        await showDialog(
                      context: context,
                      builder: (context) => const _USBPrinterSelectDialog(),
                    );
                    if (usbDevice != null) {
                      _usbPrinter = usbDevice;
                      final setting = SpTool.getSetting();
                      setting.usbPrinter = _usbPrinter;
                      SpTool.saveSetting(setting);
                    }
                  } else {
                    if (Platform.isWindows) {
                      BluetoothDevice? bluetoothDevice = await showDialog(
                        context: context,
                        builder: (context) =>
                            const _WindowsBluetoothPrinterSelectDialog(),
                      );
                      if (bluetoothDevice != null) {
                        _windowsBluetoothPrinter = bluetoothDevice;
                        final setting = SpTool.getSetting();
                        setting.windowsBluetoothPrinter =
                            _windowsBluetoothPrinter;
                        SpTool.saveSetting(setting);
                      }
                    } else {
                      List<BluetoothDto> list = await Bluetooth()
                          .getBodedDevices("")
                          .onError((error, stackTrace) => []);
                      if (context.mounted) {
                        BluetoothDto? dto = await showDialog<BluetoothDto>(
                          context: context,
                          builder: (context) =>
                              SingleSelectDialog<BluetoothDto>(
                                  title: "请选择打印机",
                                  list: list,
                                  contentGetter: (bluetooth) =>
                                      bluetooth.name ?? ""),
                        );
                        if (dto != null) {
                          setState(() {
                            _androidBluetoothPrinter = dto;
                            final setting = SpTool.getSetting();
                            setting.androidBluetoothPrinter =
                                _androidBluetoothPrinter;
                            SpTool.saveSetting(setting);
                          });
                        }
                      }
                    }
                  }
                  setState(() {});
                }
              },
              child: IconFont(IconNames.shezhi, size: 40.w),
            )
          ]),
        ],
      ),
    );
  }

  ///获取安卓蓝牙打印机
  void _getAndroidPrinterBluetoothInfo() async {
    if (_printerType == PrinterType.bluetooth) {
      _androidBluetoothPrinter = await PrintTool.getPrinter();
      setState(() {});
    }
  }
}

class _USBPrinterSelectDialog extends StatefulWidget {
  const _USBPrinterSelectDialog({Key? key}) : super(key: key);

  @override
  State<_USBPrinterSelectDialog> createState() =>
      _USBPrinterSelectDialogState();
}

class _USBPrinterSelectDialogState extends State<_USBPrinterSelectDialog> {
  final printerManager = flutter_pos_printer_platform.PrinterManager.instance;
  StreamSubscription<flutter_pos_printer_platform.PrinterDevice>? _subscription;

  final list = <flutter_pos_printer_platform.PrinterDevice>[];

  @override
  void initState() {
    super.initState();
    _subscription = printerManager
        .discovery(type: flutter_pos_printer_platform.PrinterType.usb)
        .listen((device) {
      setState(() {
        list.add(flutter_pos_printer_platform.PrinterDevice(
            name: device.name,
            address: device.address,
            productId: device.productId,
            vendorId: device.vendorId));
      });
    });
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleSelectDialog<flutter_pos_printer_platform.PrinterDevice>(
        title: "请选择打印机",
        list: list,
        contentGetter: (device) => device.name ?? "");
  }
}

class _WindowsBluetoothPrinterSelectDialog extends StatefulWidget {
  const _WindowsBluetoothPrinterSelectDialog({Key? key}) : super(key: key);

  @override
  State<_WindowsBluetoothPrinterSelectDialog> createState() =>
      _WindowsBluetoothPrinterSelectDialogState();
}

class _WindowsBluetoothPrinterSelectDialogState
    extends State<_WindowsBluetoothPrinterSelectDialog> {
  final List<BluetoothDevice> _list = [];

  @override
  void initState() {
    super.initState();
    FlutterWindowsBluetooth()
        .getAllPairedDevice()
        .then((List<BluetoothDevice>? devices) {
      if (devices != null) {
        setState(() {
          _list.addAll(devices);
        });
      }
    }).onError((error, stackTrace) {
      HaloToast.show(context, msg: "获取蓝牙设备失败，请检查蓝牙是否打开");
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleSelectDialog<BluetoothDevice>(
        title: "请选择打印机",
        list: _list,
        contentGetter: (device) => device.name ?? "");
  }
}
