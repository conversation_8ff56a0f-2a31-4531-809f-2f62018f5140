import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/update/custom_progress_indicator.dart';
import 'package:path_provider/path_provider.dart';

import '../../../widgets/halo_pos_label.dart';

class UpdateProgressDialog extends StatefulWidget {
  final String apkUrl;
  final String versionName;

  //是否允许关闭
  final bool allowClosed;

  const UpdateProgressDialog({
    Key? key,
    required this.apkUrl,
    required this.versionName,
    this.allowClosed = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return UpdateProgressDialogState();
  }
}

@pragma('vm:entry-point')
class UpdateProgressDialogState extends State<UpdateProgressDialog> {
  int _currentProgress = 0;

  String? taskId;

  double apkSize = 0;

  final double _progressbarWidth = 356.w; //进度条宽度
  final double _progressbarHeight = 12.h; //进度条宽度

  ///进度条指示器宽度
  final double _indicatorWidth = 63.w;

  void startDownload() async {
    if (_currentProgress == 100 && taskId != null) {
      FlutterDownloader.open(taskId: taskId!);
    } else {
      requestDownload(widget.apkUrl);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: HaloContainer(
        width: 500.w,
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        padding: EdgeInsets.symmetric(vertical: 12.h),
        direction: Axis.vertical,
        color: Colors.white,
        children: [
          HaloContainer(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            height: 48.h,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            border: Border(
              bottom: BorderSide(color: AppColors.dividerColor, width: 1.w),
            ),
            children: [
              HaloPosLabel(
                "正在更新",
                textStyle: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.bold,
                  decoration: TextDecoration.none,
                  color: AppColorHelper(context).getTitleTextColor(),
                ),
              ),
            ],
          ),
          HaloContainer(
            direction: Axis.vertical,
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
            children: [_progressIndicator()],
          ),
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.end,
            height: 60.h,
            padding: EdgeInsets.only(right: 36.w, bottom: 12.h, top: 12.h),
            children: [
              HaloButton(
                visible: widget.allowClosed,
                text: "关闭",
                buttonType: HaloButtonType.outlinedButton,
                height: 42.h,
                outLineWidth: 1.h,
                onPressed: () {
                  NavigateUtil.pop(context);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  final ReceivePort _port = ReceivePort();

  @override
  void initState() {
    super.initState();
    _bindBackgroundIsolate();
    initDownloader().then((value) => startDownload());
  }

  void _bindBackgroundIsolate() {
    bool isSuccess = IsolateNameServer.registerPortWithName(
      _port.sendPort,
      'downloader_send_port',
    );
    if (!isSuccess) {
      _unbindBackgroundIsolate();
      _bindBackgroundIsolate();
      return;
    }
    _port.listen((dynamic data) {
      debugPrint('UI Isolate Callback: $data');
      String id = data[0];
      DownloadTaskStatus status = DownloadTaskStatus.fromInt(data[1]);
      int progress = data[2];
      if (mounted) {
        setState(() {
          _currentProgress = progress;
        });
      }
      // 当下载完成时，调用安装
      if (status == DownloadTaskStatus.complete) {
        Future.delayed(const Duration(seconds: 2), () {
          _unbindBackgroundIsolate();
          FlutterDownloader.open(taskId: id);
        });
      }
      if (status == DownloadTaskStatus.failed) {
        HaloToast.showInfo(context, msg: "下载失败，请联系客服或者前往官网下载");
        _unbindBackgroundIsolate();
      }
    });
  }

  void _unbindBackgroundIsolate() {
    IsolateNameServer.removePortNameMapping('downloader_send_port');
  }

  @pragma('vm:entry-point')
  static void updateCallback(String id, int status, int progress) {
    debugPrint(
      "id:$id===== status=======:$status=====progress======:$progress",
    );
    final SendPort? send = IsolateNameServer.lookupPortByName(
      'downloader_send_port',
    );
    send?.send([id, status, progress]);
  }

  Future<void> initDownloader() async {
    WidgetsFlutterBinding.ensureInitialized();
    await FlutterDownloader.initialize(debug: kDebugMode);
    await FlutterDownloader.registerCallback(updateCallback);
  }

  void requestDownload(String apkUrl) async {
    //保存至 /storage/emulated/0/Android/data/$packname/cache/apk/"
    String? path = await getApkLocalPath();
    if (path != null) {
      //这个下载插件如果下载过该文件一次之后，再次下载会因为空指针导致下载失败
      //所以在下载之前要删除之前下载过的该文件
      final tasks = await FlutterDownloader.loadTasks();
      final task = tasks?.firstWhereOrNull((task) => task.url == apkUrl);
      if (task != null) {
        await FlutterDownloader.cancel(taskId: task.taskId);
      }
      String fileName = apkUrl.substring(apkUrl.lastIndexOf("/") + 1);
      File file = File("$path/$fileName");
      if (await file.exists()) {
        await file.delete();
      }
      taskId = await FlutterDownloader.enqueue(
        url: apkUrl,
        savedDir: path,
        showNotification: true,
        openFileFromNotification: true,
      );
    } else {
      HaloToast.show(context, msg: "获取存储路径失败");
      Navigator.pop(context);
    }
  }

  ///获取本地保存路径
  Future<String?> getApkLocalPath() async {
    Directory? directory;
    List<Directory>? cacheDirs = await getExternalCacheDirectories();
    if (cacheDirs != null && cacheDirs.isNotEmpty) {
      directory = cacheDirs.first;
    } else {
      directory = await getDownloadsDirectory();
    }
    if (directory?.path.isNotEmpty == true) {
      directory = Directory("${directory!.path}/apk");
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
    }
    return directory?.path.toString();
  }

  Widget _progressIndicator() {
    if (_currentProgress == -1) {
      _currentProgress = 100;
    }
    if (_currentProgress > 100) {
      _currentProgress = 100;
    }

    double padding = _progressbarWidth * (_currentProgress / 100);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.only(left: padding),
          child: _buildPercentWidget(_currentProgress),
        ),
        Padding(
          padding: EdgeInsets.only(
            left: _indicatorWidth / 2,
            right: _indicatorWidth / 2,
            top: 8.h,
          ),
          child: SizedBox(
            height: _progressbarHeight,
            width: _progressbarWidth,
            child: CustomLinearProgressIndicator(
              maxWidth: 100,
              value: _currentProgress / 100,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPercentWidget(int progress) {
    return Stack(
      alignment: AlignmentDirectional.topCenter,
      children: [
        SvgPicture.asset("assets/images/progress_indicator.svg", width: 63.w),
        Positioned(
          top: 5.h,
          child: Text(
            "$progress%",
            style: TextStyle(
              color: const Color(0xff333333),
              fontSize: 20.sp,
              decoration: TextDecoration.none,
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
    if (taskId != null) {
      FlutterDownloader.remove(taskId: taskId!);
    }
    _unbindBackgroundIsolate();
  }

  static Future<void> pop() async {
    await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
  }
}
