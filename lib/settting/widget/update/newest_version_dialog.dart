import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';

///
///@ClassName: newest_version_dialog
///@Description: 最新版本弹框
///@Author: tanglan
///@Date: 2024/7/16
//新版本的弹窗
class NewestVersionDialog extends StatelessWidget {
  const NewestVersionDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: HaloContainer(
            width: 600.w,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            padding: EdgeInsets.symmetric(vertical: 12.h),
            direction: Axis.vertical,
            color: Colors.white,
            children: [
          HaloContainer(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            height: 48.h,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            border: Border(
                bottom: BorderSide(color: AppColors.dividerColor, width: 1.w)),
            children: [
              HaloPosLabel(
                "系统更新",
                textStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.none,
                    color: AppColorHelper(context).getTitleTextColor()),
              )
            ],
          ),
          HaloContainer(
              direction: Axis.vertical,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              children: [
                Container(
                    margin: EdgeInsets.all(10.w),
                    padding: EdgeInsets.all(10.w),
                    child: SvgPicture.asset(
                      "assets/images/xitonggengxin.svg",
                      width: 100.w,
                      height: 100.h,
                    )),
                HaloPosLabel(
                  "当前已是最新版本，无需更新",
                  textStyle: TextStyle(
                      fontSize: 24.sp,
                      decoration: TextDecoration.none,
                      color: AppColors.secondTextColor),
                )
              ]),
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            width: 300.w,
            padding: EdgeInsets.symmetric(vertical: 24.h),
            children: [
              Expanded(
                  child: HaloButton(
                text: " 确定 ",
                borderColor: AppColors.btnBorderColor,
                textColor: AppColors.secondTextColor,
                buttonType: HaloButtonType.outlinedButton,
                height: 60.h,
                outLineWidth: 1.h,
                onPressed: () {
                  NavigateUtil.pop(context);
                },
              )),
            ],
          )
        ]));
  }
}
