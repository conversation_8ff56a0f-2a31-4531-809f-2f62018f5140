import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/haloui.dart';

import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/version_check.dart';
import '../../../entity/update/version_info.dart';
import '../../../widgets/base/halo_pos_alert_dialog.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:html/dom.dart' as dom;
import 'package:html/parser.dart';

///检查到新版本的弹窗
class UpdateDialog extends StatefulWidget {
  final VersionInfo versionInfo;
  final String localVersion;

  const UpdateDialog(this.versionInfo, this.localVersion, {Key? key})
    : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _UpdateDialogState();
  }
}

class _UpdateDialogState extends State<UpdateDialog> {
  ///是否强制更新
  late final bool _forceUpdate;

  ///版本名称
  late final String _versionName;

  @override
  void initState() {
    _forceUpdate =
        widget.versionInfo.forceUpdateAndroid ||
        VersionCheck.compareVersion(
          localVersion: widget.localVersion,
          remoteVersion: widget.versionInfo.minVersion,
        );
    _versionName = widget.versionInfo.versionName;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_forceUpdate,
      child: Center(
        child: HaloContainer(
          width: 500.w,
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          padding: EdgeInsets.symmetric(vertical: 12.h),
          direction: Axis.vertical,
          color: Colors.white,
          children: [
            HaloContainer(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              height: 48.h,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              border: Border(
                bottom: BorderSide(color: AppColors.dividerColor, width: 1.w),
              ),
              children: [
                HaloPosLabel(
                  "系统更新",
                  textStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.none,
                    color: AppColorHelper(context).getTitleTextColor(),
                  ),
                ),
              ],
            ),
            HaloContainer(
              constraints: BoxConstraints(maxHeight: 600.w, minHeight: 200.w),
              direction: Axis.vertical,
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              children: [
                Container(
                  margin: EdgeInsets.all(10.w),
                  padding: EdgeInsets.all(10.w),
                  child: SvgPicture.asset(
                    "assets/images/xitonggengxin.svg",
                    width: 100.w,
                    height: 100.h,
                  ),
                ),
                HaloPosLabel(
                  "当前版本：V${widget.localVersion}",
                  textStyle: TextStyle(
                    fontSize: 24.sp,
                    decoration: TextDecoration.none,
                    color: AppColors.secondTextColor,
                  ),
                ),
                HaloPosLabel(
                  "最新版本：V${widget.versionInfo.versionName}",
                  textStyle: TextStyle(
                    fontSize: 24.sp,
                    decoration: TextDecoration.none,
                    color: AppColors.secondTextColor,
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: HaloPosLabel(
                    "更新内容",
                    textStyle: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.none,
                      color: AppColors.normalTextColor,
                    ),
                  ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: HaloContainer(
                      children: [
                        HtmlText(htmlString: widget.versionInfo.updateMsg),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            HaloContainer(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 36.w),
              children: [
                Visibility(
                  visible: !_forceUpdate,
                  child: Expanded(
                    child: Container(
                      margin: EdgeInsets.only(right: 30.w),
                      child: HaloButton(
                        text: " 取消 ",
                        borderColor: AppColors.btnBorderColor,
                        textColor: AppColors.normalTextColor,
                        buttonType: HaloButtonType.outlinedButton,
                        height: 60.h,
                        outLineWidth: 1.h,
                        onPressed: () {
                          NavigateUtil.pop(context);
                        },
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: HaloButton(
                    text: " 立即更新 ",
                    height: 60.h,
                    outLineWidth: 1.h,
                    onPressed: () {
                      _onConfirmClick();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  ///点击了立即更新
  _onConfirmClick() async {
    if (Platform.isAndroid) {
      bool isGranted = await VersionCheck.checkPermission(context);
      if (!isGranted) {
        if (mounted) {
          HaloToast.showInfo(context, msg: "没有文件存储权限");
        }
        return;
      }
      if (mounted) {
        VersionCheck.showUpdatePage(context, widget.versionInfo, _forceUpdate);
      }
    } else if (Platform.isWindows) {
      await VersionCheck.downloadWindows(context, widget.versionInfo);
    }
  }
}

class HtmlText extends StatelessWidget {
  final String htmlString;

  const HtmlText({Key? key, required this.htmlString}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final document = parse(htmlString);
    final nodes = document.body?.nodes;

    final textSpans =
        nodes
            ?.map((node) {
              if (node is dom.Element) {
                if (node.localName == 'br') {
                  return TextSpan(
                    text: '\n',
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                  ); // Replace <br> with a new line character
                }
              }
              return TextSpan(
                text: node.text,
                style: TextStyle(
                  fontSize: 20.sp,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              );
            })
            .whereType<TextSpan>()
            .toList();

    return Expanded(child: RichText(text: TextSpan(children: textSpans)));
  }
}
