import 'package:flutter/material.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../iconfont/icon_font.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:webview_flutter/webview_flutter.dart';

///内置的浏览器页面，用来打开后台
class BrowserPage extends StatelessWidget {
  ///链接
  final String url;

  final WebViewController _controller;

  BrowserPage(this.url)
    : _controller =
          WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..loadRequest(Uri.parse(url));

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        if (await backWillPop()) {
          if (context.mounted) {
            Navigator.pop(context);
          }
        }
      },
      child: Scaffold(
        backgroundColor: AppColorHelper(context).getScaffoldBackgroundColor(),
        //有tabBar的页面通过控制buildAppBarBottom的preferredSize为0控制不显示titleBar
        drawerScrimColor: Colors.transparent,
        endDrawerEnableOpenDragGesture: false,
        appBar: buildAppBar(context),
        resizeToAvoidBottomInset: false,
        body: WebViewWidget(controller: _controller),
      ),
    );
  }

  Future<bool> backWillPop() {
    return _controller.canGoBack().then((value) {
      if (value) {
        _controller.goBack();
        return false;
      }
      return true;
    });
  }

  buildAppBar(BuildContext context) {
    return PreferredSize(
      child: AppBar(
        titleSpacing: 0.0,
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () => NavigateUtil.pop(context),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: IconFont(
                  IconNames.ngp_left_back,
                  size: 32.w,
                  color: ColorUtil.color2String(
                    AppColorHelper(context).getTitleBoldTextColor(),
                  ),
                ),
              ),
            ),
          ],
        ),
        centerTitle: false,
      ),
      preferredSize: Size.fromHeight(80.w),
    );
  }
}
