import 'package:flutter/cupertino.dart';
import '../../../common/net/http_util.dart';
import '../../../common/net/request_method.dart';
import '../../../entity/update/version_info.dart';
import 'package:halo_utils/halo_utils.dart';

class UpdateModel {
  ///获取版本信息
  static Future<VersionInfo?> checkVersionInfo(BuildContext context) {
    return HttpUtil.request(context,
            method: RequestMethod.GET_VERSION_INFO, requestModel: NetMethod.GET)
        .then((response) {
      if (response.data == null || response.code != 200) {
        return null;
      }
      return VersionInfo.fromMap(response.data);
    });
  }
}
