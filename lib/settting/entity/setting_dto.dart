import 'dart:io';

import 'package:bluetooth/bluetooth_dto.dart';
import 'package:flutter_pos_printer_platform_image_3/flutter_pos_printer_platform_image_3.dart';
import 'package:flutter_windows_bluetooth/entity/bluetooth_device.dart';
import 'package:hotkey_manager/hotkey_manager.dart';

import '../../../common/standard.dart';
import '../../../enum/setting/bill_page_type.dart';
import '../../../enum/setting/rounding_type.dart';
import '../../../settting/widget/system_config_page.dart';
import '../../hotkey/hotkey.dart';
import '../../plugin/third_party_scale_plugin.dart';

///[SystemConfigPage]设置页面中保存在本地的相关设置对应的实体类，
///需要注意，保存该类时需要关联‘店铺id’。
class SettingDto {
  ///是否按照商品维度进行搜索
  bool searchByPtype;

  ///是否开启零售价显示
  bool openFreshPriceVisible;

  ///是否开启fresh称重
  bool openFreshWight;

  ///是否开启会员消费密码
  bool openVipPwd;

  ///是否开启库存展示
  bool openStock;

  ///是否语音播报
  bool openTTS;

  ///是否开启抹零
  bool enableRounding;

  ///抹零类型,需要注意，这里保存的是该widget中的枚举下标
  int roundingType; //0 四舍五入，1只舍步入，2只入不舍

  // ///打印纸宽度，需要注意，这里保存的是该widget中枚举的下标
  // int printPaperWidth;
  //
  // ///小票打印张数 1-3张
  // int printPaperCount;

  ///副屏图片缩略图地址
  List<String> secondaryScreenThumbnailUrls;

  ///副屏图片地址 最多五张
  List<String> secondaryScreenPictureUrls;

  ///图片轮播时长，默认10秒，最多60秒
  int bannerTime;

  ///副屏视频地址
  String? secondaryVideoUrl;

  ///是否轮播，默认关闭
  bool isCarousel;

  ///开单方式（开单页面）
  int billPageType;

  ///打印机设备(Android蓝牙)
  BluetoothDto? androidBluetoothPrinter;

  ///打印机设备(Windows蓝牙)
  BluetoothDevice? windowsBluetoothPrinter;

  ///打印机设备
  PrinterDevice? usbPrinter;

  ///打印机类型
  int printerType;

  ///是否使用内置电子秤
  bool innerScale = Platform.isAndroid;

  ///第三方电子秤
  SimpleUsbDevice? thirdPartyScale;

  ///热键
  Map<HotkeyType, HotKey?> hotkeyMap;

  ///是否启用条码称
  bool enableBarcodeScale = false;

  ///是否启用副屏
  bool enableSecondScreen = false;

  SettingDto._(
    this.openFreshPriceVisible,
    this.openFreshWight,
    this.openVipPwd,
    this.enableRounding,
    this.roundingType,
    this.secondaryScreenThumbnailUrls,
    this.secondaryScreenPictureUrls,
    this.secondaryVideoUrl,
    this.bannerTime,
    this.isCarousel,
    this.billPageType,
    this.androidBluetoothPrinter,
    this.windowsBluetoothPrinter,
    this.usbPrinter,
    this.printerType,
    this.openStock,
    this.openTTS,
    this.innerScale,
    this.thirdPartyScale,
    this.searchByPtype,
    this.hotkeyMap,
    this.enableBarcodeScale,
    this.enableSecondScreen,
  );

  SettingDto.fromJson(Map<String, dynamic> json)
    : this._(
        json["openFreshPriceVisible"] ?? false,
        json["openFreshWight"] ?? false,
        json["enableRounding"] ?? false,
        json["openVipPwd"] ?? false,
        json["roundingType"] ?? DEFAULT_ROUNDING_TYPE.index,
        (json["secondaryScreenThumbnailUrls"] as List?)
                ?.map<String>((e) => e.toString())
                .toList() ??
            [],
        (json["secondaryScreenPictureUrls"] as List?)
                ?.map<String>((e) => e.toString())
                .toList() ??
            [],
        json["secondaryVideoUrl"] ?? "",
        json["bannerTime"] ?? 10,
        json["isCarousel"] ?? false,
        json["billPageType"] ?? BillPageType.SCAN.index,
        (json["androidBluetoothPrinter"] as Map?)?.cast<String, dynamic>().let(
          (e) => BluetoothDto.fromMap(e),
        ),
        (json["windowsBluetoothPrinter"] as Map?)?.cast<String, dynamic>().let(
          (e) => BluetoothDevice.fromMap(e),
        ),
        (json["usbPrinter"] as Map?)?.cast<String, dynamic>().let(
          (e) => _PrinterDeviceExtension.fromMap(e),
        ),
        json["printerType"] ??
            (Platform.isWindows ? PrinterType.usb : PrinterType.bluetooth)
                .index,
        json["openStock"] ?? false,
        json["openTTS"] ?? true,
        json["innerScale"] ?? Platform.isAndroid,
        (json["thirdPartyScale"] as Map?)?.cast<String, dynamic>().let(
          (e) => SimpleUsbDevice.fromMap(e),
        ),
        json["searchByPtype"] ?? false,
        Map<HotkeyType, HotKey?>.fromEntries(
          (json["hotkeyMap"] as Map<String, dynamic>?)?.entries
                  .expand<MapEntry<HotkeyType, HotKey?>>((entry) {
                    try {
                      return [
                        MapEntry(
                          HotkeyType.values.byName(entry.key),
                          (entry.value as Map<String, dynamic>?)?.let(
                            (value) => HotKey.fromJson(value),
                          ),
                        ),
                      ];
                    } catch (e) {
                      return [];
                    }
                  }) ??
              [],
        )..also(
          (map) => HotkeyType.values.forEach(
            (type) => map.putIfAbsent(type, () => defaultHotkeyMap[type]),
          ),
        ),
        json["enableBarcodeScale"] ?? false,
        json["enableSecondScreen"] ?? false,
      );

  ///默认值
  SettingDto.defaultDto()
    : openFreshWight = false,
      openFreshPriceVisible = false,
      openVipPwd = false,
      enableRounding = false,
      roundingType = DEFAULT_ROUNDING_TYPE.index,
      secondaryScreenThumbnailUrls = <String>[],
      secondaryScreenPictureUrls = <String>[],
      secondaryVideoUrl = "",
      isCarousel = false,
      bannerTime = 10,
      billPageType = BillPageType.SCAN.index,
      androidBluetoothPrinter = null,
      windowsBluetoothPrinter = null,
      usbPrinter = null,
      printerType =
          (Platform.isWindows ? PrinterType.usb : PrinterType.bluetooth).index,
      openStock = false,
      openTTS = true,
      innerScale = Platform.isAndroid,
      thirdPartyScale = null,
      searchByPtype = false,
      hotkeyMap = defaultHotkeyMap,
      enableBarcodeScale = false,
      enableSecondScreen = false;

  Map<String, dynamic> toJson() => {
    //使用jsonDecode必须实现此方法
    "openFreshWight": openFreshWight,
    "openFreshPriceVisible": openFreshPriceVisible,
    "openVipPwd": openVipPwd,
    "enableRounding": enableRounding,
    "roundingType": roundingType,
    "secondaryScreenThumbnailUrls": secondaryScreenThumbnailUrls,
    "secondaryScreenPictureUrls": secondaryScreenPictureUrls,
    "secondaryVideoUrl": secondaryVideoUrl,
    "bannerTime": bannerTime,
    "isCarousel": isCarousel,
    "billPageType": billPageType,
    "androidBluetoothPrinter": androidBluetoothPrinter?.toJson(),
    "windowsBluetoothPrinter": windowsBluetoothPrinter?.toJson(),
    "usbPrinter": usbPrinter?.toJson(),
    "printerType": printerType,
    "openStock": openStock,
    "openTTS": openTTS,
    "innerScale": innerScale,
    "thirdPartyScale": thirdPartyScale?.toJson(),
    "searchByPtype": searchByPtype,
    "hotkeyMap": hotkeyMap.map((key, value) => MapEntry(key.name, value)),
    "enableBarcodeScale": enableBarcodeScale,
    "enableSecondScreen": enableSecondScreen,
  };
}

extension _PrinterDeviceExtension on PrinterDevice {
  static PrinterDevice fromMap(Map<String, dynamic> json) {
    return PrinterDevice(
      name: json["name"],
      address: json["address"],
      vendorId: json["vendorId"],
      productId: json["productId"],
    );
  }

  Map<String, dynamic> toJson() => {
    "name": name,
    "address": address,
    "vendorId": vendorId,
    "productId": productId,
  };
}
