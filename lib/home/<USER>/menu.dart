import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:halo_pos/shiftchange/shiftchange_page_new.dart';
import 'package:halo_utils/halo_utils.dart' hide DateUtil;
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../bill/bill/bill_edit_sale_back.dart';
import '../../../bill/bill/bill_edit_sale_change.dart';
import '../../../bill/entity/order_bill_item_entity.dart';
import '../../../bill/tender/tender_bill.dart';
import '../../../bill/tendermanager/tender_manage.dart';
import '../../../bill/tool/sale_event_buds.dart';
import '../../../bill/transfer_and_allocate/transfer_request.dart';
import '../../../bill/widget/mixin/select_vip_mixin.dart';
import '../../../bill/widget/sale/sale_bill_to_sale_back_bill_dialog.dart';
import '../../../cashierbox/cash_box_dialog.dart';
import '../../../common/string_res.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../credits/credits_exchange.dart';
import '../../../entity/system/permission_dto.dart';
import '../../../enum/bill_type.dart';
import '../../../plugin/secondary_screen_plugin.dart';
import '../../../report/inventory_query.dart';
import '../../../settting/widget/browser_page.dart';
import '../../../settting/widget/system_config_page.dart';
import '../../../vip/add/vip_add_page.dart';
import '../../../vip/recharge/vip_recharge_page.dart';
import '../../../widgets/base/base_stateless.dart';
import '../../bill/baseinfo/baseinfo_ptype_add_page.dart';
import '../../bill/bill/channel/bill_channel_orders_query.dart';
import '../../bill/saleorder/sale_order_page.dart';
import '../../hotkey/hotkey_page.dart';
import '../../login/tool/shop_config_model.dart';
import '../../print/print_template_config.dart';
import '../../print/print_template_config_new.dart';
import '../../report/store_revenue_statistics.dart';
import '../../shiftchange/widget/shiftchanges_record.dart';
import '../../stockcheck/stock_check_record.dart';
import '../../vip/recharge_record/recharge_record.dart';

///定义一个点击menu中item的回调
typedef OpenChildPageCallback = Future Function(WidgetBuilder builder);

///左侧弹出的抽屉菜单
///需要将外部[Scaffold.drawer]设置为此组件
///如果不需要半透明黑色遮罩层，需手动设置[Scaffold.drawerScrimColor]为[Colors.transparent]
///如果不需要左滑弹出，则设置[Scaffold.drawerEnableOpenDragGesture]为false
///若不想appbar展示系统自带的drawer按钮，则设置[AppBar.leading]为自定义的按钮，或者设置[AppBar.automaticallyImplyLeading]为false
class Menu extends BaseStatelessPage {
  ///点击menu中item的回调
  final OpenChildPageCallback? _openChildPageCallback;

  PermissionDto get _permissionDto => SpTool.getPermission();

  ///菜单数据
  Map<String, List<MenuBean>> get _menuData {
    PermissionDto permission = _permissionDto;
    return {
      "收银": [
        // MenuBean("钱箱管理",icon: "qianxianguanli.svg"),
        MenuBean(
          "打开钱箱",
          icon: "dakaiqianxiang.svg",
          isHavePermission: permission.shopsalecashboxopen ?? false,
        ),
        const MenuBean("退货单", icon: "tuihuo.svg"),
        const MenuBean("换货单", icon: "tuihuo.svg"),
        const MenuBean("全渠道订单", icon: "quanjudaodingdan.svg"),
        // MenuBean("商品编辑",icon: "shangpinbianji.svg"),
      ],
      "库存": [
        MenuBean(
          "库存查询",
          icon: "kucunchaxun.svg",
          isHavePermission:
              permission.analysiscloudinventoryPositionview ?? false,
        ),
        MenuBean(
          "要货申请",
          icon: "yaohuoguanli.svg",
          isHavePermission:
              (permission.shopsaletransferOrdercreate ?? false) &&
              (permission.recordsheetGoodsTransOrderQueryview ?? false),
        ),
        MenuBean(
          "调货管理",
          icon: "yaohuoguanli.svg",
          isHavePermission:
              (permission.shopsaletenderManageview ?? false) &&
              (permission.analysiscloudouterInventorytransOnwayview ?? false),
        ),
        MenuBean(
          "要货管理",
          icon: "yaohuoguanli.svg",
          isHavePermission:
              (permission.shopsaletransferOrderview ?? false) &&
              (permission.recordsheetGoodsTransOrderQueryview ?? false),
        ),
        MenuBean(
          "库存调拨",
          icon: "yaohuoguanli.svg",
          isHavePermission:
              (permission.recordsheetTransGoodsBillcreate ?? false) &&
              (permission.analysiscloudouterInventorytransOnwayview ?? false),
        ),
        MenuBean(
          "新增商品",
          icon: "yaohuoguanli.svg",
          isHavePermission: permission.baseinfoptypeadd ?? false,
        ),
        // const MenuBean("库存盘点", icon: "pandian.svg"),
      ],
      "营销": [
        MenuBean(
          "新增会员",
          icon: "xinzenghuiyuan.svg",
          isHavePermission: permission.memberVipAdd ?? false,
        ),
        const MenuBean("积分兑换", icon: "jifenduihuan.svg"),
        MenuBean(
          "会员储值",
          icon: "huiyuanchongzhi.svg",
          isHavePermission: permission.shopsaleRechargeView ?? false,
        ),
        MenuBean(
          "会员储值记录",
          icon: "huiyuanchongzhi.svg",
          isHavePermission: permission.shopsalerechargeRecordview ?? false,
        ),
        // MenuBean("盘点",icon: "pandian.svg"),
      ],
      "报表": [
        // MenuBean("交接班记录",icon: "jiaojiebanjilu.svg"),
        MenuBean(
          "销售单据查询",
          icon: "xiaoshoudanjuchaxun.svg",
          isHavePermission: permission.recordsheetSaleBillQueryview ?? false,
        ),
        const MenuBean(
          "门店收入统计",
          icon: "xiaoshoudanjuchaxun.svg",
          isHavePermission: true,
        ),
        const MenuBean(
          "商品销售统计",
          icon: "shangpinxiaoshoutongji.svg",
          isHavePermission: true,
        ),
        // MenuBean("收银流水",icon: "shouyinliushui.svg"),
      ],
      "其他": [
        const MenuBean("系统配置", icon: "xitongpeizhi.svg"),
        const MenuBean("进入后台", icon: "jinruhoutai.svg"),
        MenuBean(
          "打印模版配置",
          icon: "dayinpeizhi.svg",
          isHavePermission: permission.shopsaleprintmodeledit ?? false,
        ),
        MenuBean(
          "交接班",
          icon: "jinruhoutai.svg",
          isHavePermission: permission.shopsaleshiftchangesview ?? false,
        ),
        MenuBean(
          "快捷键设置",
          isHavePermission: permission.shopsalehotkeyedit ?? false,
          icon: "kuaijiejianshezhi.svg",
        ),
      ],
    };
  }

  const Menu(this._openChildPageCallback, {Key? key}) : super(key: key);

  ///构建菜单分类ListView的内容
  Widget? _itemBuilder(
    BuildContext context,
    int index,
    Map<String, List<MenuBean>> menuData,
  ) {
    final iterator = menuData.entries.iterator;
    int i = 0;
    while (iterator.moveNext()) {
      if (i != index) {
        i++;
        continue;
      }
      final category = iterator.current;
      final currentCategory =
          category.value
              .where((element) => element.isHavePermission == true)
              .toList();
      return Visibility(
        visible: currentCategory.isNotEmpty,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(14.w),
            border: Border.all(color: const Color(0xFFE1E1E5), width: 1.w),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //每个分类的标题
              Container(
                width: double.infinity,
                padding: EdgeInsets.only(left: 29.w, top: 18.h),
                child: Text(
                  category.key,
                  style: TextStyle(
                    color: const Color(0xff333333),
                    fontSize: 28.sp,
                  ),
                ),
              ),
              //分类下的菜单,使用GridView实现
              GridView.builder(
                //是否根据子控件总高度确定自己的高度，默认为false，会尽可能多的设置自己的高度，
                //这里由于是在ListView中嵌套GridView，则必须设置为true,否则不显示
                shrinkWrap: true,
                //设置GridView不响应滑动事件，避免和ListView滑动冲突
                physics: const NeverScrollableScrollPhysics(),
                //规定GridView一行均分展示4个item，宽高比为1:1
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  childAspectRatio: 1.1,
                ),
                itemCount: currentCategory.length,
                itemBuilder: (context, position) {
                  final item = currentCategory[position];
                  return _buildMenuItem(context, item);
                },
              ),
            ],
          ),
        ),
      );
    }
    return null;
  }

  ///构建菜单每个分类下的每个按钮
  Widget _buildMenuItem(BuildContext context, MenuBean menu) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      //处理按钮点击
      onTap: () => _onMenuClick(context, menu),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            "assets/menu/${menu.icon ?? "qianxianguanli.svg"}",
            // "assets/menu/qianxianguanli.svg",
            width: 66.w,
            height: 66.w,
          ),
          Container(
            margin: EdgeInsets.only(top: 21.h),
            child: Text(
              menu.name,
              style: TextStyle(fontSize: 22.sp, color: const Color(0xff333333)),
            ),
          ),
        ],
      ),
    );
  }

  ///构建菜单标题栏
  PreferredSizeWidget? _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppColorHelper(context).getAppBarColor(),
      toolbarHeight: 80.w,
      primary: false,
      automaticallyImplyLeading: false,
      title: Text(
        getTitle(context),
        style: TextStyle(
          color: AppColorHelper(context).getTitleBoldTextColor(),
          fontSize: 34.sp,
        ),
      ),
      centerTitle: true,
    );
  }

  ///构建菜单内容
  Widget _buildMenuBody(BuildContext context) {
    final menuData = _menuData;
    return HaloContainer(
      direction: Axis.vertical,
      color: const Color(0xFFF6F7FB),
      children: [
        Expanded(
          child: Container(
            padding: EdgeInsets.only(
              top: 28.h,
              left: 23.w,
              right: 29.w,
              bottom: 28.h,
            ),
            child: ListView.separated(
              itemCount: menuData.length,
              separatorBuilder:
                  (context, index) =>
                      Divider(height: 27.h, color: Colors.transparent),
              itemBuilder:
                  (context, index) => _itemBuilder(context, index, menuData),
            ),
          ),
        ),
        SizedBox(
          height: 36.h,
          child: HaloLabel(
            "登录时间：${DateUtil.getDateStrByDateTime(DateTime.fromMillisecondsSinceEpoch(SpTool.getLoginTime())) ?? ""}",
          ),
        ),
      ],
    );
  }

  ///根据[context]得到上层的Scaffold，然后关闭drawer，
  ///然后根据[menu]按钮的类型，进行不同的操作，如打开页面
  ///注意!由于设计上是有外部的APP和内部APP，也就是两个回退栈，
  ///需要考虑：
  ///1.打开外部APP的页面(直接使用此widget的context启动即可)
  ///2.打开内部APP的页面(使用[_openChildPageCallback]回调)
  void _onMenuClick(BuildContext context, MenuBean menu) {
    Navigator.pop(context);
    Widget? target;
    switch (menu.name) {
      case "新增商品":
        target = const AddGoodsPage();
        break;
      case "换货单":
        openSaleChange(context);
        return;
      case "退货单":
        openSaleBack(context);
        return;
      case "交接班":
        exitLogin(context);
        return;
      case "快捷键设置":
        showHotkeyManagerPage(context);
        return;
      case "全渠道订单":
        target = const BillChannelOrdersQuery();
        break;
      case "打开钱箱":
        openCashBox(context);
        return;
      case "进入后台":
        _openBrowser(context);
        return;
      case "系统配置":
        target = const SystemConfigPage();
        break;
      case "销售单据查询":
        target = const SaleOrderList();
        break;
      case "打印模版配置":
        target = const PrintTemplateConfigNewPage();
        break;
      case "要货申请":
        target = const TenderBillPages();
        break;
      case "调货管理":
        target = const TenderManagePages();
        break;
      case "要货管理":
        target = const TransferRequestManagePage();
        break;
      case "积分兑换":
        _onVipClick(context);
        // target = CreditsExchangePages();
        break;
      case "会员储值":
        context = _vipRecharge(context);
        break;
      case "会员储值记录":
        target = const RechargeRecordPage();
        break;
      case "库存调拨":
        target = const TenderBillPages(billType: TenderBillType.StockBillApply);
        break;
      case "新增会员":
        target = const VipAddPage();
        break;
      case "库存盘点":
        target = const StockCheckRecord();
        break;
      case "门店收入统计":
        target = const StoreRevenueStatistics();
        break;
      case "商品销售统计":
        target = const ShiftChangesRecord();
        break;
      case "库存查询":
        showInventory(context);
        break;
    }
    // if (!_permissionDto.recordsheetGoodsTransQueryview &&
    //     target is TenderBillPages) {
    //   HaloToast.showError(context, msg: "没有查看调拨管理的权限");
    //   return;
    // }
    //跳转到子页面
    _navigateToChildPage(context, target);
  }

  ///会员充值
  BuildContext _vipRecharge(BuildContext context) {
    context = Navigator.of(context).context;
    //选择会员
    SelectVipMixin.searchVip(context, jumpToAdd: true).then((vipInfo) {
      if (vipInfo != null && vipInfo.vip?.expired != true) {
        Widget target = VipRechargePage(vipInfo: vipInfo);
        SecondaryScreenPlugin.hide();
        _navigateToChildPage(
          context,
          target,
        ).then((value) => SecondaryScreenPlugin.showSecondaryScreenImage());
      }
    });
    return context;
  }

  ///积分兑换
  void _onVipClick(BuildContext context) async {
    context = Navigator.of(context).context;
    //选择会员
    SelectVipMixin.searchVip(context, jumpToAdd: true).then((vipInfo) {
      if (vipInfo != null && !vipInfo.vip!.expired!) {
        Widget target = CreditsExchangePages(vipInfo: vipInfo);
        SecondaryScreenPlugin.hide();
        _navigateToChildPage(
          context,
          target,
        ).then((value) => SecondaryScreenPlugin.showSecondaryScreenImage());
      }
    });
  }

  ///打开后台网页
  Future<void> _openBrowser(BuildContext context) async {
    context = Navigator.of(context).context;
    ResponseModel responseModel = await ShopConfigModel.registerLoginUsing(
      context,
    );
    if (context.mounted) {
      String url = "http://www.wsgjp.com";
      if (responseModel.code != 200 ||
          null == responseModel.data ||
          StringUtil.isEmpty(responseModel.data["loginUrl"])) {
        HaloToast.show(context, msg: "自动登录失败，请手动登录");
      } else {
        url =
            responseModel.data["loginUrl"] +
            "?accesstoken=" +
            responseModel.data["accessToken"];
      }
      if (Platform.isWindows) {
        launchUrl(Uri.parse(url));
        return;
      }
      _navigateToChildPage(context, BrowserPage(url));
    }
  }

  ///打开钱箱
  static void openCashBox(BuildContext context) async {
    showDialog(context: context, builder: (_) => const CashBoxDialog());
  }

  ///退货单
  static void openSaleBack(BuildContext context) {
    if (!(SpTool.getPermission().recordsheetSaleBillview ?? false)) {
      HaloToast.showInfo(context, msg: "没有销售单查看权限!");
      return;
    }
    if (!(SpTool.getPermission().recordsheetSaleBackBillcreate ?? false)) {
      HaloToast.showInfo(context, msg: "没有退货单开单权限!");
      return;
    }
    BuildContext newContext = Navigator.of(context).context;
    HaloDialog(
      context,
      title: "选择单据",
      isAutoClose: false,
      backgroundColor: ColorUtil.stringColor("A6A6A6"),
      child: SaleBillToSaleBackBillDialog(
        billType: BillType.SaleBackBill,
        submitCallback: (OrderBillItem selectItem) {
          Navigator.push(
            newContext,
            MaterialPageRoute(
              settings: const RouteSettings(name: "SaleBill"),
              builder: (context) {
                return BillEditSaleBackPage(saleBill: selectItem);
              },
            ),
          ).then((value) {
            SaleEventBus.getInstance().fire(SaleEventBus.updateVip);
          });
        },
      ),
    ).show();
  }

  ///换货单
  static void openSaleChange(BuildContext context) {
    if (SpTool.getPermission().recordsheetSaleChangeBillcreate == false) {
      HaloToast.showMsg(context, msg: "没有销售换货单创建权限!");
      return;
    }
    BuildContext newContext = Navigator.of(context).context;
    HaloDialog(
      context,
      title: "选择单据",
      isAutoClose: false,
      backgroundColor: ColorUtil.stringColor("A6A6A6"),
      child: SaleBillToSaleBackBillDialog(
        billType: BillType.SaleChangeBill,
        submitCallback: (OrderBillItem selectItem) {
          Navigator.push(
            newContext,
            MaterialPageRoute(
              settings: const RouteSettings(name: "SaleBill"),
              builder: (context) {
                return BillEditSaleChangePage(saleBill: selectItem);
              },
            ),
          ).then((value) {
            SaleEventBus.getInstance().fire(SaleEventBus.updateVip);
          });
        },
      ),
    ).show();
  }

  ///退出登录，清空数据并且跳转至登录页面
  static void exitLogin(BuildContext context) async {
    // HaloLoginCenter.loginOut(context, loginOutSuccess: () {}).whenComplete(() {
    //   SpTool.saveEtypeInfo("", "");
    //   Navigator.of(context, rootNavigator: true).pushReplacement(
    //       MaterialPageRoute(builder: (context) => LoginPage()));
    // });
    NavigateUtil.navigateTo(context, const ShiftChangePageNew());
  }

  ///库存查询弹框
  static void showInventory(BuildContext context) {
    DialogUtil.showAlertDialog(context, child: const InventoryQuery());
  }

  // ///打开副屏
  // static void openSecondScreen(BuildContext context) {
  //   SettingDto settingDto = SpTool.getSetting();
  //   if (!settingDto.enableSecondScreen) {
  //     HaloToast.show(context, msg: "请先在系统设置-副屏设置中启用副屏");
  //     return;
  //   }
  //   showSecondaryWindow();
  // }

  ///跳转到子页面
  Future _navigateToChildPage(BuildContext context, Widget? target) {
    if (_openChildPageCallback != null && target != null) {
      return _openChildPageCallback!((BuildContext context) => target);
    }
    return Future.value(null);
  }

  @override
  Widget buildBody(BuildContext context) {
    return SizedBox(
      width: 767.w,
      child: Padding(
        padding: EdgeInsets.only(
          top: 70.w + MediaQuery.of(context).padding.top,
        ),
        child: Scaffold(
          primary: false,
          appBar: _buildAppBar(context),
          body: _buildMenuBody(context),
        ),
      ),
    );
  }

  @override
  String getTitle(BuildContext context) =>
      StringRes.MENU_TITLE.getText(context);
}

///按钮实体类
class MenuBean {
  ///按钮名称
  final String name;

  ///按钮logo
  final String? icon;

  final bool isHavePermission;

  const MenuBean(this.name, {this.icon, this.isHavePermission = true});

  @override
  bool operator ==(Object other) {
    if (other is MenuBean) {
      return name == other.name && icon == other.icon;
    }
    return false;
  }

  @override
  int get hashCode {
    const i = 17;
    var result = 1;
    result = result * i + name.hashCode;
    result = result * i + icon.hashCode;
    return result;
  }
}
