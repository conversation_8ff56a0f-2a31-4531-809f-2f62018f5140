import 'package:flutter/material.dart';
import 'package:halo_pos/shiftchange/shiftchange_page_new.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/haloui.dart';

import '../../../bill/widget/sale/promotion_page.dart';
import '../../../common/login/login_center.dart';
import '../../../common/string_res.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/permission_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../common/tool/version_check.dart';
import '../../settting/widget/update/newest_version_dialog.dart';

///
///@ClassName: custom_app_bar
///@Description: 顶部appbar
///@Author: tanglan
///@Date: 7/29/21 2:25 PM
const double iconSize = 46;

class CustomAppBar extends StatelessWidget {
  Function? menuClick;
  Function? messageClick;
  Function? onlineClick;
  Function? syncClick;
  bool hasNewVersion;

  final PermissionDto _permissionDto = SpTool.getPermission();

  CustomAppBar(
      {Key? key,
      this.menuClick,
      this.messageClick,
      this.onlineClick,
      this.syncClick,
      this.hasNewVersion = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        HaloContainer(
          padding: EdgeInsets.only(right: 18.w),
          height: 70.w,
          color: const Color(0xFF1361DB),
          mainAxisSize: MainAxisSize.max,
          children: [
            _buildIconBtn(IconNames.caidan, onTap: menuClick),
            _buildDivider(),
            _buildTextContainer(
                "收银机:${SpTool.getCashierInfo().fullname ?? ""}"),
            _buildDivider(),
            _buildTextContainer(
                "收银员:${LoginCenter.getLoginUser().etypeName ?? ""}"),
            Visibility(
                child: _buildHandover(context),
                visible: _permissionDto.shopsaleshiftchangesview ?? false),
            Expanded(
              child: Container(),
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                syncClick!();
              },
              child: Container(
                  margin: EdgeInsets.all(10.w),
                  height: iconSize.w,
                  width: iconSize.w,
                  padding: EdgeInsets.all(10.w),
                  child: Image.asset('assets/images/sync.png')),
            ),
            _buildIconBtn(IconNames.xiaoxi, onTap: messageClick),
            // _buildIconBtn(IconNames.shuaxin, onTap: () {
            //   HaloDialog(context,
            //       child: InfoSyncDialog(
            //         sycInfoList: const [
            //           SyncInfoType.permissionConfig,
            //           SyncInfoType.pType
            //         ],
            //         isAutoClose: false,
            //         afterSync: () {
            //           SaleEventBus.getInstance().fire(SaleEventBus.doClear);
            //           SaleEventBus.getInstance()
            //               .fire(SaleEventBus.refreshSearchedGoods);
            //           SaleEventBus.getInstance()
            //               .fire(SaleEventBus.updateSaleBillView);
            //         },
            //       )).show();
            // }),
            _buildIconBtn(IconNames.cuxiao, onTap: () {
              _doPromotion(context);
            }),
            _buildIconBtn(IconNames.zhuanshulaoshi, onTap: onlineClick),
            _buildUpdateBtn(context),
            IconFont(
              IconNames.lianjie_copy,
              size: 30.w,
            ),
          ],
        ),
        _buildTitle(context),
      ],
    );
  }

  _buildTitle(BuildContext context) {
    return Center(
        child: Text(
      StringRes.APP_NAME.getText(context),
      style: TextStyle(fontSize: 26.sp, color: Colors.white),
    ));
  }

  ///icon 按钮
  _buildIconBtn(IconNames name, {Function? onTap}) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (onTap != null) {
          onTap();
        }
      },
      child: Container(
          margin: EdgeInsets.all(10.w),
          height: iconSize.w,
          width: iconSize.w,
          padding: EdgeInsets.all(10.w),
          child: IconFont(
            name,
            color: "#ffffff",
          )),
    );
  }

  ///更新按钮 按钮
  _buildUpdateBtn(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          ///检查更新

          VersionCheck.checkUpdate(context).then((hasNewVersion) {
            if (!hasNewVersion) {
              return HaloDialog(context, child: const NewestVersionDialog())
                  .show();
            }
          });
        },
        child: Container(
            margin: EdgeInsets.all(10.w),
            height: iconSize.w,
            width: iconSize.w,
            padding: EdgeInsets.all(10.w),
            child: Stack(
              children: [
                SizedBox(
                    height: iconSize.w,
                    width: iconSize.w,
                    child: IconFont(
                      IconNames.xitonggengxin,
                      color: "#ffffff",
                    )),
                Visibility(
                    visible: hasNewVersion,
                    child: Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          width: 8.0,
                          height: 8.0,
                          decoration: const BoxDecoration(
                              shape:
                                  BoxShape.circle, //可以设置角度，BoxShape.circle直接圆形
                              color: Colors.red),
                        ))),
              ],
            )));
  }

  ///交接班
  _buildHandover(BuildContext context) {
    return GestureDetector(
      child: Container(
          width: 110.w,
          height: 40.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: const Color(0xFF4587FE),
              borderRadius: BorderRadius.all(Radius.circular(36.w))),
          child: HaloPosLabel(
            "交接班",
            textStyle: TextStyle(
              fontWeight: FontWeight.normal,
              fontSize: 22.sp,
              color: Colors.white,
            ),
          )),
      onTap: () {
        NavigateUtil.navigateTo(context, const ShiftChangePageNew());
      },
    );
  }

  _buildTextContainer(String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          margin: EdgeInsets.symmetric(horizontal: 20.w),
          constraints: BoxConstraints(
            maxWidth: 240.w,
          ),
          child: HaloPosLabel(
            text,
            textStyle: TextStyle(
              fontWeight: FontWeight.normal,
              fontSize: 24.sp,
              color: Colors.white,
            ),
          ),
        )
      ],
    );
  }

  _buildDivider() {
    return Container(
      height: 34.h,
      width: 1.w,
      color: Colors.white,
    );
  }

  _doPromotion(BuildContext context) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => PromotionPage()).then((value) {});
  }
}
