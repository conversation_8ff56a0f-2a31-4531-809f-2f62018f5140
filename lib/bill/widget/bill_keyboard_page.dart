import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import 'package:halo_pos/common/tool/dialog_util.dart';
import '../../../bill/model/ptype_model.dart';
import '../../../common/tool/text_input_util.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;

import '../../../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../../../bill/entity/ptype/ptype_prop_dto.dart';
import '../../../bill/tool/props_helper.dart';
import '../../../bill/widget/mixin/select_goods_and_combo_mixin.dart';
import '../../../common/keyboard_hidden.dart';
import '../../../iconfont/icon_font.dart';
import '../../common/standard.dart';
import '../../common/tool/sp_tool.dart';
import '../../entity/select_wrapper_bean.dart';
import '../../widgets/selector/scan_select.dart';
import '../entity/ptype/goods_type.dart';
import '../entity/ptype/ptype_dto.dart';
import '../tool/bill_tool.dart';
import '../tool/sale_event_buds.dart';

///带键盘和搜索商品的开单页控件
class BillGoodsAndKeyboardWidget extends StatefulWidget {
  ///商品点击回调
  final ValueChanged<PtypeListModel>? goodsClickCallback;

  final KeyboardHiddenFocusNode? focusNode;

  ///是否按商品维度进行搜索
  final bool searchByPtype;

  const BillGoodsAndKeyboardWidget({
    Key? key,
    this.focusNode,
    this.goodsClickCallback,
    this.searchByPtype = false,
  }) : super(key: key);

  @override
  State<BillGoodsAndKeyboardWidget> createState() =>
      _BillGoodsAndKeyboardWidgetState();
}

class _BillGoodsAndKeyboardWidgetState extends State<BillGoodsAndKeyboardWidget>
    with SelectGoodsAndComboMixin {
  ///按键高度
  static final _keyHeight = 86.h;

  ///按键横向间距
  static final _keySpacingHorizontal = 4.w;

  ///按键纵向间距
  static final _keySpacingVertical = 6.h;

  ///默认图片
  final defaultImage = Image.asset('assets/images/zanwutupian.png');

  ///输入框controller
  final TextEditingController _textEditingController = TextEditingController();

  ///当前搜索词
  @override
  String filterValue = "";

  ///自定义键盘是否展示
  bool _isKeyboardShow = false;

  KeyboardHiddenFocusNode? _focusNode;

  KeyboardHiddenFocusNode get focusNode =>
      widget.focusNode ?? (_focusNode ??= KeyboardHiddenFocusNode());

  ///顶部分类列表滚动controller
  final ScrollController scrollController = ScrollController();

  ///分类列表
  final List<GoodsType> typeList = [];

  ///当前一级分类
  GoodsType currentFirstType = GoodsType.all;

  ///当前二级分类
  GoodsType? currentSecondType;

  ///商品类型
  @override
  String? get typeId => currentSecondType?.typeid ?? currentFirstType.typeid;

  ///是否按照商品维度进行搜索
  @override
  bool get searchByPtype => widget.searchByPtype;

  ///控件宽度
  double width = double.infinity;

  ///高度
  double height = double.infinity;

  bool showStock = false;

  StreamSubscription? streamSubscription;

  @override
  void initState() {
    super.initState();
    getPtypeAllRootType();
    streamSubscription = SaleEventBus.getInstance().on<String>().listen((
      event,
    ) {
      if (event == SaleEventBus.refreshSearchedGoods && mounted) {
        _resetAndRefresh();
        getPtypeAllRootType();
      }
    });
  }

  @override
  void didUpdateWidget(covariant BillGoodsAndKeyboardWidget oldWidget) {
    if (oldWidget.searchByPtype != widget.searchByPtype) {
      _resetAndRefresh();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    super.dispose();
    streamSubscription?.cancel();
  }

  ///选中一个一级分类
  void _selectFirstType(GoodsType type) {
    currentFirstType = type;
    if (type.children.isNotEmpty) {
      currentSecondType = type.children.first;
    } else {
      currentSecondType = null;
    }
  }

  void _resetAndRefresh() {
    setState(() {
      _textEditingController.text = "";
      goodsAndComboList.clear();
      _selectFirstType(GoodsType.all);
      scrollController.jumpTo(0);
    });
    refresh();
  }

  @override
  Widget build(BuildContext context) {
    Widget result = Container(
      color: AppColors.pageBackgroundColor,
      child: Column(
        children: [
          SizedBox(height: 10.h),
          buildTypeList(
            context,
            typeList: typeList,
            isSelected: (type) => currentFirstType == type,
            typeSetter: (type) {
              setState(() => _selectFirstType(type));
              _textEditingController.text = "";
              filterValue = "";
              refresh();
            },
          ),
          if (currentFirstType.children.isNotEmpty) ...[
            SizedBox(height: 1.h),
            buildTypeList(
              context,
              height: 46,
              fontSize: 22,
              showSelectedBackground: false,
              typeList: currentFirstType.children,
              isSelected: (type) => currentSecondType == type,
              typeSetter: (type) {
                setState(() => currentSecondType = type);
                _textEditingController.text = "";
                filterValue = "";
                refresh();
              },
            ),
          ],
          Expanded(child: buildRefreshWidget()),
          HaloKeyboardAvoiding(child: _buildKeyboardAndSearchBar()),
        ],
      ),
    );
    if (searchByPtype) {
      return LayoutBuilder(
        builder: (context, constraints) {
          width = constraints.maxWidth;
          height = constraints.maxHeight;
          return result;
        },
      );
    }
    return result;
  }

  ///构建横向分类列表
  Widget buildTypeList(
    BuildContext context, {
    required List<GoodsType> typeList,
    required ValueSetter<GoodsType> typeSetter,
    required bool Function(GoodsType type) isSelected,
    bool showSelectedBackground = true,
    num height = 60,
    num fontSize = 24,
  }) {
    return Container(
      height: height.h,
      width: double.infinity,
      color: Colors.white,
      margin: EdgeInsets.symmetric(horizontal: 5.w),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        controller: scrollController,
        itemCount: typeList.length,
        itemBuilder: (context, index) {
          final item = typeList[index];
          bool isCurrent = isSelected(item);
          Color color = Colors.white;
          if (showSelectedBackground && isCurrent) {
            color = AppColors.accentColor;
          }
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            color: color.withOpacity(0.14),
            alignment: Alignment.center,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () => typeSetter(item),
              child: Text(
                item.fullname,
                maxLines: 1,
                style: TextStyle(
                  fontSize: fontSize.sp,
                  color:
                      isCurrent
                          ? AppColors.accentColor
                          : AppColors.normalFontColor,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  ///商品列表
  @override
  Widget buildGoodsList() {
    showStock = SpTool.getSetting().openStock;
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        mainAxisSpacing: 10.w,
        crossAxisSpacing: 10.h,
        mainAxisExtent: 180.h,
      ),
      itemBuilder: _buildGoodsItem,
      itemCount: goodsAndComboList.length,
    );
  }

  @override
  Widget buildRefreshWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      child: super.buildRefreshWidget(),
    );
  }

  ///构建图片
  Widget _buildItemImage(String picUrl, {int size = 100}) {
    return SizedBox(
      width: size.h,
      height: size.h,
      child: Image.network(
        picUrl,
        loadingBuilder: (
          BuildContext context,
          Widget child,
          ImageChunkEvent? loadingProgress,
        ) {
          if (loadingProgress == null) {
            return child;
          }
          return defaultImage;
        },
        errorBuilder:
            (BuildContext context, Object error, StackTrace? stackTrace) =>
                defaultImage,
      ),
    );
  }

  ///单个商品item
  Widget _buildGoodsItem(BuildContext context, int index) {
    PtypeListModel item = goodsAndComboList[index];
    String picUrl;
    if (!TextUtil.isEmpty(item.sku?.picUrl)) {
      picUrl = item.sku?.picUrl ?? "";
    } else {
      picUrl = item.picUrl ?? "";
    }
    TextStyle textStyle = TextStyle(
      overflow: TextOverflow.ellipsis,
      fontSize: AppPosSize.contentFontSize.sp,
      color: AppColors.describeFontColor,
    );
    String property = "";
    String standard = _buildStandardString(item);
    String type = _buildTypeString(item);
    String stock = "";
    String code = _buildCodeString(item);
    if (!searchByPtype) {
      property = _buildGoodsPropertyString(item);
    }
    if (showStock) {
      stock = _buildStock(item);
    }
    Widget image = _buildItemImage(picUrl);
    if (StringUtil.isNotEmpty(picUrl)) {
      image = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap:
            () => DialogUtil.showAlertDialog(
              context,
              actionCount: 0,
              childContent: _buildItemImage(picUrl, size: 500),
            ),
        child: image,
      );
    }
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () async => await returnGoodsToBill(context, item),
      child: HaloContainer(
        direction: Axis.vertical,
        color: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 14.w),
        borderRadius: BorderRadius.circular(4.w),
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          //商品/套餐名称
          HaloLabel(
            item.fullname,
            textStyle: textStyle.copyWith(
              fontSize: AppPosSize.totalFontSize.sp,
              color: AppColors.normalFontColor,
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 1,
          ),
          Expanded(
            child: HaloContainer(
              margin: EdgeInsets.only(top: 8.h),
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //图片
                image,
                Expanded(
                  child: HaloContainer(
                    margin: EdgeInsets.only(left: 16.w),
                    direction: Axis.vertical,
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            //商品编号
                            if (code.isNotEmpty)
                              HaloLabel(
                                code,
                                textStyle: textStyle,
                                maxLines: 1,
                              ),
                            //属性
                            if (property.isNotEmpty)
                              HaloLabel(
                                property,
                                textStyle: textStyle,
                                maxLines: 1,
                              ),
                            //规格
                            if (standard.isNotEmpty)
                              HaloLabel(
                                standard,
                                textStyle: textStyle,
                                maxLines: 1,
                              ),
                            //型号
                            if (type.isNotEmpty)
                              HaloLabel(
                                type,
                                textStyle: textStyle,
                                maxLines: 1,
                              ),
                            //库存
                            if (stock.isNotEmpty)
                              HaloLabel(
                                stock,
                                textStyle: textStyle,
                                maxLines: 1,
                              ),
                          ],
                        ),
                      ),
                      //价格
                      RichText(
                        textAlign: TextAlign.end,
                        maxLines: 1,
                        text: TextSpan(
                          text:
                              "￥${item.currencyPrice ?? 0}${(searchByPtype && (item.unitSkuPtypeList?.length ?? 0) > 1) ? "起" : ""}",
                          style: TextStyle(
                            color: AppColors.totalFontColor,
                            fontSize: AppPosSize.totalFontSize.sp,
                          ),
                          children: [
                            ///单位为空或者商品维度展示，都不显示单位
                            TextSpan(
                              text:
                                  (StringUtil.isEmpty(item.unit?.unitName) ||
                                          searchByPtype)
                                      ? ""
                                      : "/${item.unit?.unitName}",
                              style: TextStyle(
                                color: AppColors.describeFontColor,
                                fontSize: AppPosSize.describeFontSize.sp,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///商品编号
  String _buildCodeString(PtypeListModel item) {
    if (item.usercode?.isNotEmpty == true) {
      return "商品编号：${item.usercode}";
    }
    return "";
  }

  ///返回商品给开单界面
  Future<void> returnGoodsToBill(
    BuildContext context,
    PtypeListModel item, {
    bool showSkuSelectDialog = true,
  }) async {
    PtypeListModel? ptype;
    //如果是按照商品维度搜索，需要先弹窗选择商品的属性和单位
    if (searchByPtype) {
      //套餐无需选择属性，直接跳转
      if (item.comboRow ||
          item.pcategory == 2 ||
          item.unitSkuPtypeList?.length == 1) {
        ptype = item.unitSkuPtypeList?.first;
      } else if (showSkuSelectDialog) {
        ptype = await showDialog(
          context: context,
          builder:
              (context) => _UnitSkuSelectDialog(
                ptype: item,
                width: width,
                height: height,
              ),
          barrierDismissible: true,
        );
      }
    } else {
      ptype = item;
    }
    if (ptype != null) {
      widget.goodsClickCallback?.call(ptype);
    }
  }

  ///库存
  String _buildStock(PtypeListModel item) {
    String stock =
        item.stockQty == null
            ? "0"
            : BillTool.getStockQty(
              item.stockQty,
              item.unit?.unitRate,
              item.pcategory,
            );
    return "库存：$stock";
  }

  ///单位
  String _buildUnitString(PtypeListModel item) {
    if (item.unit?.unitName?.isNotEmpty == true) {
      return "单位：${item.unit?.unitName}";
    }
    return "";
  }

  ///规格
  String _buildStandardString(PtypeListModel item) {
    if (item.standard?.isNotEmpty == true) {
      return "规格：${item.standard}";
    }
    return "";
  }

  ///型号
  String _buildTypeString(PtypeListModel item) {
    if (item.ptypeType?.isNotEmpty == true) {
      return "型号：${item.ptypeType}";
    }
    return "";
  }

  ///构建商品属性拼接字符串
  String _buildGoodsPropertyString(PtypeListModel item) {
    List<PtypePropDto> prop = PropsHelper.getPropListBySkuProps(item.sku);
    if (prop.isNotEmpty) {
      StringBuffer property = StringBuffer("属性：");
      for (int i = 0; i < prop.length; i++) {
        if (i != 0) {
          property.write(":");
        }
        property.write(prop[i].propvalueName ?? "");
      }
      return property.toString();
    }
    return "";
  }

  ///键盘和搜索框
  Widget _buildKeyboardAndSearchBar() {
    return Container(
      width: double.infinity,
      color: const Color(0xFFDCDDE0),
      padding: EdgeInsets.only(
        top: 12.h,
        bottom: 10.h,
        left: 10.w,
        right: 10.w,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildSearchBar(),
          Visibility(visible: _isKeyboardShow, child: _buildKeyboard()),
        ],
      ),
    );
  }

  ///搜索框和键盘收起/弹出按钮
  Widget _buildSearchBar() {
    return SizedBox(
      height: 66.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ScanSelectWidget(
            searchFocusNode: focusNode,
            width: 680.w,
            controller: _textEditingController,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4.w),
            ),
            hint: searchByPtype ? "商品名称/商品编号/拼音码" : "商品名称/商品编号/拼音码/条码",
            onSubmitted: (text) {
              _textEditingController.text = text;
              _search();
            },
            onTapBefore: () {
              //当自定义键盘弹出时，拦截点击弹出系统键盘的事件
              return !_isKeyboardShow;
            },
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              setState(() {
                if (!_isKeyboardShow) {
                  // FocusScope.of(context).requestFocus(focusNode);
                  SystemChannels.textInput.invokeMethod<void>('TextInput.hide');
                }
                _isKeyboardShow = !_isKeyboardShow;
              });
            },
            child: Container(
              width: 86.w,
              height: 66.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4.w),
              ),
              alignment: Alignment.center,
              child: Transform.rotate(
                angle: _isKeyboardShow ? 0 : pi,
                child: IconFont(IconNames.jianpan_1, size: 30.w),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///键盘
  Widget _buildKeyboard() {
    return Padding(
      padding: EdgeInsets.only(top: 10.h),
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          //字母区域和数字区域之间的间距
          double spacingBetweenLetterAndNumber = 12.w;
          //计算按键宽度，根据父控件的宽度-按键之间的间隔,再除以按键数
          //第一排，一共 QWERTYUIOP 10个字母 和 7890 4个数字 ,也就是12(9+3)个按键间距，再加上两个区域的间隔
          double keyWidth =
              (constraints.maxWidth -
                  spacingBetweenLetterAndNumber -
                  12 * _keySpacingHorizontal) /
              14;
          return Row(
            children: [
              //左边字母键盘
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildKeys([
                      const _Key(content: "Q"),
                      const _Key(content: "W"),
                      const _Key(content: "E"),
                      const _Key(content: "R"),
                      const _Key(content: "T"),
                      const _Key(content: "Y"),
                      const _Key(content: "U"),
                      const _Key(content: "I"),
                      const _Key(content: "O"),
                      const _Key(content: "P"),
                    ], keyWidth),
                    SizedBox(height: _keySpacingVertical),
                    _buildKeys([
                      const _Key(content: "A"),
                      const _Key(content: "S"),
                      const _Key(content: "D"),
                      const _Key(content: "F"),
                      const _Key(content: "G"),
                      const _Key(content: "H"),
                      const _Key(content: "J"),
                      const _Key(content: "K"),
                      const _Key(content: "L"),
                    ], keyWidth),
                    SizedBox(height: _keySpacingVertical),
                    _buildKeys([
                      //占位空键
                      const _Key(),
                      const _Key(content: "Z"),
                      const _Key(content: "X"),
                      const _Key(content: "C"),
                      const _Key(content: "V"),
                      const _Key(content: "B"),
                      const _Key(content: "N"),
                      const _Key(content: "M"),
                      _Key.delete,
                    ], keyWidth),
                  ],
                ),
              ),
              SizedBox(width: spacingBetweenLetterAndNumber),
              //右边数字键盘
              Row(
                children: [
                  _buildKeys(
                    [
                      const _Key(content: "7"),
                      const _Key(content: "4"),
                      const _Key(content: "1"),
                    ],
                    keyWidth,
                    direction: Axis.vertical,
                  ),
                  SizedBox(width: _keySpacingHorizontal),
                  _buildKeys(
                    [
                      const _Key(content: "8"),
                      const _Key(content: "5"),
                      const _Key(content: "2"),
                    ],
                    keyWidth,
                    direction: Axis.vertical,
                  ),
                  SizedBox(width: _keySpacingHorizontal),
                  _buildKeys(
                    [
                      const _Key(content: "9"),
                      const _Key(content: "6"),
                      const _Key(content: "3"),
                    ],
                    keyWidth,
                    direction: Axis.vertical,
                  ),
                  SizedBox(width: _keySpacingHorizontal),
                  _buildKeys(
                    [const _Key(content: "0"), _Key.enter],
                    keyWidth,
                    direction: Axis.vertical,
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  ///构建一排按钮
  Widget _buildKeys(
    List<_Key> keys,
    double keyWidth, {
    Axis direction = Axis.horizontal,
  }) {
    List<Widget> children = [];
    for (int i = 0; i < keys.length; i++) {
      _Key key = keys[i];
      if (i != 0) {
        var spacing =
            direction == Axis.horizontal
                ? SizedBox(width: _keySpacingHorizontal)
                : SizedBox(height: _keySpacingVertical);
        children.add(spacing);
      }
      double height = _keyHeight;
      double width = keyWidth;
      Color color = Colors.white;
      TextStyle textStyle = TextStyle(
        fontSize: 26.sp,
        color: const Color(0xFF464646),
      );
      Widget? child;
      if (key.type == _Key.typeNormal) {
        child = Text(key.content, style: textStyle);
        if (key.content.isEmpty) {
          color = const Color(0xFFB8B9BF);
        }
      } else if (key.type == _Key.typeDelete) {
        color = const Color(0xFFB8B9BF);
        child = IconFont(IconNames.tuige, size: 30.w);
      } else if (key.type == _Key.typeEnter) {
        //这里enter键占用两个键位的高度
        height = 2 * _keyHeight + _keySpacingVertical;
        color = const Color(0xFF3B68DB);
        child = Text(
          key.content,
          style: textStyle.copyWith(color: Colors.white),
        );
      }
      child = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => _onKeyClick(key),
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4.w),
          ),
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          // child: FittedBox(child: child),
          child: child,
        ),
      );
      if (key.type == _Key.typeDelete) {
        child = Expanded(child: child);
      }
      children.add(child);
    }
    return Flex(
      direction: direction,
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }

  ///点击了键盘上的按钮
  void _onKeyClick(_Key key) {
    //搜索
    if (key.type == _Key.typeEnter) {
      _search();
    }
    //删除
    else if (key.type == _Key.typeDelete) {
      _delete();
    }
    //普通字符
    else if (key.type == _Key.typeNormal) {
      if (key.content.isNotEmpty != true) {
        return;
      }
      _insert(key.content);
    }
  }

  ///插入字符
  void _insert(String content) {
    insert(_textEditingController, content);
  }

  ///删除字符
  void _delete() {
    delete(_textEditingController);
  }

  ///搜索
  void _search() {
    filterValue = _textEditingController.text;
    setState(() {
      _selectFirstType(GoodsType.all);
      scrollController.jumpTo(0);
    });
    refresh();
  }

  @override
  Future<void> request() {
    final String searchValue = filterValue;
    //搜索之后清空输入框，方便连续扫码
    return super.request().then((value) async {
      _textEditingController.text = "";
      focusNode.requestFocus();
      if (searchByPtype) {
        for (var item in goodsAndComboList) {
          num price = item.currencyPrice ?? 0;
          int unitSkuListLength = item.unitSkuPtypeList?.length ?? 0;
          if (price <= 0 && unitSkuListLength > 1) {
            for (var e in item.unitSkuPtypeList!) {
              if ((e.currencyPrice ?? 0) > 0 &&
                  (price <= 0 || e.currencyPrice! < price)) {
                price = e.currencyPrice!;
              }
            }
          }
          item.currencyPrice = price;
        }
      }
      //如果搜索之后只有一条结果，则直接添加到单据中
      if (pageIndex <= 2 &&
          goodsAndComboList.length == 1 &&
          typeId == null &&
          StringUtil.isNotEmpty(searchValue) &&
          !searchByPtype) {
        await returnGoodsToBill(context, goodsAndComboList.first);
      }
    });
  }

  ///获取商品所有一级分类
  void getPtypeAllRootType() {
    PtypeModel.getPtypeAllRootType(context)
        .onError((error, stackTrace) => <GoodsType>[])
        .then((list) {
          if (mounted) {
            setState(() {
              typeList.clear();
              typeList.add(GoodsType.all);
              for (var type in list) {
                if (type.children.isNotEmpty) {
                  type.children.insert(0, GoodsType.all);
                }
              }
              typeList.addAll(list);
              _selectFirstType(GoodsType.all);
            });
          }
        })
        .then((value) => scrollController.jumpTo(0));
  }
}

///按键实体类
class _Key {
  ///普通按键
  static const typeNormal = 1;

  ///删除
  static const typeDelete = 2;

  ///确定
  static const typeEnter = 3;

  ///删除
  static const delete = _Key(type: typeDelete);

  ///确定
  static const enter = _Key(type: typeEnter, content: "Enter");

  ///按键类型
  final int type;

  ///按键内容
  final String content;

  const _Key({this.type = typeNormal, this.content = ""});
}

///规格选择弹窗
class _UnitSkuSelectDialog extends StatefulWidget {
  final double width;
  final double height;
  final PtypeListModel ptype;

  const _UnitSkuSelectDialog({
    Key? key,
    required this.ptype,
    required this.width,
    required this.height,
  }) : super(key: key);

  @override
  State<_UnitSkuSelectDialog> createState() => _UnitSkuSelectDialogState();
}

class _UnitSkuSelectDialogState extends State<_UnitSkuSelectDialog> {
  final _padding = EdgeInsets.symmetric(horizontal: 30.w);

  final _divider = const Divider(color: Color(0xFFE0E0E0));

  ///记录每个属性类型下面的所有属性值
  final Map<String, List<SelectWrapperBean<PtypePropDto>>> propertyValueMap =
      {};

  ///记录每个属性类型的名称
  final Map<String, String> propertyTypeMap = {};

  ///单位列表
  final List<UnitsBean> unitList = [];

  ///当前选中的unitSku商品
  PtypeListModel? currentUnitSkuPtype;

  ///当前选中的属性
  final Map<String, PtypePropDto> currentProperty = {};

  ///当前选中的单位
  UnitsBean? currentUnit;

  ///数量
  num count = 1;

  @override
  void initState() {
    super.initState();

    List<PtypeListModel> sortedUnitSkuList = List.from(
      widget.ptype.unitSkuPtypeList ?? [],
    );

    for (var unitSku in sortedUnitSkuList) {
      //属性
      _initProperty(unitSku);
      //单位
      _initUnit(unitSku);
    }

    // 对unitSkuPtypeList按照rowindex进行排序，确保属性值按PC端顺序展示
    sortedUnitSkuList.sort((a, b) {
      final aSku = a.sku;
      final bSku = b.sku;

      if (aSku == null && bSku == null) return 0;
      if (aSku == null) return 1;
      if (bSku == null) return -1;

      // 主要排序字段：rowindex1
      int aRowindex1 = aSku.rowindex1 ?? 0;
      int bRowindex1 = bSku.rowindex1 ?? 0;
      int result = aRowindex1.compareTo(bRowindex1);
      if (result != 0) return result;

      // 次要排序字段：rowindex2（仅在rowindex1相同时使用）
      int aRowindex2 = aSku.rowindex2 ?? 0;
      int bRowindex2 = bSku.rowindex2 ?? 0;
      return aRowindex2.compareTo(bRowindex2);
    });

    // 对每个属性的值列表按照对应的rowindex进行排序
    _sortPropertyValues(sortedUnitSkuList);

    //属性默认选中第一个
    if (unitList.isNotEmpty) {
      UnitsBean? baseUnit;
      for (var unit in unitList) {
        if (unit.unitCode == 1) {
          baseUnit = unit;
          break;
        }
      }
      currentUnit = baseUnit ?? unitList.first;
    }
    // if (propertyValueMap.isNotEmpty) {
    //   for (var entry in propertyValueMap.entries) {
    //     if (entry.value.isNotEmpty) {
    //       currentProperty[entry.key] = entry.value.first;
    //     }
    //   }
    // }
    _changeUnitSku();
  }

  ///初始化属性
  void _initProperty(PtypeListModel unitSku) {
    final sku = unitSku.sku;
    if (widget.ptype.propenabled == true && sku != null) {
      if (StringUtil.isNotZeroOrEmpty(sku.propId1)) {
        propertyTypeMap.putIfAbsent(sku.propId1!, () => sku.propName1 ?? "");
        List<SelectWrapperBean<PtypePropDto>> propsList = propertyValueMap
            .putIfAbsent(sku.propId1!, () => []);
        PtypePropDto propDto = PtypePropDto();
        propDto.propvalueId = sku.propvalueId1;
        propDto.propvalueName = sku.propvalueName1 ?? "";
        SelectWrapperBean<PtypePropDto> wrapperBean = SelectWrapperBean(
          data: propDto,
        );
        if (!propsList.contains(wrapperBean)) {
          propsList.add(wrapperBean);
        }
      }
      if (StringUtil.isNotZeroOrEmpty(sku.propId2)) {
        propertyTypeMap.putIfAbsent(sku.propId2!, () => sku.propName2 ?? "");
        List<SelectWrapperBean<PtypePropDto>> propsList = propertyValueMap
            .putIfAbsent(sku.propId2!, () => []);
        PtypePropDto propDto = PtypePropDto();
        propDto.propvalueId = sku.propvalueId2;
        propDto.propvalueName = sku.propvalueName2 ?? "";
        SelectWrapperBean<PtypePropDto> wrapperBean = SelectWrapperBean(
          data: propDto,
        );
        if (!propsList.contains(wrapperBean)) {
          propsList.add(wrapperBean);
        }
      }
      if (StringUtil.isNotZeroOrEmpty(sku.propId3)) {
        propertyTypeMap.putIfAbsent(sku.propId3!, () => sku.propName3 ?? "");
        List<SelectWrapperBean<PtypePropDto>> propsList = propertyValueMap
            .putIfAbsent(sku.propId3!, () => []);
        PtypePropDto propDto = PtypePropDto();
        propDto.propvalueId = sku.propvalueId3;
        propDto.propvalueName = sku.propvalueName3 ?? "";
        SelectWrapperBean<PtypePropDto> wrapperBean = SelectWrapperBean(
          data: propDto,
        );
        if (!propsList.contains(wrapperBean)) {
          propsList.add(wrapperBean);
        }
      }
      if (StringUtil.isNotZeroOrEmpty(sku.propId4)) {
        propertyTypeMap.putIfAbsent(sku.propId4!, () => sku.propName4 ?? "");
        List<SelectWrapperBean<PtypePropDto>> propsList = propertyValueMap
            .putIfAbsent(sku.propId4!, () => []);
        PtypePropDto propDto = PtypePropDto();
        propDto.propvalueId = sku.propvalueId4;
        propDto.propvalueName = sku.propvalueName4 ?? "";
        SelectWrapperBean<PtypePropDto> wrapperBean = SelectWrapperBean(
          data: propDto,
        );
        if (!propsList.contains(wrapperBean)) {
          propsList.add(wrapperBean);
        }
      }
      if (StringUtil.isNotZeroOrEmpty(sku.propId5)) {
        propertyTypeMap.putIfAbsent(sku.propId5!, () => sku.propName5 ?? "");
        List<SelectWrapperBean<PtypePropDto>> propsList = propertyValueMap
            .putIfAbsent(sku.propId5!, () => []);
        PtypePropDto propDto = PtypePropDto();
        propDto.propvalueId = sku.propvalueId5;
        propDto.propvalueName = sku.propvalueName5 ?? "";
        SelectWrapperBean<PtypePropDto> wrapperBean = SelectWrapperBean(
          data: propDto,
        );
        if (!propsList.contains(wrapperBean)) {
          propsList.add(wrapperBean);
        }
      }
      if (StringUtil.isNotZeroOrEmpty(sku.propId6)) {
        propertyTypeMap.putIfAbsent(sku.propId6!, () => sku.propName6 ?? "");
        List<SelectWrapperBean<PtypePropDto>> propsList = propertyValueMap
            .putIfAbsent(sku.propId6!, () => []);
        PtypePropDto propDto = PtypePropDto();
        propDto.propvalueId = sku.propvalueId6;
        propDto.propvalueName = sku.propvalueName6 ?? "";
        SelectWrapperBean<PtypePropDto> wrapperBean = SelectWrapperBean(
          data: propDto,
        );
        if (!propsList.contains(wrapperBean)) {
          propsList.add(wrapperBean);
        }
      }
    }
  }

  ///对属性值列表按照rowindex进行排序
  void _sortPropertyValues(List<PtypeListModel> sortedUnitSkuList) {
    for (var entry in propertyValueMap.entries) {
      String propId = entry.key;
      List<SelectWrapperBean<PtypePropDto>> propsList = entry.value;

      // 为每个属性值找到对应的rowindex进行排序
      propsList.sort((a, b) {
        // 找到包含这个属性值的sku，获取对应的rowindex
        int aRowindex = _getRowindexForPropValue(
          sortedUnitSkuList,
          propId,
          a.data.propvalueId,
        );
        int bRowindex = _getRowindexForPropValue(
          sortedUnitSkuList,
          propId,
          b.data.propvalueId,
        );
        return aRowindex.compareTo(bRowindex);
      });
    }
  }

  ///获取指定属性值对应的rowindex
  int _getRowindexForPropValue(
    List<PtypeListModel> unitSkuList,
    String propId,
    String? propvalueId,
  ) {
    for (var unitSku in unitSkuList) {
      final sku = unitSku.sku;
      if (sku == null) continue;

      // 检查每个属性位置，找到匹配的属性值
      if (propId == sku.propId1 && propvalueId == sku.propvalueId1) {
        return sku.rowindex1 ?? 0;
      }
      if (propId == sku.propId2 && propvalueId == sku.propvalueId2) {
        return sku.rowindex2 ?? 0;
      }
      if (propId == sku.propId3 && propvalueId == sku.propvalueId3) {
        return sku.rowindex3 ?? 0;
      }
      if (propId == sku.propId4 && propvalueId == sku.propvalueId4) {
        return sku.rowindex4 ?? 0;
      }
      if (propId == sku.propId5 && propvalueId == sku.propvalueId5) {
        return sku.rowindex5 ?? 0;
      }
      if (propId == sku.propId6 && propvalueId == sku.propvalueId6) {
        return sku.rowindex6 ?? 0;
      }
    }
    return 0; // 默认值
  }

  ///初始化单位
  void _initUnit(PtypeListModel unitSku) {
    final unit = unitSku.unit;
    if (unit == null) return;
    if (!unitList.contains(unit)) {
      unitList.add(unit);
    }
  }

  ///更改属性，筛选剩余的sku，将没有的属性值置灰不可选
  void _changeProp() {
    final fullFilterList = _filterByProp(
      widget.ptype.unitSkuPtypeList!,
      currentProperty.entries,
    );
    for (var entry in propertyValueMap.entries) {
      //用其他种类的属性来做筛选，判断当前的属性种类值是否可选
      Iterable<PtypeListModel> filterList =
          currentProperty.containsKey(entry.key)
              ? _filterByProp(
                widget.ptype.unitSkuPtypeList!,
                currentProperty.entries.where(
                  (element) => element.key != entry.key,
                ),
              )
              : fullFilterList;
      //拿到筛选后的sku中，当前属性有哪些id
      final enablePropValueIds =
          filterList.expand<String>((ptype) {
            final sku = ptype.sku;
            if (sku == null) {
              return [];
            }
            return [
              if (entry.key == sku.propId1 &&
                  sku.propvalueId1?.isNotEmpty == true)
                sku.propvalueId1!,
              if (entry.key == sku.propId2 &&
                  sku.propvalueId2?.isNotEmpty == true)
                sku.propvalueId2!,
              if (entry.key == sku.propId3 &&
                  sku.propvalueId3?.isNotEmpty == true)
                sku.propvalueId3!,
              if (entry.key == sku.propId4 &&
                  sku.propvalueId4?.isNotEmpty == true)
                sku.propvalueId4!,
              if (entry.key == sku.propId5 &&
                  sku.propvalueId5?.isNotEmpty == true)
                sku.propvalueId5!,
              if (entry.key == sku.propId6 &&
                  sku.propvalueId6?.isNotEmpty == true)
                sku.propvalueId6!,
            ];
          }).toSet();
      //如果id有sku，则可用，否则置灰
      propertyValueMap[entry.key] =
          entry.value
              .map(
                (e) => SelectWrapperBean(
                  data: e.data,
                  selected: e.selected,
                  enable: enablePropValueIds.contains(e.data.propvalueId),
                ),
              )
              .toList();
    }
    _changeUnitSku(filterUnitSku: fullFilterList);
  }

  ///通过属性筛选unitSku
  Iterable<PtypeListModel> _filterByProp(
    List<PtypeListModel> ptypeList,
    Iterable<MapEntry<String, PtypePropDto>> entries,
  ) {
    return ptypeList.where((element) {
      if (element.unit != currentUnit) return false;
      for (var entry in entries) {
        final sku = element.sku;
        if (sku == null) return false;
        if (entry.key == sku.propId1 &&
            entry.value.propvalueId == sku.propvalueId1) {
          continue;
        }
        if (entry.key == sku.propId2 &&
            entry.value.propvalueId == sku.propvalueId2) {
          continue;
        }
        if (entry.key == sku.propId3 &&
            entry.value.propvalueId == sku.propvalueId3) {
          continue;
        }
        if (entry.key == sku.propId4 &&
            entry.value.propvalueId == sku.propvalueId4) {
          continue;
        }
        if (entry.key == sku.propId5 &&
            entry.value.propvalueId == sku.propvalueId5) {
          continue;
        }
        if (entry.key == sku.propId6 &&
            entry.value.propvalueId == sku.propvalueId6) {
          continue;
        }
        return false;
      }
      return true;
    });
  }

  ///更改商品
  void _changeUnitSku({Iterable<PtypeListModel>? filterUnitSku}) {
    //属性没选完
    if (currentProperty.length < propertyTypeMap.length) {
      currentUnitSkuPtype = null;
      return;
    }
    filterUnitSku ??= _filterByProp(
      widget.ptype.unitSkuPtypeList!,
      currentProperty.entries,
    );
    currentUnitSkuPtype = filterUnitSku.let((list) {
      if (list.isNotEmpty) {
        return list.first;
      }
      return null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          right: 0,
          bottom: 0,
          height: widget.height,
          width: widget.width,
          child: _build(context),
        ),
      ],
    );
  }

  Widget _build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(context),
          _divider,
          _buildPtypeInfo(context),
          _divider,
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: _padding.copyWith(bottom: 50.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ..._buildPropertySelector(context),
                    ..._buildUnitSelector(context),
                    ..._buildQtySelector(context),
                  ],
                ),
              ),
            ),
          ),
          _divider,
          _buildBottom(context),
        ],
      ),
    );
  }

  ///标题
  Widget _buildTitle(BuildContext context) {
    return Container(
      height: 60.h,
      padding: _padding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "规格选择",
            style: TextStyle(color: const Color(0xFF333333), fontSize: 26.sp),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => Navigator.pop(context),
            child: IconFont(IconNames.close, size: 20.w),
          ),
        ],
      ),
    );
  }

  ///商品信息
  Widget _buildPtypeInfo(BuildContext context) {
    PtypeListModel item = currentUnitSkuPtype ?? widget.ptype;
    String picUrl;
    if (!TextUtil.isEmpty(item.sku?.picUrl)) {
      picUrl = item.sku?.picUrl ?? "";
    } else {
      picUrl = item.picUrl ?? "";
    }
    TextStyle textStyle = const TextStyle(overflow: TextOverflow.ellipsis);
    return Padding(
      padding: _padding,
      child: Row(
        children: [
          Container(
            width: 130.h,
            height: 130.h,
            margin: EdgeInsets.only(right: 20.w),
            child: Image.network(
              picUrl,
              errorBuilder:
                  (
                    BuildContext context,
                    Object error,
                    StackTrace? stackTrace,
                  ) => Container(color: Colors.grey),
              width: 130.h,
              height: 130.h,
            ),
          ),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //商品/套餐名称
                Text(
                  item.fullname,
                  style: textStyle.copyWith(
                    fontSize: 26.sp,
                    color: const Color(0xFF333333),
                  ),
                  maxLines: 1,
                ),
                //价格
                Text(
                  "￥${item.currencyPrice ?? 0}",
                  style: textStyle.copyWith(
                    color: const Color(0xFFFF0000),
                    fontSize: 34.sp,
                  ),
                  maxLines: 1,
                ),
                //条码
                Text(
                  "条码：${item.fullbarcode}",
                  style: textStyle.copyWith(
                    fontSize: 20.sp,
                    color: const Color(0xFF999999),
                  ),
                  maxLines: 1,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///属性选择标题
  Widget _buildSelectorTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(top: 30.h, bottom: 20.h),
      child: Text(
        title,
        maxLines: 1,
        style: TextStyle(
          fontSize: 24.sp,
          color: const Color(0xFF333333),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  ///构建选择器
  List<Widget> _buildSelector<T>({
    required List<T> data,
    required String title,
    required ResultFunction<T, bool> selected,
    required ResultFunction<T, String> valueGetter,
    required ValueChanged<T> onTap,
    ResultFunction<T, bool>? enabled,
  }) {
    final children =
        data.map((e) {
          bool select = selected(e);
          bool enable = enabled?.call(e) ?? true;
          final background = enable ? Colors.white : const Color(0xFFC9C9C9);
          final color =
              select ? const Color(0xFF4679FC) : const Color(0xFF333333);
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              if (enable) {
                onTap(e);
              }
            },
            child: Container(
              constraints: BoxConstraints(
                maxHeight: 50.h,
                minHeight: 50.h,
                maxWidth: 200.w,
              ),
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: background,
                borderRadius: BorderRadius.circular(4.w),
                border: Border.all(color: color),
              ),
              child: Text(
                valueGetter(e),
                maxLines: 1,
                style: TextStyle(
                  fontSize: 24.sp,
                  color: color,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          );
        }).toList();
    return [
      _buildSelectorTitle(title),
      Wrap(spacing: 24.w, runSpacing: 20.h, children: children),
    ];
  }

  ///属性选择
  List<Widget> _buildPropertySelector(BuildContext context) {
    if (propertyValueMap.isEmpty) return [];
    final children =
        propertyValueMap.entries.expand((entry) {
          return _buildSelector<SelectWrapperBean<PtypePropDto>>(
            data: entry.value,
            title: propertyTypeMap[entry.key] ?? "",
            selected: (item) => currentProperty[entry.key] == item.data,
            valueGetter: (item) => item.data.propvalueName ?? "",
            enabled: (item) => item.enable,
            onTap: (item) {
              setState(() {
                if (currentProperty[entry.key] == item.data) {
                  currentProperty.remove(entry.key);
                } else {
                  currentProperty[entry.key] = item.data;
                }
                _changeProp();
              });
            },
          );
        }).toList();
    return children;
  }

  ///单位选择
  List<Widget> _buildUnitSelector(BuildContext context) {
    if (unitList.isEmpty) return [];
    return _buildSelector<UnitsBean>(
      data: unitList,
      title: "单位",
      selected: (item) => currentUnit == item,
      valueGetter: (item) => item.unitName ?? "",
      onTap: (item) {
        if (currentUnit == item) return;
        setState(() {
          currentUnit = item;
          _changeUnitSku();
        });
      },
    );
  }

  ///数量编辑按钮
  Widget _buildNumButton(String content, VoidCallback onTap) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
        width: 50.h,
        height: 50.h,
        alignment: Alignment.center,
        child: Text(content),
      ),
    );
  }

  ///购买数量
  List<Widget> _buildQtySelector(BuildContext context) {
    const color = Color(0xFFC9C9C9);
    return [
      _buildSelectorTitle("购买数量"),
      Container(
        width: 220.w,
        height: 50.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.w),
          border: Border.all(color: color),
        ),
        child: Row(
          children: [
            _buildNumButton("-", () {
              num newCount = count - 1;
              if (newCount <= 0) {
                HaloToast.show(context, msg: "购买数量必须大于0");
                return;
              }
              setState(() => count = newCount);
            }),
            const VerticalDivider(color: color),
            Expanded(
              child: Container(
                alignment: Alignment.center,
                child: Text(
                  count.toString(),
                  maxLines: 1,
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: const Color(0xFF333333),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
            const VerticalDivider(color: color),
            _buildNumButton("+", () {
              num newCount = count + 1;
              setState(() => count = newCount);
            }),
          ],
        ),
      ),
    ];
  }

  ///底部
  Widget _buildBottom(BuildContext context) {
    String unit = currentUnit?.unitName ?? "";
    String property = _buildPropertyString();
    if (property.isNotEmpty) {
      property = "，$property";
    }
    return Container(
      padding: _padding,
      height: 80.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                Flexible(child: Text("已选：$count$unit$property")),
                if (currentUnitSkuPtype?.stockQty != null)
                  Text(
                    "(库存${BillTool.getStockQty(currentUnitSkuPtype!.stockQty, currentUnitSkuPtype!.unit?.unitRate, currentUnitSkuPtype!.pcategory)})",
                    maxLines: 1,
                    style: TextStyle(
                      fontSize: 22.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
              ],
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              if (currentUnitSkuPtype == null) {
                HaloToast.show(context, msg: "还未选择一个商品");
                return;
              }
              final copy = PtypeListModel.fromMap(currentUnitSkuPtype!.toJson())
                ..count = count;
              Navigator.pop(context, copy);
            },
            child: Container(
              width: 146.w,
              height: 68.h,
              decoration: BoxDecoration(
                color: const Color(0xFF4679FC),
                borderRadius: BorderRadius.circular(6.w),
              ),
              alignment: Alignment.center,
              child: Text(
                "确定",
                style: TextStyle(color: Colors.white, fontSize: 32.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///构建当前的属性字符串
  String _buildPropertyString() {
    return currentProperty.values.map((e) => e.propvalueName ?? "").join("，");
  }
}
