import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/widget/ptype/goods_bind_sn_page.dart';
import '../../../common/tool/dialog_util.dart';
import '../../tool/goods_tool.dart';
import 'base_goods_dialog.dart';
import 'goods_bind_batch_page.dart';
import 'multi_goods_bind_batch.dart';

///多个商品选择序列号
class MultiGoodsBindSnPage extends StatefulWidget {
  ///单据中的已有的序列号
  final List<String> existSN;

  ///要绑定序列号的商品列表
  final List<GoodsDetailDto> goodsList;

  const MultiGoodsBindSnPage(this.goodsList, this.existSN, {Key? key})
      : super(key: key);

  @override
  State<MultiGoodsBindSnPage> createState() => _MultiGoodsBindSnPageState();
}

class _MultiGoodsBindSnPageState<T extends MultiGoodsBindSnPage>
    extends BaseGoodsDialogState<T> with ListAndListTitleMixin {
  final List<GoodsDetailDto> _copyList = [];
  final List<GoodsDetailDto> _goodsList = [];

  @override
  void initState() {
    super.initState();
    widget.goodsList
        .where((element) => element.snenabled != 0 && !element.comboRow)
        .forEach((element) {
      _goodsList.add(element);
      _copyList.add(GoodsDetailDto.fromMap(element.toJson()));
    });
  }

  @override
  double get height => 838.h;

  @override
  double get width => 824.w;

  @override
  int get itemCount => _copyList.length;

  @override
  String get title => "商品序列号";

  ///表头各列标题
  @override
  List<String> get listTitles => ["商品名称", "序列号"];

  ///列表各列比例
  @override
  List<int> get listColumnFlex => [1, 3];

  @override
  Widget buildContent(BuildContext context) {
    return Column(children: [
      Expanded(child: super.buildContent(context)),
      //底部分割线及确定按钮
      _buildBottom(context)
    ]);
  }

  @override
  Widget buildListItem(BuildContext context, int index) {
    GoodsDetailDto goods = _copyList[index];
    return Column(
      children: [
        HaloContainer(height: 70.h, padding: listPadding, children: [
          Expanded(
              flex: listColumnFlex[0], child: buildText(goods.pFullName ?? "")),
          Expanded(flex: listColumnFlex[1], child: _buildSnText(goods)),
        ]),
        //分割线
        divider
      ],
    );
  }

  ///序列号选择
  _buildSnText(GoodsDetailDto goods) {
    Widget snText;
    if (goods.serialNoList.isEmpty) {
      snText = Text("请选择序列号",
          style: textStyle.copyWith(color: const Color(0xFFD0D0D0)));
    } else {
      snText = buildText(goods.serialNoList.map((e) => e.snno ?? "").join(","));
    }
    return buildSelectionContainer(snText, () {
      //如果是批次号商品，且没有选择批次号，那么需要先选择批次号
      if (goods.batchenabled && !GoodsTool.isGoodsBindWithBatch(goods)) {
        showDialog(
            context: context,
            builder: (context) => GoodsBindBatchPage(goods: goods));
        return;
      }
      //点击跳转到选择商品序列号弹窗
      showDialog(
          context: context,
          builder: (context) => GoodsBindSnPage(
                goods: goods,
                existSN: [
                  ...widget.existSN,
                  ..._copyList
                      .expand((element) => element.serialNoList)
                      .map((e) => e.snno ?? "")
                ],
                limitMaxCount: true,
              )).then((value) {
        if (value != null) {
          setState(() {});
        }
      });
    });
  }

  @override
  void onCloseButtonClick(BuildContext context) {
    DialogUtil.showConfirmDialog(
      context,
      title: "提示",
      content: "还未设置任何序列号，是否退出?",
      actionLabels: ["取消", "确定"],
      confirmCallback: () => Navigator.pop(context),
    );
  }

  ///构建底部分割线和确定按钮
  _buildBottom(BuildContext context) {
    return Column(
      children: [
        divider,
        HaloContainer(
          height: 100.h,
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          padding: dialogPadding,
          children: [
            buildButton(
              context,
              "确定",
              textColor: Colors.white,
              background: const Color(0xFF4679FC),
              borderColor: null,
              onTap: () {
                if (_copyList.any(
                    (goods) => goods.serialNoList.length != goods.unitQty)) {
                  DialogUtil.showConfirmDialog(
                    context,
                    title: "提示",
                    content: "还未设置完序列号，是否保存?",
                    actionLabels: ["取消", "确定"],
                    confirmCallback: onConfirm,
                  );
                } else {
                  onConfirm();
                }
              },
            ),
          ],
        )
      ],
    );
  }

  ///确认
  void onConfirm() {
    for (int i = 0; i < _goodsList.length; i++) {
      final goods = _goodsList[i];
      final copy = _copyList[i];
      //将该商品关联上批次号
      goods.batchId = copy.batchId;
      //批次成本
      goods.batchPrice = copy.batchPrice;
      //解决个别计价商品，选择批次后核算不通过的问题
      goods.costId = copy.costId;
      goods.batchNo = copy.batchNo;
      goods.produceDate = copy.produceDate ?? "";
      goods.expireDate = copy.expireDate;
      goods.protectDays = copy.protectDays;
      goods.batchInventoryQty = copy.batchInventoryQty;
      goods.unitQty = copy.unitQty;
      goods.serialNoList = copy.serialNoList;
      goods.unitQty = copy.unitQty;
    }
    Navigator.pop(context, widget.goodsList);
  }
}

///套餐选择序列号
class ComboBindSnPage extends MultiGoodsBindSnPage {
  final GoodsDetailDto combo;

  const ComboBindSnPage(
      List<GoodsDetailDto> goodsList, List<String> existSN, this.combo,
      {Key? key})
      : super(goodsList, existSN, key: key);

  @override
  State<ComboBindSnPage> createState() => _ComboBindSnPageState();
}

class _ComboBindSnPageState extends _MultiGoodsBindSnPageState<ComboBindSnPage>
    with GoodsInfoMixin {


  @override
  String get goodsBarcode => "";

  @override
  String get goodsName => widget.combo.pFullName ?? "";

  @override
  String get goodsPicUrl => widget.combo.picUrl ?? "";

  @override
  // TODO: implement goodsQty
  String get goodsQty => widget.combo.unitQty.toString();
}
