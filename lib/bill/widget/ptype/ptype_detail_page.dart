import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../common/style/app_color_helper.dart';
import '../../../common/tool/math.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_ptype_pop_value_change_type.dart';
import '../../../enum/bill_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../login/entity/store/store_info.dart';
import '../../../settting/widget/switch.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/ptype/ptype_serial_no_dto.dart';
import '../../entity/ptype/ptype_unit_dto.dart';
import '../../model/ptype_model.dart';
import '../../settlement/widget/input_board_page.dart';
import '../../tool/bill_price_compute_helper.dart';
import '../../tool/bill_tool.dart';
import '../../tool/decimal_display_helper.dart';
import '../../tool/goods_tool.dart';
import '../../tool/promotion/manual_price.dart';
import '../../tool/promotion/price.dart';
import '../../widget/count_stepper.dart';
import '../../widget/ptype/back_goods_bind_batch_page.dart';
import '../../widget/ptype/goods_bind_batch_page.dart';
import '../../widget/ptype/goods_bind_sn_page.dart';
import '../../widget/ptype/goods_remark_dialog.dart';
import '../../widget/ptype/ptype_textfield.dart';
import 'ptype_detail_keyboard.dart';

const double bigPaddingWidth = 15;
const double smallPaddingWidth = 10;

///返回值为bool的回调
typedef BoolCallback = bool Function();

///商品详情界面
class PtypeDetailPage extends StatefulWidget {
  final GoodsDetailDto goods;
  final GoodsBillDto goodsBillDto;
  final void Function(GoodsDetailDto goodsDetailDto, bool editRetailPrice)
  valueChanged;
  final double width;
  final double height;
  final BillType billType;
  final bool enable;

  ///商品最大数量
  final num maxCount;

  ///是否允许标记赠品
  final bool markGift;

  ///是否是按商品退货
  final bool backByGoods;

  const PtypeDetailPage(
    this.goods,
    this.goodsBillDto, {
    Key? key,
    this.enable = true,
    this.billType = BillType.SaleBill,
    this.height = 830,
    this.width = 604,
    this.markGift = true,
    this.backByGoods = false,
    required this.maxCount,
    required this.valueChanged,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _PtypeDetailPageState();
  }
}

class _PtypeDetailPageState extends State<PtypeDetailPage> {
  //region 变量
  late GoodsDetailDto goodsDetailDto;

  ///折扣
  num discount = 0;

  ///数量
  num count = 0;

  ///折后单价
  num discountPrice = 0;

  ///折后金额
  num posDisedTaxedTotal = 0;

  ///税率
  num taxRate = 0;

  ///商品备注
  String goodsRemark = "";

  ///从接口获取到的该商品的单位列表
  List<PtypeUnitDto> _unitList = [];

  ///单位PopupMenu的key，用来控制弹出
  final GlobalKey _unitPopupMenuKey = GlobalKey();

  ///权限
  bool modifyPrice = true;

  ///职员权限
  bool get discountViewControl =>
      SpTool.getPermission().shopsalesalesettingeditDetailDiscount ?? false;

  ///是否允许设置赠品
  bool get giftViewControl =>
      SpTool.getPermission().shopsalesalesettingeditGift ?? false;

  ///是否允许修改零售价
  bool get allowEditRetailPrice =>
      SpTool.getPermission().shopsalesalesettingeditRetailPrice ?? false;

  ///是否允许修改折后价格
  bool get priceViewControl =>
      SpTool.getPermission().shopsalesalesettingeditDetailPrice ?? false;

  ///end职员权限
  //endregion

  ///是否允许价格改大 todo 暂时不允许改大，等后续修改零售价需求一块发
  // bool get allowPriceOverflow => widget.billType == BillType.SaleBill;
  bool get allowPriceOverflow => false;

  ///最大价格（10倍单价）
  num get maxPrice {
    num max =
        allowPriceOverflow
            ? MathUtil.multiplyDec(goodsDetailDto.currencyPrice, 10).toDouble()
            : goodsDetailDto.currencyPrice;
    return min(max, SpTool.getSystemConfig().sysGlobalDecimalMax);
  }

  ///是否修改零售价
  bool editRetailPrice = false;

  @override
  void initState() {
    super.initState();
    //销售单
    if (widget.billType == BillType.SaleBill ||
        widget.billType == BillType.SaleChangeBill) {
      modifyPrice = true;
    }
    //退货单
    else {
      //按商品退货时允许修改价格
      modifyPrice = widget.backByGoods;
    }
    goodsDetailDto = GoodsDetailDto.fromMap(widget.goods.toJson());
    _updateValue();
  }

  _updateValue() {
    discount = goodsDetailDto.discount;
    count = goodsDetailDto.unitQty;
    discountPrice = goodsDetailDto.discountPrice;
    posDisedTaxedTotal = goodsDetailDto.discountTotal.toDouble();
    taxRate = goodsDetailDto.taxRate;
    goodsRemark = goodsDetailDto.memo;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        alignment: Alignment.center,
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                offset: Offset(0, 0), //阴影xy轴偏移量
                blurRadius: 20.0, //阴影模糊程度
                spreadRadius: 10.0, //阴影扩散程度
              ),
            ],
          ),
          width: widget.width.w,
          height: (!widget.enable ? 520 : widget.height).h,
          // color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildImageHeader(),
              _buildFirstRow(),
              _buildSecondRow(),
              _buildThirdRow(),
              if (widget.enable) _buildKeyBoard(),
              Container(
                padding: EdgeInsets.only(left: 18.w, top: 5.w),
                alignment: Alignment.centerLeft,
                child: HaloPosLabel(
                  "手工改价后不执行其他折扣优惠和促销活动",
                  textStyle: TextStyle(
                    color: Colors.red,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(child: _buildBottom()),
            ],
          ),
        ),
      ),
    );
  }

  _buildImageHeader() {
    String pFullName = goodsDetailDto.pFullName ?? "";
    String propName = BillTool.getPropName(goodsDetailDto);
    String url = goodsDetailDto.picUrl ?? "";

    Widget nameWidget;
    if (goodsDetailDto.comboRow) {
      nameWidget = Row(
        children: [
          Expanded(
            child: HaloPosLabel(
              pFullName,
              textStyle: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 18.w,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(left: 10.w),
            padding: const EdgeInsets.all(10),
            color: Colors.purpleAccent,
            child: HaloPosLabel(
              "套",
              textStyle: TextStyle(
                color: Colors.white,
                fontSize: 18.w,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      );
    } else {
      nameWidget = HaloPosLabel(
        pFullName + propName,
        textStyle: TextStyle(fontSize: 24.sp),
      );
    }
    return Container(
      height: 95.h,
      margin: EdgeInsets.symmetric(
        horizontal: bigPaddingWidth.w,
        vertical: 31.w,
      ),
      child: Row(
        children: [
          HaloImage(url),
          // Expanded(child:
          Expanded(
            child: Container(
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(color: Colors.white),
              alignment: Alignment.centerLeft,
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(alignment: Alignment.centerLeft, child: nameWidget),
                  _buildCurrencyPrice(),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 95.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: GestureDetector(
                    child: Container(
                      width: 80.w,
                      alignment: Alignment.topRight,
                      child: IconFont(
                        IconNames.close,
                        size: 24.w,
                        color: ColorUtil.color2String(Colors.black),
                      ),
                    ),
                    onTap: () => NavigateUtil.pop(context),
                  ),
                ),
                Visibility(
                  visible: !goodsDetailDto.comboRow,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () async {
                      if (widget.enable && modifyPrice) {
                        //检查是否获取到了单位列表，如果没有，发起请求获取单位。如果有单位了，则弹出PopupMenu
                        if (_unitList.isNotEmpty != true) {
                          await _getUnitList(context);
                        }
                        HaloPopWindow().show(
                          _unitPopupMenuKey,
                          backgroundColor: Colors.transparent,
                          intervalLeft: -58.w,
                          intervalTop: 2.h,
                          gravity: PopWindowGravity.bottom,
                          child: Container(
                            width: 200.w,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(6.w),
                              boxShadow: const [
                                BoxShadow(
                                  color: Colors.black12,
                                  offset: Offset(0, 0), //阴影xy轴偏移量
                                  blurRadius: 20.0, //阴影模糊程度
                                  spreadRadius: 10.0, //阴影扩散程度
                                ),
                              ],
                            ),
                            child: ListView.separated(
                              shrinkWrap: true,
                              itemBuilder:
                                  (context, index) => GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () {
                                      HaloPopWindow().disMiss();
                                      onUnitChanged(_unitList[index]);
                                    },
                                    child: Container(
                                      height: 60.h,
                                      width: 200.w,
                                      alignment: Alignment.center,
                                      child: Text(
                                        _unitList[index].unitName,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: const Color(0xFF333333),
                                          fontSize: 26.sp,
                                        ),
                                      ),
                                    ),
                                  ),
                              separatorBuilder:
                                  (context, index) => Divider(
                                    height: 2.h,
                                    color: const Color(0xFFCFCFCF),
                                  ),
                              itemCount: _unitList.length,
                            ),
                          ),
                        );
                      }
                    },
                    child: Container(
                      key: _unitPopupMenuKey,
                      width: 142.w,
                      height: 52.h,
                      padding: EdgeInsets.symmetric(
                        vertical: 8.h,
                        horizontal: 16.w,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.w),
                        color: const Color(0xFFF5F5F5),
                      ),
                      child: Text(
                        "单位：${goodsDetailDto.unitName}",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          color: const Color(0xFF333333),
                          fontSize: 26.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///零售价
  Widget _buildCurrencyPrice() {
    bool editCurrencyPrice =
        allowEditRetailPrice &&
        modifyPrice &&
        !widget.backByGoods &&
        !goodsDetailDto.gift &&
        !goodsDetailDto.promotionGift &&
        !GoodsTool.isComboDetail(goodsDetailDto);

    if (editCurrencyPrice) {
      return _buildNormalRow(
        title: "￥",
        unitString: "元",
        height: 40,
        titleTextSize: 20,
        autofocus: false,
        textString: DecimalDisplayHelper.getPriceFixed(
          goodsDetailDto.currencyPrice.toString(),
        ),
        maxValue: 1000000,
        afterPoint: SpTool.getSystemConfig().sysDigitalPrice,
        onChange: _onCurrencyPriceChange,
      );
    }
    return HaloPosLabel(
      "¥${goodsDetailDto.currencyPrice}",
      textStyle: TextStyle(color: Colors.red, fontSize: 28.sp),
    );
    // return Row(children: [
    //   Flexible(
    //     child: HaloPosLabel(
    //       "¥${goodsDetailDto.currencyPrice}",
    //       textStyle: TextStyle(color: Colors.red, fontSize: 28.sp),
    //     ),
    //   ),
    //   GestureDetector(
    //     onTap: showEditRetailPriceDialog,
    //     behavior: HitTestBehavior.opaque,
    //     child: Container(
    //       padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
    //       child: Icon(
    //         Icons.edit,
    //         size: 16.sp,
    //         color: Colors.black,
    //       ),
    //     ),
    //   ),
    // ]);
  }

  Widget _buildFirstRow() {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: 5.h,
        horizontal: bigPaddingWidth.w,
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildNormalRow(
              // right: bigPaddingWidth.w,
              // left: smallPaddingWidth.w,
              title: "折扣",
              unitString: "",
              enable: modifyPrice && discountViewControl && widget.enable,
              textString: DecimalDisplayHelper.getDiscountFixed(
                discount.toString(),
              ),
              maxValue: allowPriceOverflow ? 10 : 1,
              afterPoint: SpTool.getSystemConfig().sysDigitalDiscount,
              //pos是百分比
              onChange: _onDiscountChange,
            ),
          ),
          SizedBox(width: bigPaddingWidth.w),
          _buildCount(),
        ],
      ),
    );
  }

  _buildSecondRow() {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: 5.h,
        horizontal: bigPaddingWidth.w,
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildNormalRow(
              title: "售价",
              enable: modifyPrice && priceViewControl && widget.enable,
              unitString: "元",
              tips: "售价不能大于${allowPriceOverflow ? "10倍" : ""}单价！",
              textString: DecimalDisplayHelper.getPriceFixed(
                discountPrice.toString(),
              ),
              maxValue: maxPrice,
              afterPoint: SpTool.getSystemConfig().sysDigitalPrice,
              onChange: _onDiscountPriceChange,
            ),
          ),
          SizedBox(width: bigPaddingWidth.w),
          _buildTotal(),
        ],
      ),
    );
  }

  _buildThirdRow() {
    return Container(
      margin: EdgeInsets.symmetric(
        vertical: 5.h,
        horizontal: bigPaddingWidth.w,
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildNormalRow(
              title: "赠品",
              child: Container(
                alignment: Alignment.centerRight,
                child: HaloPosSwitch(
                  value: goodsDetailDto.gift,
                  width: 100.w,
                  height: 40.h,
                  onChanged: (value) {
                    if (modifyPrice &&
                        widget.enable &&
                        giftViewControl &&
                        widget.markGift) {
                      FocusScope.of(context).requestFocus(FocusNode());
                      goodsDetailDto.gift = !goodsDetailDto.gift;
                      _changeGift(goodsDetailDto.gift);
                    }
                  },
                ),
              ),
            ),
          ),
          SizedBox(width: bigPaddingWidth.w),
          Expanded(
            child: Visibility(
              visible:
                  (!goodsDetailDto.comboRow) &&
                  StringUtil.isZeroOrEmpty(goodsDetailDto.comboRowParId) &&
                  SpTool.getSystemConfig().sysGlobalEnabledTax &&
                  SpTool.getSystemConfig().sysGlobalEnabledSaleTax,
              child: _buildNormalRow(
                title: "税率",
                unitString: "%",
                textString: taxRate.toString(),
                afterPoint: 2,
                onChange: (String text) {
                  taxRate = num.parse(text);
                  goodsDetailDto.taxRate = num.parse(text);
                  TaxUtil.calculateDisedPriceAndTotal(goodsDetailDto);
                  setState(() {
                    _updateValue();
                  });
                },
                maxValue: 100,
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildCount() {
    bool countEnable = widget.enable;
    return Expanded(
      child: _buildNormalRow(
        title: "数量",
        child: Container(
          width: 100,
          height: 40,
          alignment: Alignment.centerRight,
          child: CountStepper(
            enable: countEnable,
            addIconSize: 22,
            subIconSize: 4,
            defaultText: count.toString(),
            readOnly: true,
            textColor: Colors.red,
            textColorDiy: true,
            maxValue: widget.maxCount,
            valueChanged: _onCountChange,
          ),
        ),
      ),
    );
  }

  _buildTotal() {
    return Expanded(
      child: _buildNormalRow(
        title: "金额",
        unitString: "元",
        enable: false,
        textString: DecimalDisplayHelper.getTotalFixed(
          posDisedTaxedTotal.toString(),
        ),
        afterPoint: SpTool.getSystemConfig().sysDigitalTotal,
        // maxValue: maxTotalPrice //这里无需编辑，所以不需要设置最大值判断
      ),
    );
  }

  Widget _buildKeyBoard() {
    return PtypeDetailKeyboard(
      enable: !goodsDetailDto.gift,
      submitCallback: _submit,
    );
  }

  Widget _buildNormalRow({
    String title = "",
    String unitString = "",
    String textString = "",
    Function(String text)? onChange,
    int afterPoint = 0,
    num? maxValue,
    Widget? child,
    String? tips,
    bool enable = true,
    bool autofocus = true,
    double height = 65,
    double titleTextSize = 24,
  }) {
    Widget? unit;
    if (child == null) {
      child = PtypeTextField(
        tips: tips,
        enable: !goodsDetailDto.gift && enable,
        autoFocus: autofocus,
        defaultText: textString,
        maxValue: maxValue,
        afterPoint: afterPoint,
        onChange: onChange,
      );
      unit = HaloPosLabel(
        unitString,
        textStyle: TextStyle(fontSize: titleTextSize.sp),
      );
    }
    return Container(
      height: height.h,
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        border: Border.all(width: 1, color: ColorUtil.stringColor("#CDCDCD")),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(right: 8.w),
                child: HaloPosLabel(
                  title,
                  textStyle: TextStyle(fontSize: titleTextSize.sp),
                ),
              ),
              Expanded(child: child),
              if (unit != null) unit,
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottom() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(width: 1, color: ColorUtil.stringColor("#CDCDCD")),
        ),
      ),
      margin: EdgeInsets.only(top: bigPaddingWidth.h),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 22.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                Visibility(
                  visible: goodsDetailDto.batchenabled,
                  child: Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: _buildBottomButton(
                      context,
                      "批次号",
                      Icons.edit,
                      widget.billType == BillType.SaleBill ||
                              widget.billType == BillType.SaleChangeBill
                          ? GoodsBindBatchPage(goods: goodsDetailDto)
                          : BackGoodsBindBatchPage(goods: goodsDetailDto),
                    ),
                  ),
                ),
                Visibility(
                  visible: goodsDetailDto.snenabled != 0,
                  child: Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: _buildBottomButton(
                      context,
                      "序列号",
                      Icons.edit,
                      GoodsBindSnPage(
                        goods: GoodsDetailDto.fromMap(goodsDetailDto.toJson()),
                        limitMaxCount: !widget.enable,
                        existSN: getExistSN(),
                        billType: widget.billType,
                      ),
                      preCheck: () {
                        if (goodsDetailDto.batchenabled) {
                          if (!GoodsTool.isGoodsBindWithBatch(goodsDetailDto)) {
                            HaloToast.show(context, msg: "请先选择批次号");
                            return false;
                          }
                        }
                        return true;
                      },
                      onResult: (goods) {
                        goodsDetailDto.serialNoList = goods.serialNoList;
                        //当从序列号弹窗返回时，可能会改变商品数量
                        if (goods.unitQty != count) {
                          _onCountChange(goods.unitQty.toString());
                        }
                      },
                    ),
                  ),
                ),
            Visibility(
              visible: goodsDetailDto.comboRow ||
                  StringUtil.isZeroOrEmpty(goodsDetailDto.comboRowParId),
              child: _buildBottomButton(
                  context,
                  "商品备注",
                  Icons.note_add,
                  Container(),
                  onTap: () => _showRemarkDialog(),
                )),
              ],
            ),
          ),
          HaloButton(
            width: 180.w,
            height: 70.w,
            text: "确定",
            fontSize: 24.sp,
            borderRadius: 6.w,
            disabledBackgroundColor: const Color(0xFF4679FC),
            backgroundColor: ColorUtil.stringColor("#4679FC"),
            fontWeight: FontWeight.normal,
            buttonType: HaloButtonType.elevatedButton,
            onPressed: () => _submit(),
          ),
        ],
      ),
    );
  }

  ///底部按钮样式
  Widget _buildBottomButton(
    BuildContext context,
    String content,
    IconData iconData,
    Widget dialog, {
    BoolCallback? preCheck,
    ValueSetter<GoodsDetailDto>? onResult,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap:
          onTap ??
          () => _onButtonClick(
            context,
            content,
            dialog,
            preCheck: preCheck,
            onResult: onResult,
          ),
      child: Container(
        height: 70.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.w),
          border: Border.all(color: const Color(0xFFB7B7B7), width: 1.w),
        ),
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon(iconData, size: 18.w),
            Padding(
              padding: EdgeInsets.only(left: 5.w),
              child: Text(
                content,
                style: TextStyle(
                  fontSize: 21.sp,
                  color: const Color(0xFF333333),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///显示备注输入框
  void _showRemarkDialog() {
    GoodsRemarkDialog.show(context, initialRemark: goodsRemark).then((value) {
      if (value != null) {
        setState(() {
          goodsRemark = value;
          goodsDetailDto.memo = value;
        });
      }
    });
  }

  ///点击按钮打开弹窗
  void _onButtonClick(
    BuildContext context,
    String content,
    Widget dialog, {
    BoolCallback? preCheck,
    ValueSetter<GoodsDetailDto>? onResult,
  }) {
    if (null != preCheck && !preCheck()) {
      return;
    }
    showDialog<GoodsDetailDto>(
      context: context,
      builder: (context) => dialog,
    ).then((goods) {
      if (goods == null) {
        return;
      }
      onResult?.call(goods);
    });
  }

  ///获取商品列表中已存在的序列号
  List<String> getExistSN() {
    return [
          goodsDetailDto,
          ...widget.goodsBillDto.inDetail,
          ...widget.goodsBillDto.outDetail,
        ]
        .where((element) => element != widget.goods) //排除当前源商品
        .expand<PtypeSerialNoDto>(
          (element) => (element.snenabled != 0) ? element.serialNoList : [],
        )
        .map<String>((e) => e.snno!)
        .toList();
  }

  ///展示修改零售价弹窗
  void showEditRetailPriceDialog() {
    InputBoardPage.showAlertDialog(
      context,
      defaultText: goodsDetailDto.currencyPrice.toString(),
      scale: SpTool.getSystemConfig().sysDigitalPrice,
      title: "修改零售价",
      maxValue: 1000000,
    ).then((value) {
      if (value is String) {
        _onCurrencyPriceChange(value);
      }
    });
  }

  ///零售价发生改变
  void _onCurrencyPriceChange(String value) {
    num newPrice = num.parse(value);
    if (newPrice != goodsDetailDto.currencyPrice) {
      goodsDetailDto.currencyPrice = newPrice;
      goodsDetailDto.discount = 1;
      editRetailPrice = true;
    }
    ManualPriceUtil.onQtyChange(goodsDetailDto, goodsDetailDto.unitQty);
    setState(() => _updateValue());
  }

  ///折扣发生改变
  void _onDiscountChange(String text) {
    discount = num.parse(text);
    ManualPriceUtil.onDiscountChange(
      goodsDetailDto,
      discount.toDouble(),
      markGift: false,
      markManualPrice: false,
    );
    setState(() {
      _updateValue();
    });
  }

  ///数量发生变化
  void _onCountChange(String text) {
    count = num.parse(text);
    if (GoodsTool.isSerialGoods(goodsDetailDto)) {
      if (goodsDetailDto.serialNoList.length > count) {
        HaloToast.show(context, msg: "商品数量不能小于序列号数量");
        count = goodsDetailDto.serialNoList.length;
      }
    }

    //默认保折扣
    bool keepDiscount = true;
    //折扣或者单价修改之后，会标记手工改价
    //此时修改数量，需要考虑保折扣还是保单价
    if (goodsDetailDto.manualPrice) {
      keepDiscount = goodsDetailDto.isManualDiscount;
    }
    GoodsQtyUtil.onQtyChange(goodsDetailDto, count, keepDiscount: keepDiscount);

    setState(() {
      _updateValue();
    });
  }

  ///折后价格改变
  void _onDiscountPriceChange(String text) {
    discountPrice = num.parse(text);
    BillPriceComputeHelper().priceCompute.onValueChange(
      goodsDetailDto,
      PtypePopValueChangeType.DISCOUNT_PRICE,
      discountPrice.toString(),
    );
    ManualPriceUtil.onPriceChange(
      goodsDetailDto,
      discountPrice,
      markGift: false,
    );
    setState(() {
      _updateValue();
    });
  }

  ///确定
  void _submit() {
    if (goodsDetailDto.currencyDisedTotal < 0) {
      HaloToast.show(context, msg: "小计不能为负");
      return;
    }

    //找到原商品，判断折扣和折后价格是否有改变
    final originGoods = widget.goods;
    bool markManualPrice = true;
    //若原商品不是手工改价，又没有更改折扣，则不更改手工改价状态
    if (!originGoods.manualPrice) {
      if (originGoods.discount == goodsDetailDto.discount) {
        markManualPrice = false;
      }
    }

    ///feat #37035
    StoreInfo storeInfo = SpTool.getStoreInfo()!;
    if (storeInfo.minDiscount != 0) {
      if (goodsDetailDto.discount < storeInfo.minDiscount! &&
          !goodsDetailDto.gift &&
          markManualPrice) {
        HaloToast.showError(context, msg: "低于设置的手工最低折扣，保存失败,如需调整，请到PC端门店资料设置");
        return;
      }
    }
    if (!goodsDetailDto.manualPrice) {
      goodsDetailDto.manualPrice =
          originGoods.discount != goodsDetailDto.discount;
    }
    ManualPriceUtil.setManualPrice(
      goodsDetailDto,
      markManualPrice: markManualPrice,
    );
    NavigateUtil.pop(context);
    widget.valueChanged(goodsDetailDto, editRetailPrice);
  }

  ///赠品开关
  void _changeGift(bool gift) async {
    goodsDetailDto.gift = gift;
    if (goodsDetailDto.gift) {
      BillPriceComputeHelper().priceCompute.onValueChange(
        goodsDetailDto,
        PtypePopValueChangeType.Gift,
        "0",
      );
      ManualPriceUtil.onDiscountChange(goodsDetailDto, 0);
    } else {
      // //刷新价格
      // await _refreshPrice();
      GoodsQtyUtil.onQtyChange(goodsDetailDto, goodsDetailDto.unitQty);
      ManualPriceUtil.onDiscountChange(goodsDetailDto, 1);
    }
    setState(() {
      _updateValue();
    });
  }

  ///单位发生变化
  Future<void> onUnitChanged(PtypeUnitDto unit) async {
    goodsDetailDto.preferentialHelp = {};
    //更改单位
    goodsDetailDto.unitId = unit.id;
    goodsDetailDto.unitName = unit.unitName;
    goodsDetailDto.unitCode = unit.unitCode;
    goodsDetailDto.unitRate = unit.unitRate;
    goodsDetailDto.fullbarcode = unit.barcode ?? "";
    goodsDetailDto.saleOtypeVipPrice = null;

    //刷新价格
    await _refreshPrice();
    if (goodsDetailDto.gift) {
      _changeGift(goodsDetailDto.gift);
    } else {
      setState(() {
        _updateValue();
      });
    }
  }

  ///刷新价格
  Future<void> _refreshPrice() async {
    num discount = goodsDetailDto.discount;
    if (context.mounted) {
      await BillTool.getPtypePrice(context, [goodsDetailDto]);
    }
    editRetailPrice = false;
    // BillPriceComputeHelper().priceCompute.onValueChange(
    //     goodsDetailDto,
    //     PtypePopValueChangeType.PRICE,
    //     goodsDetailDto.currencyPrice.toString());
    // BillPriceComputeHelper()
    //     .priceCompute
    //     .onValueChange(goodsDetailDto, PtypePopValueChangeType.DISCOUNT, "1");
    GoodsQtyUtil.onQtyChange(goodsDetailDto, goodsDetailDto.unitQty);
    ManualPriceUtil.onDiscountChange(
      goodsDetailDto,
      discount,
      markManualPrice: false,
    );
  }

  ///获取商品的单位列表
  Future<void> _getUnitList(BuildContext context) {
    return PtypeModel.getPtypeUnitList(
      context,
      goodsDetailDto.ptypeId,
      goodsDetailDto.skuId ?? "",
    ).then((value) => _unitList = value);
  }

  @override
  void dispose() {
    HaloPopWindow().disMiss();
    super.dispose();
  }
}
