import 'dart:async';

import 'package:flutter/material.dart';
import 'package:halo_pos/common/funtion_extension.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../../bill/widget/ptype/ptype_event_bus_util.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/utils/color_util.dart';

/// 创建时间：8/22/21
/// 作者：xiaotiaochong
/// 描述：

const double _defaultHeight = 390;

class SettlementKeyboard extends StatefulWidget {
  final Function() submitCallback;
  final Function() tipsCallback;

  final double height;

  final double interval;

  SettlementKeyboard({
    Key? key,
    this.height = _defaultHeight,
    this.interval = 10,
    required this.submitCallback,
    required this.tipsCallback,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SettlementKeyboardState();
  }
}

class _SettlementKeyboardState extends State<SettlementKeyboard> {
  ///长按删除
  Timer? timer;

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Colors.transparent,
        alignment: Alignment.center,
        height: widget.height.h,
        margin: EdgeInsets.all(14.w),
        child: SizedBox(
            child: Row(
              children: [
                Expanded(
                    flex: 4,
                    child: Column(
                      children: [
                        Expanded(child: _buildSingleRow(['1', '2', '3', "50"])),
                        SizedBox(
                          height: widget.interval.w,
                        ),
                        Expanded(child: _buildSingleRow(['4', '5', '6', "100"])),
                        SizedBox(
                          height: widget.interval.w,
                        ),
                        Expanded(child: _buildSingleRow(['7', '8', '9', "200"])),
                        SizedBox(
                          height: widget.interval.w,
                        ),
                        Expanded(child: _buildSingleRow(['00', '0', '.', "备注"]))
                      ],
                    )),
                SizedBox(
                  width: widget.interval.w,
                ),
                Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        Expanded(flex: 1, child: _buildSpecialButton()),
                        SizedBox(
                          height: widget.interval.w,
                        ),
                        Expanded(
                          flex: 3,
                          child: _buildButton("确定",
                              alpha: 1.0,
                              colorString: "#2869FD",
                              textColor: Colors.white),
                        ),
                      ],
                    ))
              ],
            )));
  }

  _buildSingleRow(List<String> data) {
    return Row(
      children: [
        Expanded(child: _buildButton(data[0], left: 0)),
        SizedBox(
          width: widget.interval.w,
        ),
        Expanded(child: _buildButton(data[1])),
        SizedBox(
          width: widget.interval.w,
        ),
        Expanded(child: _buildButton(data[2])),
        SizedBox(
          width: widget.interval.w,
        ),
        Expanded(child: _buildButton(data[3], right: 0))
      ],
    );
  }

  _buildSpecialButton() {
    return GestureDetector(
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: ColorUtil.stringColor("#ffffff"),
            borderRadius: BorderRadius.circular(10.w)),
        margin: EdgeInsets.all(1.h),
        child: Icon(
          Icons.backspace_outlined,
          size: 40.w,
          color: Color(0xFF5B5C5D),
        ),
      ),
      onTap: () {
        _textInput("");
      },
      onLongPressStart: (LongPressStartDetails details) {
        timer?.cancel();
        timer =
            Timer.periodic(const Duration(milliseconds: 200), (Timer timer) {
          _textInput("");
        });
      },
      onLongPressEnd: (LongPressEndDetails details) {
        timer?.cancel();
      },
    );
  }

  _buildButton(String text,
      {String colorString = "#ffffff",
        Color? textColor,
        double alpha = 1,
        double left = 1,
        double right = 1,
        double bottom = 1,
        double top = 1}) {
    return GestureDetector(
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: ColorUtil.stringColor(colorString, alpha: alpha),
            borderRadius: BorderRadius.circular(10.w)),
        margin: EdgeInsets.only(
          left: left.w,
          right: right.w,
          bottom: bottom.h,
          top: top.h,
        ),
        child: Text(text,
            style: TextStyle(
                color: textColor ?? Color(0xFF5B5C5D),
                decoration: TextDecoration.none,
                fontSize: 33.sp,
                fontWeight: FontWeight.bold)),
      ),
      onTap: () {
        if (text == "确定") {
          widget.submitCallback();
        } else if (text == "备注") {
          widget.tipsCallback();
        }else {
          _textInput(text);
        }
      }.throttle(),
    );
  }

  _textInput(String text) {
    PtypeEventBusUtils.getInstance().fire(TextKeyboardValue(text));
  }
}
