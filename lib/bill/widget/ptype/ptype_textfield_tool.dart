import 'package:flutter/material.dart';
import 'package:haloui/haloui.dart';

/// 创建时间：9/14/21
/// 作者：xiaotiaochong
/// 描述：

class PtypeTextFieldTool {
  static EditingValue specialTextFiledChangeValue(
    String text,
    TextEditingController controller,
    int index, {
    int afterPoint = 2,
    num? max,
    String? reString,
  }) {
    String returnString = controller.text;
    if (text == "") {
      if (controller.selection.start != 0) {
        index = controller.selection.end - 1;
        String string = returnString.replaceRange(
            controller.selection.start - 1, controller.selection.end, "");
        returnString = string;
      }
      if (returnString.isEmpty) {
        returnString = reString ?? "0";
      }
    } else {
      if (controller.text.contains(".")) {
        String afterPointString = controller.text.substring(
            controller.text.indexOf(".") + 1, controller.text.length);
        if (afterPointString.length == afterPoint &&
            controller.selection.start > controller.text.indexOf(".")) {
          text = "";
        }
      }
      //当小数位数为0，则不允许出现小数点
      if (afterPoint <= 0 && text == ".") {
        text = "";
      }
      // if ((controller.text == "0" ) {
      //
      // }
      if (controller.text != "" &&
          (num.parse(controller.text) == 0
              // && (controller.selection.start == 0 || controller.selection.start == 1)
              &&
              !controller.text.contains(".") &&
              text != ".")) {
        returnString = text;
        index = 0;
      } else {
        String tempString = "";
        String start = returnString.substring(0, controller.selection.start);
        String end = returnString.substring(
            controller.selection.end, returnString.length);
        tempString = start + text + end;
        returnString =
            isChangeNum(tempString, afterPoint: afterPoint) ?? tempString;
        if (returnString == tempString) {
          index = controller.selection.end + text.length;
        } else {
          index = controller.selection.end;
        }
      }
    }
    bool isMax = false;
    returnString = returnString == "-" ? "0" : returnString ?? "0";
    returnString = returnString == "." ? "0" : returnString;

    if (returnString != "" && max != null && num.parse(returnString) > max) {
      returnString = max.toString();
      index = returnString.length;
      isMax = true;
    }
    if (index > returnString.length) {
      index = returnString.length;
    }
    controller.text = returnString;
    return EditingValue(index, isMax);
  }

  static String isChange(String str, String regExpString) {
    return RegExp(regExpString).stringMatch(str)!;
  }

  static String isChangeNum(String s, {int afterPoint = 2}) {
    //默认小数点后两位
    return isChange(
        s,
        r"^[1-9]\d*\.\d{0," +
            afterPoint.toString() +
            r"}|0\.\d{0," +
            afterPoint.toString() +
            r"}|[1-9][0-9]+$");
  }
}

class EditingValue {
  int index;
  bool max;

  EditingValue(this.index, this.max);
}
