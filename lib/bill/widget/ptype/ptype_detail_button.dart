import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

/// 创建时间：12/20/21
/// 作者：xiaotiaochong
/// 描述：

class PtypeDetailButton extends StatelessWidget {
  final VoidCallback submit;
  final VoidCallback cancel;
  final EdgeInsets? margin;

  const PtypeDetailButton(
      {Key? key, required this.submit, required this.cancel, this.margin})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      margin: margin,
      children: [
        Expanded(
            child: _buildButton("确定", HaloButtonType.elevatedButton,
                callback: submit)),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
            child: _buildButton("取消", HaloButtonType.outlinedButton,
                borderColor: const Color(0xFFB7B7B7), callback: cancel))
      ],
    );
  }

  _buildButton(String text, HaloButtonType type,
      {Color? borderColor, required VoidCallback callback}) {
    return HaloButton(
      height: 90.h,
      buttonType: type,
      borderRadius: 4.w,
      fontSize: 26.sp,
      text: text,
      borderColor: borderColor,
      outLineWidth: 2.w,
      onPressed: () {
        callback();
      },
    );
  }
}
