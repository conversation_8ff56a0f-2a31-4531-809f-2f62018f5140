import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../../bill/bill/channel/widget/base_common_dialog.dart';

/// 商品备注弹窗
class GoodsRemarkDialog extends StatefulWidget {
  final String initialRemark;

  const GoodsRemarkDialog({Key? key, this.initialRemark = ''})
    : super(key: key);

  static Future<String?> show(
    BuildContext context, {
    String initialRemark = '',
  }) async {
    return await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => GoodsRemarkDialog(initialRemark: initialRemark),
    );
  }

  @override
  State<StatefulWidget> createState() => _GoodsRemarkDialogState();
}

class _GoodsRemarkDialogState extends BaseCommonDialogState<GoodsRemarkDialog> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isKeyboardVisible = false;
  static const int _maxLength = 20; // 最大字符数限制

  @override
  void initState() {
    super.initState();
    // 如果初始文本超过限制，截取前20个字符
    if (widget.initialRemark.length > _maxLength) {
      _controller.text = widget.initialRemark.substring(0, _maxLength);
    } else {
      _controller.text = widget.initialRemark;
    }
    _focusNode.addListener(_onFocusChange);

    // 添加文本变化监听，确保不超过字符限制
    _controller.addListener(() {
      if (_controller.text.length > _maxLength) {
        _controller.text = _controller.text.substring(0, _maxLength);
        _controller.selection = TextSelection.fromPosition(
          TextPosition(offset: _controller.text.length),
        );
      }
    });
  }

  void _onFocusChange() {
    setState(() {
      _isKeyboardVisible = _focusNode.hasFocus;
    });
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  double get width => 560.w;

  @override
  double get height => 350.h;

  @override
  String get title => "商品备注";

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        onCloseButtonClick(context);
      },
      child: AnimatedPadding(
        padding: EdgeInsets.only(
          bottom:
              _isKeyboardVisible
                  ? MediaQuery.of(context).viewInsets.bottom * 0.5
                  : 0,
        ),
        duration: const Duration(milliseconds: 100),
        curve: Curves.easeOut,
        child: UnconstrainedBox(
          child: SizedBox(
            child: Material(
              borderRadius: BorderRadius.circular(10.w),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10.w),
                child: HaloContainer(
                  width: width,
                  height: height,
                  color: Colors.white,
                  direction: Axis.vertical,
                  children: [
                    //标题
                    Visibility(
                      visible: title.isNotEmpty,
                      child: buildTitle(context),
                    ),
                    //内容，如列表和搜索框
                    Expanded(child: buildContent(context)),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget buildContent(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Column(
        children: [
          // 文本输入框
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6.w),
                      border: Border.all(
                        color: const Color(0xFFDDDDDD),
                        width: 1.5.w,
                      ),
                    ),
                    child: TextField(
                      controller: _controller,
                      focusNode: _focusNode,
                      maxLines: null,
                      expands: true,
                      maxLength: _maxLength,
                      buildCounter: (
                        context, {
                        required currentLength,
                        required isFocused,
                        maxLength,
                      }) {
                        return null; // 隐藏默认计数器，使用自定义计数器
                      },
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: "请输入商品备注",
                        hintStyle: TextStyle(
                          color: const Color(0xFFBBBBBB),
                          fontSize: 22.sp,
                        ),
                        contentPadding: EdgeInsets.all(15.w),
                      ),
                    ),
                  ),
                ),
                // 字符计数器
                Container(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.only(top: 8.h, right: 4.w),
                  child: ValueListenableBuilder<TextEditingValue>(
                    valueListenable: _controller,
                    builder: (context, value, child) {
                      return Text(
                        '${value.text.length}/$_maxLength',
                        style: TextStyle(
                          color:
                              value.text.length >= _maxLength
                                  ? Colors.red
                                  : const Color(0xFF999999),
                          fontSize: 18.sp,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          // 底部按钮
          Container(
            margin: EdgeInsets.only(top: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // 取消按钮
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 150.w,
                    height: 70.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.w),
                      border: Border.all(
                        color: const Color(0xFFDADADA),
                        width: 1.5.w,
                      ),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "取消",
                      style: TextStyle(
                        color: const Color(0xFF333333),
                        fontSize: 22.sp,
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 10.w),

                // 保存按钮
                GestureDetector(
                  onTap: () => Navigator.pop(context, _controller.text),
                  child: Container(
                    width: 150.w,
                    height: 70.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.w),
                      color: const Color(0xFF4679FC),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "保存",
                      style: TextStyle(color: Colors.white, fontSize: 22.sp),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
