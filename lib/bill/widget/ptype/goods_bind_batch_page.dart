import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../../bill/entity/goods_batch_dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/widget/ptype/base_goods_dialog.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/permission_dto.dart';

///商品绑定一个批次号弹窗
///会弹出一个窗体，列表中展示该类型商品的批次号列表，
///点击其中一个批次号进行绑定
class GoodsBindBatchPage extends StatefulWidget {
  ///需要绑定的商品
  final GoodsDetailDto goods;

  const GoodsBindBatchPage({required this.goods, Key? key}) : super(key: key);

  @override
  State<GoodsBindBatchPage> createState() => _GoodsBindBatchPageState();
}

class _GoodsBindBatchPageState extends BaseGoodsDialogState<GoodsBindBatchPage>
    with ListAndListTitleMixin, GoodsInfoMixin {
  @override
  String get goodsQty => widget.goods.unitQty.toString();

  @override
  String get goodsBarcode => widget.goods.fullbarcode;

  @override
  String get goodsName => widget.goods.pFullName ?? "";

  @override
  String get goodsPicUrl => widget.goods.picUrl ?? "";

  @override
  double get height => 838.h;

  @override
  double get width => 824.w;

  @override
  int get itemCount => _batchList.length;

  @override
  String get title => "选择批次号";

  ///表头各列标题
  @override
  List<String> get listTitles => ["批次号", "生产日期", "到期日期", "批次库存"];

  ///列表各列比例
  @override
  List<int> get listColumnFlex => [3, 2, 2, 1];

  ///批次列表
  final List<GoodsBatchDto> _batchList = [];

  final PermissionDto permissionDto = SpTool.getPermission();

  @override
  void initState() {
    super.initState();
    _requestData(context);
  }

  // ///构建批次号
  // Widget _buildBatchNoText(GoodsBatchDto batch) {
  //   //当商品costMode为1时，此时为个别计价，需要展示批次成本
  //   if (widget.goods.costMode == 1 && permissionDto.recordsheetProductCostPriceGroupConfigview!) {
  //     return LayoutBuilder(
  //       //这里通过LayoutBuilder拿到父控件的约束,保证Row里面的第二个没有设置约束的Text不会越界
  //       builder: (context, constraints) => Row(
  //         children: [
  //           Flexible(child: _buildText(batch.batchNo ?? "")),
  //           ConstrainedBox(
  //             constraints: constraints.copyWith(
  //                 minWidth: 0, maxWidth: constraints.maxWidth - 100.w),
  //             child: _buildText("￥${batch.batchPrice ?? 0}",
  //                 style: style.copyWith(color: const Color(0xFFFF4141))),
  //           ),
  //         ],
  //       ),
  //     );
  //   } else {
  //     return _buildText(batch.batchNo ?? "");
  //   }
  // }
  //
  // Widget _buildText(String text, {TextStyle? style}) {
  //   return Text(text,
  //       style: style ?? this.style,
  //       maxLines: 1,
  //       overflow: TextOverflow.ellipsis);
  // }

  ///构建item
  @override
  Widget buildListItem(BuildContext context, int index) {
    GoodsBatchDto batch = _batchList[index];
    return Column(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            //将该商品关联上批次号
            widget.goods.batchId = batch.batchId;
            //批次成本
            widget.goods.batchPrice = batch.batchPrice ?? 0;
            //解决个别计价商品，选择批次后核算不通过的问题
            widget.goods.costId = batch.costId;
            widget.goods.batchNo = batch.batchNo ?? "";
            widget.goods.produceDate = batch.produceDate ?? "";
            widget.goods.expireDate = batch.expireDate;
            widget.goods.protectDays = batch.protectDays;
            widget.goods.batchInventoryQty = batch.inventoryQty;
            if (widget.goods.unitQty == 0) {
              widget.goods.unitQty = 1;
            }
            widget.goods.serialNoList.clear();
            Navigator.pop(context, widget.goods);
          },
          child: HaloContainer(height: 70.h, padding: listPadding, children: [
            Expanded(
                flex: listColumnFlex[0],
                child: buildBatchNoText(
                    costMode: widget.goods.costMode,
                    batchNo: batch.batchNo ?? "",
                    batchPrice: batch.batchPrice ?? 0,
                    showBatchCost:
                        permissionDto.shopsalesalesettingviewBatchCost ??
                            false)),
            Expanded(
                flex: listColumnFlex[1],
                child: buildText(parseDate(batch.produceDate ?? ""))),
            Expanded(
                flex: listColumnFlex[2],
                child: buildText(parseDate(batch.expireDate ?? ""))),
            Expanded(
                flex: listColumnFlex[3],
                child: buildText((batch.inventoryQty ?? 0).toString())),
          ]),
        ),
        //分割线
        divider
      ],
    );
  }

  ///请求数据
  void _requestData(BuildContext context) {
    final config = SpTool.getStoreInfo();
    BillModel.getGoodsBatchList(context,
            ktypeId: config!.ktypeId,
            ptypeId: widget.goods.ptypeId,
            skuId: widget.goods.skuId,
            costMode: widget.goods.costMode)
        .then((list) {
      setState(() {
        _batchList.clear();
        _batchList.addAll(list);
      });
    });
  }

  ///退出界面时调用，如果批次号为空，则弹窗提示
  @override
  void onCloseButtonClick(BuildContext context) {
    //如果批次号为空，则弹窗提示
    // if (StringUtil.isEmpty(widget.goods.batchNo)) {
    //   DialogUtil.showConfirmDialog(context,
    //       title: "提示",
    //       content: "还未设置任何批次号，是否退出?",
    //       actionLabels: ["取消", "确定"], confirmCallback: () {
    //     Navigator.pop(context, widget.goods);
    //   });
    // } else {
    Navigator.pop(context, widget.goods);
    // }
  }
}

TextStyle get textStyle => TextStyle(
    fontWeight: FontWeight.bold,
    color: const Color(0xff333333),
    fontSize: 22.sp);

///将批次中的日期转换成本地时间
///批次中日期是utc时间,例如:2023-03-12T16:00:00Z,这是个utc时间,0时区时间
///而本地需要展示本地时间
String parseDate(String date) {
  if (date.isNotEmpty == true) {
    //Z代表utc时间,0时区，所以只需将其转换为本地时区即可
    DateTime? dateTime = DateTime.tryParse(date)?.toLocal();
    if (dateTime == null) {
      return "";
    }
    return "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}";
  }
  return "";
}

Widget buildText(String text, {TextStyle? style}) {
  style ??= textStyle;
  return Text(text, style: style, maxLines: 1, overflow: TextOverflow.ellipsis);
}

///构建批次号
Widget buildBatchNoText({
  required int costMode,
  required String batchNo,
  required num batchPrice,
  double? batchNoMinWidth,
  bool showBatchCost = false,
}) {
  batchNoMinWidth ??= 100.w;
  //当商品costMode为1时，此时为个别计价，需要展示批次成本
  if (costMode == 1 && showBatchCost) {
    return LayoutBuilder(
      //这里通过LayoutBuilder拿到父控件的约束,保证Row里面的第二个没有设置约束的Text不会越界
      builder: (context, constraints) => Row(
        children: [
          Flexible(child: buildText(batchNo)),
          ConstrainedBox(
            constraints: constraints.copyWith(
                minWidth: 0, maxWidth: constraints.maxWidth - batchNoMinWidth!),
            child: buildText("￥$batchPrice",
                style: textStyle.copyWith(color: const Color(0xFFFF4141))),
          ),
        ],
      ),
    );
  } else {
    return buildText(batchNo);
  }
}
