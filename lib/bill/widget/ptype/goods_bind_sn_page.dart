import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';

import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/ptype/ptype_serial_no_dto.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/tool/scan_tool.dart';
import '../../../bill/widget/ptype/base_goods_dialog.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../tool/goods_tool.dart';

///编辑开单商品关联的序列号列表弹窗
class GoodsBindSnPage extends StatefulWidget {
  ///需要编辑序列号的商品
  final GoodsDetailDto _goods;

  ///已经在单据中存在的序列号，用来校验新增的序列号是否重复
  ///不作展示
  final List<String> _existSN;

  ///单据类型
  final BillType _billType;

  ///是否限制序列号数量
  ///序列号数量一定小于等于商品数量
  ///当为true时，此时序列号数量最多为商品数量，
  ///当为false时，则不限制序列号数量，若序列号数量大于商品数量，则调整商品数量为序列号数量
  final bool _limitMaxCount;

  const GoodsBindSnPage(
      {Key? key,
      required GoodsDetailDto goods,
      List<String> existSN = const [],
      BillType billType = BillType.SaleBill,
      bool limitMaxCount = false})
      : assert(goods != null),
        _goods = goods,
        _existSN = existSN,
        _billType = billType,
        _limitMaxCount = limitMaxCount,
        super(key: key);

  @override
  BaseGoodsDialogState<GoodsBindSnPage> createState() =>
      _GoodsBindSnPageState();
}

class _GoodsBindSnPageState extends BaseGoodsDialogState<GoodsBindSnPage>
    with ListAndListTitleMixin, SearchMixin, GoodsInfoMixin {
  @override
  // TODO: implement goodsQty
  String get goodsQty => widget._goods.unitQty.toString();

  @override
  String get goodsBarcode => widget._goods.fullbarcode;

  @override
  String get goodsName => widget._goods.pFullName ?? "";

  @override
  String get goodsPicUrl => widget._goods.picUrl ?? "";

  @override
  double get height => 702.h;

  @override
  double get width => height;

  @override
  int get itemCount => _serialNoList.length;

  @override
  EdgeInsets get listPadding => EdgeInsets.only(left: 40.w, right: 74.w);

  @override
  double get searchBarHeight => 62.h;

  @override
  String get searchHint => "扫描或输入序列号";

  @override
  Color get searchBarColor => const Color(0xFFF4F4F4);

  @override
  String get title => "商品序列号";

  ///是否是严格序列号
  ///1为严格
  ///2为宽松
  bool get _isStrictSn => widget._goods.snenabled == 1;

  ///当前序列号列表
  final List<PtypeSerialNoDto> _serialNoList = [];

  @override
  void initState() {
    _serialNoList.addAll(widget._goods.serialNoList);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      focusNode.requestFocus();
    });
    super.initState();
  }

  @override
  Widget buildContent(BuildContext context) {
    return Column(children: [
      Expanded(child: super.buildContent(context)),
      //底部分割线及确定按钮
      _buildBottom(context)
    ]);
  }

  ///构建item
  @override
  Widget buildListItem(BuildContext context, int index) {
    final style = TextStyle(
        fontWeight: FontWeight.bold,
        color: const Color(0xff333333),
        fontSize: 24.sp);
    PtypeSerialNoDto sn = _serialNoList[index];
    return HaloContainer(
      padding: listPadding,
      height: 64.h,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
            child: Text(sn.snno ?? "",
                style: style, maxLines: 1, overflow: TextOverflow.ellipsis)),
        GestureDetector(
            onTap: () => DialogUtil.showConfirmDialog(context,
                title: "提示",
                content: "是否删除该序列号",
                actionLabels: ["取消", "确定"],
                confirmCallback: () => setState(() {
                      widget._existSN.remove(sn.snno);
                      _serialNoList.remove(sn);
                    })),
            child: IconFont(IconNames.shanchu, size: 22.w))
      ],
    );
  }

  ///构建底部分割线和确定按钮
  _buildBottom(BuildContext context) {
    return Column(
      children: [
        divider,
        HaloContainer(
          height: 100.h,
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          padding: dialogPadding,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                //严格序列号必须录入序列号才能添加商品
                if (_isStrictSn && _serialNoList.isEmpty) {
                  HaloToast.show(context, msg: "还未设置任何序列号");
                  return;
                }
                //点击确定，将改动的序列号列表赋值给商品
                widget._goods.serialNoList = _serialNoList;
                //调整商品个数
                _changeGoodsCount();
                _onCloseButtonClick(context);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF4679FC),
                  borderRadius: BorderRadius.circular(8.w),
                ),
                alignment: Alignment.center,
                width: 194.w,
                height: 66.h,
                child: HaloPosLabel(
                  "确定",
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  textStyle: TextStyle(
                      color: Colors.white,
                      decoration: TextDecoration.none,
                      fontSize: 32.sp,
                      fontWeight: FontWeight.bold),
                ),
              ),
            )
          ],
        )
      ],
    );
  }

  ///添加序列号到列表
  ///需要先判断序列号是否被使用:
  ///1.是否已经出库
  ///2.是否已经被现在这个单据使用
  Future<PtypeSerialNoDto?> _addSn(BuildContext context, String sn) {
    if (sn.isNotEmpty != true) {
      return Future.value(null);
    }
    //先检查是否被当前单据使用
    if (widget._existSN.any((element) => StringUtil.equal(element, sn))) {
      HaloToast.show(context, msg: "该序列号已被使用");
      return Future.value(null);
    }
    if (_serialNoList.any((element) => StringUtil.equal(element.snno!, sn))) {
      HaloToast.show(context, msg: "该序列号已被使用");
      return Future.value(null);
    }
    PtypeSerialNoDto snDto = PtypeSerialNoDto()
      ..snno = sn
      //默认在库,不在库通过接口判断
      ..isInTheStock = true
      ..batchNo = widget._goods.batchNo;

    if (widget._billType != BillType.SaleBill &&
        widget._billType != BillType.SaleChangeBill) {
      //todo 目前本地调拨单只有入库，没有出库。所以出库只有销售单据需要校验序列号
      return Future.value(snDto);
    }
    //如果是销售单，需要检查该序列号是否已经被使用
    return BillModel.getGoodsWithoutPriceBySn(
      context,
      scanCode: sn,
      billType: widget._billType,
      date: DateUtil.formatDate(DateTime.now()),
    ).then((GoodsDetailDto? goodsDetailDto) async {
      if (!context.mounted) return null;
      //这里调用接口通过序列号查询商品是否存在，若获取到了商品，则说明该序列号可以使用
      //并且判断查询出来的商品sku和当前商品的sku是否一致
      String? message;
      if (goodsDetailDto == null) {
        snDto.isInTheStock = false;
        message = "已被使用或不存在";
      } else {
        if (goodsDetailDto.serialNoList.first.ktypeId !=
            SpTool.getStoreInfo()!.ktypeId) {
          snDto.isInTheStock = false;
          message = "不在库";
        } else {
          ;
          //判断是否同一个商品
          bool isSamePtype = GoodsTool.compareUnitSku(
              goodsDetailDto, widget._goods,
              comparePrice: false);
          if (isSamePtype) {
            //判断是否是当前批次下的序列号
            if (widget._goods.batchenabled &&
                !ScanTool.isSameBatch(widget._goods, goodsDetailDto)) {
              message = "不是[${widget._goods.batchNo}]的批次下序列号";
            }
          } else {
            message = "已被其他商品或sku绑定";
          }
        }
      }
      if (message != null) {
        final index = await DialogUtil.showConfirmDialog(context,
            content: "序列号[$sn]$message，是否使用此序列号？", actionLabels: ["取消", "确定"]);
        if (index != 1) {
          return null;
        }
      }
      return snDto;
    }).onError((error, stackTrace) {
      return null;
    });
  }

  @override
  void onSearched(BuildContext context) {
    if (widget._limitMaxCount &&
        _serialNoList.length >= widget._goods.unitQty) {
      HaloToast.show(context, msg: "序列号数量不能大于${widget._goods.unitQty}");
      return;
    }
    //检查序列号是否被使用
    _addSn(context, filterValue).then((sn) {
      if (sn != null) {
        setState(() => _serialNoList.add(sn));
        filterValue = "";
      }
      focusNode.requestFocus();
    });
  }

  ///退出界面时调用，如果序列号列表为空，则弹窗提示
  @override
  void onCloseButtonClick(BuildContext context) {
    _onCloseButtonClick(context);
  }

  void _onCloseButtonClick(BuildContext context) {
    // //如果当前序列号列表为空，则弹窗提示
    // if (widget._goods.serialNoList.isEmpty != false) {
    //   DialogUtil.showConfirmDialog(context,
    //       title: "提示",
    //       content: "还未设置任何序列号，是否${isConfirm ? "确定" : "退出"}?",
    //       actionLabels: ["取消", "确定"], confirmCallback: () {
    //     Navigator.pop(context, result);
    //   });
    // } else {
    Navigator.pop(context, widget._goods);
    // }
  }

  ///根据序列号列表个数，调整商品数量
  void _changeGoodsCount() {
    final goods = widget._goods;
    //在严格序列号下，序列号数量和商品数量必须一致
    if (_isStrictSn) {
      //TODO 赠品数量不能改，这个位置暂时这样
      //不然少录入序列号，赠品就减少了
      if (goods.unitQty < goods.serialNoList.length) {
        goods.unitQty = goods.serialNoList.length;
      }
      // goods.unitQty = goods.serialNoList.length;
    } else {
      //序列号数量不得多于商品数量
      if (goods.serialNoList.length > (goods.unitQty)) {
        goods.unitQty = goods.serialNoList.length;
      }
    }
  }
}
