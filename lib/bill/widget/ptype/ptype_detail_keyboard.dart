import 'dart:async';

import 'package:flutter/material.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../../bill/widget/ptype/ptype_event_bus_util.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';


/// 创建时间：8/22/21
/// 作者：xiaotiaochong
/// 描述：

const double _defaultWidth = 604;
const double _defaultHeight = 300;

///enable 为false 可以手动调用 normalCallback
class PtypeDetailKeyboard extends StatefulWidget {
  Function()? submitCallback;
  double width;
  double height;
  double margin;
  bool enable;
  Function(String text)? normalCallback;

  PtypeDetailKeyboard({
    this.enable = true,
    this.width = _defaultWidth,
    this.height = _defaultHeight,
    this.margin = 24,
    this.submitCallback,
    this.normalCallback,
  });

  @override
  State<StatefulWidget> createState() {
    return _PtypeDetailKeyboardState();
  }
}

class _PtypeDetailKeyboardState extends State<PtypeDetailKeyboard>{

  late Timer timer;//长按删除

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        width: widget.width.w,
        height: widget.height.h,
        padding: EdgeInsets.symmetric(horizontal: widget.margin.w),
        child:
        // Row(
        //   children: [
        //     Expanded(
        //         child:
        Column(
          children: [
            Expanded(child: _buildSingleRow(['1', '2', '3'])),
            Expanded(child: _buildSingleRow(['4', '5', '6'])),
            Expanded(child: _buildSingleRow(['7', '8', '9'])),
            Expanded(child: _buildSpecialSingleRow(['', '.', '0']))
          ],
        )
      // ),
      // Expanded(flex: 1, child: _buildButton("确定"))
      //   ],
      // ),
    );
  }

  _buildSingleRow(List<String> data) {
    return Row(
      children: [
        Expanded(child: _buildButton(data[0],left: 0)),
        Expanded(child: _buildButton(data[1])),
        Expanded(child: _buildButton(data[2],right: 0))
      ],
    );
  }

  _buildSpecialSingleRow(List<String> data) {
    return Row(children: [
      Expanded(child: _buildSpecialButton()),
      Expanded(child: _buildButton(data[1])),
      Expanded(child: _buildButton(data[2],right: 0))
    ]);
  }

  _buildSpecialButton() {
    return GestureDetector(
      child: Container(
        height: 67.h,
        alignment: Alignment.center,
        color: ColorUtil.stringColor("#F5F5F5"),
        margin: EdgeInsets.only(
          right: 5.w,
          top: 5.w,
          bottom: 5.w,
        ),
        child: Icon(
          Icons.backspace_outlined,
          size: 40.w,
          color: Colors.black,
        ),
      ),
      onTap: () {
        _textInput("");
      },
      onLongPressStart: (LongPressStartDetails details){
        timer?.cancel();
        timer = Timer.periodic(const Duration(milliseconds: 200), (Timer timer){
          _textInput("");
        });
        print(details.toString());
      },
      onLongPressEnd: (LongPressEndDetails details){
        timer?.cancel();
        print(details.toString());
      },
    );
  }

  _buildButton(String text,{
    double left = 5,
    double right = 5,
    double bottom = 5,
    double top = 5}) {
    return GestureDetector(
      child: Container(
        height: 67.h,
        alignment: Alignment.center,
        color: ColorUtil.stringColor("#F5F5F5"),
        margin: EdgeInsets.only(
          left: left.w,
          right: right.w,
          bottom: bottom.w,
          top: top.w,
        ),
        child: HaloPosLabel(text,
            textStyle: TextStyle(
                color: Colors.black,
                decoration: TextDecoration.none,
                fontSize: 26.sp,
                fontWeight: FontWeight.bold)),
      ),
      onTap: () {
        if (text == "确定") {
          if (widget.submitCallback != null) {
            widget.submitCallback!();
          }
        } else {
          _textInput(text);
        }
      },
    );
  }
  _textInput(String text){
    if (widget.enable) {
      PtypeEventBusUtils.getInstance().fire(TextKeyboardValue(text));
    } else {
      if (widget.normalCallback != null) {
        widget.normalCallback!(text);
      }
    }
  }
}