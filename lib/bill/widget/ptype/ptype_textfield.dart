import 'dart:async';

import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;

import '../../../bill/widget/ptype/ptype_event_bus_util.dart';
import '../../../bill/widget/ptype/ptype_textfield_tool.dart';
import '../../../common/keyboard_hidden.dart';
import '../../../common/tool/decimal_scale_input_formatter.dart';
import '../../../common/tool/sp_tool.dart';

///商品价格输入框
class PtypeTextField extends StatefulWidget {
  final String defaultText;
  final Color? textColor;
  final EdgeInsetsGeometry? contentPadding;
  final Function(String text)? onChange;
  final int afterPoint;
  final num? maxValue;
  final KeyboardHiddenFocusNode? focusNode;
  final bool enable;
  final String? tips;
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final String? reString;
  final bool autoFocus;

  const PtypeTextField(
      {Key? key,
      required this.defaultText,
      this.enable = true,
      this.tips,
      this.focusNode,
      this.autoFocus = true,
      this.textColor,
      this.contentPadding,
      this.onChange,
      this.maxValue,
      this.textStyle,
      this.textAlign,
      this.reString,
      this.afterPoint = 2})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return PtypeTextFieldState();
  }
}

class PtypeTextFieldState extends State<PtypeTextField> {
  KeyboardHiddenFocusNode _focusNode = KeyboardHiddenFocusNode();
  int _editIndex = 0;
  final TextEditingController controller = TextEditingController();
  num maxValue = 0;
  StreamSubscription? subscription;

  // VoidCallback listener;

  @override
  void initState() {
    super.initState();
    controllerMax();
    initFocusNode();
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus && mounted) {
        initSelection();
      }
    });
    initSelection();
    subscription = PtypeEventBusUtils.getInstance()
        .on<TextKeyboardValue>()
        .listen((event) {
      if (_focusNode.hasFocus) {
        if (mounted) {
          setState(() {
            EditingValue editingValue =
                PtypeTextFieldTool.specialTextFiledChangeValue(
                    event.text, controller, _editIndex,
                    afterPoint: widget.afterPoint,
                    max: maxValue ?? 0,
                    reString: widget.reString);
            _editIndex = editingValue.index;
            controller.selection = TextSelection.fromPosition(TextPosition(
                affinity: TextAffinity.downstream,
                offset: _editIndex == 0 ? controller.text.length : _editIndex));
            // if (widget.onChange != null) {
            //   widget.onChange!(controller.text);
            // }
            if (editingValue.max) {
              HaloToast.show(context, msg: widget.tips ?? "已达最大值!");
            }
          });
        }
      }
    });
    controller.addListener(onChange);
  }

  void onChange() {
    if (_focusNode.hasFocus) {
      widget.onChange?.call(controller.text);
    }
  }

  @override
  void didUpdateWidget(PtypeTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    controllerMax();
    initFocusNode();
    if (!_focusNode.hasFocus) {
      _editIndex = 0;
      initSelection();
    }
  }

  void initFocusNode() {
    if (widget.focusNode != null) {
      _focusNode = widget.focusNode!;
    }
  }

  void initSelection() {
    controller.text = widget.defaultText;
    final int offset = controller.text.length;
    controller.selection = TextSelection.fromPosition(
        TextPosition(affinity: TextAffinity.downstream, offset: offset));
  }

  void controllerMax() {
    maxValue = widget.maxValue ?? SpTool.getSystemConfig().sysGlobalDecimalMax;
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardHiddenTextField(
      enabled: widget.enable,
      textAlign: widget.textAlign ?? TextAlign.right,
      style: widget.textStyle ??
          TextStyle(
            color: widget.textColor ?? Colors.black,
            fontSize: 28.sp,
          ),
      // decoration: InputDecoration(
      //   border: InputBorder.none,
      //   contentPadding:
      //       widget.contentPadding ?? const EdgeInsets.fromLTRB(5, 0, 5, 1),
      // ),
      focusNode: _focusNode,
      autofocus: widget.autoFocus,
      controller: controller,
      onTapBefore: () => false,
      cleanTextWhenSearch: false,
      inputFormatters: [
        DecimalScaleInputFormatter(scale: widget.afterPoint, max: maxValue)
      ],
      onTap: () {
        _focusNode.requestFocus();
      },
    );
  }

  @override
  void dispose() {
    controller.removeListener(onChange);
    super.dispose();
    subscription?.cancel();
  }
}
