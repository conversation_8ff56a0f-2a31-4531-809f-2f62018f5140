import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';

import '../../../bill/entity/goods_detail_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/date_picker.dart';
import '../../../widgets/halo_pos_label.dart';
import 'base_goods_dialog.dart';

///退货商品绑定批次号
class BackGoodsBindBatchPage extends StatefulWidget {
  ///需要绑定的商品
  final GoodsDetailDto goods;

  const BackGoodsBindBatchPage({required this.goods, Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _BackGoodsBindBatchPageState();
}

class _BackGoodsBindBatchPageState
    extends BaseGoodsDialogState<BackGoodsBindBatchPage> with GoodsInfoMixin {
  @override
  String get goodsQty => widget.goods.unitQty.toString();

  @override
  String get goodsBarcode => widget.goods.fullbarcode ?? "";

  @override
  String get goodsName => widget.goods.pFullName ?? "";

  @override
  String get goodsPicUrl => widget.goods.picUrl ?? "";

  @override
  double get height => 650.h;

  @override
  double get width => 702.w;

  @override
  String get title => "录入批次号";

  ///批次号
  String _batchNo = "";

  ///生产日期
  DateTime _produceDate = DateTime.now();

  ///过期日期
  DateTime _expireDate = DateTime.now();

  final TextStyle _contentTextStyle =
      TextStyle(fontSize: 26.sp, color: const Color(0xFF333333));

  ///日期转字符串，展示用
  String _dateToString(DateTime date) =>
      DateUtil.formatDate(date, format: DataFormats.y_mo_d);

  @override
  Widget buildContent(BuildContext context) {
    return Column(children: [
      //商品信息
      buildGoodsInfoWidget(),
      //批次信息
      Expanded(child: _buildContent(context)),
      //底部按钮
      _buildBottom(context)
    ]);
  }

  ///批次信息
  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: dialogPadding,
      child: Column(children: [
        //批次号
        _buildRow(
          context,
          "批次号",
          TextField(
            decoration: const InputDecoration(
              hintText: "录入批次号",
              contentPadding: EdgeInsets.zero,
              border: OutlineInputBorder(borderSide: BorderSide.none),
            ),
            style: _contentTextStyle,
            maxLines: 1,
            inputFormatters: [LengthLimitingTextInputFormatter(100)],
            onChanged: (text) => _batchNo = text.trim(),
          ),
        ),
        //生产日期和过期日期
        Visibility(
          visible: (widget.goods.protectDays ?? 0) > 0,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              //生产日期
              Padding(
                  padding: EdgeInsets.only(top: 28.h),
                  child: _buildDateRow(
                    context,
                    title: "生产日期",
                    hint: "录入生产日期",
                    content: _dateToString(_produceDate),
                    onTap: () => DatePicker.showDatePicker(
                      context,
                      title: "生产日期",
                      initialDateTime: _produceDate,
                      listener: (DateTime date) {
                        setState(() {
                          _produceDate = date;
                          //使用保质期计算出过期日期
                          _expireDate = date.add(Duration(
                              days: widget.goods.protectDays?.toInt() ?? 1));
                        });
                      },
                    ),
                  )),
              //过期日期
              Padding(
                padding: EdgeInsets.only(top: 28.h),
                child: _buildDateRow(context,
                    title: "到期日期",
                    hint: "到期日期",
                    content: _dateToString(_expireDate),
                    background: const Color(0xFFF4F4F4)),
              ),
            ],
          ),
        )
      ]),
    );
  }

  ///构建日期选择行
  Widget _buildDateRow(BuildContext context,
      {required String title,
      required String content,
      required String hint,
      VoidCallback? onTap,
      Color? background}) {
    TextEditingController controller = TextEditingController(text: content);
    return _buildRow(
        context,
        title,
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: onTap,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: hint,
                    contentPadding: EdgeInsets.zero,
                    border:
                        const OutlineInputBorder(borderSide: BorderSide.none),
                  ),
                  controller: controller,
                  style: _contentTextStyle,
                  maxLines: 1,
                  enabled: false,
                ),
              ),
              IconFont(IconNames.rili, size: 28.w)
            ],
          ),
        ),
        background: background);
  }

  ///构建带标题的文本框
  Widget _buildRow(BuildContext context, String title, Widget content,
      {Color? background}) {
    return Row(children: [
      Expanded(
          child: Text(title,
              style:
                  TextStyle(fontSize: 26.sp, color: const Color(0xFF333333)))),
      Expanded(
        flex: 4,
        child: Container(
          height: 62.h,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
              color: background ?? Colors.white,
              borderRadius: BorderRadius.circular(8.w),
              border: Border.all(color: const Color(0xFFE1E1E1), width: 2.w)),
          child: content,
        ),
      )
    ]);
  }

  ///构建底部分割线和确定按钮
  _buildBottom(BuildContext context) {
    return Column(
      children: [
        divider,
        HaloContainer(
          height: 100.h,
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          padding: dialogPadding,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                if (_batchNo?.isEmpty != false) {
                  HaloToast.show(context, msg: "请输入批次号");
                }
                widget.goods.batchNo = _batchNo;
                if ((widget.goods.protectDays ?? 0) > 0) {
                  // if (_produceDate == null || _expireDate == null) {
                  //   HaloToast.show(context, msg: "请选择生产日期");
                  //   return;
                  // }
                  //日期处理
                  widget.goods.produceDate = formatDateStringToUtc(
                      DateUtil.getFilterDate(_produceDate, isBegin: true));
                  widget.goods.expireDate = formatDateStringToUtc(
                      DateUtil.getFilterDate(_expireDate, isBegin: true));
                }
                widget.goods.serialNoList.clear();
                Navigator.pop(context, widget.goods);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF4679FC),
                  borderRadius: BorderRadius.circular(8.w),
                ),
                alignment: Alignment.center,
                width: 194.w,
                height: 66.h,
                child: HaloPosLabel(
                  "确定",
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  textStyle: TextStyle(
                      color: Colors.white,
                      decoration: TextDecoration.none,
                      fontSize: 32.sp,
                      fontWeight: FontWeight.bold),
                ),
              ),
            )
          ],
        )
      ],
    );
  }
}
