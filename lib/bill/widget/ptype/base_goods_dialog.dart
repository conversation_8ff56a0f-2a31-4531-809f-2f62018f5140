import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_search.dart';

import '../../../iconfont/icon_font.dart';

///商品选择、商品序列号选择、商品批次号选择弹窗的基类
///包含一个白色圆角背景，标题栏和关闭按钮
abstract class BaseGoodsDialogState<T extends StatefulWidget> extends State<T> {
  ///分割线
  final divider = Divider(height: 2.h, color: const Color(0xFFD0D0D0));

  ///宽
  double get width;

  ///高
  double get height;

  ///标题
  String get title;

  ///左右内边距
  EdgeInsets get dialogPadding => EdgeInsets.only(left: 20.w, right: 28.w);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        onCloseButtonClick(context);
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: Colors.transparent,
        body: Material(
          color: Colors.transparent,
          child: Container(
            alignment: Alignment.center,
            child: HaloContainer(
              width: width,
              height: height,
              color: Colors.white,
              direction: Axis.vertical,
              borderRadius: BorderRadius.circular(6.w),
              children: [
                //标题
                buildTitle(context),
                //内容，如列表和搜索框
                Expanded(child: buildContent(context)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ///标题栏和关闭按钮
  Widget buildTitle(BuildContext context) {
    return Column(
      children: [
        HaloContainer(
          height: 72.h,
          padding: const EdgeInsets.only(left: 20, right: 0),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: const Color(0xFF333333),
                fontSize: 26.sp,
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: Container(
                width: 80.w,
                alignment: Alignment.center,
                child: IconFont(IconNames.close, size: 30.w),
              ),
              onTap: () => onCloseButtonClick(context),
            ),
          ],
        ),
        //分割线
        divider,
      ],
    );
  }

  ///构建内容
  Widget buildContent(BuildContext context) => Container();

  ///当点击了关闭按钮
  void onCloseButtonClick(BuildContext context) => Navigator.pop(context);
}

///列表和列表表头的mixin
mixin ListAndListTitleMixin<T extends StatefulWidget>
    on BaseGoodsDialogState<T> {
  ///列表标题文字样式
  final TextStyle listTitleTextStyle = TextStyle(
    fontSize: 22.sp,
    color: const Color(0XFF666666),
  );

  ///表头各列标题
  List<String> get listTitles => [];

  ///列表各列比例
  List<int> get listColumnFlex => [];

  ///list长度
  int get itemCount;

  ///列表的内边距，通常情况下等于dialog的内边距
  EdgeInsets get listPadding => dialogPadding;

  @override
  Widget buildContent(BuildContext context) {
    return Column(
      children: [
        //表头
        buildListTitleRow(context),
        //列表
        Expanded(child: buildList()),
      ],
    );
  }

  ///构建列表表头行
  Widget buildListTitleRow(BuildContext context) {
    assert(listTitles.length == listColumnFlex.length);
    //表头为空，不展示表头
    if (listTitles.isEmpty) {
      return Container();
    }
    List<Widget> titles = [];
    //构建表头列
    for (int i = 0; i < listTitles.length; i++) {
      titles.add(buildListTitleColumn(i, listTitles[i]));
    }
    return Container(
      height: 60.h,
      padding: listPadding,
      color: const Color(0XFFF4F4F4),
      child: Row(children: titles),
    );
  }

  ///构建表头各列
  Widget buildListTitleColumn(int index, String title) {
    return Expanded(
      flex: listColumnFlex[index],
      child: Text(title, style: listTitleTextStyle, maxLines: 1),
    );
  }

  ///构建列表
  Widget buildList() {
    return ListView.builder(
      shrinkWrap: true,
      itemBuilder: buildListItem,
      itemCount: itemCount,
    );
  }

  Widget buildListItem(BuildContext context, int index);
}

///处理带搜索框的列表
mixin SearchMixin<T extends StatefulWidget> on ListAndListTitleMixin<T> {
  ///用来收起软键盘
  final FocusNode focusNode = FocusNode();

  final int maxLength = 100;

  ///搜索栏提示文字
  String get searchHint;

  ///搜索栏高度
  double get searchBarHeight;

  ///搜索栏背景颜色
  Color get searchBarColor => Colors.white;

  ///搜索关键字
  String filterValue = "";

  @override
  Widget buildContent(BuildContext context) {
    return Column(
      children: [
        //搜索框
        buildSearch(context),
        //list
        Expanded(child: super.buildContent(context)),
      ],
    );
  }

  ///搜索框
  Widget buildSearch(BuildContext context) {
    return Container(
      color: Colors.white,
      child: HaloSearch(
        value: filterValue,
        textInputAction: TextInputAction.done,
        padding: dialogPadding,
        inputBackGround: searchBarColor,
        inputHintTextColor: const Color(0xFF979CA2),
        isShowClear: true,
        textFontSize: 26.sp,
        isShowScan: false,
        isShowFilter: false,
        hintText: searchHint,
        focusNode: focusNode,
        onSubmitted: (text) async {
          if (text.length > maxLength) {
            HaloToast.show(context, msg: "允许输入的最大长度为$maxLength");
            return;
          }
          filterValue = text.trim();
          onSearched(context);
        },
      ),
    );
  }

  ///点击了关闭按钮
  @override
  void onCloseButtonClick(BuildContext context) {
    //关闭页面时先关闭软键盘
    focusNode.unfocus();
    Navigator.pop(context);
  }

  ///当点击了搜索
  void onSearched(BuildContext context);
}

///顶部的商品信息
mixin GoodsInfoMixin<T extends StatefulWidget> on BaseGoodsDialogState<T> {
  ///商品图片
  String get goodsPicUrl;

  ///商品名称
  String get goodsName;

  ///商品条码
  String get goodsBarcode;

  ///商品条码
  String get goodsQty;

  @override
  Widget buildContent(BuildContext context) {
    return Column(
      children: [
        //商品信息
        buildGoodsInfoWidget(),
        //列表
        Expanded(child: super.buildContent(context)),
      ],
    );
  }

  ///商品信息
  Widget buildGoodsInfoWidget() {
    return Container(
      height: 144.h,
      padding: dialogPadding,
      child: Row(
        children: [
          //商品图片
          SizedBox(height: 102.w, width: 102.w, child: HaloImage(goodsPicUrl)),
          Expanded(
            child: HaloContainer(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              direction: Axis.vertical,
              padding: EdgeInsets.only(left: 16.w),
              children: [
                //商品名称
                Text(
                  goodsName,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: const Color(0xFF333333),
                    fontSize: 24.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                //商品条码
                Padding(
                  padding: EdgeInsets.only(top: 15.h),
                  child: Text(
                    "条码：$goodsBarcode",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 22.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 0.h),
                  child: Text(
                    "数量：$goodsQty",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 22.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
