import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../../common/standard.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/permission_dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../tool/goods_tool.dart';
import 'base_goods_dialog.dart';
import 'goods_bind_batch_page.dart';

///多个商品选择批次
class MultiGoodsBindBatchPage extends StatefulWidget {
  final List<GoodsDetailDto> goodsList;

  const MultiGoodsBindBatchPage(this.goodsList, {Key? key}) : super(key: key);

  @override
  State<MultiGoodsBindBatchPage> createState() =>
      _MultiGoodsBindBatchPageState();
}

class _MultiGoodsBindBatchPageState<T extends MultiGoodsBindBatchPage>
    extends BaseGoodsDialogState<T> with ListAndListTitleMixin {
  final List<GoodsDetailDto> _copyList = [];

  final List<GoodsDetailDto> _goodsList = [];

  @override
  void initState() {
    super.initState();
    widget.goodsList
        .where((element) => element.batchenabled && !element.comboRow)
        .forEach((element) {
      _goodsList.add(element);
      _copyList.add(GoodsDetailDto.fromMap(element.toJson()));
    });
  }

  @override
  double get height => 838.h;

  @override
  double get width => 1200.w;

  @override
  int get itemCount => _copyList.length;

  @override
  String get title => "选择批次号";

  ///表头各列标题
  @override
  List<String> get listTitles => ["商品名称", "批次号", "生产日期", "到期日期", "批次库存"];

  ///列表各列比例
  @override
  List<int> get listColumnFlex => [3, 3, 2, 2, 1];

  final PermissionDto permissionDto = SpTool.getPermission();

  @override
  Widget buildContent(BuildContext context) {
    return Column(children: [
      Expanded(child: super.buildContent(context)),
      //底部分割线及确定按钮
      _buildBottom(context)
    ]);
  }

  @override
  Widget buildListItem(BuildContext context, int index) {
    GoodsDetailDto goods = _copyList[index];
    return Column(
      children: [
        HaloContainer(height: 70.h, padding: listPadding, children: [
          Expanded(
              flex: listColumnFlex[0], child: buildText(goods.pFullName ?? "")),
          Expanded(flex: listColumnFlex[1], child: _buildBatchNoText(goods)),
          Expanded(
              flex: listColumnFlex[2],
              child: buildText(parseDate(goods.produceDate ?? ""))),
          Expanded(
              flex: listColumnFlex[3],
              child: buildText(parseDate(goods.expireDate ?? ""))),
          Expanded(
              flex: listColumnFlex[4],
              child: buildText((goods.batchInventoryQty ?? 0).toString())),
        ]),
        //分割线
        divider
      ],
    );
  }

  ///批次号选择
  Widget _buildBatchNoText(GoodsDetailDto goods) {
    Widget batchNoText;
    if (!GoodsTool.isGoodsBindWithBatch(goods)) {
      batchNoText = Text("请选择批次",
          style: textStyle.copyWith(color: const Color(0xFFD0D0D0)));
    } else {
      batchNoText = buildBatchNoText(
          costMode: goods.costMode,
          batchNo: goods.batchNo,
          batchPrice: goods.batchPrice,
          batchNoMinWidth: 60.w,
          showBatchCost:
              permissionDto.shopsalesalesettingviewBatchCost ?? false);
    }
    return buildSelectionContainer(batchNoText, () {
      //点击跳转到选择商品批次号弹窗
      showDialog(
          context: context,
          builder: (context) => GoodsBindBatchPage(goods: goods)).then((value) {
        if (value != null) {
          setState(() {});
        }
      });
    });
  }

  @override
  void onCloseButtonClick(BuildContext context) {
    DialogUtil.showConfirmDialog(
      context,
      title: "提示",
      content: "还未设置任何批次号，是否退出?",
      actionLabels: ["取消", "确定"],
      confirmCallback: () => Navigator.pop(context),
    );
  }

  ///构建底部分割线和确定按钮
  Widget _buildBottom(BuildContext context) {
    return Column(
      children: [
        divider,
        HaloContainer(
          height: 100.h,
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          padding: dialogPadding,
          children: [
            buildButton(context, "取消",
                onTap: () => onCloseButtonClick(context)),
            SizedBox(width: 30.w),
            buildButton(
              context,
              "确定",
              textColor: Colors.white,
              background: const Color(0xFF4679FC),
              borderColor: null,
              onTap: () {
                if (_copyList
                    .any((goods) => !GoodsTool.isGoodsBindWithBatch(goods))) {
                  DialogUtil.showConfirmDialog(
                    context,
                    title: "提示",
                    content: "还未设置完批次号，是否保存?",
                    actionLabels: ["取消", "确定"],
                    confirmCallback: onConfirm,
                  );
                } else {
                  onConfirm();
                }
              },
            ),
          ],
        )
      ],
    );
  }

  ///确认
  void onConfirm() {
    for (int i = 0; i < _goodsList.length; i++) {
      final goods = _goodsList[i];
      final copy = _copyList[i];
      //将该商品关联上批次号
      goods.batchId = copy.batchId;
      //批次成本
      goods.batchPrice = copy.batchPrice;
      //解决个别计价商品，选择批次后核算不通过的问题
      goods.costId = copy.costId;
      goods.batchNo = copy.batchNo;
      goods.produceDate = copy.produceDate ?? "";
      goods.expireDate = copy.expireDate;
      goods.protectDays = copy.protectDays;
      goods.batchInventoryQty = copy.batchInventoryQty;
      goods.unitQty = copy.unitQty;
      goods.serialNoList = copy.serialNoList;
    }
    Navigator.pop(context, widget.goodsList);
  }
}

///底部按钮
Widget buildButton(
  BuildContext context,
  String content, {
  Color textColor = const Color(0xFF333333),
  Color background = Colors.white,
  Color? borderColor = const Color(0xFF999999),
  VoidCallback? onTap,
}) {
  return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 66.h,
        width: 196.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: background,
          borderRadius: BorderRadius.circular(4.w),
          border: borderColor?.let(
              (borderColor) => Border.all(color: borderColor, width: 2.w)),
        ),
        child:
            Text(content, style: TextStyle(color: textColor, fontSize: 26.sp)),
      ));
}

///构建点击选择框容器
Widget buildSelectionContainer(Widget child, VoidCallback onTap) {
  return GestureDetector(
    behavior: HitTestBehavior.opaque,
    onTap: onTap,
    child: Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.w),
          border: Border.all(color: const Color(0xFFD0D0D0))),
      child: child,
    ),
  );
}

///套餐选择批次
class ComboBindBatchPage extends MultiGoodsBindBatchPage {
  final GoodsDetailDto combo;

  const ComboBindBatchPage(List<GoodsDetailDto> goodsList, this.combo,
      {Key? key})
      : super(goodsList, key: key);

  @override
  State<ComboBindBatchPage> createState() => _ComboBindBatchPageState();
}

class _ComboBindBatchPageState
    extends _MultiGoodsBindBatchPageState<ComboBindBatchPage>
    with GoodsInfoMixin {
  @override
  String get goodsQty => widget.combo.unitQty.toString();

  @override
  String get goodsBarcode => widget.combo.fullbarcode;

  @override
  String get goodsName => widget.combo.pFullName ?? "";

  @override
  String get goodsPicUrl => widget.combo.picUrl ?? "";
}
