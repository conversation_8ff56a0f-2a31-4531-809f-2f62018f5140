import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../bill/entity/operation_bill_response.dart';
import '../../../common/string_res.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/halo_label.dart';

import '../../widgets/base/halo_base_stateful_widget.dart';

/// Copyright (C), 2019-2020, wsgjp.com
/// FileName: exception_item_widget
/// Author: tanglan
/// Date: 2020/10/19 14:42
/// Description:

class ExceptionItemWidget extends HaloBaseStatefulWidget {
  final String title; //一级标题
  final String content; //折叠内容，自动构建换行
  final List<RelationBillListBean> errorList;

  const ExceptionItemWidget(this.title, this.content,
      {Key? key, required this.errorList})
      : super(key: key);

  @override
  HaloBaseStatefulWidgetState<HaloBaseStatefulWidget> createState() =>
      _ExceptionItemWidgetState();
}

class _ExceptionItemWidgetState
    extends HaloBaseStatefulWidgetState<ExceptionItemWidget> {
  bool expand = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        bottom: ScreenUtil().setWidth(20.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Text(
                  widget.title,
                  style: TextStyle(
                      color: ColorUtil.stringColor("#323232"),
                      fontSize: ScreenUtil().setSp(28),
                      fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          Visibility(
              visible: StringUtil.isNotEmpty(widget.content),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    expand = !expand;
                  });
                },
                child: Container(
                  margin: EdgeInsets.only(
                      left: ScreenUtil().setWidth(48),
                      top: ScreenUtil().setWidth(10)),
                  child: Text(
                      expand
                          ? StringRes.BILL_PACK_UP.getText(context)
                          : StringRes.BILL_VIEW_ALL.getText(context),
                      style: TextStyle(
                          color: Theme.of(context).colorScheme.secondary,
                          fontSize: ScreenUtil().setSp(28))),
                ),
              )),
          Visibility(
            visible: expand,
            child: Container(
              color: ColorUtil.stringColor("#F6F5F8"),
              width: double.infinity,
              margin: EdgeInsets.only(
                  left: ScreenUtil().setWidth(30),
                  top: ScreenUtil().setWidth(5)),
              padding: const EdgeInsets.all(12),
              child: _buildContentView(),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildContentView() {
    // if (widget.errorList != null && widget.errorList?.length == 0) {
    if (widget.errorList == null || widget.errorList.isEmpty) {
      return Text(
        widget.content,
        style: TextStyle(
            color: ColorUtil.stringColor("#646464"),
            fontSize: ScreenUtil().setSp(24)),
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _buildExpandContent(),
    );
  }

  _buildExpandContent() {
    List<Widget> detailWidget = [];
    for (var element in widget.errorList) {
      if (StringUtil.isNotEmpty(element.vchtypeName)) {
        detailWidget.add(HaloLabel(
          element.vchtypeName,
          textStyle: TextStyle(
              color: ColorUtil.stringColor("#646464"),
              fontSize: ScreenUtil().setSp(24)),
        ));
      }
      if (StringUtil.isNotEmpty(element.number)) {
        detailWidget.add(HaloLabel(element.number,
            textStyle: TextStyle(
                color: ColorUtil.stringColor("#646464"),
                fontSize: ScreenUtil().setSp(24))));
      }
      //费用分摊需求 - 目前查看仅支持 费用单
      // if (element.showDetail &&
      //     BillHelper.isAccountBillType(BillTypeString[element.vchtypeEnum])) {
      //   detailWidget.add(Row(
      //     mainAxisAlignment: MainAxisAlignment.end,
      //     children: [
      //       GestureDetector(
      //         onTap: () {
      //           //收入费用查询
      //           NavigatorHelper.navigateTo(
      //               context, BillRouter.INCOME_BILL_QUERY,
      //               arguments: {"filterValue": element.number});
      //         },
      //         child: Text("查看单据",
      //             style: TextStyle(
      //                 color: ColorRes.selectorBlueText,
      //                 fontSize: ScreenUtil().setSp(24))),
      //       )
      //     ],
      //   ));
      // }

      //detailWidget.add(Text(createRelationMessage(details)))
    }
    return detailWidget;
  }
}
