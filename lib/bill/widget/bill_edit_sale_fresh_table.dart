import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

import '../../bill/entity/base_table_content.dart';
import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/tool/bill_tool.dart';
import '../../bill/widget/mixin/promotion_tips_mixin.dart';
import '../../bill/widget/ptype/ptype_detail_page.dart';
import '../../common/style/app_colors.dart';
import '../../common/tool/dialog_util.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/widget/ptype_note_richtext.dart';
import '../../enum/bill_type.dart';
import '../../iconfont/icon_font.dart';
import '../../widgets/dotted_line.dart';
import '../../widgets/halo_pos_label.dart';
import '../model/ptype_model.dart';
import '../tool/bill_goods_util.dart';
import '../tool/goods_tool.dart';
import '../tool/index_scroll_controller.dart';
import '../tool/promotion/manual_price.dart';
import '../tool/promotion/price.dart';
import '../tool/promotion/promotion.dart';

/// 创建时间：8/11/21
/// 作者：xiaotiaochong
/// 描述：开单页面商品列表(生鲜)//todo 两个开单列表可以提取公共数量计算mix
class BillEditSaleFreshTable extends StatefulWidget
    with IndexScrollWidgetMixin {
  ///单据
  final GoodsBillDto goodsBillDto;

  ///商品发生变更时的回调
  final Function(GoodsBillDto goodsBill, bool resetFocus) dataChangeCallBack;

  ///选择促销赠品的回调
  final ValueSetter<String>? onSelectPromotionGift;

  ///单据类型，销售或退货
  final BillType billType;

  @override
  final IndexScrollerController indexScrollerController;

  const BillEditSaleFreshTable({
    Key? key,
    required this.billType,
    required this.indexScrollerController,
    required this.dataChangeCallBack,
    required this.goodsBillDto,
    this.onSelectPromotionGift,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _BillEditSaleFreshTableState();
  }
}

class _BillEditSaleFreshTableState extends State<BillEditSaleFreshTable>
    with PromotionTipsMixin, IndexScrollStateMixin {
  late List<GoodsDetailDto> dataSource;
  BaseTableContentConfig baseTableContentConfig = BaseTableContentConfig();
  late GoodsBillDto goodsBillDto;
  ScrollController columnController = ScrollController();

  final Map<GoodsDetailDto, int> _indexMap = {};

  GoodsDetailDto? currentGoods;

  @override
  void initState() {
    super.initState();
    goodsBillDto = widget.goodsBillDto;
    dataSource = BillTool.getGoodsDetails(goodsBillDto, widget.billType);
    baseTableContentConfig.rowHeight = 110;
    _buildGoodsIndex();
  }

  @override
  void didUpdateWidget(covariant BillEditSaleFreshTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    goodsBillDto = widget.goodsBillDto;
    dataSource = BillTool.getGoodsDetails(goodsBillDto, widget.billType);
    _buildGoodsIndex();
  }

  //region view
  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.topLeft,
      child: ListView.builder(
        itemBuilder: (BuildContext context, int index) {
          return _buildItem(dataSource[index], index);
        },
        itemCount: dataSource.length,
        controller: columnController,
        // itemScrollController: itemScrollController,
        // itemPositionsListener: itemPositionsListener,
      ),
    );
  }

  ///计算商品index
  void _buildGoodsIndex() {
    _indexMap.clear();
    int i = 0;
    for (var goods in dataSource) {
      if (!GoodsTool.isComboDetail(goods)) {
        _indexMap[goods] = ++i;
      }
    }
  }

  Widget _buildItem(GoodsDetailDto goodsDetailDto, int index) {
    Widget result;
    if (GoodsTool.isComboDetail(goodsDetailDto)) {
      result = _buildComboDetailItem(goodsDetailDto, index);
    } else {
      int goodsDetailIndex = _indexMap[goodsDetailDto] ?? 0;
      result = _buildNormalItem(goodsDetailDto, index, goodsDetailIndex);
    }
    if (currentGoods == goodsDetailDto) {
      result = Container(color: const Color(0x75C0D7FF), child: result);
    }
    return result;
  }

  _buildComboDetailItem(GoodsDetailDto goodsDetailDto, int index) {
    BaseTableContentConfig config = BaseTableContentConfig();
    config.rowHeight = 30;
    return Column(
      children: [
        Visibility(
          visible: goodsDetailDto.visible,
          child: GestureDetector(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 12.w),
              padding: EdgeInsets.symmetric(vertical: 10.w),
              constraints: BoxConstraints(minHeight: config.rowHeight.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 11,
                        child: Padding(
                          padding: EdgeInsets.only(left: 40.w),
                          child: PtypeNoteRichText(
                            goodsDetailDto: goodsDetailDto,
                            showProp: true,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 190.w,
                        child: _buildCount(goodsDetailDto),
                      ),
                      const Expanded(flex: 6, child: SizedBox()),
                    ],
                  ),
                ],
              ),
            ),
            onTap: () {
              doShowPtypeDetail(index);
            },
          ),
        ),
        if (index == dataSource.length - 1)
          DottedLine(
            axis: Axis.horizontal,
            strokeWidth: 0.4.w,
            color: AppColors.describeFontColor,
            dashPattern: const [3, 3],
          ),
      ],
    );
  }

  _buildNormalItem(
    GoodsDetailDto goodsDetailDto,
    int index,
    int goodsDetailIndex,
  ) {
    BaseTableContentConfig config = BaseTableContentConfig();
    config.rowHeight = baseTableContentConfig.rowHeight;

    List<PromotionHints> goodsHints = goodsDetailDto.promotionHints ?? [];
    bool showDeleteButton =
        !GoodsTool.isComboDetail(goodsDetailDto) &&
        !BillGoodsUtil.isPromotionGift(goodsDetailDto, true);
    bool showEditGiftButton = BillTool.showGiftEditButton(
      goodsDetailDto,
      widget.billType,
    );

    Widget _buildUserCode() {
      if (goodsDetailDto.pUserCode?.isNotEmpty == true) {
        return Text(
          "商品编号：${goodsDetailDto.pUserCode}",
          style: TextStyle(
            fontSize: AppPosSize.contentFontSize.sp,
            color: AppColors.describeFontColor,
          ),
        );
      }
      return const SizedBox();
    }

    return GestureDetector(
      child: Column(
        children: [
          if (index != 0)
            DottedLine(
              axis: Axis.horizontal,
              strokeWidth: 0.4.w,
              color: AppColors.describeFontColor,
              dashPattern: const [3, 3],
            ),
          buildPromotionHints(goodsHints, goodsDetailDto, config),
          Container(
            margin: EdgeInsets.only(left: 12.w, right: 12.w, bottom: 6.h),
            padding: EdgeInsets.symmetric(vertical: 10.w),
            constraints: BoxConstraints(minHeight: config.rowHeight.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "$goodsDetailIndex.",
                            style: TextStyle(
                              fontSize: AppPosSize.secondaryTitleFontSize.sp,
                              color: AppColors.normalFontColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Flexible(
                            fit: FlexFit.loose,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                PtypeNoteRichText(
                                  goodsDetailDto: goodsDetailDto,
                                  showProp: true,
                                  textStyle: TextStyle(
                                    fontSize:
                                        AppPosSize.secondaryTitleFontSize.sp,
                                    color: AppColors.normalFontColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (goodsDetailDto.comboRow)
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              child: Container(
                                alignment: Alignment.center,
                                child: Text(
                                  goodsDetailDto.visible ? "收起 >" : "展开 >",
                                  style: TextStyle(
                                    color: AppColors.accentColor,
                                    fontSize: AppPosSize.contentFontSize.sp,
                                  ),
                                ),
                              ),
                              onTap: () {
                                _showComboRowDetail(goodsDetailDto);
                              },
                            ),
                        ],
                      ),
                    ),
                    if (showDeleteButton)
                      GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        child: Container(
                          width: 40.w,
                          height: 40.h,
                          alignment: Alignment.center,
                          child: IconFont(IconNames.shanchu_1, size: 26.w),
                        ),
                        onTap: () {
                          _countChange(goodsDetailDto, 0);
                        },
                      ),
                    if (showEditGiftButton)
                      GestureDetector(
                        child: Container(
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.black, width: 1.w),
                            color: Colors.white,
                            borderRadius: const BorderRadius.all(
                              Radius.circular(3),
                            ),
                          ),
                          child: Icon(
                            Icons.edit,
                            size: 24.w,
                            color: Colors.black,
                          ),
                        ),
                        onTap: () {
                          widget.onSelectPromotionGift?.call(
                            goodsDetailDto.promotionId,
                          );
                        },
                      ),
                  ],
                ),
                Row(
                  children: [
                    _buildUserCode(),
                  ],
                ),
                Row(
                  children: [
                    Expanded(flex: 11, child: _buildPrice(goodsDetailDto)),
                    SizedBox(width: 190.w, child: _buildCount(goodsDetailDto)),
                    Expanded(
                      flex: 6,
                      child: HaloPosLabel(
                        "¥${goodsDetailDto.currencyDisedTaxedTotal}",
                        textStyle: TextStyle(
                          fontSize: AppPosSize.totalFontSize.sp,
                          color: AppColors.normalFontColor,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (index == dataSource.length - 1)
            DottedLine(
              axis: Axis.horizontal,
              strokeWidth: 0.4.w,
              color: AppColors.describeFontColor,
              dashPattern: const [3, 3],
            ),
        ],
      ),
      onTap: () {
        doShowPtypeDetail(index);
      },
    );
  }

  _showComboRowDetail(GoodsDetailDto goodsDetailDto) {
    setState(() {
      BillTool.showComboRowDetail(goodsDetailDto, dataSource);
    });
  }

  _buildPrice(GoodsDetailDto goodsDetailDto) {
    String price = "";
    if (SpTool.getSetting().openFreshPriceVisible) {
      price = "/${goodsDetailDto.currencyPrice}";
    }

    String unity = "";
    if (goodsDetailDto.unitName != "") {
      unity = goodsDetailDto.unitName;
    }
    return Row(
      children: [
        Container(
          constraints: BoxConstraints(maxWidth: 120.w),
          child: HaloPosLabel(
            "¥${goodsDetailDto.currencyDisedTaxedPrice}",
            textStyle: TextStyle(
              color: AppColors.normalFontColor,
              fontSize: 20.sp,
            ),
            // )
          ),
        ),
        Flexible(
          fit: FlexFit.loose,
          child: Row(
            children: [
              Flexible(
                fit: FlexFit.loose,
                child: Container(
                  constraints: BoxConstraints(minWidth: 20.w),
                  child: HaloPosLabel(
                    price,
                    textStyle: TextStyle(
                      color: AppColors.describeFontColor,
                      fontSize: AppPosSize.totalFontSize.sp,
                    ),
                  ),
                ),
              ),
              Container(
                constraints: BoxConstraints(maxWidth: 60.w, minWidth: 30.w),
                padding: EdgeInsets.only(bottom: 4.h),
                child: HaloPosLabel(
                  unity,
                  textStyle: TextStyle(
                    color: AppColors.describeFontColor,
                    fontSize: AppPosSize.totalFontSize.sp,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  ///数量
  _buildCount(GoodsDetailDto goodsDetailDto) {
    //套餐明细商品和赠品无法调整数量
    if (!BillTool.checkPtypeDetailEnable(goodsDetailDto)) {
      return Container(
        alignment: Alignment.center,
        child: HaloPosLabel(
          goodsDetailDto.unitQty.toString(),
          textStyle: const TextStyle(color: Color(0xFFDB0E0E)),
        ),
      );
    } else {
      num countMax = SpTool.getSystemConfig().sysGlobalDecimalMax;
      if (widget.billType == BillType.SaleBackBill) {
        countMax = goodsDetailDto.maxQty! - goodsDetailDto.completedQty!;
      }
      FocusNode focusNode = FocusNode();
      focusNode.addListener(() {
        if (!focusNode.hasFocus) {
          if (goodsDetailDto.unitQty <= 0) {
            _doDelete(goodsDetailDto, delete: true);
          }
        }
      });
      return Container(
        alignment: Alignment.center,
        child: HaloCountStepper(
          focusNode: focusNode,
          decimalCount: SpTool.getSystemConfig().sysDigitalQty,
          notifier: HaloCountStepperNotifier(
            max: (countMax < 0 ? 0 : countMax).toDouble(),
            defaultValue: goodsDetailDto.unitQty,
          ),
          enable: true,
          inputBackgroundColor: Colors.white,
          textColor: Colors.black,
          inputFontWeight: FontWeight.w600,
          inputMinWidth: 90.w,
          inputMaxWidth: 90.w,
          inputFontSize: 22.sp,
          borderRadius: BorderRadius.circular(6.w),
          border: Border.all(color: AppColors.txtBorderColor, width: 1),
          textColorDiy: true,
          addIcon: Container(
            height: 40.w,
            width: 40.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.w),
              border: Border.all(color: AppColors.txtBorderColor, width: 1),
            ),
            child: Text(
              "+",
              style: TextStyle(color: const Color(0xFF606060), fontSize: 24.sp),
            ),
          ),
          subIcon: Container(
            height: 40.w,
            width: 40.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.w),
              border: Border.all(color: AppColors.txtBorderColor, width: 1),
            ),
            child: Text(
              "-",
              style: TextStyle(color: const Color(0xFF606060), fontSize: 30.sp),
            ),
          ),
          onAfterSubClick: (value) {
            _countChange(goodsDetailDto, value, delete: true);
          },
          onAfterAddClick: (value) {
            _countChange(goodsDetailDto, value, delete: false);
          },
          onChanged: (value) {
            _countChange(goodsDetailDto, value, delete: false, reset: false);
          },
          onCompleted: (value) {
            _countChange(goodsDetailDto, value, reset: true);
          },
        ),
      );
    }
  }

  //endregion

  //region action
  //编辑促销赠品
  _editPromotionGift(GoodsDetailDto goodsDetailDto) {
    //todo 选择赠品
    // PromotionTool.editPromotionGift(context,
    //     goodsDetailDto: goodsDetailDto,
    //     goodsBillDto: goodsBillDto, callback: (value) {
    //   setState(() {});
    //   widget.dataChangeCallBack(goodsBillDto, true);
    // });
  }

  ///获取单个商品最大数量
  num getGoodsMaxCount(GoodsDetailDto goods) =>
      SpTool.getSystemConfig().sysGlobalDecimalMax;

  doShowPtypeDetail(int index) {
    GoodsDetailDto goods = dataSource[index];
    HaloDialog(
      context,
      dismissOnTouchOutside: true,
      barrierColor: Colors.transparent,
      child: PtypeDetailPage(
        goods,
        goodsBillDto,
        billType: widget.billType,
        maxCount: getGoodsMaxCount(goods),
        enable: BillTool.checkPtypeDetailEnable(goods),
        valueChanged: (newGoods, editRetailPrice) {
          //零售价发生了改变
          if (editRetailPrice) {
            newGoods.memo = "手动修改零售价";
            DialogUtil.showConfirmDialog(
              context,
              content: "是否想修改后的零售价同步到零售店铺价格本？",
              actionLabels: ["取消", "确定"],
              confirmCallback:
                  () => PtypeModel.addOrUpdateOtypePrice(context, newGoods),
            );
          }
          dataSource[index] = newGoods;
          BillTool.setGoodsDetails(goodsBillDto, widget.billType, dataSource);
          _buildGoodsIndex();
          if (newGoods.unitQty == 0) {
            _doDelete(newGoods);
          } else {
            // if (value.comboRow) {
            //   BillComboHelper.syncGiftComboItemAndComboDetail(
            //       widget.billType, dataSource, dataSource[index]);
            //   BillComboPriceComputeHelper(widget.billType)
            //       .recalculateCombo(dataSource);
            // }
            //手工改价商品，重新计算手工改价优惠
            if ((goods.manualPrice || newGoods.manualPrice) &&
                !GoodsTool.isComboDetail(newGoods)) {
              ManualPriceUtil.setManualPrice(
                newGoods,
                comboDetails: GoodsTool.getComboDetailsFromGoodsList(
                  dataSource,
                  newGoods,
                ),
              );
            }
          }
          widget.dataChangeCallBack(goodsBillDto, true);
        },
      ),
    ).show();
    setState(() => currentGoods = goods);
  }

  _countChange(
    GoodsDetailDto goods,
    num value, {
    bool delete = true,
    bool reset = true,
  }) {
    //因为0会删除
    if (BillTool.snDetailRow(goods) &&
        value != 0 &&
        goods.serialNoList.length > value) {
      HaloToast.show(context, msg: "商品数量不能小于序列号数量");
      value = goods.serialNoList.length;
    }
    goods.unitQty = value;
    // //数量
    // BillPriceComputeHelper().priceCompute.onValueChange(goodsDetailDto,
    //     PtypePopValueChangeType.Qty, goodsDetailDto.unitQty.toString());
    if (!GoodsTool.isComboDetail(goods) && value > 0) {
      //手工改价商品，重新计算手工改价优惠
      if (goods.manualPrice) {
        ManualPriceUtil.onQtyChange(
          goods,
          value,
          comboDetails: GoodsTool.getComboDetailsFromGoodsList(
            dataSource,
            goods,
          ),
        );
      } else {
        GoodsQtyUtil.onQtyChange(goods, value);
      }
    }

    if (goods.unitQty <= 0) {
      // PromotionTool.afterClearGiftSetVipDiscount(
      //     goodsDetailDto, dataSource, goodsBillDto.giftCouponList);
      _doDelete(goods, delete: delete);
    } else {
      BillTool.setGoodsDetails(goodsBillDto, widget.billType, dataSource);
      widget.dataChangeCallBack(goodsBillDto, reset);
    }
  }

  _doDelete(GoodsDetailDto goodsDetailDto, {bool delete = true}) {
    if (delete && widget.billType != BillType.SaleBackBill) {
      BillTool.deleteGoods(goodsDetailDto, dataSource);
    }
    BillTool.setGoodsDetails(goodsBillDto, widget.billType, dataSource);
    widget.dataChangeCallBack(goodsBillDto, delete);
  }

  @override
  void scrollToIndex(int index) {
    if (columnController.hasClients) {
      double offset =
          MathUtil.multiplyDec(
            index,
            baseTableContentConfig.rowHeight.h,
          ).toDouble();
      columnController.animateTo(
        offset,
        duration: const Duration(seconds: 1),
        curve: Curves.easeIn,
      );
    }
  }

  //endregion
}
