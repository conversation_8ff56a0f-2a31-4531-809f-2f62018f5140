import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/iconfont/icon_font.dart';

import '../../../bill/widget/ptype/ptype_textfield.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../widgets/halo_pos_label.dart';

///按钮类型「减号、加号」
enum _ButtonType { SUB, ADD }

const double _inputHeight = 29.0; //输入框默认高度
const double _inputRadius = 0.0; //输入框圆角
const double _inputFontSize = 15.0; //输入框字体大小
const double _buttonSize = 29.0; //加减按钮大小
const double _buttonIconSize = 22.0; //加减按钮icon大小
const double _buttonSplashRadius = 20.0; //加减按钮点击波浪半径

const String _disableColorString = "#c5c5c5";
const String _lightTextColorString = "#2F2F2F";
const String _addColorString = "#2288FC";

///深色模式
const Color _darkTextColor = Colors.white; //文字颜色
const Color _darkInputBackgroundColor = Colors.black54; //背景色

///浅色模式#f8f8fb
const Color _lightTextColor = Color(0xFF2F2F2F); //文字颜色
const Color _lightInputBackgroundColor = Color(0xFFf8f8fb); //背景色

/// Copyright (C), 2020-2021, wsgjp.com
/// Time：2021-03-18
/// Author：yi.zhang
/// Description：加减器（支持编辑状态和只读状态）
class CountStepper extends StatefulWidget {
  final ValueChanged<String> valueChanged; //输入完成
  final bool isAllowNegative; //是否允许输入负数 ------------ 需要数字输入框支持
  final bool enable; //启用状态
  final bool inputEnabled; //是否允许手动输入
  final double inputHeight; //输入框高度
  final double inputFontSize; //输入框字体大小
  final FontWeight inputFontWeight; //输入框字体大小
  final Widget? subIcon; //减按钮自定义控件
  final double subIconSize;
  final Widget? addIcon; //加按钮自定义控件
  final double addIconSize;
  final Color? inputBackgroundColor; //输入框背景色
  final int decimalCount; //小数点保留位数
  final Color textColor; //自定义颜色
  final bool textColorDiy;
  final double inputMinWidth;
  final num? maxValue;

  ///最小输入框宽度
  // final double inputMaxWidth;

  ///最大输入框宽度
  final double buttonSize;

  ///pos添加
  final bool readOnly;

  final String defaultText;

  const CountStepper(
      {Key? key,
      this.maxValue,
      this.readOnly = false,
      this.defaultText = "",
      this.subIconSize = 0,
      this.addIconSize = 0,
      required this.valueChanged,
      this.isAllowNegative = false,
      this.enable = true,
      this.inputEnabled = true,
      this.inputHeight = _inputHeight,
      this.inputFontSize = _inputFontSize,
      this.inputFontWeight = FontWeight.w500,
      this.subIcon,
      this.addIcon,
      this.inputBackgroundColor,
      this.decimalCount = 0,
      this.textColor = Colors.black,
      this.textColorDiy = false,
      this.inputMinWidth = 43,
      this.buttonSize = _buttonSize})
      : super(key: key);

  @override
  State<CountStepper> createState() => _CountStepperState();
}

class _CountStepperState extends State<CountStepper> {
  final TextEditingController _inputController = TextEditingController();
  bool _subEnabled = true; //减按钮是否能点击
  bool _addEnabled = true; //加按钮是否能点击
  bool isDarkMode = false; //是否是黑色模式

  @override
  void initState() {
    super.initState();
    _init();
  }

  ///初始化数据及监听
  void _init() {
    _subEnabled = true;
    _addEnabled = true;
    _inputController.text = widget.defaultText;
  }

  @override
  void didUpdateWidget(oldWidget) {
    super.didUpdateWidget(oldWidget);
    _init();
  }

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      _buildButton(type: _ButtonType.SUB),
      Expanded(child: _buildInput()),
      _buildButton(type: _ButtonType.ADD)
    ]);
  }

  ///构造加减按钮
  Widget _buildButton({required _ButtonType type}) {
    return Visibility(
      visible: widget.enable && widget.maxValue != 0,
      child: Container(
        width: widget.buttonSize,
        height: widget.buttonSize,
        decoration: BoxDecoration(
          color: widget.subIcon != null && widget.addIcon != null
              ? Colors.transparent
              : _lightInputBackgroundColor,
          borderRadius: type == _ButtonType.SUB
              ? const BorderRadius.only(
                  topLeft: Radius.circular(4.0),
                  bottomLeft: Radius.circular(4.0))
              : const BorderRadius.only(
                  topRight: Radius.circular(4.0),
                  bottomRight: Radius.circular(4.0)),
        ),
        padding: const EdgeInsets.all(0),
        child: IconButton(
          padding: const EdgeInsets.all(0),
          splashRadius: (widget.subIcon != null && widget.addIcon != null)
              ? _buttonSplashRadius
              : 0.1,
          iconSize: _buttonIconSize,
          icon: type == _ButtonType.SUB
              ? (widget.subIcon ??
                  HaloIconFont(HaloIconNames.jiajianqijian,
                      size: widget.subIconSize.w,
                      color: _subEnabled
                          ? _lightTextColorString
                          : _disableColorString))
              : (widget.addIcon ??
                  HaloIconFont(
                    HaloIconNames.jiajianqijia,
                    size: widget.addIconSize.w,
                    color: _addEnabled ? _addColorString : _disableColorString,
                  )),
          onPressed: type == _ButtonType.SUB
              ? (_subEnabled ? _onSubPressed : null)
              : (_addEnabled ? _onAddPressed : null),
        ),
      ),
    );
  }

  ///构造输入框（编辑状态和只读状态）
  Widget _buildInput() {
    Color textColor = isDarkMode ? _darkTextColor : _lightTextColor;
    textColor = Colors.black;
    if (widget.textColorDiy) textColor = widget.textColor;
    return widget.enable && widget.maxValue != 0
        ? Stack(alignment: AlignmentDirectional.center, children: [
            Container(
              alignment: Alignment.center,
              height: widget.inputHeight,
              margin: const EdgeInsets.only(left: 2, right: 2),
              decoration: BoxDecoration(
                color: widget.inputBackgroundColor ??
                    (isDarkMode
                        ? _darkInputBackgroundColor
                        : _lightInputBackgroundColor),
                borderRadius: BorderRadius.circular(_inputRadius),
              ),
            ),
            PtypeTextField(
              contentPadding: const EdgeInsets.fromLTRB(5, 0, 5, 5),
              defaultText: _inputController.text,
              textColor: textColor,
              afterPoint: SpTool.getSystemConfig().sysDigitalQty,
              onChange: widget.valueChanged,
              maxValue: widget.maxValue ?? 0,
            ),
          ])
        : HaloPosLabel(
            _inputController.value.text,
            textAlign: TextAlign.right,
            textStyle: TextStyle(color: textColor),
          );
  }

  ///点击减按钮
  void _onSubPressed() {
    _clearFocus();
    num oldValue = (num.tryParse(_inputController.text) ?? 0);
    num value = oldValue - 1;
    if (value < 0) {
      value = 0;
    }
    if (oldValue != value) {
      setState(() {
        _inputController.text = value.toString();
        widget.valueChanged(_inputController.text);
      });
    }
  }

  ///点击加按钮
  void _onAddPressed() {
    _clearFocus();
    num oldValue = (num.tryParse(_inputController.text) ?? 0);
    num value = num.parse(_inputController.text) + 1;
    if (widget.maxValue != null && value > widget.maxValue!) {
      value = widget.maxValue!;
    }
    if (oldValue != value) {
      setState(() {
        _inputController.text = value.toString();
        widget.valueChanged(_inputController.text);
      });
    }
  }

  ///清除焦点
  void _clearFocus() {
    if (FocusScope.of(context).hasFocus) {
      FocusScope.of(context).unfocus();
    }
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }
}
