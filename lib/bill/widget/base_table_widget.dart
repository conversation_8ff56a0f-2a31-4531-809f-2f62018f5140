import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../bill/entity/base_table_content.dart';
import '../../../common/style/app_colors.dart';
import '../tool/index_scroll_controller.dart';

class BaseTableWidget extends StatefulWidget with IndexScrollWidgetMixin {
  //标题行
  final List<Widget> titleWidgetList;

  //列表
  final List<BaseTableContent> baseTableContentList;

  //列宽
  final List<ColumnWidth> columnWidthList;

  //排除点击事件的列
  final List<int>? excludeTapColumn;

  //行点击事件回调
  final Function(int index)? tapRowCallback;

  @override
  final IndexScrollerController indexScrollerController;

  BaseTableWidget({
    Key? key,
    required this.titleWidgetList,
    required this.columnWidthList,
    required this.baseTableContentList,
    required this.indexScrollerController,
    this.excludeTapColumn,
    this.tapRowCallback,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _BaseTableWidget();
  }
}

class _BaseTableWidget extends State<BaseTableWidget>
    with IndexScrollStateMixin {
  //region 变量
  ScrollController columnController = ScrollController();
  ScrollController firstRowController = ScrollController();
  ScrollController secondedRowController = ScrollController();

  Map<int, TableColumnWidth> columnWidthMap = {}; //列的宽度
  double totalWidth = 0; //总列宽
  //endregion

  //滚动行 index
  int index = 0;

  @override
  void initState() {
    super.initState();

    ///滚动监听
    scrollControllerListener();

    ///列宽度
    columnWidthAdaptation();
  }

  @override
  void didUpdateWidget(oldWidget) {
    super.didUpdateWidget(oldWidget);
    columnWidthAdaptation();
  }

  ///滚动监听
  void scrollControllerListener() {
    //监听第一行变动
    firstRowController.addListener(() {
      if (firstRowController.offset != secondedRowController.offset) {
        secondedRowController.jumpTo(firstRowController.offset);
      }
    });
    //监听第二行变动
    secondedRowController.addListener(() {
      if (firstRowController.offset != secondedRowController.offset) {
        firstRowController.jumpTo(secondedRowController.offset);
      }
    });
  }

  ///列宽度
  void columnWidthAdaptation() {
    totalWidth = 0;
    for (int i = 0; i < widget.columnWidthList.length; i++) {
      ColumnWidth columnWidth = widget.columnWidthList[i];
      if (columnWidth.isShow) {
        ///设置列宽
        columnWidthMap[i] = FixedColumnWidth(columnWidth.width);

        ///计算总宽度
        totalWidth = totalWidth + columnWidth.width;
      } else {
        columnWidthMap[i] = FixedColumnWidth(0.w);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MediaQuery.removePadding(
        removeTop: true,
        context: context,
        child: NotificationListener(
          ///表格
          child: Column(children: [
            ///标题行
            Container(
              alignment: Alignment.topLeft,
              child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  scrollDirection: Axis.horizontal, //horizontal
                  controller: firstRowController,
                  child: SizedBox(
                    width: totalWidth,
                    child: Table(
                      children: _buildTableOneRow(),
                      columnWidths: columnWidthMap,
                    ),
                  )),
            ),

            ///表格内容
            Expanded(
              child: ListView(
                physics: const ClampingScrollPhysics(),
                controller: columnController,
                children: [
                  SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    controller: secondedRowController,
                    scrollDirection: Axis.horizontal, //horizontal
                    child: Row(
                      children: [
                        SizedBox(
                          width: totalWidth,
                          child: Table(
                            children: _buildTableRow(),
                            columnWidths: columnWidthMap,
                          ), //设置列的行宽
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ]),
        ));
  }

  //创建tableRow的行和列
  List<TableRow> _buildTableRow() {
    List<TableRow> returnList = [];
    for (int j = 0; j < widget.baseTableContentList.length; j++) {
      //添加行
      returnList.add(_buildSingleRow(j, widget.baseTableContentList[j]));
    }
    return returnList;
  }

  //第一行标题
  List<TableRow> _buildTableOneRow() {
    List<TableRow> returnList = [];
    returnList.add(TableRow(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
              bottom: BorderSide(
                  color: AppColors.dividerColor,
                  style: BorderStyle.solid,
                  width: 1.w)),
        ),
        children: widget.titleWidgetList));
    return returnList;
  }

  //创建行tableRow单独一行
  TableRow _buildSingleRow(int index, BaseTableContent content) {
    BorderSide borderSide;
    if (content.config.bottomLine) {
      borderSide = BorderSide(
          color: AppColors.dividerColor,
          style: BorderStyle.solid,
          width: ScreenUtil().setHeight(1));
    } else {
      borderSide = BorderSide(
          color: AppColors.dividerColor,
          style: BorderStyle.solid,
          width: ScreenUtil().setHeight(0));
    }

    return TableRow(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(bottom: borderSide),
        ),
        children: _buildSingleRowChildren(index, content));
  }

  //单独行的所有wight,即此行每一列
  _buildSingleRowChildren(int index, BaseTableContent content) {
    List<Widget> children = [];
    for (int i = 0; i < content.contentWidgetList.length; i++) {
      if (widget.excludeTapColumn != null &&
          widget.excludeTapColumn!.contains(i)) {
        ///排除点击事件
        children.add(Container(
          color: Colors.transparent,
          child: content.contentWidgetList[i],
        ));
      } else {
        children.add(GestureDetector(
          child: Container(
            height: content.config.rowHeight.h,
            color: Colors.transparent,
            child: content.contentWidgetList[i],
          ),
          onTap: () {
            if (widget.tapRowCallback != null) {
              widget.tapRowCallback!(index);
            }
          },
        ));
      }
    }
    return children;
  }

  @override
  void dispose() {
    // BaseTableWidgetTool().off(widget.eventString ?? "");
    columnController.dispose();
    firstRowController.dispose();
    secondedRowController.dispose();
    super.dispose();
  }

  //滑动到底部，之前需要有，后面又取消了
  void scrollToBottom() {
    if (widget.baseTableContentList.isNotEmpty) {
      double offset = 0;
      for (var content in widget.baseTableContentList) {
        offset += content.config.rowHeight.h;
      }
      columnController.animateTo(offset,
          duration: const Duration(seconds: 1), curve: Curves.ease);
    }
  }

  ///滑动到添加行
  @override
  void scrollToIndex(int index) {
    if (widget.baseTableContentList.isNotEmpty) {
      double offset =
          MathUtil.multiplyDec(index, BaseTableContentConfig().rowHeight.h)
              .toDouble();
      columnController.animateTo(offset,
          duration: const Duration(seconds: 1), curve: Curves.easeIn);
    }
  }
}

class ColumnWidth {
  bool isShow = true;
  double width = 0;
}
