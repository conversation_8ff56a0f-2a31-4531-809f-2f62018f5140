import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';

import '../../widgets/halo_pos_label.dart';
import '../../widgets/scanner_dialog.dart';
import '../entity/goods_bill.dto.dart';

/// 创建时间：2024/1/2
/// 作者：xiaotiaochong
/// 描述：

class QuickScanPayDialog extends StatefulWidget {
  static showQuickScanPayDialog(
      BuildContext context, GoodsBillDto goodsBillDto,Function(String) callback) {
    showDialog(
            context: context, builder: (c) => QuickScanPayDialog(goodsBillDto))
        .then((value) {
          String resultString = value is String ? value : "";
          if (resultString.isNotEmpty) {
            callback(resultString);
          }
    });
  }

  final GoodsBillDto goodsBillDto;

  const QuickScanPayDialog(this.goodsBillDto, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _QuickScanPayDialogState();
  }
}

class _QuickScanPayDialogState extends State<QuickScanPayDialog> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        alignment: Alignment.center,
        color: Colors.transparent,
        child: Container(
          width: 540.w,
          height: 280.h,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.black),
            borderRadius: BorderRadius.all(Radius.circular(8.w)),
          ),
          child: Column(
            children: [
              ///扫码控件
              const ScannerDialog(),
              Padding(
                  padding: EdgeInsets.only(top: 20.h, bottom: 20.h),
                  child: HaloPosLabel(
                    "金额 ¥${widget.goodsBillDto.currencyBillTotal}",
                    textStyle: TextStyle(
                        fontSize: 32.sp,
                        color: Colors.red,
                        fontWeight: FontWeight.bold),
                  )),
              HaloPosLabel(
                "等待扫码支付...",
                textStyle: TextStyle(fontSize: 26.sp, color: Colors.black),
              ),
              SizedBox(
                height: 10.h,
              ),
              HaloPosLabel(
                "为避免扫码异常，扫码过程中请勿使用键盘",
                textStyle: TextStyle(fontSize: 24.sp, color: Colors.grey),
              ),
              const Expanded(child: SizedBox()),
              Divider(
                height: 1.h,
                color: Colors.grey,
              ),
              GestureDetector(
                child: Container(
                  color: Colors.transparent,
                  padding: EdgeInsets.symmetric(vertical: 10.h),
                  alignment: Alignment.center,
                  child: HaloPosLabel(
                    "取消",
                    textStyle: TextStyle(fontSize: 32.sp, color: Colors.blue),
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
