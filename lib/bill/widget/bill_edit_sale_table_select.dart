import 'package:flutter/material.dart';
import 'package:halo_pos/bill/tool/goods_tool.dart';
import 'package:halo_pos/common/num_extension.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:haloui/widget/halo_dialog.dart';

import '../../bill/entity/base_table_content.dart';
import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../../enum/bill_type.dart';
import '../../iconfont/icon_font.dart';
import '../tool/back.dart';
import '../tool/bill_tool.dart';
import '../tool/decimal_display_helper.dart';
import '../tool/index_scroll_controller.dart';
import 'base_table_widget.dart';
import 'bill_edit_sale_table.dart';
import 'ptype/ptype_detail_page.dart';

///退货商品列表
class BillEditSaleTableSelect extends BillEditSaleTable {
  const BillEditSaleTableSelect(
      {Key? key,
      required BillType billType,
      required IndexScrollerController indexScrollerController,
      required Function(GoodsBillDto goodsBill, bool resetFocus)
          dataChangeCallBack,
      required GoodsBillDto goodsBillDto})
      : super(
            key: key,
            billType: billType,
            indexScrollerController: indexScrollerController,
            dataChangeCallBack: dataChangeCallBack,
            goodsBillDto: goodsBillDto);

  @override
  State<StatefulWidget> createState() {
    return BillEditSaleTableSelectState();
  }
}

class BillEditSaleTableSelectState extends BillEditSaleTableState {
  bool selectAll = true;

  ///表格勾选框列的宽度
  double get checkBoxColumnWidth => 60.w;

  ///这里表格前面要添加一个checkBox，所以宽度减去
  @override
  double get tableWidth =>
      MathUtil.subtractDec(super.tableWidth, checkBoxColumnWidth).toDouble();

  @override
  List<ColumnWidth> buildColumnWidth() {
    return [
      ColumnWidth()
        ..isShow = true
        ..width = checkBoxColumnWidth,
      ...super.buildColumnWidth()
    ];
  }

  @override
  List<Widget> buildTitleWidget() {
    selectAll = dataSource
        .where((element) => (element.maxQty ?? 0) > 0)
        .every((element) => element.inDetailSelect);
    return [
      GestureDetector(
        behavior: HitTestBehavior.opaque,
        child: Container(
          alignment: Alignment.center,
          color: const Color(0xFFF5F5F5),
          height: 80.w,
          width: 30.w,
          padding: EdgeInsets.only(left: 30.w),
          child: IconFont(
              selectAll ? IconNames.xuanzhong : IconNames.weixuanzhong,
              size: 20.w,
              color: "#2769FF"),
        ),
        onTap: () => doSelectAllItem(),
      ),
      ...super.buildTitleWidget(),
    ];
  }

  ///折扣列
  @override
  Widget buildDiscountCell(GoodsDetailDto goods) {
    String discount =
        DecimalDisplayHelper.getDiscountFixed(goods.lastDiscount.toString());
    return buildLabel(discount);
  }

  ///最终优惠列
  @override
  Widget buildReducePriceCell(GoodsDetailDto goods) =>
      buildLabel(MathUtil.subtractDec(goods.currencyPreferentialTotal,
              goods.currencyGivePreferentialTotal)
          .toString());

  ///现价列
  @override
  Widget buildCurrentPriceCell(GoodsDetailDto goods) =>
      buildLabel(goods.posCurrencyDisedTaxedPrice.toString());

  ///小计列
  @override
  Widget buildTotalCell(GoodsDetailDto goods) =>
      buildLabel(goods.posCurrencyDisedTaxedTotal.toString(),
          color: const Color(0xFFF0642E));

  @override
  num getGoodsMaxCount(GoodsDetailDto goods) {
    return MathUtil.subtractDec(goods.maxQty!, goods.completedQty!)
        .toDouble()
        .getIntWhenInteger;
  }

  ///单独行和配置
  @override
  BaseTableContent buildContentWidget(GoodsDetailDto goods) {
    Widget checkBox;
    //非套餐行，才展示选择框
    //能退货才展示选择框
    if (!GoodsTool.isComboDetail(goods) && (goods.maxQty ?? 0) > 0) {
      checkBox = GestureDetector(
        behavior: HitTestBehavior.opaque,
        child: Container(
          alignment: Alignment.center,
          child: IconFont(
              goods.inDetailSelect
                  ? IconNames.xuanzhong
                  : IconNames.weixuanzhong,
              size: 20.w,
              color: "#2769FF"),
        ),
        onTap: () => selectItem(goods),
      );
    } else {
      checkBox = Container();
    }
    checkBox = Container(
      padding: EdgeInsets.only(left: 30.w),
      color: goods == currentGoods ? const Color(0x75C0D7FF) : null,
      // width: 30.w,
      child: checkBox,
    );
    BaseTableContent content = super.buildContentWidget(goods);
    content.contentWidgetList = [checkBox, ...content.contentWidgetList];
    return content;
  }

  ///弹出商品详情界面
  @override
  void showItemDetail(int index) {
    GoodsDetailDto goods = dataSource[index];
    HaloDialog(context,
        dismissOnTouchOutside: true,
        barrierColor: Colors.transparent,
        child: PtypeDetailPage(
          goods,
          goodsBillDto,
          billType: widget.billType,
          maxCount: getGoodsMaxCount(goods),
          markGift: false,
          enable: BillTool.checkPtypeDetailEnable(goods),
          valueChanged: (newGoods, editRetailPrice) {
            dataSource[index] = newGoods;
            countChange(newGoods, newGoods.unitQty);
          },
        )).show();
  }

  ///全选/反选
  void doSelectAllItem() {
    for (GoodsDetailDto goodsDetailDto in dataSource) {
      num count = 0;
      if (!selectAll) {
        count = goodsDetailDto.maxQty ?? 0;
      }
      countChange(goodsDetailDto, count,
          delete: false, requestSearchBarFocus: false, callback: false);
    }
    widget.dataChangeCallBack(goodsBillDto, false);
  }

  ///选中单个明细行
  void selectItem(GoodsDetailDto goodsDetailDto) {
    if (goodsDetailDto.unitQty == 0) {
      goodsDetailDto.unitQty = goodsDetailDto.maxQty ?? 0;
    } else {
      goodsDetailDto.unitQty = 0;
    }
    countChange(goodsDetailDto, goodsDetailDto.unitQty,
        delete: false, requestSearchBarFocus: false);
  }

  @override
  void countChange(
    GoodsDetailDto goods,
    num value, {
    bool delete = true,
    bool requestSearchBarFocus = true,
    bool callback = true,
  }) {
    goods.unitQty = value;
    BackBillUtil.onQtyChange(goods,
        comboDetails:
            GoodsTool.getComboDetailsFromGoodsList(dataSource, goods));
    if (callback) {
      widget.dataChangeCallBack(goodsBillDto, requestSearchBarFocus);
    }
  }

  ///当商品数量为0时，不删除商品
  @override
  void doDelete(GoodsDetailDto goods, {bool delete = true}) {}
}
