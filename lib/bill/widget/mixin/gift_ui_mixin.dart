import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../common/style/app_colors.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/constant/js.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/common/app_color_helper.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/keyboard_default_hidden_textview/keyboard_hidden_textfield.dart';

/// 创建时间：2/20/23 
/// 作者：xiaotiaochong
/// 描述：

mixin GiftUIMixin {
  //
  // Widget build(BuildContext context) {
  //   return UnconstrainedBox(
  //     child: Material(
  //       child: HaloContainer(
  //         width: 800.w,
  //         height: 700.h,
  //         color: Colors.white,
  //         border: Border.all(color: AppColors.dividerColor, width: 1),
  //         borderRadius: BorderRadius.all(Radius.circular(4.w)),
  //         mainAxisSize: MainAxisSize.max,
  //         direction: Axis.vertical,
  //         children: [
  //           _buildTitle(),
  //           _buildList(),
  //           if (!onlyRead) Divider(height: 1.h,color: ColorUtil.stringColor("D0D0D0"),),
  //           if (!onlyRead)_buildBottom(),
  //         ],
  //       ),
  //     ),
  //   );
  // }
  //
  //
  // ///标题栏和关闭按钮
  // Widget _buildTitle() {
  //   String promotionName = backModel.billPromotionInfoDto.fullname;
  //   return Column(
  //     children: [
  //       HaloContainer(
  //         height: 80.h,
  //         padding: EdgeInsets.only(left: 34.w, right: 34.w),
  //         mainAxisSize: MainAxisSize.max,
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(title,
  //               style: TextStyle(
  //                   color: AppColorHelper(context).getTitleBoldTextColor(),
  //                   fontWeight: FontWeight.w500,
  //                   fontSize: 28.sp)),
  //           GestureDetector(
  //             child: IconFont(IconNames.close, size: 30.w),
  //             onTap: () => Navigator.pop(context),
  //           )
  //         ],
  //       ),
  //       //分割线
  //       Divider(height: 1.h, color: const Color(0xFFDBDBDB)),
  //       Container(
  //         padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h),
  //         child: Row(
  //           children: [
  //             HaloPosLabel(
  //               "满足",
  //               textStyle: TextStyle(fontSize: 18.sp),
  //             ),
  //             HaloPosLabel(
  //               "【$promotionName】",
  //               textStyle: TextStyle(color: Colors.orange, fontSize: 18.sp),
  //             ),
  //             HaloPosLabel("条件，可以在下列商品中指定赠送",
  //                 textStyle: TextStyle(fontSize: 18.sp)),
  //             if (!onlyRead)HaloPosLabel("【$maxCount】",
  //                 textStyle: TextStyle(color: Colors.blue, fontSize: 18.sp)),
  //           ],
  //         ),
  //       ),
  //       HaloContainer(
  //         margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
  //         padding: EdgeInsets.symmetric(
  //           horizontal: 20.w,
  //         ),
  //         height: 40.h,
  //         color: Color(0x0f000000),
  //         borderRadius: BorderRadius.all(
  //           Radius.circular(2.0),
  //         ),
  //         children: [
  //           IconFont(
  //             IconNames.ngp_search,
  //             color: "#67686A",
  //           ),
  //           SizedBox(
  //             width: 4.w,
  //           ),
  //           Expanded(
  //             child: KeyboardHiddenTextField(
  //               defaultText: "",
  //               hintText: "扫描选择商品",
  //               focusNode: searchFocusNode,
  //               textColor: AppColorHelper(context).getTitleBoldTextColor(),
  //               onSubmitted: (text) {
  //                 _doSearch(text);
  //               },
  //             ),
  //           )
  //         ],
  //       )
  //     ],
  //   );
  // }


}