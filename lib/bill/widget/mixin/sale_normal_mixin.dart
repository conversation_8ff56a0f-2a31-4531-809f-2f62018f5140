import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/haloui.dart';

import '../../../common/keyboard_hidden.dart';
import '../../../common/style/app_colors.dart';
import '../../../enum/bill_type.dart';
import '../../../login/entity/store/barcode_scale_config.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/dotted_line.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../../widgets/selector/scan_select.dart';
import '../../entity/barcode_scale_scan.dart';
import '../../entity/goods_detail_dto.dart';
import '../../tool/decimal_display_helper.dart';
import '../../tool/scan_tool.dart';
import '../ptype/goods_and_combo_select_list_page.dart';
import 'bill_mixin.dart';

///普通单据页面mixin，即非选品开单页面，包含出库单，退货单，换货单
mixin SaleNormalMixin<T extends BaseStatefulPage>
    on BaseStatefulPageState<T>, BillMixin<T> {
  ///控制搜索栏焦点
  @override
  final KeyboardHiddenFocusNode searchFocusNode = KeyboardHiddenFocusNode();

  final bool showSubDetail = true;

  final double singleButtonHeight = 80;

  ///搜索关键字
  String searchString = "";

  ///底部两根虚线之间的控件，展示金额数量
  Widget buildSubDetail() => Container();

  ///构建底部的vip信息以及各种按钮
  Widget buildVipBottom(BuildContext context);

  @override
  Widget buildLeftBody(BuildContext context) {
    // if (goodsBillDto.vchcode == null) {
    //   return GestureDetector(
    //     child: Container(
    //       color: Colors.transparent,
    //       alignment: Alignment.topCenter,
    //       padding: EdgeInsets.symmetric(vertical: 200.h),
    //       child: Text(afterFirstLoad ? "单据获取失败，点击重新获取" : ""),
    //     ),
    //     onTap: () {
    //       loadData();
    //     },
    //   );
    // } else
    if (hasSalePermission) {
      return buildTable();
    } else {
      return Center(
        child: Text("此账号没有${getActionBarTitle() ?? ""}权限"),
      );
    }
  }

  ///构建商品列表
  Widget buildTable();

  ///各项数据和收银按钮
  @override
  Widget buildBottomBody(BuildContext context) {
    if (!hasSalePermission) {
      return Container();
    }
    return HaloContainer(
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.end,
      color: Colors.white,
      children: [
        buildDottedLine(), // if (showSubDetail)
        buildSubDetail(),
        if (showSubDetail) buildDottedLine(),
        buildVipBottom(context)
      ],
    );
  }

  ///虚线分割
  Widget buildDottedLine() {
    return DottedLine(
      axis: Axis.horizontal,
      strokeWidth: 0.5.w,
      color: const Color(0xFF979797),
      dashPattern: const [3, 3],
    );
  }

  ///竖直分割线
  Widget buildDivider() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 10.w,
      ),
      color: Colors.grey,
      width: 1.w,
      height: 40.h,
    );
  }

  ///搜索框
  Widget buildSearch() {
    return ScanSelectWidget(
        searchFocusNode: searchFocusNode,
        hint: "扫描或输入商品条码/序列号",
        onSubmitted: (text) {
          doScanCode(text);
        },
        onTapBefore: () {
          //当自定义键盘弹出时，拦截点击弹出系统键盘的事件
          return false;
        });
  }

  ///添加商品按钮
  Widget buildAddGoodsProp() {
    return buildAddGoodsButton("选择商品", showSearchGoodsDialog);
  }

  ///构建无码商品和添加商品按钮
  Widget buildAddGoodsButton(String title, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 140.w,
        height: 70.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: AppColors.buttonBorderColor),
            borderRadius: const BorderRadius.all(Radius.circular(5))),
        padding: EdgeInsets.all(12.w),
        child: Text(
          title,
          style: TextStyle(
              height: 1, color: const Color(0xFF67686A), fontSize: 22.sp),
        ),
      ),
    );
  }

  ///构建提交单据按钮，收银或者退款
  Widget buildTotalPriceView(BuildContext context) {
    return GestureDetector(
      child: Container(
          decoration: const BoxDecoration(
            color: Color(0xFF2769FF),
            borderRadius: BorderRadius.all(Radius.circular(5.0)),
          ),
          alignment: Alignment.center,
          width: 340.w,
          height: singleButtonHeight.h,
          margin: EdgeInsets.all(10.w),
          child: buildTotalPriceDetailView()),
      onTap: () {
        doSubmit(context);
      },
    );
  }

  ///结算按钮内容
  @override
  Widget buildTotalPriceDetailViewCustomer() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 20.w),
          child: Text(
            billTotalTitle,
            textAlign: TextAlign.center,
            maxLines: 1,
            style: TextStyle(
                color: Colors.white,
                decoration: TextDecoration.none,
                fontSize: 30.sp,
                fontWeight: FontWeight.bold),
          ),
        ),
        Flexible(
          fit: FlexFit.loose,
          child: Padding(
              padding: EdgeInsets.only(left: 10.w, right: 20.w),
              child: HaloPosLabel(
                "¥${DecimalDisplayHelper.getTotalFixed(num.parse(sumAllFinalTotal).abs().toString())}",
                textStyle: TextStyle(
                    color: Colors.white,
                    decoration: TextDecoration.none,
                    fontSize: 32.sp,
                    fontWeight: FontWeight.bold),
              )),
        ),
      ],
    );
  }

  ///搜索商品弹窗
  void showSearchGoodsDialog() {
    showDialog(
            context: context,
            builder: (context) => const GoodsAndComboSelectListPage())
        .then((goods) =>
            ScanTool.handleScanResult(context, goods, goodsList, billType))
        .then((List<GoodsDetailDto>? list) {
      if (list != null) {
        String vchType = BillTypeData[billType]!;
        for (var element in list) {
          element.vchtype = vchType;
        }
        handleSearchResult(list);
      }
    });
  }

  ///扫描添加商品
  void doScanCode(String text,
      {BarcodeScaleScan? barcodeScaleScan,
      BarcodeScaleConfig? barcodeScaleConfig}) async {
    searchString = text;
    ScanTool.scan(
            context: context,
            list: goodsList,
            billType: billType,
            scanCode: StringUtil.trim(searchString),
            billDate:
                DateUtil.formatDate(DateTime.now(), format: DateFormats.full),
            skuBarCode: barcodeScaleScan?.pTypePosition ?? "")
        .then((value) async {
      searchString = "";
      if (null != value) {
        handleSearchResult(value,
            barcodeScaleScan: barcodeScaleScan,
            barcodeScaleConfig: barcodeScaleConfig);
      } else {
        setState(() {
          searchFocusNode.requestFocus();
        });
      }
    });
  }
}
