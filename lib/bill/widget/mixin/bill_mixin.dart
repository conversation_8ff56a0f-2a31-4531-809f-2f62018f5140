import 'dart:async';

import 'package:flutter/material.dart';
import 'package:halo_pos/bill/settlement/settlement_mixin.dart';
import 'package:halo_pos/bill/widget/mixin/select_vip_mixin.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_pos/offline/offline_tool.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_type.dart';
import '../../../login/entity/store/barcode_scale_config.dart';
import '../../../login/entity/store/store_payment.dart';
import '../../entity/barcode_scale_scan.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../tool/bill_isolate_tool.dart';
import '../../tool/bill_tool.dart';
import '../../tool/decimal_display_helper.dart';
import '../../tool/index_scroll_controller.dart';

///单据基础mixin
mixin BillMixin<T extends StatefulWidget> on SelectVipMixin<T> {
  BillType billType = BillType.SaleBill;

  ///控制搜索栏焦点
  FocusNode get searchFocusNode;

  bool get enableStore => vipInfo != null;

  ///新增商品后控制跳转到该行
  final IndexScrollerController indexScrollerController =
      IndexScrollerController();

  ///底部提交按钮标题，收银
  final String billTotalTitle = "收银";

  ///开单是否有权限
  bool get hasSalePermission =>
      SpTool.getPermission().recordsheetSaleBillcreate ?? false;

  ///商品列表
  List<GoodsDetailDto> get goodsList =>
      BillTool.getGoodsDetails(goodsBillDto, billType);

  ///可用的支付方式
  List<StorePayway> get storePaywayList => filterPayWayList(
      enableStore: enableStore, isOffline: OffLineTool().isOfflineLogin);

  ///单据实体类，用于提交单据
  GoodsBillDto goodsBillDto = GoodsBillDto();

  ///总数量
  String sumAllQty = "0";

  ///总金额
  String sumAllTotal = "0";

  ///总优惠
  String sumAllPreferential = "0";

  ///应收金额
  String sumAllFinalTotal = "0";

  ///结算按钮
  Widget buildTotalPriceDetailViewCustomer();

  ///计算按钮
  Widget buildTotalPriceDetailView() {
    if (BillIsolateTool().isIsoFinish) {
      return buildTotalPriceDetailViewCustomer();
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 20.w),
            child: Text(
              "计算中...",
              textAlign: TextAlign.center,
              maxLines: 1,
              style: TextStyle(
                  color: Colors.white,
                  decoration: TextDecoration.none,
                  fontSize: 36.sp,
                  fontWeight: FontWeight.w600),
            ),
          ),
        ],
      );
    }
  }

  ///构建底部的功能按钮，[tips]即按钮上的红色标记数字
  Widget buildSingleButton(String title,
      {double width = double.maxFinite,
      double height = 30,
      double fontSize = 20,
      String? tips,
      bool enable = true,
      Color? foregroundColor,
      Color? backgroundColor,
      VoidCallback? action}) {
    foregroundColor ??=
        enable ? AppColors.accentColor : const Color(0xFFBBBBBB);
    backgroundColor ??= enable ? Colors.white : const Color(0xFFEEEEEE);
    Widget result = Align(
      alignment: Alignment.center,
      child: FittedBox(
        fit: BoxFit.contain,
        child: Text(
          title,
          maxLines: 1,
          style: TextStyle(fontSize: fontSize, color: foregroundColor),
        ),
      ),
    );
    //优惠券展示数字角标
    if (tips?.isNotEmpty == true) {
      result = Stack(
        children: [
          result,
          Positioned(
            top: 10.h,
            right: 10.w,
            child: Container(
              alignment: Alignment.center,
              width: 20.w,
              height: 20.w,
              decoration: const BoxDecoration(
                  color: Color(0xFFFF3F3F), shape: BoxShape.circle),
              child: Text(
                tips!,
                style: TextStyle(fontSize: 14.sp, color: Colors.white),
              ),
            ),
          )
        ],
      );
    }
    result = Container(
      width: width,
      height: height,
      padding: EdgeInsets.symmetric(
        horizontal: 5.w,
      ),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.0),
          color: backgroundColor,
          border: Border.all(color: foregroundColor, width: 1.w)),
      child: result,
    );
    if (enable && action != null) {
      result = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: action,
        child: result,
      );
    }
    return result;
  }

  ///拉取一张新单据
  Future<void> loadData() async {
    //初始化单据
    goodsBillDto = GoodsBillDto.fromMap({});
    reCalcStatistic();
    cleanVip();
  }

  ///将新商品添加到商品列表中，或者合并
  ///[mergeHalfPriceGoods] 是否合并第二件半价商品，现在执行打折和促销之前，应该先将第二件半价商品合并，
  ///避免出现之前触发第二件半价，而再次执行的时候不触发，导致出现两个相同的商品行出现，而在这之后再合并的话，可能会导致金额和优惠有精度问题
  GoodsDetailDto? addOrMergeGoodsList(List<GoodsDetailDto> details,
      {bool mergeHalfPriceGoods = false}) {
    return BillTool.addGoodsDetails(goodsBillDto, goodsList, details, billType,
        mergeHalfPriceGoods: mergeHalfPriceGoods);
  }

  ///商品新增
  void handleSearchResult(List<GoodsDetailDto> details,
      {BarcodeScaleScan? barcodeScaleScan,
      BarcodeScaleConfig? barcodeScaleConfig}) {
    addGoodsDetails(details);
  }

  ///添加新商品
  void addGoodsDetails(List<GoodsDetailDto> details) {
    //合并商品，在执行促销前，合并第二件半价商品
    GoodsDetailDto? goods =
        addOrMergeGoodsList(details, mergeHalfPriceGoods: true);
    if (goods != null) {
      indexScrollerController.scrollToIndex(goodsList.indexOf(goods));
    }
  }

  ///刷新商品列表
  void updateGoodsDetail({bool requestSearchFocus = true}) {
    //合并商品，在执行促销前，合并第二件半价商品
    addOrMergeGoodsList(goodsList, mergeHalfPriceGoods: true);
  }

  ///重算单据相关金额
  void reCalcStatistic() {
    BackSumAllTotal backSumAllTotal = BillTool.reCalcStatisticSaleOrSaleBack(
        goodsBillDto,
        billType: billType);
    sumAllQty =
        DecimalDisplayHelper.getQtyFixed(backSumAllTotal.sumQty.toString());
    sumAllTotal =
        DecimalDisplayHelper.getTotalFixed(backSumAllTotal.sumTotal.toString());
    sumAllFinalTotal = DecimalDisplayHelper.getTotalFixed(
        backSumAllTotal.sumPosDisedTaxedTotal.toString());
    sumAllPreferential = DecimalDisplayHelper.getTotalFixed(
        backSumAllTotal.sumFinalPreferential.toString());
  }

  ///检查商品列表是否为空，以及是否数量都为0
  bool checkDetailIsEmpty(List<GoodsDetailDto> details) {
    return details.isEmpty || details.every((element) => element.unitQty == 0);
  }

  Future<void> doSubmit(BuildContext context) async {
    if (!checkNextStep(goodsBillDto)) {
      return;
    }
    submitBill();
  }

  ///提交单据前的检查
  bool checkNextStep(GoodsBillDto goodsBillDto);

  ///提交单据
  void submitBill() {
    List<StorePayway> storePaywayList = this.storePaywayList;
    BillTool.setGoodsBill(goodsBillDto, vchtype: billType);
    if (storePaywayList.isNotEmpty) {
      showSettlementDialog(bill: goodsBillDto);
    } else {
      HaloToast.showError(context, msg: "支付方式设置有误");
    }
  }

  ///弹出结算页面
  Future showSettlementDialog({required GoodsBillDto bill});

  void onPaySuccess();
}
