import 'dart:async';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/bill/entity/bill_vip_assert_change.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_pos/common/tool/payment.dart';
import 'package:halo_pos/common/tool/payment_orders_mixin.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';

import '../../../common/login/login_center.dart';
import '../../../common/num_extension.dart';
import '../../../common/style/app_pos_size.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../enum/bill_pay_state.dart';
import '../../../enum/bill_post_state.dart';
import '../../../enum/bill_ptype_pop_value_change_type.dart';
import '../../../enum/bill_type.dart';
import '../../../login/entity/store/barcode_scale_config.dart';
import '../../../login/entity/store/barcode_scale_type_enum.dart';
import '../../../login/entity/store/store_etype.dart';
import '../../../login/entity/store/store_payment.dart';
import '../../../offline/offline_dialog.dart';
import '../../../offline/offline_tool.dart';
import '../../../plugin/secondary_screen_plugin.dart';
import '../../../print/tool/print_tool.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../vip/utils/svip_util.dart';
import '../../../widgets/base/halo_pos_alert_dialog.dart';
import '../../bill/bill_edit_sale_back.dart';
import '../../entity/barcode_scale_scan.dart';
import '../../entity/bill_promotion_info_dto.dart';
import '../../entity/bill_save_reslut_dto.dart';
import '../../entity/bill_save_result_type.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/order_bill_item_entity.dart';
import '../../entity/pay_result_dto.dart';
import '../../entity/payment_dto.dart';
import '../../entity/preferential_dto.dart';
import '../../entity/ss_card_dto.dart';
import '../../model/bill_model.dart';
import '../../model/card_template_model.dart';
import '../../saleorder/sale_order_page.dart';
import '../../saleorder/widget/order_list_page.dart';
import '../../settlement/entity/score_configuration.dart';
import '../../settlement/sale_bill_settlement.dart';
import '../../settlement/widget/add_tips_page.dart';
import '../../settlement/widget/card_select_dialog.dart';
import '../../settlement/widget/input_board_page.dart';
import '../../settlement/widget/preferential_detail_dialog.dart';
import '../../settlement/widget/use_score_dialog.dart';
import '../../tool/bill_goods_util.dart';
import '../../tool/bill_isolate_tool.dart';
import '../../tool/bill_price_compute_helper.dart';
import '../../tool/bill_tool.dart';
import '../../tool/decimal_display_helper.dart';

// import '../../tool/promotion/coupon_tool.dart';
import '../../tool/goods_tool.dart';
import '../../tool/promotion/card.dart';
import '../../tool/promotion/manual_price.dart';
import '../../tool/promotion/preferential.dart';
import '../../tool/promotion/price.dart';
import '../../tool/promotion/promotion.dart'
    show
        CouponGift,
        PromotionGift,
        PromotionHints,
        PromotionType,
        PromotionTypeExtension,
        PromotionUtil,
        autoChoosePromotionGift;
import '../../tool/sale_event_buds.dart';
import '../../tool/scan_tool.dart';
import '../../widget/mixin/select_vip_mixin.dart';
import '../../widget/sale/gift_rights_dialog.dart';
import '../../widget/sale/sale_bill_to_sale_back_bill_dialog.dart';
import '../sale/promotion_gift_select.dart';
import 'bill_mixin.dart';

///出库单mixin
mixin SaleBusinessMixin<T extends StatefulWidget>
    on State<T>, SelectVipMixin<T>, BillMixin<T>, PaymentOrdersMixin<T> {
  ///退货是否有权限
  bool get hasSaleBackPermission =>
      SpTool.getPermission().recordsheetSaleBackBillcreate ?? false;

  ///销售单查看权限
  bool get hasSaleViewPermission =>
      SpTool.getPermission().recordsheetSaleBillview ?? false;

  ///是否有新的卡券
  bool get showGiftCoupon =>
      vipInfo != null && goodsBillDto.giftCouponList.isNotEmpty;

  ///整单折扣权限
  bool get billDiscountPermission =>
      SpTool.getPermission().shopsalesalesettingbillDiscount ?? false;

  ///整单优惠权限
  bool get manualPreferentialView =>
      SpTool.getPermission().shopsalesalesettingeditOrderPreferential ?? false;

  ///整单折扣记录
  num billDiscount = 1;

  ///当前使用积分数量
  int scoreUsed = 0;

  String get draftString {
    return goodsBillDto.outDetail.isNotEmpty ? "挂单" : "取单";
  }

  ///总额优惠金额(整单优惠分摊)
  num manualPreferential = 0;

  ///积分策略
  ScoreConfiguration? scoreConfiguration;

  ///是否使用全部积分
  bool _useAllScore = false;

  ///是否支持积分抵扣
  bool get checkScoreEnable =>
      vipInfo != null &&
      billType == BillType.SaleBill &&
      scoreConfiguration != null &&
      !isVipExpired(
        vipInfo?.vip?.validDate,
        vipType: vipInfo?.level?.vipType == true ? 1 : 0,
      );

  ///当前已选择的优惠券列表
  List<SsCardDto> selectedCardList = [];

  ///会员权益卡列表
  List<SsCardDto>? vipRightsCardList;

  ///会员优惠券张数
  num cardLength = 0;

  ///优惠券张数
  String get couponCardCount {
    String couponCardCount;
    if (cardLength == 0) {
      cardLength = vipInfo?.cardList?.length ?? 0;
    }
    if (cardLength == 0) {
      couponCardCount = "";
    } else if (cardLength < 9) {
      couponCardCount = cardLength.toString();
    } else {
      couponCardCount = "9+";
    }
    return couponCardCount;
  }

  StreamSubscription? streamSubscription;

  ///拦截计算未完成时，点击收银
  Completer<void>? completer;

  ///优惠明细按钮
  Widget buildPreferentialView({Color? color}) {
    color = color ?? AppColors.describeFontColor;
    return GestureDetector(
      child: Text(
        "优惠明细 >",
        style: TextStyle(
          color: color,
          fontSize: AppPosSize.secondaryTitleFontSize.sp,
        ),
      ),
      onTap: () {
        doPreferentialGoods();
      },
    );
  }

  ///订单满减界面
  Widget? buildBillPromotionView({bool showCorner = false}) {
    if (goodsBillDto.promotionHints?.isNotEmpty == true) {
      return Container(
        height: 48.h,
        decoration: BoxDecoration(
          color: const Color(0xFFFFEFEF),
          borderRadius: showCorner ? BorderRadius.circular(8.w) : null,
        ),
        padding: EdgeInsets.symmetric(horizontal: 6.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 36.h,
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              decoration: BoxDecoration(
                color: const Color(0xFFFE404C),
                borderRadius: BorderRadius.circular(4.w),
              ),
              child: Text(
                "订单满减",
                style: TextStyle(color: Colors.white, fontSize: 20.sp),
              ),
            ),
            SizedBox(width: 10.w),
            Flexible(
              child: HaloLabel(
                goodsBillDto.promotionHints!.first.hints,
                textStyle: TextStyle(
                  color: const Color(0xFFFA2F22),
                  fontSize: 22.sp,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      );
    }
    return null;
  }

  //region initData
  ///刷新数据的观察者,只能调用一次
  void addVipListener() {
    streamSubscription = SaleEventBus.getInstance().on<String>().listen((
      event,
    ) {
      if (event == SaleEventBus.updateVip) {
        refreshVipInfo(context);
      } else if (event == SaleEventBus.updateGoodsDetail) {
        changeVip(vipInfo);
      } else if (event == SaleEventBus.doClear) {
        doClear();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    streamSubscription?.cancel();
  }

  @override
  void initState() {
    super.initState();
    BillModel.getPromotionAutomation(context).then((value) {
      autoChoosePromotionGift = value;
    });
  }

  ///拉取一张新单据
  @override
  Future<void> loadData() async {
    _useAllScore = false;
    billDiscount = 1;
    //刷新副屏
    SecondaryScreenPlugin.showGoodsList(goods: [], total: "0");
    await super.loadData();
  }

  //endregion

  ///获取积分抵扣策略
  Future<void> getScoreConfiguration() async {
    if (vipInfo != null &&
        (BillType.SaleBill == billType ||
            BillType.SaleChangeBill == billType)) {
      scoreConfiguration = SpTool.getScoreConfiguration();
    }
  }

  @override
  void changeVip(
    VipWithLevelAssertsRightsCardDTO? vipInfo, {
    bool updateGoods = true,
  }) async {
    cardLength = 0;
    scoreUsed = 0;
    scoreConfiguration = null;
    //清空会员权益卡
    vipRightsCardList = null;
    // if (this.vipInfo?.vip?.id != vipInfo?.vip?.id) {
    //   selectedCardList = [];
    //   this.vipInfo = vipInfo;
    //   resetDataVip();
    // } else {
    //   this.vipInfo = vipInfo;
    //   resetDataVip(changeVip: false);
    // }
    //todo 不管会员有无变化，先统一当做切换了会员处理
    //删除提货券赠品
    _deleteGiftCouponGift();
    selectedCardList = [];
    this.vipInfo = vipInfo;
    if (vipInfo != null) {
      //获取积分抵扣策略
      await getScoreConfiguration();
      vipRightsCardList = await CardTemplateModel.getCardByVipId(
        context,
        vipId: vipInfo.vip!.id,
      );
    }
    if (updateGoods) {
      updateGoodsDetail();
    } else {
      setState(() {});
    }
  }

  @override
  void cleanVip({bool updateGoods = true}) {
    super.cleanVip();
    changeVip(null, updateGoods: updateGoods);
  }

  ///删除提货券赠品
  void _deleteGiftCouponGift() {
    if (selectedCardList.any(
      (element) => element.cardType == CardType.giftCoupon.value,
    )) {
      CouponUtil.cleanAllGiftCouponGift(goodsBillDto);
    }
  }

  //region methods

  ///刷新副屏幕
  void refreshSecondaryScreen() {
    //所有优惠和支付方式改动都会调用此方法，故在此刷新副屏
    if (billType == BillType.SaleBill) {
      SecondaryScreenPlugin.showGoodsList(
        goods: SecondaryScreenPlugin.goodsDetailListToGoodsItemList(
          goodsDetails: goodsList,
        ),
        total: goodsBillDto.currencyBillTotal.toString(),
        discount: sumAllPreferential,
        vipInfo:
            vipInfo == null
                ? null
                : VipInfo(
                  name: vipInfo!.vip?.name,
                  level: vipInfo!.level?.levelName,
                  phone: vipInfo!.vip?.phone,
                  store:
                      vipInfo!.asserts?.totalMoney.getIntWhenInteger
                          .toString() ??
                      "0",
                  score: vipInfo!.asserts?.availableScore.toString() ?? "0",
                ),
      );
    }
  }

  ///刷新金额和副屏
  void reCalcStatisticAndResetSecondaryScreen() {
    setState(() {
      reCalcStatistic();
      refreshSecondaryScreen();
    });
  }

  ///检查是否可以进行结算
  @override
  bool checkNextStep(GoodsBillDto goodsBillDto) {
    goodsList.removeWhere((element) => element.unitQty == 0);
    if (null == SpTool.getStoreInfo() ||
        null == SpTool.getStoreInfo()!.paywayList ||
        SpTool.getStoreInfo()!.paywayList!.isEmpty) {
      DialogUtil.showAlertDialog(context, content: "没有配置账户");
      return false;
    }
    if (checkDetailIsEmpty(goodsList)) {
      HaloToast.showMsg(context, msg: "商品为空");
      return false;
    }
    for (GoodsDetailDto data in goodsList) {
      if (data.currencyDisedTotal < 0) {
        HaloToast.showMsg(context, msg: "有商品价格为负");
        return false;
      }
    }
    return true;
  }

  //region goodsDetail

  ///商品新增
  @override
  void handleSearchResult(
    List<GoodsDetailDto> details, {
    BarcodeScaleScan? barcodeScaleScan,
    BarcodeScaleConfig? barcodeScaleConfig,
  }) {
    ///带入条码秤的结果需满足三个条件
    ///1.条码秤解析实体有值
    ///2.details不为套餐（非套餐是仅一条商品数据返回），
    ///3.明细的条码和条码秤的解析商品码一致
    if (null != barcodeScaleConfig &&
        null != barcodeScaleScan &&
        StringUtil.isNotEmpty(barcodeScaleScan.pTypePosition) &&
        details.length == 1 &&
        barcodeScaleScan.pTypePosition == details.first.xcode) {
      handleBarcodeScaleDetail(
        details,
        barcodeScaleScan: barcodeScaleScan,
        barcodeScaleConfig: barcodeScaleConfig,
      );
      return;
    } else {
      addGoodsDetails(details);
    }
  }

  ///执行打折-》促销-》优惠分摊
  void startGoodsPreferential({bool requestSearchFocus = true}) {
    // 离线模式下清除会员信息和促销相关数据
    if (OffLineTool().isOfflineLogin) {
      _clearOfflineModeData();
    }

    PreferentialUtil.startAtDiscount(
      bill: goodsBillDto,
      billType: billType,
      vipInfo: OffLineTool().isOfflineLogin ? null : vipInfo,
      rightsCards: OffLineTool().isOfflineLogin ? [] : vipRightsCardList,
      selectedCouponList: OffLineTool().isOfflineLogin ? [] : selectedCardList,
      manualPreferential: manualPreferential,
      enableScore: OffLineTool().isOfflineLogin ? false : scoreUsed > 0,
      useMaxScore: OffLineTool().isOfflineLogin ? false : _useAllScore,
      scoreUse: OffLineTool().isOfflineLogin ? 0 : scoreUsed,
      scoreConfiguration: scoreConfiguration,
      onManualPreferentialChange: (m) => manualPreferential = m,
      onScoreUseChange: (score) => scoreUsed = score,
      onCouponNotUse: (coupon) {
        if (null != coupon) {
          selectedCardList.remove(coupon);
        }
      },
    );
    reCalcStatisticAndResetSecondaryScreen();
    if (requestSearchFocus) {
      searchFocusNode.requestFocus();
    }
  }

  ///清除离线模式下的会员和促销数据
  void _clearOfflineModeData() {
    // 清除会员信息
    vipInfo = null;
    vipRightsCardList?.clear();
    selectedCardList.clear();

    // 清除积分相关
    scoreUsed = 0;
    _useAllScore = false;

    // 清除商品上的促销信息
    for (var goods in goodsBillDto.outDetail) {
      goods.promotionId = "";
      goods.promotionType = -1;
      goods.promotionHints?.clear();
      goods.preferentialHelp.clear();
      goods.calIntegral = true;
      goods.vipRights = true;
      goods.joinOrder = true;
    }

    // 清除单据级别的促销信息
    goodsBillDto.promotionHints?.clear();
    goodsBillDto.preferentialHelp.clear();
  }

  ///执行打折-》促销-》优惠分摊
  void startBillPreferential() {
    PreferentialUtil.startBillPreferential(
      bill: goodsBillDto,
      billType: billType,
      vipInfo: vipInfo,
      selectedCouponList: selectedCardList,
      manualPreferential: manualPreferential,
      enableScore: scoreUsed > 0,
      useMaxScore: _useAllScore,
      scoreUse: scoreUsed,
      scoreConfiguration: scoreConfiguration,
      onManualPreferentialChange: (m) => manualPreferential = m,
      onScoreUseChange: (score) => scoreUsed = score,
      onCouponNotUse: (coupon) {
        if (null != coupon) {
          selectedCardList.remove(coupon);
        }
      },
    );
    reCalcStatisticAndResetSecondaryScreen();
    searchFocusNode.requestFocus();
  }

  ///添加新商品
  @override
  void addGoodsDetails(List<GoodsDetailDto> details) {
    //合并商品，在执行促销前，合并第二件半价商品
    GoodsDetailDto? goods = addOrMergeGoodsList(
      details,
      mergeHalfPriceGoods: true,
    );
    if (goods != null) {
      indexScrollerController.scrollToIndex(goodsList.indexOf(goods));
    }
    //开始优惠流程
    startGoodsPreferential();
  }

  ///刷新商品列表
  @override
  void updateGoodsDetail({bool requestSearchFocus = true}) {
    SsCardDto? giftCoupon = selectedCardList.firstWhereOrNull(
      (coupon) => coupon.cardType == CardType.giftCoupon.value,
    );
    bool removeGiftCoupon = true;
    goodsBillDto.outDetail.removeWhere((goods) {
      // if (goods.unitQty == 0) return true;
      if (BillGoodsUtil.isCouponGift(goods)) {
        if (giftCoupon == null) {
          return true;
        } else {
          removeGiftCoupon = false;
        }
      }
      return false;
    });
    if (removeGiftCoupon && giftCoupon != null) {
      selectedCardList.remove(giftCoupon);
      goodsBillDto.preferentialHelp.remove(Preferential.giftCoupon.name);
    }
    //合并商品，在执行促销前，合并第二件半价商品
    addOrMergeGoodsList(goodsList, mergeHalfPriceGoods: true);
    //开始优惠流程
    startGoodsPreferential(requestSearchFocus: requestSearchFocus);
  }

  ///将扫码获取的数据根据条码成解析数据进行处理
  void handleBarcodeScaleDetail(
    List<GoodsDetailDto> details, {
    BarcodeScaleScan? barcodeScaleScan,
    BarcodeScaleConfig? barcodeScaleConfig,
  }) {
    if (null == barcodeScaleScan || null == barcodeScaleConfig) {
      addGoodsDetails(details);
      return;
    }
    GoodsDetailDto detailDto = details.first;

    ///当条码返回仅金额
    if (barcodeScaleConfig.outputFormatType ==
        BarcodeScaleTypeEnum.onlyTotal.index) {
      ///金额码解析和零售价配置是否有效
      if (!checkTotalValid(details, detailDto, barcodeScaleScan)) {
        return;
      }

      String qty = SystemConfigTool.doubleDivision(
        barcodeScaleScan.totalPosition,
        detailDto.currencyPrice.toString(),
        BillDecimalType.QTY,
      );
      BillPriceComputeHelper().priceCompute.onValueChange(
        detailDto,
        PtypePopValueChangeType.Qty,
        qty,
      );
      addGoodsDetails(details);
      return;
    }
    ///当条码返回仅重量
    else if (barcodeScaleConfig.outputFormatType ==
        BarcodeScaleTypeEnum.onlyWeight.index) {
      ///重量码解析和商品重量配置是否有效
      if (!checkWeightValid(details, detailDto, barcodeScaleScan)) {
        return;
      }

      String qty = SystemConfigTool.doubleDivision(
        barcodeScaleScan.weightPosition,
        detailDto.weight.toString(),
        BillDecimalType.QTY,
      );
      BillPriceComputeHelper().priceCompute.onValueChange(
        detailDto,
        PtypePopValueChangeType.Qty,
        qty.toString(),
      );
      addGoodsDetails(details);
      return;
    }
    ///当条码返回金额码+重量码/重量码+金额码时
    else if (barcodeScaleConfig.outputFormatType ==
            BarcodeScaleTypeEnum.weightAndTotal.index ||
        barcodeScaleConfig.outputFormatType ==
            BarcodeScaleTypeEnum.totalAndWeight.index) {
      ///重量码解析和零售价配置是否有效
      if (!checkWeightValid(details, detailDto, barcodeScaleScan)) {
        return;
      }

      ///金额码解析和零售价配置是否有效
      if (!checkTotalValid(details, detailDto, barcodeScaleScan)) {
        return;
      }

      ///条码秤中配置的零售价
      num barcodeScalePrice = SystemConfigTool.divisionToDecimalFromStr(
        barcodeScaleScan.totalPosition,
        barcodeScaleScan.weightPosition,
        BillDecimalType.PRICE,
      );
      num qty = SystemConfigTool.doubleDivisionToDecimal(
        num.parse(barcodeScaleScan.weightPosition),
        detailDto.weight!,
        BillDecimalType.QTY,
      );
      BillPriceComputeHelper().priceCompute.onValueChange(
        detailDto,
        PtypePopValueChangeType.Qty,
        qty.toString(),
      );

      ///通过条码秤返回的金额和重量计算的单价与商品单价不一致，将条码秤返回的单价赋值给售价，并标记为手工改价（保持与手工改价的逻辑一致）
      if (barcodeScalePrice != detailDto.currencyPrice) {
        BillPriceComputeHelper().priceCompute.onValueChange(
          detailDto,
          PtypePopValueChangeType.DISCOUNT_PRICE,
          barcodeScalePrice.toString(),
        );
      }

      if (detailDto.discount != 1) {
        BillPriceComputeHelper().priceCompute.onValueChange(
          detailDto,
          PtypePopValueChangeType.DISCOUNT,
          detailDto.discount.toString(),
        );

        ///标记成手工改价
        detailDto.manualPrice = true;
        detailDto.preferentialHelp = {};
        PreferentialDto preferentialDto = PreferentialDto();
        preferentialDto.discount = detailDto.discount;
        preferentialDto.total = num.parse(
          DecimalDisplayHelper.getTotalFixed(
            detailDto.preferentialDiscount.toString(),
          ),
        );
        preferentialDto.type = PreferentialType.Manual.value;

        detailDto.preferentialHelp[PreferentialDtoType.manualGoods] =
            preferentialDto;
      }

      addGoodsDetails(details);
      return;
    }
  }

  ///验证金额的解析和零售价
  bool checkTotalValid(
    List<GoodsDetailDto> details,
    GoodsDetailDto detailDto,
    BarcodeScaleScan barcodeScaleScan,
  ) {
    ///条码解析金额异常
    if (null == num.tryParse(barcodeScaleScan.totalPosition) ||
        0 == num.parse(barcodeScaleScan.totalPosition)) {
      handleScaleValid(
        details,
        msg: scaleExceptionMsg[ScaleExceptionType.scaleTotalError],
      );
      return false;
    }

    ///商品未设置零售价时
    if (detailDto.currencyPrice == 0) {
      handleScaleValid(
        details,
        msg: scaleExceptionMsg[ScaleExceptionType.priceError],
      );
      return false;
    }

    return true;
  }

  ///验证重量的解析和商品重量设置
  bool checkWeightValid(
    List<GoodsDetailDto> details,
    GoodsDetailDto detailDto,
    BarcodeScaleScan barcodeScaleScan,
  ) {
    ///条码解析重量异常
    if (null == num.tryParse(barcodeScaleScan.weightPosition) ||
        0 == num.parse(barcodeScaleScan.weightPosition)) {
      handleScaleValid(
        details,
        msg: scaleExceptionMsg[ScaleExceptionType.scaleWeightError],
      );
      return false;
    }

    ///商品未设置重量
    if (null == detailDto.weight || detailDto.weight == 0) {
      handleScaleValid(
        details,
        msg: scaleExceptionMsg[ScaleExceptionType.weightError],
      );
      return false;
    }
    return true;
  }

  ///条码配置，或者条码解析异常时，处理方式，默认添加一条数量为1的商品，客户手动维护数据
  void handleScaleValid(List<GoodsDetailDto> details, {required String? msg}) {
    HaloPosAlertDialog.showAlertDialog(
      context,
      content: msg,
      onCancelCallBack: () {
        searchFocusNode.requestFocus();
      },
      onSubmitCallBack: () {
        addGoodsDetails(details);
      },
    );
  }

  ///暂时不需要的逻辑，特殊情况都请客户手动维护数据，暂保留逻辑
  // ///条码秤返回仅金额，但零售价为0时的逻辑
  // ///逻辑：商品未设置零售价时，客户确认是否添加，需要添加，规则为：数量默认为1，金额不变，将金额赋给商品售价
  // void handleNoCurrentPrice(GoodsDetailDto detailDto,
  //     BarcodeScaleScan barcodeScaleScan, List<GoodsDetailDto> details) {
  //   HaloPosAlertDialog.showAlertDialog(context,
  //       content: "商品零售价为0，无法计算商品数量，请是否继续添加？", onCancelCallBack: () {
  //     searchFocusNode.requestFocus();
  //   }, onSubmitCallBack: () {
  //     BillPriceComputeHelper().priceCompute.onValueChange(
  //         detailDto,
  //         PtypePopValueChangeType.DISCOUNT_TAXED_PRICE,
  //         barcodeScaleScan.totalPosition);
  //
  //     ///标记成手工改价
  //     detailDto.manualPrice = true;
  //     detailDto.preferentialHelp = {};
  //     PreferentialDto preferentialDto = PreferentialDto();
  //     preferentialDto.total = num.parse(DecimalDisplayHelper.getTotalFixed(
  //         detailDto.preferentialDiscount.toString()));
  //     preferentialDto.type =
  //         PreferentialTool.preferentialType(PreferentialType.Manual);
  //     preferentialDto.discount = detailDto.discount;
  //     detailDto.preferentialHelp[PreferentialDtoType.manualGoods] =
  //         preferentialDto;
  //     addGoodsDetails(details);
  //   });
  // }

  // ///条码秤返回仅金额，但未设置weight逻辑
  // ///逻辑：重量为0，无法计算商品数量，是否继续添加，规则为：数量默认为1，金额不变，将金额赋给商品售价
  // void handleNoWeight(
  //     GoodsDetailDto detailDto,
  //     BarcodeScaleScan barcodeScaleScan,
  //     List<GoodsDetailDto> details,
  //     String tipMsg) {
  //   HaloPosAlertDialog.showAlertDialog(context, content: tipMsg,
  //       onCancelCallBack: () {
  //     searchFocusNode.requestFocus();
  //   }, onSubmitCallBack: () {
  //     addGoodsDetails(details);
  //   });
  // }

  //endregion
  //region dialog

  ///积分开启页面
  void showScoreView() {
    showDialog(
      context: context,
      builder:
          (context) => Scaffold(
            backgroundColor: Colors.transparent,
            body: UseScoreDialog(
              scoreConfiguration: scoreConfiguration ?? ScoreConfiguration(),
              goodsBillDto: goodsBillDto,
              useAllScore: _useAllScore,
              userScore: scoreUsed,
              scoreChange: (score, useAllScore) {
                scoreUsed = score;
                _useAllScore = useAllScore;
                //重新执行整单优惠流程
                startBillPreferential();
              },
              vipInfo: vipInfo!,
            ),
          ),
    );
  }

  ///手工整单优惠分摊
  ///preferentialTotal 排除 整单优惠、积分抵现
  void showManualPreferentialDialog() {
    Decimal max = BillPreferentialTool.getBillTotalBeforeBillPreferential(
      goodsBillDto,
      Preferential.billPreferential,
    );
    InputBoardPage.showAlertDialog(
      context,
      defaultText: manualPreferential.getIntWhenInteger.toString(),
      scale: SpTool.getSystemConfig().sysDigitalTotal,
      title: "总额优惠",
      maxValue: max.toDouble(),
    ).then((value) {
      if (value is String) {
        onBillPreferentialChange(value);
      }
    });
  }

  ///整单折扣弹窗
  void showBillDiscountDialog() {
    InputBoardPage.showAlertDialog(
      context,
      defaultText: billDiscount.toString(),
      scale: SpTool.getSystemConfig().sysDigitalDiscount,
      title: "整单折扣",
      tips: "说明:整单折扣不执行其他折扣促销",
      maxValue: 1,
    ).then((value) {
      if (value is String) {
        billDiscount = num.tryParse(value) ?? 1;
        ManualPriceUtil.setBillDiscount(goodsBillDto, billDiscount);
        updateGoodsDetail();
      }
    });
  }

  ///优惠券选择
  void showCouponCardView() {
    showDialog(
      context: context,
      builder:
          (context) => CardSelectDialog(
            vipId: vipInfo?.vip?.id ?? '',
            selectedList: selectedCardList,
          ),
    ).then((value) {
      if (value is List && value.isNotEmpty) {
        if (value.first is List<SsCardDto>) {
          onCardChanged(selectedCardList: value.first);
        }
        if (value.last is int) {
          cardLength = value.last;
        }
      }
    });
  }

  //endregion

  //region action
  ///选择赠品
  ///[promotionId] 如果不为空，则选择单个促销赠品
  void selectPromotionGift([String? promotionId]) {
    if (!PromotionUtil.canSelectGift(goodsBillDto, promotionId)) {
      HaloToast.show(context, msg: "已经达到赠品上限");
      return;
    }
    showDialog<Map<BillPromotionInfoDto, List<PromotionGift>>>(
      context: context,
      builder: (c) => PromotionGiftSelectDialog(bill: goodsBillDto),
    ).then((map) async {
      if (map == null || map.isEmpty) return;
      List<CouponGift> couponGiftList = [];
      List<GoodsDetailDto> goodsGiftList = [];
      for (var entry in map.entries) {
        if (entry.value.isEmpty) continue;
        BillPromotionInfoDto promotion = entry.key;
        bool isCouponGift = entry.value.first is CouponGift;
        if (isCouponGift) {
          couponGiftList.addAll(entry.value.cast());
        } else {
          List<GoodsDetailDto> list = entry.value.cast();
          goodsGiftList.addAll(list);
          //将商品赠品添加到促销最后一个商品后面
          PromotionUtil.addMultipleGoodsGift(
            originalGoodsList: goodsBillDto.outDetail,
            giftList: list,
            promotion: promotion,
            //在选择赠品页面已经处理过优惠信息和套餐，所以这里无需再处理
            comboDetailsMap: {},
            calculatePreferential: false,
          );
        }
      }
      try {
        if (goodsGiftList.isNotEmpty) {
          await ScanTool.getPtypeAutoBatch(goodsGiftList, context);
        }
      } finally {
        //合并优惠券
        PromotionUtil.mergeCouponGift(goodsBillDto, couponGiftList);
        //合并商品
        addOrMergeGoodsList(goodsBillDto.outDetail);
        //汇总优惠
        PreferentialUtil.collectBill(goodsBillDto, billType: billType);
        reCalcStatisticAndResetSecondaryScreen();
        searchFocusNode.requestFocus();
        setState(() {});
      }
    });
  }

  ///优惠明细弹窗
  void doPreferentialGoods() {
    HaloDialog(
      context,
      dismissOnTouchOutside: true,
      child: PreferentialDetailDialog(dataSource: goodsBillDto.outDetail),
    ).show();
  }

  ///查看促销赠送的优惠券
  void checkGiftCoupon() {
    List<String> cardModelIds =
        goodsBillDto.giftCouponList.map((e) => e.pid).toList();
    if (cardModelIds.isNotEmpty) {
      CardTemplateModel.getCardTemplateListByIds(context, cardModelIds).then((
        value,
      ) {
        if (value != null) {
          HaloDialog(
            context,
            dismissOnTouchOutside: true,
            child: GiftRightsDialog(
              goodsBillDto: goodsBillDto,
              cardTemplateList: value,
              callback: () {
                setState(() {});
              },
            ),
          ).show();
        }
      });
    } else {
      HaloDialog(
        context,
        dismissOnTouchOutside: true,
        child: GiftRightsDialog(
          goodsBillDto: goodsBillDto,
          cardTemplateList: const [],
          callback: () {},
        ),
      ).show();
    }
  }

  ///商品列表变动回调(编辑商品)
  void onGoodsListDataChange(GoodsBillDto goodsBill, bool resetFocus) {
    updateGoodsDetail(requestSearchFocus: resetFocus);
  }

  //region 挂单和取单

  /// 挂单和取单
  void doDraft() {
    if (goodsBillDto.outDetail.isEmpty) {
      //取单
      doGetDraft();
    } else {
      //挂单
      doSaveDraft();
    }
  }

  ///挂单
  void doSaveDraft() async {
    if (SpTool.getPermission().shopsalesalesettingsaveGraft == false) {
      HaloToast.showInfo(context, msg: "没有挂单权限");
      return;
    }
    if (!checkNextStep(goodsBillDto)) {
      return;
    }
    goodsBillDto = BillTool.setGoodsBill(
      goodsBillDto,
      vchtype: BillType.SaleBill,
    );
    BillTool.setGoodsBillDate(goodsBillDto);
    setDraftInfo();
    goodsBillDto.memo = goodsBillDto.tips;
    if (context.mounted) {
      goodsBillDto.payState = BillPayStateString[BillPayState.UnPay];
      goodsBillDto.postState = BillPostStateString[BillPostState.UNCONFIRMED];
      BillModel.saveGoodsBill(
        context,
        goodsBillDto,
        paySaveBillEnum: PaySaveBillEnum.COMMON,
      ).then((payResult) {
        BillSaveResultDto value = payResult!.resultDTO!;
        if (value.resultType == BillSaveResultType.SUCCESS) {
          loadData();
        } else {
          HaloToast.showInfo(context, msg: "挂单失败");
        }
      });
    }
  }

  ///挂单清除单据中不要的数据
  void setDraftInfo() {
    //清除优惠辅助和促销信息
    goodsBillDto.preferentialHelp.clear();
    goodsBillDto.promotionHints = null;
    goodsBillDto.giftCouponList.clear();
    //清除非手工改价赠品，保留手工改价的折扣，其他的折后还原为1
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    addOrMergeGoodsList(this.goodsList, mergeHalfPriceGoods: true);
    List<GoodsDetailDto> goodsList = GoodsTool.filterGoodsListAndGetCombo(
      goodsBillDto.outDetail,
      comboDetailsMap: comboDetailsMap,
      filter: (goods) {
        if (goods.memo == "手动修改零售价") {
          goods.memo = "";
        }

        //删除除了手工改价的全部赠品
        if (goods.gift && !goods.manualPrice) {
          return false;
        }
        PromotionUtil.cleanGoodsPromotion(goods);
        return true;
      },
    );
    for (var goods in goodsList) {
      if (GoodsTool.isComboDetail(goods)) continue;
      List<GoodsDetailDto> comboDetails = GoodsTool.getComboDetailsFromMap(
        goods,
        comboDetailsMap,
      );
      if (!goods.manualPrice) {
        goods.discount = 1;
        GoodsQtyUtil.onQtyChange(goods, goods.unitQty);
        if (goods.comboRow) {
          ComboPreferentialTool.handleComboDetail(
            goods,
            comboDetails,
            totalGetter: (g) => g.currencyTotal,
            totalSetter: (g, t) => GoodsTotalUtil.onCurrencyTotalChange(g, t),
          );
        }
      } else {
        ManualPriceUtil.setManualPrice(
          goods,
          comboDetails: comboDetails,
          setPreferential: false,
        );
      }
    }
    // 在挂单时判断手工改价商品并设置 discountSourceType = 3
    for (var goods in goodsList) {
      if (goods.manualPrice &&
          goods.preferentialHelp.containsKey('manualPrice')) {
        // 有手工改价且有优惠辅助信息，设置为指定折扣类型，用于取单时保护数据
        goods.discountSourceType = 3;
      }
    }

    goodsBillDto.outDetail.clear();
    goodsBillDto.outDetail.addAll(goodsList);
    reCalcStatistic();
    goodsBillDto.postState = BillPostStateString[BillPostState.UNCONFIRMED];
    goodsBillDto.vipCardId = vipInfo?.vip?.id;
  }

  ///取单
  void doGetDraft() async {
    if (SpTool.getPermission().shopsalesalesettinggetGraft == false) {
      HaloToast.showInfo(context, msg: "没有取单权限");
      return;
    }
    bool crossCash = SpTool.getStoreInfo()?.crossCashBill ?? false;
    if (context.mounted) {
      NavigateUtil.navigateTo(
        context,
        SaleOrderList(
          billType: OrderBillType.OrderSelling,
          crossCash: crossCash,
        ),
      ).then((value) {
        if (value != null) {
          onGetDraftData(value);
        }
      });
    }
  }

  ///取单回调
  void onGetDraftData(GoodsBillDto? value) async {
    if (value != null) {
      cleanVip(updateGoods: false);
      goodsBillDto = value;
      goodsBillDto.tips = goodsBillDto.memo;
      goodsBillDto.memo = "";
      goodsBillDto.ktypeId = SpTool.getStoreInfo()?.ktypeId;
      goodsBillDto.kfullname = SpTool.getStoreInfo()?.ktypeName;
      goodsBillDto.btypeId = SpTool.getStoreInfo()?.btypeId;
      goodsBillDto.bfullname = SpTool.getStoreInfo()?.btypeName;
      goodsBillDto.createEtypeId = LoginCenter.getLoginUser().employeeId;
      goodsBillDto.createEfullname = LoginCenter.getLoginUser().etypeName;
      goodsBillDto.createUserCode = LoginCenter.getLoginUser().userCode ?? '';
      goodsBillDto.otypeId = SpTool.getStoreInfo()?.otypeId;
      goodsBillDto.ofullname = SpTool.getStoreInfo()?.otypeFullname;
      VipWithLevelAssertsRightsCardDTO? vipInfo;
      if (goodsBillDto.vipBillInfo != null) {
        vipInfo = await SelectVipMixin.searchVip(
          context,
          phone: goodsBillDto.vipBillInfo!.phone,
          fuzzyQuery: false,
        );
      }
      if (context.mounted) {
        await BillTool.getPtypePrice(
          context,
          goodsBillDto.outDetail,
          calculateDiscount: false,
        );
      }
      Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
      GoodsTool.filterGoodsListAndGetCombo(
        goodsBillDto.outDetail,
        comboDetailsMap: comboDetailsMap,
      );
      for (GoodsDetailDto goods in goodsBillDto.outDetail) {
        ManualPriceUtil.onQtyChange(
          goods,
          goods.unitQty,
          comboDetails: GoodsTool.getComboDetailsFromMap(
            goods,
            comboDetailsMap,
          ),
        );
      }
      changeVip(vipInfo);

      // 取单完成后，重置手工改价商品的 discountSourceType，允许用户后续正常操作
      for (GoodsDetailDto goods in goodsBillDto.outDetail) {
        if (goods.discountSourceType == 3 && goods.manualPrice) {
          // 检查是否有手工改价的优惠辅助信息，如果有则保留标记，否则重置
          if (goods.preferentialHelp.containsKey('manualPrice')) {
            // 有手工改价优惠信息，保持 discountSourceType = 3，但允许后续修改
            // 这里可以添加一个标记表示取单已完成
            goods.discountSourceType = 0; // 重置为0，允许用户后续修改价格
          } else {
            // 没有手工改价优惠信息，直接重置
            goods.discountSourceType = 0;
          }
        }
      }
    }
  }

  //endregion 挂单和取单

  ///手工优惠分摊
  void onBillPreferentialChange(String text) {
    manualPreferential = num.tryParse(text) ?? 0;
    //执行整单优惠流程
    startBillPreferential();
  }

  ///备注弹窗
  void memoBill() {
    showDialog(
      context: context,
      builder: (c) {
        return AddTipsPage(
          defaultText: goodsBillDto.tips,
          submitCallback: (String text) {
            goodsBillDto.tips = text;
          },
        );
      },
    );
  }

  ///清空按钮点击事件
  void doClear() {
    goodsList.clear();
    goodsBillDto.tips = "";
    goodsBillDto.memo = "";
    goodsBillDto.couponMemo = "";
    goodsBillDto.giftCouponList.clear();
    goodsBillDto.preferentialHelp.clear();
    goodsBillDto.promotionGiftRecord = null;
    goodsBillDto.outDetail.clear();
    scoreUsed = 0;
    manualPreferential = 0;
    billDiscount = 1;
    cleanVip(updateGoods: false);
    reCalcStatisticAndResetSecondaryScreen();
  }

  ///打印上单
  void doBeforeBill() {
    String lastVchcode = SpTool.getLastVchCode()!;
    if (StringUtil.isZeroOrEmpty(lastVchcode)) {
      HaloToast.show(context, msg: "不存在上单");
      return;
    }
    PrintTool.printBillByVchCode(
      context,
      billType,
      vchCode: lastVchcode,
      printCount: 1,
    );
  }

  ///打印预结单
  void printPreview() {
    if (goodsList.isEmpty || !BillIsolateTool().isIsoFinish) {
      return;
    }
    PrintTool.printBillPreView(
      context,
      billType,
      GoodsBillDto.fromMap(goodsBillDto.toJson())
        //预结单时，没有vchtype和vchcode
        ..vchtype = BillTypeData[BillType.SaleBill]!,
    );
  }

  ///当优惠券被选中或删除，调用此方法，拉去优惠券对应的权益列表，重新进行价格计算
  ///[chooseGift] 删除优惠券时也会调用此回调，此时无需弹窗选择赠品
  Future<void> onCardChanged({
    required List<SsCardDto> selectedCardList,
    bool chooseGift = true,
  }) async {
    List<SsCardDto> oldCouponList = [...this.selectedCardList];
    this.selectedCardList = selectedCardList;
    //找有之前选择的礼品券，和现在选的礼品券，做比对，若发生改变，则删除之前的礼品券对应的商品
    SsCardDto? oldGiftCoupon = CardUtil.getCardFromCardType(
      oldCouponList,
      CardType.giftCoupon.value,
    );
    SsCardDto? newGiftCoupon = CardUtil.getCardFromCardType(
      selectedCardList,
      CardType.giftCoupon.value,
    );
    List<GoodsDetailDto>? giftList = await CouponUtil.chooseGiftByGiftCoupon(
      context,
      goodsBillDto,
      newGiftCoupon,
      oldGiftCoupon: oldGiftCoupon,
      chooseGift: chooseGift,
    );
    if (giftList != null && giftList.isNotEmpty) {
      if (mounted) {
        await ScanTool.getPtypeAutoBatch(giftList, context);
      }
      //id不同，无需合并
      if (oldGiftCoupon?.id != newGiftCoupon?.id) {
        goodsList.addAll(giftList);
      } else {
        addOrMergeGoodsList(giftList);
      }
    } else {
      if (newGiftCoupon != null) {
        //id不同，或者商品列表中没有次提货券的赠品，则移除此券
        if ((oldGiftCoupon?.id != newGiftCoupon.id) ||
            CouponUtil.getExistGiftCount(goodsBillDto) == Decimal.zero) {
          this.selectedCardList.remove(newGiftCoupon);
        }
      }
    }
    //如果折扣券发生改变，则从打折开始执行
    SsCardDto? oldDiscountCoupon = CardUtil.getCardFromCardType(
      oldCouponList,
      CardType.discountCoupon.value,
    );
    SsCardDto? newDiscountCoupon = CardUtil.getCardFromCardType(
      selectedCardList,
      CardType.discountCoupon.value,
    );
    if (oldDiscountCoupon?.id != newDiscountCoupon?.id) {
      startGoodsPreferential();
      return;
    }
    //否则若代金券发生改变，则从整单优惠开始执行
    SsCardDto? oldAmountCoupon = CardUtil.getCardFromCardType(
      oldCouponList,
      CardType.amountCoupon.value,
    );
    SsCardDto? newAmountCoupon = CardUtil.getCardFromCardType(
      selectedCardList,
      CardType.amountCoupon.value,
    );
    if (oldAmountCoupon?.id != newAmountCoupon?.id) {
      startBillPreferential();
      return;
    }
    //没有执行优惠流程，则汇总单据
    PreferentialUtil.collectBill(goodsBillDto, billType: billType);
    reCalcStatisticAndResetSecondaryScreen();
  }

  ///跳转到结算页面
  @override
  Future<void> doSubmit(BuildContext context) async {
    // BillDBManager.deleteSaleBillFromProfileId(profileId: LoginCenter.getLoginUser().profileId!);
    if (completer != null) {
      return;
    }
    if (!BillIsolateTool().isIsoFinish) {
      ///计算未完成，弹窗等待计算完成
      HaloDialog(
        context,
        isShowProgress: true,
        dismissOnTouchOutside: false,
      ).show();
      completer = Completer<void>();
      await completer?.future;
      if (context.mounted) Navigator.pop(context);
      completer = null;
    }
    if (context.mounted) {
      await super.doSubmit(context);
    }
  }

  ///处理开单资产变动
  void buildSettlementInfo(GoodsBillDto bill) {
    bill.vipAsserts = [];
    if (vipInfo == null) {
      for (var goods in bill.outDetail) {
        goods.giveVipScore = false;
      }
      bill.giveScoreMoney = 0;
      return;
    }
    //赠送积分的金额
    Decimal? giveScoreMoney = BillTool.getGiveScoreMoney(
      goodsBill: bill,
      billType: billType,
      scoreConfiguration: scoreConfiguration,
    );
    if (giveScoreMoney < Decimal.zero) {
      giveScoreMoney = null;
    }
    bill.giveScoreMoney = giveScoreMoney?.toDouble();
    //使用的积分数，抵扣积分数
    if (scoreUsed > 0) {
      bill.vipAsserts!.add(
        BillVipAssertChange(
          qty: -scoreUsed,
          typed: 0,
          memo: "积分抵扣",
          changeType: 6,
        ),
      );
    }
    //会员使用的优惠劵
    List<String> usedCardIds = CouponUtil.getUsedCouponIds(
      bill,
      selectedCardList,
    );
    if (usedCardIds.isNotEmpty) {
      bill.vipAsserts!.addAll(
        usedCardIds.map(
          (e) => BillVipAssertChange(
            qty: -1,
            assertId: e,
            typed: 4,
            memo: "使用优惠券",
            changeType: 6,
          ),
        ),
      );
    }
    //促销赠送的优惠券
    if (goodsBillDto.giftCouponList.isNotEmpty) {
      bill.vipAsserts!.addAll(
        goodsBillDto.giftCouponList
            .where((element) => element.unitQty > 0)
            .map(
              (e) => BillVipAssertChange(
                qty: e.unitQty,
                cardTemplateId: e.pid,
                typed: 4,
                memo: "促销赠送优惠券",
                changeType: 5,
              ),
            ),
      );
    }
  }

  @override
  Future showSettlementDialog({required GoodsBillDto bill}) {
    buildSettlementInfo(bill);
    return HaloDialog(
      context,
      dismissOnTouchOutside: true,
      child: SaleBillSettlementPage(
        goodsBillDto: bill,
        vipInfo: vipInfo,
        successCallback: onPaySuccess,
      ),
    ).show();
  }

  @override
  void onPaySuccess() {
    if (OffLineTool().isOfflineLogin) {
      OfflineDialogTool.showSyncBillDialog(context);
    }
    goodsList.clear();
    goodsBillDto.giftCouponList.clear();
    goodsBillDto.outDetail.clear();
    manualPreferential = 0;
    loadData();
  }

  //endregion
  //region 快捷支付

  Map payInfo = {};
  bool isPaying = false;
  Function? scanPaySuccess;

  ///扫码支付成功后的操作
  void setScanPaySuccess() {
    scanPaySuccess = () {
      goodsList.clear();
      goodsBillDto.giftCouponList.clear();
      goodsBillDto.outDetail.clear();
      manualPreferential = 0;
      loadData();
    };
  }

  ///点击了扫码支付
  void quickScanPayBill() async {
    if (OffLineTool().isOfflineLogin) {
      HaloToast.showMsg(context, msg: "离线模式不可用");
      return;
    }
    if (completer != null) {
      return;
    }
    if (!BillIsolateTool().isIsoFinish) {
      ///计算未完成，弹窗等待计算完成
      HaloDialog(
        context,
        isShowProgress: true,
        dismissOnTouchOutside: false,
      ).show();
      completer = Completer<void>();
      await completer?.future;
      if (context.mounted) Navigator.pop(context);
      completer = null;
    }
    if (!checkNextStep(goodsBillDto)) {
      return;
    }

    List<StorePayway> storePaywayList = SpTool.getStoreInfo()?.paywayList ?? [];
    List<StorePayway> paymentList =
        storePaywayList.where((element) => element.paywayType == 2).toList();
    if (paymentList.length > 1) {
      if (context.mounted) {
        HaloToast.showMsg(context, msg: "存在多个扫码支付方式，请使用收银功能在结算界面选择后扫码");
      }
      return;
    } else if (paymentList.isEmpty) {
      if (context.mounted) {
        HaloToast.showMsg(context, msg: "暂无可用的扫码支付");
      }
      return;
    }
    // if (needResetVchcode) {
    //   goodsBillDto.vchcode = null;
    //   goodsBillDto.number = null;
    // }

    BillTool.setGoodsBill(goodsBillDto, vchtype: BillType.SaleBill);

    BillTool.setGoodsBillDefaultValue(
      goodsBillDto,
      StoreEtype()
        ..etypeId = SpTool.getEtypeId()
        ..etypeName = SpTool.getEtypeName(),
    );

    GoodsBillDto goodsBill = GoodsBillDto.fromMap(goodsBillDto.toJson());
    goodsBill.vipCardId = vipInfo?.vip?.id;

    ///将支付方式提交上去
    _setPayment(goodsBill, paymentList);

    ///添加备注
    goodsBill.memo = BillTool.buildMemo(goodsBillDto, vipInfo);

    BillTool.setGoodsBillDate(goodsBill);

    _onLineSubmitBill(goodsBill);
  }

  ///设置账户信息,这里的支付方式只有扫码支付，和结算页面不同
  _setPayment(GoodsBillDto goodsBill, List<StorePayway> storePaywayList) {
    Map atype = BillTool.getAtypeMessage(goodsBill, billType);
    goodsBill.payment =
        storePaywayList.where((element) => element.paywayType == 2).map((e) {
          PaymentDto payment =
              PaymentDto()
                ..afullnameCation = atype["afullnameCation"]
                ..atypeTotalCation = atype["atypeTotalCation"]
                ..atypeId = e.atypeId
                ..atypeFullName = e.atypeFullname
                ..paywayId = e.paywayId
                ..paywayType = e.paywayType
                ..paywayFullname = e.paywayName
                ..currencyAtypeTotal = goodsBill.currencyBillTotal;
          return payment;
        }).toList();
  }

  ///有网络提交
  _onLineSubmitBill(GoodsBillDto goodsBill) {
    //校验单据
    PaymentUtil.validaGoodsBill(context, goodsBill, (value) {
      buildSettlementInfo(goodsBill);
      payOrderBill(context, goodsBill);
    });
  }

  @override
  paymentSuccessCallBack() {
    if (scanPaySuccess != null) {
      scanPaySuccess!();
    }
    return super.paymentSuccessCallBack();
  }

  //endregion
}

enum ScaleExceptionType {
  ///未设置重量
  weightError,

  ///未设置零售价
  priceError,

  ///条码重量码解析为0
  scaleWeightError,

  ///条码金额码解析为0
  scaleTotalError,
}

const Map<ScaleExceptionType, String> scaleExceptionMsg = {
  ScaleExceptionType.weightError: "商品重量未配置,添加后数量为【1】，需手动完善信息。请确认是否继续添加？",
  ScaleExceptionType.priceError: "商品零售价未配置,添加后数量为【1】，需手动完善信息。请确认是否继续添加？",
  ScaleExceptionType.scaleWeightError:
      "重量解析为0，请确认条码称和后台配置是否一致。添加后数量为【1】需手动完善信息，请确认是否继续添加?",
  ScaleExceptionType.scaleTotalError:
      "金额解析为0，请确认条码称和后台配置是否一致。添加后数量为【1】需手动完善信息，请确认是否继续添加?",
};
