import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:halo_utils/navigator/navigate_util.dart';

import '../../../common/tool/sp_tool.dart';
import '../../../home/<USER>/menu.dart';
import '../../../hotkey/hotkey.dart';
import '../../../hotkey/hotkey_manager.dart';
import '../../bill/channel/bill_channel_orders_query.dart';
import '../../settlement/widget/add_tips_page.dart';
import 'sale_business_mixin.dart';

mixin HotkeyMixin<T extends StatefulWidget> on SaleBusinessMixin<T> {
  @override
  void initState() {
    super.initState();
    //在设置页面中，有两种开单页面可以切换，切换过程中，是新的开单页面的init先触发，而后旧的开单页面触发dispose，会导致新页面注册的回调被清空，所以这里添加一个延时
    Future.delayed(
      const Duration(milliseconds: 500),
    ).then((value) => registerHotkeyCallback());
  }

  @override
  void dispose() {
    debugPrint("注销热键");
    HotkeyManager.unregisterAllCallback();
    super.dispose();
  }

  ///注册快捷键，
  ///商品选择功能，只有扫码开单界面才有
  void registerHotkeyCallback() {
    debugPrint("注册热键");
    for (var type in HotkeyType.values.where(
      (element) => element != HotkeyType.selectGoods,
    )) {
      HotkeyManager.registerCallback(type, onHotkeyCall);
    }
  }

  ///当回调无法触发，或回调事件已经被消费掉，则返回true。子类继承此方法可根据返回值来判断是否继续触发
  bool onHotkeyCall(HotkeyType hotkeyType) {
    //只有开单页面在栈顶才会触发热键回调
    if (!mounted || ModalRoute.of(context)?.isCurrent != true) return true;
    switch (hotkeyType) {
      //挂/取单
      case HotkeyType.saveDraft:
        doDraft();
        return true;
      //打开钱箱
      case HotkeyType.openCashBox:
        if (SpTool.getPermission().shopsalecashboxopen == true) {
          Menu.openCashBox(context);
        }
        return true;
      //交接班
      case HotkeyType.shiftChange:
        if (SpTool.getPermission().shopsaleshiftchangesview == true) {
          Menu.exitLogin(context);
        }
        return true;
      //选择会员
      case HotkeyType.selectVip:
        if (vipInfo == null) {
          onVipClick(context);
        }
        return true;
      //库存查询
      case HotkeyType.stockQuery:
        if (SpTool.getPermission().analysiscloudinventoryPositionview == true) {
          Menu.showInventory(context);
        }
        return true;
      //整单优惠
      case HotkeyType.orderPreferential:
        if(manualPreferentialView) {
          showManualPreferentialDialog();
        }
        return true;
      //单据备注
      case HotkeyType.orderMemo:
        showDialog(
          context: context,
          builder:
              (c) => AddTipsPage(
                defaultText: goodsBillDto.tips,
                submitCallback: (String text) => goodsBillDto.tips = text,
              ),
        );
        return true;
      //自提核销
      case HotkeyType.channel:
        NavigateUtil.navigateTo(context, const BillChannelOrdersQuery());
        return true;
      //扫码支付
      case HotkeyType.scanPay:
        quickScanPayBill();
        return true;
      //收银
      case HotkeyType.settlement:
        doSubmit(context);
        return true;
      //退货
      case HotkeyType.backBill:
        Menu.openSaleBack(context);
        return true;
      //换货
      case HotkeyType.changeBill:
        Menu.openSaleChange(context);
        return true;
      //打印预结单
      case HotkeyType.printPreBill:
        printPreview();
        return true;
      //整单折扣
      case HotkeyType.orderDiscount:
        if (billDiscountPermission) {
          showBillDiscountDialog();
        }
        return true;
      default:
        return false;
    }
  }
}
