import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';

import '../../../bill/entity/base_table_content.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../tool/promotion/promotion.dart';
import '../sale/promotion_gift_select.dart';

/// 创建时间：2/15/23
/// 作者：xiaotiaochong
/// 描述：

///促销提醒
mixin PromotionTipsMixin<T extends StatefulWidget> on State<T> {
  ///促销提醒
  buildPromotionHints(List<PromotionHints> promotionHintsList,
      GoodsDetailDto goodsDetailDto, BaseTableContentConfig config,
      {double leftPadding = 12}) {
    List<Widget> children = [];
    for (var e in promotionHintsList) {
      children.add(_buildPromotionHintsDetail(e));
    }
    return Container(
        padding: children.isEmpty
            ? EdgeInsets.zero
            : EdgeInsets.only(top: 12.w, left: leftPadding.w, right: 12.w),
        child: Column(
          children: children,
        ));
  }

  _buildPromotionHintsDetail(PromotionHints promotionHints) {
    Color color = AppColors.promotionTipsFontColor;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
              color: color,
              borderRadius: const BorderRadius.all(Radius.circular(2))),
          padding: EdgeInsets.symmetric(horizontal: 2.w),
          margin: EdgeInsets.only(right: 4.w),
          child: Text(
            promotionHints.typeName,
            style: TextStyle(
                color: Colors.white, fontSize: AppPosSize.describeFontSize.sp),
          ),
        ),
        HaloPosLabel(
          promotionHints.hints,
          textStyle:
              TextStyle(color: color, fontSize: AppPosSize.contentFontSize.sp),
        ),
        if (promotionHints.showDetail)
          GestureDetector(
            child: Text(
              "查看详情",
              style: TextStyle(
                color: AppColors.secondaryFontColor,
                fontSize: AppPosSize.contentFontSize.sp,
                decoration: TextDecoration.underline,
                decorationStyle: TextDecorationStyle.solid,
                decorationThickness: 1.h,
              ),
            ),
            onTap: () {
              showDialog(
                  context: context,
                  builder: (c) => PromotionGiftCheckDialog(
                      promotionId: promotionHints.promotionId,
                      giftScope: promotionHints.promotionGiftScope));
            },
          )
      ],
    );
  }
}
