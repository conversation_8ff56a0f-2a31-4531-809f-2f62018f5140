import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import '../../../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../../../bill/model/ptype_model.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../login/entity/store/store_info.dart';
import '../../../startup.dart';
import '../../entity/bill_ptype_request.dart';

mixin SelectGoodsAndComboMixin<T extends StatefulWidget> on State<T> {
  ///搜索关键字
  String get filterValue;

  ///商品类型
  String? get typeId => null;

  ///是否按照商品维度进行搜索
  bool get searchByPtype => false;

  ///分页大小
  static const pageSize = 40;

  ///页码
  int pageIndex = 1;

  ///是否有更多数据
  bool _hasMore = true;

  ///得到的商品和套餐列表
  final List<PtypeListModel> goodsAndComboList = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) => refresh());
  }

  ///刷新控件
  Widget buildRefreshWidget() {
    return EasyRefresh(
      header: MaterialHeader(),
      footer: MaterialFooter(),
      behavior: Platform.isWindows ? WindowsScrollBehavior() : null,
      firstRefresh: false,
      onRefresh: () async => refresh(),
      onLoad: _hasMore && pageIndex != 1 ? request : null,
      child: buildGoodsList(),
    );
  }

  ///列表
  Widget buildGoodsList();

  ///刷新
  void refresh() {
    pageIndex = 1;
    request();
  }

  ///获取数据
  Future<void> request() async {
    StoreInfo storeInfo = SpTool.getStoreInfo()!;
    BillPtypeRequest paramRequest = BillPtypeRequest();
    paramRequest.btypeId = storeInfo.btypeId;
    paramRequest.otypeId = storeInfo.otypeId;
    paramRequest.ktypeId = storeInfo.ktypeId;
    paramRequest.filterValue = filterValue;
    paramRequest.typeId = typeId;
    paramRequest.searchByPtype = searchByPtype;

    List<PtypeListModel> ptypeList = await PtypeModel.selectPtypeAndCombo(
        context,
        queryParam: paramRequest,
        pageIndex: pageIndex,
        pageSize: pageSize);
    if (mounted) {
      setState(() {
        if (pageIndex == 1) {
          goodsAndComboList.clear();
        }
        _hasMore = ptypeList.length >= pageSize;
        goodsAndComboList.addAll(ptypeList);
        pageIndex++;
      });
    }
  }
}
