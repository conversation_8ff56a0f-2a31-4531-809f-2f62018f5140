import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../common/tool/dialog_util.dart';
import '../../widgets/halo_pos_label.dart';

//region 数据模型
/// 支付错误弹窗数据模型
/// [errorTitle] 错误标题，可选
/// [errorDesc] 错误描述，必填
/// [onActionListener] 按钮点击回调，可选
class PaymentErrorDialogData {
  final String? errorTitle;
  final String errorDesc;
  final Function(int)? onActionListener;

  PaymentErrorDialogData({
    this.errorTitle,
    required this.errorDesc,
    this.onActionListener,
  });
}
//endregion

//region 支付错误弹窗
/// 支付错误弹窗组件
/// 用于显示支付过程中的错误信息和操作选项
class PaymentErrorDialog extends StatefulWidget {
  /// 错误标题
  final String? errorTitle;

  /// 错误描述
  final String errorDesc;

  /// 按钮点击回调
  final Function(int)? onActionListener;

  /// 内容更新通知器
  final ValueNotifier<PaymentErrorDialogData>? notifier;

  /// 弹窗关闭回调
  final VoidCallback? onDialogClose;

  const PaymentErrorDialog({
    Key? key,
    this.errorTitle,
    required this.errorDesc,
    this.onActionListener,
    this.notifier,
    this.onDialogClose,
  }) : super(key: key);

  @override
  State<PaymentErrorDialog> createState() => _PaymentErrorDialogState();
}

class _PaymentErrorDialogState extends State<PaymentErrorDialog> {
  /// 当前错误标题
  late String? _errorTitle;

  /// 当前错误描述
  late String _errorDesc;

  /// 当前按钮点击回调
  late Function(int)? _onActionListener;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _setupNotifierListener();
  }

  /// 初始化数据
  void _initializeData() {
    _errorTitle = widget.errorTitle;
    _errorDesc = widget.errorDesc;
    _onActionListener = widget.onActionListener;
  }

  /// 设置通知器监听
  void _setupNotifierListener() {
    widget.notifier?.addListener(_updateContent);
  }

  @override
  void dispose() {
    widget.notifier?.removeListener(_updateContent);
    super.dispose();
  }

  /// 更新弹窗内容
  void _updateContent() {
    if (widget.notifier != null) {
      setState(() {
        _errorTitle = widget.notifier!.value.errorTitle;
        _errorDesc = widget.notifier!.value.errorDesc;
        _onActionListener = widget.notifier!.value.onActionListener;
      });
    }
  }

  final divider = const Divider(height: 1, color: Color(0xFFEEEEEE));

  //region UI构建方法
  /// 构建标题部分
  Widget _buildTitle() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Center(
          child: Text(
            _errorTitle ?? "交易失败",
            style: TextStyle(
              fontSize: 35.sp,
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建内容部分
  Widget _buildContent() {
    return Flexible(
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 45.w, right: 45.w, bottom: 5.w),
              child: HaloPosLabel(
                "提示信息：$_errorDesc",
                textStyle: TextStyle(fontSize: 25.sp, color: Colors.black),
                maxLines: 10,
              ),
            ),
            SizedBox(height: 20.h),
            Padding(
              padding: EdgeInsets.all(15.w),
              child: HaloPosLabel(
                "提示：如果跟顾客确认已收到钱款，可手动完成收款",
                textStyle: TextStyle(
                  fontSize: 25.sp,
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建按钮部分
  Widget _buildButtons() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: 18.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Expanded(
            child: HaloButton(
              backgroundColor: ColorUtil.stringColor("#4679FC"),
              height: 80.w,
              onPressed: () {
                Navigator.pop(context);
                _onActionListener?.call(0);
              },
              text: "已收到钱款",
            ),
          ),
          SizedBox(width: 20.w),
          Expanded(
            child: HaloButton(
              buttonType: HaloButtonType.outlinedButton,
              outLineWidth: 2.w,
              borderColor: ColorUtil.stringColor("#999999"),
              backgroundColor: ColorUtil.stringColor("#ffffff"),
              textColor: ColorUtil.stringColor("#999999"),
              height: 80.w,
              onPressed: () {
                DialogUtil.showAlertDialog(
                  context,
                  child: PaymentErrorConfirmedDialog(
                    onActionListener: (int index) {
                      Navigator.pop(context);
                      _onActionListener?.call(1);
                    },
                  ),
                );
              },
              text: "未收到钱款",
            ),
          ),
        ],
      ),
    );
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.75;

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) {
          // 通知外部弹窗即将关闭
          if (widget.onDialogClose != null) {
            widget.onDialogClose!();
          }
        }
      },
      child: Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 750.w, maxHeight: maxHeight),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildTitle(),
              _buildContent(),
              divider,
              _buildButtons(),
            ],
          ),
        ),
      ),
    );
  }
}
//endregion

//region 支付错误确认弹窗
/// 支付错误确认弹窗组件
/// 用于确认用户是否确实未收到款项
class PaymentErrorConfirmedDialog extends StatefulWidget {
  /// 按钮点击回调
  final Function(int)? onActionListener;

  const PaymentErrorConfirmedDialog({Key? key, this.onActionListener})
    : super(key: key);

  @override
  State<PaymentErrorConfirmedDialog> createState() =>
      _PaymentErrorConfirmedDialogState();
}

class _PaymentErrorConfirmedDialogState
    extends State<PaymentErrorConfirmedDialog> {
  /// 构建标题部分
  Widget _buildTitle() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Center(
          child: Text(
            "提示",
            style: TextStyle(
              fontSize: 32.sp,
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxHeight = screenHeight * 0.75;

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 750.w, maxHeight: maxHeight),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTitle(),
            Flexible(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(25.w),
                  child: HaloPosLabel(
                    "未收到钱款会删除当前单据，需要重新发起收银结算，确定要取消吗？",
                    textAlign: TextAlign.center,
                    textStyle: TextStyle(fontSize: 27.sp, color: Colors.black),
                    maxLines: 2,
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: 18.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Expanded(
                    child: HaloButton(
                      backgroundColor: ColorUtil.stringColor("#4679FC"),
                      height: 80.w,
                      onPressed: () {
                        Navigator.pop(context);
                        widget.onActionListener?.call(1);
                      },
                      text: "确定",
                    ),
                  ),
                  SizedBox(width: 20.w),
                  Expanded(
                    child: HaloButton(
                      buttonType: HaloButtonType.outlinedButton,
                      outLineWidth: 2.w,
                      borderColor: ColorUtil.stringColor("#999999"),
                      backgroundColor: ColorUtil.stringColor("#ffffff"),
                      textColor: ColorUtil.stringColor("#999999"),
                      height: 80.w,
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      text: "取消",
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

//endregion
