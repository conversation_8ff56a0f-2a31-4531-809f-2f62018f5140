import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../iconfont/icon_font.dart';
import 'package:haloui/haloui.dart';

///无码商品界面，在此界面输入无码商品的单价
///点击确定，返回一个单价的无码商品

class InputGoodsPriceDialog extends StatelessWidget {
  const InputGoodsPriceDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      constrainedAxis: Axis.vertical,
      child: SizedBox(
        width: 463.w,
        child: const Dialog(
            insetPadding: EdgeInsets.zero, child: _InputGoodsPricePage()),
      ),
    );
  }

  ///由于无码商品需要系统后台配置一个无码商品，
  ///在弹出无码商品弹出之前，需要判断是否有无码商品的这个id，
  ///若没有，则提示用户创建无码商品
  static bool isAllowedNoCodeGoods(BuildContext context) {
    if (SpTool.getStoreInfo()?.noBarPtypeId?.isNotEmpty == true) {
      return true;
    }
    HaloToast.show(context, msg: "还未配置无码商品");
    return false;
  }
}

class _InputGoodsPricePage extends StatefulWidget {
  const _InputGoodsPricePage({Key? key}) : super(key: key);

  @override
  _InputGoodsPricePageState createState() => _InputGoodsPricePageState();
}

class _InputGoodsPricePageState extends State<_InputGoodsPricePage> {
  String? _price = "0";

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      width: 463.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitle(context),
          _buildPrice(context),
          _buildKeyboard(context)
        ],
      ),
    );
  }

  ///标题栏
  Widget _buildTitle(BuildContext context) {
    return Column(
      children: [
        HaloContainer(
          height: 69.h,
          padding: EdgeInsets.only(left: 20.w, right: 21.w),
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              "无码商品",
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xff333333),
                  fontSize: 24.sp),
            ),
            GestureDetector(
              child: IconFont(IconNames.close, size: 30.w),
              onTap: () => Navigator.pop(context),
            )
          ],
        ),
        _buildDivider()
      ],
    );
  }

  ///分割线
  Widget _buildDivider({bool horizontal = true}) {
    const color = Color(0xffe9e9e9);
    return horizontal
        ? Divider(height: 1.h, color: color)
        : VerticalDivider(width: 1.w, color: color);
  }

  ///价格
  Widget _buildPrice(BuildContext context) {
    return HaloContainer(
      height: 137.h,
      padding: EdgeInsets.only(left: 32.w),
      children: [
        Text("单价",
            style: TextStyle(
                color: const Color(0xff333333),
                fontSize: 26.sp,
                fontWeight: FontWeight.bold)),
        Expanded(
          child: Center(
            child: Text(
              _price ?? "",
              style: TextStyle(
                  backgroundColor: const Color(0xffb2cdfc),
                  color: const Color(0xff333333),
                  fontWeight: FontWeight.bold,
                  fontSize: 28.sp),
              maxLines: 1,
            ),
          ),
        ),
      ],
    );
  }

  ///小键盘
  Widget _buildKeyboard(BuildContext context) {
    return Column(
      children: [
        //分割线
        _buildDivider(),
        //123x
        _buildRow(context, [_Key.one, _Key.two, _Key.three, _Key.back]),
        //分割线
        _buildDivider(),
        //456清空
        _buildRow(context, [_Key.four, _Key.five, _Key.six, _Key.clear]),
        Row(children: [
          Expanded(
            child: Column(children: [
              //分割线
              _buildDivider(),
              //789
              _buildRow(context, [_Key.seven, _Key.eight, _Key.nine]),
              //分割线
              _buildDivider(),
              //. 0 00
              _buildRow(
                context,
                [_Key.dot, _Key.zero, _Key.doubleZero],
              ),
            ]),
          ),
          //确定
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: Container(
              height: 156.h,
              width: 116.w,
              color: const Color(0xFF2869FD),
              alignment: Alignment.center,
              child: Text("确定",
                  style: TextStyle(fontSize: 26.sp, color: Colors.white)),
            ),
            onTap: () => _onConfirmPressed(context),
          ),
        ]),
      ],
    );
  }

  ///构建一行小按钮
  Widget _buildRow(BuildContext context, List<_Key> list) {
    final children = <Widget>[];
    for (int i = 0; i < list.length; i++) {
      if (i != 0) {
        children.add(_buildDivider(horizontal: false));
      }
      // children.add(Expanded(child: _buildKey(context, list[i]), flex: 1));
      children.add(_buildKey(context, list[i]));
    }
    return SizedBox(
      height: 77.h,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: children,
      ),
    );
  }

  ///构建小按钮
  Widget _buildKey(BuildContext context, _Key key) {
    final isBack = key == _Key.back;
    final isClear = key == _Key.clear;
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: isClear
            ? _onClearPressed
            : isBack
                ? _onBackPressed
                : () => _onNumPressed(key),
        child: Container(
          alignment: Alignment.center,
          height: double.infinity,
          width: 115.w,
          child: isBack
              ? Icon(
                  Icons.backspace_outlined,
                  color: const Color(0xff333333),
                  size: 37.w,
                )
              : Text(key.getString(),
                  style: TextStyle(
                      color: const Color(0xff333333),
                      fontWeight: isClear ? FontWeight.normal : FontWeight.bold,
                      fontSize: (isClear ? 24 : 26).sp)),
        ));
  }

  void _onClearPressed() {
    _price = null;
    _checkPriceEmpty();
    setState(() {});
  }

  ///点击了回退
  void _onBackPressed() {
    if (_price != null && _price?.isNotEmpty == true) {
      _price = _price!.substring(0, _price!.length - 1);
    }
    _checkPriceEmpty();
    setState(() {});
  }

  ///检查输入的值是否为空
  void _checkPriceEmpty() {
    //这里先要求，若字符串为空，则展示一个0
    if (_price == null || _price?.isEmpty == true) {
      _price = "0";
    }
  }

  ///点击了数字和小数点
  void _onNumPressed(_Key key) {
    //点击了小数点，需要先判断是否已经包含小数点
    if (key == _Key.dot) {
      //已经包含小数点，不响应
      if (_price!.contains(".")) {
        return;
      }
    }
    _price = _price! + key.getString();
    Characters characters;
    //排除字符串第一位为0，第二位不为小数点的情况
    while ((characters = _price!.characters).length > 1 &&
        characters.characterAt(0).string == "0" &&
        characters.characterAt(1).string != ".") {
      _price = _price!.substring(1);
    }
    _checkNumberLength();
    setState(() {});
  }

  ///规定整数部分最大长度为12位，小数部分读取配置
  void _checkNumberLength() {
    final split = _price!.split(".");
    var integer = split[0];
    if (integer.length > 12) {
      integer = integer.substring(0, 12);
    }
    if (split.length <= 1) {
      _price = integer;
    } else {
      //小数位数
      final digital = SpTool.getSystemConfig()?.sysDigitalTotal ?? 2;
      var float = split[1];
      if (float.length > digital) {
        float = float.substring(0, digital);
      }
      _price = "$integer.$float";
    }
  }

  ///点击了确定按钮
  void _onConfirmPressed(BuildContext context) {
    double price;
    try {
      price = double.parse(_price!);
    } catch (e) {
      price = 0;
    }
    final ptypeId = SpTool.getStoreInfo()?.noBarPtypeId;
    final currencyPrice = price;
    final goods = GoodsDetailDto.noCodeGoods(ptypeId!, currencyPrice);
    Navigator.pop(context, goods);
  }
}

enum _Key {
  ///1
  one,

  ///2
  two,

  ///3
  three,

  ///4
  four,

  ///5
  five,

  ///6
  six,

  ///7
  seven,

  ///8
  eight,

  ///9
  nine,

  ///.
  dot,

  ///0
  zero,

  ///00
  doubleZero,

  ///回退
  back,

  ///清空
  clear
}

extension _KeyExtension on _Key {
  String getString() {
    switch (this) {
      case _Key.one:
        return "1";
      case _Key.two:
        return "2";
      case _Key.three:
        return "3";
      case _Key.four:
        return "4";
      case _Key.five:
        return "5";
      case _Key.six:
        return "6";
      case _Key.seven:
        return "7";
      case _Key.eight:
        return "8";
      case _Key.nine:
        return "9";
      case _Key.zero:
        return "0";
      case _Key.doubleZero:
        return "00";
      case _Key.dot:
        return ".";
      case _Key.clear:
        return "清空";
      default:
        return "";
    }
  }
}
