import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/model/card_template_model.dart';
import '../../../bill/widget/sale/gift_rights_detail_dialog.dart';
import '../../../iconfont/icon_font.dart';
import '../../../vip/entity/card_coupon_card_template.dart';
import '../../../widgets/base/halo_pos_alert_dialog.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../tool/promotion/promotion.dart';
import 'dashed_line.dart';

///促销赠送的优惠券展示列表
class GiftRightsDialog extends StatelessWidget {
  final GoodsBillDto goodsBillDto;
  final List<CardCouponCardTemplate> cardTemplateList;
  final Function callback;

  const GiftRightsDialog(
      {Key? key,
      required this.goodsBillDto,
      required this.cardTemplateList,
      required this.callback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      constrainedAxis: Axis.vertical,
      child: SizedBox(
        child: Dialog(
            insetPadding: EdgeInsets.zero,
            child: _GiftRightsDialog(goodsBillDto, cardTemplateList, callback)),
      ),
    );
  }
}

class _GiftRightsDialog extends StatefulWidget {
  final GoodsBillDto goodsBillDto;
  final List<CardCouponCardTemplate> cardTemplateList;
  final Function deleteCallback;

  const _GiftRightsDialog(
      this.goodsBillDto, this.cardTemplateList, this.deleteCallback);

  @override
  State<StatefulWidget> createState() {
    return _GiftRightsDialogState();
  }
}

class _GiftRightsDialogState extends State<_GiftRightsDialog> {
  ///是否是编辑模式
  bool isEditing = false;

  List<CouponGift> get giftCouponList => widget.goodsBillDto.giftCouponList;

  final Map<String, CardCouponCardTemplate> cardTemplateMap = {};

  @override
  void initState() {
    super.initState();
    for (var template in widget.cardTemplateList) {
      if (template.id == null) continue;
      cardTemplateMap.putIfAbsent(template.id!, () => template);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 980.w,
      height: 850.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(),
          Divider(
            height: 1.h,
            color: ColorUtil.stringColor("#000000", alpha: 0.1),
          ),
          _buildGridTitle(),
          _buildGrid(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      margin: EdgeInsets.only(left: 28.w),
      height: 80.h,
      child: Row(
        children: [
          Expanded(
              child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              HaloPosLabel(
                "赠送权益  ",
                textStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 28.sp,
                    color: const Color(0xFF333333)),
              ),
              HaloPosLabel(
                "(长按进入或退出删除模式)",
                textStyle: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 20.sp,
                    color: const Color(0xFF888888)),
              ),
            ],
          )),
          GestureDetector(
            child: Container(
                width: 80.w,
                height: 80.h,
                alignment: Alignment.center,
                child: IconFont(
                  IconNames.close,
                  size: 34.w,
                  color: ColorUtil.color2String(Colors.black),
                )),
            onTap: () => NavigateUtil.pop(context),
          ),
        ],
      ),
    );
  }

  Widget _buildGridTitle() {
    return Container(
      margin: EdgeInsets.only(left: 28.w, top: 24.h),
      height: 60.h,
      child: HaloPosLabel(
        "优惠券",
        textStyle: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 24.sp,
            color: const Color(0xFF333333)),
      ),
    );
  }

  Widget _buildGrid() {
    List<Widget> children =
        giftCouponList.map((e) => _buildGridItem(e)).toList();
    return Expanded(
        child: Container(
            margin: EdgeInsets.symmetric(horizontal: 28.w),
            child: GridView(
              gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                maxCrossAxisExtent: 350.w,
                mainAxisSpacing: 12.w,
                crossAxisSpacing: 20.w,
                childAspectRatio: 2 / 1,
              ),
              children: children,
            )));
  }

  Widget _buildGridItem(CouponGift couponGift) {
    CardCouponCardTemplate cardTemplate =
        cardTemplateMap[couponGift.pid] ?? CardCouponCardTemplate();
    String endDate = "";
    if (cardTemplate.validType == 1) {
      endDate = "领券后【${cardTemplate.validValue}】天";
    } else {
      endDate = "固定到期:${cardTemplate.validValue}";
    }

    String colorString = "";
    String cardType = "";
    if (cardTemplate.cardType == 2) {
      cardType = "代金券";
      colorString = "#E96A6A";
    } else if (cardTemplate.cardType == 3) {
      cardType = "折扣券";
      colorString = "#E1B278";
    } else if (cardTemplate.cardType == 4) {
      cardType = "礼品券";
      colorString = "#54D5C1";
    }
    String oFullNames =
        cardTemplate.ofullNames == "" ? "全部门店" : cardTemplate.ofullNames ?? "";
    GlobalKey detailViewKey = GlobalKey();
    return GestureDetector(
      child: Stack(
        key: detailViewKey,
        children: [
          Container(
              decoration: BoxDecoration(
                border: Border.all(
                    width: 1.w, color: ColorUtil.stringColor("#E8E8E8")),
                borderRadius: BorderRadius.circular(8),
              ),
              clipBehavior: Clip.hardEdge,
              margin: EdgeInsets.symmetric(vertical: 8.h),
              child: Row(
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                            alignment: Alignment.topLeft,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.only(
                                    bottomRight: Radius.circular(8)),
                                color: ColorUtil.stringColor(colorString),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 6.w),
                              child: HaloPosLabel(
                                cardType,
                                textStyle: TextStyle(
                                    color: Colors.white, fontSize: 16.sp),
                              ),
                            )),
                        Container(
                            padding: EdgeInsets.symmetric(horizontal: 6.w),
                            child: HaloPosLabel(
                              "优惠券名字:${cardTemplate.fullname}",
                              textStyle: TextStyle(
                                  fontWeight: FontWeight.w500, fontSize: 22.sp),
                            )),
                        Container(
                            padding: EdgeInsets.symmetric(horizontal: 6.w),
                            child: HaloPosLabel(
                              "到期日期:$endDate",
                              textStyle: TextStyle(
                                  fontWeight: FontWeight.normal,
                                  fontSize: 16.sp,
                                  color: ColorUtil.stringColor("#000000",
                                      alpha: 0.3)),
                            )),
                        Container(
                            padding: EdgeInsets.symmetric(horizontal: 6.w),
                            child: HaloPosLabel(
                              "可用门店:$oFullNames",
                              textStyle: TextStyle(
                                  fontWeight: FontWeight.normal,
                                  fontSize: 16.sp,
                                  color: ColorUtil.stringColor("#000000",
                                      alpha: 0.3)),
                            )),
                        Expanded(child: Container())
                      ],
                    ),
                  ),
                  Container(
                      alignment: Alignment.topCenter,
                      width: 20.w,
                      height: 60.h,
                      child: DashedLine(
                        axis: Axis.vertical,
                        count: 10,
                        dashedWidth: 1.w,
                        dashedHeight: 1.h,
                        color: Colors.black,
                      )),
                  Container(
                    alignment: Alignment.center,
                    width: 40.w,
                    child: HaloPosLabel(
                      "x${couponGift.unitQty}",
                      textStyle: TextStyle(fontSize: 22.sp),
                    ),
                  ),
                ],
              )),
          if (isEditing)
            GestureDetector(
              child: Container(
                  alignment: Alignment.topRight,
                  child: IconFont(
                    IconNames.shanchu_2,
                    size: 20.w,
                    // color: ColorUtil.color2String(Colors.white),
                  )),
              onTap: () {
                HaloPosAlertDialog.showAlertDialog(context,
                    dismissOnTouchOutside: false,
                    dismissOnBackKeyPress: false,
                    content: "是否删除该项权益?", onSubmitCallBack: () {
                  setState(() => giftCouponList.remove(couponGift));
                  widget.deleteCallback();
                });
              },
            )
        ],
      ),
      onTap: () {
        CardTemplateModel.getCardTemplateById(context, couponGift.pid)
            .then((value) {
          if (value != null) {
            HaloPopWindow().show(detailViewKey,
                intervalLeft: 0,
                backgroundColor: Colors.transparent,
                gravity: PopWindowGravity.bottom,
                child: GiftRightsDetailDialog(value, "到期日期:$endDate"));
          }
        });
      },
      onLongPressStart: (_) => setState(() => isEditing = !isEditing),
    );
  }

}
