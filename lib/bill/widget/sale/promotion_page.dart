import 'package:flutter/material.dart';
import '../../../bill/tool/sale_event_buds.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../../../bill/entity/bill_promotion_info_dto.dart';
import '../../../bill/model/promotion_model.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/promotion_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../model/bill_model.dart';
import '../../tool/promotion/promotion.dart';

/// 创建时间：12/8/21
/// 作者：xiaotiaochong
/// 描述：促销查看页面

class PromotionPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _PromotionPageState();
  }
}

class _PromotionPageState extends State<PromotionPage> {
  List<BillPromotionInfoDto> datas = [];

  @override
  void initState() {
    super.initState();
    datas = SpTool.getPromotionList();
  }

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      child: HaloContainer(
        width: 1000.w,
        height: 800.h,
        color: Colors.white,
        border: Border.all(color: AppColors.dividerColor, width: 1),
        borderRadius: BorderRadius.all(Radius.circular(4.w)),
        mainAxisSize: MainAxisSize.max,
        direction: Axis.vertical,
        children: [
          _buildTitle(context),
          _buildList(context),
        ],
      ),
    );
  }

  ///标题栏和关闭按钮
  Widget _buildTitle(BuildContext context) {
    return Column(
      children: [
        HaloContainer(
          height: 80.h,
          padding: EdgeInsets.only(left: 20.w, right: 0.w),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text("促销活动",
                style: TextStyle(
                    color: AppColorHelper(context).getTitleBoldTextColor(),
                    fontSize: 28.sp)),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: Container(
                width: 60,
                alignment: Alignment.center,
                child: IconFont(
                  IconNames.close,
                ),
              ),
              onTap: () => Navigator.pop(context),
            )
          ],
        ),
        //分割线
        Divider(height: 1.h, color: const Color(0xFFDBDBDB)),
        Container(
          margin: EdgeInsets.only(top: 16.w),
          child: HaloLabel(
            "——————————下滑刷新促销活动——————————",
            textStyle: TextStyle(
                color: AppColorHelper(context).getNormalTitleTextColor(),
                fontSize: 14.sp),
          ),
        )
      ],
    );
  }

  Widget _buildList(BuildContext context) {
    return Expanded(
        child: HaloList(
      datas,
      buildItemContent: (buildContext, index) {
        return _buildListItem(buildContext, index);
      },
      hasMoreData: false,
      onRefresh: () async {
        List<BillPromotionInfoDto>? promotionList =
            await PromotionModel.getFullPromotionList(context);
        if (promotionList != null) {
          SpTool.savePromotionList(promotionList);
          setState(() {
            datas = SpTool.getPromotionList();
            SaleEventBus.getInstance().fire(SaleEventBus.updateGoodsDetail);
          });
        }
        if(mounted){
         await BillModel.getPromotionAutomation(context)
              .then((value) {
           autoChoosePromotionGift = value;
         });
        }
      },
    ));
  }

  Widget _buildListItem(BuildContext context, int index) {
    BillPromotionInfoDto model = datas[index];
    String name = "${model.fullname}";
    String otypeName = "促销门店:${model.filterNames}";
    String date =
        "活动时间:${DateUtil.formatDateStr(model.startDate, format: DateFormats.y_mo_d)}~${DateUtil.formatDateStr(model.endDate, format: DateFormats.y_mo_d)}  每天${model.startTime}~${model.endTime}";
    String way = "促销方式:${model.promotionTypeName}";
    return GestureDetector(
      child: HaloContainer(
        direction: Axis.horizontal,
        mainAxisSize: MainAxisSize.max,
        margin:
            EdgeInsets.only(top: 18.h, bottom: 18.h, left: 30.w, right: 30.h),
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 16.h),
        border: Border.all(color: AppColors.dividerColor, width: 1.5.w),
        borderRadius: BorderRadius.all(Radius.circular(4.w)),
        width: 200.w,
        children: [
          IconFont(
            PromotionTypeIcon[model.promotionType] ?? IconNames.cuxiaojia,
            size: 60.w,
          ),
          Expanded(
              child: HaloContainer(
            direction: Axis.vertical,
            color: Colors.white,
            margin: EdgeInsets.only(left: 30.w),
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              HaloPosLabel(
                name,
                textStyle: TextStyle(
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  fontSize: 26.sp,
                ),
              ),
              SizedBox(
                height: 16.h,
              ),
              HaloPosLabel(otypeName,
                  textStyle: TextStyle(
                      color: AppColorHelper(context).getNormalTitleTextColor(),
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500)),
              HaloPosLabel(way,
                  textStyle: TextStyle(
                      color: AppColorHelper(context).getNormalTitleTextColor(),
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500)),
              HaloPosLabel(date,
                  textStyle: TextStyle(
                      color: AppColorHelper(context).getNormalTitleTextColor(),
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500))
            ],
          ))
        ],
      ),
      onTap: () {
        _onClickListItem(model);
      },
    );
  }

  _onClickListItem(BillPromotionInfoDto model) {
  }
}
