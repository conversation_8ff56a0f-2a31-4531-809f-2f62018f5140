import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;
import 'package:haloui/utils/math_util.dart';

import '../../../common/keyboard_hidden.dart';
import '../../../common/num_extension.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../tool/promotion/card.dart';
import '../../tool/promotion/promotion.dart';

///分割线
Widget get divider => Divider(height: 2.h, color: const Color(0xFFD0D0D0));

///构建按钮
buildBottomButton(
  String text, {
  num margin = 20,
  Color? color,
  Color? textColor,
  Color? borderColor,
  HaloButtonType? buttonType,
  VoidCallback? onTap,
}) {
  return Container(
    margin: EdgeInsets.only(right: margin.w),
    child: HaloButton(
      buttonType: buttonType ?? HaloButtonType.elevatedButton,
      borderColor: borderColor ?? Colors.transparent,
      outLineWidth: 2.w,
      text: text,
      textColor: textColor ?? Colors.white,
      width: 180.w,
      height: 66.h,
      fontSize: 18.sp,
      backgroundColor: color,
      onPressed: onTap,
    ),
  );
}

abstract class SelectTabPageState<T, W extends StatefulWidget>
    extends State<W> {
  final KeyboardHiddenFocusNode _searchFocusNode = KeyboardHiddenFocusNode();

  final String searchBarHint = "";

  ///左右内边距
  double get paddingHorizontal => 20.w;

  ///全部的赠品列表
  final List<GiftWrapper<T>> allGiftList = [];

  ///展示的赠品列表
  final List<GiftWrapper<T>> showGiftList = [];

  ///剩余可选赠品数量
  num remainCount = 0;

  ///赠品最大数量
  num get maxCount;

  ///数量小数位数
  int get qtyDigital => 0;

  ///初始化赠品列表
  List<GiftWrapper<T>> initGiftList();

  @override
  void initState() {
    super.initState();
    allGiftList.addAll(initGiftList());
    showGiftList.addAll(allGiftList);
    remainCount = maxCount;
  }

  ///构建搜索框
  Widget buildSearch() {
    return HaloContainer(
      margin:
          EdgeInsets.symmetric(horizontal: paddingHorizontal, vertical: 20.h),
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      height: 40.h,
      borderRadius: const BorderRadius.all(Radius.circular(2.0)),
      children: [
        IconFont(
          IconNames.ngp_search,
          color: "#67686A",
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: KeyboardHiddenTextField(
            hint: "扫描选择赠品",
            focusNode: _searchFocusNode,
            style: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 28.sp),
            onSubmitted: onSearch,
          ),
        )
      ],
    );
  }

  ///构建列表
  Widget buildList() {
    return ListView.separated(
        itemBuilder: buildItem,
        separatorBuilder: (context, index) => divider,
        itemCount: showGiftList.length);
  }

  ///构建单个赠品
  Widget buildItem(BuildContext context, int index);

  ///构建计数器
  Widget buildCountStepper(GiftWrapper<T> gift) {
    return Container(
      height: 40.h,
      alignment: Alignment.center,
      child: HaloCountStepper(
        decimalCount: qtyDigital,
        notifier: HaloCountStepperNotifier(
            max: SpTool.getSystemConfig().sysGlobalDecimalMax,
            defaultValue: gift.count),
        enable: true,
        inputBackgroundColor: Colors.white,
        textColor: Colors.black,
        inputFontWeight: FontWeight.w600,
        inputMinWidth: 90.w,
        inputFontSize: 22.sp,
        borderRadius: BorderRadius.circular(6.w),
        border: Border.all(color: AppColors.txtBorderColor, width: 1),
        textColorDiy: true,
        addIcon: Container(
          height: 40.w,
          width: 40.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.w),
              border: Border.all(color: AppColors.txtBorderColor, width: 1)),
          child: Text(
            "+",
            style: TextStyle(color: const Color(0xFF606060), fontSize: 24.sp),
          ),
        ),
        subIcon: Container(
          height: 40.w,
          width: 40.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.w),
              border: Border.all(color: AppColors.txtBorderColor, width: 1)),
          child: Text(
            "-",
            style: TextStyle(color: const Color(0xFF606060), fontSize: 30.sp),
          ),
        ),
        onChanged: (value) {
          onCountChange(gift, value.toString());
        },
      ),
    );
  }

  ///构建随机按钮
  Widget buildRandomButton() {
    return buildBottomButton("随机赠送", onTap: () {
      RandomGiftUtil.randomChooseGift<GiftWrapper<T>>(
          maxQty: remainCount,
          giftList: allGiftList,
          onChooseGift: (gift, count) {
            gift.count = MathUtil.addDec(gift.count, count).toDouble();
          });
      remainCount = 0;
      setState(() {});
      //触发回调
      onGiftSelect();
    });
  }

  ///搜索赠品
  void onSearch(String text) {
    setState(() {
      showGiftList.clear();
      if (text.isEmpty) {
        showGiftList.addAll(allGiftList);
      } else {
        showGiftList.addAll(allGiftList.where((e) => filterGift(e, text)));
      }
    });
  }


  bool filterGift(GiftWrapper<T> gift, String text) => true;

  ///赠品数量发生改变
  void onCountChange(GiftWrapper<T> gift, String value) {
    num count = num.tryParse(value) ?? 0;
    gift.count = count;
    //其他商品之和
    Decimal anotherTotalCount = allGiftList
        .where((element) => element != gift)
        .fold<Decimal>(
            Decimal.zero,
            (previousValue, element) =>
                previousValue + Decimal.parse(element.count.toString()));
    Decimal countTotal = Decimal.parse(count.toString()) + anotherTotalCount;
    Decimal sub = Decimal.parse(maxCount.toString()) - countTotal;
    if (sub < Decimal.zero) {
      HaloToast.showMsg(context, msg: "数量已达最大值");
      gift.count =
          (Decimal.parse(maxCount.toString()) - anotherTotalCount).toDouble();
      remainCount = 0;
    } else {
      remainCount = sub.toDouble();
    }
    setState(() {});
    //触发回调
    onGiftSelect();
  }

  static num getRemainCount<T>(List<GiftWrapper<T>> list, num maxCount) {
    Decimal allCount = list.fold<Decimal>(
        Decimal.zero,
        (previousValue, element) =>
            previousValue + Decimal.parse(element.count.toString()));
    Decimal sub = Decimal.parse(maxCount.toString()) - allCount;
    if (sub < Decimal.zero) {
      return 0;
    } else {
      return sub.toDouble();
    }
  }

  ///赠品被选择的回调
  void onGiftSelect();
}

///选择赠品弹窗基类
abstract class PromotionSelectTabPageState<T, W extends StatefulWidget>
    extends SelectTabPageState<T, W> {
  @override
  String get searchBarHint => "扫描选择赠品";

  ///是否可以编辑
  final bool editable = true;

  ///数量小数位数
  @override
  int get qtyDigital => SpTool.getSystemConfig().sysDigitalQty;

  String? getGiftFullbarcode(T gift);

  ///获取赠品名称(仅名称)
  String? getGiftName(T gift);

  ///获取赠品名称
  String? getGiftNameStr(T gift) => getGiftName(gift);

  ///赠品类型是否是优惠券
  bool isCouponGift(T gift) => false;

  ///赠品优惠券类型
  CardType? getGiftCardType(T gift) => null;

  ///获取赠品图片路径
  String getGiftImageUrl(T gift);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (editable) ...[
          SizedBox(height: 20.h),
          Text("还可选${remainCount.getIntWhenInteger}件赠品"),
          divider,
        ],
        buildSearch(),
        divider,
        Expanded(child: buildList()),
        if (editable) ...[
          SizedBox(height: 10.h),
          buildRandomButton(),
          SizedBox(height: 10.h),
        ],
      ],
    );
  }

  ///构建单个赠品
  @override
  Widget buildItem(BuildContext context, int index) {
    GiftWrapper<T> gift = showGiftList[index];
    return HaloContainer(
        height: 80.h,
        direction: Axis.horizontal,
        mainAxisSize: MainAxisSize.max,
        padding:
            EdgeInsets.symmetric(vertical: 6.h, horizontal: paddingHorizontal),
        children: [
          _buildGiftPicture(gift.gift),
          Expanded(child: _buildGiftName(gift.gift)),
          if (editable) buildCountStepper(gift)
        ]);
  }

  ///构建优惠券图片
  Widget buildCouponGiftPicture(CardType? cardType) {
    switch (cardType) {
      case CardType.amountCoupon:
        return Image.asset('assets/images/daijinquan.png');
      case CardType.discountCoupon:
        return Image.asset('assets/images/dikouquan.png');
      case CardType.giftCoupon:
        return Image.asset('assets/images/lipinquan.png');
      default:
        return Image.asset('assets/images/nodata.png');
    }
  }
  @override
  filterGift(GiftWrapper<T> gift, String text){
    return  getGiftName(gift.gift)?.contains(text) == true ||
        getGiftFullbarcode(gift.gift)?.contains(text) == true;
  }

  ///赠品图片
  Widget _buildGiftPicture(T gift) {
    Widget image;
    if (isCouponGift(gift)) {
      image = buildCouponGiftPicture(getGiftCardType(gift));
    } else {
      image = HaloImage(getGiftImageUrl(gift));
    }
    return SizedBox(
      width: 120.w,
      height: 60.h,
      child: image,
    );
  }

  ///赠品名称
  Widget _buildGiftName(T gift) {
    return Container(
      margin: EdgeInsets.only(left: 10.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HaloPosLabel(
            getGiftNameStr(gift) ?? "",
            textStyle: TextStyle(fontSize: 18.sp),
          ),
          HaloPosLabel(
            getGiftFullbarcode(gift) ?? "",
            textStyle: TextStyle(fontSize: 18.sp),
          )
        ],
      ),
    );
  }
}

///促销赠品包装，包装赠品和数量
class GiftWrapper<T> {
  final T gift;

  ///已选数量
  num count = 0;

  GiftWrapper(this.gift, [this.count = 0]);
}
