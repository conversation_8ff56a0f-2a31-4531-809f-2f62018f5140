import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;

import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/ss_card_dto.dart';
import '../../../bill/tool/bill_tool.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../entity/preferential_dto.dart';
import '../../tool/promotion/preferential.dart';
import '../../tool/promotion/price.dart';
import '../../tool/props_helper.dart';
import '../ptype/base_goods_dialog.dart';
import 'select_gift.dart';

///提货券选择赠品
class CouponGiftSelectPage extends StatefulWidget {
  ///礼品券
  final SsCardDto couponEntity;

  const CouponGiftSelectPage(
      {Key? key, required this.couponEntity, this.maxQty = 0})
      : super(key: key);

  final num maxQty;

  @override
  State<CouponGiftSelectPage> createState() {
    return _CouponGiftSelectPageState();
  }
}

class _CouponGiftSelectPageState
    extends BaseGoodsDialogState<CouponGiftSelectPage> {
  final List<GiftWrapper<PtypeListBean>> giftList = [];

  @override
  double get height => 900.h;

  @override
  String get title => "选择赠品";

  @override
  double get width => 600.w;

  @override
  void initState() {
    super.initState();
    // element.changeToGoodsDetailDto(widget.couponEntity)
    if (widget.couponEntity.memberEquityValues.isNotEmpty &&
        widget.couponEntity.memberEquityValues.first.detailList.isNotEmpty &&
        widget.couponEntity.cardDetails.isNotEmpty) {
      for (var element in widget
          .couponEntity.memberEquityValues.first.detailList.first.ptypeList) {
        giftList.add(GiftWrapper(element));
      }
    }
    if (giftList.isEmpty) {
      HaloToast.showMsg(context, msg: "无可用赠品");
    }
  }

  @override
  Widget buildContent(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h),
          child: Row(
            children: [
              HaloPosLabel(
                "已选礼品券:【${widget.couponEntity.fullname}】",
                textStyle: TextStyle(color: Colors.orange, fontSize: 18.sp),
              ),
              HaloPosLabel("可以在下列商品中选择礼品",
                  textStyle: TextStyle(fontSize: 18.sp)),
              // HaloPosLabel("【$maxCountString】",
              //     textStyle: TextStyle(color: Colors.blue, fontSize: 18.sp)),
            ],
          ),
        ),
        if (widget.maxQty <= 0 || giftList.isEmpty)
          Expanded(child: Container()),
        if (widget.maxQty > 0 || giftList.isNotEmpty)
          Expanded(child: _SubPage(giftList, widget.maxQty)),
        divider,
        Padding(
          padding: EdgeInsets.symmetric(vertical: 10.h),
          child: _buildBottom(),
        )
      ],
    );
  }

  Widget _buildBottom() {
    return HaloContainer(
      margin: EdgeInsets.all(20.w),
      height: 80.h,
      direction: Axis.horizontal,
      children: [
        buildBottomButton("取消",
            margin: 160,
            color: Colors.white,
            textColor: Colors.black,
            buttonType: HaloButtonType.outlinedButton,
            borderColor: ColorUtil.stringColor("999999"),
            onTap: () => Navigator.pop(context)),
        buildBottomButton("确定", margin: 0, onTap: _doSubmit),
      ],
    );
  }

  void _doSubmit() {
    List<GoodsDetailDto> goodsList = giftList
        .where((element) => element.count > 0)
        .map((e) => e.gift.changeToGoodsDetailDto()..unitQty = e.count)
        .toList();
    String typeId = widget.couponEntity.cardDetails.first.id;
    BillTool.getPtypePrice(context, goodsList).then((value) {
      for (var goods in goodsList) {
        goods.discount = 0;
        GoodsQtyUtil.onQtyChange(goods, goods.unitQty);
        PreferentialHelper.setGoodsPreferential(
            goods, Preferential.giftCoupon, goods.currencyTotal,
            typeId: typeId);
      }
      Navigator.pop(context, goodsList);
    });
  }
}

///选择赠品页面
class _SubPage extends StatefulWidget {
  final num maxCount;
  final List<GiftWrapper<PtypeListBean>> giftList;

  const _SubPage(this.giftList, this.maxCount);

  @override
  State<_SubPage> createState() => _SubPageState();
}

class _SubPageState
    extends PromotionSelectTabPageState<PtypeListBean, _SubPage> {
  @override
  String? getGiftFullbarcode(PtypeListBean gift) => gift.fullbarcode;

  @override
  String getGiftImageUrl(PtypeListBean gift) {
    String? picUrl = gift.picUrl;
    picUrl = (picUrl != null && !picUrl.contains("http"))
        ? SpTool.getQiNiuThumbnail(picUrl, true)
        : picUrl;
    return picUrl ?? "";
  }

  @override
  String? getGiftName(PtypeListBean gift) => gift.ptypeName;

  @override
  String getGiftNameStr(PtypeListBean gift) {
    GoodsDetailDto temp = gift.changeToGoodsDetailDto();
    String prop = PropsHelper.buildGoodsPropertyString(temp.prop ?? []);
    return "${gift.fullname ?? ""} $prop ${gift.unitName ?? ""}";
  }

  @override
  List<GiftWrapper<PtypeListBean>> initGiftList() => widget.giftList;

  @override
  num get maxCount => widget.maxCount;

  @override
  void onGiftSelect() {}
}
