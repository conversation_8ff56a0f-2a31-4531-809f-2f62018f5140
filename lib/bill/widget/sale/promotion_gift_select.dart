import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../common/tool/sp_tool.dart';
import '../../entity/bill_promotion_info_dto.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../tool/promotion/card.dart';
import '../../tool/promotion/promotion.dart';
import '../ptype/base_goods_dialog.dart';
import 'select_gift.dart';

///选择赠品的回调
typedef _OnGiftSelectCallback = void Function(
    BillPromotionInfoDto promotion, List<GiftWrapper<PromotionPtype>> giftList);

//region 选择多个促销赠品的弹窗
///促销选择赠品弹窗
class PromotionGiftSelectDialog extends StatefulWidget {
  ///单据
  final GoodsBillDto bill;

  ///单个促销的id，用于选择单个促销的赠品
  final String? promotionId;

  const PromotionGiftSelectDialog(
      {Key? key, required this.bill, this.promotionId})
      : super(key: key);

  @override
  State<PromotionGiftSelectDialog> createState() =>
      _PromotionGiftSelectDialogState();
}

class _PromotionGiftSelectDialogState
    extends BaseGoodsDialogState<PromotionGiftSelectDialog>
    with SingleTickerProviderStateMixin {
  @override
  double get height => 900.h;

  @override
  String get title => "选择赠品";

  @override
  double get width => 600.w;

  GoodsBillDto get bill => widget.bill;

  ///在此页面中选择的赠品
  final Map<String, List<GiftWrapper<PromotionPtype>>> selectedGiftMap = {};

  ///标题
  final List<Widget> tabTitleList = [];

  ///子页面
  final List<_PromotionSelectTabPage> _tabPageList = [];

  ///促销map
  final Map<String, BillPromotionInfoDto> promotionMap = {};

  late final TabController _controller;

  @override
  void initState() {
    //找到已有赠品的数量并记录
    //已有赠品数量map,key为促销id
    Map<String, num> existGiftCountMap = {};
    for (var goods in bill.outDetail) {
      if (goods.gift &&
          goods.promotionGift &&
          goods.promotionGiftScope == PromotionGiftScope.chooseGoods.value &&
          StringUtil.isNotZeroOrEmpty(goods.promotionId)) {
        num count = existGiftCountMap[goods.promotionId] ?? 0;
        count = MathUtil.addDec(count, goods.unitQty).toDouble();
        existGiftCountMap[goods.promotionId] = count;
      }
    }
    for (var coupon in bill.giftCouponList) {
      if (StringUtil.isNotZeroOrEmpty(coupon.promotionId)) {
        num count = existGiftCountMap[coupon.promotionId] ?? 0;
        count = MathUtil.addDec(count, coupon.unitQty).toDouble();
        existGiftCountMap[coupon.promotionId] = count;
      }
    }

    //获取全部促销
    promotionMap
        .addAll(PromotionUtil.groupByPromotionId(SpTool.getPromotionList()));
    //遍历促销记录，筛选促销，获取促销赠品数量
    Set<PromotionGiftRecord> recordSet = bill.promotionGiftRecord ?? {};
    for (var record in recordSet) {
      //单个促销的赠品选择
      if (widget.promotionId != null &&
          record.promotionId != widget.promotionId) {
        continue;
      }
      if (record.giftCount <= 0 ||
          StringUtil.isZeroOrEmpty(record.promotionId)) {
        continue;
      }
      final promotion = promotionMap[record.promotionId];
      if (promotion == null) continue;
      num count = record.giftCount;
      //找出促销已有赠品数量，这里选择赠品的数量=最大数量-已有赠品数量
      num? existCount = existGiftCountMap[record.promotionId];
      if (existCount != null) {
        count = MathUtil.subtractDec(count, existCount).toDouble();
      }
      if (count <= 0) continue;
      //构建tab标题
      tabTitleList.add(Text(promotion.fullname ?? "",
          style: const TextStyle(color: Colors.black)));
      //记录促销
      _tabPageList.add(_PromotionSelectTabPage(
          promotion: promotion, maxCount: count, onGiftSelect: _onGiftSelect));
    }

    _controller = TabController(length: _tabPageList.length, vsync: this);
    super.initState();
  }

  ///赠品选择
  void _onGiftSelect(
      BillPromotionInfoDto promotion, List<GiftWrapper<PromotionPtype>> gifts) {
    //记录赠品
    selectedGiftMap[promotion.id] = gifts;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget buildContent(BuildContext context) {
    return Column(
      children: [
        if (_tabPageList.isEmpty) Expanded(child: Container()),
        if (_tabPageList.isNotEmpty)
          SizedBox(
            height: 60.h,
            child: TabBar(
              tabs: tabTitleList,
              controller: _controller,
              isScrollable: true,
            ),
          ),
        if (_tabPageList.isNotEmpty)
          Expanded(
              child: TabBarView(
            controller: _controller,
            children: _tabPageList,
          )),
        divider,
        Padding(
          padding: EdgeInsets.symmetric(vertical: 10.h),
          child: _buildConfirmButton(),
        )
      ],
    );
  }

  ///确定按钮
  Widget _buildConfirmButton() {
    return buildBottomButton("确定", onTap: () {
      Map<BillPromotionInfoDto, List<PromotionGift>> giftMap =
          _generateGiftMap();
      Navigator.of(context).pop(giftMap);
    });
  }

  ///生成赠品列表
  Map<BillPromotionInfoDto, List<PromotionGift>> _generateGiftMap() {
    Map<BillPromotionInfoDto, List<PromotionGift>> giftMap = {};
    for (var entry in selectedGiftMap.entries) {
      final promotion = promotionMap[entry.key];
      if (promotion == null) continue;
      List<PromotionGift> gifts = entry.value
          .where((element) => element.count > 0)
          .expand<PromotionGift>((e) {
        if (e.gift.ptypeGroup == PromotionGiftType.goods.value) {
          return _generateGoods(e, promotion);
        } else if (e.gift.ptypeGroup == PromotionGiftType.coupon.value) {
          return [e.gift.changeToCouponGift(promotion)..unitQty = e.count];
        } else {
          return [];
        }
      }).toList();
      giftMap[promotion] = gifts;
    }
    return giftMap;
  }

  ///生成商品赠品
  List<GoodsDetailDto> _generateGoods(
      GiftWrapper<PromotionPtype> gift, BillPromotionInfoDto promotion) {
    List<GoodsDetailDto> list = [];
    GoodsDetailDto goods = gift.gift.changeToGoodsDetailDto(promotion);
    goods.unitQty = gift.count;
    list.add(goods);
    List<GoodsDetailDto>? comboDetails;
    if (goods.comboRow) {
      comboDetails =
          PromotionUtil.generatePromotionComboDetailGift(gift.gift, goods);
      list.addAll(comboDetails);
    }
    //处理赠品的优惠信息
    PromotionUtil.setGiftPreferential(promotion, goods, comboDetails);
    return list;
  }
}
//endregion 选择多个促销赠品的弹窗

//region 查看促销赠品弹窗
class PromotionGiftCheckDialog extends StatefulWidget {
  ///单个促销的id，用于选择单个促销的赠品
  final String promotionId;

  final int giftScope;

  const PromotionGiftCheckDialog(
      {Key? key, required this.promotionId, required this.giftScope})
      : super(key: key);

  @override
  State<PromotionGiftCheckDialog> createState() =>
      _PromotionGiftCheckDialogState();
}

class _PromotionGiftCheckDialogState
    extends BaseGoodsDialogState<PromotionGiftCheckDialog> {
  @override
  double get height => 900.h;

  @override
  String get title => "查看赠品";

  @override
  double get width => 600.w;

  BillPromotionInfoDto? promotion;

  @override
  void initState() {
    super.initState();
    List<BillPromotionInfoDto> promotionList = SpTool.getPromotionList();
    promotion = promotionList
        .firstWhereOrNull((promotion) => promotion.id == widget.promotionId);
  }

  @override
  Widget buildContent(BuildContext context) {
    if (promotion == null) {
      return Container();
    }
    return _PromotionSelectTabPage(
      promotion: promotion!,
      maxCount: 0,
      onGiftSelect: (p, _) {},
      editable: false,
      giftScope: widget.giftScope,
    );
  }
}

//endregion  查看促销赠品弹窗

//region 单个促销选择赠品的tab子页面
///单个促销选择赠品的tab子页面
class _PromotionSelectTabPage extends StatefulWidget {
  ///当前促销
  final BillPromotionInfoDto promotion;

  final int giftScope;

  ///最大数量
  final num maxCount;

  ///选择赠品回调
  final _OnGiftSelectCallback onGiftSelect;

  final bool editable;

  _PromotionSelectTabPage({
    Key? key,
    required this.promotion,
    required this.maxCount,
    required this.onGiftSelect,
    int? giftScope,
    this.editable = true,
  })  : giftScope = giftScope ?? PromotionGiftScope.chooseGoods.value,
        super(key: key);

  @override
  State<_PromotionSelectTabPage> createState() =>
      _PromotionSelectTabPageState();
}

class _PromotionSelectTabPageState extends PromotionSelectTabPageState<
    PromotionPtype, _PromotionSelectTabPage> {
  @override
  String? getGiftFullbarcode(PromotionPtype gift) => gift.fullbarcode;

  @override
  String getGiftImageUrl(PromotionPtype gift) {
    String? picUrl = gift.picUrl;
    picUrl = (picUrl != null && !picUrl.contains("http"))
        ? SpTool.getQiNiuThumbnail(picUrl, true)
        : picUrl;
    return picUrl ?? "";
  }

  @override
  String? getGiftName(PromotionPtype gift) => gift.fullname;

  @override
  String? getGiftNameStr(PromotionPtype gift) {
    StringBuffer prop = StringBuffer();
    if (StringUtil.isNotEmpty(gift.propvalueName1)) {
      prop.write("${gift.propvalueName1}:");
    }
    if (StringUtil.isNotEmpty(gift.propvalueName2)) {
      prop.write("${gift.propvalueName2}");
    }
    return "${gift.fullname ?? ""} ${prop.toString()} ${gift.unitName ?? ""}";
  }

  @override
  List<GiftWrapper<PromotionPtype>> initGiftList() {
    List<PromotionPtype> list;
    if (widget.giftScope == PromotionGiftScope.specifiedGoods.value) {
      list = widget.promotion.specifiedGiftPtypeList;
    } else {
      list = widget.promotion.giftPtypeList;
    }
    return list.map((e) => GiftWrapper<PromotionPtype>(e)).toList();
  }

  ///赠品类型是否是优惠券
  @override
  bool isCouponGift(PromotionPtype gift) =>
      gift.ptypeGroup == PromotionGiftType.coupon.value;

  ///赠品优惠券类型
  @override
  CardType? getGiftCardType(PromotionPtype gift) {
    switch (gift.valueTypeName) {
      case "代金券":
        return CardType.amountCoupon;
      case "折扣券":
        return CardType.discountCoupon;
      case "提货券":
      case "礼品券":
        return CardType.giftCoupon;
    }
    return null;
  }

  @override
  num get maxCount => widget.maxCount;

  @override
  bool get editable => widget.editable;

  @override
  void onGiftSelect() => widget.onGiftSelect(widget.promotion, allGiftList);
}
//endregion 单个促销选择赠品的tab子页面
