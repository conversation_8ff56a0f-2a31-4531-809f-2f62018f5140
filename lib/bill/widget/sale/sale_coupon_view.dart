import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../bill/entity/ss_card_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/utils/math_util.dart';

///普通开单页面，已选优惠券的展示
class SaleCouponView extends StatelessWidget {
  final List dataSource;
  final Function(int index)? deleteCallback;

  const SaleCouponView(this.dataSource, {Key? key, this.deleteCallback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        scrollDirection: Axis.horizontal,
        itemBuilder: (BuildContext context, int index) {
          return _itemBuild(context, index);
        },
        separatorBuilder: (BuildContext context, int index) {
          return _separatorBuild(context, index);
        },
        itemCount: dataSource.length);
  }

  _itemBuild(BuildContext context, int index) {
    return Container(
        alignment: Alignment.centerRight,
        child: Stack(
          alignment: Alignment.centerRight,
          children: [
            Container(
              alignment: Alignment.center,
              margin: EdgeInsets.symmetric(horizontal: 10.w),
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              height: 60.h,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  fit: BoxFit.fitWidth,
                  image: AssetImage("assets/images/youhuiquan.png"),
                ),
              ),
              child: HaloPosLabel(
                _getCardLimit(dataSource[index]),
                showFullText: true,
                textStyle: TextStyle(
                    color: ColorUtil.stringColor(
                      "#FF4141",
                    ),
                    fontSize: 22.sp),
              ),
            ),
            Container(
                alignment: Alignment.topRight,
                color: Colors.transparent,
                margin: EdgeInsets.only(top: 20.w, left: 20.w),
                child: Container(
                    width: 28.w,
                    height: 28.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14.w),
                      color: Colors.white,
                    ),
                    child: GestureDetector(
                      onTap: () => deleteCallback?.call(index),
                      behavior: HitTestBehavior.opaque,
                      child: IconFont(
                        IconNames.shanchu_1,
                        color: "#FF4141",
                      ),
                    ))),
          ],
        ));
  }

  _separatorBuild(BuildContext context, int index) => SizedBox(width: 1.w);

  ///获取优惠券使用说明
  String _getCardLimit(SsCardDto card) {
    if (card.memberEquityValues.isNotEmpty != true) {
      return "";
    }
    MemberEquityValuesBean value = card.memberEquityValues[0];
    switch (card.cardType) {
      case 2:
      case 3:
        DetailListBean? detail =
            value.detailList.isNotEmpty == true ? value.detailList[0] : null;
        if (detail == null) return "";
        if (card.cardType == 2) {
          return "满${detail.valueCondition}元减${detail.valueDetail ?? 0}元";
        } else {
          return "满${detail.valueCondition}元打${MathUtil.multiplication((detail.valueDetail ?? 0).toString(), "10").toString()}折";
        }
      case 4:
        return "可提货${value.detailList.first.valueDetail ?? 0}件";
      default:
        return "";
    }
  }
}
