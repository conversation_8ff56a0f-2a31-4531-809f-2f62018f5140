import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../vip/entity/card_coupon_card_template.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_utils/utils/math_util.dart';

class GiftRightsDetailDialog extends StatelessWidget {
  GiftRightsDetailDialog(this.cardTemplate,this. endDateString);
  final CardCouponCardTemplate cardTemplate;
  final String endDateString;
  @override
  Widget build(BuildContext context) {

    return Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.0),
            boxShadow: [
              BoxShadow(
                  color: Colors.black12,
                  offset: Offset(2, 2), //阴影xy轴偏移量
                  blurRadius: 15.0, //阴影模糊程度
                  spreadRadius: 1.0 //阴影扩散程度
                  )
            ]),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(left: 20.w, right: 0, top: 0, bottom: 0),
              child: Container(
                width: 12,
                height: 0,
                decoration: new BoxDecoration(
                    border: Border(
                      // 四个值 top right bottom left
                      bottom: BorderSide(
                          color: Colors.white,
                          width: ScreenUtil().setHeight(8),
                          style: BorderStyle.solid),
                      right: BorderSide(
                          color: Colors.transparent,
                          width: 6,
                          style: BorderStyle.solid),
                      left: BorderSide(
                          color: Colors.transparent,
                          width: 6,
                          style: BorderStyle.solid),
                    ),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.black12,
                          offset: Offset(2, 2), //阴影xy轴偏移量
                          blurRadius: 15.0, //阴影模糊程度
                          spreadRadius: 1.0 //阴影扩散程度
                          )
                    ]),
              ),
            ),
            Container(
                margin: EdgeInsets.only(top: 16.h),
                decoration: new BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                ),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: HaloPosLabel(
                    "${cardTemplate.fullname}",
                    maxLines: 1,
                    textStyle: TextStyle(fontSize: 22.sp),
                  ),
                )),
            Container(
                constraints:
                BoxConstraints(maxWidth: 462.w, maxHeight: 188.h),
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                decoration: new BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(8.0)),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      child: HaloPosLabel(
                        "权益详情:",
                        maxLines: 1,
                        textStyle: TextStyle(
                            fontSize: 18.sp,
                            color: ColorUtil.stringColor("#666666")),
                      ),
                    ),
                    Expanded(
                        child: Container(
                      child: HaloPosLabel(
                        getDetailString(),
                        maxLines: 4,
                        textStyle: TextStyle(
                            fontSize: 18.sp,
                            color: ColorUtil.stringColor("#666666")),
                      ),
                    )),
                  ],
                )),
            Container(
                margin: EdgeInsets.only(left: 16.w,right: 16.w,bottom: 16.h),
                decoration: new BoxDecoration(
                  color: Colors.white,
                ),
                child: Container(
                  child: HaloPosLabel(
                    endDateString,
                    maxLines: 1,
                    textStyle: TextStyle(
                        fontSize: 18.sp,
                        color: ColorUtil.stringColor("#666666")),
                  ),
                )),
          ],
        ));
  }

  String getDetailString() {
    if (cardTemplate.memberEquityValues?.isNotEmpty != true) {
      return "";
    }
    MemberEquityValuesBean value = cardTemplate.memberEquityValues![0];
    switch (cardTemplate.cardType) {
      case 2:
      case 3:
      //miniCostType 0限制，1不限制
        DetailListBean? detail =
        value.detailList!.isNotEmpty
            ? value.detailList![0]
            : null;
        if (detail == null) return "";
        if (cardTemplate.cardType == 2) {
          return "满${detail.valueCondition ?? 0}元减${detail.valueDetail ?? 0}元";
        } else {
          return "满${detail.valueCondition ?? 0}元打${MathUtil.multiplication((detail.valueDetail ?? 0).toString(), "10").toString()}折";
        }
        break;
      case 4:
        return "可提货${value.detailList!.first.valueDetail ?? 0}件";
      default:
        return "";
    }
  }
}
