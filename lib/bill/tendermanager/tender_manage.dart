import 'package:flutter/material.dart';
import 'package:halo_pos/common/widget/datetime_filter.dart';
import '../../../bill/entity/order_bill_item_entity.dart';
import '../../../bill/tendermanager/widget/column_config_drawer.dart';
import '../../../bill/tendermanager/widget/tender_detail_manage.dart';
import '../../../bill/tendermanager/widget/tender_list_manage.dart';
import '../../../common/change_notifier.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/permission_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart' show SizeExtension;
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/widget/datepicker/pduration.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/halo_pop_window.dart';
import 'package:haloui/widget/halo_textfield.dart';
import 'package:haloui/widget/halo_toast.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';
import 'package:halo_utils/utils/string_util.dart';
import '../../../common/tool/dialog_util.dart';

///调拨单据查询的条件枚举
enum _FilterType {
  ///单据类型
  type,

  ///单据状态
  state,
}

///筛选条件，调拨单类型
const _filterSourceType = ["调出", "调入"];

///筛选条件，调出的postState
const _filterSourceStateOut = ["已出库"];

///筛选条件，调入的postState
const _filterSourceStateIn = ["全部", "待入库", "已入库"];

///调货管理页面
class TenderManagePages extends BaseStatefulPage {
  const TenderManagePages({Key? key})
    : super(key: key, rightFlex: 3, showEndDrawer: false);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _TenderManagePagesState();
}

class _TenderManagePagesState extends BaseStatefulPageState<TenderManagePages>
    with DateTimeFilterMixin {
  ///单据类型选择框key
  final GlobalKey _keyBusinessType = GlobalKey();

  ///单据状态选择框key
  final GlobalKey _keyPostState = GlobalKey();

  final TextEditingController textBillNumberController =
      TextEditingController();

  ///当前单据状态筛选条件
  late FilterController _filterController;

  ///当前选中的单据
  final ValueNotifier<OrderBillItem?> billOrderController = ValueNotifier(null);

  ///权限
  late PermissionDto permissionDto;

  @override
  String getActionBarTitle() => "调货管理";

  @override
  Future<void> onInitState() async {
    _filterController = FilterController._(
      startTime: textStartTimeController.text,
      endTime: textEndTimeController.text,
      billNumber: textBillNumberController.text,
      businessType: "调出",
      postState: "已出库",
    );
    //监听时间变化
    textStartTimeController.addListener(
      () => _filterController.startTime.value = textStartTimeController.text,
    );
    textEndTimeController.addListener(
      () => _filterController.endTime.value = textEndTimeController.text,
    );
    permissionDto = SpTool.getPermission();
  }

  @override
  buildEndDrawer() {
    return const ColumnConfigDrawer(type: Type.receive);
  }

  ///左侧调拨单据列表
  @override
  Widget buildLeftBody(BuildContext context) {
    return TenderListManage(
      filterController: _filterController,
      onChanged: (int value, item) => billOrderController.value = item,
    );
  }

  ///右侧当前选中的单据明细
  @override
  Widget buildRightBody(BuildContext context) {
    return HaloContainer(
      border: const Border(
        left: BorderSide(width: 1, color: AppColors.dividerColor),
      ),
      direction: Axis.vertical,
      mainAxisSize: MainAxisSize.max,
      children: [
        TenderDetailPages(
          billOrderController: billOrderController,
          onChange: () => _filterController._notify(),
        ),
      ],
    );
  }

  ///顶部筛选条件
  @override
  Widget buildTopBody(BuildContext context) {
    return HaloContainer(
      height: 84.w,
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        buildDateTimeQuery(context),
        _buildBillFilterWidget(context, _FilterType.type),
        _buildBillFilterWidget(context, _FilterType.state),
        _buildSaleBillNumberQuery(),
        Padding(
          padding: EdgeInsets.only(left: 12.w),
          child: SizedBox(
            height: 54.w,
            child: HaloButton(
              borderRadius: 5.sp,
              text: "查询",
              fontSize: 25.sp,
              backgroundColor: AppColors.accentColor,
              onPressed: () {
                FocusScopeNode currentFocus = FocusScope.of(context);
                if (!currentFocus.hasPrimaryFocus &&
                    currentFocus.focusedChild != null) {
                  FocusManager.instance.primaryFocus?.unfocus();
                }
                _filterController.billNumber.value =
                    textBillNumberController.text;
              },
            ),
          ),
        ),
      ],
    );
  }

  ///下拉筛选条件
  Widget _buildBillFilterWidget(BuildContext context, _FilterType filterType) {
    String title;
    GlobalKey key;
    ValueNotifier<String> currentFilter;
    if (filterType == _FilterType.type) {
      title = "调拨单据类型:";
      key = _keyBusinessType;
      currentFilter = _filterController.businessType;
    } else {
      title = "单据状态:";
      key = _keyPostState;
      currentFilter = _filterController.postState;
    }
    TextStyle style = TextStyle(
      fontSize: 24.sp,
      color: AppColorHelper(context).getTitleBoldTextColor(),
    );
    return HaloContainer(
      height: 60.w,
      color: Colors.white,
      margin: EdgeInsets.only(left: 12.w, top: 14.h, bottom: 14.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      children: [
        Text(title, style: style),
        GestureDetector(
          onTap:
              () => _showPostStateListPopWindow(
                context,
                filterType: filterType,
                key: key,
                currentFilter: currentFilter,
              ),
          child: Container(
            key: key,
            width: 100.w,
            height: 60.w,
            alignment: Alignment.center,
            child: HaloPosLabel(
              currentFilter.value,
              textAlign: TextAlign.center,
              textStyle: style,
            ),
          ),
        ),
      ],
    );
  }

  ///弹出单据类型下拉弹窗
  _showPostStateListPopWindow(
    BuildContext context, {
    required _FilterType filterType,
    required GlobalKey key,
    required ValueNotifier<String> currentFilter,
  }) {
    List<String> source = _getFilterSource(filterType);
    HaloPopWindow().show(
      key,
      gravity: PopWindowGravity.bottom,
      backgroundColor: Colors.transparent,
      // intervalTop: 10,
      child: Container(
        width: 100.w,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: AppColors.dividerColor),
          borderRadius: BorderRadius.all(Radius.circular(8.w)),
        ),
        child: ListView.separated(
          itemCount: source.length,
          shrinkWrap: true,
          separatorBuilder:
              (context, index) =>
                  Divider(height: 2.h, color: AppColors.dividerColor),
          itemBuilder: (buildContext, index) {
            String item = source[index];
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                setState(() {
                  //更改调拨单类型时，同时会更改单据状态
                  if (filterType == _FilterType.type) {
                    //拦截监听，避免多余的网络请求
                    _filterController.postState.interceptNotify = true;
                    List<String> stateSource = _getFilterSource(
                      _FilterType.state,
                      item,
                    );
                    _filterController.postState.value = stateSource.first;
                    _filterController.postState.interceptNotify = false;
                  }
                  currentFilter.value = item;
                });
                HaloPopWindow().disMiss();
              },
              child: Container(
                height: 60.w,
                alignment: Alignment.center,
                child: Text(
                  item,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: AppColorHelper(context).getTitleBoldTextColor(),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  //单据输入
  Widget _buildSaleBillNumberQuery() {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      color: Colors.white,
      margin: EdgeInsets.only(left: 12.w, top: 14.h, bottom: 14.h),
      padding: EdgeInsets.only(left: 15.w, right: 15.w),
      width: 341.w,
      height: 56.h,
      border: Border.all(color: const Color(0xFFF6F6F6), width: 1),
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 3),
          child: HaloPosLabel(
            "单据编号: ",
            textAlign: TextAlign.center,
            textStyle: TextStyle(
              fontSize: 24.sp,
              color: AppColorHelper(context).getTitleBoldTextColor(),
            ),
          ),
        ),
        Expanded(
          child: HaloTextField(
            controller: textBillNumberController,
            fontSize: 24.sp,
            contentPadding: 0,
            maxLines: 1,
          ),
        ),
      ],
    );
  }

  bool _checkStartTime(DateTime dateTime) {
    if (dateTime.millisecondsSinceEpoch -
            DateUtil.getDateTime(
              textEndTimeController.text,
            )!.millisecondsSinceEpoch >
        0) {
      HaloToast.showError(context, msg: "开始时间不可以大于结束时间，请重新选择!");
      return false;
    }
    return true;
  }

  bool _checkEndTime(DateTime dateTime) {
    if (dateTime.millisecondsSinceEpoch -
            DateUtil.getDateTime(
              textStartTimeController.text,
            )!.millisecondsSinceEpoch <
        0) {
      HaloToast.showError(context, msg: "结束时间不可以小于开始时间，请重新选择!");
      return false;
    }
    return true;
  }

  List<String> _getFilterSource(
    _FilterType filterType, [
    String? businessType,
  ]) {
    List<String> source;
    if (filterType == _FilterType.type) {
      source = _filterSourceType;
    } else {
      businessType ??= _filterController.businessType.value;
      source =
          businessType == "调出" ? _filterSourceStateOut : _filterSourceStateIn;
    }
    return source;
  }

  ///构建图片
  Widget _buildItemImage(String picUrl, {int size = 100}) {
    final defaultImage = Container(
      width: size.w,
      height: size.w,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: Icon(
        Icons.image_not_supported,
        color: Colors.grey[400],
        size: 24.w,
      ),
    );

    return SizedBox(
      width: size.w,
      height: size.w,
      child: Image.network(
        picUrl,
        fit: BoxFit.cover,
        width: size.w,
        height: size.w,
        loadingBuilder: (
          BuildContext context,
          Widget child,
          ImageChunkEvent? loadingProgress,
        ) {
          if (loadingProgress == null) {
            return child;
          }
          return defaultImage;
        },
        errorBuilder:
            (BuildContext context, Object error, StackTrace? stackTrace) =>
                defaultImage,
      ),
    );
  }

  ///构建可点击查看大图的图片控件
  Widget _buildClickableImage(
    BuildContext context,
    String picUrl, {
    int size = 100,
  }) {
    Widget image = _buildItemImage(picUrl, size: size);
    if (StringUtil.isNotEmpty(picUrl)) {
      image = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap:
            () => DialogUtil.showAlertDialog(
              context,
              actionCount: 0,
              childContent: _buildItemImage(picUrl, size: 500),
            ),
        child: image,
      );
    }
    return image;
  }
}

///筛选条件的订阅通知
class FilterController extends ChangeNotifier {
  ///开始时间
  final ValueNotifier<String> startTime;

  ///结束时间
  final ValueNotifier<String> endTime;

  ///单据编号
  final ValueNotifier<String> billNumber;

  ///单据类型
  final ValueNotifier<String> businessType;

  ///单据状态
  final InterceptValueNotifier<String> postState;

  FilterController._({
    required String startTime,
    required String endTime,
    required String billNumber,
    required String businessType,
    required String postState,
  }) : startTime = ValueNotifier(startTime),
       endTime = ValueNotifier(endTime),
       billNumber = ValueNotifier(billNumber),
       businessType = ValueNotifier(businessType),
       postState = InterceptValueNotifier(postState);

  ///强制通知listener，用于右侧单据操作后，刷新左侧列表
  void _notify() {
    notifyListeners();
  }

  @override
  void addListener(VoidCallback listener) {
    super.addListener(listener);
    startTime.addListener(listener);
    endTime.addListener(listener);
    billNumber.addListener(listener);
    businessType.addListener(listener);
    postState.addListener(listener);
  }

  @override
  void removeListener(VoidCallback listener) {
    super.removeListener(listener);
    startTime.removeListener(listener);
    endTime.removeListener(listener);
    billNumber.removeListener(listener);
    businessType.removeListener(listener);
    postState.removeListener(listener);
  }

  @override
  void dispose() {
    super.dispose();
    startTime.dispose();
    endTime.dispose();
    billNumber.dispose();
    businessType.dispose();
    postState.dispose();
  }
}
