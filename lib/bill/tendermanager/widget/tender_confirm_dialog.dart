import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/ptype/ptype_serial_no_dto.dart';
import '../../../bill/tendermanager/entity/tender_manage_config.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/decimal_scale_input_formatter.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/select_wrapper_bean.dart';
import '../../../entity/system/permission_dto.dart';
import '../../../iconfont/icon_font.dart';
import '../../../settting/widget/checkbox.dart';
import '../../../widgets/custom_table.dart';
import '../../../widgets/multi_select_dialog.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

///确认入库弹窗
class TenderConfirmDialog extends StatefulWidget {
  final GoodsBillDto goodsBillDto;
  final VoidCallback openColumnConfigCallback;

  const TenderConfirmDialog(
      {Key? key,
      required this.goodsBillDto,
      required this.openColumnConfigCallback})
      : super(key: key);

  @override
  State<TenderConfirmDialog> createState() => _TenderConfirmDialogState();
}

class _TenderConfirmDialogState extends State<TenderConfirmDialog> {
  ///权限
  final PermissionDto _permissionDto = SpTool.getPermission();

  ///严格序列号
  final isStrict =
      SpTool.getSystemConfig().sysIndustryEnabledStrictSerialNumber;
  final TenderManageConfig config = SpTool.getTenderManageConfig();
  final Map<int, double> columnConfig = {};
  final Map<int, String> columnTitle = {};

  ///入库请求的参数
  final Map<String, Map<String, dynamic>> requestParam = {};

  Map<String, dynamic> getRequestDetailItem(String detailId) {
    Map<String, dynamic>? item = requestParam[detailId];
    if (item == null) {
      item = {
        "detailId": detailId,
      };
      requestParam[detailId] = item;
    }
    return item;
  }

  ///根据商品明细行的detailId拿到缓存的已选中的序列号
  List<PtypeSerialNoDto> getSelectedSerialByDetailId(String detailId) {
    Map<String, dynamic> item = getRequestDetailItem(detailId);
    List<PtypeSerialNoDto>? list = item["serialNoList"];
    if (list == null) {
      list = [];
      item["serialNoList"] = list;
    }
    return list;
  }

  @override
  void initState() {
    for (var entry in config.columnConfig.entries) {
      int columnType = int.parse(entry.key);
      columnConfig[columnType] = entry.value ? 1 : 0;
      columnTitle[columnType] = ColumnType.values[columnType].title;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        color: Colors.white,
        width: 1800.w,
        height: 800.h,
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 30.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTop(context),
            SizedBox(height: 20.h),
            Expanded(child: buildTable(context)),
            buildBottomButton(context),
          ],
        ),
      ),
    );
  }

  Widget buildTop(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text("确认入库",
            style: TextStyle(fontSize: 30.sp, fontWeight: FontWeight.bold)),
        // Visibility(
        //   visible: _permissionDto.shopsaletenderManagecolumnConfig == true,
        //   child: GestureDetector(
        //     onTap: () {
        //       Navigator.pop(context);
        //       widget.openColumnConfigCallback();
        //     },
        //     behavior: HitTestBehavior.opaque,
        //     child: IconFont(IconNames.shezhi, size: 30.w),
        //   ),
        // ),
      ],
    );
  }

  Widget buildTable(BuildContext context) {
    BorderSide borderSide = const BorderSide(color: AppColors.lineColor);
    return CustomColumnTable<GoodsDetailDto>(
      columnConfig: columnConfig,
      columnTitle: columnTitle,
      cellBuilder: buildCell,
      columnTitleBuilder: (title, columnType) => Container(
        padding: EdgeInsets.symmetric(vertical: 20.h),
        alignment: Alignment.center,
        child: Text(title, overflow: TextOverflow.ellipsis),
      ),
      data: widget.goodsBillDto.outDetail,
      border: TableBorder(
          top: borderSide, bottom: borderSide, horizontalInside: borderSide),
      scrollable: true,
    );
  }

  Widget buildCell(GoodsDetailDto item, int columnType) {
    TextStyle style = const TextStyle(overflow: TextOverflow.ellipsis);
    Widget? child;
    if (columnType == ColumnType.userCode.index) {
      child = Text(item.pUserCode ?? "", style: style);
    } else if (columnType == ColumnType.barcode.index) {
      child = Text(item.fullbarcode, style: style);
    } else if (columnType == ColumnType.ptypeName.index) {
      child =
          Text("${item.pFullName ?? ""}${item.skuName ?? ""}", style: style);
    } else if (columnType == ColumnType.shouldReceiveQty.index) {
      child = Text(item.unitQty.toString(), style: style);
    } else if (columnType == ColumnType.uncomplateQty.index) {
      Decimal unComplete = getUnCompleteQty(item);
      child = Text(unComplete.toString(), style: style);
    } else if (columnType == ColumnType.actualQty.index) {
      child = buildItemActualQty(item);
    } else if (columnType == ColumnType.unit.index) {
      child = Text(item.unitName, style: style);
    } else if (columnType == ColumnType.batch.index) {
      child = Text(item.batchNo, style: style);
    } else if (columnType == ColumnType.produceDate.index) {
      child = Text(_parseDate(item.produceDate), style: style);
    } else if (columnType == ColumnType.protectDays.index) {
      child = Text(item.protectDays?.toString() ?? "", style: style);
    } else if (columnType == ColumnType.sn.index) {
      child = buildItemSn(item);
    }
    return Container(
      height: 80.h,
      alignment: Alignment.center,
      child: child,
    );
  }

  ///构建item本次入库数量
  Widget buildItemActualQty(GoodsDetailDto item) {
    Map<String, dynamic> requestItem = getRequestDetailItem(item.detailId!);
    TextEditingController controller =
        TextEditingController(text: (requestItem["unitQty"] ?? 0).toString());
    controller.addListener(() {
      num qty = num.tryParse(controller.text) ?? 0;
      int snCount = getSelectedSerialByDetailId(item.detailId!).length;
      // bool isStrict = item.snenabled == 1;
      if (isStrict && qty != snCount && snCount > 0) {
        controller.text = snCount.toString();
        HaloToast.show(context, msg: "入库数量必须和序列号数量一致");
        return;
      }
      if (qty < snCount) {
        controller.text = snCount.toString();
        HaloToast.show(context, msg: "实际到货数量不得少于序列号数量");
        controller.selection =
            TextSelection.collapsed(offset: controller.text.length);
        return;
      }
      num unComplete = getUnCompleteQty(item).toDouble();
      if (qty > unComplete) {
        controller.text = unComplete.toString();
        controller.selection =
            TextSelection.collapsed(offset: controller.text.length);
        HaloToast.show(context, msg: "入库数量不得大于待入库数量");
        return;
      }
      final detailItem = getRequestDetailItem(item.detailId!);
      detailItem["unitQty"] = qty;
      detailItem["unitRate"] = item.unitRate ?? 1;
      detailItem["goods"] = GoodsDetailDto.fromMap(item.toJson())
        ..unitQty = qty;
    });
    num unComplete = getUnCompleteQty(item).toDouble();
    return TextField(
      textInputAction: TextInputAction.done,
      textAlign: TextAlign.center,
      inputFormatters: [DecimalScaleInputFormatter(scale: 2)],
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      enabled: unComplete > 0,
      decoration:
          const InputDecoration(isCollapsed: true, border: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey, width: 1.0),
          ),),
      controller: controller,
    );
  }

  ///构建item序列号
  Widget buildItemSn(GoodsDetailDto item) {
    TextStyle style = const TextStyle(overflow: TextOverflow.ellipsis);
    List<PtypeSerialNoDto> selectSnList =
        getSelectedSerialByDetailId(item.detailId!);
    Widget result =
        Text(selectSnList.map((e) => e.snno).join(","), style: style);
    if (item.snenabled != 0) {
      result = GestureDetector(
        onTap: () {
          showDialog(
            context: context,
            builder: (context) => MultiSelectDialog<PtypeSerialNoDto>(
                title: "序列号选择",
                listGetter: () async {
                  return item.serialNoList
                      .where((element) => element.completed != true)
                      .map((e) => SelectWrapperBean<PtypeSerialNoDto>(
                          data: e, selected: selectSnList.contains(e)))
                      .toList();
                },
                nameGetter: (sn) => sn.snno ?? ""),
          ).then((value) {
            if (value is List<PtypeSerialNoDto>) {
              setState(() {
                Map<String, dynamic> requestItem =
                    getRequestDetailItem(item.detailId!);
                //如果是严格序列号，或序列号数量大于商品数量，则调整商品数量为序列号数量
                if (isStrict || value.length > (requestItem["unitQty"] ?? 0)) {
                  requestItem["unitQty"] = value.length;
                  requestItem["unitRate"] = item.unitRate ?? 1;
                }
                selectSnList.clear();
                selectSnList.addAll(value);
              });
            }
          });
        },
        behavior: HitTestBehavior.opaque,
        child: Center(
          child: result,
        ),
      );
    }
    return result;
  }

  Widget buildBottomButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Visibility(
            visible: _permissionDto.shopsaletenderManageautoPrint == true,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  config.autoPrint = !config.autoPrint;
                  SpTool.saveTenderManageConfig(config);
                });
              },
              behavior: HitTestBehavior.opaque,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 20.h),
                child: Row(mainAxisSize: MainAxisSize.min, children: [
                  HaloPosCheckBox(
                      value: config.autoPrint,
                      width: 20.w,
                      height: 20.w,
                      checkImage: IconNames.xuanzhong,
                      uncheckImage: IconNames.weixuanzhong),
                  const Text("确认后自动打印单据"),
                ]),
              ),
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              HaloButton(
                backgroundColor: Colors.blue,
                height: 60.h,
                width: 120.w,
                textColor: Colors.white,
                text: "确认",
                fontSize: 22.sp,
                onPressed: _confirm,
              ),
              SizedBox(width: 20.w),
              HaloButton(
                onPressed: () => Navigator.pop(context),
                backgroundColor: Colors.white,
                height: 60.h,
                width: 120.w,
                buttonType: HaloButtonType.outlinedButton,
                textColor: Colors.black54,
                text: "取消",
                borderColor: Colors.black54,
                outLineWidth: 1,
                fontSize: 22.sp,
              ),
            ],
          )
        ],
      ),
    );
  }

  ///确认入库
  void _confirm() {
    var details = requestParam.values.where((item) {
      num unitQty = (item["unitQty"] ?? 0);
      bool filter = unitQty > 0;
      if (filter) {
        List<PtypeSerialNoDto>? snList = item["serialNoList"];
        if (snList != null) {
          item["serialNoList"] =
              snList.map((e) => e.toJson()..["completed"] = true).toList();
        }
        item["qty"] =
            MathUtil.multiplyDec(unitQty, item["unitRate"] ?? 1).toDouble();
      }
      return filter;
    }).toList();
    if (details.isEmpty) {
      HaloToast.show(context, msg: "本次还未入库任何商品!");
      return;
    }
    Map<String, dynamic> result = {
      "vchcode": widget.goodsBillDto.vchcode,
      "details": details,
    };
    Navigator.pop(context, result);
  }

  Decimal getUnCompleteQty(GoodsDetailDto goods) {
    Decimal unComplete = MathUtil.subtraction(
        goods.unitQty.toString(),
        MathUtil.division((goods.completedQty ?? 0).toString(),
                (goods.unitRate ?? 1).toString())
            .toString());
    return unComplete;
  }

  String _parseDate(String? date) {
    if (date?.isNotEmpty == true) {
      //Z代表utc时间,0时区，所以只需将其转换为本地时区即可
      DateTime? dateTime = DateTime.tryParse(date!)?.toLocal();
      if (dateTime == null) {
        return "";
      }
      return "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}";
    }
    return "";
  }
}
