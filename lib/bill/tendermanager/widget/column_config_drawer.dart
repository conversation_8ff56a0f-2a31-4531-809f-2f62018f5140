import 'package:flutter/material.dart';
import '../../../bill/tendermanager/entity/tender_manage_config.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../settting/widget/checkbox.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';

enum Type {
  ///调拨单发货
  send,

  ///调拨单确认收货
  receive
}

class ColumnConfigDrawer extends StatefulWidget {
  final Type type;

  const ColumnConfigDrawer({Key? key, required this.type}) : super(key: key);

  @override
  State<ColumnConfigDrawer> createState() => _ColumnConfigDrawerState();
}

class _ColumnConfigDrawerState extends State<ColumnConfigDrawer> {
  late final TransferColumnConfig config;

  Map<String, bool> get columnConfig => config.columnConfig;

  @override
  void initState() {
    config = widget.type == Type.receive
        ? SpTool.getTenderManageConfig()
        : SpTool.getTransferConfig();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.white,
      width: 400.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
            child: HaloPosLabel(
              "选择列表中显示的字段",
              textStyle: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColorHelper(context).getTitleBoldTextColor()),
            ),
          ),
          const Divider(color: AppColors.lineColor, height: 1),
          Expanded(
            child: ListView.builder(
              itemBuilder: (context, index) {
                Widget checkBox;
                ColumnType type = ColumnType.values[int.parse(columnConfig.keys.toList()[index])];
                Map<String, bool> defaultColumnConfig;
                if (widget.type == Type.receive) {
                  defaultColumnConfig = TenderManageConfig.defaultColumnConfig;
                } else if (widget.type == Type.send) {
                  defaultColumnConfig =
                      TransferAndAllocateConfig.defaultColumnConfig;
                } else {
                  return Container();
                }
                if (defaultColumnConfig[type.index.toString()] == true) {
                  checkBox = IconFont(IconNames.xuanzhong,
                      size: 20.w, color: "#666666");
                } else {
                  checkBox = HaloPosCheckBox(
                      value: columnConfig[type.index.toString()] == true,
                      width: 20.w,
                      height: 20.w,
                      checkImage: IconNames.xuanzhong,
                      uncheckImage: IconNames.weixuanzhong,
                      onChanged: (value) {
                        setState(() {
                          columnConfig[type.index.toString()] = value;
                        });
                        if (widget.type == Type.receive) {
                          if (config is TenderManageConfig) {
                            SpTool.saveTenderManageConfig(
                                config as TenderManageConfig);
                          }
                        } else if (widget.type == Type.send) {
                          if (config is TransferAndAllocateConfig) {
                            SpTool.saveTransferConfig(
                                config as TransferAndAllocateConfig);
                          }
                        }
                      });
                }
                return Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
                  child: Row(
                    children: [
                      checkBox,
                      SizedBox(
                        width: 28.w,
                      ),
                      HaloPosLabel(type.title,
                          textStyle: TextStyle(
                              fontSize: 22.sp,
                              color: AppColorHelper(context)
                                  .getTitleBoldTextColor())),
                    ],
                  ),
                );
              },
              itemCount: columnConfig.length,
            ),
          )
        ],
      ),
    );
  }
}
