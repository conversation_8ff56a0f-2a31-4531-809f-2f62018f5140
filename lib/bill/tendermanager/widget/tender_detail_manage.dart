import 'package:flutter/material.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_pos/common/widget/ptype_note_richtext.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';
import 'package:halo_utils/utils/string_util.dart';
import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/order_bill_item_entity.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/tendermanager/entity/tender_manage_config.dart';
import '../../../bill/tendermanager/widget/tender_confirm_dialog.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/column_config.dart';
import '../../../entity/system/permission_dto.dart';
import '../../../enum/bill_post_state.dart';
import '../../../enum/bill_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../print/tool/print_tool.dart';
import '../../../settting/widget/column_config_page.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../../widgets/custom_table.dart';
import '../../model/ptype_model.dart';

///调货管理右侧单据详情
class TenderDetailPages extends StatefulWidget {
  final Function onChange;

  final ValueNotifier<OrderBillItem?> billOrderController;

  const TenderDetailPages({
    Key? key,
    required this.billOrderController,
    required this.onChange,
  }) : super(key: key);

  @override
  TenderDetailPagesState createState() => TenderDetailPagesState();
}

class TenderDetailPagesState extends State<TenderDetailPages> {
  ///单据详情
  GoodsBillDto goodsBillDto = GoodsBillDto();

  ///权限
  final PermissionDto _permissionDto = SpTool.getPermission();

  ///当前门店仓库id
  final String ktypeId = SpTool.getStoreInfo()!.ktypeId!;

  ///是否是调入单据
  bool get isReceive => ktypeId == goodsBillDto.ktypeId2;

  ///当调入单并且状态是待入库，则展示入库按钮
  bool get enableReceive =>
      isReceive &&
      (goodsBillDto.postState == BillPostState.PROCESS_COMPLETED.name ||
          goodsBillDto.postState == BillPostState.STOCK_INOUT_PART.name) &&
      (_permissionDto.shopsaletenderManageconfirm == true);

  /// 列配置
  late List<TenderColumnConfig> columnConfig;

  @override
  void initState() {
    super.initState();
    columnConfig = getColumnData();
    widget.billOrderController.addListener(_billOrderControllerChanged);
    if (widget.billOrderController.value != null) {
      _billOrderControllerChanged();
    }
  }

  /// 获取列配置数据
  List<TenderColumnConfig> getColumnData({List<TenderColumnConfig>? config}) {
    List<TenderColumnConfig> temp =
        config ?? SpTool.getTenderManageColumnConfig();

    // 确保设置列存在
    if (temp.every((element) => element.type != TenderColumnType.setting)) {
      temp.add(TenderColumnConfig(title: "设置", type: TenderColumnType.setting));
    }

    return temp.where((element) {
      return element.isShow && element.title != '全部';
    }).toList();
  }

  ///构建图片
  Widget _buildItemImage(String picUrl, {int size = 100}) {
    final defaultImage = Container(
      width: size.w,
      height: size.w,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: Icon(
        Icons.image_not_supported,
        color: Colors.grey[400],
        size: 24.w,
      ),
    );

    return SizedBox(
      width: size.w,
      height: size.w,
      child: Image.network(
        picUrl,
        fit: BoxFit.cover,
        width: size.w,
        height: size.w,
        loadingBuilder: (
          BuildContext context,
          Widget child,
          ImageChunkEvent? loadingProgress,
        ) {
          if (loadingProgress == null) {
            return child;
          }
          return defaultImage;
        },
        errorBuilder:
            (BuildContext context, Object error, StackTrace? stackTrace) =>
                defaultImage,
      ),
    );
  }

  //表格中的图片
  Widget _buildTabPic(String picUrl) {
    return Column(
      children: [
        Container(
          height: 70.w,
          alignment: Alignment.center,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5.w),
            child: _buildClickableImage(context, picUrl, size: 60),
          ),
        ),
        Divider(height: 1, color: AppColors.lineColor),
      ],
    );
  }

  ///构建可点击查看大图的图片控件
  Widget _buildClickableImage(
    BuildContext context,
    String picUrl, {
    int size = 100,
  }) {
    Widget image = _buildItemImage(picUrl, size: size);
    if (StringUtil.isNotEmpty(picUrl)) {
      image = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap:
            () => DialogUtil.showAlertDialog(
              context,
              actionCount: 0,
              childContent: _buildItemImage(picUrl, size: 500),
            ),
        child: image,
      );
    }
    return image;
  }

  //item tab
  Widget _buildTabItem(
    String? text, {
    EdgeInsets padding = EdgeInsets.zero,
    AlignmentGeometry? alignment,
    GoodsDetailDto? detailDto,
  }) {
    return HaloContainer(
      height: 70.w,
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: detailDto != null,
          child: Expanded(
            child: Container(
              padding: padding,
              alignment: alignment ?? Alignment.centerLeft,
              child: PtypeNoteRichText(
                goodsDetailDto: detailDto ?? GoodsDetailDto(),
                showProp: false,
                textStyle: TextStyle(
                  color: ColorUtil.stringColor("#494848"),
                  fontSize: 24.sp,
                  height: 1,
                ),
              ),
            ),
          ),
        ),
        Visibility(
          visible: detailDto == null,
          child: Expanded(
            child: Container(
              padding: padding,
              alignment: alignment ?? Alignment.centerLeft,
              child: HaloPosLabel(
                text ?? "",
                textStyle: TextStyle(
                  color: ColorUtil.stringColor("#494848"),
                  fontSize: 24.sp,
                  height: 1,
                ),
              ),
            ),
          ),
        ),
        const Divider(height: 1, color: AppColors.lineColor),
      ],
    );
  }

  @override
  void didUpdateWidget(covariant TenderDetailPages oldWidget) {
    if (widget.billOrderController != oldWidget.billOrderController) {
      oldWidget.billOrderController.removeListener(_billOrderControllerChanged);
      widget.billOrderController.addListener(_billOrderControllerChanged);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.billOrderController.removeListener(_billOrderControllerChanged);
    super.dispose();
  }

  void _billOrderControllerChanged() {
    OrderBillItem? order = widget.billOrderController.value;
    if (order?.vchcode != null) {
      onRequestData(order!.vchcode).then((value) {
        setState(() => goodsBillDto = value);
      });
    } else {
      setState(() => goodsBillDto = GoodsBillDto());
    }
  }

  @override
  Widget build(BuildContext context) {
    if ((goodsBillDto.outDetail.isEmpty && goodsBillDto.inDetail.isEmpty)) {
      return Expanded(
        child: HaloEmptyContainer(
          gravity: EmptyGravity.CENTER,
          image: Image.asset('assets/images/nodata.png'),
          title: "暂无数据",
          titleStyle: TextStyle(
            decoration: TextDecoration.none,
            fontSize: ScreenUtil().setSp(30),
            color: Colors.grey,
          ),
        ),
      );
    }
    return Expanded(
      child: HaloContainer(
        padding: const EdgeInsets.all(0),
        color: Colors.white,
        direction: Axis.vertical,
        children: [
          HaloContainer(
            color: AppColorHelper(context).getScaffoldBackgroundColor(),
            padding: EdgeInsets.only(right: 20.w, bottom: 15.w, left: 20.w, top: 10.w,),
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              HaloPosLabel(
                "商品信息",
                textStyle: const TextStyle(fontWeight: FontWeight.w600),
              ),
              HaloPosLabel(getCountWithBill()),
            ],
          ),
          _buildTable(context),
          const Divider(height: 1, color: AppColors.lineColor),
          _buildOrderInfoWidget(context),
          const Divider(height: 1, color: AppColors.lineColor),
          _buildButton(context),
        ],
      ),
    );
  }

  /// 构建表格
  Widget _buildTable(BuildContext context) {
    final rowHeight = 70.w;

    // 获取启用的列类型
    final enabledColumnTypes =
        columnConfig
            .where(
              (config) =>
                  (config.isShow || config.isRequired) &&
                  config.type != TenderColumnType.all,
            )
            .map((config) => config.type)
            .toList();

    // 确保设置列总是显示
    if (!enabledColumnTypes.contains(TenderColumnType.setting)) {
      enabledColumnTypes.add(TenderColumnType.setting);
    }

    // 创建列宽度和标题配置
    Map<int, double> columnWidths = {};
    Map<int, String> columnTitles = {};

    // 配置列宽和标题
    for (int i = 0; i < enabledColumnTypes.length; i++) {
      var type = enabledColumnTypes[i];

      // 根据列类型设置宽度
      double width = 1.0;
      if (type == TenderColumnType.pName) {
        width = 2.0;
      } else if (type == TenderColumnType.userCode ||
          type == TenderColumnType.barCode) {
        width = 1.5;
      } else if (type == TenderColumnType.image) {
        width = 1.0;
      } else if (type == TenderColumnType.setting) {
        width = 0.5;
      }

      columnWidths[i] = width;

      // 设置列标题
      String title =
          columnConfig
              .firstWhere(
                (config) => config.type == type,
                orElse: () => TenderColumnConfig(title: "Unknown"),
              )
              .title;
      columnTitles[i] = title;
    }

    List<GoodsDetailDto> details =
        goodsBillDto.outDetail.isEmpty
            ? goodsBillDto.inDetail
            : goodsBillDto.outDetail;

    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: CustomColumnTable<GoodsDetailDto>(
          scrollable: true,
          columnConfig: columnWidths,
          columnTitle: columnTitles,
          data: details,
          border: const TableBorder(
            horizontalInside: BorderSide(color: AppColors.lineColor),
          ),
          columnTitleBuilder: (title, columnIndex) {
            var type = enabledColumnTypes[columnIndex];

            // 如果是设置列，显示设置图标
            if (type == TenderColumnType.setting) {
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  //设置列配置
                  DialogUtil.showAlertDialog(
                    context,
                    child: ColumnConfigPages(
                      columnPagesType: ColumnPagesType.ColumnPagesTenderManage,
                      changed: (config) {
                        setState(() {
                          columnConfig = getColumnData(config: config);
                        });
                      },
                    ),
                  );
                },
                child: Container(
                  height: rowHeight,
                  color: ColorUtil.stringColor("#F5F5F5"),
                  alignment: Alignment.center,
                  child: IconFont(IconNames.shezhi),
                ),
              );
            }

            // 确定对齐方式
            AlignmentGeometry alignment;
            switch (type) {
              case TenderColumnType.unit:
              case TenderColumnType.image:
              case TenderColumnType.number:
                alignment = Alignment.center;
                break;
              default:
                alignment = Alignment.centerLeft;
            }

            return Container(
              height: rowHeight,
              color: ColorUtil.stringColor("#F5F5F5"),
              alignment: alignment,
              child: HaloPosLabel(
                title,
                maxLines: 1,
                textStyle: TextStyle(
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  decoration: TextDecoration.none,
                  fontSize: ScreenUtil().setSp(20),
                ),
              ),
            );
          },
          cellBuilder: (item, columnIndex) {
            var type = enabledColumnTypes[columnIndex];

            // 处理不同类型的列
            switch (type) {
              case TenderColumnType.userCode: // 商品编码
                return _buildTabItem(item.pUserCode ?? "");
              case TenderColumnType.image: // 图片
                return _buildTabPic(item.picUrl ?? "");
              case TenderColumnType.pName: // 商品名称
                return _buildTabItem(
                  item.pFullName ?? "",
                  detailDto: item,
                  alignment: Alignment.center,
                );
              case TenderColumnType.unit: // 单位
                return _buildTabItem(
                  item.unitName,
                  alignment: Alignment.center,
                );
              case TenderColumnType.number: // 数量
                return _buildTabItem(
                  item.unitQty.toString(),
                  alignment: Alignment.center,
                );
              case TenderColumnType.barCode: // 条码
                return _buildTabItem(item.fullbarcode);
              case TenderColumnType.model: // 型号
                return _buildTabItem(item.ptypetype ?? "");
              case TenderColumnType.spec: // 规格
                return _buildTabItem(item.standard ?? "");
              case TenderColumnType.attributeFormat: // 属性格式
                return _buildTabItem(item.propFormat ?? "");
              case TenderColumnType.attributeCombo: // 属性组合
                return _buildTabItem(item.skuName ?? "");
              case TenderColumnType.serialNumber: // 序列号
                return _buildTabItem(
                  item.serialNoList.map((e) => e.snno).join(","),
                );
              case TenderColumnType.produceDate: // 生产日期
                return _buildTabItem(
                  item.produceDate != null
                      ? formatDateStringToLocal(
                        item.produceDate,
                        format: "yyyy-MM-dd",
                      )
                      : "",
                );
              case TenderColumnType.qualityDays: // 保质期
                return _buildTabItem(item.protectDays?.toString() ?? "");
              case TenderColumnType.expireDate: // 到期日期
                return _buildTabItem(
                  item.expireDate != null
                      ? formatDateStringToLocal(
                        item.expireDate,
                        format: "yyyy-MM-dd",
                      )
                      : "",
                );
              case TenderColumnType.batchNumber: // 批次号
                return _buildTabItem(item.batchNo ?? "");
              case TenderColumnType.setting: // 设置列
                return Container(
                  height: rowHeight,
                  color: Colors.white,
                  child: Column(
                    children: [
                      Expanded(child: Container()),
                      const Divider(height: 1, color: AppColors.lineColor),
                    ],
                  ),
                );
              default:
                return Container(height: rowHeight, color: Colors.white);
            }
          },
        ),
      ),
    );
  }

  ///单据信息
  Widget _buildOrderInfoWidget(BuildContext context) {
    final style = TextStyle(fontSize: 24.sp, overflow: TextOverflow.ellipsis);
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  "调出仓库：${goodsBillDto.kfullname ?? ""}",
                  style: style,
                ),
              ),
              Expanded(
                child: Text(
                  "调入仓库：${goodsBillDto.kfullname2 ?? ""}",
                  style: style,
                ),
              ),
              Expanded(
                child: Text(
                  "经手人：${goodsBillDto.efullname ?? ""}",
                  style: style,
                ),
              ),
            ],
          ),
          SizedBox(height: 10.h),
          Row(
            children: [
              Expanded(
                child: Text(
                  "单据日期：${formatDateStringToLocal(goodsBillDto.date)}",
                  style: style,
                ),
              ),
              Expanded(child: Text("备注：${goodsBillDto.memo}", style: style)),
              Expanded(child: Container()),
            ],
          ),
        ],
      ),
    );
  }

  ///底部确认入库和打印
  Widget _buildButton(BuildContext context) {
    return Visibility(
      visible:
          _permissionDto.shopsaletenderManageprint == true || enableReceive,
      child: Container(
        alignment: Alignment.centerRight,
        padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            HaloButton(
              visible: enableReceive,
              width: 200.w,
              height: 66.h,
              borderRadius: 6.w,
              backgroundColor: const Color(0xFFF1A51C),
              textColor: Colors.white,
              fontSize: 30.sp,
              text: "确认入库",
              onPressed: () {
                showDialog<Map<String, dynamic>>(
                  context: context,
                  builder:
                      (_) => TenderConfirmDialog(
                        goodsBillDto: goodsBillDto,
                        openColumnConfigCallback:
                            () => Scaffold.of(context).openEndDrawer(),
                      ),
                ).then((request) => _confirm(request));
              },
            ),
            SizedBox(width: 20.w),
            HaloButton(
              visible: _permissionDto.shopsaletenderManageprint == true,
              width: 200.w,
              height: 66.h,
              borderRadius: 6.w,
              backgroundColor: Colors.white,
              borderColor: const Color(0xFF666666),
              buttonType: HaloButtonType.outlinedButton,
              outLineWidth: 1,
              textColor: AppColors.titleBoldTextColor,
              fontSize: 30.sp,
              text: "打印",
              onPressed:
                  () =>
                      PrintTool.printBill(context, goodsBillDto, printCount: 1),
            ),
          ],
        ),
      ),
    );
  }

  ///调拨入库
  void _confirm(Map<String, dynamic>? request) {
    if (request != null) {
      List<Map<String, dynamic>> details = request["details"];
      List<GoodsDetailDto> stockGoodsList =
          details
              .map((e) => e.remove("goods") as GoodsDetailDto?)
              .where((element) => element != null)
              .cast<GoodsDetailDto>()
              .toList();
      BillModel.goodsTransInStock(context, request)
          .then((result) {
            if (result != null) {
              _autoPrint();
              //若入库后单据状态大于等于已入库,则刷新列表
              if ((result.intPostState ?? 0) >=
                  BillPostState.STOCK_INOUT_COMPLETED.state) {
                widget.onChange();
              }
              //增加本地库存
              if (stockGoodsList.isNotEmpty) {
                PtypeModel.changePtypeStockQtyByGoodsBill(
                  inDetail: stockGoodsList,
                );
              }
            }
          })
          .whenComplete(() => _billOrderControllerChanged());
    }
  }

  String getCountWithBill() {
    List<GoodsDetailDto> detail =
        goodsBillDto.outDetail.isEmpty
            ? goodsBillDto.inDetail
            : goodsBillDto.outDetail;
    int count = 0;
    for (var element in detail) {
      if (element.comboId != "0" || element.comboRow) {
        //套餐行
        count += element.unitQty.toInt();
      } else if (element.comboRowParId != "0") {
        //套餐明细行
      } else {
        count += element.unitQty.toInt();
      }
    }
    return "总数量:$count";
  }

  ///自动打印小票
  void _autoPrint() {
    TenderManageConfig config = SpTool.getTenderManageConfig();
    if (config.autoPrint &&
        (_permissionDto.shopsaletenderManageautoPrint == true)) {
      PrintTool.printBill(context, goodsBillDto, printCount: 1);
    }
  }

  Future<GoodsBillDto> onRequestData(String vchCode) async {
    return await (BillModel.getGoodsBill(
          context,
          vchCode,
          BillTypeData[BillType.GoodsTrans],
          BillBusinessTypeString[BillBusinessType.GoodsTrans]!,
        )) ??
        GoodsBillDto();
  }
}
