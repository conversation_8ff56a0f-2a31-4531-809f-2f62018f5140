import 'package:flutter/material.dart';
import '../../../bill/entity/order_bill_item_entity.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/tendermanager/tender_manage.dart';
import '../../../common/tool/date_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_post_state.dart';
import '../../../widgets/base/base_list.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart' hide DateUtil;
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/widget/halo_container.dart';

///调货管理页面左侧单据列表
class TenderListManage extends BaseListPage {
  final void Function(int index, dynamic item) onChanged;

  final FilterController filterController;

  const TenderListManage(
      {Key? key, required this.onChanged, required this.filterController})
      : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      TenderListManageState();
}

class TenderListManageState extends BaseListPageState<TenderListManage> {
  int selectIndex = 0;

  @override
  Widget buildItemView(int index, item) {
    OrderBillItem itemData = item;
    return HaloContainer(
      height: 100.w,
      color: selectIndex == index ? const Color(0xFFF4F7FF) : Colors.white,
      padding: EdgeInsets.only(
          left: ScreenUtil().setWidth(32),
          right: ScreenUtil().setWidth(15),
          top: ScreenUtil().setHeight(15),
          bottom: ScreenUtil().setHeight(10)),
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.max,
      children: [
        Flexible(
          flex: 3,
          child: HaloContainer(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              HaloContainer(
                direction: Axis.vertical,
                crossAxisAlignment: CrossAxisAlignment.start,
                padding: EdgeInsets.only(left: ScreenUtil().setWidth(11)),
                children: [
                  HaloPosLabel(
                    itemData.billNumber ?? "",
                    textStyle: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                        decoration: TextDecoration.none,
                        fontSize: 26.sp),
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  HaloPosLabel(
                    formatDateStringToLocal(itemData.billDate),
                    textStyle: TextStyle(
                        color: Colors.grey,
                        decoration: TextDecoration.none,
                        fontSize: 20.sp),
                  ),
                ],
              ),
            ],
          ),
        ),
        Visibility(
          visible: true,
          child: Flexible(
            child: HaloPosLabel(
              itemData.postState == BillPostState.PROCESS_COMPLETED.state ||
                      itemData.postState == BillPostState.STOCK_INOUT_PART.state
                  ? "待入库"
                  : "",
              textStyle: TextStyle(
                  color: const Color(0xFF21BE76),
                  decoration: TextDecoration.none,
                  fontSize: 24.sp),
            ),
          ),
        )
      ],
    );
  }

  @override
  String getActionBarTitle() => "";

  @override
  onItemClick(int index, item) {
    setState(() => selectIndex = index);
    widget.onChanged(selectIndex, item);
  }

  @override
  Future<List> onRequestData() {
    return BillModel.orderBillAllList(context, getRequestParams())
        .then((value) {
      if (pageIndex == 1) {
        ///下拉
        selectIndex = 0;
        widget.onChanged(
            selectIndex, value.isNotEmpty ? value[selectIndex] : null);
      }
      return value;
    });
  }

  @override
  void initState() {
    filterParams["vchtypes"] = [3000];
    // filterParams["otypeId"] = SpTool.getStoreInfo()?.otypeId ?? "";
    filterParams["orderSaleModeList"] = [];
    getFilterParams();
    widget.filterController.addListener(onFilterParamsChange);
    super.initState();
  }

  @override
  void didUpdateWidget(TenderListManage oldWidget) {
    if (oldWidget.filterController != widget.filterController) {
      oldWidget.filterController.removeListener(onFilterParamsChange);
      widget.filterController.addListener(onFilterParamsChange);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.filterController.removeListener(onFilterParamsChange);
    super.dispose();
  }

  ///构建请求参数
  Map<String, dynamic> getRequestParams() {
    Map<String, dynamic> data = {};
    data["pageIndex"] = pageIndex;
    data["pageSize"] = pageSize;
    data["queryParams"] = filterParams;
    return data;
  }

  ///获取筛选条件
  void getFilterParams() {
    filterParams["startTime"] =
        formatDateStringToUtc(widget.filterController.startTime.value);
    filterParams["endTime"] =
        formatDateStringToUtc(widget.filterController.endTime.value);
    filterParams["billNumber"] = widget.filterController.billNumber.value;
    String state = widget.filterController.postState.value;
    //postState 7=已出入库 8=已核算 10=待出入库
    if (state == "全部") {
      filterParams["postStateList"] = [
        BillPostState.PROCESS_COMPLETED.state, //待出入库
        BillPostState.STOCK_INOUT_PART.state, //部分出入库
        BillPostState.STOCK_INOUT_COMPLETED.state, //出入库完成
        BillPostState.ACCOUNTING_COMPLETED.state //核算完成
      ];
    } else if (state == "已出库") {
      filterParams["postStateList"] = [
        BillPostState.STOCK_INOUT_PART.state, //部分出入库
        BillPostState.STOCK_INOUT_COMPLETED.state, //出入库完成
        BillPostState.ACCOUNTING_COMPLETED.state //核算完成
      ];
    } else if (state == "待入库") {
      filterParams["postStateList"] = [
        BillPostState.PROCESS_COMPLETED.state, //待出入库
        BillPostState.STOCK_INOUT_PART.state, //部分出入库
      ];
    } else if (state == "已入库") {
      filterParams["postStateList"] = [
        BillPostState.STOCK_INOUT_COMPLETED.state, //出入库完成
        BillPostState.ACCOUNTING_COMPLETED.state //核算完成
      ];
    }
    final ktypeId = SpTool.getStoreInfo()!.ktypeId;
    if (widget.filterController.businessType.value == "调出") {
      filterParams["ktypeId"] = ktypeId;
      filterParams["ktypeId2"] = null;
    } else {
      filterParams["ktypeId"] = null;
      filterParams["ktypeId2"] = ktypeId;
    }
  }

  ///筛选条件更改的回调方法
  void onFilterParamsChange() {
    getFilterParams();
    selectIndex = 0;
    pageIndex = 1;
    onLoadData();
    dataSource.clear();
  }
}
