enum ColumnType {
  ///商品编码
  userCode,

  ///条码
  barcode,

  ///商品名称
  ptypeName,

  ///应到数量
  shouldReceiveQty,

  ///待入库数量
  uncomplateQty,

  ///实际到货数量
  actualQty,

  ///单位
  unit,

  ///批次
  batch,

  ///生产日期
  produceDate,

  ///保质期
  protectDays,

  ///序列号
  sn,

  ///数量
  qty,
}

extension ColumnTypeExtension on ColumnType {
  String get title {
    switch (this) {
      case ColumnType.userCode:
        return "商品编码";
      case ColumnType.barcode:
        return "条码";
      case ColumnType.ptypeName:
        return "商品名称";
      case ColumnType.shouldReceiveQty:
        return "应到数量";
      case ColumnType.uncomplateQty:
        return "待入库数量";
      case ColumnType.actualQty:
        return "实际到货数量";
      case ColumnType.unit:
        return "单位";
      case ColumnType.batch:
        return "批次";
      case ColumnType.produceDate:
        return "生产日期";
      case ColumnType.protectDays:
        return "保质期(天数)";
      case ColumnType.sn:
        return "序列号";
      case ColumnType.qty:
        return "数量";
    }
  }
}

abstract class TransferColumnConfig {
  ///列配置
  final Map<String, bool> columnConfig;

  TransferColumnConfig({required this.columnConfig});

  Map<String, dynamic> toJson() {
    return {
      "columnConfig": columnConfig,
    };
  }
}

///调拨单管理配置
class TenderManageConfig extends TransferColumnConfig {
  ///默认列配置
  static final Map<String, bool> defaultColumnConfig = {
    ColumnType.userCode.index.toString(): false,
    ColumnType.barcode.index.toString(): true,
    ColumnType.ptypeName.index.toString(): true,
    ColumnType.shouldReceiveQty.index.toString(): true,
    ColumnType.uncomplateQty.index.toString(): true,
    ColumnType.actualQty.index.toString(): true,
    ColumnType.unit.index.toString(): true,
    ColumnType.batch.index.toString(): false,
    ColumnType.produceDate.index.toString(): false,
    ColumnType.protectDays.index.toString(): false,
    ColumnType.sn.index.toString(): false,
  };

  ///自动打印
  bool autoPrint;

  TenderManageConfig.defaultConfig()
      : autoPrint = false,
        super(columnConfig: {...defaultColumnConfig});

  TenderManageConfig.fromJson(Map<String, dynamic> json)
      : autoPrint = json["autoPrint"] ?? false,
        super(
            columnConfig: (json["columnConfig"] as Map<String, dynamic>?)
                    ?.cast<String, bool>() ??
                {...defaultColumnConfig});

  @override
  Map<String, dynamic> toJson() {
    return super.toJson()..addAll({"autoPrint": autoPrint});
  }
}

///调拨订单配置
class TransferAndAllocateConfig extends TransferColumnConfig {
  ///默认列配置
  static final Map<String, bool> defaultColumnConfig = {
    ColumnType.userCode.index.toString(): false,
    ColumnType.barcode.index.toString(): false,
    ColumnType.ptypeName.index.toString(): true,
    ColumnType.qty.index.toString(): true,
    ColumnType.unit.index.toString(): true,
    ColumnType.sn.index.toString(): false,
    ColumnType.batch.index.toString(): false,
    ColumnType.produceDate.index.toString(): false,
    ColumnType.protectDays.index.toString(): false,

  };

  TransferAndAllocateConfig.defaultConfig()
      : super(columnConfig: {...defaultColumnConfig});

  TransferAndAllocateConfig.fromJson(Map<String, dynamic> json)
      : super(
            columnConfig: (json["columnConfig"] as Map<String, dynamic>?)
                    ?.cast<String, bool>() ??
                {...defaultColumnConfig});
}
