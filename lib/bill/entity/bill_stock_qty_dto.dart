class BillStockQtyDto {
  BillStockQtyDto({
      this.rowIndex, 
      this.ptypeId, 
      this.ktypeId, 
      this.skuId, 
      this.batchenabled, 
      this.propenabled, 
      this.batchNo, 
      this.produceDate, 
      this.expireDate, 
      this.costId, 
      this.protectDays,});

  BillStockQtyDto.fromJson(dynamic json) {
    rowIndex = json['rowIndex'];
    ptypeId = json['ptypeId'];
    ktypeId = json['ktypeId'];
    skuId = json['skuId'];
    batchenabled = json['batchenabled'];
    propenabled = json['propenabled'];
    batchNo = json['batchNo'];
    produceDate = json['produceDate'];
    expireDate = json['expireDate'];
    costId = json['costId'];
    protectDays = json['protectDays'];
  }
  num? rowIndex;
  String? ptypeId;
  String? ktypeId;
  String? skuId;
  bool? batchenabled;
  bool? propenabled;
  dynamic batchNo;
  dynamic produceDate;
  dynamic expireDate;
  dynamic costId;
  dynamic protectDays;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['rowIndex'] = rowIndex;
    map['ptypeId'] = ptypeId;
    map['ktypeId'] = ktypeId;
    map['skuId'] = skuId;
    map['batchenabled'] = batchenabled;
    map['propenabled'] = propenabled;
    map['batchNo'] = batchNo;
    map['produceDate'] = produceDate;
    map['expireDate'] = expireDate;
    map['costId'] = costId;
    map['protectDays'] = protectDays;
    return map;
  }

}