/**     
  * @ClassName:      bill_save_exception_detail_dto.dart
   * @CreateDate:    2020/3/30 15:13
  * @Author:         tlan
  * @Description:   单据保存异常明细对象
*/

class BillSaveExceptionDetailDTO {
  String? batchNo;
  String? costPrice;
  String? expirationDate;
  String? kfullName;
  String? kfullname;
  String? kfullname2;
  String? ktypeId;
  String? ktypeId2;
  String? message;
  String? minPrice;
  String? pfullname;
  String? produceDate;
  String? prop1Name;
  String? prop2Name;
  String? propValues;
  String? protectDays;
  String? ptypeId;
  String? ptypeUnit;
  String? ptypeUserCode;
  String? qty;
  String? recPrice;
  String? snNo;
  String? snNoMemo;
  String? stockQty;

  static BillSaveExceptionDetailDTO fromMap(Map<String, dynamic>? map) {
    if (map == null) return BillSaveExceptionDetailDTO();
    BillSaveExceptionDetailDTO billSaveExceptionDetailDTO =
        BillSaveExceptionDetailDTO();
    billSaveExceptionDetailDTO.batchNo = map['batchNo'];
    billSaveExceptionDetailDTO.costPrice = map['costPrice'].toString();
    billSaveExceptionDetailDTO.expirationDate = map['expirationDate'];
    billSaveExceptionDetailDTO.kfullName = map['kfullName'];
    billSaveExceptionDetailDTO.kfullname = map['kfullname'];
    billSaveExceptionDetailDTO.kfullname2 = map['kfullname2'];
    billSaveExceptionDetailDTO.ktypeId = map['ktypeId'];
    billSaveExceptionDetailDTO.ktypeId2 = map['ktypeId2'];
    billSaveExceptionDetailDTO.message = map['message'];
    billSaveExceptionDetailDTO.minPrice = map['minPrice'].toString();
    billSaveExceptionDetailDTO.pfullname = map['pfullname'];
    billSaveExceptionDetailDTO.produceDate = map['produceDate'];
    billSaveExceptionDetailDTO.prop1Name = map['prop1Name'];
    billSaveExceptionDetailDTO.prop2Name = map['prop2Name'];
    billSaveExceptionDetailDTO.propValues = map['propValues'];
    billSaveExceptionDetailDTO.protectDays = map['protectDays'];
    billSaveExceptionDetailDTO.ptypeId = map['ptypeId'];
    billSaveExceptionDetailDTO.ptypeUnit = map['ptypeUnit'];
    billSaveExceptionDetailDTO.ptypeUserCode = map['ptypeUserCode'];
    billSaveExceptionDetailDTO.qty = map['qty'].toString();
    billSaveExceptionDetailDTO.recPrice = map['recPrice'].toString();
    billSaveExceptionDetailDTO.snNo = map['snNo'];
    billSaveExceptionDetailDTO.snNoMemo = map['snNoMemo'];
    billSaveExceptionDetailDTO.stockQty = map['stockQty'];
    return billSaveExceptionDetailDTO;
  }

  Map toJson() => {
        "batchNo": batchNo,
        "costPrice": costPrice,
        "expirationDate": expirationDate,
        "kfullName": kfullName,
        "kfullname": kfullname,
        "kfullname2": kfullname2,
        "ktypeId": ktypeId,
        "ktypeId2": ktypeId2,
        "message": message,
        "minPrice": minPrice,
        "pfullname": pfullname,
        "produceDate": produceDate,
        "prop1Name": prop1Name,
        "prop2Name": prop2Name,
        "propValues": propValues,
        "protectDays": protectDays,
        "ptypeId": ptypeId,
        "ptypeUnit": ptypeUnit,
        "ptypeUserCode": ptypeUserCode,
        "qty": qty,
        "recPrice": recPrice,
        "snNo": snNo,
        "snNoMemo": snNoMemo,
        "stockQty": stockQty,
      };
}
