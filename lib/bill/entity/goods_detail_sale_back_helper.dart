import 'package:haloui/utils/math_util.dart';

import 'goods_detail_dto.dart';

/// 创建时间：11/28/22
/// 作者：xiaotiaochong
/// 描述：

class GoodsSaleBackHelper {
  ///每一个数量的优惠分摊,退货单使用
  // num qtyPreferentialShare = 0.0;

  //region 最后一次退相关数据
  // num lastCurrencyDisedTaxedTotal = 0.0;
  // num lastCurrency = 0.0;
  //
  // //当第一次退，这就是优惠分摊最大值
  // //多次退，是当前最大值
  // num lastCurrencyOrderPreferentialAllotTotal = 0.0;
  // num lastCurrencyPreferentialTotal = 0.0;
  // num lastCurrencyTaxTotal = 0.0;
  // num lastCurrencyTotal = 0.0;

  // num lastGivePreferentialTotalTotal = 0.0;

  ///是否是提货券赠品
  bool isCouponGift = false;

  ///原单原商品
  GoodsDetailDto? originalGoods;

  ///原单商品赠金优惠
  num originalGivePreferentialTotal = 0;

  ///已退回赠金金额
  num refundGivePreferentialTotal = 0;

  ///原单单品优惠金额
  num originalPtypePreferentialTotal = 0;

  ///已退回单品优惠金额
  num refundPtypePreferentialTotal = 0;

  ///原单商品分摊金额
  num originalCurrencyOrderPreferentialAllotTotal = 0;

  ///已退回商品分摊金额
  num refundCurrencyOrderPreferentialAllotTotal = 0;

  ///原单最终优惠
  num originalCurrencyPreferentialTotal = 0;

  ///已退最终优惠
  num refundCurrencyPreferentialTotal = 0;

  ///原单税额
  num originalCurrencyTaxTotal = 0;

  ///已退税额
  num refundCurrencyTaxTotal = 0;

  ///原单单据金额
  num originalCurrencyTotal = 0;

  ///已退单据金额
  num refundCurrencyTotal = 0;

  ///原单折后含税金额
  num originalCurrencyDisedTaxedTotal = 0;

  ///已退折后含税金额
  num refundCurrencyDisedTaxedTotal = 0;

  ///原单折后不含税金额
  num originalCurrencyDisedTotal = 0;

  ///已退折后不含税金额
  num refundCurrencyDisedTotal = 0;

  //endregion

  static GoodsSaleBackHelper fromMap(Map<dynamic, dynamic>? map) {
    if (map == null) return GoodsSaleBackHelper();
    GoodsSaleBackHelper helper = GoodsSaleBackHelper();
    helper.originalGivePreferentialTotal =
        map["originalGivePreferentialTotal"] ?? 0;
    helper.originalCurrencyOrderPreferentialAllotTotal =
        map["originalCurrencyOrderPreferentialAllotTotal"] ?? 0;
    helper.refundGivePreferentialTotal =
        map["refundGivePreferentialTotal"] ?? 0;
    helper.refundCurrencyOrderPreferentialAllotTotal =
        map["refundCurrencyOrderPreferentialAllotTotal"] ?? 0;
    helper.refundGivePreferentialTotal =
        map["refundGivePreferentialTotal"] ?? 0;
    helper.originalCurrencyTaxTotal = map["originalCurrencyTaxTotal"] ?? 0;
    helper.refundCurrencyTotal = map["refundCurrencyTotal"] ?? 0;
    helper.refundCurrencyTaxTotal = map["refundCurrencyTaxTotal"] ?? 0;
    helper.originalCurrencyTotal = map["originalCurrencyTotal"] ?? 0;
    helper.refundCurrencyPreferentialTotal =
        map["refundCurrencyPreferentialTotal"] ?? 0;
    helper.originalCurrencyPreferentialTotal =
        map["originalCurrencyPreferentialTotal"] ?? 0;
    helper.originalCurrencyDisedTaxedTotal =
        map["originalCurrencyDisedTaxedTotal"] ?? 0;
    helper.refundCurrencyDisedTaxedTotal =
        map["refundCurrencyDisedTaxedTotal"] ?? 0;
    helper.originalCurrencyDisedTotal = map["originalCurrencyDisedTotal"] ?? 0;
    helper.refundCurrencyDisedTotal = map["refundCurrencyDisedTotal"] ?? 0;
    helper.originalPtypePreferentialTotal =
        map["originalPtypePreferentialTotal"] ?? 0;
    helper.refundPtypePreferentialTotal =
        map["refundPtypePreferentialTotal"] ?? 0;
    helper.isCouponGift = map["isCouponGift"] ?? false;
    var o = map["originalGoods"];
    if (o != null) {
      helper.originalGoods = GoodsDetailDto.fromMap(o);
    }
    return helper;
  }

  Map toJson() => {
        "refundCurrencyTaxTotal": refundCurrencyTaxTotal,
        "originalCurrencyTotal": originalCurrencyTotal,
        "refundCurrencyTotal": refundCurrencyTotal,
        "originalGivePreferentialTotal": originalGivePreferentialTotal,
        "originalCurrencyOrderPreferentialAllotTotal":
            originalCurrencyOrderPreferentialAllotTotal,
        "refundGivePreferentialTotal": refundGivePreferentialTotal,
        "refundCurrencyOrderPreferentialAllotTotal":
            refundCurrencyOrderPreferentialAllotTotal,
        "originalCurrencyTaxTotal": originalCurrencyTaxTotal,
        "refundCurrencyPreferentialTotal": refundCurrencyPreferentialTotal,
        "originalCurrencyDisedTaxedTotal": originalCurrencyDisedTaxedTotal,
        "refundCurrencyDisedTaxedTotal": refundCurrencyDisedTaxedTotal,
        "originalCurrencyDisedTotal": originalCurrencyDisedTotal,
        "refundCurrencyDisedTotal": refundCurrencyDisedTotal,
        "originalCurrencyPreferentialTotal": originalCurrencyPreferentialTotal,
        "originalPtypePreferentialTotal": originalPtypePreferentialTotal,
        "refundPtypePreferentialTotal": refundPtypePreferentialTotal,
        "originalGoods": originalGoods?.toJson(),
        "isCouponGift": isCouponGift,
      };

  ///剩余未退的储值赠金优惠
  num remainGivePreferentialTotal() {
    return MathUtil.subtractDec(
            originalGivePreferentialTotal, refundGivePreferentialTotal)
        .toDouble();
  }

  ///剩余未退的税额
  num remainCurrencyTaxTotal() {
    return MathUtil.subtractDec(
            originalCurrencyTaxTotal, refundCurrencyTaxTotal)
        .toDouble();
  }

  ///剩余未退的单据金额
  num remainCurrencyTotal() {
    return MathUtil.subtractDec(originalCurrencyTotal, refundCurrencyTotal)
        .toDouble();
  }

  ///剩余未退的商品分摊金额
  num remainCurrencyOrderPreferentialAllotTotal() {
    return MathUtil.subtractDec(originalCurrencyOrderPreferentialAllotTotal,
            refundCurrencyOrderPreferentialAllotTotal)
        .toDouble();
  }

  ///剩余未退的最终优惠
  num remainCurrencyPreferentialTotal() {
    return MathUtil.subtractDec(
            originalCurrencyPreferentialTotal, refundCurrencyPreferentialTotal)
        .toDouble();
  }

  ///剩余未退的折后含税金额
  num remainCurrencyDisedTaxedTotal() {
    return MathUtil.subtractDec(
            originalCurrencyDisedTaxedTotal, refundCurrencyDisedTaxedTotal)
        .toDouble();
  }

  ///剩余未退的不含税金额
  num remainCurrencyDisedTotal() {
    return MathUtil.subtractDec(
            originalCurrencyDisedTotal, refundCurrencyDisedTotal)
        .toDouble();
  }

  ///剩余未退的单品优惠金额
  num remainPtypePreferentialTotal() {
    return MathUtil.subtractDec(
            originalPtypePreferentialTotal, refundPtypePreferentialTotal)
        .toDouble();
  }
}
