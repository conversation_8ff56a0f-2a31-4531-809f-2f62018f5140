import '../../bill/tool/decimal_display_helper.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/entity/ptype_suit_detail_model.dart';
import 'package:haloui/utils/math_util.dart';

/// id : "589408601261162339"
/// profileId : "450279889958981633"
/// typeid : "00029"
/// partypeid : "00000"
/// usercode : "3"
/// fullname : "3"
/// shortname : null
/// namepy : null
/// classed : null
/// stoped : false
/// deleted : false
/// rowindex : null
/// barcode : "333"
/// standard : null
/// ptypeType : null
/// ptypeArea : null
/// memo : ""
/// createType : null
/// costMode : null
/// pcategory : null
/// taxNumber : null
/// taxRate : null
/// costPrice : null
/// supplyInfo : null
/// brandId : null
/// ktypeLimit : null
/// snenabled : null
/// propenabled : null
/// batchenabled : null
/// protectDays : null
/// protectDaysUnit : null
/// protectWarndays : null
/// protectWarndaysUnit : null
/// weight : 0
/// weightUnit : 1
/// lithiumBattery : null
/// solid : null
/// difficultyLevel : null
/// weighted : null
/// retailDefaultUnit : null
/// saleDefaultUnit : null
/// buyDefaultUnit : null
/// stockDefaultUnit : null
/// syncStock : true
/// ptypeLength : null
/// ptypeWidth : null
/// ptypeHeight : null
/// lengthUnit : null
/// createTime : "2020-05-12T08:27:45.000+0000"
/// updateTime : "2020-05-12T08:27:45.000+0000"
/// parFullname : null
/// ptypeCombo : {"comboId":null,"profileId":"450279889958981633","total":5E+1,"needPrintName":1,"comboType":0,"singlePtype":false,"needSplitDetail":false,"uniqueMark":null,"logisticsPrintName":true,"goodsPrintName":true,"createTime":"2020-05-12T08:27:45.000+0000","updateTime":"2020-05-12T08:27:45.000+0000"}
/// picUrl : null
/// ptypeComboTotal : 5E+1
/// ptypeComboNeedPrintName : 1
/// ptypeComboComboType : 0
/// ptypeComboSinglePtype : false
/// ptypeComboNeedSplitDetail : false
/// ptypeComboLogisticsPrintName : true
/// ptypeComboGoodsPrintName : true

class PtypeSuitModel {
  bool isUpdatePrice = false;
  num count = 1;
  String discount = "1";
  String unitId = "0";

  String id = "";
  String? profileId;
  String? typeid;
  String? partypeid;
  String? usercode;
  String? fullname;
  dynamic shortname;
  dynamic namepy;
  dynamic classed;
  bool? stoped;
  bool? deleted;
  dynamic rowindex;
  String? barcode;
  dynamic standard;
  dynamic ptypeType;
  dynamic ptypeArea;
  String? memo;
  dynamic createType;
  dynamic costMode;

  ///商品类型，0为实物商品，1为虚拟商品
  dynamic pcategory;
  dynamic taxNumber;
  dynamic taxRate;
  dynamic costPrice;
  dynamic supplyInfo;
  dynamic brandId;
  dynamic ktypeLimit;
  dynamic snenabled;
  dynamic propenabled;
  dynamic batchenabled;
  dynamic protectDays;
  dynamic protectDaysUnit;
  dynamic protectWarndays;
  dynamic protectWarndaysUnit;
  double? weight;
  int? weightUnit;
  dynamic lithiumBattery;
  dynamic solid;
  dynamic difficultyLevel;
  dynamic weighted;
  dynamic retailDefaultUnit;
  dynamic saleDefaultUnit;
  dynamic buyDefaultUnit;
  dynamic stockDefaultUnit;
  bool? syncStock;
  dynamic ptypeLength;
  dynamic ptypeWidth;
  dynamic ptypeHeight;
  dynamic lengthUnit;

  PtypeSuitUnit? unit;

  String? createTime;
  String? updateTime;
  dynamic parFullname;
  PtypeComboBean? ptypeCombo;
  dynamic picUrl;
  num? ptypeComboTotal;
  num? ptypeComboNeedPrintName;
  num? ptypeComboComboType;
  bool? ptypeComboSinglePtype;
  bool? ptypeComboNeedSplitDetail;
  bool? ptypeComboLogisticsPrintName;
  bool? ptypeComboGoodsPrintName;
  List<PtypeSuitDetailModel>? suitDetails;

  GoodsDetailDto changeModel(String ktypeId, String kfullName) {
    GoodsDetailDto goods = GoodsDetailDto();
    String comBoCostPrice =
        DecimalDisplayHelper.getPriceFixed(ptypeCombo?.total.toString() ?? "0");
    goods.costPrice = MathUtil.stringToDouble(comBoCostPrice) ?? 0;
    goods.currencyPrice = MathUtil.stringToDouble(comBoCostPrice)!;
    goods.currencyDisedTaxedPrice = MathUtil.stringToDouble(comBoCostPrice)!;
    goods.currencyDisedPrice = MathUtil.stringToDouble(comBoCostPrice)!;
    goods.currencyDisedTaxedTotal =
        MathUtil.multiplication(comBoCostPrice, count.toString()).toDouble();
    goods.currencyDisedTotal =
        MathUtil.multiplication(comBoCostPrice, count.toString()).toDouble();
    goods.currencyPreferentialTotal = 0;
    goods.currencyTaxTotal =
        MathUtil.multiplication(comBoCostPrice, count.toString()).toDouble();
    goods.currencyTotal =
        MathUtil.multiplication(comBoCostPrice, count.toString()).toDouble();
    goods.fullbarcode = barcode ?? "";
    goods.batchNo = "";
    goods.comboId = ptypeCombo?.comboId ?? id;
    goods.comboRowId = rowindex ?? "";
    goods.comboRowParId = "0";
    goods.comboRow = true;
    goods.needPrintName = ptypeCombo?.needPrintName;
    goods.profileId = profileId;
    goods.picUrl = picUrl ?? "";
    goods.discount = MathUtil.stringToDouble(discount)!;
    goods.pFullName = fullname ?? "";
    goods.pUserCode = usercode;
    goods.ptypeId = id;

    goods.unitId = null == unit ? "" : unit!.id ?? "";
    goods.unitName = null == unit ? "" : unit!.unitName ?? "";
    goods.unitRate = null == unit ? 1 : unit!.unitRate;

    goods.unitId = goods?.unitId ?? "";

    goods.unitQty = count;
    goods.kfullname = kfullName;
    goods.ktypeId = ktypeId;
    goods.skuPrice = 0;
    goods.costMode = costMode ?? 0;
    //商品类型，0为实物商品，1为虚拟商品,2为套餐
    goods.pcategory = pcategory ?? 0;
    return goods;
  }

  static PtypeSuitModel fromMap(Map<dynamic, dynamic> map) {
    PtypeSuitModel ptypeSuitModelBean = PtypeSuitModel();
    ptypeSuitModelBean.unitId = map["unitId"] ?? "";
    ptypeSuitModelBean.id = map['id'];
    ptypeSuitModelBean.profileId = map['profileId'];
    ptypeSuitModelBean.typeid = map['typeid'];
    ptypeSuitModelBean.partypeid = map['partypeid'];
    ptypeSuitModelBean.usercode = map['usercode'];
    ptypeSuitModelBean.fullname = map['fullname'];
    ptypeSuitModelBean.shortname = map['shortname'];
    ptypeSuitModelBean.namepy = map['namepy'];
    ptypeSuitModelBean.classed = map['classed'];
    ptypeSuitModelBean.stoped = map['stoped'];
    ptypeSuitModelBean.deleted = map['deleted'];
    ptypeSuitModelBean.rowindex = map['rowindex'];
    ptypeSuitModelBean.barcode = map['barcode'];
    ptypeSuitModelBean.standard = map['standard'];
    ptypeSuitModelBean.ptypeType = map['ptypeType'];
    ptypeSuitModelBean.ptypeArea = map['ptypeArea'];
    ptypeSuitModelBean.memo = map['memo'];
    ptypeSuitModelBean.createType = map['createType'];
    ptypeSuitModelBean.costMode = map['costMode'];
    ptypeSuitModelBean.pcategory = map['pcategory'];
    ptypeSuitModelBean.taxNumber = map['taxNumber'];
    ptypeSuitModelBean.taxRate = map['taxRate'];
    ptypeSuitModelBean.costPrice = map['costPrice'];
    ptypeSuitModelBean.supplyInfo = map['supplyInfo'];
    ptypeSuitModelBean.brandId = map['brandId'];
    ptypeSuitModelBean.ktypeLimit = map['ktypeLimit'];
    ptypeSuitModelBean.snenabled = map['snenabled'];
    ptypeSuitModelBean.propenabled = map['propenabled'];
    ptypeSuitModelBean.batchenabled = map['batchenabled'];
    ptypeSuitModelBean.protectDays = map['protectDays'];
    ptypeSuitModelBean.protectDaysUnit = map['protectDaysUnit'];
    ptypeSuitModelBean.protectWarndays = map['protectWarndays'];
    ptypeSuitModelBean.protectWarndaysUnit = map['protectWarndaysUnit'];
    ptypeSuitModelBean.weight = map['weight'];
    ptypeSuitModelBean.weightUnit = map['weightUnit'];
    ptypeSuitModelBean.lithiumBattery = map['lithiumBattery'];
    ptypeSuitModelBean.solid = map['solid'];
    ptypeSuitModelBean.difficultyLevel = map['difficultyLevel'];
    ptypeSuitModelBean.weighted = map['weighted'];
    ptypeSuitModelBean.retailDefaultUnit = map['retailDefaultUnit'];
    ptypeSuitModelBean.saleDefaultUnit = map['saleDefaultUnit'];
    ptypeSuitModelBean.buyDefaultUnit = map['buyDefaultUnit'];
    ptypeSuitModelBean.stockDefaultUnit = map['stockDefaultUnit'];
    ptypeSuitModelBean.syncStock = map['syncStock'];
    ptypeSuitModelBean.ptypeLength = map['ptypeLength'];
    ptypeSuitModelBean.ptypeWidth = map['ptypeWidth'];
    ptypeSuitModelBean.ptypeHeight = map['ptypeHeight'];
    ptypeSuitModelBean.lengthUnit = map['lengthUnit'];
    ptypeSuitModelBean.createTime = map['createTime'];
    ptypeSuitModelBean.updateTime = map['updateTime'];
    ptypeSuitModelBean.parFullname = map['parFullname'];
    ptypeSuitModelBean.ptypeCombo = PtypeComboBean.fromMap(map['ptypeCombo']);
    ptypeSuitModelBean.unit = PtypeSuitUnit.fromMap(map["unit"]);
    ptypeSuitModelBean.picUrl = map['picUrl'];
    ptypeSuitModelBean.ptypeComboTotal = map['ptypeComboTotal'];
    ptypeSuitModelBean.ptypeComboNeedPrintName = map['ptypeComboNeedPrintName'];
    ptypeSuitModelBean.ptypeComboComboType = map['ptypeComboComboType'];
    ptypeSuitModelBean.ptypeComboSinglePtype = map['ptypeComboSinglePtype'];
    ptypeSuitModelBean.ptypeComboNeedSplitDetail =
        map['ptypeComboNeedSplitDetail'];
    ptypeSuitModelBean.ptypeComboLogisticsPrintName =
        map['ptypeComboLogisticsPrintName'];
    ptypeSuitModelBean.ptypeComboGoodsPrintName =
        map['ptypeComboGoodsPrintName'];
    ptypeSuitModelBean.suitDetails = <PtypeSuitDetailModel>[];
    List suitDetails = map['suitDetails'];
    suitDetails.forEach((value) {
      ptypeSuitModelBean.suitDetails!.add(PtypeSuitDetailModel.fromMap(value));
    });
    return ptypeSuitModelBean;
  }

  Map toJson() => {
        "unitId": unitId,
        "id": id,
        "profileId": profileId,
        "typeid": typeid,
        "partypeid": partypeid,
        "usercode": usercode,
        "fullname": fullname,
        "shortname": shortname,
        "namepy": namepy,
        "classed": classed,
        "stoped": stoped,
        "deleted": deleted,
        "rowindex": rowindex,
        "barcode": barcode,
        "standard": standard,
        "ptypeType": ptypeType,
        "ptypeArea": ptypeArea,
        "memo": memo,
        "createType": createType,
        "costMode": costMode,
        "pcategory": pcategory,
        "taxNumber": taxNumber,
        "taxRate": taxRate,
        "costPrice": costPrice,
        "supplyInfo": supplyInfo,
        "brandId": brandId,
        "ktypeLimit": ktypeLimit,
        "snenabled": snenabled,
        "propenabled": propenabled,
        "batchenabled": batchenabled,
        "protectDays": protectDays,
        "protectDaysUnit": protectDaysUnit,
        "protectWarndays": protectWarndays,
        "protectWarndaysUnit": protectWarndaysUnit,
        "weight": weight,
        "weightUnit": weightUnit,
        "lithiumBattery": lithiumBattery,
        "solid": solid,
        "difficultyLevel": difficultyLevel,
        "weighted": weighted,
        "retailDefaultUnit": retailDefaultUnit,
        "saleDefaultUnit": saleDefaultUnit,
        "buyDefaultUnit": buyDefaultUnit,
        "stockDefaultUnit": stockDefaultUnit,
        "syncStock": syncStock,
        "ptypeLength": ptypeLength,
        "ptypeWidth": ptypeWidth,
        "ptypeHeight": ptypeHeight,
        "lengthUnit": lengthUnit,
        "createTime": createTime,
        "updateTime": updateTime,
        "parFullname": parFullname,
        "ptypeCombo": ptypeCombo,
        "picUrl": picUrl,
        "ptypeComboTotal": ptypeComboTotal,
        "ptypeComboNeedPrintName": ptypeComboNeedPrintName,
        "ptypeComboComboType": ptypeComboComboType,
        "ptypeComboSinglePtype": ptypeComboSinglePtype,
        "ptypeComboNeedSplitDetail": ptypeComboNeedSplitDetail,
        "ptypeComboLogisticsPrintName": ptypeComboLogisticsPrintName,
        "ptypeComboGoodsPrintName": ptypeComboGoodsPrintName,
        "unit": unit
      };
}

/// comboId : null
/// profileId : "450279889958981633"
/// total : 5E+1
/// needPrintName : 1
/// comboType : 0
/// singlePtype : false
/// needSplitDetail : false
/// uniqueMark : null
/// logisticsPrintName : true
/// goodsPrintName : true
/// createTime : "2020-05-12T08:27:45.000+0000"
/// updateTime : "2020-05-12T08:27:45.000+0000"

class PtypeComboBean {
  dynamic comboId;
  String? profileId;
  double? total;
  num? needPrintName;
  num? comboType;
  bool? singlePtype;
  bool? needSplitDetail;
  dynamic uniqueMark;
  bool? logisticsPrintName;
  bool? goodsPrintName;
  String? createTime;
  String? updateTime;

  static PtypeComboBean fromMap(Map<String, dynamic>? map) {
    PtypeComboBean ptypeComboBean = PtypeComboBean();
    if (map == null) return ptypeComboBean;
    ptypeComboBean.comboId = map['comboId'];
    ptypeComboBean.profileId = map['profileId'];
    ptypeComboBean.total = map['total'];
    ptypeComboBean.needPrintName = map['needPrintName'];
    ptypeComboBean.comboType = map['comboType'];
    ptypeComboBean.singlePtype = map['singlePtype'];
    ptypeComboBean.needSplitDetail = map['needSplitDetail'];
    ptypeComboBean.uniqueMark = map['uniqueMark'];
    ptypeComboBean.logisticsPrintName = map['logisticsPrintName'];
    ptypeComboBean.goodsPrintName = map['goodsPrintName'];
    ptypeComboBean.createTime = map['createTime'];
    ptypeComboBean.updateTime = map['updateTime'];
    return ptypeComboBean;
  }

  Map toJson() => {
        "comboId": comboId,
        "profileId": profileId,
        "total": total,
        "needPrintName": needPrintName,
        "comboType": comboType,
        "singlePtype": singlePtype,
        "needSplitDetail": needSplitDetail,
        "uniqueMark": uniqueMark,
        "logisticsPrintName": logisticsPrintName,
        "goodsPrintName": goodsPrintName,
        "createTime": createTime,
        "updateTime": updateTime,
      };
}

/// id : "584092720210012514"
/// profileId : null
/// ptypeId : "584092720209881442"
/// unitCode : 1
/// unitName : ""
/// unitRate : 1.0
/// barcode : ""
/// createTime : null
/// updateTime : null

class PtypeSuitUnit {
  String? id;
  dynamic profileId;
  String? ptypeId;
  int? unitCode;
  String? unitName;
  double? unitRate;
  String? barcode;
  dynamic createTime;
  dynamic updateTime;

  static PtypeSuitUnit fromMap(Map<String, dynamic>? map) {
    PtypeSuitUnit ptypeSuitUnit = PtypeSuitUnit();
    if (map == null) return ptypeSuitUnit;
    ptypeSuitUnit.id = map['id'];
    ptypeSuitUnit.profileId = map['profileId'];
    ptypeSuitUnit.ptypeId = map['ptypeId'];
    ptypeSuitUnit.unitCode = map['unitCode'];
    ptypeSuitUnit.unitName = map['unitName'];
    ptypeSuitUnit.unitRate = map['unitRate'];
    ptypeSuitUnit.barcode = map['barcode'];
    ptypeSuitUnit.createTime = map['createTime'];
    ptypeSuitUnit.updateTime = map['updateTime'];
    return ptypeSuitUnit;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "unitCode": unitCode,
        "unitName": unitName,
        "unitRate": unitRate,
        "barcode": barcode,
        "createTime": createTime,
        "updateTime": updateTime,
      };
}
