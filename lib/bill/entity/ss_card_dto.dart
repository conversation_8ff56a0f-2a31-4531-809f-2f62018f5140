import '../../bill/entity/ptype/ptype_prop_dto.dart';
import '../../common/tool/sp_tool.dart';
import '../../enum/bill_type.dart';
import 'package:halo_utils/utils/string_util.dart';

import 'goods_detail_dto.dart';

/// id : "895019052173390994"
/// cardTemplateId : "895018846014960786"
/// fullname : "限制折扣券"
/// cardType : 3
/// cardNo : "1664417622576"
/// expiryDay : "2027-09-29 00:00:00"
/// createTime : "2022-09-29 10:13:43"
/// memberEquityValues : [{"id":"895018846015026322","valueType":"discount","fullName":"折扣券","rightsDesc":"","codes":null,"profileId":null,"deleted":0,"createTime":null,"updateTime":null,"stoped":null,"cardTemplateId":null,"cardDeleted":null,"check":false,"detailList":[{"id":"895018846015091858","createTime":null,"updateTime":null,"deleted":null,"stoped":null,"equityValueId":null,"valueCondition":0.0,"valueDetail":0.99,"priority":null,"profileId":null,"miniCostType":1,"ptypeRang":1,"ptypeList":[{"id":"895018846015157394","profileId":"767856022915227648","createTime":"2022-09-29T02:13:18.641+00:00","updateTime":"2022-09-29T02:13:18.641+00:00","ptypeId":"888323696375106943","skuId":"888466967894297983","unitId":"888323696375238015","deleted":false,"pid":"888323696375106943","rowindex":0,"equityValueDetailId":"895018846015091858","ptypeRang":0,"ptypeName":"商品01","usercode":"001","propertyName":"","retailPrice":50.0,"unitName":"个","pcategory":"0","batchEnabled":false,"snEnabled":0}]}]}]
/// cardDetails : [{"id":"895019052173456530","profileId":"767856022915227648","vipId":"894317391956309138","cardTemplateId":"895018846014960786","cardId":"895019052173390994","equityValueId":"895018846015026322","memo":"pos发放优惠券","userCount":0,"count":1}]
/// equityStr : null
/// expiryStr : null
/// expiryDayStr : null
/// validType : 0
/// validValue : "2027-09-29"

///[cardType] 卡券类型 0：等级权益卡，1：普通权益卡，2：代金券 3 折扣券 4礼品券
class SsCardDto {
  String? id;
  String? cardTemplateId;
  String? fullname;
  int? cardType;
  String? cardNo;
  String? expiryDay;
  String? createTime;
  List<MemberEquityValuesBean> memberEquityValues = [];
  List<CardDetailsBean> cardDetails = [];
  dynamic equityStr;
  dynamic expiryStr;
  dynamic expiryDayStr;
  int? validType;
  String? validValue;

  static SsCardDto fromMap(Map<String, dynamic>? map) {
    SsCardDto ssCardDtoBean = SsCardDto();
    if (map == null) return ssCardDtoBean;
    ssCardDtoBean.id = map['id'];
    ssCardDtoBean.cardTemplateId = map['cardTemplateId'];
    ssCardDtoBean.fullname = map['fullname'];
    ssCardDtoBean.cardType = map['cardType'];
    ssCardDtoBean.cardNo = map['cardNo'];
    ssCardDtoBean.expiryDay = map['expiryDay'];
    ssCardDtoBean.createTime = map['createTime'];
    ssCardDtoBean.memberEquityValues =
        (map['memberEquityValues'] as List?)
            ?.map((o) => MemberEquityValuesBean.fromMap(o))
            .toList() ??
        [];
    ssCardDtoBean.cardDetails =
        (map['cardDetails'] as List?)
            ?.map((o) => CardDetailsBean.fromMap(o))
            .toList() ??
        [];
    ssCardDtoBean.equityStr = map['equityStr'];
    ssCardDtoBean.expiryStr = map['expiryStr'];
    ssCardDtoBean.expiryDayStr = map['expiryDayStr'];
    ssCardDtoBean.validType = map['validType'];
    ssCardDtoBean.validValue = map['validValue'];
    return ssCardDtoBean;
  }

  Map toJson() => {
    "id": id,
    "cardTemplateId": cardTemplateId,
    "fullname": fullname,
    "cardType": cardType,
    "cardNo": cardNo,
    "expiryDay": expiryDay,
    "createTime": createTime,
    "memberEquityValues": memberEquityValues,
    "cardDetails": cardDetails,
    "equityStr": equityStr,
    "expiryStr": expiryStr,
    "expiryDayStr": expiryDayStr,
    "validType": validType,
    "validValue": validValue,
  };
}

/// id : "895019052173456530"
/// profileId : "767856022915227648"
/// vipId : "894317391956309138"
/// cardTemplateId : "895018846014960786"
/// cardId : "895019052173390994"
/// equityValueId : "895018846015026322"
/// memo : "pos发放优惠券"
/// userCount : 0
/// count : 1

class CardDetailsBean {
  String id = "";
  String? profileId;
  String? vipId;
  String? cardTemplateId;
  String? cardId;
  String? equityValueId;
  String? memo;
  int? userCount;
  int? count;

  static CardDetailsBean fromMap(Map<String, dynamic> map) {
    CardDetailsBean cardDetailsBean = CardDetailsBean();
    cardDetailsBean.id = map['id'];
    cardDetailsBean.profileId = map['profileId'];
    cardDetailsBean.vipId = map['vipId'];
    cardDetailsBean.cardTemplateId = map['cardTemplateId'];
    cardDetailsBean.cardId = map['cardId'];
    cardDetailsBean.equityValueId = map['equityValueId'];
    cardDetailsBean.memo = map['memo'];
    cardDetailsBean.userCount = map['userCount'];
    cardDetailsBean.count = map['count'];
    return cardDetailsBean;
  }

  Map toJson() => {
    "id": id,
    "profileId": profileId,
    "vipId": vipId,
    "cardTemplateId": cardTemplateId,
    "cardId": cardId,
    "equityValueId": equityValueId,
    "memo": memo,
    "userCount": userCount,
    "count": count,
  };
}

/// id : "895018846015026322"
/// valueType : "discount"
/// fullName : "折扣券"
/// rightsDesc : ""
/// codes : null
/// profileId : null
/// deleted : 0
/// createTime : null
/// updateTime : null
/// stoped : null
/// cardTemplateId : null
/// cardDeleted : null
/// check : false
/// detailList : [{"id":"895018846015091858","createTime":null,"updateTime":null,"deleted":null,"stoped":null,"equityValueId":null,"valueCondition":0.0,"valueDetail":0.99,"priority":null,"profileId":null,"miniCostType":1,"ptypeRang":1,"ptypeList":[{"id":"895018846015157394","profileId":"767856022915227648","createTime":"2022-09-29T02:13:18.641+00:00","updateTime":"2022-09-29T02:13:18.641+00:00","ptypeId":"888323696375106943","skuId":"888466967894297983","unitId":"888323696375238015","deleted":false,"pid":"888323696375106943","rowindex":0,"equityValueDetailId":"895018846015091858","ptypeRang":0,"ptypeName":"商品01","usercode":"001","propertyName":"","retailPrice":50.0,"unitName":"个","pcategory":"0","batchEnabled":false,"snEnabled":0}]}]

class MemberEquityValuesBean {
  String? id;
  String? valueType;
  String? fullName;
  String? rightsDesc;
  dynamic codes;
  dynamic profileId;
  int? deleted;
  dynamic createTime;
  dynamic updateTime;
  dynamic stoped;
  dynamic cardTemplateId;
  dynamic cardDeleted;
  bool? check;
  List<DetailListBean> detailList = [];

  static MemberEquityValuesBean fromMap(Map<String, dynamic>? map) {
    MemberEquityValuesBean memberEquityValuesBean = MemberEquityValuesBean();
    if (map == null) return memberEquityValuesBean;
    memberEquityValuesBean.id = map['id'];
    memberEquityValuesBean.valueType = map['valueType'];
    memberEquityValuesBean.fullName = map['fullName'];
    memberEquityValuesBean.rightsDesc = map['rightsDesc'];
    memberEquityValuesBean.codes = map['codes'];
    memberEquityValuesBean.profileId = map['profileId'];
    memberEquityValuesBean.deleted = map['deleted'];
    memberEquityValuesBean.createTime = map['createTime'];
    memberEquityValuesBean.updateTime = map['updateTime'];
    memberEquityValuesBean.stoped = map['stoped'];
    memberEquityValuesBean.cardTemplateId = map['cardTemplateId'];
    memberEquityValuesBean.cardDeleted = map['cardDeleted'];
    memberEquityValuesBean.check = map['check'];
    memberEquityValuesBean.detailList =
        (map['detailList'] as List?)
            ?.map((o) => DetailListBean.fromMap(o))
            .toList() ??
        [];
    return memberEquityValuesBean;
  }

  Map toJson() => {
    "id": id,
    "valueType": valueType,
    "fullName": fullName,
    "rightsDesc": rightsDesc,
    "codes": codes,
    "profileId": profileId,
    "deleted": deleted,
    "createTime": createTime,
    "updateTime": updateTime,
    "stoped": stoped,
    "cardTemplateId": cardTemplateId,
    "cardDeleted": cardDeleted,
    "check": check,
    "detailList": detailList,
  };
}

/// id : "895018846015091858"
/// createTime : null
/// updateTime : null
/// deleted : null
/// stoped : null
/// equityValueId : null
/// valueCondition : 0.0
/// valueDetail : 0.99
/// priority : null
/// profileId : null
/// miniCostType : 1
/// ptypeRang : 1
/// ptypeList : [{"id":"895018846015157394","profileId":"767856022915227648","createTime":"2022-09-29T02:13:18.641+00:00","updateTime":"2022-09-29T02:13:18.641+00:00","ptypeId":"888323696375106943","skuId":"888466967894297983","unitId":"888323696375238015","deleted":false,"pid":"888323696375106943","rowindex":0,"equityValueDetailId":"895018846015091858","ptypeRang":0,"ptypeName":"商品01","usercode":"001","propertyName":"","retailPrice":50.0,"unitName":"个","pcategory":"0","batchEnabled":false,"snEnabled":0}]

class DetailListBean {
  String? id;
  dynamic createTime;
  dynamic updateTime;
  dynamic deleted;
  dynamic stoped;
  dynamic equityValueId;
  num valueCondition = 0;
  num? valueDetail;
  dynamic priority;
  dynamic profileId;

  ///是否有最低消费 0有 1没有
  int? miniCostType;

  ///会员等级折扣卡，0全部商品，1部分商品，3，排除商品
  int? ptypeRang;
  List<PtypeListBean> ptypeList = [];

  static DetailListBean fromMap(Map<String, dynamic> map) {
    DetailListBean detailListBean = DetailListBean();
    detailListBean.id = map['id'];
    detailListBean.createTime = map['createTime'];
    detailListBean.updateTime = map['updateTime'];
    detailListBean.deleted = map['deleted'];
    detailListBean.stoped = map['stoped'];
    detailListBean.equityValueId = map['equityValueId'];
    detailListBean.valueCondition = map['valueCondition'] ?? 0;
    detailListBean.valueDetail = map['valueDetail'];
    detailListBean.priority = map['priority'];
    detailListBean.profileId = map['profileId'];
    detailListBean.miniCostType = map['miniCostType'];
    detailListBean.ptypeRang = map['ptypeRang'];
    detailListBean.ptypeList =
        (map['ptypeList'] as List?)
            ?.map((o) => PtypeListBean.fromMap(o))
            .toList() ??
        [];
    return detailListBean;
  }

  Map toJson() => {
    "id": id,
    "createTime": createTime,
    "updateTime": updateTime,
    "deleted": deleted,
    "stoped": stoped,
    "equityValueId": equityValueId,
    "valueCondition": valueCondition,
    "valueDetail": valueDetail,
    "priority": priority,
    "profileId": profileId,
    "miniCostType": miniCostType,
    "ptypeRang": ptypeRang,
    "ptypeList": ptypeList,
  };
}

/// id : "895018846015157394"
/// profileId : "767856022915227648"
/// createTime : "2022-09-29T02:13:18.641+00:00"
/// updateTime : "2022-09-29T02:13:18.641+00:00"
/// ptypeId : "888323696375106943"
/// skuId : "888466967894297983"
/// unitId : "888323696375238015"
/// deleted : false
/// pid : "888323696375106943"
/// rowindex : 0
/// equityValueDetailId : "895018846015091858"
/// ptypeRang : 0
/// ptypeName : "商品01"
/// usercode : "001"
/// propertyName : ""
/// retailPrice : 50.0
/// unitName : "个"
/// pcategory : "0"
/// batchEnabled : false
/// snEnabled : 0

class PtypeListBean {
  String? id;
  String? profileId;
  dynamic fullbarcode;
  String? fullname;
  String? valueTypeName;
  dynamic protectDays;
  dynamic stockQty;
  dynamic weight;
  int? weightUnit;
  dynamic pcategory;
  String ptypeId = "";
  String? ptypeTypeId; // 商品分类ID
  String? pid;
  String? ptypeName;
  dynamic usercode;
  dynamic shortname;
  dynamic standard;
  dynamic ptypeType;
  String skuId = "";
  dynamic unitName;
  String unitId = "";
  num? unitRate;
  dynamic createTime;
  String? updateTime;
  bool? combo;
  bool? batchEnabled;
  int? snEnabled;
  num? retailPrice;
  int? qty;
  dynamic picUrl;
  dynamic propId1;
  dynamic propId2;
  dynamic propId3;
  dynamic propId4;
  dynamic propId5;
  dynamic propId6;
  dynamic propName1;
  dynamic propName2;
  dynamic propName3;
  dynamic propName4;
  dynamic propName5;
  dynamic propName6;
  dynamic propvalueName1;
  dynamic propvalueName2;
  dynamic propvalueName3;
  dynamic propvalueName4;
  dynamic propvalueName5;
  dynamic propvalueName6;
  dynamic propvalueId1;
  dynamic propvalueId2;
  dynamic propvalueId3;
  dynamic propvalueId4;
  dynamic propvalueId5;
  dynamic propvalueId6;
  int? skuPrice;
  String? validFullName;
  int? validType;
  String? validValue;
  int? costMode;

  static PtypeListBean fromMap(Map<String, dynamic> map) {
    PtypeListBean giftPtypeListBean = PtypeListBean();
    giftPtypeListBean.costMode = map["costMode"];
    giftPtypeListBean.id = map['id'];
    giftPtypeListBean.profileId = map['profileId'];
    giftPtypeListBean.fullbarcode = map['fullbarcode'];
    giftPtypeListBean.fullname = map['fullname'];
    giftPtypeListBean.valueTypeName = map['valueTypeName'];
    giftPtypeListBean.protectDays = map['protectDays'];
    giftPtypeListBean.stockQty = map['stockQty'];
    giftPtypeListBean.weight = map['weight'];
    giftPtypeListBean.weightUnit = map['weightUnit'];
    giftPtypeListBean.pcategory = map['pcategory'];
    giftPtypeListBean.ptypeId = map['ptypeId'] ?? "";
    giftPtypeListBean.ptypeTypeId = map['ptypeTypeId'];
    giftPtypeListBean.pid = map['pid'];
    giftPtypeListBean.ptypeName = map['ptypeName'];
    giftPtypeListBean.usercode = map['usercode'];
    giftPtypeListBean.shortname = map['shortname'];
    giftPtypeListBean.standard = map['standard'];
    giftPtypeListBean.ptypeType = map['ptypeType'];
    giftPtypeListBean.skuId = map['skuId'] ?? "";
    giftPtypeListBean.unitName = map['unitName'];
    giftPtypeListBean.unitId = map['unitId'] ?? "";
    giftPtypeListBean.unitRate = map['unitRate'];
    giftPtypeListBean.createTime = map['createTime'];
    giftPtypeListBean.updateTime = map['updateTime'];
    giftPtypeListBean.combo = map['combo'];
    giftPtypeListBean.batchEnabled = map['batchEnabled'];
    giftPtypeListBean.snEnabled = map['snEnabled'];
    giftPtypeListBean.retailPrice = map['retailPrice'];
    giftPtypeListBean.qty = map['qty'];
    giftPtypeListBean.picUrl = map['picUrl'];
    giftPtypeListBean.propId1 = map['propId1'];
    giftPtypeListBean.propId2 = map['propId2'];
    giftPtypeListBean.propId3 = map['propId3'];
    giftPtypeListBean.propId4 = map['propId4'];
    giftPtypeListBean.propId5 = map['propId5'];
    giftPtypeListBean.propId6 = map['propId6'];
    giftPtypeListBean.propName1 = map['propName1'];
    giftPtypeListBean.propName2 = map['propName2'];
    giftPtypeListBean.propName3 = map['propName3'];
    giftPtypeListBean.propName4 = map['propName4'];
    giftPtypeListBean.propName5 = map['propName5'];
    giftPtypeListBean.propName6 = map['propName6'];
    giftPtypeListBean.propvalueName1 = map['propvalueName1'];
    giftPtypeListBean.propvalueName2 = map['propvalueName2'];
    giftPtypeListBean.propvalueName3 = map['propvalueName3'];
    giftPtypeListBean.propvalueName4 = map['propvalueName4'];
    giftPtypeListBean.propvalueName5 = map['propvalueName5'];
    giftPtypeListBean.propvalueName6 = map['propvalueName6'];
    giftPtypeListBean.propvalueId1 = map['propvalueId1'];
    giftPtypeListBean.propvalueId2 = map['propvalueId2'];
    giftPtypeListBean.propvalueId3 = map['propvalueId3'];
    giftPtypeListBean.propvalueId4 = map['propvalueId4'];
    giftPtypeListBean.propvalueId5 = map['propvalueId5'];
    giftPtypeListBean.propvalueId6 = map['propvalueId6'];
    giftPtypeListBean.skuPrice = map['skuPrice'];
    giftPtypeListBean.validFullName = map['validFullName'];
    giftPtypeListBean.validType = map['validType'];
    giftPtypeListBean.validValue = map['validValue'];
    return giftPtypeListBean;
  }

  Map toJson() => {
    "id": id,
    "costMode": costMode,
    "profileId": profileId,
    "fullbarcode": fullbarcode,
    "fullname": fullname,
    "valueTypeName": valueTypeName,
    "protectDays": protectDays,
    "stockQty": stockQty,
    "weight": weight,
    "weightUnit": weightUnit,
    "pcategory": pcategory,
    "ptypeId": ptypeId,
    "ptypeTypeId": ptypeTypeId,
    "pid": pid,
    "ptypeName": ptypeName,
    "usercode": usercode,
    "shortname": shortname,
    "standard": standard,
    "ptypeType": ptypeType,
    "skuId": skuId,
    "unitName": unitName,
    "unitId": unitId,
    "unitRate": unitRate,
    "createTime": createTime,
    "updateTime": updateTime,
    "combo": combo,
    "batchEnabled": batchEnabled,
    "snEnabled": snEnabled,
    "retailPrice": retailPrice,
    "qty": qty,
    "picUrl": picUrl,
    "propId1": propId1,
    "propId2": propId2,
    "propId3": propId3,
    "propId4": propId4,
    "propId5": propId5,
    "propId6": propId6,
    "propName1": propName1,
    "propName2": propName2,
    "propName3": propName3,
    "propName4": propName4,
    "propName5": propName5,
    "propName6": propName6,
    "propvalueName1": propvalueName1,
    "propvalueName2": propvalueName2,
    "propvalueName3": propvalueName3,
    "propvalueName4": propvalueName4,
    "propvalueName5": propvalueName5,
    "propvalueName6": propvalueName6,
    "propvalueId1": propvalueId1,
    "propvalueId2": propvalueId2,
    "propvalueId3": propvalueId3,
    "propvalueId4": propvalueId4,
    "propvalueId5": propvalueId5,
    "propvalueId6": propvalueId6,
    "skuPrice": skuPrice,
    "validFullName": validFullName,
    "validType": validType,
    "validValue": validValue,
  };

  GoodsDetailDto changeToGoodsDetailDto() {
    GoodsDetailDto goodsDetailDto = GoodsDetailDto();
    goodsDetailDto.weight = weight;
    goodsDetailDto.weightUnit = weightUnit;
    goodsDetailDto.costMode = costMode ?? 0;
    goodsDetailDto.pid = pid ?? '';
    goodsDetailDto.skuPrice = skuPrice;
    goodsDetailDto.unitId = unitId;
    goodsDetailDto.unitRate = unitRate;
    goodsDetailDto.unitName = unitName ?? "";
    goodsDetailDto.ptypetype = ptypeType;
    goodsDetailDto.standard = standard;
    goodsDetailDto.stockQty = stockQty;
    goodsDetailDto.fullbarcode = fullbarcode ?? "";
    goodsDetailDto.ptypeId = ptypeId;
    goodsDetailDto.profileId = profileId;
    goodsDetailDto.pFullName = ptypeName ?? "";
    goodsDetailDto.pUserCode = usercode;
    goodsDetailDto.standard = standard;
    goodsDetailDto.ptypetype = ptypeType;
    goodsDetailDto.ptypeGroup = 1;
    goodsDetailDto.skuId = skuId;
    goodsDetailDto.currencyPrice = retailPrice ?? 0;
    goodsDetailDto.currencyDisedTaxedPrice = 0;
    goodsDetailDto.comboRow = false;
    goodsDetailDto.batchenabled = batchEnabled ?? false;
    goodsDetailDto.snenabled = snEnabled ?? 0;
    goodsDetailDto.unitQty = 0;
    goodsDetailDto.vchtype = BillTypeData[BillType.SaleBill];
    goodsDetailDto.discount = 0;
    goodsDetailDto.gift = true;
    goodsDetailDto.costPrice = 0;
    goodsDetailDto.picUrl =
        (picUrl != null && !picUrl.contains("http"))
            ? SpTool.getQiNiuThumbnail(picUrl, true)
            : picUrl;
    List<PtypePropDto> props = [];
    if (StringUtil.isNotEmpty(propvalueName1)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propIndex = 1;
      propDto.propvalueId = propvalueId1;
      propDto.propvalueName = propvalueName1;
      props.add(propDto);
    }
    if (StringUtil.isNotEmpty(propvalueName2)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propIndex = 2;
      propDto.propvalueId = propvalueId2;
      propDto.propvalueName = propvalueName2;
      props.add(propDto);
    }
    if (StringUtil.isNotEmpty(propvalueName3)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propIndex = 3;
      propDto.propvalueId = propvalueId3;
      propDto.propvalueName = propvalueName3;
      props.add(propDto);
    }
    if (StringUtil.isNotEmpty(propvalueName4)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propIndex = 4;
      propDto.propvalueId = propvalueId4;
      propDto.propvalueName = propvalueName4;
      props.add(propDto);
    }
    if (StringUtil.isNotEmpty(propvalueName5)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propIndex = 5;
      propDto.propvalueId = propvalueId5;
      propDto.propvalueName = propvalueName5;
      props.add(propDto);
    }
    if (StringUtil.isNotEmpty(propvalueName6)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propIndex = 6;
      propDto.propvalueId = propvalueId6;
      propDto.propvalueName = propvalueName6;
      props.add(propDto);
    }
    goodsDetailDto.prop = props;
    goodsDetailDto.valueTypeName = valueTypeName ?? "";
    return goodsDetailDto;
  }
}
