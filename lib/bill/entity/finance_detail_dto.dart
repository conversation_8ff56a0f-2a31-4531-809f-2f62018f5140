///财务类单据明细/多账户信息
class FinanceDetailDTO {
  /// 科目明细ID
  String? id;

  String? profileId;

  /// 单据明细ID 和上面的区别是 这个是构建完对象后随机生成的 怕影响上面业务重新新开字段
  String? saveDetailId;

  /// 单据ID
  String? vchcode;

  /// 单据明细ID
  String? detailId;

  /// 单据类型
  String? vchtype;

  /// 往来单位
  String? btypeId;

  /**
   * 结算单位
   */
//     String? balanceBtypeId;
  /// 科目Id
  String? atypeId;

  /// 贷方金额
  double? creditTotal;

  /// 借方金额
  double? debitTotal;

  /// 科目名称
  String? atypeFullName;

  /// 科目编号
  String? atypeUserCode;

  /// 科目金额
  double? total;

  // 总分摊金额
  double? currencySharedTotal;

  //待分摊金额
  double? currencySharedRemain;

  //已分摊金额
  double? currencyHasSharedTotal;

  /// 科目金额-外币
  double? currencyTotal;

  /// 贷方金额
  double? credit;

  /// 借方金额
  double? debit;

  /// 备注
  String? memo;

  /// 账户Id
  String? sourceAccountId;

  /// 结算单位类型枚举
  String? accountType;

  String? btypeFullName;

  String? btypeUserCode;

  //以下字段方便会计凭证标记辅助核算
  String? assistItem;

  String? typeid;

  String? partypeid;

  /// 科目明细类型
  int? accountDetailType;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'saveDetailId': saveDetailId,
      'vchcode': vchcode,
      'detailId': detailId,
      'vchtype': vchtype,
      'btypeId': btypeId,
      'atypeId': atypeId,
      'creditTotal': creditTotal,
      'debitTotal': debitTotal,
      'atypeFullName': atypeFullName,
      'atypeUserCode': atypeUserCode,
      'total': total,
      'currencySharedTotal': currencySharedTotal,
      'currencySharedRemain': currencySharedRemain,
      'currencyHasSharedTotal': currencyHasSharedTotal,
      'currencyTotal': currencyTotal,
      'credit': credit,
      'debit': debit,
      'memo': memo,
      'sourceAccountId': sourceAccountId,
      'accountType': accountType,
      'btypeFullName': btypeFullName,
      'btypeUserCode': btypeUserCode,
      'assistItem': assistItem,
      'typeid': typeid,
      'partypeid': partypeid,
      'accountDetailType': accountDetailType,
    };
  }

  FinanceDetailDTO.fromMap(Map<String, dynamic> map)
      : this(
          id: map['id'],
          profileId: map['profileId'],
          saveDetailId: map['saveDetailId'],
          vchcode: map['vchcode'],
          detailId: map['detailId'],
          vchtype: map['vchtype'],
          btypeId: map['btypeId'],
          atypeId: map['atypeId'],
          creditTotal: map['creditTotal'],
          debitTotal: map['debitTotal'],
          atypeFullName: map['atypeFullName'],
          atypeUserCode: map['atypeUserCode'],
          total: map['total'],
          currencySharedTotal: map['currencySharedTotal'],
          currencySharedRemain: map['currencySharedRemain'],
          currencyHasSharedTotal: map['currencyHasSharedTotal'],
          currencyTotal: map['currencyTotal'],
          credit: map['credit'],
          debit: map['debit'],
          memo: map['memo'],
          sourceAccountId: map['sourceAccountId'],
          accountType: map['accountType'],
          btypeFullName: map['btypeFullName'],
          btypeUserCode: map['btypeUserCode'],
          assistItem: map['assistItem'],
          typeid: map['typeid'],
          partypeid: map['partypeid'],
          accountDetailType: map['accountDetailType'],
        );

  FinanceDetailDTO({
    this.id,
    this.profileId,
    this.saveDetailId,
    this.vchcode,
    this.detailId,
    this.vchtype,
    this.btypeId,
    this.atypeId,
    this.creditTotal,
    this.debitTotal,
    this.atypeFullName,
    this.atypeUserCode,
    this.total,
    this.currencySharedTotal,
    this.currencySharedRemain,
    this.currencyHasSharedTotal,
    this.currencyTotal,
    this.credit,
    this.debit,
    this.memo,
    this.sourceAccountId,
    this.accountType,
    this.btypeFullName,
    this.btypeUserCode,
    this.assistItem,
    this.typeid,
    this.partypeid,
    this.accountDetailType,
  });
}
