///
///@ClassName: barcode_scale_scan
///@Description: 条码秤扫码结果
///@Author: tanglan
///@Date: 2024/4/22
class BarcodeScaleScan {
  String markPosition="";
  String weightPosition="";
  String totalPosition="";
  String pTypePosition="";

  static BarcodeScaleScan fromMap(Map<String?, dynamic>? map) {
    if (map == null) return BarcodeScaleScan();
    BarcodeScaleScan feeDto = BarcodeScaleScan();
    feeDto.markPosition = map['markPosition'];
    feeDto.weightPosition = map['weightPosition'];
    feeDto.totalPosition = map['totalPosition'];
    feeDto.pTypePosition = map['pTypePosition'];
    return feeDto;
  }

  Map<String?, dynamic> toJson() => {
        "markPosition": markPosition,
        "weightPosition": weightPosition,
        "totalPosition": totalPosition,
        "pTypePosition": pTypePosition,
      };
}
