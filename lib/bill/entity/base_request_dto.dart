class BaseRequestDto {
  /// 账套ID
  String? profileId;

  /// 操作员ID
  String? employeeId;

  int? pageSize;

  int? pageIndex;

  ///来源
  String source = "POS";

  String? printfrom;

  ///是否是职员权限范围控制
  bool? isEtypeLimited;

  ///是否是仓库权限范围控制
  bool? isKtypeLimited;

  ///是否是店铺权限范围控制
  bool? isOtypeLimited;

  bool? isPtypeLimited;

  /// 往来单位权限
  bool? isBtypeLimited;

  List<String>? Logs;

  List<QueryFilterItem>? gridFilter;

  List<Sort>? sorts;

  BaseRequestDto({
    this.profileId,
    this.employeeId,
    this.pageSize,
    this.pageIndex,
    this.source = "POS",
    this.printfrom,
    this.isEtypeLimited,
    this.isKtypeLimited,
    this.isOtypeLimited,
    this.isPtypeLimited,
    this.isBtypeLimited,
    this.Logs,
    this.gridFilter,
    this.sorts,
  });

  BaseRequestDto copyWith({
    String? profileId,
    String? employeeId,
    int? pageSize,
    int? pageIndex,
    String? source,
    String? printfrom,
    bool? isEtypeLimited,
    bool? isKtypeLimited,
    bool? isOtypeLimited,
    bool? isPtypeLimited,
    bool? isBtypeLimited,
    List<String>? Logs,
    List<QueryFilterItem>? gridFilter,
    List<Sort>? sorts,
  }) {
    return BaseRequestDto(
      profileId: profileId ?? this.profileId,
      employeeId: employeeId ?? this.employeeId,
      pageSize: pageSize ?? this.pageSize,
      pageIndex: pageIndex ?? this.pageIndex,
      source: source ?? this.source,
      printfrom: printfrom ?? this.printfrom,
      isEtypeLimited: isEtypeLimited ?? this.isEtypeLimited,
      isKtypeLimited: isKtypeLimited ?? this.isKtypeLimited,
      isOtypeLimited: isOtypeLimited ?? this.isOtypeLimited,
      isPtypeLimited: isPtypeLimited ?? this.isPtypeLimited,
      isBtypeLimited: isBtypeLimited ?? this.isBtypeLimited,
      Logs: Logs ?? this.Logs,
      gridFilter: gridFilter ?? this.gridFilter,
      sorts: sorts ?? this.sorts,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'profileId': profileId,
      'employeeId': employeeId,
      'pageSize': pageSize,
      'pageIndex': pageIndex,
      'source': source,
      'printfrom': printfrom,
      'isEtypeLimited': isEtypeLimited,
      'isKtypeLimited': isKtypeLimited,
      'isOtypeLimited': isOtypeLimited,
      'isPtypeLimited': isPtypeLimited,
      'isBtypeLimited': isBtypeLimited,
      'Logs': Logs,
      'gridFilter': gridFilter?.map((e) => e.toJson()).toList(),
      'sorts': sorts?.map((e) => e.toJson()).toList(),
    };
  }

  BaseRequestDto.fromMap(Map<String, dynamic> map)
      : this(
          profileId: map['profileId'],
          employeeId: map['employeeId'],
          pageSize: map['pageSize'],
          pageIndex: map['pageIndex'],
          source: map['source'] ?? "POS",
          printfrom: map['printfrom'],
          isEtypeLimited: map['isEtypeLimited'],
          isKtypeLimited: map['isKtypeLimited'],
          isOtypeLimited: map['isOtypeLimited'],
          isPtypeLimited: map['isPtypeLimited'],
          isBtypeLimited: map['isBtypeLimited'],
          Logs: (map['Logs'] as List?)?.cast(),
          gridFilter: (map['gridFilter'] as List?)
              ?.map((e) => QueryFilterItem.fromMap(e))
              .toList(),
          sorts: (map['sorts'] as List?)?.map((e) => Sort.fromMap(e)).toList(),
        );
}

class QueryFilterItem {
  String? dataField;
  String? type;
  dynamic value;
  dynamic value1;
  dynamic value2;

  QueryFilterItem({
    this.dataField,
    this.type,
    this.value,
    this.value1,
    this.value2,
  });

  Map<String, dynamic> toJson() {
    return {
      'dataField': dataField,
      'type': type,
      'value': value,
      'value1': value1,
      'value2': value2,
    };
  }

  QueryFilterItem.fromMap(Map<String, dynamic> map)
      : this(
          dataField: map['dataField'],
          type: map['type'],
          value: map['value'],
          value1: map['value1'],
          value2: map['value2'],
        );
}

class Sort {
  String? dataField;
  bool? ascending;
  bool? sysCase;

  Sort({
    this.dataField,
    this.ascending,
    this.sysCase,
  });

  Map<String, dynamic> toJson() {
    return {
      'dataField': dataField,
      'ascending': ascending,
      'sysCase': sysCase,
    };
  }

  Sort.fromMap(Map<String, dynamic> map)
      : this(
          dataField: map['dataField'],
          ascending: map['ascending'],
          sysCase: map['sysCase'],
        );
}
