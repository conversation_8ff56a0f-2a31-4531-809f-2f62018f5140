/**
 * @ClassName:      account_detail_dto.dart
 * @CreateDate:    2020/3/30 14:33
 * @Author:         tlan
 * @Description:财务类单据明细/多账户信息
 */

class AccountDetailDto {
  int? accountType;
  String? atypeFullName;
  String? atypeId;
  String? atypeUserCode;
  String? btypeId;
  String? credit;
  String? debit;
  String? detailId;
  String? id;
  String? memo;
  String? total="0";
  String? vchcode;
  String? vchtype;

  static AccountDetailDto fromMap(Map<String?, dynamic>? map) {
    if (map == null) return AccountDetailDto();
    AccountDetailDto accountDetailDto = AccountDetailDto();
    accountDetailDto.accountType = map['accountType'];
    accountDetailDto.atypeFullName = map['atypeFullName'];
    accountDetailDto.atypeId = map['atypeId'];
    accountDetailDto.atypeUserCode = map['atypeUserCode'];
    accountDetailDto.btypeId = map['btypeId'];
    accountDetailDto.credit = map['credit'];
    accountDetailDto.debit = map['debit'];
    accountDetailDto.detailId = map['detailId'];
    accountDetailDto.id = map['id'];
    accountDetailDto.memo = map['memo'];
    accountDetailDto.total = map['total']??"";
    accountDetailDto.vchcode = map['vchcode'];
    accountDetailDto.vchtype = map['vchtype'];
    return accountDetailDto;
  }

  Map toJson() {
    return {
      "accountType": accountType,
      "atypeFullName": atypeFullName,
      "atypeId": atypeId,
      "atypeUserCode": atypeUserCode,
      "btypeId": btypeId,
      "credit": credit,
      "debit": debit,
      "detailId": detailId,
      "id": id,
      "memo": memo,
      "total": total,
      "vchcode": vchcode,
      "vchtype": vchtype,
    };
  }
}
