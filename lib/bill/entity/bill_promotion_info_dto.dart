import 'package:halo_utils/utils/String_util.dart';

import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/entity/ptype/ptype_prop_dto.dart';
import '../../common/tool/sp_tool.dart';
import '../../enum/bill_type.dart';
import '../tool/promotion/promotion.dart';
import '../tool/promotion/rule_model.dart';
import 'bill_promotion_info_dto_rang_value.dart';
import 'ptype/ptype_and_sku_dto.dart';

class BillPromotionInfoDto {
  ///同类型促销之间的优先级
  ///优先级高的值更大
  int priority = 0;
  String id = "";
  int? mode;
  String? fullname;
  String? etypeId;
  String? etypeName;

  ///商品促销是否叠加订单促销：0不叠加，1叠加
  bool joinOrder = true;

  ///促销是否累计积分
  bool calIntegral = false;

  ///商品促销是否叠加执行会员权益：0不叠加，1叠加
  bool vipRights = false;

  ///0 商品特价 1订单满减 2商品半价 3满件赠 4积分兑换 5满额赠
  int promotionType = -1;
  String? promotionTypeName;

  ///1 全部 2仅会员 3会员等级
  int? rangType;

  ///适配顾客类型规则
  List<PromotionRangValue>? rangValueList;

  String startDate = "";
  String endDate = "";
  int? state;
  String? startTime;
  String? endTime;
  bool? deleted;
  bool? stoped;
  String? createTime;
  String? updateTime;
  List<String?>? filterIds;
  dynamic filterNames;
  String? profileId;

  ///促销策略
  List<StrategyBean> strategy = [];
  List<OtypeListBean>? filterTypesList;
  String? promotionRangeDate;
  double? preferentialTotal;
  List<PromotionPtype> ptypeList = [];

  ///赠品列表，包含商品和优惠券
  List<PromotionPtype> giftPtypeList = [];

  ///指定赠品列表
  List<PromotionPtype> specifiedGiftPtypeList = [];

  /// 将UTC时间字符串转换为本地时间字符串
  /// 例如：2025-07-22T16:00:00.000+00:00 -> 2025-07-23 00:00:00
  static String _parseUtcDateToLocal(String? utcDateString) {
    if (utcDateString == null || utcDateString.trim().isEmpty) {
      return "";
    }
    try {
      // 解析UTC时间并转换为本地时间
      DateTime? dateTime = DateTime.tryParse(utcDateString!)?.toLocal();
      if (dateTime == null) {
        return utcDateString;
      }
      // 格式化为本地时间字符串 yyyy-MM-dd HH:mm:ss
      return "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} "
          "${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}";
    } catch (e) {
      // 如果转换失败，返回原始字符串
      return utcDateString ?? "";
    }
  }

  static BillPromotionInfoDto fromMap(Map<String?, dynamic>? map) {
    if (map == null) return BillPromotionInfoDto();
    BillPromotionInfoDto promotion = BillPromotionInfoDto();
    promotion.id = map['id'];
    promotion.priority = map['priority'] ?? 0;
    promotion.vipRights = map["vipRights"];
    promotion.calIntegral = map["calIntegral"];
    promotion.joinOrder = map['joinOrder'];
    promotion.mode = map['mode'];
    promotion.fullname = map['fullname'];
    promotion.etypeId = map['etypeId'];
    promotion.etypeName = map['etypeName'];
    promotion.promotionType = map['promotionType'] ?? -1;
    promotion.promotionTypeName = map['promotionTypeName'];
    promotion.rangType = map['rangType'];
    promotion.startDate = _parseUtcDateToLocal(map['startDate']);
    promotion.endDate = _parseUtcDateToLocal(map['endDate']);
    promotion.state = map['state'];
    promotion.startTime = map['startTime'];
    promotion.endTime = map['endTime'];
    promotion.deleted = map['deleted'];
    promotion.stoped = map['stoped'];
    promotion.createTime = _parseUtcDateToLocal(map['createTime']);
    promotion.updateTime = _parseUtcDateToLocal(map['updateTime']);
    promotion.filterIds =
        (map['otypeIds'] as List?)?.map((o) => o.toString()).toList() ?? [];
    promotion.filterNames = map['filterNames'];
    promotion.profileId = map['profileId'];
    promotion.strategy = (map['strategy'] as List?)
            ?.map((o) => StrategyBean.fromMap(o))
            .toList() ??
        [];
    promotion.filterTypesList = (map['filterTypesList'] as List?)
            ?.map((o) => OtypeListBean.fromMap(o))
            .toList() ??
        [];
    promotion.promotionRangeDate = map['promotionRangeDate'];
    promotion.preferentialTotal = map['preferentialTotal'];
    promotion.ptypeList = (map['ptypeList'] as List?)
            ?.map((o) => PromotionPtype.fromMap(o))
            .toList() ??
        [];
    promotion.giftPtypeList = (map['giftPtypeList'] as List?)
            ?.map((o) => PromotionPtype.fromMap(o))
            .toList() ??
        [];
    promotion.specifiedGiftPtypeList = (map['specifiedGiftPtypeList'] as List?)
            ?.map((o) => PromotionPtype.fromMap(o))
            .toList() ??
        [];
    promotion.rangValueList = (map['rangValueList'] as List?)
            ?.map((o) => PromotionRangValue.fromMap(o))
            .toList() ??
        [];
    return promotion;
  }

  Map toJson() => {
        "id": id,
        "priority": priority,
        "vipRights": vipRights,
        "calIntegral": calIntegral,
        "mode": mode,
        "fullname": fullname,
        "etypeId": etypeId,
        "etypeName": etypeName,
        "promotionType": promotionType,
        "promotionTypeName": promotionTypeName,
        "rangType": rangType,
        "startDate": startDate,
        "endDate": endDate,
        "state": state,
        "startTime": startTime,
        "endTime": endTime,
        "deleted": deleted,
        "stoped": stoped,
        "createTime": createTime,
        "updateTime": updateTime,
        "filterIds": filterIds,
        "filterNames": filterNames,
        "profileId": profileId,
        "strategy": strategy,
        "filterTypesList": filterTypesList,
        "promotionRangeDate": promotionRangeDate,
        "preferentialTotal": preferentialTotal,
        "ptypeList": ptypeList,
        "giftPtypeList": giftPtypeList,
        "specifiedGiftPtypeList": specifiedGiftPtypeList,
        "joinOrder": joinOrder,
        "rangValueList": rangValueList,
      };
}

/// id : "710983010664581237"
/// profileId : "617794922333872129"
/// promotionId : "710982134491252853"
/// otypeId : "674762632334414480"
/// createTime : "2022-01-24"
/// updateTime : "2022-01-24"
/// otypeName : "孵化池"

class OtypeListBean {
  String? id;
  String? profileId;
  String? promotionId;
  String? filterId;
  String? createTime;
  String? updateTime;
  String? filterName;

  static OtypeListBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return OtypeListBean();
    OtypeListBean otypeListBean = OtypeListBean();
    otypeListBean.id = map['id'];
    otypeListBean.profileId = map['profileId'];
    otypeListBean.promotionId = map['promotionId'];
    otypeListBean.filterId = map['filterId'];
    otypeListBean.createTime = map['createTime'];
    otypeListBean.updateTime = map['updateTime'];
    otypeListBean.filterName = map['filterName'];
    return otypeListBean;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "promotionId": promotionId,
        "filterId": filterId,
        "createTime": createTime,
        "updateTime": updateTime,
        "filterName": filterName,
      };
}

/// id : "710982134491318389"
/// profileId : "617794922333872129"
/// promotionId : "710982134491252853"
/// strategyType : 0
/// rule : "{\"type\":1}"
/// createTime : "2022-01-24"
/// updateTime : "2022-01-24"
/// rowIndex : 0

class StrategyBean {
  String? id;
  String? profileId;
  String? promotionId;

  ///策略类型 0=促销商品 1=促销方式 2=促销条件
  int strategyType = 0;

  ///每个促销规则对应都不同，参见[RuleModel]
  String rule = "{}";
  String? createTime;
  String? updateTime;
  int? rowIndex;

  static StrategyBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return StrategyBean();
    StrategyBean strategyBean = StrategyBean();
    strategyBean.id = map['id'];
    strategyBean.profileId = map['profileId'];
    strategyBean.promotionId = map['promotionId'];
    strategyBean.strategyType = map['strategyType'];
    strategyBean.rule = map['rule'];
    strategyBean.createTime = map['createTime'];
    strategyBean.updateTime = map['updateTime'];
    strategyBean.rowIndex = map['rowIndex'];
    return strategyBean;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "promotionId": promotionId,
        "strategyType": strategyType,
        "rule": rule,
        "createTime": createTime,
        "updateTime": updateTime,
        "rowIndex": rowIndex,
      };
}

///参加促销的商品或促销赠品
class PromotionPtype {
  String? id;
  String? profileId;
  String? fullbarcode;

  String? fullname;
  String? valueTypeName;
  String? skuBarcode;
  int? protectDays;
  num? stockQty;
  num? weight;
  int? weightUnit;
  int? pcategory;
  String? promotionId;

  ///品类类型
  ///1 = 优惠券
  ///6 = 商品
  int? ptypeGroup;
  String? ptypeId;
  String? pid;
  String? ptypeName;
  String? usercode;
  String? shortname;
  String? standard;
  String? ptypeType;
  int? promotionPtypeType;
  String? skuId;
  String? propertyName;
  num? retailPrice;
  String? unitName;
  String? unitId;
  num? unitRate;

  ///折扣/促销价/赠品数量（json）兑换所需积分
  String? preferential;
  String? strategyId;
  String? createTime;
  String? updateTime;
  bool? combo;

  ///是否开启批次管理
  bool? batchEnabled;
  bool? propenabled;

  ///批次号
  String? batchNo;
  List<String>? batchNos;

  ///序列号商品
  int? snEnabled;
  int? rowindex;
  num? currentPrice;
  num? qty;

  ///指定赠品时1组的数量
  num? giftQty;
  String? picUrl;
  int? changeCount;
  String? propId1;
  String? propId2;
  String? propId3;
  String? propId4;
  String? propId5;
  String? propId6;
  String? propName1;
  String? propName2;
  String? propName3;
  String? propName4;
  String? propName5;
  String? propName6;
  String? propvalueName1;
  String? propvalueName2;
  String? propvalueName3;
  String? propvalueName4;
  String? propvalueName5;
  String? propvalueName6;
  String? propvalueId1;
  String? propvalueId2;
  String? propvalueId3;
  String? propvalueId4;
  String? propvalueId5;
  String? propvalueId6;

  ///是否开启sku定价
  int? skuPrice;
  num? price;
  String? ofullNames;
  String? validFullName;
  int? validType;
  String? validValue;
  int? costMode;

  List<PtypeListModel>? comboDetail;

  PromotionPtype();

  PromotionPtype.fromMap(Map<String?, dynamic> map) {
    costMode = map["costMode"];
    id = map['id'];
    profileId = map['profileId'];
    fullbarcode = map['fullbarcode'];
    skuBarcode = map['skuBarcode'];
    fullname = map['fullname'];
    valueTypeName = map['valueTypeName'];
    protectDays = map['protectDays'];
    stockQty = map['stockQty'];
    weight = map['weight'];
    propenabled = map['propenabled'];
    weightUnit = map['weightUnit'];
    pcategory = map['pcategory'];
    promotionId = map['promotionId'];
    ptypeGroup = map['ptypeGroup'];
    ptypeId = map['ptypeId'];
    pid = map['pid'];
    ptypeName = map['ptypeName'];
    usercode = map['usercode'];
    shortname = map['shortname'];
    standard = map['standard'];
    ptypeType = map['ptypeType'];
    promotionPtypeType = map['promotionPtypeType'];
    skuId = map['skuId'];
    propertyName = map['propertyName'];
    retailPrice = map['retailPrice'];
    unitName = map['unitName'];
    unitId = map['unitId'];
    unitRate = map['unitRate'];
    preferential = map['preferential'];
    strategyId = map['strategyId'];
    createTime = map['createTime'];
    updateTime = map['updateTime'];
    combo = map['combo'];
    batchEnabled = map['batchEnabled'];
    batchNo = map['batchNo'];
    batchNos = (map['batchNos'] as List?)
            ?.map((e) => e.toString())
            .where((element) => element.isNotEmpty)
            .toList() ??
        [];
    snEnabled = map['snEnabled'];
    rowindex = map['rowindex'];
    currentPrice = map['currentPrice'];
    qty = map['qty'];
    giftQty = map['giftQty'];
    picUrl = map['picUrl'];
    changeCount = map['changeCount'];
    propId1 = map['propId1'];
    propId2 = map['propId2'];
    propId3 = map['propId3'];
    propId4 = map['propId4'];
    propId5 = map['propId5'];
    propId5 = map['propId6'];
    propName1 = map['propName1'];
    propName2 = map['propName2'];
    propName3 = map['propName3'];
    propName4 = map['propName4'];
    propName5 = map['propName5'];
    propName6 = map['propName6'];
    propvalueName1 = map['propvalueName1'];
    propvalueName2 = map['propvalueName2'];
    propvalueName3 = map['propvalueName3'];
    propvalueName4 = map['propvalueName4'];
    propvalueName5 = map['propvalueName5'];
    propvalueName6 = map['propvalueName6'];
    propvalueId1 = map['propvalueId1'];
    propvalueId2 = map['propvalueId2'];
    propvalueId3 = map['propvalueId3'];
    propvalueId4 = map['propvalueId4'];
    propvalueId5 = map['propvalueId5'];
    propvalueId6 = map['propvalueId6'];
    skuPrice = map['skuPrice'];
    price = map['price'];
    ofullNames = map['ofullNames'];
    validFullName = map['validFullName'];
    validType = map['validType'];
    validValue = map['validValue'];
    comboDetail = (map['comboDetail'] as List?)
            ?.map((e) => PtypeListModel.fromMap(e))
            .toList() ??
        [];
  }

  Map toJson() => {
        "id": id,
        "costMode": costMode,
        "profileId": profileId,
        "fullbarcode": fullbarcode,
        "skuBarcode": skuBarcode,
        "fullname": fullname,
        "valueTypeName": valueTypeName,
        "protectDays": protectDays,
        "stockQty": stockQty,
        "weight": weight,
        "weightUnit": weightUnit,
        "propenabled": propenabled,
        "pcategory": pcategory,
        "promotionId": promotionId,
        "ptypeGroup": ptypeGroup,
        "ptypeId": ptypeId,
        "pid": pid,
        "ptypeName": ptypeName,
        "usercode": usercode,
        "shortname": shortname,
        "standard": standard,
        "ptypeType": ptypeType,
        "promotionPtypeType": promotionPtypeType,
        "skuId": skuId,
        "propertyName": propertyName,
        "retailPrice": retailPrice,
        "unitName": unitName,
        "unitId": unitId,
        "unitRate": unitRate,
        "preferential": preferential,
        "strategyId": strategyId,
        "createTime": createTime,
        "updateTime": updateTime,
        "combo": combo,
        "batchEnabled": batchEnabled,
        "batchNo": batchNo,
        "batchNos": batchNos,
        "snEnabled": snEnabled,
        "rowindex": rowindex,
        "currentPrice": currentPrice,
        "qty": qty,
        "giftQty": giftQty,
        "picUrl": picUrl,
        "changeCount": changeCount,
        "propId1": propId1,
        "propId2": propId2,
        "propId3": propId3,
        "propId4": propId4,
        "propId5": propId5,
        "propId6": propId6,
        "propName1": propName1,
        "propName2": propName2,
        "propName3": propName3,
        "propName4": propName4,
        "propName5": propName5,
        "propName6": propName6,
        "propvalueName1": propvalueName1,
        "propvalueName2": propvalueName2,
        "propvalueName3": propvalueName3,
        "propvalueName4": propvalueName4,
        "propvalueName5": propvalueName5,
        "propvalueName6": propvalueName6,
        "propvalueId1": propvalueId1,
        "propvalueId2": propvalueId2,
        "propvalueId3": propvalueId3,
        "propvalueId4": propvalueId4,
        "propvalueId5": propvalueId5,
        "propvalueId6": propvalueId6,
        "skuPrice": skuPrice,
        "price": price,
        "ofullNames": ofullNames,
        "validFullName": validFullName,
        "validType": validType,
        "validValue": validValue,
        "comboDetail": comboDetail?.map((e) => e.toJson()).toList(),
      };

  GoodsDetailDto changeToGoodsDetailDto(BillPromotionInfoDto promotion,
      [int? promotionType, int? promotionGiftScope]) {
    GoodsDetailDto goods = GoodsDetailDto();
    goods.weight = weight;
    goods.weightUnit = weightUnit;
    goods.costMode = costMode ?? 0;
    goods.pid = pid ?? "";
    goods.skuPrice = skuPrice;
    goods.unitId = unitId ?? "";
    goods.unitRate = unitRate;
    goods.unitName = unitName ?? "";
    goods.ptypetype = ptypeType ?? "";
    goods.standard = standard ?? "";
    goods.stockQty = stockQty ?? 0;
    goods.fullbarcode = fullbarcode ?? "";
    goods.ptypeId = ptypeId ?? "";
    goods.profileId = profileId;
    goods.pFullName = ptypeName ?? "";
    goods.pUserCode = usercode;
    goods.standard = standard ?? "";
    goods.ptypetype = ptypeType ?? "";
    goods.ptypeGroup = ptypeGroup ?? 1;
    goods.skuId = skuId ?? "";
    goods.currencyPrice = retailPrice ?? 0;
    // goods.currencyDisedTaxedPrice = num.parse(preferential ?? "0");
    goods.comboRow = combo ?? false;
    goods.pcategory = pcategory ?? 0;
    goods.batchenabled = batchEnabled ?? false;
    goods.batchNo = batchNo ?? "";
    goods.snenabled = snEnabled ?? 0;
    goods.propenabled = propenabled ?? false;
    goods.unitQty = 0;
    goods.vchtype = BillTypeData[BillType.SaleBill];
    goods.gift = true;
    goods.promotionGift = true;
    goods.costPrice = 0;
    goods.picUrl = (picUrl != null && !picUrl!.contains("http"))
        ? SpTool.getQiNiuThumbnail(picUrl!, true)
        : picUrl;
    List<PtypePropDto> props = [];
    if (StringUtil.isNotEmpty(propvalueName1)) {
      PtypePropDto propDto1 = PtypePropDto();
      propDto1.propIndex = 1;
      propDto1.propvalueId = propvalueId1;
      propDto1.propvalueName = propvalueName1;
      props.add(propDto1);
    }
    if (StringUtil.isNotEmpty(propvalueName2)) {
      PtypePropDto propDto2 = PtypePropDto();
      propDto2.propIndex = 2;
      propDto2.propvalueId = propvalueId2;
      propDto2.propvalueName = propvalueName2;
      props.add(propDto2);
    }
    if (StringUtil.isNotEmpty(propvalueName3)) {
      PtypePropDto propDto3 = PtypePropDto();
      propDto3.propIndex = 3;
      propDto3.propvalueId = propvalueId3;
      propDto3.propvalueName = propvalueName3;
      props.add(propDto3);
    }
    if (StringUtil.isNotEmpty(propvalueName4)) {
      PtypePropDto propDto4 = PtypePropDto();
      propDto4.propIndex = 4;
      propDto4.propvalueId = propvalueId4;
      propDto4.propvalueName = propvalueName4;
      props.add(propDto4);
    }
    if (StringUtil.isNotEmpty(propvalueName5)) {
      PtypePropDto propDto5 = PtypePropDto();
      propDto5.propIndex = 5;
      propDto5.propvalueId = propvalueId5;
      propDto5.propvalueName = propvalueName5;
      props.add(propDto5);
    }
    if (StringUtil.isNotEmpty(propvalueName6)) {
      PtypePropDto propDto6 = PtypePropDto();
      propDto6.propIndex = 6;
      propDto6.propvalueId = propvalueId6;
      propDto6.propvalueName = propvalueName6;
      props.add(propDto6);
    }
    goods.prop = props;
    goods.valueTypeName = valueTypeName ?? "";
    PromotionUtil.setGoodsPromotion(goods: goods, promotion: promotion);

    //套餐生成套餐行id
    if (goods.comboRow) {
      goods.comboId = goods.ptypeId;
      goods.comboRowId = DateTime.now().millisecondsSinceEpoch.toString();
    }
    if (promotionGiftScope != null) {
      goods.promotionGiftScope = promotionGiftScope;
    }
    return goods;
  }

  CouponGift changeToCouponGift(BillPromotionInfoDto promotion,
      [int? promotionGiftScope]) {
    promotionGiftScope ??= PromotionGiftScope.chooseGoods.value;
    return CouponGift(
      promotionId: promotion.id,
      promotionType: promotion.promotionType,
      pid: pid ?? "",
      unitQty: 0,
      couponName: ptypeName ?? "",
      promotionGiftScope: promotionGiftScope,
    );
  }
}
