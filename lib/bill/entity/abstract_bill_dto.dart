import '../../common/enum_util.dart';
import '../../enum/bill_post_state.dart';
import '../../enum/bill_type.dart';
import '../tool/decimal_display_helper.dart';
import 'base_request_dto.dart';
import 'bill_audit_info_dto.dart';
import 'bill_sale_bill_detail_dto.dart';
import 'bill_save_exception_dto.dart';
import 'payment_dto.dart';
import 'save_model.dart';
import 'upload_bill_enclosure_dto.dart';
import 'validater_entity.dart';

class AbstractBillDto extends BaseRequestDto {
  ///找零
  String pettyCash = "0";

  ///手工备注
  String tips = "";

  ///支付方式
  List<PaymentDto> payment = [];

  /// 预收款 或 储值
  num currencyAdvanceTotal = 0;

  ///会员卡id
  String? vipCardId;

  /// 单据编号
  String? number;

  ///费用分摊类型
  int? shareType;

  ///运费分摊类型
  int? freightShareType;

  /// 录单日期
  String? date;

  /// 单据类型
  late String vchtype;

  /// 单据ID
  String? vchcode;

  ///退货时原单的vchcode
  String? sourceVchcode;

  /// 往来单位ID
  String? btypeId;

  /// 店长id
  String? storeManagerId;

  /// 往来单位全名
  String? bfullname;

  /// 结算方式
  int? balanceType;

  /// 业务员ID
  String? etypeId;

  /// 业务员全名
  String? efullname;

  /// 部门ID
  String? dtypeId;

  /// 部门全名
  String dfullname = "";

  /// 店铺ID
  String? shopId;

  /// 买家信息Id
  String? buyerId;

  /// 卖家信息Id
  String? senderId;

  /// 店铺全名
  String? sfullname;

  /// 收付款科目Id
  String? atypeId;

  /// 收付款科目全名
  String? afullname;

  /// 销售机构Id
  String? otypeId;

  /// 销售机构Id
  String? ofullname;

  /// 收付款科目金额
  num? currencyAtypeTotal;

  /// 摘要
  String? summary;

  /// 备注
  String memo = "";

  /// 单据金额（不含赠金）
  /// 出库单/退货单，都为正数
  /// 换货单，有正有负
  String currencyBillTotal = "";

  /// 退入明细金额汇总
  num sumInDetailTotal = 0;

  /// 退入明细金额汇总(含赠金)
  num sumPosInDetailTotal = 0;

  /// 退入明细金额赠金汇总
  num sumInDetailGiveTotal = 0;

  /// 单据金额(含赠金)
  String posCurrencyBillTotal = "";

  /// 原单本金支付金额
  num originCurrencyAdvanceTotal = 0;

  /// 录单人ID
  String? createEtypeId;

  /// 录单人全名
  String? createEfullname;

  /// 录单人编号
  String? createUserCode;

  /// 单据检测索引
  int? checkIndex;

  /// 单据完成状态 这个字段很诡异，jxc返回的是int，sale返回的是string
  String? postState;

  /// 原单状态 这个字段很诡异，jxc返回的是int，sale返回的是string
  String? oldPostState;

  /// 货币币种
  String? currencyId;

  /// 货币汇率
  num? exchangeRate;

  /// 优惠金额，优惠分摊金额汇总
  num currencyOrderPreferentialAllotTotal = 0.0;

  /// 其他收入金额
  String? currencyOrderOtherincomeTotal;

  /// 费用金额
  num? currencyOrderFeeAllotTotal;

  /// 过账人
  String? postEtypeId;

  /// 提示信息确认
  bool? confirm;

  /// 订单生成
  bool? orderCreate;

  /// 生成的财务类单据主键
  String? accountBillVchcode;

  /// 验证器集合
  List<ValidaterEntity>? validaterEntites;

  /// 验证器排序
  int? validaterOrder;

  /// 单据类型（商品类，财务类）
  String? billType;

  //保存方式
  SaveModel saveModel = SaveModel.SAVE_NEW;

  ///不需要生成skuid，第三方接口使用
  bool needCreateSku = true;

  ///单据编号检查
  bool checkNumber = false;

  ///分组Id
  String checkGroupId = "0";

  ///"保存异常信息"
  List<BillSaveExceptionDto>? saveExceptions;

  ///"单据审核信息(含当前用户)"
  BillAuditInfoDTO billAuditInfo = BillAuditInfoDTO();

  ///"结算是否反向"
  bool? balanceReverse;

  ///收入/费用类型 调拨方式 可能值为：直接调拨(0)、调拨出库(1)、调拨入库(2) 详见：GoodsTransTypeEnum
  int customType = 1; //组装拆卸单 生产类型 1=组装,2=拆卸

  /// 业务类型
  String? businessType;

  ///物流公司
  String? freightBtypeId;

  ///物流公司名称
  String? freightBtypeName;
  String? createType;

  ///单据编号初始值
  String? displayNumber;

  //销售模式，门店默认为6  这个字段很诡异，jxc返回的是int，sale返回的是string
  String? orderSaleMode = OrderBillModel.STORE_SALE.name;

  // 整单折扣
  num? billDiscount = 1;

  // 单据附件列表
  List<UploadBillEnclosureDto>? uploadDetail;

  ///打印次数
  int? printCount;

  ///生成的财务类单据列表
  List<String>? accountBillVchcodeList;

  /// 当前操作流程是否是否是审核
  bool? auditOperation;

  /// 过账自动更新录单时间
  bool? autoBillDate;

  ///应付/收单位全名
  String? bfullname2;

  ///应付/收单位ID
  String? btypeId2;

  String? billDeliverType;

  ///billEntity
  dynamic billEntity;

  ///单据格式：2=明细格式,1=标准格式,0=总量格式
  int? billMode;

  /// 往来单位对应的税号
  String? btypeInvoiceTax;

  /// 往来单位编号
  String? busercode;

  /// 是否取消签收
  bool? cancelSignIn;

  /// 对账状态 对账状态（0:未对账 1:已对账 2:手工确认）
  int? checkStatus;

  /// 分佣方
  String? commissionBtypeId;

  /// 单据创建时间
  String? createTime;

  String? createTypeEnumName;

  ///费用金额
  num? currencyFeeTotal;

  ///物流代收金额-外币
  num? currencyFreightCollectTotal;

  /// 抹零优惠
  num? currencyOrderWipeZeroTotal;

  ///其他收入金额
  num? currencyOtherincomeTotal;

  ///优惠金额
  num? currencyPreferentialTotal;

  /// 自定义字段
  String? customHead01;
  String? customHead02;
  String? customHead03;
  String? customHead04;
  String? customHead05;
  num? customHead06;
  num? customHead07;
  num? customHead08;
  String? customHead09;
  String? customHead10;
  String? customHead11;

  /// 异常信息明细
  List<dynamic>? exceptionDetailList;

  /// 物流运费付款账户
  String? freightaTypeId;

  /// 物流运费付款账户Name
  String? freightAtypeName;

  /// 物流付款金额;
  String? freightaTypeTotal;

  /// 物流单号
  String? freightBillNo;

  /// 是否物流代收
  int? freightCollectType;

  /// 运费
  String? freightFee;

  /// 物流信息id
  String? freightInfoId;

  /// 物流单打印状态
  int? freightPrintState;

  /// 生成的物流分摊单单据主键
  String? freightShareAccountBillVchcode;

  /// 物流模板id
  String? freightTemplateId;

  String? freightVchcode;

  int? intVchtype;

  /// 仓库全名
  String? kfullname;

  /// 仓库ID
  String? ktypeId;

  String? kusercode;

  /// 提交方式  0:用户保存 1: 系统生单
  int? LogCreateType;

  /// 客户端日志描述(从安全库存预警中心处生成采购订单/从负库存预警中心处生成采购订单)
  String? logMsg;

  ///是否需要重算价格
  bool? needAmountFlag;

  /// 生成新单据编号
  bool? newNumber;

  /// 获取系统单号时间
  String? numberDate;

  /// 原单自定义类型
  /// 调拨方式
  /// 可能值为：直接调拨(0)、调拨出库(1)、调拨入库(2)
  /// 详见：GoodsTransTypeEnum
  int? oldCustomType;

  /// 只核算单据
  bool? onlyPost;

  /// 操作单据检测 返回枚举
  String? opCheckResultEnum;

  /// 其他收入金额
  num? orderOtherincomeTotal;

  /// 整单优惠
  num? orderPreferenceFee;

  /// 整单优惠
  num? orderPreferentialFee;

  /// 用json存储的其他字段，如大头笔，目的地编码，原寄地编码，三段码等
  String? otherInfo;

  /// 店铺编号
  String? ousercode;

  String? partialDateFields;

  /// 收付款到期日期
  String? paymentDate;

  bool? postBillFlag;

  /// 成本核算时间
  String? postTime;

  /// 要打印到面单上的数据，可能是加密后的
  String? printData;

  ///处理方式0=普通单据,1=零售生成单据
  int? processType;

  /// 收件地址id
  String? receiveAddressId;

  /// 收件地址类型(0=网店下载从buyer获取，1=人工编辑从base_deliverinfo获取)
  int? receiveAddressType;

  dynamic relationsEntity;

  /// 仓库类型：0自有仓1在途仓库2外接WMS仓3海关WMS仓4加盟店仓5委托仓6委外加工仓7受托仓
  int? scategory;

  /// 生成的分摊单据主键
  String? shareAccountBillVchcode;

  String? sourceId;

  ///来源单号
  String? sourceNumber;

  ///存货状态：0在库，1在途
  int? stockState;

  /// 存货属性：0正品，1次品
  int? stockType;

  /// 编号流水
  String? streamNumber;

  /// 提交发货
  bool? submitDelivery;

  /// 提交仓储[自有仓]
  bool? submitWarehousing;

  /// 代发供货商id
  String? supplierId;

  /// 代发供货商
  String? supplierName;

  ///任务id（采购过程组用）
  String? taskId;

  String? updateTime;

  /// 使用单据设置项
  bool? useBillSetting;

  ///任务关联地址明细-采购生产组用
  String? vtaskCustomerDetailId;

  num? wmsRealityQty;

  ///sale中提交单据接口独有（jxc无），在sale中默认为true
  ///在sale服务中，提交单据后是否自动过账
  ///主要是调拨单中，库存调拨出库后，还要收货方进行入库，不能直接出库后就过账
  bool? whetherPost;

  ///开单后马上打印小票用
  ///不涉及到接口序列号和反序列化，本地用
  ///当前开单后会员剩余储值
  num vipStore = 0;

  //发票打印sign
  String? invoiceSign = "";
  VipBillInfoBean? vipBillInfo;
  bool changeDate = true;

  ///赠金优惠金额
  num currencyGivePreferentialTotal = 0;

  /// 可以赠送积分的金额
  num? giveScoreMoney;

  ///收银机id
  String? cashierId;

// //积分余额（用于小票打印）
//   int? scoreTotal = 0;
//
//   //本次积分（用于小票打印）
//   int? saleScore = 0;

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = super.toJson();
    map.addAll({
      "pettyCash": pettyCash,
      'number': number,
      'date': date,
      'vchtype': vchtype,
      'vchcode': vchcode,
      'sourceVchcode': sourceVchcode,
      'btypeId': btypeId,
      'storeManagerId': storeManagerId,
      'bfullname': bfullname,
      'balanceType': balanceType,
      'etypeId': etypeId,
      'efullname': efullname,
      'dtypeId': dtypeId,
      'dfullname': dfullname,
      'shopId': shopId,
      'buyerId': buyerId,
      'senderId': senderId,
      'sfullname': sfullname,
      'atypeId': atypeId,
      'afullname': afullname,
      'otypeId': otypeId,
      'currencyAtypeTotal': currencyAtypeTotal,
      'summary': summary,
      'memo': memo,
      'currencyBillTotal': currencyBillTotal,
      "posCurrencyBillTotal": posCurrencyBillTotal,
      "sumInDetailGiveTotal": sumInDetailGiveTotal,
      "sumInDetailTotal": sumInDetailTotal,
      "sumPosInDetailTotal": sumPosInDetailTotal,
      'createEtypeId': createEtypeId,
      'createEfullname': createEfullname,
      'createUserCode': createUserCode,
      'checkIndex': checkIndex,
      'postState': postState,
      'oldPostState': oldPostState,
      'currencyId': currencyId,
      'exchangeRate': exchangeRate,
      'currencyOrderPreferentialAllotTotal': num.tryParse(
          DecimalDisplayHelper.getTotalFixed(
              currencyOrderPreferentialAllotTotal.toString())),
      'currencyOrderOtherincomeTotal': currencyOrderOtherincomeTotal,
      'currencyOrderFeeAllotTotal': currencyOrderFeeAllotTotal,
      'postEtypeId': postEtypeId,
      'confirm': confirm,
      'orderCreate': orderCreate,
      'accountBillVchcode': accountBillVchcode,
      'validaterOrder': validaterOrder,
      'billType': billType,
      'needCreateSku': needCreateSku,
      'checkNumber': checkNumber,
      'checkGroupId': checkGroupId,
      'saveExceptions': saveExceptions,
      'billAuditInfo': billAuditInfo,
      'balanceReverse': balanceReverse,
      'customType': customType,
      'businessType': businessType,
      'freightBtypeId': freightBtypeId,
      'freightBtypeName': freightBtypeName,
      'ofullname': ofullname,
      'createType': createType,
      'displayNumber': displayNumber,
      "shareType": shareType,
      "freightShareType": freightShareType,
      'billDiscount': billDiscount,
      'uploadDetail': uploadDetail,
      "currencyAdvanceTotal": currencyAdvanceTotal,
      "originCurrencyAdvanceTotal": originCurrencyAdvanceTotal,
      "vipCardId": vipCardId,
      "orderSaleMode": orderSaleMode,
      'saveModel': SaveModelData[saveModel],
      "printCount": printCount,
      'accountBillVchcodeList': accountBillVchcodeList,
      'auditOperation': auditOperation,
      'autoBillDate': autoBillDate,
      'bfullname2': bfullname2,
      'btypeId2': btypeId2,
      'billDeliverType': billDeliverType,
      'billEntity': billEntity,
      'billMode': billMode,
      'btypeInvoiceTax': btypeInvoiceTax,
      'busercode': busercode,
      'cancelSignIn': cancelSignIn,
      'checkStatus': checkStatus,
      'commissionBtypeId': commissionBtypeId,
      'createTime': createTime,
      'createTypeEnumName': createTypeEnumName,
      'currencyFeeTotal': currencyFeeTotal,
      'currencyFreightCollectTotal': currencyFreightCollectTotal,
      'currencyOrderWipeZeroTotal': currencyOrderWipeZeroTotal,
      'currencyOtherincomeTotal': currencyOtherincomeTotal,
      'currencyPreferentialTotal': currencyPreferentialTotal,
      'customHead01': customHead01,
      'customHead02': customHead02,
      'customHead03': customHead03,
      'customHead04': customHead04,
      'customHead05': customHead05,
      'customHead06': customHead06,
      'customHead07': customHead07,
      'customHead08': customHead08,
      'customHead09': customHead09,
      'customHead10': customHead10,
      'customHead11': customHead11,
      'exceptionDetailList': exceptionDetailList,
      'freightaTypeId': freightaTypeId,
      'freightAtypeName': freightAtypeName,
      'freightaTypeTotal': freightaTypeTotal,
      'freightBillNo': freightBillNo,
      'freightCollectType': freightCollectType,
      'freightFee': freightFee,
      'freightInfoId': freightInfoId,
      'freightPrintState': freightPrintState,
      'freightShareAccountBillVchcode': freightShareAccountBillVchcode,
      'freightTemplateId': freightTemplateId,
      'freightVchcode': freightVchcode,
      'intVchtype': intVchtype,
      'kfullname': kfullname,
      'ktypeId': ktypeId,
      'kusercode': kusercode,
      'LogCreateType': LogCreateType,
      'logMsg': logMsg,
      'needAmountFlag': needAmountFlag,
      'newNumber': newNumber,
      'numberDate': numberDate,
      'oldCustomType': oldCustomType,
      'onlyPost': onlyPost,
      'opCheckResultEnum': opCheckResultEnum,
      'orderOtherincomeTotal': orderOtherincomeTotal,
      'orderPreferenceFee': orderPreferenceFee,
      'orderPreferentialFee': orderPreferentialFee,
      'otherInfo': otherInfo,
      'ousercode': ousercode,
      'partialDateFields': partialDateFields,
      'paymentDate': paymentDate,
      'postBillFlag': postBillFlag,
      'postTime': postTime,
      'printData': printData,
      'processType': processType,
      'receiveAddressId': receiveAddressId,
      'receiveAddressType': receiveAddressType,
      'relationsEntity': relationsEntity,
      'scategory': scategory,
      'shareAccountBillVchcode': shareAccountBillVchcode,
      'sourceId': sourceId,
      'sourceNumber': sourceNumber,
      'stockState': stockState,
      'stockType': stockType,
      'streamNumber': streamNumber,
      'submitDelivery': submitDelivery,
      'submitWarehousing': submitWarehousing,
      'supplierId': supplierId,
      'supplierName': supplierName,
      'taskId': taskId,
      'updateTime': updateTime,
      'useBillSetting': useBillSetting,
      'vtaskCustomerDetailId': vtaskCustomerDetailId,
      'wmsRealityQty': wmsRealityQty,
      'whetherPost': whetherPost,
      'tips': tips,
      "payment": payment.map((e) => e.toJson()).toList(),
      "invoiceSign": invoiceSign,
      "changeDate": changeDate,
      "currencyGivePreferentialTotal": currencyGivePreferentialTotal,
      "vipBillInfo": vipBillInfo?.toJson(),
      "giveScoreMoney": giveScoreMoney,
      "cashierId": cashierId,

    });
    return map;
  }

  AbstractBillDto.fromMap(Map<String, dynamic> map) : super.fromMap(map) {
    giveScoreMoney = map["giveScoreMoney"];
    cashierId = map["cashierId"];
    pettyCash = map["pettyCash"] ?? "0";
    payment = (map['payment'] as List?)?.map((o) {
          return o is PaymentDto ? o : PaymentDto.fromMap(o);
        }).toList() ??
        [];
    vipCardId = map["vipCardId"];
    currencyAdvanceTotal = map['currencyAdvanceTotal'] ?? 0;
    originCurrencyAdvanceTotal = map['originCurrencyAdvanceTotal'] ?? 0;
    number = map['number'];
    date = map['date'];
    vchtype = map['vchtype'] is int
        ? BillTypeCoreToString[map['vchtype']]
        : map['vchtype'] ?? "";
    vchcode = map['vchcode'];
    sourceVchcode = map['sourceVchcode'];
    btypeId = map['btypeId'];
    storeManagerId = map['storeManagerId'];
    bfullname = map['bfullname'];
    balanceType = map['balanceType'];
    etypeId = map['etypeId'];
    efullname = map['efullname'];
    dtypeId = map['dtypeId'];
    dfullname = map['dfullname'] ?? "";
    shopId = map['shopId'];
    buyerId = map['buyerId'];
    senderId = map['senderId'];
    sfullname = map['sfullname'];
    atypeId = map['atypeId'];
    afullname = map['afullname'];
    otypeId = map['otypeId'];
    currencyAtypeTotal = map['currencyAtypeTotal'];
    summary = map['summary'];
    memo = map['memo'] ?? "";
    currencyBillTotal = map['currencyBillTotal'].toString();
    posCurrencyBillTotal = map['posCurrencyBillTotal'].toString();
    sumInDetailGiveTotal = map['sumInDetailGiveTotal'] ?? 0;
    sumInDetailTotal = map['sumInDetailTotal'] ?? 0;
    sumPosInDetailTotal = map["sumPosInDetailTotal"] ?? 0;
    createEtypeId = map['createEtypeId'] ?? "0";
    createEfullname = map['createEfullname'];
    createUserCode = map['createUserCode'];
    checkIndex = map['checkIndex'];
    postState = map['postState'] ?? "";
    changeDate = map['changeDate'] ?? true;
    currencyGivePreferentialTotal = map["currencyGivePreferentialTotal"] ?? 0;
    oldPostState =
        (map['oldPostState'] ?? BillPostStateString[BillPostState.UNCONFIRMED]);
    currencyId = map['currencyId'];
    exchangeRate = map['exchangeRate'];
    currencyOrderPreferentialAllotTotal =
        map['currencyOrderPreferentialAllotTotal'] ?? 0.0;
    if (map['currencyOrderOtherincomeTotal'] != null) {
      currencyOrderOtherincomeTotal =
          map['currencyOrderOtherincomeTotal'].toString();
    } else {
      currencyOrderOtherincomeTotal = null;
    }
    currencyOrderFeeAllotTotal = map['currencyOrderFeeAllotTotal'];
    postEtypeId = map['postEtypeId'];
    confirm = map['confirm'];
    orderCreate = map['orderCreate'];
    accountBillVchcode = map['accountBillVchcode'];
    validaterOrder = map['validaterOrder'];
    billType = map['billType'];

    needCreateSku = map['needCreateSku'] ?? true;
    checkNumber = map['checkNumber'] ?? false;
    checkGroupId = map['checkGroupId'] ?? "0";
    saveExceptions = (map['saveExceptions'] as List? ?? [])
        .map((o) => BillSaveExceptionDto.fromMap(o))
        .toList();
    billAuditInfo = map['billAuditInfo'] is BillAuditInfoDTO
        ? map['billAuditInfo']
        : BillAuditInfoDTO.fromMap(map['billAuditInfo']);
    balanceReverse = map['balanceReverse'];
    customType = map['customType'] ?? 1;
    businessType = map['businessType'] is int
        ? BillBusinessTypeIntToString[map['businessType']]
        : map['businessType'];
    freightBtypeId = map['freightBtypeId'];
    freightBtypeName = map['freightBtypeName'];
    ofullname = map['ofullname'];
    createType = map["createType"];
    displayNumber = map['displayNumber'];
    billDiscount = map['billDiscount'] ?? 1;
    shareType = map['shareType'];
    freightShareType = map['freightShareType'];
    orderSaleMode = map['orderSaleMode'] ?? OrderBillModel.STORE_SALE.name;
    printCount = map["printCount"];
    uploadDetail = UploadBillEnclosureDto.enclosures(map['uploadDetail'] ?? []);
    saveModel = EnumUtil.valueToEnum(
        SaveModelData, map['saveModel'] ?? "", SaveModel.SAVE_NEW) as SaveModel;
    accountBillVchcodeList = (map['accountBillVchcodeList'] as List?)?.cast();
    auditOperation = map['auditOperation'];
    autoBillDate = map['autoBillDate'];
    bfullname2 = map['bfullname2'];
    btypeId2 = map['btypeId2'];
    billDeliverType = map['billDeliverType'];
    billEntity = map['billEntity'];
    billMode = map['billMode'];
    btypeInvoiceTax = map['btypeInvoiceTax'];
    busercode = map['busercode'];
    cancelSignIn = map['cancelSignIn'];
    checkStatus = map['checkStatus'];
    commissionBtypeId = map['commissionBtypeId'];
    createTime = map['createTime'];
    createTypeEnumName = map['createTypeEnumName'];
    currencyFeeTotal = map['currencyFeeTotal'];
    currencyFreightCollectTotal = map['currencyFreightCollectTotal'];
    currencyOrderWipeZeroTotal = map['currencyOrderWipeZeroTotal'];
    currencyOtherincomeTotal = map['currencyOtherincomeTotal'];
    currencyPreferentialTotal = map['currencyPreferentialTotal'];
    customHead01 = map['customHead01'];
    customHead02 = map['customHead02'];
    customHead03 = map['customHead03'];
    customHead04 = map['customHead04'];
    customHead05 = map['customHead05'];
    customHead06 = map['customHead06'];
    customHead07 = map['customHead07'];
    customHead08 = map['customHead08'];
    customHead09 = map['customHead09'];
    customHead10 = map['customHead10'];
    customHead11 = map['customHead11'];
    exceptionDetailList = map['exceptionDetailList'];
    freightaTypeId = map['freightaTypeId'];
    freightAtypeName = map['freightAtypeName'];
    freightaTypeTotal = map['freightaTypeTotal'];
    freightBillNo = map['freightBillNo'];
    freightCollectType = map['freightCollectType'];
    freightFee = map['freightFee'];
    freightInfoId = map['freightInfoId'];
    freightPrintState = map['freightPrintState'];
    freightShareAccountBillVchcode = map['freightShareAccountBillVchcode'];
    freightTemplateId = map['freightTemplateId'];
    freightVchcode = map['freightVchcode'];
    intVchtype = map['intVchtype'];
    kfullname = map['kfullname'];
    ktypeId = map['ktypeId'];
    kusercode = map['kusercode'];
    LogCreateType = map['LogCreateType'];
    logMsg = map['logMsg'];
    needAmountFlag = map['needAmountFlag'];
    newNumber = map['newNumber'];
    numberDate = map['numberDate'];
    oldCustomType = map['oldCustomType'];
    onlyPost = map['onlyPost'];
    opCheckResultEnum = map['opCheckResultEnum'];
    orderOtherincomeTotal = map['orderOtherincomeTotal'];
    orderPreferenceFee = map['orderPreferenceFee'];
    orderPreferentialFee = map['orderPreferentialFee'];
    otherInfo = map['otherInfo'];
    ousercode = map['ousercode'];
    partialDateFields = map['partialDateFields'];
    paymentDate = map['paymentDate'];
    postBillFlag = map['postBillFlag'];
    postTime = map['postTime'];
    printData = map['printData'];
    processType = map['processType'];
    receiveAddressId = map['receiveAddressId'];
    receiveAddressType = map['receiveAddressType'];
    relationsEntity = map['relationsEntity'];
    scategory = map['scategory'];
    shareAccountBillVchcode = map['shareAccountBillVchcode'];
    sourceId = map['sourceId'];
    sourceNumber = map['sourceNumber'];
    stockState = map['stockState'];
    stockType = map['stockType'];
    streamNumber = map['streamNumber'];
    submitDelivery = map['submitDelivery'];
    submitWarehousing = map['submitWarehousing'];
    supplierId = map['supplierId'];
    supplierName = map['supplierName'];
    taskId = map['taskId'];
    updateTime = map['updateTime'];
    useBillSetting = map['useBillSetting'];
    vtaskCustomerDetailId = map['vtaskCustomerDetailId'];
    wmsRealityQty = map['wmsRealityQty'];
    whetherPost = map['whetherPost'];
    tips = map['tips'] ?? "";
    invoiceSign = map["invoiceSign"] ?? "";
    vipBillInfo = map["vipBillInfo"] != null
        ? VipBillInfoBean.fromMap(map["vipBillInfo"])
        : null;
  }

  AbstractBillDto(
      {this.tips = "",
      this.pettyCash = "0",
      this.currencyAdvanceTotal = 0,
      this.originCurrencyAdvanceTotal = 0,
      this.vipCardId,
      this.number,
      this.shareType,
      this.freightShareType,
      this.date,
      this.vchtype = "",
      this.vchcode,
      this.sourceVchcode,
      this.btypeId,
      this.storeManagerId,
      this.bfullname,
      this.balanceType,
      this.etypeId,
      this.efullname,
      this.dtypeId,
      this.dfullname = "",
      this.shopId,
      this.buyerId,
      this.senderId,
      this.sfullname,
      this.atypeId,
      this.afullname,
      this.otypeId,
      this.ofullname,
      this.currencyAtypeTotal,
      this.summary,
      this.memo = "",
      this.currencyBillTotal = "",
      this.posCurrencyBillTotal = "",
      this.sumInDetailGiveTotal = 0,
      this.sumInDetailTotal = 0,
      this.sumPosInDetailTotal = 0,
      this.createEtypeId,
      this.createEfullname,
      this.createUserCode,
      this.checkIndex,
      this.postState,
      this.oldPostState,
      this.currencyId,
      this.exchangeRate,
      this.currencyOrderPreferentialAllotTotal = 0.0,
      this.currencyOrderOtherincomeTotal,
      this.currencyOrderFeeAllotTotal,
      this.postEtypeId,
      this.confirm,
      this.orderCreate,
      this.accountBillVchcode,
      this.validaterEntites,
      this.validaterOrder,
      this.billType,
      this.saveModel = SaveModel.SAVE_NEW,
      this.needCreateSku = true,
      this.checkNumber = false,
      this.checkGroupId = "0",
      this.saveExceptions,
      BillAuditInfoDTO? billAuditInfo,
      this.balanceReverse,
      this.customType = 1,
      this.businessType,
      this.freightBtypeId,
      this.freightBtypeName,
      this.createType,
      this.displayNumber,
      String? orderSaleMode,
      this.billDiscount,
      this.uploadDetail,
      this.printCount,
      this.accountBillVchcodeList,
      this.auditOperation,
      this.autoBillDate,
      this.bfullname2,
      this.btypeId2,
      this.billDeliverType,
      this.billEntity,
      this.billMode,
      this.btypeInvoiceTax,
      this.busercode,
      this.cancelSignIn,
      this.checkStatus,
      this.commissionBtypeId,
      this.createTime,
      this.createTypeEnumName,
      this.currencyFeeTotal,
      this.currencyFreightCollectTotal,
      this.currencyOrderWipeZeroTotal,
      this.currencyOtherincomeTotal,
      this.currencyPreferentialTotal,
      this.customHead01,
      this.customHead02,
      this.customHead03,
      this.customHead04,
      this.customHead05,
      this.customHead06,
      this.customHead07,
      this.customHead08,
      this.customHead09,
      this.customHead10,
      this.customHead11,
      this.exceptionDetailList,
      this.freightaTypeId,
      this.freightAtypeName,
      this.freightaTypeTotal,
      this.freightBillNo,
      this.freightCollectType,
      this.freightFee,
      this.freightInfoId,
      this.freightPrintState,
      this.freightShareAccountBillVchcode,
      this.freightTemplateId,
      this.freightVchcode,
      this.intVchtype,
      this.kfullname,
      this.ktypeId,
      this.kusercode,
      this.LogCreateType,
      this.logMsg,
      this.needAmountFlag,
      this.newNumber,
      this.numberDate,
      this.oldCustomType,
      this.onlyPost,
      this.opCheckResultEnum,
      this.orderOtherincomeTotal,
      this.orderPreferenceFee,
      this.orderPreferentialFee,
      this.otherInfo,
      this.ousercode,
      this.partialDateFields,
      this.paymentDate,
      this.postBillFlag,
      this.postTime,
      this.printData,
      this.processType,
      this.receiveAddressId,
      this.receiveAddressType,
      this.relationsEntity,
      this.scategory,
      this.shareAccountBillVchcode,
      this.sourceId,
      this.sourceNumber,
      this.stockState,
      this.stockType,
      this.streamNumber,
      this.submitDelivery,
      this.submitWarehousing,
      this.supplierId,
      this.supplierName,
      this.taskId,
      this.updateTime,
      this.useBillSetting,
      this.vtaskCustomerDetailId,
      this.wmsRealityQty,
      this.whetherPost,
      this.invoiceSign})
      : billAuditInfo = billAuditInfo ?? BillAuditInfoDTO(),
        orderSaleMode = orderSaleMode ?? OrderBillModel.STORE_SALE.name,
        super();
}
