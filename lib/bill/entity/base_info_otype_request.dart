class BaseInfoOtypeRequest {
  BaseInfoOtypeRequest({
      this.type, 
      this.undefinedList, 
      this.priceType, 
      this.logs,});

  BaseInfoOtypeRequest.fromJson(dynamic json) {
    type = json['type'];
    if (json['undefinedList'] != null) {
      undefinedList = [];
      json['undefinedList'].forEach((v) {
        undefinedList?.add(UndefinedList.fromJson(v));
      });
    }
    priceType = json['priceType'];
    logs = json['logs'] != null ? json['logs'].cast<String>() : [];
  }
  String? type;
  List<UndefinedList>? undefinedList;
  num? priceType;
  List<String>? logs;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['type'] = type;
    if (undefinedList != null) {
      map['undefinedList'] = undefinedList?.map((v) => v.toJson()).toList();
    }
    map['priceType'] = priceType;
    map['logs'] = logs;
    return map;
  }

}

class UndefinedList {
  UndefinedList({
      this.pFullName, 
      this.pUserCode, 
      this.unitId, 
      this.unitName, 
      this.ptypeId, 
      this.fullbarcode, 
      this.unit, 
      this.unitRate, 
      this.xcode, 
      this.skuId, 
      this.propFormat, 
      this.propValues, 
      this.skuPrice, 
      this.salePrice, 
      this.saleDiscount, 
      this.rowIndex, 
      this.xtypeId, 
      this.createType, 
      this.saleOtypeVipPrice,});

  UndefinedList.fromJson(dynamic json) {
    pFullName = json['pFullName'];
    pUserCode = json['pUserCode'];
    unitId = json['unitId'];
    unitName = json['unitName'];
    ptypeId = json['ptypeId'];
    fullbarcode = json['fullbarcode'];
    unit = json['unit'] != null ? Unit.fromJson(json['unit']) : null;
    unitRate = json['unitRate'];
    xcode = json['xcode'];
    skuId = json['skuId'];
    propFormat = json['propFormat'];
    propValues = json['propValues'];
    skuPrice = json['skuPrice'];
    salePrice = json['salePrice'];
    saleDiscount = json['saleDiscount'];
    rowIndex = json['__rowIndex'];
    xtypeId = json['xtypeId'];
    createType = json['createType'];
    saleOtypeVipPrice = json['saleOtypeVipPrice'];
  }
  String? pFullName;
  String? pUserCode;
  String? unitId;
  String? unitName;
  String? ptypeId;
  String? fullbarcode;
  Unit? unit;
  num? unitRate;
  String? xcode;
  String? skuId;
  String? propFormat;
  String? propValues;
  num? skuPrice;
  String? salePrice;
  String? saleDiscount;
  num? rowIndex;
  String? xtypeId;
  num? createType;
  String? saleOtypeVipPrice;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['pFullName'] = pFullName;
    map['pUserCode'] = pUserCode;
    map['unitId'] = unitId;
    map['unitName'] = unitName;
    map['ptypeId'] = ptypeId;
    map['fullbarcode'] = fullbarcode;
    if (unit != null) {
      map['unit'] = unit?.toJson();
    }
    map['unitRate'] = unitRate;
    map['xcode'] = xcode;
    map['skuId'] = skuId;
    map['propFormat'] = propFormat;
    map['propValues'] = propValues;
    map['skuPrice'] = skuPrice;
    map['salePrice'] = salePrice;
    map['saleDiscount'] = saleDiscount;
    map['__rowIndex'] = rowIndex;
    map['xtypeId'] = xtypeId;
    map['createType'] = createType;
    map['saleOtypeVipPrice'] = saleOtypeVipPrice;
    return map;
  }

}

class Unit {
  Unit({
      this.id, 
      this.profileId, 
      this.ptypeId, 
      this.unitCode, 
      this.unitName, 
      this.unitRate, 
      this.barcode, 
      this.createTime, 
      this.updateTime, 
      this.ptypeLength, 
      this.ptypeWidth, 
      this.ptypeHeight, 
      this.ptypeVolume, 
      this.ptypeLengthUnit, 
      this.ptypeWeight, 
      this.ptypeWeightUnit,});

  Unit.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    ptypeId = json['ptypeId'];
    unitCode = json['unitCode'];
    unitName = json['unitName'];
    unitRate = json['unitRate'];
    barcode = json['barcode'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    ptypeLength = json['ptypeLength'];
    ptypeWidth = json['ptypeWidth'];
    ptypeHeight = json['ptypeHeight'];
    ptypeVolume = json['ptypeVolume'];
    ptypeLengthUnit = json['ptypeLengthUnit'];
    ptypeWeight = json['ptypeWeight'];
    ptypeWeightUnit = json['ptypeWeightUnit'];
  }
  String? id;
  dynamic profileId;
  String? ptypeId;
  num? unitCode;
  String? unitName;
  num? unitRate;
  String? barcode;
  dynamic createTime;
  dynamic updateTime;
  dynamic ptypeLength;
  dynamic ptypeWidth;
  dynamic ptypeHeight;
  dynamic ptypeVolume;
  dynamic ptypeLengthUnit;
  dynamic ptypeWeight;
  dynamic ptypeWeightUnit;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['ptypeId'] = ptypeId;
    map['unitCode'] = unitCode;
    map['unitName'] = unitName;
    map['unitRate'] = unitRate;
    map['barcode'] = barcode;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['ptypeLength'] = ptypeLength;
    map['ptypeWidth'] = ptypeWidth;
    map['ptypeHeight'] = ptypeHeight;
    map['ptypeVolume'] = ptypeVolume;
    map['ptypeLengthUnit'] = ptypeLengthUnit;
    map['ptypeWeight'] = ptypeWeight;
    map['ptypeWeightUnit'] = ptypeWeightUnit;
    return map;
  }

}