/// @ClassName:      payment_dto.dart
/// @CreateDate:    2020/3/30 14:42
/// @Author:         tlan
/// @Description:   收付款信息
/// todo 这个类，进销存和sale中的已经存在不同，只能本地做了处理，主要是outNo,jxc叫payOutNo
class PaymentDto {
  String? afullnameCation;
  String? atypeFullName;
  String? atypeId;
  String? atypeTotalCation;

  ///支付金额（不包含赠金）
  String? currencyAtypeTotal = "0";

  ///支付金额（包含赠金）
  String posCurrencyAtypeTotal = "0";
  String? outNo;
  String? payOrderNo;
  String? paywayId;
  int? balanceType;

  ///支付方式名称"
  String? paywayName;
  String? paywayFullname;
  int? paywayType; //"支付方式类型：0=现金，1=银行，2=淘淘谷，3=预存款"
  String? payType; //聚合支付支付类型：1 淘淘谷
  String? product; //支付产品，如淘淘谷扫码支付
  String? content; //聚合支付支付配置内容
  String? accountDetailType; // 储值需要,payment不进核算 ADVANCE_ACCOUNTS

  static PaymentDto fromMap(Map<String, dynamic>? map) {
    PaymentDto paymentDto = PaymentDto();
    if (map == null) return paymentDto;
    paymentDto.afullnameCation = map['afullnameCation'];
    paymentDto.atypeFullName = map['atypeFullName'];
    paymentDto.atypeId = map['atypeId'];
    paymentDto.outNo = map['outNo'] ?? map["payOutNo"]; //兼容处理
    paymentDto.atypeTotalCation = map['atypeTotalCation'];
    paymentDto.currencyAtypeTotal = map['currencyAtypeTotal'] is double
        ? map['currencyAtypeTotal'].toString()
        : map['currencyAtypeTotal'] ?? "0";

    paymentDto.paywayId = map['paywayId'];
    paymentDto.paywayName = map['paywayName'];
    paymentDto.paywayFullname = map['paywayFullname'];
    paymentDto.paywayType = map['paywayType'];
    paymentDto.payType = map['payType'];
    paymentDto.product = map['product'];
    paymentDto.content = map['content'];
    paymentDto.accountDetailType = map['accountDetailType'];
    paymentDto.balanceType = map['balanceType'];
    paymentDto.payOrderNo = map['payOrderNo'];
    return paymentDto;
  }

  Map<String, dynamic> toJson() => {
        "accountDetailType": accountDetailType,
        "afullnameCation": afullnameCation,
        "atypeFullName": atypeFullName,
        "atypeId": atypeId,
        "atypeTotalCation": atypeTotalCation,
        "currencyAtypeTotal": currencyAtypeTotal,
        "posCurrencyAtypeTotal": posCurrencyAtypeTotal,
        "outNo": outNo,
        "payOutNo": outNo, //兼容处理
        "paywayId": paywayId,
        "paywayName": paywayName,
        "paywayFullname": paywayFullname,
        "paywayType": paywayType,
        "payType": payType,
        "product": product,
        "content": content,
        "balanceType": balanceType,
        "payOrderNo": payOrderNo,
      };
}
