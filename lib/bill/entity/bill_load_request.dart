import '../../bill/entity/base_request_dto.dart';

///单据加载请求
class BillLoadRequest extends BaseRequestDto {
  String? vchcode;
  String? vchtype;
  bool? copyEnable;
  String? sourceVchtype;
  String? targetVchtype;
  int? postState;
  int? customType;
  String? businessType;
  String? orderSaleMode;
  String? sourceTag;

  /// 用于权限展示
  bool? show = false;

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = super.toJson();
    json.addAll({
      'vchcode': vchcode,
      'vchtype': vchtype,
      'copyEnable': copyEnable,
      'sourceVchtype': sourceVchtype,
      'targetVchtype': targetVchtype,
      'postState': postState,
      'customType': customType,
      'businessType': businessType,
      'orderSaleMode': orderSaleMode,
      'sourceTag': sourceTag,
      'show': show,
    });
    return json;
  }

  BillLoadRequest.fromMap(Map<String, dynamic> map) : super.fromMap(map) {
    vchcode = map['vchcode'];
    vchtype = map['vchtype'];
    copyEnable = map['copyEnable'];
    sourceVchtype = map['sourceVchtype'];
    targetVchtype = map['targetVchtype'];
    postState = map['postState'];
    customType = map['customType'];
    businessType = map['businessType'];
    orderSaleMode = map['orderSaleMode'];
    sourceTag = map['sourceTag'];
    show = map['show'];
  }

  BillLoadRequest({
    this.vchcode,
    this.vchtype,
    this.copyEnable,
    this.sourceVchtype,
    this.targetVchtype,
    this.postState,
    this.customType,
    this.businessType,
    this.orderSaleMode,
    this.sourceTag,
    this.show,
  });
}
