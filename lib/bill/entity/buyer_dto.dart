/// @ClassName:      buyer_dto.dart
/// @CreateDate:    2020/3/30 14:38
/// @Author:         tlan
/// @Description:发货信息DTO
class BuyerDto {
  ///主键Id
  String? buyerId;

  ///收货人
  String? receiver;

  ///详细地址
  String? receiverAddress;

  ///市
  String? receiverCity;

  ///区
  String? receiverDistrict;

  ///收货人手机号码
  String? receiverMobile;

  ///收货电话
  String? receiverPhone;

  ///省
  String? receiverProvince;

  ///收货人邮编
  String? receiverZipCode;

  ///街道
  String? receiverStreet;

  static BuyerDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return BuyerDto();
    BuyerDto buyerDto = BuyerDto();
    buyerDto.buyerId = map['buyerId'] ?? "0";
    buyerDto.receiver = map['receiver'];
    buyerDto.receiverAddress = map['receiverAddress'];
    buyerDto.receiverCity = map['receiverCity'];
    buyerDto.receiverDistrict = map['receiverDistrict'];
    buyerDto.receiverMobile = map['receiverMobile'];
    buyerDto.receiverPhone = map['receiverPhone'];
    buyerDto.receiverProvince = map['receiverProvince'];
    buyerDto.receiverZipCode = map['receiverZipCode'];
    buyerDto.receiverStreet = map['receiverStreet'];
    return buyerDto;
  }

  Map toJson() => {
        "buyerId": buyerId,
        "receiver": receiver,
        "receiverAddress": receiverAddress,
        "receiverCity": receiverCity,
        "receiverDistrict": receiverDistrict,
        "receiverMobile": receiverMobile,
        "receiverPhone": receiverPhone,
        "receiverProvince": receiverProvince,
        "receiverZipCode": receiverZipCode,
        "receiverStreet": receiverStreet,
      };
}
