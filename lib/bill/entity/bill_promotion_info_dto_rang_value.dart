///促销中适用用户对象的规则
class PromotionRangValue {

  ///促销id
  final String? promotionId;

  ///对象类型 0=会员 1=会员等级
  final int rangeValueType;

  ///对象id（会员等级id）
  final String? rangId;

  ///会员类型 0:免费会员 1:付费会员
  final int vipType;

  ///对象名称（等级名称）
  final String? rangName;

  PromotionRangValue.fromMap(Map<String, dynamic> map)
      : promotionId = map['promotionId'],
        rangeValueType = map['rangeValueType'],
        rangId = map['rangId'],
        vipType = map['vipType'],
        rangName = map['rangName'];

  Map<String, dynamic> toJson() => {
        'promotionId': promotionId,
        'rangeValueType': rangeValueType,
        'rangId': rangId,
        'vipType': vipType,
        'rangName': rangName,
      };
}
