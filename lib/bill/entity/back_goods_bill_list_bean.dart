import '../../bill/entity/goods_bill.dto.dart';

import 'bill_sale_bill_detail_dto.dart';

/// 创建时间：11/28/22
/// 作者：xiaotiaochong
/// 描述：

class BackGoodsBillListBean {
  List<PreferentialBillsBean>? preferentialBills;
  List<PreferentialGoodsDetailsBean>? preferentialGoodsDetails;
  VipAssertsBillDtoBean? vipAssertsBillDto;
  GoodsBillDto? goodsbill;

  static BackGoodsBillListBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return BackGoodsBillListBean();
    BackGoodsBillListBean backGoodsBillListBean = BackGoodsBillListBean();
    backGoodsBillListBean.preferentialBills =
        (map['preferentialBills'] as List ?? [])
            .map((o) => PreferentialBillsBean.fromMap(o))
            .toList();
    backGoodsBillListBean.vipAssertsBillDto =
        VipAssertsBillDtoBean.fromMap(map['vipAssertsBillDto']);
    backGoodsBillListBean.preferentialGoodsDetails =
        (map['preferentialGoodsDetails'] as List ?? [])
            .map((o) => PreferentialGoodsDetailsBean.fromMap(o))
            .toList();
    backGoodsBillListBean.goodsbill = GoodsBillDto.fromMap(map['goodsbill']);
    return backGoodsBillListBean;
  }

  Map toJson() => {
        "preferentialBills": preferentialBills,
        "preferentialGoodsDetails": preferentialGoodsDetails,
        "goodsbill": goodsbill,
        "vipAssertsBillDto": vipAssertsBillDto,
      };
}
