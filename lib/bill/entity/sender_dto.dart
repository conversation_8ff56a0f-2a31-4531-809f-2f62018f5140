/// 寄件人信息
class SenderDTO {
  /// 主键Id
  String? senderId;

  /// 发货人
  String? sender;

  /// 发货电话
  String? senderPhone;

  /// 发货人手机号码
  String? senderMobile;

  /// 发货人邮编
  String? senderZipCode;

  /// 省
  String? senderProvince;

  /// 市
  String? senderCity;

  /// 区
  String? senderDistrict;

  /// 街道
  String? senderStreet;

  /// 详细地址
  String? senderAddress;

  Map<String, dynamic> toJson() {
    return {
      'senderId': senderId,
      'sender': sender,
      'senderPhone': senderPhone,
      'senderMobile': senderMobile,
      'senderZipCode': senderZipCode,
      'senderProvince': senderProvince,
      'senderCity': senderCity,
      'senderDistrict': senderDistrict,
      'senderStreet': senderStreet,
      'senderAddress': senderAddress,
    };
  }

  SenderDTO.fromMap(Map<String, dynamic> map)
      : this(
          senderId: map['senderId'],
          sender: map['sender'],
          senderPhone: map['senderPhone'],
          senderMobile: map['senderMobile'],
          senderZipCode: map['senderZipCode'],
          senderProvince: map['senderProvince'],
          senderCity: map['senderCity'],
          senderDistrict: map['senderDistrict'],
          senderStreet: map['senderStreet'],
          senderAddress: map['senderAddress'],
        );

  SenderDTO({
    this.senderId,
    this.sender,
    this.senderPhone,
    this.senderMobile,
    this.senderZipCode,
    this.senderProvince,
    this.senderCity,
    this.senderDistrict,
    this.senderStreet,
    this.senderAddress,
  });
}
