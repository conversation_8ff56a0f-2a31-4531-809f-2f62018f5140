/// Copyright (C), 2019-2020, wsgjp.com
/// FileName:atype_fee_dto
/// Author: lidingwen
/// Date: 8/1/21
/// Description:
class AtypeFeeDTO {
  String? atypeId;
  String? atypeTotal;
  String? afullname;

  static AtypeFeeDTO fromMap(Map<String?, dynamic>? map) {
    if (map == null) return AtypeFeeDTO();
    AtypeFeeDTO feeDto = AtypeFeeDTO();
    feeDto.atypeId = map['atypeId'];
    feeDto.atypeTotal = map['atypeTotal'];
    feeDto.afullname = map['afullname'];

    return feeDto;
  }

  Map<String?, dynamic> toJson() => {
        "atypeId": atypeId,
        "atypeTotal": atypeTotal,
        "afullname": afullname,
      };
}
