class UploadBillEnclosureDto {
  String? id;
  String? vchcode;
  String? size;
  String? fileName;
  String? uploadEmployeeId;
  String? uploadEmployeeName;
  String? profileId;
  String? memo;
  String? fileId;
  String? fileUrl;
  String? uploadTime;

  UploadBillEnclosureDto({
    this.id,
    this.vchcode,
    this.size,
    this.fileName,
    this.uploadEmployeeId,
    this.uploadEmployeeName,
    this.profileId,
    this.memo,
    this.fileId,
    this.fileUrl,
    this.uploadTime,
  });

  static UploadBillEnclosureDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return UploadBillEnclosureDto();
    UploadBillEnclosureDto dto = UploadBillEnclosureDto();
    dto.id = map['id'];
    dto.vchcode = map['vchcode'];
    dto.size = map['size'];
    dto.fileName = map['fileName'];
    dto.uploadEmployeeId = map['uploadEmployeeId'];
    dto.uploadEmployeeName = map['uploadEmployeeName'];
    dto.profileId = map['profileId'];
    dto.memo = map['memo'];
    dto.fileId = map['fileId'];
    dto.fileUrl = map['fileUrl'];
    dto.uploadTime = map['uploadTime'];

    return dto;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'vchcode': vchcode,
      'size': size,
      'fileName': fileName,
      'uploadEmployeeId': uploadEmployeeId,
      'uploadEmployeeName': uploadEmployeeName,
      'profileId': profileId,
      'memo': memo,
      'fileId': fileId,
      'fileUrl': fileUrl,
      'uploadTime': uploadTime,
    };
  }

  static List<UploadBillEnclosureDto> enclosures(List data) {
    List<UploadBillEnclosureDto> billEnclosures = [];
    data?.forEach((e) => billEnclosures.add(UploadBillEnclosureDto.fromMap(e)));
    return billEnclosures;
  }
}
