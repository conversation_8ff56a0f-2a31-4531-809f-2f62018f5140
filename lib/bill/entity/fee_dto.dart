import 'atype_fee_dto.dart';

/**
  * @ClassName:      fee_dto.dart
   * @CreateDate:    2020/3/30 14:39
  * @Author:         tlan
  * @Description:   费用信息对象
*/

class FeeDto {
  // /**
  //  * 构建bill_account赋值
  //  */
  String?  id;

  // /**
  //  * 费用往来单位
  //  */
  String?  feeBtypeId;
  // /**
  //  * 费用往来单位
  //  */
  String?  feeBtypeFullName;
  // /**
  //  * 费用科目
  //  */
  String?  feeAccountId;
  // /**
  //  * 费用科目
  //  */
  String?  feeAccountFullName;

  // /**
  //  * 费用金额
  //  */
  String?  currencyFeeTotal;

  // /**
  //  * 费用付款账户
  //  */
  String?  feeAtypeId;

  // /**
  //  * 费用付款账户
  //  */
  String?  feeAtypeFullName;

  // /**
  //  * 费用付款金额
  //  */
  String?  currencyFeeAtypeTotal;

  // /**
  //  * 费用单科目明细
  //  */
  List<AtypeFeeDTO>? feeDetail;
  // /**
  //  * 费用单付款科目明细
  //  */
  List<AtypeFeeDTO>? paymentDetail;
  String?  memo;

  static FeeDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return FeeDto();
    FeeDto feeDto = FeeDto();
    feeDto.currencyFeeAtypeTotal = map['currencyFeeAtypeTotal'];
    feeDto.currencyFeeTotal = map['currencyFeeTotal'];
    feeDto.feeAccountFullName = map['feeAccountFullName'];
    feeDto.feeAccountId = map['feeAccountId'];
    feeDto.feeAtypeFullName = map['feeAtypeFullName'];
    feeDto.feeAtypeId = map['feeAtypeId'];
    feeDto.feeBtypeFullName = map['feeBtypeFullName'];
    feeDto.feeBtypeId = map['feeBtypeId'];
    feeDto.feeDetail = []..addAll((map['feeDetail'] as List ?? []).map((o) {
        return o is AtypeFeeDTO ? o : AtypeFeeDTO.fromMap(o);
      }));
    feeDto.paymentDetail = []
      ..addAll((map['paymentDetail'] as List ?? []).map((o) {
        return o is AtypeFeeDTO ? o : AtypeFeeDTO.fromMap(o);
      }));

    feeDto.memo = map['memo'];

    return feeDto;
  }

  Map toJson() => {
        "currencyFeeAtypeTotal": currencyFeeAtypeTotal,
        "currencyFeeTotal": currencyFeeTotal,
        "feeAccountFullName": feeAccountFullName,
        "feeAccountId": feeAccountId,
        "feeAtypeFullName": feeAtypeFullName,
        "feeAtypeId": feeAtypeId,
        "feeBtypeFullName": feeBtypeFullName,
        "feeBtypeId": feeBtypeId,
        "feeDetail": feeDetail,
        "paymentDetail": paymentDetail,
        "memo": memo
      };
}
