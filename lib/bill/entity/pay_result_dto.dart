import 'dart:convert';

import 'package:halo_utils/utils/String_util.dart';

import '../../enum/payment_enum.dart';
import 'bill_save_reslut_dto.dart';
import 'pay_result_dto_entity.dart';

enum PaySaveBillEnum {
  COMMON,
  SCANPAY,
  SCANREFUND,
}

class PayResult {
  bool? successed;
  String? message;
  dynamic errcode;
  Map? data;
  PayResultDtoTradeResult? tradeResult;
  BillSaveResultDto? resultDTO;
  Map? payInfo;
  String? queryType;

  PayResult();
  //支付("订单状态（1 交易成功，2 待支付，4 已取 消，9 等待用户输入密码确认）")
  //退款("状态(1 成功，其它值为不成功)")
  String? status;
  String? outNo;
  String? orderNo;

  PayResult.fromJson(Map map) {
    status = map["status"];
    outNo = map["outNo"];
    orderNo = map["orderNo"];
    successed = map["successed"];
    message = map["message"];
    errcode = map["errcode"];
    data = map["data"];
    payInfo = map["payInfo"];
    if(StringUtil.isNotEmpty(map["tradeResult"])){
      tradeResult = PayResultDtoTradeResult.fromJson(json.decode(map["tradeResult"]));
    }
    queryType = map["queryType"];
    resultDTO = BillSaveResultDto.fromMap(map["resultDTO"]);

  }
}
