class DeleteBillRequest {
  String? vchcode;
  String? vchtype;
  int? billPostState;
  String? billDate;
  bool? accountBill;
  String? businessType;

  static DeleteBillRequest fromMap(Map<String, dynamic>? map) {
    if (map == null) return DeleteBillRequest();
    DeleteBillRequest deleteBillRequestBean = DeleteBillRequest();

    deleteBillRequestBean.vchcode = map['vchcode'];
    deleteBillRequestBean.vchtype = map['vchtype'];
    deleteBillRequestBean.businessType = map['businessType'];
    deleteBillRequestBean.billPostState = map['billPostState'];
    deleteBillRequestBean.billDate = map['billDate'];
    deleteBillRequestBean.accountBill = map['accountBill'];
    return deleteBillRequestBean;
  }

  Map toJson() => {
        "vchcode": vchcode,
        "vchtype": vchtype,
        "billPostState": billPostState,
        "billDate": billDate,
        "accountBill": accountBill,
    "businessType": businessType,
  };
}
