class BillCoreListRequestDto {
  ///收银机
  String? cashierId;
  String? goodsId;
  String? unitId;

  ///时间
  String? beginDate;
  String? endDate;

  String? postTimeBeginTime;
  String? postTimeEndTime;
  String? createEtypeId = "";

  String? billNumber;

  String? phone;

  String? payOutNo;

  ///单据编号
  String? btypeId;

  ///往来单位ID
  String? comment;

  ///附加说明
  String? customerReceiverCity;
  String? customerReceiverDistrict;
  String? customerReceiverProvince;
  String? employeeId;
  String? etypeId;

  ///业务员ID
  String? ktypeId;

  ///仓库ID
  String? ktypeId2;

  ///仓库2
  BigInt? maxTotal;

  ///结束金额
  BigInt? minTotal;

  ///开始金额
  int? overState;

  ///订单完成状态
  int? postState;

  ///支付状态 0：未支付  1：已支付
  int? payState;

  ///单据状态
  List? postStateList;

  ///
  List? auditStateList;

  ///订单完成状态
  List? overStateList;

  ///单据状态
  int? auditState;

  ///订单审核状态
  String? profileId;
  String? ptypeId;

  String? businessType;

  ///商品id
  String? startTime;

  ///时间范围
  String? endTime;
  String? summary;

  ///摘要
  List vchtypes = [];
  List businessTypeList = [];
  int? saleOrbuy;
  int? paymentType;
  int? invoiceType;

  ///默认为空 采购为1 销售为2
  bool? showBackBill; //原单退货（订单处理方式=订单处理且创建方式不等于销售订单生成，processtype=1的不显示）
  bool? showCompletedBill; //是否查已完成单子
  String? log;
  String? number;
  int? createType;
  String? skuId;
  int? buyWarnStatus;
  int? saleWarnStatus;
  String? otypeId;
  int? conformType;
  int? orderBillModel;
  bool? excludeBackCompletedBill;

  ///单据类型集合
  static BillCoreListRequestDto fromMap(Map<String?, dynamic>? map) {
    if (map == null) return BillCoreListRequestDto();
    BillCoreListRequestDto billCoreListRequestDtoBean =
        BillCoreListRequestDto();
    billCoreListRequestDtoBean.cashierId = map['cashierId'];
    billCoreListRequestDtoBean.goodsId = map['goodsId'];
    billCoreListRequestDtoBean.otypeId = map['otypeId'];
    billCoreListRequestDtoBean.buyWarnStatus = map['buyWarnStatus'];
    billCoreListRequestDtoBean.saleWarnStatus = map['saleWarnStatus'];
    billCoreListRequestDtoBean.auditState = map['auditState'];
    billCoreListRequestDtoBean.beginDate = map['beginDate'];
    billCoreListRequestDtoBean.billNumber = map['billNumber'];
    billCoreListRequestDtoBean.phone = map['phone'];
    billCoreListRequestDtoBean.payOutNo = map['payOutNo'];

    billCoreListRequestDtoBean.btypeId = map['btypeId'];
    billCoreListRequestDtoBean.comment = map['comment'];
    billCoreListRequestDtoBean.customerReceiverCity =
        map['customerReceiverCity'];
    billCoreListRequestDtoBean.customerReceiverDistrict =
        map['customerReceiverDistrict'];
    billCoreListRequestDtoBean.customerReceiverProvince =
        map['customerReceiverProvince'];
    billCoreListRequestDtoBean.employeeId = map['employeeId'];
    billCoreListRequestDtoBean.endDate = map['endDate'];
    billCoreListRequestDtoBean.endTime = map['endTime'];
    billCoreListRequestDtoBean.etypeId = map['etypeId'];
    billCoreListRequestDtoBean.ktypeId = map['ktypeId'];
    billCoreListRequestDtoBean.ktypeId2 = map['ktypeId2'];
    billCoreListRequestDtoBean.maxTotal = map['maxTotal'];
    billCoreListRequestDtoBean.minTotal = map['minTotal'];
    billCoreListRequestDtoBean.overState = map['overState'];
    billCoreListRequestDtoBean.postStateList = map['postStateList'];
    billCoreListRequestDtoBean.postState = map['postState'];
    billCoreListRequestDtoBean.payState = map['payState'];
    billCoreListRequestDtoBean.profileId = map['profileId'];
    billCoreListRequestDtoBean.ptypeId = map['ptypeId'];
    billCoreListRequestDtoBean.startTime = map['startTime'];
    billCoreListRequestDtoBean.summary = map['summary'];
    billCoreListRequestDtoBean.saleOrbuy = map['saleOrbuy'];
    billCoreListRequestDtoBean.paymentType = map['paymentType'];
    billCoreListRequestDtoBean.invoiceType = map['invoiceType'];
    billCoreListRequestDtoBean.unitId = map['unitId'];

    billCoreListRequestDtoBean.showBackBill = map['showBackBill'];
    billCoreListRequestDtoBean.showCompletedBill = map['showCompletedBill'];
    billCoreListRequestDtoBean.log = map['log'];
    billCoreListRequestDtoBean.number = map['number'];
    billCoreListRequestDtoBean.createType = map['createType'];
    billCoreListRequestDtoBean.skuId = map['skuId'];
    billCoreListRequestDtoBean.conformType = map['conformType'];
    billCoreListRequestDtoBean.orderBillModel = map['orderBillModel'];
    billCoreListRequestDtoBean.excludeBackCompletedBill =
        map['excludeBackCompletedBill'];

    billCoreListRequestDtoBean.vchtypes = (map['vchtypes'] as List ?? [])
        .map((o) => int?.tryParse(o.toString()))
        .toList();
    billCoreListRequestDtoBean.auditStateList =
        (map['auditStateList'] as List ?? [])
            .map((o) => int?.tryParse(o.toString()))
            .toList();
    billCoreListRequestDtoBean.overStateList =
        (map['overStateList'] as List ?? [])
            .map((o) => int?.tryParse(o.toString()))
            .toList();
    billCoreListRequestDtoBean.businessTypeList =
        (map['businessTypeList'] as List ?? [])
            .map((o) => int?.tryParse(o.toString()))
            .toList();
    return billCoreListRequestDtoBean;
  }

  Map toJson() => {
        "cashierId": cashierId,
        "goodsId": goodsId,
        "otypeId": otypeId,
        "buyWarnStatus": buyWarnStatus,
        "saleWarnStatus": saleWarnStatus,
        "auditState": auditState,
        "beginDate": beginDate,
        "billNumber": billNumber,
        "phone": phone,
        "payOutNo": payOutNo,
        "btypeId": btypeId,
        "comment": comment,
        "unitId": unitId,
        "customerReceiverCity": customerReceiverCity,
        "customerReceiverDistrict": customerReceiverDistrict,
        "customerReceiverProvince": customerReceiverProvince,
        "employeeId": employeeId,
        "endDate": endDate,
        "endTime": endTime,
        "etypeId": etypeId,
        "ktypeId": ktypeId,
        "ktypeId2": ktypeId2,
        "maxTotal": maxTotal,
        "minTotal": minTotal,
        "overState": overState,
        "postState": postState,
        "payState": payState,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "startTime": startTime,
        "summary": summary,
        "saleOrbuy": saleOrbuy,
        "paymentType": paymentType,
        "invoiceType": invoiceType,
        "auditStateList": auditStateList,
        "overStateList": overStateList,
        "postStateList": postStateList,
        "vchtypes": vchtypes,
        "businessTypeList": businessTypeList,
        "showBackBill": showBackBill,
        "showCompletedBill": showCompletedBill,
        "log": log,
        "number": number,
        "createType": createType,
        "skuId": skuId,
        "conformType": conformType,
        "orderBillModel": orderBillModel,
        "excludeBackCompletedBill": excludeBackCompletedBill
      };
}
