import '../../bill/entity/ptype/ptype_dto.dart';
import '../../bill/entity/ptype/ptype_prop_dto.dart';
import '../../bill/tool/props_helper.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/entity/ptype/unit_bean.dart';
import 'package:haloui/utils/math_util.dart';

/// id : "589408607703613283"
/// comboId : "589408601261162339"
/// profileId : "450279889958981633"
/// ptypeId : "589408601260711742"
/// skuId : "589408601260726163"
/// unitId : "589408603408195390"
/// qty : 1
/// price : 1E+1
/// total : 1E+1
/// joinAdjustTotal : true
/// necessarySku : false
/// stoped : false
/// gifted : false
/// createTime : "2020-05-12T08:27:45.317+0000"
/// updateTime : "2020-05-12T08:27:45.317+0000"
/// ptype : {"id":"589408601260711742","profileId":null,"typeid":null,"partypeid":null,"usercode":"","fullname":"5555fox test unit","shortname":"fox","namepy":null,"classed":null,"stoped":false,"deleted":null,"rowindex":null,"barcode":null,"standard":"","ptypeType":"","ptypeArea":"","memo":"","createType":null,"costMode":null,"pcategory":null,"taxNumber":"","taxRate":0,"costPrice":0,"supplyInfo":null,"brandId":"0","ktypeLimit":null,"snenabled":0,"propenabled":false,"batchenabled":false,"protectDays":0,"protectDaysUnit":0,"protectWarndays":0,"protectWarndaysUnit":0,"weight":0,"weightUnit":0,"lithiumBattery":null,"solid":null,"difficultyLevel":null,"weighted":null,"retailDefaultUnit":null,"saleDefaultUnit":null,"buyDefaultUnit":null,"stockDefaultUnit":null,"syncStock":true,"ptypeLength":0,"ptypeWidth":0,"ptypeHeight":0,"lengthUnit":0,"createTime":null,"updateTime":null}
/// brandtype : {"id":"0","profileId":null,"brandName":null,"stoped":null,"createTime":null,"updateTime":null}
/// ptypePic : {"id":null,"ptypeId":null,"profileId":null,"picUrl":"http://picture.qiniu.mygjp.com.cn/450279889958981633%2F589408601261238255.png!200x200","picName":null,"rowindex":null,"createTime":null,"updateTime":null}
/// sku : {"id":"589408601260726163","profileId":null,"ptypeId":"589408601260711742","fullbarcode":null,"stoped":null,"propId1":"0","propName1":"","propvalueId1":"0","propvalueName1":"","propId2":"0","propName2":"","propvalueId2":"0","propvalueName2":"","propId3":"0","propName3":"","propvalueId3":"0","propvalueName3":"","propId4":"0","propName4":"","propvalueId4":"0","propvalueName4":"","propId5":"0","propName5":"","propvalueId5":"0","propvalueName5":"","propId6":"0","propName6":"","propvalueId6":"0","propvalueName6":"","createTime":null,"updateTime":null}
/// unit : {"id":"589408603408195390","profileId":null,"ptypeId":"589408601260711742","unitCode":1,"unitName":"个","unitRate":1,"barcode":null,"retailPrice":null,"preprice1":null,"preprice2":null,"preprice3":null,"preprice4":null,"preprice5":null,"preprice6":null,"preprice7":null,"preprice8":null,"preprice9":null,"preprice10":null,"buyPrice":null,"minSalePrice":null,"createTime":null,"updateTime":null}
/// fullbarcode : null
/// xcode : null
/// stockQty : null
/// saleQty : null

class PtypeSuitDetailModel {
  String? id;
  String? comboId;
  String? profileId;
  String ptypeId = "";
  String skuId = "";
  String unitId = "";
  num? qty;
  num? price;
  num? total;
  bool? joinAdjustTotal;
  bool? necessarySku;
  bool? stoped;
  bool? gifted;
  String? createTime;
  String? updateTime;
  PtypeBean? ptype;
  BrandtypeBean? brandtype;
  PtypePicBean? ptypePic;
  SkuBean? sku;
  UnitBean? unit;
  dynamic fullbarcode;
  dynamic xcode;
  dynamic stockQty;
  double? scale; //分摊比例

  String discount = "1";

  GoodsDetailDto changeModel(int count) {
    GoodsDetailDto goods = GoodsDetailDto();
    goods.pcategory = ptype?.pcategory ?? 0;
    if (sku != null) {
      goods.prop = [];
      if (sku?.propId1 != "0") {
        PtypePropDto propDto = PtypePropDto();
        propDto.propIndex = 0;
        propDto.propvalueName = sku?.propvalueName1;
        propDto.propvalueId = sku?.propvalueId1;
        goods.prop?.add(propDto);
      }
      if (sku?.propId2 != "0") {
        PtypePropDto propDto = PtypePropDto();
        propDto.propIndex = 1;
        propDto.propvalueName = sku?.propvalueName2;
        propDto.propvalueId = sku?.propvalueId2;
        goods.prop?.add(propDto);
      }
      if (sku?.propId3 != "0") {
        PtypePropDto propDto = PtypePropDto();
        propDto.propIndex = 2;
        propDto.propvalueName = sku?.propvalueName3;
        propDto.propvalueId = sku?.propvalueId3;
        goods.prop?.add(propDto);
      }
      if (sku?.propId4 != "0") {
        PtypePropDto propDto = PtypePropDto();
        propDto.propIndex = 3;
        propDto.propvalueName = sku?.propvalueName4;
        propDto.propvalueId = sku?.propvalueId4;
        goods.prop?.add(propDto);
      }
      if (sku?.propId5 != "0") {
        PtypePropDto propDto = PtypePropDto();
        propDto.propIndex = 4;
        propDto.propvalueName = sku?.propvalueName5;
        propDto.propvalueId = sku?.propvalueId5;
        goods.prop?.add(propDto);
      }
      if (sku?.propId6 != "0") {
        PtypePropDto propDto = PtypePropDto();
        propDto.propIndex = 5;
        propDto.propvalueName = sku?.propvalueName6;
        propDto.propvalueId = sku?.propvalueId6;
        goods.prop?.add(propDto);
      }

      PropsHelper.setPropValueString(goods);
    }
    goods.pUserCode = ptype?.usercode;
    goods.standard = ptype?.standard ?? "";
    goods.ptypetype = ptype?.ptypeType ?? "";
    goods.protectDays = ptype?.protectDays;
    goods.costMode = ptype?.costMode;
    goods.comboRow = false;
    goods.picUrl = ptypePic?.picUrl ?? "";
    goods.snenabled = ptype?.snenabled ?? 0;
    goods.batchenabled = ptype?.batchenabled ?? false;
    goods.comboId = comboId ?? "";
    goods.comboDetailId = id;
    goods.profileId = profileId;
    goods.skuId = skuId;
    goods.unitId = unitId;
    goods.unitQty =
        MathUtil.multiplication(qty.toString(), count.toString()).toDouble();
    goods.costPrice = price ?? 0;
    goods.propenabled = ptype?.propenabled ?? false;
    goods.costTotal =
        MathUtil.multiplication(total.toString(), count.toString()).toDouble();
    goods.currencyPrice = price ?? 0;
    goods.currencyTotal =
        MathUtil.multiplication(total.toString(), count.toString()).toDouble();
    goods.currencyDisedTaxedPrice = price ?? 0;
    goods.currencyDisedTaxedTotal =
        MathUtil.multiplication(total.toString(), count.toString()).toDouble();
    goods.currencyDisedPrice = price ?? 0;
    goods.currencyDisedTotal =
        MathUtil.multiplication(total.toString(), count.toString()).toDouble();
    goods.discount = MathUtil.stringToDouble(discount)!;
    goods.taxRate = ptype?.taxRate ?? 0;
    goods.ptypeId = ptypeId;
    goods.costTotal = total;
    goods.gift = gifted ?? false;
    goods.pFullName = ptype?.fullname ?? "";
    goods.brandName = brandtype?.brandName;
    goods.unitName = unit?.unitName ?? "";
    goods.unitRate = unit?.unitRate ?? 0;
    goods.pUserCode = ptype?.usercode;
    goods.fullbarcode = fullbarcode ?? "";
    goods.fullbarcode = sku?.fullbarcode ?? "";
    goods.comboQtyRate = qty;
    goods.comboShareScale = scale ?? 0;
    goods.weight = ptype?.weight;
    goods.weightUnit = ptype?.weightUnit;
    goods.skuPrice = ptype?.skuPrice;
    // goods.costMode = ptype.costMode;
    return goods;
  }

  static PtypeSuitDetailModel fromMap(Map<String, dynamic>? map) {
    PtypeSuitDetailModel ptypeSuitDetailModelBean = PtypeSuitDetailModel();
    if (map == null) return ptypeSuitDetailModelBean;
    ptypeSuitDetailModelBean.id = map['id'];
    ptypeSuitDetailModelBean.comboId = map['comboId'];
    ptypeSuitDetailModelBean.profileId = map['profileId'];
    ptypeSuitDetailModelBean.ptypeId = map['ptypeId'];
    ptypeSuitDetailModelBean.skuId = map['skuId'];
    ptypeSuitDetailModelBean.unitId = map['unitId'];
    ptypeSuitDetailModelBean.qty = map['qty'];
    ptypeSuitDetailModelBean.price = map['price'];
    ptypeSuitDetailModelBean.total = map['total'];
    ptypeSuitDetailModelBean.joinAdjustTotal = map['joinAdjustTotal'];
    ptypeSuitDetailModelBean.necessarySku = map['necessarySku'];
    ptypeSuitDetailModelBean.stoped = map['stoped'];
    ptypeSuitDetailModelBean.gifted = map['gifted'];
    ptypeSuitDetailModelBean.createTime = map['createTime'];
    ptypeSuitDetailModelBean.updateTime = map['updateTime'];
    ptypeSuitDetailModelBean.ptype = PtypeBean.fromMap(map['ptype']);
    ptypeSuitDetailModelBean.brandtype =
        BrandtypeBean.fromMap(map['brandtype']);
    ptypeSuitDetailModelBean.ptypePic = PtypePicBean.fromMap(map['ptypePic']);
    ptypeSuitDetailModelBean.sku = SkuBean.fromMap(map['sku']);
    ptypeSuitDetailModelBean.unit = UnitBean.fromMap(map['unit']);
    ptypeSuitDetailModelBean.fullbarcode = map['fullbarcode'];
    ptypeSuitDetailModelBean.xcode = map['xcode'];
    ptypeSuitDetailModelBean.stockQty = map['stockQty'];
    ptypeSuitDetailModelBean.scale = map['scale'];
    return ptypeSuitDetailModelBean;
  }

  Map toJson() => {
        "id": id,
        "comboId": comboId,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "skuId": skuId,
        "unitId": unitId,
        "qty": qty,
        "price": price,
        "total": total,
        "joinAdjustTotal": joinAdjustTotal,
        "necessarySku": necessarySku,
        "stoped": stoped,
        "gifted": gifted,
        "createTime": createTime,
        "updateTime": updateTime,
        "ptype": ptype,
        "brandtype": brandtype,
        "ptypePic": ptypePic,
        "sku": sku,
        "unit": unit,
        "fullbarcode": fullbarcode,
        "xcode": xcode,
        "stockQty": stockQty,
        "scale": scale,
      };
}

/// id : null
/// ptypeId : null
/// profileId : null
/// picUrl : "http://picture.qiniu.mygjp.com.cn/450279889958981633%2F589408601261238255.png!200x200"
/// picName : null
/// rowindex : null
/// createTime : null
/// updateTime : null

class PtypePicBean {
  dynamic id;
  dynamic ptypeId;
  dynamic profileId;
  String? picUrl;
  dynamic picName;
  dynamic rowindex;
  dynamic createTime;
  dynamic updateTime;

  static PtypePicBean fromMap(Map<String, dynamic>? map) {
    PtypePicBean ptypePicBean = PtypePicBean();
    if (map == null) return ptypePicBean;
    ptypePicBean.id = map['id'];
    ptypePicBean.ptypeId = map['ptypeId'];
    ptypePicBean.profileId = map['profileId'];
    ptypePicBean.picUrl = map['picUrl'];
    ptypePicBean.picName = map['picName'];
    ptypePicBean.rowindex = map['rowindex'];
    ptypePicBean.createTime = map['createTime'];
    ptypePicBean.updateTime = map['updateTime'];
    return ptypePicBean;
  }

  Map toJson() => {
        "id": id,
        "ptypeId": ptypeId,
        "profileId": profileId,
        "picUrl": picUrl,
        "picName": picName,
        "rowindex": rowindex,
        "createTime": createTime,
        "updateTime": updateTime,
      };
}

/// id : "0"
/// profileId : null
/// brandName : null
/// stoped : null
/// createTime : null
/// updateTime : null

class BrandtypeBean {
  String? id;
  dynamic profileId;
  dynamic brandName;
  dynamic stoped;
  dynamic createTime;
  dynamic updateTime;

  static BrandtypeBean fromMap(Map<String, dynamic> map) {
    BrandtypeBean brandtypeBean = BrandtypeBean();
    brandtypeBean.id = map['id'];
    brandtypeBean.profileId = map['profileId'];
    brandtypeBean.brandName = map['brandName'];
    brandtypeBean.stoped = map['stoped'];
    brandtypeBean.createTime = map['createTime'];
    brandtypeBean.updateTime = map['updateTime'];
    return brandtypeBean;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "brandName": brandName,
        "stoped": stoped,
        "createTime": createTime,
        "updateTime": updateTime,
      };
}

/// id : "589408601260711742"
/// profileId : null
/// typeid : null
/// partypeid : null
/// usercode : ""
/// fullname : "5555fox test unit"
/// shortname : "fox"
/// namepy : null
/// classed : null
/// stoped : false
/// deleted : null
/// rowindex : null
/// barcode : null
/// standard : ""
/// ptypeType : ""
/// ptypeArea : ""
/// memo : ""
/// createType : null
/// costMode : null
/// pcategory : null
/// taxNumber : ""
/// taxRate : 0
/// costPrice : 0
/// supplyInfo : null
/// brandId : "0"
/// ktypeLimit : null
/// snenabled : 0
/// propenabled : false
/// batchenabled : false
/// protectDays : 0
/// protectDaysUnit : 0
/// protectWarndays : 0
/// protectWarndaysUnit : 0
/// weight : 0
/// weightUnit : 0
/// lithiumBattery : null
/// solid : null
/// difficultyLevel : null
/// weighted : null
/// retailDefaultUnit : null
/// saleDefaultUnit : null
/// buyDefaultUnit : null
/// stockDefaultUnit : null
/// syncStock : true
/// ptypeLength : 0
/// ptypeWidth : 0
/// ptypeHeight : 0
/// lengthUnit : 0
/// createTime : null
/// updateTime : null

class PtypeBean {
  String? id;
  dynamic profileId;
  dynamic typeid;
  dynamic partypeid;
  String? usercode;
  String? fullname;
  String? shortname;
  dynamic namepy;
  dynamic classed;
  bool? stoped;
  dynamic deleted;
  dynamic rowindex;
  dynamic barcode;
  String? standard;
  String? ptypeType;
  String? ptypeArea;
  String? memo;
  dynamic createType;
  dynamic costMode;

  ///商品类型 0为实物，1为虚拟
  num? pcategory;
  String? taxNumber;
  num? taxRate;
  num? costPrice;
  dynamic supplyInfo;
  String? brandId;
  dynamic ktypeLimit;
  int? snenabled;
  bool? propenabled;
  bool? batchenabled;
  num? protectDays;
  num? protectDaysUnit;
  num? protectWarndays;
  num? protectWarndaysUnit;
  dynamic weight;
  int? weightUnit;
  dynamic lithiumBattery;
  dynamic solid;
  dynamic difficultyLevel;
  dynamic weighted;
  dynamic retailDefaultUnit;
  dynamic saleDefaultUnit;
  dynamic buyDefaultUnit;
  dynamic stockDefaultUnit;
  bool? syncStock;
  dynamic ptypeLength;
  dynamic ptypeWidth;
  dynamic ptypeHeight;
  num? lengthUnit;
  dynamic createTime;
  dynamic updateTime;
  int? skuPrice;

  static PtypeBean fromMap(Map<String, dynamic> map) {
    PtypeBean ptypeBean = PtypeBean();
    ptypeBean.id = map['id'];
    ptypeBean.profileId = map['profileId'];
    ptypeBean.typeid = map['typeid'];
    ptypeBean.partypeid = map['partypeid'];
    ptypeBean.usercode = map['usercode'];
    ptypeBean.fullname = map['fullname'];
    ptypeBean.shortname = map['shortname'];
    ptypeBean.namepy = map['namepy'];
    ptypeBean.classed = map['classed'];
    ptypeBean.stoped = map['stoped'];
    ptypeBean.deleted = map['deleted'];
    ptypeBean.rowindex = map['rowindex'];
    ptypeBean.barcode = map['barcode'] ?? "";
    ptypeBean.standard = map['standard'];
    ptypeBean.ptypeType = map['ptypeType'];
    ptypeBean.ptypeArea = map['ptypeArea'];
    ptypeBean.memo = map['memo'];
    ptypeBean.createType = map['createType'];
    ptypeBean.costMode = map['costMode'];
    ptypeBean.pcategory = map['pcategory'];
    ptypeBean.taxNumber = map['taxNumber'];
    ptypeBean.taxRate = map['taxRate'];
    ptypeBean.costPrice = map['costPrice'];
    ptypeBean.supplyInfo = map['supplyInfo'];
    ptypeBean.brandId = map['brandId'];
    ptypeBean.ktypeLimit = map['ktypeLimit'];
    ptypeBean.snenabled = map['snenabled'];
    ptypeBean.propenabled = map['propenabled'];
    ptypeBean.batchenabled = map['batchenabled'];
    ptypeBean.protectDays = map['protectDays'];
    ptypeBean.protectDaysUnit = map['protectDaysUnit'];
    ptypeBean.protectWarndays = map['protectWarndays'];
    ptypeBean.protectWarndaysUnit = map['protectWarndaysUnit'];
    ptypeBean.weight = map['weight'];
    ptypeBean.weightUnit = map['weightUnit'];
    ptypeBean.lithiumBattery = map['lithiumBattery'];
    ptypeBean.solid = map['solid'];
    ptypeBean.difficultyLevel = map['difficultyLevel'];
    ptypeBean.weighted = map['weighted'];
    ptypeBean.retailDefaultUnit = map['retailDefaultUnit'];
    ptypeBean.saleDefaultUnit = map['saleDefaultUnit'];
    ptypeBean.buyDefaultUnit = map['buyDefaultUnit'];
    ptypeBean.stockDefaultUnit = map['stockDefaultUnit'];
    ptypeBean.syncStock = map['syncStock'];
    ptypeBean.ptypeLength = map['ptypeLength'];
    ptypeBean.ptypeWidth = map['ptypeWidth'];
    ptypeBean.ptypeHeight = map['ptypeHeight'];
    ptypeBean.lengthUnit = map['lengthUnit'];
    ptypeBean.createTime = map['createTime'];
    ptypeBean.updateTime = map['updateTime'];
    ptypeBean.skuPrice = map['skuPrice'];
    return ptypeBean;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "typeid": typeid,
        "partypeid": partypeid,
        "usercode": usercode,
        "fullname": fullname,
        "shortname": shortname,
        "namepy": namepy,
        "classed": classed,
        "stoped": stoped,
        "deleted": deleted,
        "rowindex": rowindex,
        "barcode": barcode,
        "standard": standard,
        "ptypeType": ptypeType,
        "ptypeArea": ptypeArea,
        "memo": memo,
        "createType": createType,
        "costMode": costMode,
        "pcategory": pcategory,
        "taxNumber": taxNumber,
        "taxRate": taxRate,
        "costPrice": costPrice,
        "supplyInfo": supplyInfo,
        "brandId": brandId,
        "ktypeLimit": ktypeLimit,
        "snenabled": snenabled,
        "propenabled": propenabled,
        "batchenabled": batchenabled,
        "protectDays": protectDays,
        "protectDaysUnit": protectDaysUnit,
        "protectWarndays": protectWarndays,
        "protectWarndaysUnit": protectWarndaysUnit,
        "weight": weight,
        "weightUnit": weightUnit,
        "lithiumBattery": lithiumBattery,
        "solid": solid,
        "difficultyLevel": difficultyLevel,
        "weighted": weighted,
        "retailDefaultUnit": retailDefaultUnit,
        "saleDefaultUnit": saleDefaultUnit,
        "buyDefaultUnit": buyDefaultUnit,
        "stockDefaultUnit": stockDefaultUnit,
        "syncStock": syncStock,
        "ptypeLength": ptypeLength,
        "ptypeWidth": ptypeWidth,
        "ptypeHeight": ptypeHeight,
        "lengthUnit": lengthUnit,
        "createTime": createTime,
        "updateTime": updateTime,
        "skuPrice": skuPrice,
      };
}
