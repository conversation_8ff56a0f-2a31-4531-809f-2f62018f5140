class BaseInfoRequestDto {
  BaseInfoRequestDto({
      this.id, 
      this.profileId, 
      this.typeid, 
      this.partypeid, 
      this.usercode, 
      this.fullname, 
      this.shortname, 
      this.namepy, 
      this.classed, 
      this.stoped, 
      this.deleted, 
      this.rowindex, 
      this.barcode, 
      this.standard, 
      this.ptypeType, 
      this.ptypeArea, 
      this.memo, 
      this.createType, 
      this.costMode, 
      this.pcategory, 
      this.taxNumber, 
      this.taxRate, 
      this.costPrice, 
      this.brandId, 
      this.ktypeLimit, 
      this.snenabled, 
      this.propenabled, 
      this.batchenabled, 
      this.protectDays, 
      this.protectDaysUnit, 
      this.protectWarndays, 
      this.protectWarndaysUnit, 
      this.weight, 
      this.weightUnit, 
      this.retailDefaultUnit, 
      this.saleDefaultUnit, 
      this.buyDefaultUnit, 
      this.stockDefaultUnit, 
      this.ptypeLength, 
      this.ptypeWidth, 
      this.ptypeHeight, 
      this.lengthUnit, 
      this.createTime, 
      this.updateTime, 
      this.skuPrice, 
      this.propvaluesDescartCount, 
      this.fullbarcodeRuleId, 
      this.subUnit, 
      this.shareType, 
      this.invoiceFullname, 
      this.preparationType, 
      this.industryCategory, 
      this.auditState, 
      this.buyDays, 
      this.fullbarcodeRuleName, 
      this.ptypeBatchUpdateType, 
      this.allowDelUnusedSKU, 
      this.ptypeidlist, 
      this.protectDaysView, 
      this.protectWarndaysView, 
      this.parFullname, 
      this.brandName, 
      this.pics, 
      this.units, 
      this.priceList, 
      this.discountList, 
      this.customField, 
      this.fullbarcodes, 
      this.xcodes, 
      this.props, 
      this.propvalues, 
      this.skus, 
      this.ptypeBtypeRelations, 
      this.ptypeKtypeRelations, 
      this.ptypeOtypeRelations, 
      this.ptypeRelations, 
      this.retailDefaultUnitCode, 
      this.saleDefaultUnitCode, 
      this.buyDefaultUnitCode, 
      this.stockDefaultUnitCode, 
      this.ptypeLabels, 
      this.auditMessage, 
      this.message,});

  BaseInfoRequestDto.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    typeid = json['typeid'];
    partypeid = json['partypeid'];
    usercode = json['usercode'];
    fullname = json['fullname'];
    shortname = json['shortname'];
    namepy = json['namepy'];
    classed = json['classed'];
    stoped = json['stoped'];
    deleted = json['deleted'];
    rowindex = json['rowindex'];
    barcode = json['barcode'];
    standard = json['standard'];
    ptypeType = json['ptypeType'];
    ptypeArea = json['ptypeArea'];
    memo = json['memo'];
    createType = json['createType'];
    costMode = json['costMode'];
    pcategory = json['pcategory'];
    taxNumber = json['taxNumber'];
    taxRate = json['taxRate'];
    costPrice = json['costPrice'];
    brandId = json['brandId'];
    ktypeLimit = json['ktypeLimit'];
    snenabled = json['snenabled'];
    propenabled = json['propenabled'];
    batchenabled = json['batchenabled'];
    protectDays = json['protectDays'];
    protectDaysUnit = json['protectDaysUnit'];
    protectWarndays = json['protectWarndays'];
    protectWarndaysUnit = json['protectWarndaysUnit'];
    weight = json['weight'];
    weightUnit = json['weightUnit'];
    retailDefaultUnit = json['retailDefaultUnit'];
    saleDefaultUnit = json['saleDefaultUnit'];
    buyDefaultUnit = json['buyDefaultUnit'];
    stockDefaultUnit = json['stockDefaultUnit'];
    ptypeLength = json['ptypeLength'];
    ptypeWidth = json['ptypeWidth'];
    ptypeHeight = json['ptypeHeight'];
    lengthUnit = json['lengthUnit'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    skuPrice = json['skuPrice'];
    propvaluesDescartCount = json['propvaluesDescartCount'];
    fullbarcodeRuleId = json['fullbarcodeRuleId'];
    subUnit = json['subUnit'];
    shareType = json['shareType'];
    invoiceFullname = json['invoiceFullname'];
    preparationType = json['preparationType'];
    industryCategory = json['industryCategory'];
    auditState = json['auditState'];
    buyDays = json['buyDays'];
    fullbarcodeRuleName = json['fullbarcodeRuleName'];
    ptypeBatchUpdateType = json['ptypeBatchUpdateType'];
    allowDelUnusedSKU = json['allowDelUnusedSKU'];
    ptypeidlist = json['ptypeidlist'];
    protectDaysView = json['protectDaysView'];
    protectWarndaysView = json['protectWarndaysView'];
    parFullname = json['parFullname'];
    brandName = json['brandName'];
    if (json['pics'] != null) {
      pics = [];
    }
    if (json['units'] != null) {
      units = [];
      json['units'].forEach((v) {
        units?.add(Units.fromJson(v));
      });
    }
    if (json['priceList'] != null) {
      priceList = [];
      json['priceList'].forEach((v) {
        priceList?.add(PriceList.fromJson(v));
      });
    }
    discountList = json['discountList'];
    customField = json['customField'];
    if (json['fullbarcodes'] != null) {
      fullbarcodes = [];
      json['fullbarcodes'].forEach((v) {
        fullbarcodes?.add(Fullbarcodes.fromJson(v));
      });
    }
    xcodes = json['xcodes'];
    if (json['props'] != null) {
      props = [];
    }
    if (json['propvalues'] != null) {
      propvalues = [];
    }
    if (json['skus'] != null) {
      skus = [];
      json['skus'].forEach((v) {
        skus?.add(Skus.fromJson(v));
      });
    }
    if (json['ptypeBtypeRelations'] != null) {
      ptypeBtypeRelations = [];
    }
    if (json['ptypeKtypeRelations'] != null) {
      ptypeKtypeRelations = [];
    }
    ptypeOtypeRelations = json['ptypeOtypeRelations'];
    if (json['ptypeRelations'] != null) {
      ptypeRelations = [];
    }
    retailDefaultUnitCode = json['retailDefaultUnitCode'];
    saleDefaultUnitCode = json['saleDefaultUnitCode'];
    buyDefaultUnitCode = json['buyDefaultUnitCode'];
    stockDefaultUnitCode = json['stockDefaultUnitCode'];
    if (json['ptypeLabels'] != null) {
      ptypeLabels = [];
    }
    auditMessage = json['auditMessage'];
    message = json['message'];
  }
  String? id;
  String? profileId;
  String? typeid;
  String? partypeid;
  String? usercode;
  String? fullname;
  String? shortname;
  String? namepy;
  bool? classed;
  bool? stoped;
  bool? deleted;
  String? rowindex;
  String? barcode;
  String? standard;
  String? ptypeType;
  String? ptypeArea;
  String? memo;
  num? createType;
  num? costMode;
  num? pcategory;
  String? taxNumber;
  num? taxRate;
  num? costPrice;
  String? brandId;
  bool? ktypeLimit;
  num? snenabled;
  bool? propenabled;
  bool? batchenabled;
  num? protectDays;
  num? protectDaysUnit;
  num? protectWarndays;
  num? protectWarndaysUnit;
  num? weight;
  num? weightUnit;
  String? retailDefaultUnit;
  String? saleDefaultUnit;
  String? buyDefaultUnit;
  String? stockDefaultUnit;
  num? ptypeLength;
  num? ptypeWidth;
  num? ptypeHeight;
  num? lengthUnit;
  String? createTime;
  String? updateTime;
  num? skuPrice;
  num? propvaluesDescartCount;
  String? fullbarcodeRuleId;
  String? subUnit;
  num? shareType;
  String? invoiceFullname;
  dynamic preparationType;
  dynamic industryCategory;
  num? auditState;
  num? buyDays;
  String? fullbarcodeRuleName;
  dynamic ptypeBatchUpdateType;
  bool? allowDelUnusedSKU;
  dynamic ptypeidlist;
  num? protectDaysView;
  num? protectWarndaysView;
  String? parFullname;
  String? brandName;
  List<dynamic>? pics;
  List<Units>? units;
  List<PriceList>? priceList;
  dynamic discountList;
  dynamic customField;
  List<Fullbarcodes>? fullbarcodes;
  dynamic xcodes;
  List<dynamic>? props;
  List<dynamic>? propvalues;
  List<Skus>? skus;
  List<dynamic>? ptypeBtypeRelations;
  List<dynamic>? ptypeKtypeRelations;
  dynamic ptypeOtypeRelations;
  List<dynamic>? ptypeRelations;
  num? retailDefaultUnitCode;
  num? saleDefaultUnitCode;
  num? buyDefaultUnitCode;
  num? stockDefaultUnitCode;
  List<dynamic>? ptypeLabels;
  dynamic auditMessage;
  dynamic message;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['typeid'] = typeid;
    map['partypeid'] = partypeid;
    map['usercode'] = usercode;
    map['fullname'] = fullname;
    map['shortname'] = shortname;
    map['namepy'] = namepy;
    map['classed'] = classed;
    map['stoped'] = stoped;
    map['deleted'] = deleted;
    map['rowindex'] = rowindex;
    map['barcode'] = barcode;
    map['standard'] = standard;
    map['ptypeType'] = ptypeType;
    map['ptypeArea'] = ptypeArea;
    map['memo'] = memo;
    map['createType'] = createType;
    map['costMode'] = costMode;
    map['pcategory'] = pcategory;
    map['taxNumber'] = taxNumber;
    map['taxRate'] = taxRate;
    map['costPrice'] = costPrice;
    map['brandId'] = brandId;
    map['ktypeLimit'] = ktypeLimit;
    map['snenabled'] = snenabled;
    map['propenabled'] = propenabled;
    map['batchenabled'] = batchenabled;
    map['protectDays'] = protectDays;
    map['protectDaysUnit'] = protectDaysUnit;
    map['protectWarndays'] = protectWarndays;
    map['protectWarndaysUnit'] = protectWarndaysUnit;
    map['weight'] = weight;
    map['weightUnit'] = weightUnit;
    map['retailDefaultUnit'] = retailDefaultUnit;
    map['saleDefaultUnit'] = saleDefaultUnit;
    map['buyDefaultUnit'] = buyDefaultUnit;
    map['stockDefaultUnit'] = stockDefaultUnit;
    map['ptypeLength'] = ptypeLength;
    map['ptypeWidth'] = ptypeWidth;
    map['ptypeHeight'] = ptypeHeight;
    map['lengthUnit'] = lengthUnit;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['skuPrice'] = skuPrice;
    map['propvaluesDescartCount'] = propvaluesDescartCount;
    map['fullbarcodeRuleId'] = fullbarcodeRuleId;
    map['subUnit'] = subUnit;
    map['shareType'] = shareType;
    map['invoiceFullname'] = invoiceFullname;
    map['preparationType'] = preparationType;
    map['industryCategory'] = industryCategory;
    map['auditState'] = auditState;
    map['buyDays'] = buyDays;
    map['fullbarcodeRuleName'] = fullbarcodeRuleName;
    map['ptypeBatchUpdateType'] = ptypeBatchUpdateType;
    map['allowDelUnusedSKU'] = allowDelUnusedSKU;
    map['ptypeidlist'] = ptypeidlist;
    map['protectDaysView'] = protectDaysView;
    map['protectWarndaysView'] = protectWarndaysView;
    map['parFullname'] = parFullname;
    map['brandName'] = brandName;
    if (pics != null) {
      map['pics'] = pics?.map((v) => v.toJson()).toList();
    }
    if (units != null) {
      map['units'] = units?.map((v) => v.toJson()).toList();
    }
    if (priceList != null) {
      map['priceList'] = priceList?.map((v) => v.toJson()).toList();
    }
    map['discountList'] = discountList;
    map['customField'] = customField;
    if (fullbarcodes != null) {
      map['fullbarcodes'] = fullbarcodes?.map((v) => v.toJson()).toList();
    }
    map['xcodes'] = xcodes;
    if (props != null) {
      map['props'] = props?.map((v) => v.toJson()).toList();
    }
    if (propvalues != null) {
      map['propvalues'] = propvalues?.map((v) => v.toJson()).toList();
    }
    if (skus != null) {
      map['skus'] = skus?.map((v) => v.toJson()).toList();
    }
    if (ptypeBtypeRelations != null) {
      map['ptypeBtypeRelations'] = ptypeBtypeRelations?.map((v) => v.toJson()).toList();
    }
    if (ptypeKtypeRelations != null) {
      map['ptypeKtypeRelations'] = ptypeKtypeRelations?.map((v) => v.toJson()).toList();
    }
    map['ptypeOtypeRelations'] = ptypeOtypeRelations;
    if (ptypeRelations != null) {
      map['ptypeRelations'] = ptypeRelations?.map((v) => v.toJson()).toList();
    }
    map['retailDefaultUnitCode'] = retailDefaultUnitCode;
    map['saleDefaultUnitCode'] = saleDefaultUnitCode;
    map['buyDefaultUnitCode'] = buyDefaultUnitCode;
    map['stockDefaultUnitCode'] = stockDefaultUnitCode;
    if (ptypeLabels != null) {
      map['ptypeLabels'] = ptypeLabels?.map((v) => v.toJson()).toList();
    }
    map['auditMessage'] = auditMessage;
    map['message'] = message;
    return map;
  }

}

class Skus {
  Skus({
      this.id, 
      this.profileId, 
      this.ptypeId, 
      this.stoped, 
      this.propId1, 
      this.propName1, 
      this.propvalueId1, 
      this.propvalueName1, 
      this.propId2, 
      this.propName2, 
      this.propvalueId2, 
      this.propvalueName2, 
      this.propId3, 
      this.propName3, 
      this.propvalueId3, 
      this.propvalueName3, 
      this.propId4, 
      this.propName4, 
      this.propvalueId4, 
      this.propvalueName4, 
      this.propId5, 
      this.propName5, 
      this.propvalueId5, 
      this.propvalueName5, 
      this.propId6, 
      this.propName6, 
      this.propvalueId6, 
      this.propvalueName6, 
      this.createTime, 
      this.updateTime, 
      this.memo, 
      this.picUrl, 
      this.propNames, 
      this.propvalueNames, 
      this.deleted, 
      this.costPrice,});

  Skus.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    ptypeId = json['ptypeId'];
    stoped = json['stoped'];
    propId1 = json['propId1'];
    propName1 = json['propName1'];
    propvalueId1 = json['propvalueId1'];
    propvalueName1 = json['propvalueName1'];
    propId2 = json['propId2'];
    propName2 = json['propName2'];
    propvalueId2 = json['propvalueId2'];
    propvalueName2 = json['propvalueName2'];
    propId3 = json['propId3'];
    propName3 = json['propName3'];
    propvalueId3 = json['propvalueId3'];
    propvalueName3 = json['propvalueName3'];
    propId4 = json['propId4'];
    propName4 = json['propName4'];
    propvalueId4 = json['propvalueId4'];
    propvalueName4 = json['propvalueName4'];
    propId5 = json['propId5'];
    propName5 = json['propName5'];
    propvalueId5 = json['propvalueId5'];
    propvalueName5 = json['propvalueName5'];
    propId6 = json['propId6'];
    propName6 = json['propName6'];
    propvalueId6 = json['propvalueId6'];
    propvalueName6 = json['propvalueName6'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    memo = json['memo'];
    picUrl = json['picUrl'];
    propNames = json['propNames'];
    propvalueNames = json['propvalueNames'];
    deleted = json['deleted'];
    costPrice = json['costPrice'];
  }
  String? id;
  String? profileId;
  String? ptypeId;
  bool? stoped;
  String? propId1;
  String? propName1;
  String? propvalueId1;
  String? propvalueName1;
  String? propId2;
  String? propName2;
  String? propvalueId2;
  String? propvalueName2;
  String? propId3;
  String? propName3;
  String? propvalueId3;
  String? propvalueName3;
  String? propId4;
  String? propName4;
  String? propvalueId4;
  String? propvalueName4;
  String? propId5;
  String? propName5;
  String? propvalueId5;
  String? propvalueName5;
  String? propId6;
  String? propName6;
  String? propvalueId6;
  String? propvalueName6;
  String? createTime;
  String? updateTime;
  String? memo;
  String? picUrl;
  String? propNames;
  String? propvalueNames;
  bool? deleted;
  num? costPrice;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['ptypeId'] = ptypeId;
    map['stoped'] = stoped;
    map['propId1'] = propId1;
    map['propName1'] = propName1;
    map['propvalueId1'] = propvalueId1;
    map['propvalueName1'] = propvalueName1;
    map['propId2'] = propId2;
    map['propName2'] = propName2;
    map['propvalueId2'] = propvalueId2;
    map['propvalueName2'] = propvalueName2;
    map['propId3'] = propId3;
    map['propName3'] = propName3;
    map['propvalueId3'] = propvalueId3;
    map['propvalueName3'] = propvalueName3;
    map['propId4'] = propId4;
    map['propName4'] = propName4;
    map['propvalueId4'] = propvalueId4;
    map['propvalueName4'] = propvalueName4;
    map['propId5'] = propId5;
    map['propName5'] = propName5;
    map['propvalueId5'] = propvalueId5;
    map['propvalueName5'] = propvalueName5;
    map['propId6'] = propId6;
    map['propName6'] = propName6;
    map['propvalueId6'] = propvalueId6;
    map['propvalueName6'] = propvalueName6;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['memo'] = memo;
    map['picUrl'] = picUrl;
    map['propNames'] = propNames;
    map['propvalueNames'] = propvalueNames;
    map['deleted'] = deleted;
    map['costPrice'] = costPrice;
    map['hashCode'] = hashCode;
    return map;
  }

}

class Fullbarcodes {
  Fullbarcodes({
      this.id, 
      this.profileId, 
      this.ptypeId, 
      this.skuId, 
      this.unitId, 
      this.fullbarcode, 
      this.defaulted, 
      this.createTime, 
      this.updateTime, 
      this.unitCode, 
      this.sku, 
      this.pBarcode, 
      this.uBarcode,});

  Fullbarcodes.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    ptypeId = json['ptypeId'];
    skuId = json['skuId'];
    unitId = json['unitId'];
    fullbarcode = json['fullbarcode'];
    defaulted = json['defaulted'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    unitCode = json['unitCode'];
    sku = json['sku'] != null ? Sku.fromJson(json['sku']) : null;
    pBarcode = json['pBarcode'];
    uBarcode = json['uBarcode'];
  }
  String? id;
  dynamic profileId;
  dynamic ptypeId;
  dynamic skuId;
  dynamic unitId;
  dynamic fullbarcode;
  bool? defaulted;
  dynamic createTime;
  dynamic updateTime;
  num? unitCode;
  Sku? sku;
  dynamic pBarcode;
  dynamic uBarcode;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['ptypeId'] = ptypeId;
    map['skuId'] = skuId;
    map['unitId'] = unitId;
    map['fullbarcode'] = fullbarcode;
    map['defaulted'] = defaulted;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['unitCode'] = unitCode;
    if (sku != null) {
      map['sku'] = sku?.toJson();
    }
    map['pBarcode'] = pBarcode;
    map['uBarcode'] = uBarcode;
    return map;
  }

}

class Sku {
  Sku({
      this.id, 
      this.profileId, 
      this.ptypeId, 
      this.stoped, 
      this.propId1, 
      this.propName1, 
      this.propvalueId1, 
      this.propvalueName1, 
      this.propId2, 
      this.propName2, 
      this.propvalueId2, 
      this.propvalueName2, 
      this.propId3, 
      this.propName3, 
      this.propvalueId3, 
      this.propvalueName3, 
      this.propId4, 
      this.propName4, 
      this.propvalueId4, 
      this.propvalueName4, 
      this.propId5, 
      this.propName5, 
      this.propvalueId5, 
      this.propvalueName5, 
      this.propId6, 
      this.propName6, 
      this.propvalueId6, 
      this.propvalueName6, 
      this.createTime, 
      this.updateTime, 
      this.memo, 
      this.picUrl, 
      this.propNames, 
      this.propvalueNames, 
      this.deleted, 
      this.costPrice, });

  Sku.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    ptypeId = json['ptypeId'];
    stoped = json['stoped'];
    propId1 = json['propId1'];
    propName1 = json['propName1'];
    propvalueId1 = json['propvalueId1'];
    propvalueName1 = json['propvalueName1'];
    propId2 = json['propId2'];
    propName2 = json['propName2'];
    propvalueId2 = json['propvalueId2'];
    propvalueName2 = json['propvalueName2'];
    propId3 = json['propId3'];
    propName3 = json['propName3'];
    propvalueId3 = json['propvalueId3'];
    propvalueName3 = json['propvalueName3'];
    propId4 = json['propId4'];
    propName4 = json['propName4'];
    propvalueId4 = json['propvalueId4'];
    propvalueName4 = json['propvalueName4'];
    propId5 = json['propId5'];
    propName5 = json['propName5'];
    propvalueId5 = json['propvalueId5'];
    propvalueName5 = json['propvalueName5'];
    propId6 = json['propId6'];
    propName6 = json['propName6'];
    propvalueId6 = json['propvalueId6'];
    propvalueName6 = json['propvalueName6'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    memo = json['memo'];
    picUrl = json['picUrl'];
    propNames = json['propNames'];
    propvalueNames = json['propvalueNames'];
    deleted = json['deleted'];
    costPrice = json['costPrice'];
  }
  dynamic id;
  dynamic profileId;
  dynamic ptypeId;
  dynamic stoped;
  dynamic propId1;
  dynamic propName1;
  dynamic propvalueId1;
  dynamic propvalueName1;
  dynamic propId2;
  dynamic propName2;
  dynamic propvalueId2;
  dynamic propvalueName2;
  dynamic propId3;
  dynamic propName3;
  dynamic propvalueId3;
  dynamic propvalueName3;
  dynamic propId4;
  dynamic propName4;
  dynamic propvalueId4;
  dynamic propvalueName4;
  dynamic propId5;
  dynamic propName5;
  dynamic propvalueId5;
  dynamic propvalueName5;
  dynamic propId6;
  dynamic propName6;
  dynamic propvalueId6;
  dynamic propvalueName6;
  dynamic createTime;
  dynamic updateTime;
  dynamic memo;
  dynamic picUrl;
  dynamic propNames;
  dynamic propvalueNames;
  dynamic deleted;
  dynamic costPrice;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['ptypeId'] = ptypeId;
    map['stoped'] = stoped;
    map['propId1'] = propId1;
    map['propName1'] = propName1;
    map['propvalueId1'] = propvalueId1;
    map['propvalueName1'] = propvalueName1;
    map['propId2'] = propId2;
    map['propName2'] = propName2;
    map['propvalueId2'] = propvalueId2;
    map['propvalueName2'] = propvalueName2;
    map['propId3'] = propId3;
    map['propName3'] = propName3;
    map['propvalueId3'] = propvalueId3;
    map['propvalueName3'] = propvalueName3;
    map['propId4'] = propId4;
    map['propName4'] = propName4;
    map['propvalueId4'] = propvalueId4;
    map['propvalueName4'] = propvalueName4;
    map['propId5'] = propId5;
    map['propName5'] = propName5;
    map['propvalueId5'] = propvalueId5;
    map['propvalueName5'] = propvalueName5;
    map['propId6'] = propId6;
    map['propName6'] = propName6;
    map['propvalueId6'] = propvalueId6;
    map['propvalueName6'] = propvalueName6;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['memo'] = memo;
    map['picUrl'] = picUrl;
    map['propNames'] = propNames;
    map['propvalueNames'] = propvalueNames;
    map['deleted'] = deleted;
    map['costPrice'] = costPrice;
    return map;
  }

}

class PriceList {
  PriceList({
      this.id, 
      this.profileId, 
      this.unitId, 
      this.ptypeId, 
      this.skuId, 
      this.retailPrice, 
      this.preprice1, 
      this.preprice2, 
      this.preprice3, 
      this.preprice4, 
      this.preprice5, 
      this.preprice6, 
      this.preprice7, 
      this.preprice8, 
      this.preprice9, 
      this.preprice10, 
      this.buyPrice, 
      this.minSalePrice, 
      this.lastSalePrice, 
      this.lastBuyPrice, 
      this.createTime, 
      this.updateTime, 
      this.lastSaleTime, 
      this.lastBuyTime, 
      this.lastSaleDpPrice, 
      this.lastBuyDpPrice, 
      this.minBuyPrice, 
      this.unitCode, 
      this.sku,});

  PriceList.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    unitId = json['unitId'];
    ptypeId = json['ptypeId'];
    skuId = json['skuId'];
    retailPrice = json['retailPrice'];
    preprice1 = json['preprice1'];
    preprice2 = json['preprice2'];
    preprice3 = json['preprice3'];
    preprice4 = json['preprice4'];
    preprice5 = json['preprice5'];
    preprice6 = json['preprice6'];
    preprice7 = json['preprice7'];
    preprice8 = json['preprice8'];
    preprice9 = json['preprice9'];
    preprice10 = json['preprice10'];
    buyPrice = json['buyPrice'];
    minSalePrice = json['minSalePrice'];
    lastSalePrice = json['lastSalePrice'];
    lastBuyPrice = json['lastBuyPrice'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    lastSaleTime = json['lastSaleTime'];
    lastBuyTime = json['lastBuyTime'];
    lastSaleDpPrice = json['lastSaleDpPrice'];
    lastBuyDpPrice = json['lastBuyDpPrice'];
    minBuyPrice = json['minBuyPrice'];
    unitCode = json['unitCode'];
    sku = json['sku'];
  }
  String? id;
  dynamic profileId;
  dynamic unitId;
  dynamic ptypeId;
  dynamic skuId;
  String? retailPrice;
  num? preprice1;
  num? preprice2;
  num? preprice3;
  num? preprice4;
  num? preprice5;
  num? preprice6;
  num? preprice7;
  num? preprice8;
  num? preprice9;
  num? preprice10;
  num? buyPrice;
  num? minSalePrice;
  num? lastSalePrice;
  num? lastBuyPrice;
  dynamic createTime;
  dynamic updateTime;
  String? lastSaleTime;
  String? lastBuyTime;
  num? lastSaleDpPrice;
  num? lastBuyDpPrice;
  num? minBuyPrice;
  num? unitCode;
  dynamic sku;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['unitId'] = unitId;
    map['ptypeId'] = ptypeId;
    map['skuId'] = skuId;
    map['retailPrice'] = retailPrice;
    map['preprice1'] = preprice1;
    map['preprice2'] = preprice2;
    map['preprice3'] = preprice3;
    map['preprice4'] = preprice4;
    map['preprice5'] = preprice5;
    map['preprice6'] = preprice6;
    map['preprice7'] = preprice7;
    map['preprice8'] = preprice8;
    map['preprice9'] = preprice9;
    map['preprice10'] = preprice10;
    map['buyPrice'] = buyPrice;
    map['minSalePrice'] = minSalePrice;
    map['lastSalePrice'] = lastSalePrice;
    map['lastBuyPrice'] = lastBuyPrice;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['lastSaleTime'] = lastSaleTime;
    map['lastBuyTime'] = lastBuyTime;
    map['lastSaleDpPrice'] = lastSaleDpPrice;
    map['lastBuyDpPrice'] = lastBuyDpPrice;
    map['minBuyPrice'] = minBuyPrice;
    map['unitCode'] = unitCode;
    map['sku'] = sku;
    return map;
  }

}

class Units {
  Units({
    this.weight,
    this.retailPrice,
      this.id, 
      this.profileId, 
      this.ptypeId, 
      this.unitCode, 
      this.unitName, 
      this.unitRate, 
      this.barcode, 
      this.createTime, 
      this.updateTime, 
      this.ptypeLength, 
      this.ptypeWidth, 
      this.ptypeHeight, 
      this.ptypeVolume, 
      this.ptypeLengthUnit, 
      this.ptypeWeight, 
      this.ptypeWeightUnit,});

  Units.fromJson(dynamic json) {
    weight= json["weight"];
    id = json['id'];
    retailPrice = json["retailPrice"];
    profileId = json['profileId'];
    ptypeId = json['ptypeId'];
    unitCode = json['unitCode'];
    unitName = json['unitName'];
    unitRate = json['unitRate'];
    barcode = json['barcode'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    ptypeLength = json['ptypeLength'];
    ptypeWidth = json['ptypeWidth'];
    ptypeHeight = json['ptypeHeight'];
    ptypeVolume = json['ptypeVolume'];
    ptypeLengthUnit = json['ptypeLengthUnit'];
    ptypeWeight = json['ptypeWeight'];
    ptypeWeightUnit = json['ptypeWeightUnit'];
  }
  String? id;
  String? profileId;
  String? ptypeId;
  num? unitCode;
  String? unitName;
  num? unitRate;
  String? barcode;
  dynamic createTime;
  dynamic updateTime;
  num? ptypeLength;
  num? ptypeWidth;
  num? ptypeHeight;
  num? ptypeVolume;
  num? ptypeLengthUnit;
  num? ptypeWeight;
  num? ptypeWeightUnit;
  String? retailPrice;
  num? weight;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['retailPrice'] = retailPrice;
    map['weight'] = weight;
    map['profileId'] = profileId;
    map['ptypeId'] = ptypeId;
    map['unitCode'] = unitCode;
    map['unitName'] = unitName;
    map['unitRate'] = unitRate;
    map['barcode'] = barcode;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['ptypeLength'] = ptypeLength;
    map['ptypeWidth'] = ptypeWidth;
    map['ptypeHeight'] = ptypeHeight;
    map['ptypeVolume'] = ptypeVolume;
    map['ptypeLengthUnit'] = ptypeLengthUnit;
    map['ptypeWeight'] = ptypeWeight;
    map['ptypeWeightUnit'] = ptypeWeightUnit;
    return map;
  }

}