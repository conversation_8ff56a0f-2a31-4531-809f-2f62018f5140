import '../../enum/bill_type.dart';

class OrderBillItem {
  String? vipName;
  String? vipPhone;
  bool select = false;

  num? giftPreferentialBillTotal;
  dynamic costTotal;
  int? accountingType;
  String? arriveDate;
  int? auditState;
  String? balanceFullname;
  var balanceSettled;
  String? bfullname;
  String? billDate;
  String? billNumber;
  double? billTotal;
  String? btypeId;
  String? buyerId;
  var changeType;
  String? countQty;
  String? createEfullname;
  String? createEtypeId;
  String? createTime;
  var createType;
  double? currencyBillTotal;
  double? currencyDepositTotal;
  double? currencyFeeTotal;
  String? currencyId;
  double? currencyOrderOtherincomeTotal;
  double? currencyOrderPreferentialAllotTotal;
  String? customerEmail;
  String? customerReceiveFullAddress;
  String? customerReceiver;
  String? customerReceiverAddress;
  String? customerReceiverCity;
  String? customerReceiverCountry;
  String? customerReceiverDistrict;
  String? customerReceiverMobile;
  String? customerReceiverPhone;
  String? customerReceiverProvince;
  String? customerReceiverZipCode;
  var deliveryType;
  String? dfullname;
  String? dtypeId;
  String? efullname;
  String? etypeId;
  double? exchangeRate;
  double? feeTotal;
  int? invoiceType;
  String? kfullname;
  String? kfullname2;
  String? ktypeId;
  String? ktypeId2;
  String? memo;
  double? otherIncomeTotal;
  String? otypeId;
  String? paymentDate;
  int? period;
  String? postEfullname;
  String? postEtypeId;
  var postState;
  String? postTime;
  double? preferentialTotal;
  int? printCount;
  var processType;
  String? profileId;
  String? summary;
  String? updateTime;
  String vchcode = "";
  late int vchtype;
  String? vchtypeEnum;
  String? vchtypeName;
  String? postAndauditStateName;
  int? postAndauditState;
  String? businessTypeEnum;
  String? businessTypeName;
  String? billBusinessTypeEnum;
  String? billBusinessTypeName;
  String? businessType;
  String? ofullname;
  List<OrderBillMark?> orderBillMarkList = [];

  static OrderBillItem fromMap(Map<String, dynamic>? map) {
    if (map == null) return OrderBillItem();
    OrderBillItem tdBillCoreDtoBean = OrderBillItem();
    tdBillCoreDtoBean.vipPhone = map['vipPhone'];
    tdBillCoreDtoBean.vipName = map['vipName'];
    tdBillCoreDtoBean.giftPreferentialBillTotal =
        map['giftPreferentialBillTotal'] ?? 0;
    tdBillCoreDtoBean.billBusinessTypeEnum = map['billBusinessTypeEnum'];
    tdBillCoreDtoBean.costTotal = map['costTotal'];
    tdBillCoreDtoBean.billBusinessTypeName = map['billBusinessTypeName'];
    tdBillCoreDtoBean.accountingType = map['accountingType'];
    tdBillCoreDtoBean.arriveDate = map['arriveDate'];
    tdBillCoreDtoBean.auditState = map['auditState'];
    tdBillCoreDtoBean.balanceFullname = map['balanceFullname'];
    tdBillCoreDtoBean.balanceSettled = map['balanceSettled'];
    tdBillCoreDtoBean.bfullname = map['bfullname'];
    tdBillCoreDtoBean.billDate = map['billDate'];
    tdBillCoreDtoBean.billNumber = map['billNumber'];
    tdBillCoreDtoBean.billTotal = map['billTotal'];
    tdBillCoreDtoBean.btypeId = map['btypeId'];
    tdBillCoreDtoBean.buyerId = map['buyerId'];
    tdBillCoreDtoBean.changeType = map['changeType'];
    tdBillCoreDtoBean.countQty = map['countQty'];
    tdBillCoreDtoBean.createEfullname = map['createEfullname'];
    tdBillCoreDtoBean.createEtypeId = map['createEtypeId'];
    tdBillCoreDtoBean.createTime = map['createTime'];
    tdBillCoreDtoBean.createType = map['createType'];
    tdBillCoreDtoBean.currencyBillTotal = map['currencyBillTotal'];
    tdBillCoreDtoBean.currencyDepositTotal = map['currencyDepositTotal'];
    tdBillCoreDtoBean.currencyFeeTotal = map['currencyFeeTotal'];
    tdBillCoreDtoBean.currencyId = map['currencyId'];
    tdBillCoreDtoBean.currencyOrderOtherincomeTotal =
        map['currencyOrderOtherincomeTotal'];
    tdBillCoreDtoBean.currencyOrderPreferentialAllotTotal =
        map['currencyOrderPreferentialAllotTotal'];
    tdBillCoreDtoBean.customerEmail = map['customerEmail'];
    tdBillCoreDtoBean.customerReceiveFullAddress =
        map['customerReceiveFullAddress'];
    tdBillCoreDtoBean.customerReceiver = map['customerReceiver'];
    tdBillCoreDtoBean.customerReceiverAddress = map['customerReceiverAddress'];
    tdBillCoreDtoBean.customerReceiverCity = map['customerReceiverCity'];
    tdBillCoreDtoBean.customerReceiverCountry = map['customerReceiverCountry'];
    tdBillCoreDtoBean.customerReceiverDistrict =
        map['customerReceiverDistrict'];
    tdBillCoreDtoBean.customerReceiverMobile = map['customerReceiverMobile'];
    tdBillCoreDtoBean.customerReceiverPhone = map['customerReceiverPhone'];
    tdBillCoreDtoBean.customerReceiverProvince =
        map['customerReceiverProvince'];
    tdBillCoreDtoBean.customerReceiverZipCode = map['customerReceiverZipCode'];
    tdBillCoreDtoBean.deliveryType = map['deliveryType'];
    tdBillCoreDtoBean.dfullname = map['dfullname'];
    tdBillCoreDtoBean.dtypeId = map['dtypeId'];
    tdBillCoreDtoBean.efullname = map['efullname'];
    tdBillCoreDtoBean.etypeId = map['etypeId'];
    tdBillCoreDtoBean.exchangeRate = map['exchangeRate'];
    tdBillCoreDtoBean.feeTotal = map['feeTotal'];
    tdBillCoreDtoBean.invoiceType = map['invoiceType'];
    tdBillCoreDtoBean.kfullname = map['kfullname'];
    tdBillCoreDtoBean.kfullname2 = map['kfullname2'];
    tdBillCoreDtoBean.ktypeId = map['ktypeId'];
    tdBillCoreDtoBean.ktypeId2 = map['ktypeId2'];
    tdBillCoreDtoBean.memo = map['memo'];
    tdBillCoreDtoBean.otherIncomeTotal = map['otherIncomeTotal'];
    tdBillCoreDtoBean.otypeId = map['otypeId'];
    tdBillCoreDtoBean.paymentDate = map['paymentDate'];
    tdBillCoreDtoBean.period = map['period'];
    tdBillCoreDtoBean.postEfullname = map['postEfullname'];
    tdBillCoreDtoBean.postEtypeId = map['postEtypeId'];
    tdBillCoreDtoBean.postState = map['postState'];
    tdBillCoreDtoBean.postTime = map['postTime'];
    tdBillCoreDtoBean.preferentialTotal = map['preferentialTotal'];
    tdBillCoreDtoBean.printCount = map['printCount'];
    tdBillCoreDtoBean.processType = map['processType'];
    tdBillCoreDtoBean.profileId = map['profileId'];
    tdBillCoreDtoBean.summary = map['summary'];
    tdBillCoreDtoBean.updateTime = map['updateTime'];
    tdBillCoreDtoBean.vchcode = map['vchcode'];
    tdBillCoreDtoBean.vchtype = map['vchtype'];
    tdBillCoreDtoBean.vchtypeEnum = map['vchtypeEnum'];
    tdBillCoreDtoBean.vchtypeName = map['vchtypeName'];
    tdBillCoreDtoBean.businessTypeEnum = map['businessTypeEnum'];
    tdBillCoreDtoBean.businessTypeName = map['businessTypeName'];
    tdBillCoreDtoBean.businessType = map['businessType'];
    tdBillCoreDtoBean.postAndauditStateName = map['postAndauditStateName'];
    tdBillCoreDtoBean.postAndauditState = map['postAndauditState'];
    tdBillCoreDtoBean.ofullname = map['ofullname'];
    if (map['mark'] != null) {
      tdBillCoreDtoBean.orderBillMarkList = [];
      map['mark'].forEach((v) {
        tdBillCoreDtoBean.orderBillMarkList.add(OrderBillMark.fromJson(v));
      });
    }
    return tdBillCoreDtoBean;
  }

  Map<String, dynamic> toJson() => {
        "vipPhone": vipPhone,
        "vipName": vipName,
        "giftPreferentialBillTotal": giftPreferentialBillTotal,
        "costTotal": costTotal,
        "billBusinessTypeEnum": billBusinessTypeEnum,
        "billBusinessTypeName": billBusinessTypeName,
        "accountingType": accountingType,
        "arriveDate": arriveDate,
        "auditState": auditState,
        "balanceFullname": balanceFullname,
        "balanceSettled": balanceSettled,
        "bfullname": bfullname,
        "billDate": billDate,
        "billNumber": billNumber,
        "billTotal": billTotal,
        "btypeId": btypeId,
        "buyerId": buyerId,
        "changeType": changeType,
        "countQty": countQty,
        "createEfullname": createEfullname,
        "createEtypeId": createEtypeId,
        "createTime": createTime,
        "createType": createType,
        "currencyBillTotal": currencyBillTotal,
        "currencyDepositTotal": currencyDepositTotal,
        "currencyFeeTotal": currencyFeeTotal,
        "currencyId": currencyId,
        "currencyOrderOtherincomeTotal": currencyOrderOtherincomeTotal,
        "currencyOrderPreferentialAllotTotal":
            currencyOrderPreferentialAllotTotal,
        "customerEmail": customerEmail,
        "customerReceiveFullAddress": customerReceiveFullAddress,
        "customerReceiver": customerReceiver,
        "customerReceiverAddress": customerReceiverAddress,
        "customerReceiverCity": customerReceiverCity,
        "customerReceiverCountry": customerReceiverCountry,
        "customerReceiverDistrict": customerReceiverDistrict,
        "customerReceiverMobile": customerReceiverMobile,
        "customerReceiverPhone": customerReceiverPhone,
        "customerReceiverProvince": customerReceiverProvince,
        "customerReceiverZipCode": customerReceiverZipCode,
        "deliveryType": deliveryType,
        "dfullname": dfullname,
        "dtypeId": dtypeId,
        "efullname": efullname,
        "etypeId": etypeId,
        "exchangeRate": exchangeRate,
        "feeTotal": feeTotal,
        "invoiceType": invoiceType,
        "kfullname": kfullname,
        "kfullname2": kfullname2,
        "ktypeId": ktypeId,
        "ktypeId2": ktypeId2,
        "memo": memo,
        "otherIncomeTotal": otherIncomeTotal,
        "otypeId": otypeId,
        "paymentDate": paymentDate,
        "period": period,
        "postEfullname": postEfullname,
        "postEtypeId": postEtypeId,
        "postState": postState,
        "postTime": postTime,
        "preferentialTotal": preferentialTotal,
        "printCount": printCount,
        "processType": processType,
        "profileId": profileId,
        "summary": summary,
        "updateTime": updateTime,
        "vchcode": vchcode,
        "vchtype": vchtype,
        "vchtypeEnum": vchtypeEnum,
        "vchtypeName": vchtypeName,
        "businessTypeEnum": businessTypeEnum,
        "businessTypeName": businessTypeName,
        "businessType": businessType,
        "postAndauditStateName": postAndauditStateName,
        "postAndauditState": postAndauditState,
        "ofullname": ofullname,
        "orderBillMarkList": orderBillMarkList.map((v) => v?.toJson()).toList(),
      };
}

class OrderBillMark {
  String? bubble;
  String? markCode;
  String? bigData;
  String? id;
  String? vchcode;
  String? profileId;
  String? createTime;
  String? updateTime;
  String? markTarget;
  String? detailId;
  String? showType;
  String? orderType;
  String? markUseType;
  String? createType;
  String? markDataId;
  String? baseMarkBigData;
  String? reason;
  BillMarkEnum? baseOrderMarkEnum;
  String? userMark;
  String? deliverRelationDTO;
  String? markComputedState;
  bool? accData;
  String? uniqueness;
  bool? forcedDeletion;
  String? ukMark;
  bool? deleteMarkOnAdd;
  String? markShowName;
  String? markColor;
  String? backgroundColor;
  String? borderColor;
  String? fontColor;

  OrderBillMark.fromJson(Map<String, dynamic> json) {
    bubble = json['bubble'];
    markCode = json['markCode'];
    bigData = json['bigData'];
    id = json['id'];
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    markTarget = json['markTarget'];
    detailId = json['detailId'];
    showType = json['showType'];
    orderType = json['orderType'];
    markUseType = json['markUseType'];
    createType = json['createType'];
    markDataId = json['markDataId'];
    baseMarkBigData = json['baseMarkBigData'];
    reason = json['reason'];
    baseOrderMarkEnum = (json['markCode'] is String
        ? BillMarkEnumInt[json['markCode']]
        : BillMarkEnum.NORMAL_BILL);
    userMark = json['userMark'];
    deliverRelationDTO = json['deliverRelationDTO'];
    markComputedState = json['markComputedState'];
    accData = json['accData'];
    uniqueness = json['uniqueness'];
    forcedDeletion = json['forcedDeletion'];
    ukMark = json['ukMark'];
    deleteMarkOnAdd = json['deleteMarkOnAdd'];
    markShowName = json['markShowName'];
    markColor = (json['markColor'] as Map?)?["fontColor"];
    fontColor = (json['markColor'] as Map?)?["fontColor"];
    borderColor = (json['markColor'] as Map?)?["fontColor"];
    backgroundColor = (json['markColor'] as Map?)?["fontColor"];
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = <String, dynamic>{};
    data['bubble'] = this.bubble;
    data['markCode'] = this.markCode;
    data['bigData'] = this.bigData;
    data['id'] = this.id;
    data['vchcode'] = this.vchcode;
    data['profileId'] = this.profileId;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['markTarget'] = this.markTarget;
    data['detailId'] = this.detailId;
    data['showType'] = this.showType;
    data['orderType'] = this.orderType;
    data['markUseType'] = this.markUseType;
    data['createType'] = this.createType;
    data['markDataId'] = this.markDataId;
    data['baseMarkBigData'] = this.baseMarkBigData;
    data['reason'] = this.reason;
    data['baseOrderMarkEnum'] = BillMarkEnumString[this.baseOrderMarkEnum];
    data['userMark'] = this.userMark;
    data['deliverRelationDTO'] = this.deliverRelationDTO;
    data['markComputedState'] = markComputedState;
    data['accData'] = this.accData;
    data['uniqueness'] = this.uniqueness;
    data['forcedDeletion'] = this.forcedDeletion;
    data['ukMark'] = this.ukMark;
    data['deleteMarkOnAdd'] = this.deleteMarkOnAdd;
    data['markShowName'] = this.markShowName;
    data['markColor'] = this.markColor;
    return data;
  }
}
