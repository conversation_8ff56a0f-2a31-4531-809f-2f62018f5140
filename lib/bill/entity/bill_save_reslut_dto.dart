import 'package:halo_utils/utils/string_util.dart';

import 'bill_audit_info_dto.dart';
import 'bill_save_exception_dto.dart';
import 'bill_save_result_type.dart';

/// vchcode : null
/// billNumber : null
/// resultType : "ERROR"
/// exceptionInfo : [{"message":null,"detailList":null}]
/// checkIndex : null
/// message : null

class BillSaveResultDto {
  ///单据id
  String? vchcode;

  ///单据编号
  String? billNumber;

  ///单据类型，后端的枚举
  String? vchtype;

  ///返回类型
  BillSaveResultType? resultType;

  ///单据状态int值
  int? intPostState;

  ///单据录单日期
  String billDate = "";

  ///单据状态，调拨单时返回的int，但是后端定义bean时是BillPostState枚举
  dynamic postState;

  ///异常对象
  List<BillSaveExceptionDto>? exceptionInfo;
  BillAuditInfoDTO? billAuditInfo; //单据审核信息(含当前用户)

  dynamic checkIndex;
  String? message;
  String? invoiceSign;
  int? saleScore;

  static BillSaveResultDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return BillSaveResultDto();
    BillSaveResultDto billSaveResultDtoBean = BillSaveResultDto();
    billSaveResultDtoBean.vchcode = map['vchcode'];
    billSaveResultDtoBean.billNumber = map['billNumber'];
    billSaveResultDtoBean.vchtype = map['vchtype'];
    billSaveResultDtoBean.resultType = StringUtil.enumFromString(
        BillSaveResultType.values, map['resultType'].toString());
    billSaveResultDtoBean.exceptionInfo = (map['exceptionInfo'] as List?)
            ?.map((o) => BillSaveExceptionDto.fromMap(o))
            .toList() ??
        [];
    billSaveResultDtoBean.checkIndex = map['checkIndex'];
    billSaveResultDtoBean.message = map['message'] ?? "";
    billSaveResultDtoBean.billAuditInfo =
        BillAuditInfoDTO.fromMap(map["billAuditInfo"]);
    billSaveResultDtoBean.intPostState = map["intPostState"];
    billSaveResultDtoBean.postState = map["postState"];
    billSaveResultDtoBean.invoiceSign = map["invoiceSign"];
    billSaveResultDtoBean.saleScore = map["saleScore"];
    billSaveResultDtoBean.billDate = map["billDate"] ?? "";
    return billSaveResultDtoBean;
  }

  Map toJson() => {
        "vchcode": vchcode,
        "billNumber": billNumber,
        "vchtype": vchtype,
        "resultType": resultType,
        "exceptionInfo": exceptionInfo,
        "checkIndex": checkIndex,
        "message": message,
        "billAuditInfo": billAuditInfo,
        "intPostState": intPostState,
        "postState": postState,
        "invoiceSign": invoiceSign,
        "saleScore": saleScore,
        "billDate": billDate
      };
}
