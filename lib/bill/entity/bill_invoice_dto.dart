/// isShow : "0"
/// invoiceUserType : 0
/// invoiceType : 0

class BillInvoiceDTO {
  String? id;
  String? vchcode;
  int? invoiceType;

  // 发票抬头
  String? invoiceTitle;

  //税号
  String? invoiceTax;

  //开票单位 0,个人 1企业
  int? invoiceUserType;

  //是否需要发票
  bool? needInvoice;
  int? isShow = 0;
  int? invoiceState; //发票状态
  String? address; //注册地址
  String? bank; //开户银行
  String? bankAccount; //开户账号
  String? memo; //发票备注
  String? phone; //注册电话

  static BillInvoiceDTO fromMap(Map<dynamic, dynamic>? map) {
    if (map == null) return BillInvoiceDTO();
    BillInvoiceDTO billInvoice = BillInvoiceDTO();
    billInvoice.invoiceUserType = map['invoiceUserType'];
    billInvoice.invoiceType = map['invoiceType'];
    billInvoice.id = map['id'];
    billInvoice.vchcode = map['vchcode'];
    billInvoice.invoiceTitle = map['invoiceTitle'];
    billInvoice.invoiceTax = map['invoiceTax'];
    billInvoice.isShow = map['isShow'];
    billInvoice.needInvoice = map['needInvoice'];
    billInvoice.invoiceState = map['invoiceState'];
    billInvoice.address = map['address'];
    billInvoice.bank = map['bank'];
    billInvoice.memo = map['memo'];
    billInvoice.phone = map['phone'];
    billInvoice.bankAccount = map['bankAccount'];
    return billInvoice;
  }

  Map<dynamic, dynamic> toJson() => {
        "invoiceUserType": invoiceUserType,
        "invoiceType": invoiceType,
        "id": id,
        "vchcode": vchcode,
        "invoiceTitle": invoiceTitle,
        "invoiceTax": invoiceTax,
        "isShow": isShow,
        "needInvoice": needInvoice,
        "invoiceState": invoiceState,
        "address": address,
        "bankAccount": bankAccount,
        "bank": bank,
        "memo": memo,
        "phone": phone,
      };
}
