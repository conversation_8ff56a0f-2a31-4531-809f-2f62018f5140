/// filterkey : "string"
/// filtervalue : "string"
/// pageIndex : 0
/// parTypeId : "string"
/// scategory : "string"
/// stoped : true

class KtypeQueryParamsDto {
  String? filterkey;
  String filtervalue="";
  int? pageIndex;
  String? parTypeId;
  String? scategory;
  bool? stoped;

  static KtypeQueryParamsDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return KtypeQueryParamsDto();
    KtypeQueryParamsDto ktypeQueryDtoBean = KtypeQueryParamsDto();
    ktypeQueryDtoBean.filterkey = map['filterkey'];
    ktypeQueryDtoBean.filtervalue = map['filtervalue'];
    ktypeQueryDtoBean.pageIndex = map['pageIndex'];
    ktypeQueryDtoBean.parTypeId = map['parTypeId'];
    ktypeQueryDtoBean.scategory = map['scategory'];
    ktypeQueryDtoBean.stoped = map['stoped'];
    return ktypeQueryDtoBean;
  }

  Map toJson() => {
    "filterkey": filterkey,
    "filtervalue": filtervalue,
    "pageIndex": pageIndex,
    "parTypeId": parTypeId,
    "scategory": scategory,
    "stoped": stoped,
  };
}