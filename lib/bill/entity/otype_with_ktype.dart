///门店和关联的仓库
class OtypeWithKtypeDTO {
  String? otypeId;
  String? ofullname;
  String? ktypeId;
  String? kfullname;

  Map<String, dynamic> toJson() {
    return {
      'otypeId': otypeId,
      'ofullname': ofullname,
      'ktypeId': ktypeId,
      'kfullname': kfullname,
    };
  }

  OtypeWithKtypeDTO.fromMap(Map<String, dynamic> map)
      : otypeId = map['otypeId'],
        ofullname = map['ofullname'],
        ktypeId = map['ktypeId'],
        kfullname = map['kfullname'];
}
