///单据实体
class TdBillCoreDTO {
  //页面枚举
  String? vchtypeName;

  String? vchtypeEnum;

  num? ptypeSubQty;

  num? ptypeCompletedSubQty;

  String? businessTypeEnum;

  //单据ID
  String? vchcode;

  //帐套ID
  String? profileId;

  //vchtype
  int? vchtype;

  //处理流程
  int? processType;

  //交货方式（默认自提
  int? deliveryType;

  //核算方式
  int? accountingType;

  //单据编号
  String? billNumber;

  //录单日期
  String? billDate;

  //会计期间
  int? period;

  //店铺
  String? otypeId;

  //客户或供应商（网店订单卖给平台的往来单位）
  String? btypeId;

  String? bfullname;

  /// 结算单位全名
  String? balanceFullname;

  //仓库1
  String? ktypeId;

  /// 仓库1全名
  String? kfullname;

  //仓库2（同时涉及出入库才用）
  String? ktypeId2;

  /// 仓库2全名
  String? kfullname2;

  //业务员
  String? etypeId;

  /// 业务员全名
  String? efullname;

  //部门
  String? dtypeId;

  /// 部门全名
  String? dfullname;

  //摘要
  String? summary;

  //备注
  String? memo;

  //创建方式:0=手工录入, 1=导入，2=手工下载，3=自动下载
  int? createType;

  //开票类型
  int? invoiceType;

  //货币币种
  String? currencyId;

  //货币汇率
  num? exchangeRate;

  //费用金额_本币
  num? orderFeeAllotTotal;

  //其他收入金额_本币
  num? orderOtherincomeTotal;

  //优惠金额_本币
  num? preferentialTotal;

  //单据金额_本币
  num? billTotal;

  //费用金额
  num? currencyOrderFeeAllotTotal;

  //优惠金额
  num? currencyOrderPreferentialAllotTotal;

  //单据金额
  num? currencyBillTotal;

  //已结算金额
  num? balanceSettled;

  //单据完成状态（草稿、审核中、已审核、已发货、库存已核算、核算完成、已废弃）
  int? postState;

  //过帐时间
  String? postTime;

  //过帐人
  String? postEtypeId;

  String? postEfullname;

  //录单人
  String? createEtypeId;

  String? createEfullname;

  //创建时间
  String? createTime;

  //修改时间
  String? updateTime;

  //合计数量
  num? countQty;

  /// 之下 为扩展表 acc_bill_assinfo 的字段
  //换货类型
  int? changeType;

  //payment_date
  String? paymentDate;

  //到货日期
  String? arriveDate;

  //单据打印次数
  int? printCount;

  /// 买家Id
  String? buyerId;

  /// 收货人
  String? customerReceiver;

  /// 收货电话
  String? customerReceiverPhone;

  /// 收货手机号
  String? customerReceiverMobile;

  /// 国家
  String? customerReceiverCountry;

  /// 省
  String? customerReceiverProvince;

  /// 市
  String? customerReceiverCity;

  /// 区
  String? customerReceiverDistrict;

  /// 地址
  String? customerReceiverAddress;

  /// 详细地址
  String? customerReceiveFullAddress;

  /// 邮编
  String? customerReceiverZipCode;

  /// 邮箱
  String? customerEmail;

  /// 订金金额
  num? currencyDepositTotal;

  num? ptypeunitQty;

  String? todate;

  int? postAndauditState;

  String? postAndauditStateName;

  Map<String, dynamic> toJson() {
    return {
      'vchtypeName': vchtypeName,
      'vchtypeEnum': vchtypeEnum,
      'ptypeSubQty': ptypeSubQty,
      'ptypeCompletedSubQty': ptypeCompletedSubQty,
      'businessTypeEnum': businessTypeEnum,
      'vchcode': vchcode,
      'profileId': profileId,
      'vchtype': vchtype,
      'processType': processType,
      'deliveryType': deliveryType,
      'accountingType': accountingType,
      'billNumber': billNumber,
      'billDate': billDate,
      'period': period,
      'otypeId': otypeId,
      'btypeId': btypeId,
      'bfullname': bfullname,
      'balanceFullname': balanceFullname,
      'ktypeId': ktypeId,
      'kfullname': kfullname,
      'ktypeId2': ktypeId2,
      'kfullname2': kfullname2,
      'etypeId': etypeId,
      'efullname': efullname,
      'dtypeId': dtypeId,
      'dfullname': dfullname,
      'summary': summary,
      'memo': memo,
      'createType': createType,
      'invoiceType': invoiceType,
      'currencyId': currencyId,
      'exchangeRate': exchangeRate,
      'orderFeeAllotTotal': orderFeeAllotTotal,
      'orderOtherincomeTotal': orderOtherincomeTotal,
      'preferentialTotal': preferentialTotal,
      'billTotal': billTotal,
      'currencyOrderFeeAllotTotal': currencyOrderFeeAllotTotal,
      'currencyOrderPreferentialAllotTotal':
          currencyOrderPreferentialAllotTotal,
      'currencyBillTotal': currencyBillTotal,
      'balanceSettled': balanceSettled,
      'postState': postState,
      'postTime': postTime,
      'postEtypeId': postEtypeId,
      'postEfullname': postEfullname,
      'createEtypeId': createEtypeId,
      'createEfullname': createEfullname,
      'createTime': createTime,
      'updateTime': updateTime,
      'countQty': countQty,
      'changeType': changeType,
      'paymentDate': paymentDate,
      'arriveDate': arriveDate,
      'printCount': printCount,
      'buyerId': buyerId,
      'customerReceiver': customerReceiver,
      'customerReceiverPhone': customerReceiverPhone,
      'customerReceiverMobile': customerReceiverMobile,
      'customerReceiverCountry': customerReceiverCountry,
      'customerReceiverProvince': customerReceiverProvince,
      'customerReceiverCity': customerReceiverCity,
      'customerReceiverDistrict': customerReceiverDistrict,
      'customerReceiverAddress': customerReceiverAddress,
      'customerReceiveFullAddress': customerReceiveFullAddress,
      'customerReceiverZipCode': customerReceiverZipCode,
      'customerEmail': customerEmail,
      'currencyDepositTotal': currencyDepositTotal,
      'ptypeunitQty': ptypeunitQty,
      'todate': todate,
      'postAndauditState': postAndauditState,
      'postAndauditStateName': postAndauditStateName,
    };
  }

  TdBillCoreDTO.fromMap(Map<String, dynamic> map) {
    vchtypeName = map['vchtypeName'];
    vchtypeEnum = map['vchtypeEnum'];
    ptypeSubQty = map['ptypeSubQty'];
    ptypeCompletedSubQty = map['ptypeCompletedSubQty'];
    businessTypeEnum = map['businessTypeEnum'];
    vchcode = map['vchcode'];
    profileId = map['profileId'];
    vchtype = map['vchtype'];
    processType = map['processType'];
    deliveryType = map['deliveryType'];
    accountingType = map['accountingType'];
    billNumber = map['billNumber'];
    billDate = map['billDate'];
    period = map['period'];
    otypeId = map['otypeId'];
    btypeId = map['btypeId'];
    bfullname = map['bfullname'];
    balanceFullname = map['balanceFullname'];
    ktypeId = map['ktypeId'];
    kfullname = map['kfullname'];
    ktypeId2 = map['ktypeId2'];
    kfullname2 = map['kfullname2'];
    etypeId = map['etypeId'];
    efullname = map['efullname'];
    dtypeId = map['dtypeId'];
    dfullname = map['dfullname'];
    summary = map['summary'];
    memo = map['memo'];
    createType = map['createType'];
    invoiceType = map['invoiceType'];
    currencyId = map['currencyId'];
    exchangeRate = map['exchangeRate'];
    orderFeeAllotTotal = map['orderFeeAllotTotal'];
    orderOtherincomeTotal = map['orderOtherincomeTotal'];
    preferentialTotal = map['preferentialTotal'];
    billTotal = map['billTotal'];
    currencyOrderFeeAllotTotal = map['currencyOrderFeeAllotTotal'];
    currencyOrderPreferentialAllotTotal:
    map['currencyOrderPreferentialAllotTotal'];
    currencyBillTotal = map['currencyBillTotal'];
    balanceSettled = map['balanceSettled'];
    postState = map['postState'];
    postTime = map['postTime'];
    postEtypeId = map['postEtypeId'];
    postEfullname = map['postEfullname'];
    createEtypeId = map['createEtypeId'];
    createEfullname = map['createEfullname'];
    createTime = map['createTime'];
    updateTime = map['updateTime'];
    countQty = map['countQty'];
    changeType = map['changeType'];
    paymentDate = map['paymentDate'];
    arriveDate = map['arriveDate'];
    printCount = map['printCount'];
    buyerId = map['buyerId'];
    customerReceiver = map['customerReceiver'];
    customerReceiverPhone = map['customerReceiverPhone'];
    customerReceiverMobile = map['customerReceiverMobile'];
    customerReceiverCountry = map['customerReceiverCountry'];
    customerReceiverProvince = map['customerReceiverProvince'];
    customerReceiverCity = map['customerReceiverCity'];
    customerReceiverDistrict = map['customerReceiverDistrict'];
    customerReceiverAddress = map['customerReceiverAddress'];
    customerReceiveFullAddress = map['customerReceiveFullAddress'];
    customerReceiverZipCode = map['customerReceiverZipCode'];
    customerEmail = map['customerEmail'];
    currencyDepositTotal = map['currencyDepositTotal'];
    ptypeunitQty = map['ptypeunitQty'];
    todate = map['todate'];
    postAndauditState = map['postAndauditState'];
    postAndauditStateName = map['postAndauditStateName'];
  }
}
