///
///@ClassName: bill_ptype_request
///@Description:  单据商品查询实体
///@Author: tanglan
///@Date: 2024/4/23
class BillPtypeRequest {
  String? btypeId;
  String? otypeId;
  String? ktypeId;
  String? etypeId;
  bool searchBySn = true;
  String? filterValue;
  bool fullBarCode = false;
  bool ptypeLimit = true;
  bool stockSelect = false;
  bool searchByPtype = false;
  String? typeId;
  String? skuBarCode;

  static BillPtypeRequest fromMap(Map<String?, dynamic>? map) {
    if (map == null) return BillPtypeRequest();
    BillPtypeRequest feeDto = BillPtypeRequest();
    feeDto.btypeId = map['btypeId'];
    feeDto.otypeId = map['otypeId'];
    feeDto.ktypeId = map['ktypeId'];
    feeDto.etypeId = map['etypeId'];
    feeDto.searchBySn = map['searchBySn'];
    feeDto.filterValue = map['filterValue'];
    feeDto.fullBarCode = map['fullBarCode'];
    feeDto.ptypeLimit = map['ptypeLimit'];
    feeDto.stockSelect = map['stockSelect'];

    feeDto.searchByPtype = map['searchByPtype'];
    feeDto.typeId = map['typeId'];
    feeDto.skuBarCode = map['skuBarCode'];

    return feeDto;
  }

  Map<String?, dynamic> toJson() => {
        "btypeId": btypeId,
        "ktypeId": ktypeId,
        "otypeId": otypeId,
        "etypeId": etypeId,
        "searchBySn": searchBySn,
        "filterValue": filterValue,
        "fullBarCode": fullBarCode,
        "ptypeLimit": ptypeLimit,
        "stockSelect": stockSelect,
        "typeId": typeId,
        "skuBarCode": skuBarCode,
        "searchByPtype": searchByPtype,
      };
}
