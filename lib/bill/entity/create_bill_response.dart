//生单成功返回对象
class CreteBillResponse {
  /// 是否成功
  bool? success;

  /// 消息
  String? message;
  List<OrderToSendResponse>? responses;
  int? createCount;
  int? failCount;
  List<String>? vchcodeList;

  CreteBillResponse({
    this.success,
    this.message,
    this.responses,
    this.createCount,
    this.failCount,
    this.vchcodeList,
  });

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'responses': responses?.map((e) => e.toJson()).toList(),
      'createCount': createCount,
      'failCount': failCount,
      'vchcodeList': vchcodeList,
    };
  }

  CreteBillResponse.fromMap(Map<String, dynamic> map) {
    success = map['success'];
    message = map['message'];
    responses = (map['responses'] as List?)
        ?.map((e) => OrderToSendResponse.fromMap(e))
        .toList();
    createCount = map['createCount'];
    failCount = map['failCount'];
    vchcodeList = (map['vchcodeList'] as List?)?.cast();
  }
}

class OrderToSendResponse {
  String? vchcode;

  String? billNumber;

  String? efullname;

  String? kfullname;

  String? message;

  OrderToSendResponse({
    this.vchcode,
    this.billNumber,
    this.efullname,
    this.kfullname,
    this.message,
  });

  Map<String, dynamic> toJson() {
    return {
      'vchcode': vchcode,
      'billNumber': billNumber,
      'efullname': efullname,
      'kfullname': kfullname,
      'message': message,
    };
  }

  OrderToSendResponse.fromMap(Map<String, dynamic> map) {
    vchcode = map['vchcode'];
    billNumber = map['billNumber'];
    efullname = map['efullname'];
    kfullname = map['kfullname'];
    message = map['message'];
  }
}
