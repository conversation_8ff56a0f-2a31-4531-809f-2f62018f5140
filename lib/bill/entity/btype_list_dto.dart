library bctypelistmodel;

class BtypeListDto {
  int? pageIndex;
  int? pageSize;
  String? total;
  List<BtypeItemDto>? list;

  BtypeListDto({this.pageIndex, this.pageSize, this.total, this.list});

  factory BtypeListDto.fromJson(Map<String, dynamic> parsedJson) {
    var list = parsedJson['list'] as List;
    List<BtypeItemDto> btypeList = list.map((i) => BtypeItemDto.fromMap(i)).toList();

    return BtypeListDto(
        pageIndex: parsedJson['pageIndex'], pageSize: parsedJson['pageSize'], total: parsedJson['total'], list: btypeList);
  }
}

class BtypeItemDto {
  bool? allused;
  int? apWarnup;
  String? arTotal; //应收余额
  String? apTotal; //应付余额
  String? areafullname;
  String? areatypeid;
  String? balancebtypeid;
  String? balancebtypename;
  String? bank;
  String? bankAccount;
  int? bcategory;
  String? bcategoryname = "";
  int? bondedPriceLevel;
  String? btypeArea;
  String? buyDiscount;
  bool? classed;
  String? currencyid;
  bool? deleted;
  String? efullname;
  String? email;
  String? etypeid;
  String? fax;
  String? freightAlias;
  bool? freighted;
  String? fullname;
  String? id;
  int? level;
  int? longitude;
  int? mailPriceLevel;
  String? memo;
  String? namepy;
  String? parfullname;
  String? parid;
  String? paymentType;//账期类型
  String? paymentDays;//账期天数/日期
  String? person;
  String? phone;
  String? picName;
  String? picUrl;
  String? postcode;
  int? priceLevel;
  String? registerAddr;
  String? rowindex;
  String? saleDiscount;
  String? shortname;
  bool? stoped;
  String? tax;
  String? taxNumber;
  String? tel;
  String? typeid;
  String? usercode;

  ///部门信息
  String? dtypeId = ""; //部门id
  String? dtypeName = ""; //部门名称
  ///收货信息
  String? receiverId; //收货人id
  String? receiverPeople = ""; //收货人
  String? receiverTelephone = ""; //电话号码
  String? receiverCellphone = ""; //手机号码
  String? receiverZipcode = ""; //邮编
  String? province = ""; //省
  String? city = ""; //市
  String? district = ""; //区
  String? receiverAddress = ""; //详细地址

  static BtypeItemDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return BtypeItemDto();
    BtypeItemDto testDtoBean = BtypeItemDto();
    testDtoBean.allused = map['allused'];
    testDtoBean.apTotal = map['apTotal'];
    testDtoBean.apWarnup = map['apWarnup'];
    testDtoBean.arTotal = map['arTotal'];
    testDtoBean.areafullname = map['areafullname'];
    testDtoBean.areatypeid = map['areatypeid'];
    testDtoBean.balancebtypeid = map['balancebtypeid'];
    testDtoBean.balancebtypename = map['balancebtypename'];
    testDtoBean.bank = map['bank'];
    testDtoBean.bankAccount = map['bankAccount'];
    testDtoBean.bcategory = map['bcategory'];
    testDtoBean.bcategoryname = bcategoryNameStr[map['bcategory']];
    testDtoBean.bondedPriceLevel = map['bondedPriceLevel'];
    testDtoBean.btypeArea = map['btypeArea'];
    testDtoBean.buyDiscount = map['buyDiscount'];
    testDtoBean.classed = map['classed'];
    testDtoBean.currencyid = map['currencyid'];
    testDtoBean.deleted = map['deleted'];
    testDtoBean.efullname = map['efullname'];
    testDtoBean.email = map['email'];
    testDtoBean.etypeid = map['etypeid'];
    testDtoBean.fax = map['fax'];
    testDtoBean.freightAlias = map['freightAlias'];
    testDtoBean.freighted = map['freighted'];
    testDtoBean.fullname = map['fullname'];
    testDtoBean.id = map['id'];
    testDtoBean.level = map['level'];
    testDtoBean.longitude = map['longitude'];
    testDtoBean.mailPriceLevel = map['mailPriceLevel'];
    testDtoBean.memo = map['memo'];
    testDtoBean.namepy = map['namepy'];
    testDtoBean.parfullname = map['parfullname'];
    testDtoBean.parid = map['parid'];
    testDtoBean.paymentType = map['paymentType'];
    testDtoBean.paymentDays = map['paymentDays'];
    testDtoBean.person = map['person'];
    testDtoBean.phone = map['phone'];
    testDtoBean.picName = map['picName'];
    testDtoBean.picUrl = map['picUrl'];
    testDtoBean.postcode = map['postcode'];
    testDtoBean.priceLevel = map['priceLevel'];
    testDtoBean.registerAddr = map['registerAddr'] ?? "";
    testDtoBean.rowindex = map['rowindex'];
    testDtoBean.saleDiscount = map['saleDiscount'];
    testDtoBean.shortname = map['shortname'];
    testDtoBean.stoped = map['stoped'];
    testDtoBean.tax = map['tax'];
    testDtoBean.taxNumber = map['taxNumber'];
    testDtoBean.tel = map['tel'];
    testDtoBean.typeid = map['typeid'];
    testDtoBean.usercode = map['usercode'];
    return testDtoBean;
  }

  Map toJson() => {
        "allused": allused,
        "apTotal": apTotal,
        "apWarnup": apWarnup,
        "arTotal": arTotal,
        "areafullname": areafullname,
        "areatypeid": areatypeid,
        "balancebtypeid": balancebtypeid,
        "balancebtypename": balancebtypename,
        "bank": bank,
        "bankAccount": bankAccount,
        "bcategory": bcategory,
        "bondedPriceLevel": bondedPriceLevel,
        "btypeArea": btypeArea,
        "buyDiscount": buyDiscount,
        "classed": classed,
        "currencyid": currencyid,
        "deleted": deleted,
        "efullname": efullname,
        "email": email,
        "etypeid": etypeid,
        "fax": fax,
        "freightAlias": freightAlias,
        "freighted": freighted,
        "fullname": fullname,
        "id": id,
        "level": level,
        "longitude": longitude,
        "mailPriceLevel": mailPriceLevel,
        "memo": memo,
        "namepy": namepy,
        "parfullname": parfullname,
        "parid": parid,
        "paymentType":paymentType,
        "paymentDays": paymentDays,
        "person": person,
        "phone": phone,
        "picName": picName,
        "picUrl": picUrl,
        "postcode": postcode,
        "priceLevel": priceLevel,
        "registerAddr": registerAddr,
        "rowindex": rowindex,
        "saleDiscount": saleDiscount,
        "shortname": shortname,
        "stoped": stoped,
        "tax": tax,
        "taxNumber": taxNumber,
        "tel": tel,
        "typeid": typeid,
        "usercode": usercode,
      };

  static const Map<int, String> bcategoryNameStr = {0: "客户", 1: "供应商", 2: "物流公司"};
}
