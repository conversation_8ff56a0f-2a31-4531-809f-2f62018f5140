/// list : [{"address":"string","birthday":"string","classed":true,"createTime":"2020-03-24T01:46:37.010Z","deleted":true,"dtypeId":0,"dtypeName":"string","email":"string","etypeId":0,"fullname":"string","id":0,"loginEndTime":"string","loginPhone":"string","loginStartTime":"string","loginUser":true,"loginUserName":"string","loginxEnddate":"2020-03-24T01:46:37.010Z","loginxFriday":true,"loginxMonday":true,"loginxSaturday":true,"loginxStartdate":"2020-03-24T01:46:37.010Z","loginxSunday":true,"loginxThursday":true,"loginxTuesday":true,"loginxWednesday":true,"memo":"string","mobile":"string","namepy":"string","partypeid":"string","profileId":0,"rowindex":0,"shortname":"string","stoped":true,"sysid":true,"tel":"string","typeid":"string","unstoped":true,"updateTime":"2020-03-24T01:46:37.010Z","usercode":"string"}]
/// pageIndex : 0
/// pageSize : 0
/// total : 0

class EtypeListDto {
  List<EtypeItemDto>? list;
  int? pageIndex;
  int? pageSize;
  String? total;

  static EtypeListDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return EtypeListDto();
    EtypeListDto etypeListDtoBean = EtypeListDto();
    etypeListDtoBean.list =
      (map['list'] as List ?? []).map((o) => EtypeItemDto.fromMap(o))
    .toList();
    etypeListDtoBean.pageIndex = map['pageIndex'];
    etypeListDtoBean.pageSize = map['pageSize'];
    etypeListDtoBean.total = map['total'];
    return etypeListDtoBean;
  }

  Map toJson() => {
    "list": list,
    "pageIndex": pageIndex,
    "pageSize": pageSize,
    "total": total,
  };
}

class EtypeItemDto {
  String?  address;
  String?  birthday;
  bool? classed;
  String?  createTime;
  bool? deleted;
  String?  dtypeId;
  String?  dtypeName;
  String?  email;
  String?  etypeId;
  String?  fullname;
  String?  id;
  String?  loginEndTime;
  String?  loginPhone;
  String?  loginStartTime;
  bool? loginUser;
  String?  loginUserName;
  String?  loginxEnddate;
  bool? loginxFriday;
  bool? loginxMonday;
  bool? loginxSaturday;
  String?  loginxStartdate;
  bool? loginxSunday;
  bool? loginxThursday;
  bool? loginxTuesday;
  bool? loginxWednesday;
  String?  memo;
  String?  mobile;
  String?  namepy;
  String?  partypeid;
  String?  profileId;
  String?  rowindex;
  String?  shortname;
  bool? stoped;
  String? sysid;
  String?  tel;
  String?  typeid;
  bool? unstoped;
  String?  updateTime;
  String?  usercode;

  static EtypeItemDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return EtypeItemDto();
    EtypeItemDto listBean = EtypeItemDto();
    listBean.address = map['address'];
    listBean.birthday = map['birthday'];
    listBean.classed = map['classed'];
    listBean.createTime = map['createTime'];
    listBean.deleted = map['deleted'];
    listBean.dtypeId = map['dtypeId'];
    listBean.dtypeName = map['dtypeName'];
    listBean.email = map['email'];
    listBean.etypeId = map['etypeId'];
    listBean.fullname = map['fullname'];
    listBean.id = map['id'];
    listBean.loginEndTime = map['loginEndTime'];
    listBean.loginPhone = map['loginPhone'];
    listBean.loginStartTime = map['loginStartTime'];
    listBean.loginUser = map['loginUser'];
    listBean.loginUserName = map['loginUserName'];
    listBean.loginxEnddate = map['loginxEnddate'];
    listBean.loginxFriday = map['loginxFriday'];
    listBean.loginxMonday = map['loginxMonday'];
    listBean.loginxSaturday = map['loginxSaturday'];
    listBean.loginxStartdate = map['loginxStartdate'];
    listBean.loginxSunday = map['loginxSunday'];
    listBean.loginxThursday = map['loginxThursday'];
    listBean.loginxTuesday = map['loginxTuesday'];
    listBean.loginxWednesday = map['loginxWednesday'];
    listBean.memo = map['memo'];
    listBean.mobile = map['mobile'];
    listBean.namepy = map['namepy'];
    listBean.partypeid = map['partypeid'];
    listBean.profileId = map['profileId'];
    listBean.rowindex = map['rowindex'];
    listBean.shortname = map['shortname'];
    listBean.stoped = map['stoped'];
    listBean.sysid = map['sysid'];
    listBean.tel = map['tel'];
    listBean.typeid = map['typeid'];
    listBean.unstoped = map['unstoped'];
    listBean.updateTime = map['updateTime'];
    listBean.usercode = map['usercode'];
    return listBean;
  }

  Map toJson() => {
    "address": address,
    "birthday": birthday,
    "classed": classed,
    "createTime": createTime,
    "deleted": deleted,
    "dtypeId": dtypeId,
    "dtypeName": dtypeName,
    "email": email,
    "etypeId": etypeId,
    "fullname": fullname,
    "id": id,
    "loginEndTime": loginEndTime,
    "loginPhone": loginPhone,
    "loginStartTime": loginStartTime,
    "loginUser": loginUser,
    "loginUserName": loginUserName,
    "loginxEnddate": loginxEnddate,
    "loginxFriday": loginxFriday,
    "loginxMonday": loginxMonday,
    "loginxSaturday": loginxSaturday,
    "loginxStartdate": loginxStartdate,
    "loginxSunday": loginxSunday,
    "loginxThursday": loginxThursday,
    "loginxTuesday": loginxTuesday,
    "loginxWednesday": loginxWednesday,
    "memo": memo,
    "mobile": mobile,
    "namepy": namepy,
    "partypeid": partypeid,
    "profileId": profileId,
    "rowindex": rowindex,
    "shortname": shortname,
    "stoped": stoped,
    "sysid": sysid,
    "tel": tel,
    "typeid": typeid,
    "unstoped": unstoped,
    "updateTime": updateTime,
    "usercode": usercode,
  };
}