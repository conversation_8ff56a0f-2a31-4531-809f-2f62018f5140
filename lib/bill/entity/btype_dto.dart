class BtypeDto {
  bool allused = false;//是否可以作为客户/供应商使用和bcategory配合使用
  String arWarnup = "0";//应收信用额度上限
  String areafullname = "";//所属地区名称
  dynamic areatypeid = 0;//所属地区id
  String balancebfullname = "";//往来单位之结算单位名称
  dynamic balancebtypeid = 0;//往来单位之结算单位ID
  String bank = "";//开户银行
  String bankAccount = "";//银行账号.银行账号不能超过25个字符
  dynamic bcategory = 0;//往来单位类型：0客户1供应商 默认客户
  String bcategoryname = "客户";
  String btypeArea = "";//地址
  String buyDiscount = "1";//进货折扣
  dynamic changeDay = 0;//换货天数
  dynamic changeRate = 0;//换货比例
  bool classed = false;//是否分类
  dynamic currencyid = 0;//币种
  bool deleted = false;//是否删除
  String efullname = "";//经手人
  String email = "";//邮箱
  String etypeid = "0";//经手人id
  String fax = "";//传真
  String freightAlias = "";//物流公司别名
  bool freighted = false;//是否为物流公司
  String fullname = "";//全名
  String id ="0";
  dynamic insureFixedValue = 0;//物流公司保价固定金额
  String insureSectionValue = "";//物流公司保价金额区间
  dynamic insureType = 0;//物流公司保价配置类型
  String memo = "";//备注
  String namepy = "";//拼音
  String parfullname = "";
  String parid = "0";
  String? paymentDays;//账期：分为固定账期（比如每月xx号）及账期天数（期限为xx天），空表示不启用账期管理
  String? paymentType;//0 没有账期，1周期天数，2月结日期
  String person = "";//联系人
  String phone = "";//手机
  String postcode = "";//邮编
  int priceLevel=0;//价格等级
  String priceLevelName = "";//价格等级名称
  String registerAddr = "";//公司地址
  dynamic rowindex = 0;//顺序号
  String saleDiscount = "1";//销售折扣
  String shortname = "";//简名
  bool stoped = false;//是否停用
  bool sysrow = false;//是否为系统数据
  dynamic tax = 0;//默认税率(单位%)
  String taxNumber = "";//税号
  String tel = "";//电话
  String usercode = "";//编号 编号不能超过20个字符
  bool warnupEnabled = false;//信用额度管理启用标识

  static BtypeDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return BtypeDto();
    BtypeDto btypeModelBean = BtypeDto();
    btypeModelBean.allused = map['allused'];
    btypeModelBean.arWarnup = map['arWarnup'];
    btypeModelBean.areafullname = map['areafullname'];
    btypeModelBean.areatypeid = map['areatypeid'];
    btypeModelBean.balancebfullname = map['balancebfullname'];
    btypeModelBean.balancebtypeid = map['balancebtypeid'];
    btypeModelBean.bank = map['bank'];
    btypeModelBean.bankAccount = map['bankAccount'];
    btypeModelBean.bcategory = map['bcategory'];
    btypeModelBean.bcategoryname = map["bcategoryname"];
    btypeModelBean.btypeArea = map['btypeArea'];
    btypeModelBean.buyDiscount = map['buyDiscount'];
    btypeModelBean.changeDay = map['changeDay'];
    btypeModelBean.changeRate = map['changeRate'];
    btypeModelBean.classed = map['classed'];
    btypeModelBean.currencyid = map['currencyid'];
    btypeModelBean.deleted = map['deleted'];
    btypeModelBean.efullname = map['efullname'];
    btypeModelBean.email = map['email'];
    btypeModelBean.etypeid = map['etypeid'];
    btypeModelBean.fax = map['fax'];
    btypeModelBean.freightAlias = map['freightAlias'];
    btypeModelBean.freighted = map['freighted'];
    btypeModelBean.fullname = map['fullname'];
    btypeModelBean.id = map['id'];
    btypeModelBean.insureFixedValue = map['insureFixedValue'];
    btypeModelBean.insureSectionValue = map['insureSectionValue'];
    btypeModelBean.insureType = map['insureType'];
    btypeModelBean.memo = map['memo'];
    btypeModelBean.namepy = map['namepy'];
    btypeModelBean.parfullname = map['parfullname'];
    btypeModelBean.parid = map['parid'];
    btypeModelBean.paymentType = map['paymentType'];
    btypeModelBean.paymentDays = map['paymentDays'];
    btypeModelBean.person = map['person'];
    btypeModelBean.phone = map['phone'];
    btypeModelBean.postcode = map['postcode'];
    btypeModelBean.priceLevel = map['priceLevel'];
    btypeModelBean.priceLevelName = map['priceLevelName'];
    btypeModelBean.registerAddr = map['registerAddr'];
    btypeModelBean.rowindex = map['rowindex'];
    btypeModelBean.saleDiscount = map['saleDiscount'];
    btypeModelBean.shortname = map['shortname'];
    btypeModelBean.stoped = map['stoped'];
    btypeModelBean.sysrow = map['sysrow'];
    btypeModelBean.tax = map['tax'];
    btypeModelBean.taxNumber = map['taxNumber'];
    btypeModelBean.tel = map['tel'];
    btypeModelBean.usercode = map['usercode'];
    btypeModelBean.warnupEnabled = map['warnupEnabled'];
    return btypeModelBean;
  }

  Map<String, dynamic> toJson() => {
        "allused": allused,
        "arWarnup": arWarnup,
        "areafullname": areafullname,
        "areatypeid": areatypeid,
        "balancebfullname": balancebfullname,
        "balancebtypeid": balancebtypeid,
        "bank": bank,
        "bankAccount": bankAccount,
        "bcategory": bcategory,
        "bcategoryname": bcategoryname,
        "btypeArea": btypeArea,
        "buyDiscount": buyDiscount,
        "changeDay": changeDay,
        "changeRate": changeRate,
        "classed": classed,
        "currencyid": currencyid,
        "deleted": deleted,
        "efullname": efullname,
        "email": email,
        "etypeid": etypeid,
        "fax": fax,
        "freightAlias": freightAlias,
        "freighted": freighted,
        "fullname": fullname,
        "id": id,
        "insureFixedValue": insureFixedValue,
        "insureSectionValue": insureSectionValue,
        "insureType": insureType,
        "memo": memo,
        "namepy": namepy,
        "parfullname": parfullname,
        "parid": parid,
        "paymentType":paymentType,
        "paymentDays": paymentDays,
        "person": person,
        "phone": phone,
        "postcode": postcode,
        "priceLevel": priceLevel,
        "priceLevelName": priceLevelName,
        "registerAddr": registerAddr,
        "rowindex": rowindex,
        "saleDiscount": saleDiscount,
        "shortname": shortname,
        "stoped": stoped,
        "sysrow": sysrow,
        "tax": tax,
        "taxNumber": taxNumber,
        "tel": tel,
        "usercode": usercode,
        "warnupEnabled": warnupEnabled,
      };
}
