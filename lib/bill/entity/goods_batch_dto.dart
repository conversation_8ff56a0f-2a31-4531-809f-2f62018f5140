/// batchId : "批次id"
/// comboRowId : null
/// ktypeId : "614502464955146240"
/// ptypeId : "624848790163087512"
/// skuId : "624878502746841240"
/// batchNo : "批次号"
/// produceDate : "2021-09-29T16:00:00Z"
/// expireDate : "2021-10-19T16:00:00Z"
/// stockQty : 10
/// unitQty : null
/// stockPrice : 3.6
/// stockTotal : 36
/// protectDays : 20
/// batchTime : "2021-09-30T06:57:25Z"
/// costId : "625016989672509092"

///商品批次号实体类
class GoodsBatchDto {
  GoodsBatchDto({
    this.batchId,
    this.comboRowId,
    this.ktypeId,
    this.ptypeId,
    this.skuId,
    this.batchNo,
    this.produceDate,
    this.expireDate,
    this.stockQty,
    this.inventoryQty,
    this.batchSendQty,
    this.unitQty,
    this.stockPrice,
    this.stockTotal,
    this.protectDays,
    this.batchTime,
    this.costId,
    this.batchPrice,
  });

   GoodsBatchDto.fromJson(dynamic json) {
    batchId = json['batchId'];
    comboRowId = json['comboRowId'];
    ktypeId = json['ktypeId'];
    ptypeId = json['ptypeId'];
    skuId = json['skuId'];
    batchNo = json['batchNo'];
    produceDate = json['produceDate'];
    expireDate = json['expireDate'];
    stockQty = json['stockQty'];
    inventoryQty = json['inventoryQty'];
    batchSendQty = json['batchSendQty'];
    unitQty = json['unitQty'];
    stockPrice = json['stockPrice'];
    stockTotal = json['stockTotal'];
    protectDays = json['protectDays'];
    batchTime = json['batchTime'];
    costId = json['costId'];
    batchPrice = json['batchPrice'];
  }

  ///批次号id
  String? batchId;
  ///套餐？
  String? comboRowId;
  ///仓库id
  String? ktypeId;
  ///商品id
  String? ptypeId;
  ///商品skuId
  String? skuId;
  ///批次号
  String? batchNo;
  ///生产日期
  String? produceDate;
  ///过期日期
  String? expireDate;
  ///批次库存
  num? stockQty;
  ///可发货库存
  num? batchSendQty;
  /// 实物库存
  num? inventoryQty;
  /// 单位数量
  num? unitQty;
  /// 批次成本(单价)
  double? batchPrice;
  double? stockPrice;
  String? stockTotal;
  int? protectDays;
  String? batchTime;
  String? costId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['batchId'] = batchId;
    map['comboRowId'] = comboRowId;
    map['ktypeId'] = ktypeId;
    map['ptypeId'] = ptypeId;
    map['skuId'] = skuId;
    map['batchNo'] = batchNo;
    map['produceDate'] = produceDate;
    map['expireDate'] = expireDate;
    map['stockQty'] = stockQty;
    map['batchSendQty'] = batchSendQty;
    map['inventoryQty'] = inventoryQty;
    map['unitQty'] = unitQty;
    map['stockPrice'] = stockPrice;
    map['stockTotal'] = stockTotal;
    map['protectDays'] = protectDays;
    map['batchTime'] = batchTime;
    map['costId'] = costId;
    map['batchPrice'] = batchPrice;
    return map;
  }
}
