/// auditEnable : false
/// submitAuditEnabled : false
/// auditPass : false
/// exsitChildBills : {}
/// overState : "UNOVER"
/// order : false

class AuditEnableResponse {
  bool? auditEnable;
  bool? submitAuditEnabled;
  int? auditPass; //0-未提交审核  1-未审核  2-审核中  3-审核不通过  4 -审核通过
  Map<String, dynamic>? exsitChildBills;
  String? overState;
  bool? order;

  static AuditEnableResponse fromMap(Map<String, dynamic>? map) {
    if (map == null) return AuditEnableResponse();
    AuditEnableResponse auditEnableResponseBean = AuditEnableResponse();
    auditEnableResponseBean.auditEnable = map['auditEnable'];
    auditEnableResponseBean.submitAuditEnabled = map['submitAuditEnabled'];
    auditEnableResponseBean.auditPass = map['auditPass'];
    auditEnableResponseBean.exsitChildBills = map['exsitChildBills'];
    auditEnableResponseBean.overState = map['overState'];
    auditEnableResponseBean.order = map['order'];
    return auditEnableResponseBean;
  }

  Map toJson() => {
        "auditEnable": auditEnable,
        "submitAuditEnabled": submitAuditEnabled,
        "auditPass": auditPass,
        "exsitChildBills": exsitChildBills,
        "overState": overState,
        "order": order,
      };
}
