
import 'audit_enable_response.dart';

/// auditWaitForMe : false
/// auditCompletedForMe : false
/// flowId : "450279890764288000"
/// bill : {"auditEnable":false,"submitAuditEnabled":false,"auditPass":false,"exsitChildBills":{},"overState":"UNOVER","order":false}

class BillAuditInfoDTO {
  bool? auditWaitForMe;
  bool? auditCompletedForMe;
  String? flowId;
  AuditEnableResponse? bill;

  static BillAuditInfoDTO fromMap(Map<String, dynamic>? map) {
    if (map == null) return BillAuditInfoDTO();
    BillAuditInfoDTO billAuditInfoDtoBean = BillAuditInfoDTO();
    billAuditInfoDtoBean.auditWaitForMe = map['auditWaitForMe'];
    billAuditInfoDtoBean.auditCompletedForMe = map['auditCompletedForMe'];
    billAuditInfoDtoBean.flowId = map['flowId'];
    billAuditInfoDtoBean.bill = AuditEnableResponse.fromMap(Map<String, dynamic>.from(map['bill'] ?? {}));
    return billAuditInfoDtoBean;
  }

  Map toJson() => {
        "auditWaitForMe": auditWaitForMe,
        "auditCompletedForMe": auditCompletedForMe,
        "flowId": flowId,
        "bill": bill?.toJson(),
      };
}
