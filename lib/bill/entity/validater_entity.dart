/**     
  * @ClassName:      validater_entity.dart
   * @CreateDate:    2020/3/30 15:08
  * @Author:         tlan
  * @Description:  验证对象
*/
class ValidaterEntity {
  String? order;
  ValidaterBean? validater;
  String? validationType;

  static ValidaterEntity fromMap(Map<String, dynamic>? map) {
    if (map == null) return ValidaterEntity();
    ValidaterEntity validaterEntity = ValidaterEntity();
    validaterEntity.order = map['order'];
    validaterEntity.validater = ValidaterBean.fromMap(map['validater']);
    validaterEntity.validationType = map['validationType'];
    return validaterEntity;
  }

  Map toJson() => {
    "order": order,
    "validater": validater,
    "validationType": validationType,
  };
}

class ValidaterBean {
  static ValidaterBean fromMap(Map<String, dynamic> map) {
    ValidaterBean validaterBean = ValidaterBean();
    return validaterBean;
  }

  Map toJson() => {};
}