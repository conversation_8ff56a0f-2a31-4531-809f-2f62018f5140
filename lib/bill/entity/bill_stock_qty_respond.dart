class BillStockQtyRespond {
  BillStockQtyRespond({
      this.rowIndex, 
      this.ptypeId, 
      this.stockQty, 
      this.inventoryQty, 
      this.sendQty,
      this.stockSubQty, 
      this.unitId, 
      this.skuId, 
      this.ktypeId, 
      this.price, 
      this.discount, 
      this.errorMsg, 
      this.skuPrice, 
      this.bUserCode, 
      this.comboRowId, 
      this.costId, 
      this.propenabled, 
      this.finalTotal, 
      this.uniquePrimaryKey,});

  BillStockQtyRespond.fromJson(dynamic json) {
    rowIndex = json['rowIndex'];
    ptypeId = json['ptypeId'];
    stockQty = json['stockQty'];
    inventoryQty = json['inventoryQty'];
    sendQty = json['sendQty'];
    stockSubQty = json['stockSubQty'];
    unitId = json['unitId'];
    skuId = json['skuId'];
    ktypeId = json['ktypeId'];
    price = json['price'];
    discount = json['discount'];
    errorMsg = json['errorMsg'];
    skuPrice = json['skuPrice'];
    bUserCode = json['bUserCode'];
    comboRowId = json['comboRowId'];
    costId = json['costId'];
    propenabled = json['propenabled'];
    finalTotal = json['finalTotal'];
    uniquePrimaryKey = json['uniquePrimaryKey'];
  }
  int? rowIndex;
  String? ptypeId;
  num? stockQty;
  num? inventoryQty;
  num? sendQty;
  num? stockSubQty;
  String? unitId;
  String? skuId;
  String? ktypeId;
  num? price;
  num? discount;
  dynamic errorMsg;
  dynamic skuPrice;
  dynamic bUserCode;
  dynamic comboRowId;
  String? costId;
  bool? propenabled;
  num? finalTotal;
  dynamic uniquePrimaryKey;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['rowIndex'] = rowIndex;
    map['ptypeId'] = ptypeId;
    map['stockQty'] = stockQty;
    map['inventoryQty'] = inventoryQty;
    map['sendQty'] = sendQty;
    map['stockSubQty'] = stockSubQty;
    map['unitId'] = unitId;
    map['skuId'] = skuId;
    map['ktypeId'] = ktypeId;
    map['price'] = price;
    map['discount'] = discount;
    map['errorMsg'] = errorMsg;
    map['skuPrice'] = skuPrice;
    map['bUserCode'] = bUserCode;
    map['comboRowId'] = comboRowId;
    map['costId'] = costId;
    map['propenabled'] = propenabled;
    map['finalTotal'] = finalTotal;
    map['uniquePrimaryKey'] = uniquePrimaryKey;
    return map;
  }

}