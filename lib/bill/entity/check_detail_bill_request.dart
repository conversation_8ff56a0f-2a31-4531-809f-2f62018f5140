import '../../bill/entity/base_request_dto.dart';

///校验订单是否满足生单条件(明细生单) 请求实体类
class CheckDetailBillRequest extends BaseRequestDto {
  List<String>? detailIdList;

  List<SelectOrderBillDTO>? newDetailList;

  String? createVchtype;

  int? createType;

  bool? batch = false;

  List<String>? vchcodeList;

  String? fromVchtype;

  int? customType = 0;

  CheckDetailBillRequest();

  @override
  Map<String, dynamic> toJson() {
    return super.toJson()
      ..addAll({
        'detailIdList': detailIdList,
        'newDetailList': newDetailList?.map((e) => e.toJson()).toList(),
        'createVchtype': createVchtype,
        'createType': createType,
        'batch': batch,
        'vchcodeList': vchcodeList,
        'fromVchtype': fromVchtype,
        'customType': customType,
      });
  }

  CheckDetailBillRequest.fromMap(Map<String, dynamic> map)
      : super.fromMap(map) {
    detailIdList = (map['detailIdList'] as List?)?.cast();
    newDetailList = (map['newDetailList'] as List?)
        ?.map((e) => SelectOrderBillDTO.fromMap(e))
        .toList();
    createVchtype = map['createVchtype'];
    createType = map['createType'];
    batch = map['batch'];
    vchcodeList = (map['vchcodeList'] as List?)?.cast();
    fromVchtype = map['fromVchtype'];
    customType = map['customType'];
  }
}

class SelectOrderBillDTO {
  String? detailId;

  num? selectQty;

  num? selectSubQty;

  Map<String, dynamic> toJson() {
    return {
      'detailId': detailId,
      'selectQty': selectQty,
      'selectSubQty': selectSubQty,
    };
  }

  SelectOrderBillDTO.fromMap(Map<String, dynamic> map) {
    detailId = map['detailId'];
    selectQty = map['selectQty'];
    selectSubQty = map['selectSubQty'];
  }

  SelectOrderBillDTO({
    this.detailId,
    this.selectQty,
    this.selectSubQty,
  });
}
