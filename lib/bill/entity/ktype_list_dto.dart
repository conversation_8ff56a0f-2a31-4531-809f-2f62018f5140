class KtypeListDto {
  List<KtypeItemDto>? list;
  int? pageIndex;
  int? pageSize;
  String? total;

  static KtypeListDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return KtypeListDto();
    KtypeListDto ktypeListDtoBean = KtypeListDto();
    ktypeListDtoBean.list = (map['list'] as List ?? [])
        .map((o) => KtypeItemDto.fromMap(o))
        .toList();
    ktypeListDtoBean.pageIndex = map['pageIndex'];
    ktypeListDtoBean.pageSize = map['pageSize'];
    ktypeListDtoBean.total = map['total'];
    return ktypeListDtoBean;
  }

  Map toJson() => {
    "list": list,
    "pageIndex": pageIndex,
    "pageSize": pageSize,
    "total": total,
  };
}

class KtypeItemDto {
  String? address;
  int? bondedType;
  String? cellphone;
  String? city;
  String? cityId;
  bool? classed;
  String? createTime;
  int? customsType;
  bool? deleted;
  String? district;
  String? districtId;
  String? fullname;
  String? id;
  String? memo;
  String? namepy;
  String? partypeid;
  String?  people;
  String?  profileId;
  String?  province;
  String?  provinceId;
  String?  rowindex;
  int? scategory;
  String?  sections;
  String?  shortname;
  bool? stoped;
  bool? sysrow;
  String?  typeid;
  String?  updateTime;
  String?  usercode;
  String?  zipcode;

  static KtypeItemDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return KtypeItemDto();
    KtypeItemDto listBean = KtypeItemDto();
    listBean.address = map['address'];
    listBean.bondedType = map['bondedType'];
    listBean.cellphone = map['cellphone'];
    listBean.city = map['city'];
    listBean.cityId = map['cityId'];
    listBean.classed = map['classed'];
    listBean.createTime = map['createTime'];
    listBean.customsType = map['customsType'];
    listBean.deleted = map['deleted'];
    listBean.district = map['district'];
    listBean.districtId = map['districtId'];
    listBean.fullname = map['fullname'];
    listBean.id = map['id'];
    listBean.memo = map['memo'];
    listBean.namepy = map['namepy'];
    listBean.partypeid = map['partypeid'];
    listBean.people = map['people'];
    listBean.profileId = map['profileId'];
    listBean.province = map['province'];
    listBean.provinceId = map['provinceId'];
    listBean.rowindex = map['rowindex'];
    listBean.scategory = map['scategory'];
    listBean.sections = map['sections'];
    listBean.shortname = map['shortname'];
    listBean.stoped = map['stoped'];
    listBean.sysrow = map['sysrow'];
    listBean.typeid = map['typeid'];
    listBean.updateTime = map['updateTime'];
    listBean.usercode = map['usercode'];
    listBean.zipcode = map['zipcode'];
    return listBean;
  }

  Map toJson() => {
    "address": address,
    "bondedType": bondedType,
    "cellphone": cellphone,
    "city": city,
    "cityId": cityId,
    "classed": classed,
    "createTime": createTime,
    "customsType": customsType,
    "deleted": deleted,
    "district": district,
    "districtId": districtId,
    "fullname": fullname,
    "id": id,
    "memo": memo,
    "namepy": namepy,
    "partypeid": partypeid,
    "people": people,
    "profileId": profileId,
    "province": province,
    "provinceId": provinceId,
    "rowindex": rowindex,
    "scategory": scategory,
    "sections": sections,
    "shortname": shortname,
    "stoped": stoped,
    "sysrow": sysrow,
    "typeid": typeid,
    "updateTime": updateTime,
    "usercode": usercode,
    "zipcode": zipcode,
  };
}