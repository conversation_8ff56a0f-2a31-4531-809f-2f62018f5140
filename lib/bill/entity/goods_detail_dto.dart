import 'dart:convert';

import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../tool/decimal_display_helper.dart';
import '../tool/promotion/promotion.dart';
import 'preferential_dto.dart';
import 'ptype/ptype_dto.dart';
import 'ptype/ptype_fullbarcode_dto.dart';
import 'ptype/ptype_prop_dto.dart';
import 'ptype/ptype_serial_no_dto.dart';
import '../../enum/bill_type.dart';
import '../../enum/discount_source_type.dart';

import 'goods_detail_sale_back_helper.dart';

class GoodsDetailDto implements PromotionGift {
  String? saleOtypeVipPrice;

  bool get inDetailSelect => unitQty != 0;

  //region pos前端呈现（需将赠金还原到对应数据上）

  ///折后含税单价 ,包含储值赠金
  num posCurrencyDisedTaxedPrice = 0;

  ///折后含税金额 ,包含储值赠金
  num posCurrencyDisedTaxedTotal = 0;

  // ///不包含的优惠（折扣优惠+分摊优惠）仅供pos前端展示计算
  // num posPreferentialTotalExcludeGiveTotal = 0;

  ///储值赠金
  num? posGiftPrice;

  ///折后单价
  num discountPrice = 0;

  ///折后金额
  num discountTotal = 0;

  ///类型 ：区分优惠券或是商品
  num ptypeGroup = 1;

  ///赠品优惠券名称 todo 待拆分
  String? valueTypeName;

  ///赠品优惠券模板id todo 待拆分
  String pid = "";

  ///退货帮助类
  GoodsSaleBackHelper saleBackHelper = GoodsSaleBackHelper();

  ///优惠辅助表
  ///key PreferentialDtoType.String
  Map<String, PreferentialDto> preferentialHelp = {};

  ///手工改价
  bool manualPrice = false;

  ///折扣来源类型
  ///0=系统默认 1=会员价 2=促销 3=指定折扣(手工改价)
  int discountSourceType = 0;

  ///是否是手工修改折扣，若为false=》手工修改单价
  ///本地变量
  bool isManualDiscount = true;

  ///促销是否有效
  bool promotionValid = true;

  ///商品特价是否累计积分
  bool calIntegral = true;

  ///商品促销是否参与订单促销
  ///默认所有都参加，包含普通商品，添加促销时不参加的false
  bool joinOrder = true;

  ///商品促销是否参与会员权益
  ///默认所有都参加，包含普通商品，添加促销时不参加的false
  bool vipRights = true;

  String? promotionParentSkuid;
  String? promotionParentUnitId;
  String? promotionParentPtypeId;

  ///是否是单品
  bool promotionGiftSingle = false;

  ///用于ui 收起或展开
  bool visible = false;

  String? picUrl;
  String? pFullName;
  String fullbarcode = "";

  // num inOutQty;

  ///该商品是否开启了序列号
  ///0=>未开启,1=>严格序列号，2=>宽松序列号
  int snenabled = 0;
  String ptypetype = "";
  String? pUserCode;
  String standard = "";
  String ptypeId = "";

  ///商品分类ID (层级ID)
  String? typeid;

  ///商品标签ID列表
  List<String>? labelIds;

  num? stockQty;

  ///批次号
  String batchNo = "";

  ///批次id
  String? batchId;

  ///批次个别计价id
  String? costId;

  ///批次成本（单价）
  num batchPrice = 0;
  bool batchenabled = false;
  String? brandName;
  num? ptypeLength;
  num? ptypeWidth;
  num? ptypeHeight;
  num? weight;
  int? lengthUnit;
  dynamic weightUnit;
  String? buyPrice;
  String? comboDetailId;
  bool comboRow = false;
  String comboRowId = "0";
  String comboRowParId = "0";

  ///完成数量(基本单位)
  num? completedQty;

  ///完成数量（副单位）
  num? completedSubQty;

  ///完成数量（单位）
  num? unitCompletedQty;
  num? completedTotal;
  num? needPrintName;
  num costPrice = 0; //
  num? costTotal;

  ///折后单价(最终价格) 不含税
  num currencyDisedPrice = 0;

  ///折后金额 不含税
  num currencyDisedTotal = 0;

  ///折后含税单价(最终价格，不含赠金,含税)
  num currencyDisedTaxedPrice = 0;

  ///折后含税总额（最终金额，不含赠金，含税）
  ///即 优惠前优惠+优惠分摊+赠金优惠 的最终金额
  num currencyDisedTaxedTotal = 0;

  num? currencyOrderFeeAllotTotal; //单据价格调整（费用金额）
  ///当为1时，为个别计价批次商品
  int costMode = 0;

  ///优惠分摊 (总) ：等于所有优惠分摊相加
  num currencyOrderPreferentialAllotTotal = 0;

  ///最终优惠
  /// = [preferentialDiscount]优惠前优惠 + [currencyOrderPreferentialAllotTotal]优惠分摊总额+赠金优惠
  num currencyPreferentialTotal = 0;

  ///优惠前优惠，即优惠分摊前的优惠金额
  ///包含:
  ///1.会员价/权益卡/折扣券 打折
  ///2.促销 商品优惠
  num preferentialDiscount = 0;

  ///单品优惠金额，即促销优惠金额
  num currencyPtypePreferentialTotal = 0;

  ///价格,零售价,单价
  num currencyPrice = 0;
  num currencyTaxTotal = 0;
  num currencyTotal = 0;
  String comboId = "0";

  ///明细行id，调拨单用
  String? detailId;

  ///折扣
  num discount = 1;

  ///促销后单价（优惠分摊前单价）
  num promotedPrice = 0;

  ///促销后总金额（优惠分摊前金额）
  num promotedTotal = 0;

  num? orgDiscount; //优惠分摊前的折扣

  ///最终折扣
  num lastDiscount = 1;
  String? expireDate;
  int? forecastCostPrice;
  int? forecastCostTotal;
  bool gift = false;
  dynamic inoutType;
  String? kfullname;
  String? ktypeId;
  String memo = "";
  num? overQty;
  num? overTotal;

  ///0->实物商品
  ///1->虚拟商品
  ///2->套餐
  num pcategory = 0;
  String? position;
  String? produceDate;
  String? profileId;
  List<PtypePropDto>? prop;
  String? propFormat;
  String? propValues;
  bool propenabled = false;
  num? protectDays;
  dynamic comboQtyRate; //套餐商品时用作商品的单位数量
  //序列号
  List<PtypeSerialNoDto> serialNoList = [];
  String? skuId;
  String? skuName;
  String? snnoStrButton;
  String? sourceVchcode;
  dynamic sourceVchtype;
  String? sourceBfullname;
  String? sourceBillDate;
  String? sourceBillNumber;
  String? sourceBtypeId;
  String? sourceDetailId;
  int? sourceDetailPeriod;
  String? sourceDfullname;
  String? sourceDtypeId;
  String? sourceEfullname;
  String? sourceEtypeId;
  String? sourceKfullname;
  String? sourceKtypeId;
  String? sourceOfullname;
  String? sourceOtypeId;
  dynamic sourceBusinessType;

  bool hasSource = false;

  ///税率
  num taxRate = 0;
  num? unOverQty;
  num? unOverTotal;
  String unitId = "";
  int? unitCode;
  String unitName = "";

  ///当前可退最大数量
  num? maxQty;

  //region  原销售单信息（用于前端计算退货）
  ///原单商品最大数量
  num originalMaxQty = 0;

  //endregion
  //region  已退货信息（用于前端计算退货）
  ///已退回数量（基本单位）
  num refundQty = 0;

  ///已退回副单位数量
  num? refundSubQty;

  //endregion

  ///当前单位数量
  num unitQty = 0;

  ///基本单位数量
  num? qty;

  ///副单位数量
  num? subQty;

  ///原单基本单位数量
  num? requestQty;

  ///原单副单位数量
  num? requestSubQty;

  ///原单当前单位数量
  num? requestUnitQty;

  ///退回数量
  num? requestBackQty;

  ///当前单位和基本单位之间的倍数
  num? unitRate;
  String? vchcode;
  String? vchtype;
  String? xcode;
  int? skuPrice; //是否启用sku计价
  //库存数量

  ///零售类单据默认单位

  String? retailDefaultUnit;

  ///销售类单据默认单位

  String? saleDefaultUnit;

  /// 进货类单据默认单位

  String? buyDefaultUnit;

  ///库存类单据默认单位

  String? stockDefaultUnit;

  /// 保质期(天)
  int? expireDay;

  List<UnitsBean>? unitList;

  ///临时变量 用于存储订单/退货单生单的剩余数量及剩余优惠分摊通过接口 getDetailComplete 获取
  String? remainUnitQty;

  ///临时变量  剩余优惠分摊
  String? remainCurrencyPreferentialShare;

  ///临时变量  原始的单位ID  由于订单或退货生成单据时的剩余优惠分摊
  String? orgUnitId;

  ///临时变量 缓存lastPropValueId
  String? lastPropValueId;

  ///商品条码信息
  List<PtypeFullbarcodeDto>? fullbarcodes;

  ///是否展开套餐
  bool isExpandCombo = false;

  ///套餐明细价格分摊比例
  num comboShareScale = 0;

  ///套餐行明细行
  List<GoodsDetailDto?> comboDetailList = [];
  bool isShowCombo = false;

  ///临时变量，批次实物库存
  ///用于套餐/多个商品，批次选择弹窗中，批次库存的展示
  num? batchInventoryQty;

  ///促销id
  @override
  String promotionId = "0";

  ///促销类型
  @override
  int promotionType = -1;

  ///促销提示
  List<PromotionHints>? promotionHints;

  ///促销赠品数量
  @override
  set promotionGiftQty(num qty) => unitQty = qty;

  ///促销赠品数量
  @override
  num get promotionGiftQty => unitQty;

  ///促销赠品名称
  @override
  String get promotionGiftName => pFullName ?? "";

  ///是否是促销中的赠品
  ///例如满减增、满额赠、以及优惠券中的赠品等等
  ///需要注意，对于第二件半价的半价的那个商品，也算为促销赠品
  bool promotionGift = false;

  ///促销商品赠品的范围(类型)，分为买赠同品、选择赠品、指定赠品
  @override
  int promotionGiftScope = PromotionGiftScope.chooseGoods.value;

  ///促销赠品类型
  @override
  final int promotionGiftType = PromotionGiftType.goods.value;

  ///赠金优惠金额（外币）
  num currencyGivePreferentialTotal = 0;

  /// 赠金优惠金额（本币）
  num givePreferentialTotal = 0;

  ///此明细行是否赠送积分
  bool giveVipScore = false;

  GoodsDetailDto.buildFromMap(Map<dynamic, dynamic> map) {
    saleOtypeVipPrice = map["saleOtypeVipPrice"];
    promotionParentUnitId = map["promotionParentUnitId"];
    promotionParentSkuid = map["promotionParentSkuid"];
    promotionParentPtypeId = map["promotionParentPtypeId"];
    posCurrencyDisedTaxedPrice = map["posCurrencyDisedTaxedPrice"] ?? 0;
    posCurrencyDisedTaxedTotal = map["posCurrencyDisedTaxedTotal"] ?? 0;
    // posPreferentialTotalExcludeGiveTotal =
    //     map["posPreferentialTotalExcludeGiveTotal"] ?? 0;
    originalMaxQty = map["originalMaxQty"] ?? 0;
    pid = map["pid"] ?? "";
    ptypeGroup = map["ptypeGroup"] ?? 1;
    maxQty = map["maxQty"] ?? 0.0;
    saleBackHelper = GoodsSaleBackHelper.fromMap(map["saleBackHelper"]);
    vipRights = map["vipRights"] ?? true;
    manualPrice = map["manualPrice"] ?? false;
    discountSourceType = DiscountSourceTypeUtil.fromStringToInt(
      map["discountSourceType"],
    );
    isManualDiscount = map["isManualDiscount"] ?? true;
    calIntegral = map["calIntegral"] ?? true;
    joinOrder = map["joinOrder"] ?? true;
    batchId = map["batchId"];
    costId = map["costId"];
    posGiftPrice = map["posGiftPrice"] ?? 0;
    promotionType = map["promotionType"] ?? -1;
    promotionGiftSingle = map["promotionGiftSingle"] ?? false;
    visible = map["visible"] ?? false;
    lastDiscount = map["lastDiscount"] ?? 1;
    promotionId = map["promotionId"] ?? "0";
    preferentialDiscount = map["preferentialDiscount"] ?? 0;
    currencyPtypePreferentialTotal = map["currencyPtypePreferentialTotal"] ?? 0;
    stockQty = map['stockQty'];
    batchNo = map['batchNo'] ?? "";
    batchPrice =
        map['batchPrice'] is int
            ? double.parse(map['batchPrice'].toString())
            : map['batchPrice'] ?? 0;
    batchenabled = map['batchenabled'] ?? false;
    brandName = map['brandName'] ?? "";
    ptypeLength = map['ptypeLength'];
    ptypeWidth = map['ptypeWidth'];
    ptypeHeight = map['ptypeHeight'];
    weight = map['weight'];
    lengthUnit = map['lengthUnit'];
    weightUnit = map['weightUnit'];
    buyPrice = map['buyPrice'];
    comboDetailId = map['comboDetailId'];
    comboId = map['comboId'] ?? "0";
    comboRow = map['comboRow'] ?? false;
    comboRowId = map['comboRowId'] ?? "";
    comboRowParId = map['comboRowParId'] ?? "";
    completedQty = map['completedQty'] ?? 0.0;
    completedSubQty = map['completedSubQty'];
    unitCompletedQty = map['unitCompletedQty'];
    completedTotal = map['completedTotal'];
    needPrintName = map['needPrintName'];
    costPrice = map['costPrice'] ?? 0;
    costTotal = map['costTotal'];
    currencyDisedPrice = map['currencyDisedPrice'] ?? 0;
    discountPrice = map['discountPrice'] ?? 0;
    discountTotal = map['discountTotal'] ?? 0;
    currencyDisedTaxedPrice = map['currencyDisedTaxedPrice'] ?? 0;
    currencyDisedTaxedTotal = map['currencyDisedTaxedTotal'] ?? 0;
    currencyDisedTotal = map['currencyDisedTotal'] ?? 0;
    currencyOrderFeeAllotTotal = map[' currencyOrderFeeAllotTotal'];
    currencyOrderPreferentialAllotTotal =
        map['currencyOrderPreferentialAllotTotal'] ?? 0;
    currencyPreferentialTotal = map['currencyPreferentialTotal'] ?? 0;
    currencyPrice = map['currencyPrice'] ?? 0;
    currencyTaxTotal = map['currencyTaxTotal'] ?? 0;
    currencyTotal = map['currencyTotal'] ?? 0;
    detailId = map['detailId'];
    discount = map['discount'] ?? 0;
    promotedPrice = map['promotedPrice'] ?? 0;
    promotedTotal = map['promotedTotal'] ?? 0;
    orgDiscount = map['orgDiscount'] ?? 0;
    expireDate = map['expireDate'];
    forecastCostPrice = map['forecastCostPrice'];
    forecastCostTotal = map['forecastCostTotal'];
    gift = map['gift'];
    inoutType = map['inoutType'];
    kfullname = map['kfullname'];
    ktypeId = map['ktypeId'];
    memo = map['memo'] ?? "";
    valueTypeName = map["valueTypeName"];
    overQty = map['overQty'];
    overTotal = map['overTotal'];
    fullbarcode = map['fullbarcode'] ?? "";
    pFullName = map['pFullName'];
    pUserCode = map['pUserCode'];
    pcategory = map['pcategory'] ?? 0;
    picUrl = map['picUrl'];
    position = map['position'];
    produceDate = map['produceDate'];
    profileId = map['profileId'];
    costMode = map['costMode'] ?? 0;
    prop =
        map['prop'] == null
            ? null
            : [
              ...((map['prop'] ?? []) as List).map((o) {
                if (o is PtypePropDto) {
                  return o;
                }
                return PtypePropDto.fromMap(o);
              }),
            ];
    propFormat = map['propFormat'];
    propValues = map['propValues'];
    propenabled = map['propenabled'] ?? false;
    protectDays = map['protectDays'];
    ptypeId = map['ptypeId'];
    ptypetype = map['ptypetype'] ?? (map['ptypeType'] ?? "");
    typeid = map['typeId'];
    labelIds = (map['labelIds'] as List?)?.cast<String>();
    comboQtyRate = map['comboQtyRate'];
    serialNoList = [
      ...((map['serialNoList'] ?? []) as List).map((o) {
        if (o is PtypeSerialNoDto) {
          return o;
        }
        return PtypeSerialNoDto.fromMap(o);
      }),
    ];
    skuId = map['skuId'] ?? (comboRow ? "0" : null);
    skuName = map['skuName'];
    snenabled = map['snenabled'] ?? 0;
    snnoStrButton = map['snnoStrButton'];
    hasSource = map['hasSource'] ?? false;
    sourceDetailId = map['sourceDetailId'] ?? "";
    sourceVchcode = map['sourceVchcode'];
    sourceVchtype = map['sourceVchtype'];
    sourceBfullname = map['sourceBfullname'];
    sourceBillDate = map['sourceBillDate'];
    sourceBillNumber = map['sourceBillNumber'];
    sourceBtypeId = map['sourceBtypeId'];
    sourceDetailPeriod = map['sourceDetailPeriod'];
    sourceDfullname = map['sourceDfullname'];
    sourceDtypeId = map['sourceDtypeId'];
    sourceEfullname = map['sourceEfullname'];
    sourceEtypeId = map['sourceEtypeId'];
    sourceKfullname = map['sourceKfullname'];
    sourceKtypeId = map['sourceKtypeId'];
    sourceOfullname = map['sourceOfullname'];
    sourceOtypeId = map['sourceOtypeId'];
    sourceBusinessType = map['sourceBusinessType'];
    standard = map['standard'] ?? "";
    taxRate = map['taxRate'] ?? 0;
    unOverQty = map['unOverQty'];
    unOverTotal = map['unOverTotal'];
    unitId = map['unitId'];
    unitCode = map['unitCode'];
    unitName = map['unitName'] ?? "";
    unitQty = map['unitQty'] ?? 0;
    subQty = map['subQty'];
    qty = map['qty'];
    requestQty = map['requestQty'];
    requestUnitQty = map['requestUnitQty'];
    requestSubQty = map['requestSubQty'];
    requestBackQty = map['requestBackQty'];
    refundQty = map["refundQty"] ?? 0;

    refundSubQty = map["refundSubQty"] ?? 0;
    unitRate = map['unitRate'];
    vchcode = map['vchcode'];
    vchtype = map['vchtype'];
    xcode = map['xcode'];
    skuPrice = map['skuPrice'];
    expireDay = map['expireDay'];
    buyDefaultUnit = map["buyDefaultUnit"];
    retailDefaultUnit = map["retailDefaultUnit"];
    saleDefaultUnit = map["saleDefaultUnit"];
    stockDefaultUnit = map["stockDefaultUnit"];
    remainUnitQty = map["remainUnitQty"];
    isExpandCombo = map["isExpandCombo"] ?? false;
    comboShareScale = map["comboShareScale"] ?? 0;
    remainCurrencyPreferentialShare = map["remainCurrencyPreferentialShare"];
    orgUnitId = map["orgUnitId"];
    if (orgUnitId == "" || orgUnitId == null) {
      orgUnitId = unitId;
    }
    unitList =
        (map['unitList'] as List?)?.map((o) {
          if (o is UnitsBean) {
            return o;
          }
          return UnitsBean.fromMap(o);
        }).toList() ??
        [];
    fullbarcodes =
        map['fullbarcodes'] == null
            ? null
            : (map['fullbarcodes'] as List?)
                    ?.map((o) => PtypeFullbarcodeDto.fromMap(o))
                    .toList() ??
                [];
    preferentialHelp =
        {}..addAll(
          ((map["preferentialHelp"] ?? {}) as Map).map((key, value) {
            if (value is Map) {
              return MapEntry(key, PreferentialDto.fromMap(value));
            } else {
              return MapEntry(key, value);
            }
          }),
        );
    promotionHints =
        (map["promotionHints"] as List?)
            ?.map((e) => PromotionHints.fromMap(e))
            .toList();
    promotionGift = map["promotionGift"] ?? false;
    promotionGiftScope =
        map["promotionGiftScope"] ?? PromotionGiftScope.chooseGoods.value;
    currencyGivePreferentialTotal = map["currencyGivePreferentialTotal"] ?? 0;
    givePreferentialTotal = map["givePreferentialTotal"] ?? 0;
    giveVipScore = map["giveVipScore"] ?? false;
  }

  static GoodsDetailDto fromMap(Map<dynamic, dynamic> map) {
    GoodsDetailDto goods = GoodsDetailDto();
    goods.saleOtypeVipPrice = map["saleOtypeVipPrice"];
    goods.promotionParentUnitId = map["promotionParentUnitId"];
    goods.promotionParentSkuid = map["promotionParentSkuid"];
    goods.promotionParentPtypeId = map["promotionParentPtypeId"];
    goods.posCurrencyDisedTaxedPrice = map["posCurrencyDisedTaxedPrice"] ?? 0;
    goods.posCurrencyDisedTaxedTotal = map["posCurrencyDisedTaxedTotal"] ?? 0;
    // goods.posPreferentialTotalExcludeGiveTotal =
    //     map["posPreferentialTotalExcludeGiveTotal"] ?? 0;
    goods.originalMaxQty = map["originalMaxQty"] ?? 0;
    goods.pid = map["pid"] ?? "";
    goods.ptypeGroup = map["ptypeGroup"] ?? 1;
    goods.maxQty = map["maxQty"] ?? 0.0;
    goods.saleBackHelper = GoodsSaleBackHelper.fromMap(map["saleBackHelper"]);
    goods.vipRights = map["vipRights"] ?? true;
    goods.manualPrice = map["manualPrice"] ?? false;
    goods.discountSourceType = DiscountSourceTypeUtil.fromStringToInt(
      map["discountSourceType"],
    );
    goods.isManualDiscount = map["isManualDiscount"] ?? true;
    goods.calIntegral = map["calIntegral"] ?? true;
    goods.joinOrder = map["joinOrder"] ?? true;
    goods.batchId = map["batchId"];
    goods.costId = map["costId"];
    goods.posGiftPrice = map["posGiftPrice"] ?? 0;
    goods.promotionType = map["promotionType"] ?? -1;
    goods.promotionGiftSingle = map["promotionGiftSingle"] ?? false;
    goods.visible = map["visible"] ?? false;
    goods.lastDiscount = map["lastDiscount"] ?? 1;
    goods.promotionId = map["promotionId"] ?? "0";
    goods.preferentialDiscount = map["preferentialDiscount"] ?? 0;
    goods.currencyPtypePreferentialTotal =
        map["currencyPtypePreferentialTotal"] ?? 0;
    goods.stockQty = map['stockQty'];
    goods.batchNo = map['batchNo'];
    goods.batchPrice = map['batchPrice'] ?? 0;
    goods.batchenabled = map['batchenabled'] ?? false;
    goods.brandName = map['brandName'] ?? "";
    goods.ptypeLength = map['ptypeLength'];
    goods.ptypeWidth = map['ptypeWidth'];
    goods.ptypeHeight = map['ptypeHeight'];
    goods.weight = map['weight'];
    goods.lengthUnit = map['lengthUnit'];
    goods.weightUnit = map['weightUnit'];
    goods.buyPrice = map['buyPrice'];
    goods.comboDetailId = map['comboDetailId'];
    goods.comboId = map['comboId'] ?? "";
    goods.comboRow = map['comboRow'] ?? false;
    goods.comboRowId = map['comboRowId'];
    goods.comboRowParId = map['comboRowParId'];
    goods.completedQty = map['completedQty'] ?? 0.0;
    goods.completedSubQty = map['completedSubQty'];
    goods.unitCompletedQty = map['unitCompletedQty'];
    goods.completedTotal = map['completedTotal'];
    goods.needPrintName = map['needPrintName'];
    goods.costPrice = map['costPrice'] ?? 0;
    goods.costTotal = map['costTotal'];
    goods.currencyDisedPrice = map['currencyDisedPrice'];
    goods.discountPrice = map['discountPrice'] ?? 0;
    goods.discountTotal = map['discountTotal'] ?? 0;
    goods.currencyDisedTaxedPrice = map['currencyDisedTaxedPrice'];
    goods.currencyDisedTaxedTotal = map['currencyDisedTaxedTotal'];
    goods.currencyDisedTotal = map['currencyDisedTotal'];
    goods.currencyOrderFeeAllotTotal = map[' currencyOrderFeeAllotTotal'];
    goods.currencyOrderPreferentialAllotTotal =
        map['currencyOrderPreferentialAllotTotal'] ?? 0;
    goods.currencyPreferentialTotal = map['currencyPreferentialTotal'] ?? 0;
    goods.currencyPrice = map['currencyPrice'] ?? 0;
    goods.currencyTaxTotal = map['currencyTaxTotal'];
    goods.currencyTotal = map['currencyTotal'];
    goods.detailId = map['detailId'];
    goods.orgDiscount = map['orgDiscount'];
    goods.discount = map['discount'];
    goods.promotedPrice = map['promotedPrice'] ?? 0;
    goods.promotedTotal = map['promotedTotal'] ?? 0;
    goods.expireDate = map['expireDate'];
    goods.forecastCostPrice = map['forecastCostPrice'];
    goods.forecastCostTotal = map['forecastCostTotal'];
    goods.gift = map['gift'];
    goods.inoutType = map['inoutType'];
    goods.kfullname = map['kfullname'];
    goods.ktypeId = map['ktypeId'];
    goods.memo = map['memo'];
    goods.valueTypeName = map["valueTypeName"];
    goods.overQty = map['overQty'];
    goods.overTotal = map['overTotal'];
    goods.fullbarcode = map['fullbarcode'] ?? "";
    goods.pFullName = map['pFullName'];
    goods.pUserCode = map['pUserCode'];
    goods.pcategory = map['pcategory'] ?? 0;
    goods.picUrl = map['picUrl'];
    goods.position = map['position'];
    goods.produceDate = map['produceDate'];
    goods.profileId = map['profileId'];
    goods.costMode = map['costMode'] ?? 0;
    goods.prop =
        map['prop'] == null
            ? null
            : [
              ...((map['prop'] ?? []) as List).map((o) {
                if (o is PtypePropDto) {
                  return o;
                }
                return PtypePropDto.fromMap(o);
              }),
            ];
    goods.propFormat = map['propFormat'];
    goods.propValues = map['propValues'];
    goods.propenabled = map['propenabled'];
    goods.protectDays = map['protectDays'];
    goods.ptypeId = map['ptypeId'];
    goods.ptypetype = map['ptypetype'] ?? (map['ptypeType'] ?? "");
    goods.typeid = map['typeId'];
    goods.labelIds = (map['labelIds'] as List?)?.cast<String>();
    goods.comboQtyRate = map['comboQtyRate'];
    goods.serialNoList = [
      ...((map['serialNoList'] ?? []) as List).map((o) {
        if (o is PtypeSerialNoDto) {
          return o;
        }
        return PtypeSerialNoDto.fromMap(o);
      }),
    ];
    goods.skuId = map['skuId'] ?? (goods.comboRow ? "0" : null);
    goods.skuName = map['skuName'];
    goods.snenabled = map['snenabled'];
    goods.snnoStrButton = map['snnoStrButton'];
    goods.hasSource = map['hasSource'] ?? false;
    goods.sourceDetailId = map['sourceDetailId'] ?? "";
    goods.sourceVchcode = map['sourceVchcode'];
    goods.sourceVchtype = map['sourceVchtype'];
    goods.sourceBfullname = map['sourceBfullname'];
    goods.sourceBillDate = map['sourceBillDate'];
    goods.sourceBillNumber = map['sourceBillNumber'];
    goods.sourceBtypeId = map['sourceBtypeId'];
    goods.sourceDetailPeriod = map['sourceDetailPeriod'];
    goods.sourceDfullname = map['sourceDfullname'];
    goods.sourceDtypeId = map['sourceDtypeId'];
    goods.sourceEfullname = map['sourceEfullname'];
    goods.sourceEtypeId = map['sourceEtypeId'];
    goods.sourceKfullname = map['sourceKfullname'];
    goods.sourceKtypeId = map['sourceKtypeId'];
    goods.sourceOfullname = map['sourceOfullname'];
    goods.sourceOtypeId = map['sourceOtypeId'];
    goods.sourceBusinessType =
        map['sourceBusinessType'] is String
            ? int.parse(map['sourceBusinessType'])
            : map['sourceBusinessType'];
    goods.standard = map['standard'] ?? "";
    goods.taxRate = map['taxRate'] ?? 0;
    goods.unOverQty = map['unOverQty'];
    goods.unOverTotal = map['unOverTotal'];
    goods.unitId = map['unitId'];
    goods.unitCode = map['unitCode'];
    goods.unitName = map['unitName'];
    goods.unitQty = map['unitQty'] ?? 0;
    goods.subQty = map['subQty'];
    goods.qty = map['qty'];
    goods.requestQty = map['requestQty'];
    goods.requestUnitQty = map['requestUnitQty'];
    goods.requestSubQty = map['requestSubQty'];
    goods.requestBackQty = map['requestBackQty'];
    goods.refundQty = map["refundQty"] ?? 0;
    goods.refundSubQty = map["refundSubQty"] ?? 0;
    goods.unitRate = map['unitRate'];
    goods.vchcode = map['vchcode'];
    goods.vchtype = map['vchtype'];
    goods.xcode = map['xcode'];
    goods.skuPrice = map['skuPrice'];
    goods.expireDay = map['expireDay'];
    goods.buyDefaultUnit = map["buyDefaultUnit"];
    goods.retailDefaultUnit = map["retailDefaultUnit"];
    goods.saleDefaultUnit = map["saleDefaultUnit"];
    goods.stockDefaultUnit = map["stockDefaultUnit"];
    goods.remainUnitQty = map["remainUnitQty"];
    goods.isExpandCombo = map["isExpandCombo"] ?? false;
    goods.comboShareScale = map["comboShareScale"] ?? 0;
    goods.remainCurrencyPreferentialShare =
        map["remainCurrencyPreferentialShare"];
    goods.orgUnitId = map["orgUnitId"];
    if (goods.orgUnitId == "" || goods.orgUnitId == null) {
      goods.orgUnitId = goods.unitId;
    }
    goods.unitList =
        (map['unitList'] as List?)?.map((o) {
          if (o is UnitsBean) {
            return o;
          }
          return UnitsBean.fromMap(o);
        }).toList() ??
        [];
    goods.fullbarcodes =
        map['fullbarcodes'] == null
            ? null
            : (map['fullbarcodes'] as List?)
                    ?.map((o) => PtypeFullbarcodeDto.fromMap(o))
                    .toList() ??
                [];
    goods.preferentialHelp =
        {}..addAll(
          ((map["preferentialHelp"] ?? {}) as Map).map((key, value) {
            if (value is Map) {
              return MapEntry(key, PreferentialDto.fromMap(value));
            } else {
              return MapEntry(key, value);
            }
          }),
        );
    goods.promotionHints =
        (map["promotionHints"] as List?)
            ?.map((e) => PromotionHints.fromMap(e))
            .toList();
    goods.promotionGift = map["promotionGift"] ?? false;
    goods.promotionGiftScope =
        map["promotionGiftScope"] ?? PromotionGiftScope.chooseGoods.value;
    goods.givePreferentialTotal = map["givePreferentialTotal"] ?? 0;
    goods.currencyGivePreferentialTotal =
        map["currencyGivePreferentialTotal"] ?? 0;
    goods.giveVipScore = map["giveVipScore"] ?? false;
    return goods;
  }

  GoodsDetailDto();

  ///无码商品
  GoodsDetailDto.noCodeGoods(this.ptypeId, this.currencyPrice) {
    pFullName = "无码商品";
    // unitName = "-";
    unitQty = 1;
    discount = 1.0;
    //无码商品，优惠分摊为0
    currencyOrderPreferentialAllotTotal = 0;
    //税率 0
    taxRate = 0.0;
    stockQty = 0.0;
    unitName = "";
  }

  num getRefundUnitQty() {
    if (null == unitRate || 0 == unitRate) {
      return refundQty;
    }
    return MathUtil.divideDec(refundQty, unitRate ?? 1).toDouble();
  }

  Map toJson() => {
    "saleOtypeVipPrice": saleOtypeVipPrice,
    "promotionParentUnitId": promotionParentUnitId,
    "promotionParentSkuid": promotionParentSkuid,
    "promotionParentPtypeId": promotionParentPtypeId,
    "originalMaxQty": originalMaxQty,
    "pid": pid,
    "ptypeGroup": ptypeGroup,
    "maxQty": maxQty,
    "valueTypeName": valueTypeName,
    "saleBackHelper": saleBackHelper.toJson(),
    // "maxPreferentialShare": maxPreferentialShare,
    // "qtyPreferentialShare": qtyPreferentialShare,
    // "qtyGiveStoreShare": qtyGiveStoreShare,
    // "qtyStoreShare": qtyStoreShare,
    "hasSource": hasSource,
    "preferentialHelp": preferentialHelp,
    "preferentialList": preferentialHelp.values.toList(),
    "vipRights": vipRights,
    "manualPrice": manualPrice,
    "discountSourceType": discountSourceType,
    "isManualDiscount": isManualDiscount,
    "calIntegral": calIntegral,
    "joinOrder": joinOrder,
    "posGiftPrice": posGiftPrice,
    "costId": costId,
    "batchId": batchId,
    "promotionType": promotionType,
    "promotionGiftSingle": promotionGiftSingle,
    "lastDiscount": lastDiscount,
    "visible": visible,
    "promotionId": promotionId,
    "preferentialDiscount": preferentialDiscount,
    "currencyPtypePreferentialTotal": currencyPtypePreferentialTotal,
    "stockQty": MathUtil.getNumByValueDouble(stockQty?.toDouble() ?? 0, 6) ?? 0,
    "batchNo": batchNo,
    "batchPrice": batchPrice,
    "batchenabled": batchenabled,
    "ptypeLength": ptypeLength,
    "ptypeWidth": ptypeWidth,
    "ptypeHeight": ptypeHeight,
    "weight": weight,
    "lengthUnit": lengthUnit,
    "weightUnit": weightUnit,
    "buyPrice": buyPrice,
    "comboDetailId": comboDetailId,
    "comboId": comboId,
    "comboRow": comboRow,
    "comboRowId": comboRowId,
    "comboRowParId": comboRowParId,
    "completedQty": completedQty,
    "completedSubQty": completedSubQty,
    "unitCompletedQty": unitCompletedQty,
    "completedTotal": completedTotal,
    "needPrintName": needPrintName,
    "costPrice": costPrice,
    "costTotal": costTotal,
    "currencyDisedPrice": currencyDisedPrice,
    "discountPrice": discountPrice,
    "discountTotal": discountTotal,
    "currencyDisedTaxedPrice": currencyDisedTaxedPrice,
    "currencyDisedTaxedTotal": currencyDisedTaxedTotal,
    "currencyDisedTotal": currencyDisedTotal,
    " currencyOrderFeeAllotTotal": currencyOrderFeeAllotTotal,
    "currencyOrderPreferentialAllotTotal": num.tryParse(
      DecimalDisplayHelper.getTotalFixed(
        currencyOrderPreferentialAllotTotal.toString(),
      ),
    ),
    "currencyPreferentialTotal": currencyPreferentialTotal,
    "currencyPrice": currencyPrice,
    "currencyTaxTotal": currencyTaxTotal,
    "currencyTotal": currencyTotal,
    "detailId": detailId,
    "discount": discount,
    "promotedPrice": promotedPrice,
    "promotedTotal": promotedTotal,
    "orgDiscount": orgDiscount,
    "expireDate": expireDate,
    "forecastCostPrice": forecastCostPrice,
    "forecastCostTotal": forecastCostTotal,
    "gift": gift,
    "inoutType": inoutType,
    "kfullname": kfullname,
    "ktypeId": ktypeId,
    "memo": memo,
    "overQty": overQty,
    "overTotal": overTotal,
    "fullbarcode": fullbarcode,
    "pFullName": pFullName,
    "pUserCode": pUserCode,
    "pcategory": pcategory,
    "picUrl": picUrl,
    "position": position,
    "produceDate": produceDate,
    "profileId": profileId,
    "prop": prop,
    "propFormat": propFormat,
    "propValues": propValues,
    "propenabled": propenabled,
    "protectDays": protectDays,
    "ptypeId": ptypeId,
    "ptypetype": ptypetype,
    "typeId": typeid,
    "labelIds": labelIds,
    "comboQtyRate": comboQtyRate,
    "serialNoList": serialNoList,
    "skuId": skuId,
    "skuName": skuName,
    "snenabled": snenabled,
    "snnoStrButton": snnoStrButton,
    "sourceDetailId": sourceDetailId,
    "sourceVchcode": sourceVchcode,
    "sourceVchtype": sourceVchtype,
    "sourceBfullname": sourceBfullname,
    "sourceBillDate": sourceBillDate,
    "sourceBillNumber": sourceBillNumber,
    "sourceBtypeId": sourceBtypeId,
    "sourceDetailPeriod": sourceDetailPeriod,
    "sourceDfullname": sourceDfullname,
    "sourceDtypeId": sourceDtypeId,
    "sourceEfullname": sourceEfullname,
    "sourceEtypeId": sourceEtypeId,
    "sourceKfullname": sourceKfullname,
    "sourceKtypeId": sourceKtypeId,
    "sourceOfullname": sourceOfullname,
    "sourceOtypeId": sourceOtypeId,
    "sourceBusinessType": sourceBusinessType,
    "standard": standard,
    "taxRate": taxRate,
    "unOverQty": unOverQty,
    "unOverTotal": unOverTotal,
    "unitId": unitId,
    "unitCode": unitCode,
    "unitName": unitName,
    "unitQty": unitQty,
    "subQty": subQty,
    "qty": qty,
    "requestQty": requestQty,
    "requestUnitQty": requestUnitQty,
    "requestSubQty": requestSubQty,
    "requestBackQty": requestBackQty,
    "refundQty": refundQty,
    "refundSubQty": refundSubQty,
    "unitRate": unitRate,
    "vchcode": vchcode,
    "vchtype":
        vchtype?.isEmpty == true ? BillTypeData[BillType.SaleBill] : vchtype,
    "xcode": xcode,
    "skuPrice": skuPrice,
    "expireDay": expireDay,
    "unitList": unitList,
    "stockDefaultUnit": stockDefaultUnit,
    "saleDefaultUnit": saleDefaultUnit,
    "buyDefaultUnit": buyDefaultUnit,
    "retailDefaultUnit": retailDefaultUnit,
    "remainUnitQty": remainUnitQty,
    "remainCurrencyPreferentialShare": remainCurrencyPreferentialShare,
    "orgUnitId": orgUnitId,
    "isExpandCombo": isExpandCombo,
    "comboShareScale": comboShareScale,
    "fullbarcodes": jsonDecode(jsonEncode(fullbarcodes)),
    "costMode": costMode,
    "posCurrencyDisedTaxedPrice": posCurrencyDisedTaxedPrice,
    "posCurrencyDisedTaxedTotal": posCurrencyDisedTaxedTotal,
    // "posPreferentialTotalExcludeGiveTotal":
    //     posPreferentialTotalExcludeGiveTotal,
    "promotionHints": promotionHints?.map((e) => e.toJson()).toList(),
    "promotionGift": promotionGift,
    "promotionGiftScope": promotionGiftScope,
    "currencyGivePreferentialTotal": currencyGivePreferentialTotal,
    "givePreferentialTotal": givePreferentialTotal,
    "giveVipScore": giveVipScore,
  };
}
