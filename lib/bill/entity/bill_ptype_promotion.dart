/// preferential : 0
/// promotionId : 0
/// promotionName : "string"
/// ptypeId : 0
/// skuId : 0
/// unitId : 0

class BillPtypePromotion {
  num? preferential;
  String? promotionId;
  String? promotionName;
  String? ptypeId;
  String? skuId;
  String? unitId;

  static BillPtypePromotion fromMap(Map<String, dynamic>? map) {
    if (map == null) return BillPtypePromotion();
    BillPtypePromotion billPtypePromotionBean = BillPtypePromotion();
    billPtypePromotionBean.preferential = map['preferential'];
    billPtypePromotionBean.promotionId = map['promotionId'];
    billPtypePromotionBean.promotionName = map['promotionName'];
    billPtypePromotionBean.ptypeId = map['ptypeId'];
    billPtypePromotionBean.skuId = map['skuId'];
    billPtypePromotionBean.unitId = map['unitId'];
    return billPtypePromotionBean;
  }

  Map toJson() => {
    "preferential": preferential,
    "promotionId": promotionId,
    "promotionName": promotionName,
    "ptypeId": ptypeId,
    "skuId": skuId,
    "unitId": unitId,
  };
}