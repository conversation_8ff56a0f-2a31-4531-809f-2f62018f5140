///单据会员资产变动
class BillVipAssertChange {
  num qty;

  String? memo;

  String? assertId;

  ///卡券模板id，只有通过模板赠送卡券时使用(促销赠送)
  String? cardTemplateId;

  ///会员资产变动种类（0：积分:、1：储值、2：赠送储值、3：成长值:、4：卡券、5：服务商品）
  int typed;

  ///资产变动类型
  ///     NEW_VIP(0, "新增会员"),
  ///     BIRTHDAY_GIFT(1, "生日赠送"),
  ///     RECHARGE_GIFT(2, "充值赠送"),
  ///     CARD_OPENING_GIFT(3, "开卡赠送"),
  ///     UPGRADE_GIFT(4, "升级赠送"),
  ///     CONSUME_GET(5, "消费获得"),
  ///     CONSUME_USE(6, "消费使用"),
  ///     REFUND_RETURN(7, "退款退回"),
  ///     REFUND_DEDUCTION(8, "退款扣除"),
  ///     VIP_RECHARGE(9, "会员充值"),
  ///     STORED_VALUE_REFUND(10, "储值退款"),
  ///     RECHARGE_INVALIDATE(11, "充值作废扣除"),
  ///     SCORE_EXCHANGE(12, "积分兑换"),
  ///     MANUAL_ADJUSTMENT(13, "手工调整"),
  ///     MANUAL_DISTRIBUTION(14, "手工发放"),
  ///     MANUAL_UNBINDING(15, "手工解绑"),
  ///     MANUALLY_CHANGE_LEVEL(16, "手动变更等级"),
  ///     SCORE_EXPIRE(17, "积分过期"),
  ///     CARD_EXPIRE(18, "卡券到期"),
  ///     ACCUMULATED_GIFT(19, "累计消费笔数赠送");
  int changeType;

  BillVipAssertChange({
    required this.qty,
    this.memo,
    this.assertId,
    this.cardTemplateId,
    required this.typed,
    required this.changeType,
  });

  Map<String, dynamic> toJson() => {
        'qty': qty,
        'memo': memo,
        'assertId': assertId,
        'cardTemplateId': cardTemplateId,
        'typed': typed,
        'changeType': changeType,
      };

  BillVipAssertChange.fromMap(Map<String, dynamic> map)
      : this(
          qty: map['qty'] ?? 0,
          memo: map['memo'],
          assertId: map['assertId'],
          cardTemplateId: map['cardTemplateId'],
          typed: map['typed'] ?? -1,
          changeType: map['changeType'] ?? -1,
        );
}
