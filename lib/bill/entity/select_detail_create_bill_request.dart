import 'base_request_dto.dart';
import 'check_detail_bill_request.dart';

///订单生单接口
class SelectDetailCreateBillRequest extends BaseRequestDto {
  List<CreateBillRequestItem>? items;
  bool? spilt = true;
  String? createVchtype;
  String? createBusinessType;
  List<String>? detailIdList;
  List<SelectOrderBillDTO>? newDetailList;
  List<String>? vchcodeList;
  String? fromVchtype;
  String? ktypeId;
  String? kfullname;
  int? customType = 0;

  @override
  Map<String, dynamic> toJson() {
    return super.toJson()
      ..addAll({
        'items': items?.map((e) => e.toJson()).toList(),
        'spilt': spilt,
        'createVchtype': createVchtype,
        'createBusinessType': createBusinessType,
        'detailIdList': detailIdList,
        'newDetailList': newDetailList?.map((e) => e.toJson()).toList(),
        'vchcodeList': vchcodeList,
        'fromVchtype': fromVchtype,
        'ktypeId': ktypeId,
        'kfullname': kfullname,
        'customType': customType,
      });
  }

  SelectDetailCreateBillRequest.fromMap(Map<String, dynamic> map)
      : super.fromMap(map) {
    items = (map['items'] as List?)
        ?.map((e) => CreateBillRequestItem.fromMap(e))
        .toList();
    spilt = map['spilt'];
    createVchtype = map['createVchtype'];
    createBusinessType = map['createBusinessType'];
    detailIdList = (map['detailIdList'] as List?)?.cast();
    newDetailList = (map['newDetailList'] as List?)
        ?.map((e) => SelectOrderBillDTO.fromMap(e))
        .toList();
    vchcodeList = (map['vchcodeList'] as List?)?.cast();
    fromVchtype = map['fromVchtype'];
    ktypeId = map['ktypeId'];
    kfullname = map['kfullname'];
    customType = map['customType'];
  }

  SelectDetailCreateBillRequest({
    this.items,
    this.spilt,
    this.createVchtype,
    this.createBusinessType,
    this.detailIdList,
    this.newDetailList,
    this.vchcodeList,
    this.fromVchtype,
    this.ktypeId,
    this.kfullname,
    this.customType,
  });
}

class CreateBillRequestItem {
  String? vchcode;
  String? businessType;

  Map<String, dynamic> toJson() {
    return {
      'vchcode': vchcode,
      'businessType': businessType,
    };
  }

  CreateBillRequestItem.fromMap(Map<String, dynamic> map) {
    vchcode = map['vchcode'];
    businessType = map['businessType'];
  }

  CreateBillRequestItem({
    this.vchcode,
    this.businessType,
  });
}
