import 'package:collection/collection.dart';

/// 创建时间：7/11/22
/// 作者：xiaotia<PERSON>ong
/// 描述：

// ignore_for_file: constant_identifier_names, duplicate_ignore

class PreferentialDto {
  ///做辅助id 使用 例:优惠券id
  String? helpId;

  ///优惠辅助表type 0无 1促销 2权益卡 3优惠券 4积分抵现 5手工录入 6 储值赠金 7抹零 8会员价
  int type = 0;

  ///优惠辅助表type id (对应促销id,优惠券价值id,权益价值id)
  String typeId = "0";

  ///优惠折扣
  num discount = 1.0;

  ///优惠金额(折扣金额 或 优惠分摊)
  num total = 0.0;

  ///积分
  num value = 0.0;

  static PreferentialDto fromMap(Map<dynamic, dynamic>? map) {
    PreferentialDto preferentialDto = PreferentialDto();
    if (map == null) return preferentialDto;
    preferentialDto.helpId = map["helpId"];
    preferentialDto.type = map["type"] ?? 0;
    preferentialDto.typeId = map["typeId"] ?? "0";
    preferentialDto.discount = map["discount"] ?? 1.0;
    preferentialDto.total = map["total"] ?? 0.0;
    preferentialDto.value = map["value"] ?? 0.0;
    return preferentialDto;
  }

  Map toJson() => {
        "type": type,
        "typeId": typeId,
        "discount": discount,
        "total": total,
        "value": value,
      };
}

///商品级别goods 单据级别bill
///map 中的 key
class PreferentialDtoType {
  ///会员价格
  static const String vipPriceGoods = "vipPriceGoods";

  ///权益卡(等级权益和权益卡种选一个折扣最低的)
  static const String rightsCardGoods = "rightsCardGoods";

  ///优惠券(包括折扣券和提货券，都是商品级别的)
  static const String couponGoods = "couponGoods";

  ///促销(特价、第二件半价、满件赠、满额赠)
  static const String promotionGoods = "promotionGoods";

  ///手工改价(赠品和改价)
  static const String manualGoods = "manualGoods";

  //商品单据分割

  ///手工录入(整单优惠)
  static const String manualBill = "manualBill";

  ///储值赠金
  static const String storeGiftTotalBill = "storeGiftTotalBill";

  ///订单满减
  static const String promotionBill = "promotionBill";

  ///代金券
  static const String couponBill = "couponBill";

  ///积分抵扣
  static const String scoreBill = "scoreBill";

  ///抹零
  static const String eraseZeroBill = "eraseZeroBill";
}

///优惠券优惠
const Set<Preferential> couponPreferentialSet = {
  Preferential.discountCoupon,
  Preferential.giftCoupon,
  Preferential.moneyCoupon
};

///打折优惠
const Set<Preferential> discountPreferentialSet = {
  Preferential.vipPrice,
  Preferential.discountRightsCard,
  Preferential.discountCoupon,
};

///商品级别优惠
const Set<Preferential> goodsPreferentialSet = <Preferential>{
  Preferential.vipPrice,
  Preferential.manualPrice,
  Preferential.goodsPromotion,
  Preferential.billPromotion,
  Preferential.discountRightsCard,
  Preferential.giftCoupon,
  Preferential.discountCoupon,
};

///单据优惠分摊的优惠
const Set<Preferential> billPreferentialSet = <Preferential>{
  Preferential.moneyCoupon,
  Preferential.billPreferential,
  Preferential.scoreDiscount,
  Preferential.eraseZero,
};

///单据优惠分摊的优惠(按照优先级从小到大排序)
///不要直接编辑此列表
final sortedBillPreferentialList = billPreferentialSet
    .sorted((a, b) =>
        a.billPreferentialPriority.compareTo(b.billPreferentialPriority))
    .toList(growable: false);

///优惠(场景)
enum Preferential {
  //商品级别优惠
  ///会员价
  vipPrice,

  ///手工改价
  manualPrice,

  ///商品促销
  goodsPromotion,

  ///单据促销(订单满减)
  billPromotion,

  ///打折权益卡(等级权益卡和普通权益卡中取一个折扣最低的)
  discountRightsCard,

  ///提货券
  giftCoupon,

  ///折扣券
  discountCoupon,

  //单据级别优惠
  ///储值赠金
  giftStore,

  ///积分抵扣
  scoreDiscount,

  ///整单优惠(现总额优惠)
  billPreferential,

  ///代金券
  moneyCoupon,

  ///抹零
  eraseZero,
}

extension PreferentialExtension on Preferential {
  PreferentialType get type {
    switch (this) {
      case Preferential.vipPrice:
        return PreferentialType.VipPrice;
      case Preferential.manualPrice:
        return PreferentialType.Manual;
      case Preferential.goodsPromotion:
      case Preferential.billPromotion:
        return PreferentialType.Promotion;
      case Preferential.discountRightsCard:
        return PreferentialType.RightsCard;
      case Preferential.giftCoupon:
      case Preferential.discountCoupon:
      case Preferential.moneyCoupon:
        return PreferentialType.Coupon;
      case Preferential.scoreDiscount:
        return PreferentialType.Score;
      case Preferential.eraseZero:
        return PreferentialType.EraseZero;
      case Preferential.giftStore:
        return PreferentialType.GiftTotal;
      case Preferential.billPreferential:
        return PreferentialType.BillPreferential;
    }
  }

  ///整单级别优惠的优先级(小的在前面)
  int get billPreferentialPriority {
    switch (this) {
      //代金券
      case Preferential.moneyCoupon:
        return 0;
      //手工优惠分摊
      case Preferential.billPreferential:
        return 1;
      //积分抵扣
      case Preferential.scoreDiscount:
        return 2;
      //抹零
      case Preferential.eraseZero:
        return 3;
      default:
        return 999999;
    }
  }
}

enum PreferentialType {
  ///促销
  Promotion,

  ///权益卡
  RightsCard,

  ///优惠券
  Coupon,

  ///积分抵扣
  Score,

  ///手工录入
  Manual,

  ///储值赠金
  GiftTotal,

  ///抹零
  EraseZero,

  ///会员价
  VipPrice,

  ///整单优惠
  BillPreferential
}

extension PreferentialTypeExtension on PreferentialType {
  int get value {
    switch (this) {
      case PreferentialType.Promotion:
        return 1;
      case PreferentialType.RightsCard:
        return 2;
      case PreferentialType.Coupon:
        return 3;
      case PreferentialType.Score:
        return 4;
      case PreferentialType.Manual:
        return 5;
      case PreferentialType.GiftTotal:
        return 6;
      case PreferentialType.EraseZero:
        return 7;
      case PreferentialType.VipPrice:
        return 8;
      case PreferentialType.BillPreferential:
        return 9;
    }
  }
}
