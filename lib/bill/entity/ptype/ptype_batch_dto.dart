///商品批次，用于商品自动带出批次
class PtypeBatchDto {
  PtypeBatchDto({
    this.batchId,
    this.inventoryFlag,
    this.comboRowId,
    this.ktypeId,
    this.kfullname,
    this.costPrice,
    this.batchPrice,
    this.ptypeId,
    this.skuId,
    this.batchNo,
    this.produceDate,
    this.expireDate,
    this.stockQty,
    this.qty,
    this.stockSubQty,
    this.subUnitName,
    this.unitQty,
    this.stockPrice,
    this.stockTotal,
    this.protectDays,
    this.batchTime,
    this.costId,
    this.sendQty,
    this.batchSendQty,
    this.groupName,
    this.enabled,
    this.inventoryQty,
  });

  PtypeBatchDto.fromJson(dynamic json) {
    batchId = json['batchId'];
    inventoryFlag = json['inventoryFlag'];
    comboRowId = json['comboRowId'];
    ktypeId = json['ktypeId'];
    kfullname = json['kfullname'];
    costPrice = json['costPrice'];
    batchPrice = json['batchPrice'];
    ptypeId = json['ptypeId'];
    skuId = json['skuId'];
    batchNo = json['batchNo'];
    produceDate = json['produceDate'];
    expireDate = json['expireDate'];
    stockQty = json['stockQty'];
    qty = json['qty'];
    stockSubQty = json['stockSubQty'];
    subUnitName = json['subUnitName'];
    unitQty = json['unitQty'];
    stockPrice = json['stockPrice'];
    stockTotal = json['stockTotal'];
    protectDays = json['protectDays'];
    batchTime = json['batchTime'];
    costId = json['costId'];
    sendQty = json['sendQty'];
    batchSendQty = json['batchSendQty'];
    groupName = json['groupName'];
    enabled = json['enabled'];
    inventoryQty = json['inventoryQty'];
  }

  ///批次id
  String? batchId;
  String? inventoryFlag;

  ///套餐行id
  String? comboRowId;

  ///仓库id
  String? ktypeId;

  ///仓库名称
  String? kfullname;
  num? costPrice;

  ///批次成本(单价)
  num? batchPrice;

  ///商品id
  String? ptypeId;

  ///skuId
  String? skuId;

  ///批次号
  String? batchNo;

  ///生产日期
  String? produceDate;

  ///过期日期
  String? expireDate;

  ///批次库存数量
  num? stockQty;

  ///批次库存数量
  num? qty;

  ///批次副单位库存数量
  num? stockSubQty;

  ///副单位名
  String? subUnitName;

  ///单位数量
  num? unitQty;

  ///批次库存均价
  num? stockPrice;

  ///批次库存金额
  num? stockTotal;

  ///保质期
  int? protectDays;

  String? batchTime;

  ///个别计价id
  String? costId;

  ///可发货库存
  num? sendQty;

  ///批次可发货库存
  num? batchSendQty;
  String? groupName;
  bool? enabled;
  num? inventoryQty;

  Map<String?, dynamic> toJson() {
    final map = <String?, dynamic>{};
    map['batchId'] = batchId;
    map['inventoryFlag'] = inventoryFlag;
    map['comboRowId'] = comboRowId;
    map['ktypeId'] = ktypeId;
    map['kfullname'] = kfullname;
    map['costPrice'] = costPrice;
    map['batchPrice'] = batchPrice;
    map['ptypeId'] = ptypeId;
    map['skuId'] = skuId;
    map['batchNo'] = batchNo;
    map['produceDate'] = produceDate;
    map['expireDate'] = expireDate;
    map['stockQty'] = stockQty;
    map['qty'] = qty;
    map['stockSubQty'] = stockSubQty;
    map['subUnitName'] = subUnitName;
    map['unitQty'] = unitQty;
    map['stockPrice'] = stockPrice;
    map['stockTotal'] = stockTotal;
    map['protectDays'] = protectDays;
    map['batchTime'] = batchTime;
    map['costId'] = costId;
    map['sendQty'] = sendQty;
    map['batchSendQty'] = batchSendQty;
    map['groupName'] = groupName;
    map['enabled'] = enabled;
    map['inventoryQty'] = inventoryQty;
    return map;
  }
}
