/// id : "694262728751210703"
/// profileId : "617794922333872129"
/// ptypeId : "694262728751079631"
/// unitCode : 1
/// unitName : "个"
/// unitRate : 1
/// barcode : ""
/// createTime : "2022-01-01T14:15:10.966+0000"
/// updateTime : "2022-01-01T14:15:10.966+0000"

///商品单位的实体类
class PtypeUnitDto {
  ///unitId
  String id = "";

  ///账套id
  String? profileId;

  ///商品id
  String? ptypeId;

  ///单位序号(不引用)基本单位则为1 其他依次递增
  int? unitCode;

  ///单位名称
  String unitName = "";

  ///该单位对应基础单位的数量
  num? unitRate;
  String? barcode;

  ///创建日期 "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
  String? createTime;
  String? updateTime;

  PtypeUnitDto({
    this.id = "",
    this.profileId,
    this.ptypeId,
    this.unitCode,
    this.unitName = "",
    this.unitRate,
    this.barcode,
    this.createTime,
    this.updateTime,
  });

  PtypeUnitDto.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    ptypeId = json['ptypeId'];
    unitCode = json['unitCode'];
    unitName = json['unitName'] ?? "";
    unitRate = json['unitRate'];
    barcode = json['barcode'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
  }

  Map<String?, dynamic> toJson() {
    final map = <String?, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['ptypeId'] = ptypeId;
    map['unitCode'] = unitCode;
    map['unitName'] = unitName;
    map['unitRate'] = unitRate;
    map['barcode'] = barcode;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    return map;
  }
}
