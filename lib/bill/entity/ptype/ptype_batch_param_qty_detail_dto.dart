///用于自动带出商品批次号接口的请求商品实体类
class PtypeBatchParamQtyDetailDto {
  PtypeBatchParamQtyDetailDto({
    this.comboRowId,
    this.ptypeId,
    this.count,
    this.skuId,
    this.protectDays,
    this.costMode,
  });

  PtypeBatchParamQtyDetailDto.fromJson(dynamic json) {
    comboRowId = json['comboRowId'];
    ptypeId = json['ptypeId'];
    count = json['count'];
    skuId = json['skuId'];
    protectDays = json['protectDays'];
    costMode = json['costMode'];
  }

  ///套餐行明细id
  String? comboRowId;

  ///商品id
  String? ptypeId;

  ///数量
  double? count;

  ///skuId
  String? skuId;

  ///保质期
  num? protectDays;

  ///成本类型 当商品costMode为1时，此时为个别计价批次
  int? costMode;

  Map<String?, dynamic> toJson() {
    final map = <String?, dynamic>{};
    map['comboRowId'] = comboRowId;
    map['ptypeId'] = ptypeId;
    map['count'] = count;
    map['skuId'] = skuId;
    map['protectDays'] = protectDays;
    map['costMode'] = costMode;
    return map;
  }
}
