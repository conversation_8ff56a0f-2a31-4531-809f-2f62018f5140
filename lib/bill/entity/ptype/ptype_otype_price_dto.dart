import '../../../db/database_helper.dart';

class PtypeOtypePriceDto {
  String? btypeId;
  String? createTime;
  double? lastSaleDiscount;
  double? lastBuyPrice;
  int? createType;
  double? lastBuyDiscount;
  String? skuId;
  double? salePrice;
  double? saleOtypeVipPrice;
  double? buyDiscount;
  String? updateTime;
  double? lastSalePrice;
  String? profileId;
  double? saleDiscount;
  dynamic lastBuyTime;
  String? ptypeId;
  double? buyPrice;
  String? id;
  String? xtypeId;
  String? unitId;
  dynamic lastSaleTime;

  static PtypeOtypePriceDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return PtypeOtypePriceDto();
    map = DatabaseHelper.changeUnderscoreCaseMapToCamelCaseMap(map);
    PtypeOtypePriceDto ptypeOtypePriceDtoBean = PtypeOtypePriceDto();
    ptypeOtypePriceDtoBean.btypeId = map['btypeId'];
    ptypeOtypePriceDtoBean.createTime = map['createTime'];
    ptypeOtypePriceDtoBean.lastSaleDiscount = map['lastSaleDiscount'];
    ptypeOtypePriceDtoBean.lastBuyPrice = map['lastBuyPrice'];
    ptypeOtypePriceDtoBean.createType = map['createType'];
    ptypeOtypePriceDtoBean.lastBuyDiscount = map['lastBuyDiscount'];
    ptypeOtypePriceDtoBean.skuId = map['skuId'] ?? "0";
    ptypeOtypePriceDtoBean.salePrice = map['salePrice'];
    ptypeOtypePriceDtoBean.saleOtypeVipPrice = map['saleOtypeVipPrice'];
    ptypeOtypePriceDtoBean.buyDiscount = map['buyDiscount'];
    ptypeOtypePriceDtoBean.updateTime = map['updateTime'];
    ptypeOtypePriceDtoBean.lastSalePrice = map['lastSalePrice'];
    ptypeOtypePriceDtoBean.profileId = map['profileId'];
    ptypeOtypePriceDtoBean.saleDiscount = map['saleDiscount'];
    ptypeOtypePriceDtoBean.lastBuyTime = map['lastBuyTime'];
    ptypeOtypePriceDtoBean.ptypeId = map['ptypeId'];
    ptypeOtypePriceDtoBean.buyPrice = map['buyPrice'];
    ptypeOtypePriceDtoBean.id = map['id'];
    ptypeOtypePriceDtoBean.xtypeId = map['xtypeId'];
    ptypeOtypePriceDtoBean.unitId = map['unitId'];
    ptypeOtypePriceDtoBean.lastSaleTime = map['lastSaleTime'];
    return ptypeOtypePriceDtoBean;
  }

  Map toJson() => {
    "btypeId": btypeId,
    "createTime": createTime,
    "lastSaleDiscount": lastSaleDiscount,
    "lastBuyPrice": lastBuyPrice,
    "createType": createType,
    "lastBuyDiscount": lastBuyDiscount,
    "skuId": skuId,
    "salePrice": salePrice,
    "saleOtypeVipPrice": saleOtypeVipPrice,
    "buyDiscount": buyDiscount,
    "updateTime": updateTime,
    "lastSalePrice": lastSalePrice,
    "profileId": profileId,
    "saleDiscount": saleDiscount,
    "lastBuyTime": lastBuyTime,
    "ptypeId": ptypeId,
    "buyPrice": buyPrice,
    "id": id,
    "xtypeId": xtypeId,
    "unitId": unitId,
    "lastSaleTime": lastSaleTime,
  };
}