/// Copyright (C), 2019-2020, wsgjp.com
/// FileName: ptype_serial_no_dto.dart
/// Author: lidingwen
/// Date: 2020/7/22 2:33 PM
/// Description:

class PtypeSerialNoDto {
  String? batchNo;
  String? ktypeId;
  String? kfullname;

  ///调拨单用，单据商品列表详情id
  String? detailId;

  ///调拨单用
  String? inoutId;

  ///调拨单用
  String? inoutDetailId;

  ///调拨单用，是否入库完成
  bool? completed;
  String? id;
  String? memo; //是否有用？
  String? profileId;
  String? ptypeId;
  String? snno;
  String? sn1;
  String? sn2;
  String? produceDate;
  String? expireDate;
  String? billDate;
  String? snMemo;

  /// 序列号是否在库
  bool isInTheStock = true;

  PtypeSerialNoDto();
  PtypeSerialNoDto._( this.id,this.snno,this.sn1,this.sn2,);

  factory PtypeSerialNoDto.build({required String id ,required String snno,required String sn1,required String sn2}) {
    return PtypeSerialNoDto._(id, snno, sn1, sn2);
  }

  static PtypeSerialNoDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return PtypeSerialNoDto();
    PtypeSerialNoDto checkSnListBean = PtypeSerialNoDto();
    checkSnListBean.batchNo = map['batchNo'];
    checkSnListBean.ktypeId = map['ktypeId'];
    checkSnListBean.kfullname = map['kfullname'];
    checkSnListBean.detailId = map['detailId'];
    checkSnListBean.inoutId = map['inoutId'];
    checkSnListBean.inoutDetailId = map['inoutDetailId'];
    checkSnListBean.completed = map['completed'];
    checkSnListBean.id = map['id'];
    checkSnListBean.memo = map['memo'];
    checkSnListBean.profileId = map['profileId'];
    checkSnListBean.ptypeId = map['ptypeId'];
    checkSnListBean.snno = map['snno'];
    checkSnListBean.sn1 = map['sn1'];
    checkSnListBean.sn2 = map['sn2'];
    checkSnListBean.produceDate = map['produceDate'] ?? "";
    checkSnListBean.expireDate = map['expireDate'] ?? "";
    checkSnListBean.billDate = map['billDate'] ?? "";
    checkSnListBean.snMemo = map["snMemo"];
    checkSnListBean.isInTheStock =
        map["isInTheStock"] ?? map["inTheStock"] ?? false;
    return checkSnListBean;
  }

  Map<String, dynamic> toJson() => {
        "batchNo": batchNo,
        "kfullname": kfullname,
        "ktupeId": ktypeId,
        "detailId": detailId,
        "inoutId": inoutId,
        "inoutDetailId": inoutDetailId,
        "completed": completed,
        "id": id,
        "memo": memo,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "snno": snno,
        "sn1": sn1,
        "sn2": sn2,
        "produceDate": produceDate,
        "expireDate": expireDate,
    "billDate": billDate,
    "snMemo": snMemo,
        "isInTheStock": isInTheStock
      };
}
