
class UnitBean {
  String? barcode;
  String? buyPrice;
  String? createTime;
  String? id;
  String? minSalePrice;
  String? preprice1;
  String? preprice10;
  String? preprice2;
  String? preprice3;
  String? preprice4;
  String? preprice5;
  String? preprice6;
  String? preprice7;
  String? preprice8;
  String? preprice9;
  String? profileId;
  String? ptypeId;
  String? retailPrice;
  int? unitCode;
  String? unitName;
  num? unitRate;
  String? updateTime;

  static UnitBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return UnitBean();
    UnitBean unitBean = UnitBean();
    unitBean.barcode = map['barcode'];
    unitBean.buyPrice = map['buyPrice'];
    unitBean.createTime = map['createTime'];
    unitBean.id = map['id'];
    unitBean.minSalePrice = map['minSalePrice'];
    unitBean.preprice1 = map['preprice1'];
    unitBean.preprice10 = map['preprice10'];
    unitBean.preprice2 = map['preprice2'];
    unitBean.preprice3 = map['preprice3'];
    unitBean.preprice4 = map['preprice4'];
    unitBean.preprice5 = map['preprice5'];
    unitBean.preprice6 = map['preprice6'];
    unitBean.preprice7 = map['preprice7'];
    unitBean.preprice8 = map['preprice8'];
    unitBean.preprice9 = map['preprice9'];
    unitBean.profileId = map['profileId'];
    unitBean.ptypeId = map['ptypeId'];
    unitBean.retailPrice = map['retailPrice'];
    unitBean.unitCode = map['unitCode'];
    unitBean.unitName = map['unitName'];
    unitBean.unitRate = map['unitRate'];
    unitBean.updateTime = map['updateTime'];
    return unitBean;
  }

  Map toJson() => {
    "barcode": barcode,
    "buyPrice": buyPrice,
    "createTime": createTime,
    "id": id,
    "minSalePrice": minSalePrice,
    "preprice1": preprice1,
    "preprice10": preprice10,
    "preprice2": preprice2,
    "preprice3": preprice3,
    "preprice4": preprice4,
    "preprice5": preprice5,
    "preprice6": preprice6,
    "preprice7": preprice7,
    "preprice8": preprice8,
    "preprice9": preprice9,
    "profileId": profileId,
    "ptypeId": ptypeId,
    "retailPrice": retailPrice,
    "unitCode": unitCode,
    "unitName": unitName,
    "unitRate": unitRate,
    "updateTime": updateTime,
  };
}