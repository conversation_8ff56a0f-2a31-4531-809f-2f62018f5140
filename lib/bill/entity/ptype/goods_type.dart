///商品分类
class GoodsType {
  static const GoodsType all = GoodsType("全部", null, null, []);

  final String fullname;

  final String? typeid;

  final String? partypeid;

  final List<GoodsType> children;

  const GoodsType(this.fullname, this.typeid, this.partypeid, this.children);

  GoodsType.fromMap(Map<String, dynamic> map)
      : this(
            map['fullname'],
            map['typeid'],
            map['partypeid'],
            (map['children'] as List?)
                    ?.map((e) => GoodsType.fromMap(e))
                    .toList() ??
                []);
}
