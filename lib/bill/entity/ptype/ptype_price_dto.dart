///商品价格
class PtypePriceDto {
  PtypePriceDto({
    this.ptypeId,
    this.skuId,
    this.unitId,
    this.skuPrice,
    this.retailPrice,
    this.otypePrice,
    this.vipPrice,
  });

  PtypePriceDto.fromMap(dynamic json) {
    ptypeId = json['ptypeId'];
    skuId = json['skuId'];
    unitId = json['unitId'];
    skuPrice = json['skuPrice'];
    retailPrice = json['retailPrice'];
    otypePrice = json['otypePrice'];
    vipPrice = json['vipPrice'];
  }

  ///商品id
  String? ptypeId;

  ///skuId
  String? skuId;

  ///单位id
  String? unitId;

  ///是否开启sku定价管理，若不为1，则未开启，此时[skuId]为0
  int? skuPrice;

  ///零售价
  num? retailPrice;

  ///门店价格
  num? otypePrice;

  ///会员价
  num? vipPrice;

  PtypePriceDto copyWith({
    String? ptypeId,
    String? skuId,
    String? unitId,
    int? skuPrice,
    num? retailPrice,
    num? otypePrice,
    num? vipPrice,
  }) =>
      PtypePriceDto(
        ptypeId: ptypeId ?? this.ptypeId,
        skuId: skuId ?? this.skuId,
        unitId: unitId ?? this.unitId,
        skuPrice: skuPrice ?? this.skuPrice,
        retailPrice: retailPrice ?? this.retailPrice,
        otypePrice: otypePrice ?? this.otypePrice,
        vipPrice: vipPrice ?? this.vipPrice,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['ptypeId'] = ptypeId;
    map['skuId'] = skuId;
    map['unitId'] = unitId;
    map['skuPrice'] = skuPrice;
    map['retailPrice'] = retailPrice;
    map['otypePrice'] = otypePrice;
    map['vipPrice'] = vipPrice;
    return map;
  }
}
