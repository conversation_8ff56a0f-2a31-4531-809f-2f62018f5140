import '../../../common/base_type_util.dart';
import '../../entity/ptype/ptype_dto.dart';
import '../../entity/ptype/ptype_serial_no_dto.dart';
import '../../tool/decimal_display_helper.dart';
import '../../tool/props_helper.dart';
import '../../../enum/bill_type.dart';
import 'package:haloui/utils/math_util.dart';

import '../goods_detail_dto.dart';

class PtypeListModel {
  ///是否仅仅是商品，而不是unitSku商品
  /// 若为true，则在{@link #unitSkuPtypeList}取该商品下所有的unitSku商品
  bool? onlyPtype;

  /// 当按照商品维度搜索时，返回的unitSku商品列表
  List<PtypeListModel>? unitSkuPtypeList;

  String? saleOtypeVipPrice;

  num? stockQty; //库存
  num? presetPrice; //预设售价
  num? count = 1; //数量

  String id = "";
  String? profileId;
  String? typeid;
  String? partypeid;

  ///商品标签ID列表
  List<String>? labelIds;
  String? usercode;
  String fullname = "";
  String? shortname;
  String? namepy;
  bool? classed;
  bool? stoped;
  bool? deleted;
  String? rowindex;
  String? barcode;
  String fullbarcode = "";
  String? standard;
  String? ptypeType;
  String? ptypeArea;
  String? memo;
  int? createType;
  int? costMode;
  dynamic discount;

  //商品类型 0位实物，1为虚拟
  int? pcategory;
  String? taxnumber;
  double? taxRate;
  dynamic costPrice;
  String? supplyInfo;
  String? brandId;
  bool? ktypeLimit;
  int? snenabled;
  bool? propenabled;
  bool? batchenabled;
  int? protectDays;
  int? protectDaysUnit;
  int? protectDaysView;
  int? protectWarndays;
  int? protectWarndaysUnit;
  num? weight;
  int? weightUnit;
  bool? lithiumBattery;
  bool? solid;
  String? difficultyLevel;
  bool? weighted;
  String? retailDefaultUnit;
  String? saleDefaultUnit;
  String? buyDefaultUnit;
  String? stockDefaultUnit;
  dynamic syncStock;
  String? ptypeLength;
  String? ptypeWidth;
  String? ptypeHeight;
  dynamic lengthUnit;
  String? createTime;
  String? updateTime;
  String? picUrl = "";
  dynamic brandName;
  String? parId;
  String? parFullname;
  UnitsBean? unit;
  String? qty;
  String? subQty;
  num? total;

  List<PtypeSerialNoDto> serialNoList = [];
  List<UnitsBean>? unitList;
  SkuBean? sku;

  String? batchNo;
  num? batchPrice;
  String? produceDate;
  String? expireDate;

  ///是否sku定价
  int? skuPrice;

  ///单价
  num? currencyPrice;

  ///是否套餐行
  bool comboRow = false;

  ///套餐id
  String? comboId;

  ///套餐明细行分摊比例
  num? scale;

  ///套餐赠品标记
  bool gift = false;

  ///成本id
  String? costId;

  ///套餐明细行
  List<PtypeListModel>? comboDetails;

  String? xcode;

  static PtypeListModel fromMap(Map<String?, dynamic>? map) {
    if (map == null) return PtypeListModel();
    PtypeListModel ptypeAndSkuDtoBean = PtypeListModel();
    ptypeAndSkuDtoBean.saleOtypeVipPrice = map["saleOtypeVipPrice"]?.toString();
    ptypeAndSkuDtoBean.stockQty = map["stockQty"];
    ptypeAndSkuDtoBean.presetPrice = map["presetPrice"];
    ptypeAndSkuDtoBean.id = map['id'];
    ptypeAndSkuDtoBean.profileId = map['profileId'];
    ptypeAndSkuDtoBean.typeid = map['typeid'];
    ptypeAndSkuDtoBean.labelIds =
        (map['labelIds'] as List?)?.cast<String>() ?? [];
    ptypeAndSkuDtoBean.partypeid = map['partypeid'];
    ptypeAndSkuDtoBean.usercode = map['usercode'];
    ptypeAndSkuDtoBean.fullname = map['fullname'];
    ptypeAndSkuDtoBean.shortname = map['shortname'];
    ptypeAndSkuDtoBean.namepy = map['namepy'];
    ptypeAndSkuDtoBean.classed = BaseTypeUtil.toBoolean(map['classed']);
    ptypeAndSkuDtoBean.stoped = map['stoped'];
    ptypeAndSkuDtoBean.deleted = map['deleted'];
    ptypeAndSkuDtoBean.rowindex = map['rowindex'];
    ptypeAndSkuDtoBean.barcode = map['barcode'];
    ptypeAndSkuDtoBean.fullbarcode = map['fullbarcode'] ?? "";
    ptypeAndSkuDtoBean.standard = map['standard'];
    ptypeAndSkuDtoBean.ptypeType = map['ptypeType'];
    ptypeAndSkuDtoBean.ptypeArea = map['ptypeArea'];
    ptypeAndSkuDtoBean.memo = map['memo'];
    ptypeAndSkuDtoBean.createType = map['createType'];
    ptypeAndSkuDtoBean.costMode = map['costMode'];
    ptypeAndSkuDtoBean.pcategory = map['pcategory'];
    ptypeAndSkuDtoBean.taxnumber = map['taxnum?ber'];
    ptypeAndSkuDtoBean.taxRate = map['taxRate'];
    ptypeAndSkuDtoBean.costPrice = map['costPrice'];
    ptypeAndSkuDtoBean.discount = (map['discount'] as num?)?.toDouble();
    ptypeAndSkuDtoBean.supplyInfo = map['supplyInfo'];
    ptypeAndSkuDtoBean.brandId = map['brandId'];
    ptypeAndSkuDtoBean.ktypeLimit = BaseTypeUtil.toBoolean(map['ktypeLimit']);
    ptypeAndSkuDtoBean.snenabled = map['snenabled'];
    ptypeAndSkuDtoBean.propenabled = BaseTypeUtil.toBoolean(map['propenabled']);
    ptypeAndSkuDtoBean.batchenabled = BaseTypeUtil.toBoolean(
      map['batchenabled'],
    );
    ptypeAndSkuDtoBean.protectDays = map['protectDays'];
    ptypeAndSkuDtoBean.protectDaysUnit = map['protectDaysUnit'];
    ptypeAndSkuDtoBean.protectDaysView = map['protectDaysView'];
    ptypeAndSkuDtoBean.protectWarndays = map['protectWarndays'];
    ptypeAndSkuDtoBean.protectWarndaysUnit = map['protectWarndaysUnit'];
    ptypeAndSkuDtoBean.weight = map['weight'] ?? 0;
    ptypeAndSkuDtoBean.weightUnit = map['weightUnit'];
    ptypeAndSkuDtoBean.lithiumBattery = map['lithiumBattery'];
    ptypeAndSkuDtoBean.solid = map['solid'];
    ptypeAndSkuDtoBean.difficultyLevel = map['difficultyLevel'].toString();
    ptypeAndSkuDtoBean.weighted = map['weighted'];
    ptypeAndSkuDtoBean.retailDefaultUnit = map['retailDefaultUnit'];
    ptypeAndSkuDtoBean.saleDefaultUnit = map['saleDefaultUnit'];
    ptypeAndSkuDtoBean.buyDefaultUnit = map['buyDefaultUnit'];
    ptypeAndSkuDtoBean.stockDefaultUnit = map['stockDefaultUnit'];
    ptypeAndSkuDtoBean.syncStock = map['syncStock'];
    ptypeAndSkuDtoBean.ptypeLength = map['ptypeLength'].toString();
    ptypeAndSkuDtoBean.ptypeWidth = map['ptypeWidth'].toString();
    ptypeAndSkuDtoBean.ptypeHeight = map['ptypeHeight'].toString();
    ptypeAndSkuDtoBean.lengthUnit = map['lengthUnit'];
    ptypeAndSkuDtoBean.createTime = map['createTime'];
    ptypeAndSkuDtoBean.updateTime = map['updateTime'];
    ptypeAndSkuDtoBean.picUrl = map['picUrl'];
    ptypeAndSkuDtoBean.brandName = map['brandName'];
    ptypeAndSkuDtoBean.parId = map['parId'];
    ptypeAndSkuDtoBean.parFullname = map['parFullname'];
    ptypeAndSkuDtoBean.unit = UnitsBean.fromMap(map['unit']);
    ptypeAndSkuDtoBean.qty = map['qty'].toString();
    ptypeAndSkuDtoBean.subQty = map['subQty'].toString();
    ptypeAndSkuDtoBean.total = map['total'];
    ptypeAndSkuDtoBean.serialNoList =
        (map['serialNoList'] as List?)
            ?.map((o) => PtypeSerialNoDto.fromMap(o))
            .toList() ??
        [];
    ptypeAndSkuDtoBean.sku = SkuBean.fromMap(map["sku"]);
    ptypeAndSkuDtoBean.unitList =
        (map['unitList'] as List?)?.map((o) {
          if (o is UnitsBean) {
            return o;
          }
          return UnitsBean.fromMap(o);
        }).toList() ??
        [];
    ptypeAndSkuDtoBean.batchNo = map['batchNo'];
    ptypeAndSkuDtoBean.batchPrice = map['batchPrice'];
    ptypeAndSkuDtoBean.expireDate = map['expireDate'];
    ptypeAndSkuDtoBean.produceDate = map['produceDate'];
    ptypeAndSkuDtoBean.skuPrice = map['skuPrice'];
    ptypeAndSkuDtoBean.count = map['count'];
    ptypeAndSkuDtoBean.currencyPrice = map['currencyPrice'];
    ptypeAndSkuDtoBean.comboRow =
        BaseTypeUtil.toBoolean(map['comboRow'], false)!;
    ptypeAndSkuDtoBean.comboId = map['comboId'];
    ptypeAndSkuDtoBean.scale = map['scale'];
    ptypeAndSkuDtoBean.gift = map['gift'] ?? false;
    ptypeAndSkuDtoBean.xcode = map['xcode'];
    ptypeAndSkuDtoBean.comboDetails =
        (map['comboDetails'] as List?)
            ?.map((e) => PtypeListModel.fromMap(e))
            .toList() ??
        [];
    ptypeAndSkuDtoBean.costId = map['costId'];
    ptypeAndSkuDtoBean.onlyPtype = map['onlyPtype'] ?? false;
    ptypeAndSkuDtoBean.unitSkuPtypeList =
        (map['unitSkuPtypeList'] as List?)
            ?.map((e) => PtypeListModel.fromMap(e))
            .toList() ??
        [];
    return ptypeAndSkuDtoBean;
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "profileId": profileId,
    "saleOtypeVipPrice": saleOtypeVipPrice,
    "typeid": typeid,
    "labelIds": labelIds,
    "partypeid": partypeid,
    "usercode": usercode,
    "fullname": fullname,
    "shortname": shortname,
    "namepy": namepy,
    "classed": classed,
    "stoped": stoped,
    "deleted": deleted,
    "rowindex": rowindex,
    "barcode": barcode,
    "fullbarcode": fullbarcode,
    "standard": standard,
    "ptypeType": ptypeType,
    "ptypeArea": ptypeArea,
    "memo": memo,
    "createType": createType,
    "costMode": costMode,
    "discount": discount,
    "pcategory": pcategory,
    "taxnumber": taxnumber,
    "taxRate": taxRate,
    "costPrice": costPrice,
    "supplyInfo": supplyInfo,
    "brandId": brandId,
    "ktypeLimit": ktypeLimit,
    "snenabled": snenabled,
    "propenabled": propenabled,
    "batchenabled": batchenabled,
    "protectDays": protectDays,
    "protectDaysUnit": protectDaysUnit,
    "protectDaysView": protectDaysView,
    "protectWarndays": protectWarndays,
    "protectWarndaysUnit": protectWarndaysUnit,
    "weight": weight,
    "weightUnit": weightUnit,
    "lithiumBattery": lithiumBattery,
    "solid": solid,
    "difficultyLevel": difficultyLevel,
    "weighted": weighted,
    "retailDefaultUnit": retailDefaultUnit,
    "saleDefaultUnit": saleDefaultUnit,
    "buyDefaultUnit": buyDefaultUnit,
    "stockDefaultUnit": stockDefaultUnit,
    "syncStock": syncStock,
    "ptypeLength": ptypeLength,
    "ptypeWidth": ptypeWidth,
    "ptypeHeight": ptypeHeight,
    "lengthUnit": lengthUnit,
    "createTime": createTime,
    "updateTime": updateTime,
    "picUrl": picUrl,
    "brandName": brandName,
    "parId": parId,
    "parFullname": parFullname,
    "unit": unit?.toJson(),
    "qty": qty,
    "subQty": subQty,
    "total": total,
    "serialNoList": serialNoList,
    "sku": sku?.toJson(),
    "unitList": unitList,
    "batchNo": batchNo,
    "batchPrice": batchPrice,
    "expireDate": expireDate,
    "produceDate": produceDate,
    "skuPrice": skuPrice,
    "count": count,
    "currencyPrice": currencyPrice,
    "comboRow": comboRow,
    "comboId": comboId,
    "scale": scale,
    "gift": gift,
    "comboDetails": comboDetails,
    "costId": costId,
    "onlyPtype": onlyPtype,
    "unitSkuPtypeList": unitSkuPtypeList,
    "xcode": xcode,
  };

  ///商品实体类转化 Ptype to GoodsDetail 仅开单选择、扫描商品时使用此转换
  GoodsDetailDto changeModel({String? vchtype}) {
    GoodsDetailDto detailDto = GoodsDetailDto();
    detailDto.saleOtypeVipPrice = saleOtypeVipPrice;
    detailDto.brandName = brandName ?? "";
    detailDto.ptypeId = id;
    detailDto.pUserCode = usercode;
    detailDto.pFullName = fullname;
    detailDto.typeid = typeid;
    detailDto.labelIds = labelIds;
    detailDto.stockQty = stockQty;
    detailDto.fullbarcode = fullbarcode;
    detailDto.ptypetype = ptypeType ?? "";
    detailDto.standard = standard ?? "";
    detailDto.taxRate = taxRate ?? 0;
    detailDto.snenabled = snenabled ?? 0;
    detailDto.propenabled = propenabled ?? false;
    detailDto.batchenabled = batchenabled ?? false;
    detailDto.produceDate = produceDate ?? "";
    detailDto.expireDate = expireDate;
    detailDto.batchNo = batchNo ?? "";
    detailDto.batchPrice = batchPrice ?? 0;
    detailDto.costId = costId;
    detailDto.protectDays = protectDays;
    detailDto.costMode = costMode ?? 0;
    detailDto.picUrl =
        sku?.picUrl?.isNotEmpty == true ? sku?.picUrl : picUrl ?? "";
    detailDto.unitQty = count ?? 0;
    detailDto.currencyPrice = currencyPrice ?? 0;
    detailDto.currencyTotal =
        MathUtil.stringToDouble(
          DecimalDisplayHelper.getTotalFixed(
            MathUtil.multiplication(
              count.toString(),
              currencyPrice.toString(),
            ).toString(),
          ),
        )!;
    detailDto.weight = weight;
    detailDto.weightUnit = weightUnit;
    if (unit != null) {
      detailDto.unitId = unit!.id.toString();
      detailDto.unitCode = unit!.unitCode ?? 0;
      detailDto.unitName = unit!.unitName ?? "";
      detailDto.unitRate = unit!.unitRate;
      //toDo 后期进销存换成新的方式以后，需要直接去ptypeWeight，目前使用老方式，使用 基础单位重量*unitrate
      //  detailDto.weight = unit!.ptypeWeight;
      // detailDto.weight =
      //     MathUtil.multiplyDec(detailDto.weight ?? 0, unit!.unitRate ?? 0).toDouble();
    }
    detailDto.costPrice = costPrice;
    detailDto.costTotal = MathUtil.stringToDouble(
      DecimalDisplayHelper.getTotalFixed(
        MathUtil.multiplication(
          detailDto.costPrice.toString(),
          detailDto.unitQty.toString(),
        ).toString(),
      ),
    );
    detailDto.vchcode = null;
    detailDto.vchtype = vchtype ?? BillTypeData[BillType.SaleBill];
    detailDto.discount = discount ?? 1;
    detailDto.currencyDisedPrice =
        MathUtil.stringToDouble(
          DecimalDisplayHelper.getPriceFixed(
            MathUtil.multiplication(
              detailDto.currencyPrice.toString(),
              detailDto.discount.toString(),
            ).toString(),
          ),
        )!;
    detailDto.currencyDisedTotal =
        MathUtil.stringToDouble(
          DecimalDisplayHelper.getTotalFixed(
            MathUtil.multiplication(
              detailDto.currencyDisedPrice.toString(),
              detailDto.unitQty.toString(),
            ).toString(),
          ),
        )!;
    detailDto.currencyTaxTotal = 0;
    detailDto.currencyDisedTaxedTotal = detailDto.currencyDisedTotal;
    detailDto.currencyDisedTaxedPrice = detailDto.currencyDisedPrice;
    detailDto.serialNoList = serialNoList;
    if (detailDto.snenabled == 1 &&
        detailDto.serialNoList != null &&
        detailDto.serialNoList.isNotEmpty) {
      PtypeSerialNoDto serialNoDto = detailDto.serialNoList[0];
      detailDto.kfullname = serialNoDto.kfullname;
      detailDto.ktypeId = serialNoDto.ktypeId;
      detailDto.batchNo = serialNoDto.batchNo ?? "";
      detailDto.expireDate = serialNoDto.expireDate;
      detailDto.produceDate = serialNoDto.produceDate ?? "";
    }
    detailDto.unitList = unitList;

    //处理sku，注意套餐行没有sku
    if (sku != null && comboRow != true) {
      detailDto.skuId = sku!.id!;
      detailDto.prop = PropsHelper.getPropListBySkuProps(sku);
      detailDto.skuName = PropsHelper.buildGoodsPropertyString(
        detailDto.prop ?? [],
      );
      detailDto.propFormat = sku!.propNames ?? "";
    }
    //设置 detailDto.propValues
    PropsHelper.setPropValueString(detailDto);
    detailDto.skuPrice = skuPrice;
    //商品类型 0为实物，1为虚拟
    detailDto.pcategory = pcategory ?? 0;
    //套餐
    detailDto.comboRow = comboRow ?? false;
    detailDto.comboId = comboId ?? "";
    detailDto.comboShareScale = scale ?? 0;
    detailDto.gift = gift;
    detailDto.comboQtyRate = count;
    detailDto.xcode = xcode;
    if (detailDto.skuId == null && comboRow) {
      detailDto.skuId = "0";
    }
    return detailDto;
  }
}
