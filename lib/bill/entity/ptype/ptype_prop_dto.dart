/**     
  * @ClassName:      ptype_prop_dto.dart
   * @CreateDate:    2020/3/30 14:57
  * @Author:         tlan
  * @Description:   商品属性对象
*/

class PtypePropDto {
  dynamic propIndex;
  String? propvalueId;
  String? propvalueName;

  static PtypePropDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return PtypePropDto();
    PtypePropDto ptypePropDto = PtypePropDto();
    ptypePropDto.propIndex = map['propIndex'];
    ptypePropDto.propvalueId = map['propvalueId'] ?? map['propValueId'];
    ptypePropDto.propvalueName = map['propvalueName'] ?? map['propValueName'] ;
    return ptypePropDto;
  }

  Map toJson() => {
    "propIndex": propIndex,
    "propValueId": propvalueId,
    "propValueName": propvalueName,
  };

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PtypePropDto &&
          runtimeType == other.runtimeType &&
          propvalueId == other.propvalueId;

  @override
  int get hashCode => propvalueId.hashCode;
}
