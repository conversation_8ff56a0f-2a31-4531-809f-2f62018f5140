class PtypeSku {
  String? createTime;
  String? fullbarcode = "";
  String? id;
  String? profileId;
  String? propId1;
  String? propId2;
  String? propId3;
  String? propId4;
  String? propId5;
  String? propId6;
  String? propName1;
  String? propName2;
  String? propName3;
  String? propName4;
  String? propName5;
  String? propName6;
  String? propvalueId1;
  String? propvalueId2;
  String? propvalueId3;
  String? propvalueId4;
  String? propvalueId5;
  String? propvalueId6;
  String? propvalueName1 = "";
  String? propvalueName2 = "";
  String? propvalueName3;
  String? propvalueName4;
  String? propvalueName5;
  String? propvalueName6;
  String? ptypeId;
  bool? stoped;
  String? updateTime;

  String? picUrl;
  String? propNames;
  String? propvalueNames;
  String? deleted;
  String? costPrice;

  static PtypeSku fromMap(Map<String?, dynamic>? map) {
    if (map == null) return PtypeSku();
    PtypeSku skuBean = PtypeSku();
    skuBean.createTime = map['createTime'];
    skuBean.fullbarcode = map['fullbarcode'] ?? "";
    skuBean.picUrl = map['picUrl'] ?? "";
    skuBean.propNames = map['propNames'] ?? "";
    skuBean.propvalueNames = map['propvalueNames'] ?? "";
    skuBean.deleted = map['deleted'] ?? "";
    skuBean.costPrice = map['costPrice'] ?? "";
    skuBean.id = map['id'];
    skuBean.profileId = map['profileId'];
    skuBean.propId1 = map['propId1'];
    skuBean.propId2 = map['propId2'];
    skuBean.propId3 = map['propId3'];
    skuBean.propId4 = map['propId4'];
    skuBean.propId5 = map['propId5'];
    skuBean.propId6 = map['propId6'];
    skuBean.propName1 = map['propName1'];
    skuBean.propName2 = map['propName2'];
    skuBean.propName3 = map['propName3'];
    skuBean.propName4 = map['propName4'];
    skuBean.propName5 = map['propName5'];
    skuBean.propName6 = map['propName6'];
    skuBean.propvalueId1 = map['propvalueId1'];
    skuBean.propvalueId2 = map['propvalueId2'];
    skuBean.propvalueId3 = map['propvalueId3'];
    skuBean.propvalueId4 = map['propvalueId4'];
    skuBean.propvalueId5 = map['propvalueId5'];
    skuBean.propvalueId6 = map['propvalueId6'];
    skuBean.propvalueName1 = map['propvalueName1'] ?? "";
    skuBean.propvalueName2 = map['propvalueName2'] ?? "";
    skuBean.propvalueName3 = map['propvalueName3'];
    skuBean.propvalueName4 = map['propvalueName4'];
    skuBean.propvalueName5 = map['propvalueName5'];
    skuBean.propvalueName6 = map['propvalueName6'];
    skuBean.ptypeId = map['ptypeId'];
    skuBean.stoped = map['stoped'];
    skuBean.updateTime = map['updateTime'];
    return skuBean;
  }

  Map toJson() => {
        "createTime": createTime,
        "fullbarcode": fullbarcode,
        "picUrl": picUrl,
        "propNames": propNames,
        "propvalueNames": propvalueNames,
        "deleted": deleted,
        "costPrice": costPrice,
        "id": id,
        "profileId": profileId,
        "propId1": propId1,
        "propId2": propId2,
        "propId3": propId3,
        "propId4": propId4,
        "propId5": propId5,
        "propId6": propId6,
        "propName1": propName1,
        "propName2": propName2,
        "propName3": propName3,
        "propName4": propName4,
        "propName5": propName5,
        "propName6": propName6,
        "propvalueId1": propvalueId1,
        "propvalueId2": propvalueId2,
        "propvalueId3": propvalueId3,
        "propvalueId4": propvalueId4,
        "propvalueId5": propvalueId5,
        "propvalueId6": propvalueId6,
        "propvalueName1": propvalueName1,
        "propvalueName2": propvalueName2,
        "propvalueName3": propvalueName3,
        "propvalueName4": propvalueName4,
        "propvalueName5": propvalueName5,
        "propvalueName6": propvalueName6,
        "ptypeId": ptypeId,
        "stoped": stoped,
        "updateTime": updateTime,
      };
}
