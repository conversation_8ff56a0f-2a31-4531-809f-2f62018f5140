

import 'ptype_dto.dart';

/// parId : null
/// parFullname : null
/// ptype : {"id":"589408601262870701","profileId":null,"typeid":null,"partypeid":null,"usercode":"dsxsp","fullname":"单属性商品","shortname":"单属性商","namepy":"dsxsp","classed":null,"stoped":false,"deleted":null,"rowindex":null,"barcode":null,"standard":"","ptypeType":"","ptypeArea":"","memo":"","createType":null,"costMode":null,"pcategory":null,"taxNumber":"","taxRate":0,"costPrice":0,"supplyInfo":null,"brandId":"0","ktypeLimit":null,"snenabled":0,"propenabled":true,"batchenabled":false,"protectDays":null,"protectDaysUnit":null,"protectWarndays":null,"protectWarndaysUnit":null,"weight":0,"weightUnit":1,"lithiumBattery":null,"solid":null,"difficultyLevel":null,"weighted":null,"retailDefaultUnit":null,"saleDefaultUnit":null,"buyDefaultUnit":null,"stockDefaultUnit":null,"syncStock":null,"ptypeLength":0,"ptypeWidth":0,"ptypeHeight":0,"lengthUnit":0,"createTime":null,"updateTime":null}
/// brandtype : {"id":"0","profileId":null,"brandName":null,"stoped":null,"createTime":null,"updateTime":null}
/// ptypePic : null
/// sku : {"id":"589408601262886695","profileId":null,"ptypeId":"589408601262870701","fullbarcode":null,"stoped":false,"propId1":"589408605557837997","propName1":"材质","propvalueId1":"589408601262870674","propvalueName1":"金","propId2":null,"propName2":null,"propvalueId2":null,"propvalueName2":null,"propId3":null,"propName3":null,"propvalueId3":null,"propvalueName3":null,"propId4":null,"propName4":null,"propvalueId4":null,"propvalueName4":null,"propId5":null,"propName5":null,"propvalueId5":null,"propvalueName5":null,"propId6":null,"propName6":null,"propvalueId6":null,"propvalueName6":null,"createTime":null,"updateTime":null}
/// unit : {"id":"589408603410354349","profileId":null,"ptypeId":"589408601262870701","unitCode":1,"unitName":"个","unitRate":1,"barcode":"","retailPrice":2E+1,"preprice1":2E+1,"preprice2":0,"preprice3":0,"preprice4":0,"preprice5":0,"preprice6":0,"preprice7":0,"preprice8":0,"preprice9":0,"preprice10":0,"buyPrice":8,"minSalePrice":1E+1,"createTime":null,"updateTime":null,"lastSalePrice":0,"lastBuyPrice":0,"lastSaleTime":"1989-12-31T16:00:00.000+0000","lastBuyTime":"1989-12-31T16:00:00.000+0000"}
/// fullbarcode : {"id":null,"profileId":null,"ptypeId":null,"skuId":null,"unitId":null,"fullbarcode":"222","defaulted":null,"createTime":null,"updateTime":null}
/// fullbarcodes : null
/// xcode : null
/// xcodes : null
/// qty : null
/// subQty : null

class PtypeUnitSkuPo {
  String? parId;
  String? parFullname;
  PtypeBean? ptype;
  BrandtypeBean? brandtype;
  PicsBean? ptypePic;
  UnitSkuBean? sku;
  CheckPtypeUnitBean? unit;
  FullbarcodeBean? fullbarcode;
  List? fullbarcodes;
  String? xcode;
  List? xcodes;
  String? qty;
  String? subQty;

  static PtypeUnitSkuPo fromMap(Map<String?, dynamic>? map) {
    if (map == null) return PtypeUnitSkuPo();
    PtypeUnitSkuPo ptypeUnitSkuPoBean = PtypeUnitSkuPo();
    ptypeUnitSkuPoBean.parId = map['parId'];
    ptypeUnitSkuPoBean.parFullname = map['parFullname'];
    ptypeUnitSkuPoBean.ptype = PtypeBean.fromMap(map['ptype']);
    ptypeUnitSkuPoBean.brandtype = BrandtypeBean.fromMap(map['brandtype']);
    ptypeUnitSkuPoBean.ptypePic = PicsBean.fromMap(map['ptypePic']);
    ptypeUnitSkuPoBean.sku = UnitSkuBean.fromMap(map['sku']);
    ptypeUnitSkuPoBean.unit = CheckPtypeUnitBean.fromMap(map['unit']);
    ptypeUnitSkuPoBean.fullbarcode =
        FullbarcodeBean.fromMap(map['fullbarcode']);
    ptypeUnitSkuPoBean.fullbarcodes = map['fullbarcodes'];
    ptypeUnitSkuPoBean.xcode = map['xcode'];
    ptypeUnitSkuPoBean.xcodes = map['xcodes'];
    ptypeUnitSkuPoBean.qty = map['qty'];
    ptypeUnitSkuPoBean.subQty = map['subQty'];
    return ptypeUnitSkuPoBean;
  }

  Map toJson() => {
        "parId": parId,
        "parFullname": parFullname,
        "ptype": ptype,
        "brandtype": brandtype,
        "ptypePic": ptypePic,
        "sku": sku,
        "unit": unit,
        "fullbarcode": fullbarcode,
        "fullbarcodes": fullbarcodes,
        "xcode": xcode,
        "xcodes": xcodes,
        "qty": qty,
        "subQty": subQty,
      };
}

/// id : null
/// profileId : null
/// ptypeId : null
/// skuId : null
/// unitId : null
/// fullbarcode : "222"
/// defaulted : null
/// createTime : null
/// updateTime : null

class FullbarcodeBean {
  String? id;
  String? profileId;
  String? ptypeId;
  String? skuId;
  String? unitId;
  String? fullbarcode;
  bool? defaulted;
  String? createTime;
  String? updateTime;

  static FullbarcodeBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return FullbarcodeBean();
    FullbarcodeBean fullbarcodeBean = FullbarcodeBean();
    fullbarcodeBean.id = map['id'];
    fullbarcodeBean.profileId = map['profileId'];
    fullbarcodeBean.ptypeId = map['ptypeId'];
    fullbarcodeBean.skuId = map['skuId'];
    fullbarcodeBean.unitId = map['unitId'];
    fullbarcodeBean.fullbarcode = map['fullbarcode'];
    fullbarcodeBean.defaulted = map['defaulted'];
    fullbarcodeBean.createTime = map['createTime'];
    fullbarcodeBean.updateTime = map['updateTime'];
    return fullbarcodeBean;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "skuId": skuId,
        "unitId": unitId,
        "fullbarcode": fullbarcode,
        "defaulted": defaulted,
        "createTime": createTime,
        "updateTime": updateTime,
      };
}

/// id : "589408603410354349"
/// profileId : null
/// ptypeId : "589408601262870701"
/// unitCode : 1
/// unitName : "个"
/// unitRate : 1
/// barcode : ""
/// retailPrice : 2E+1
/// preprice1 : 2E+1
/// preprice2 : 0
/// preprice3 : 0
/// preprice4 : 0
/// preprice5 : 0
/// preprice6 : 0
/// preprice7 : 0
/// preprice8 : 0
/// preprice9 : 0
/// preprice10 : 0
/// buyPrice : 8
/// minSalePrice : 1E+1
/// createTime : null
/// updateTime : null
/// lastSalePrice : 0
/// lastBuyPrice : 0
/// lastSaleTime : "1989-12-31T16:00:00.000+0000"
/// lastBuyTime : "1989-12-31T16:00:00.000+0000"

class CheckPtypeUnitBean {
  String? id;
  String? profileId;
  String? ptypeId;
  int? unitCode;
  String? unitName;
  String? unitRate;
  String? barcode;
  String? retailPrice;
  String? preprice1;
  String? preprice2;
  String? preprice3;
  String? preprice4;
  String? preprice5;
  String? preprice6;
  String? preprice7;
  String? preprice8;
  String? preprice9;
  String? preprice10;
  String? buyPrice;
  String? minSalePrice;
  String? createTime;
  String? updateTime;
  String? lastSalePrice;
  String? lastBuyPrice;
  String? lastSaleTime;
  String? lastBuyTime;

  static CheckPtypeUnitBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return CheckPtypeUnitBean();
    CheckPtypeUnitBean unitBean = CheckPtypeUnitBean();
    unitBean.id = map['id'];
    unitBean.profileId = map['profileId'];
    unitBean.ptypeId = map['ptypeId'];
    unitBean.unitCode = map['unitCode'];
    unitBean.unitName = map['unitName'];
    unitBean.unitRate = map['unitRate'];
    unitBean.barcode = map['barcode'];
    unitBean.retailPrice = map['retailPrice'];
    unitBean.preprice1 = map['preprice1'];
    unitBean.preprice2 = map['preprice2'];
    unitBean.preprice3 = map['preprice3'];
    unitBean.preprice4 = map['preprice4'];
    unitBean.preprice5 = map['preprice5'];
    unitBean.preprice6 = map['preprice6'];
    unitBean.preprice7 = map['preprice7'];
    unitBean.preprice8 = map['preprice8'];
    unitBean.preprice9 = map['preprice9'];
    unitBean.preprice10 = map['preprice10'];
    unitBean.buyPrice = map['buyPrice'];
    unitBean.minSalePrice = map['minSalePrice'];
    unitBean.createTime = map['createTime'];
    unitBean.updateTime = map['updateTime'];
    unitBean.lastSalePrice = map['lastSalePrice'];
    unitBean.lastBuyPrice = map['lastBuyPrice'];
    unitBean.lastSaleTime = map['lastSaleTime'];
    unitBean.lastBuyTime = map['lastBuyTime'];
    return unitBean;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "unitCode": unitCode,
        "unitName": unitName,
        "unitRate": unitRate,
        "barcode": barcode,
        "retailPrice": retailPrice,
        "preprice1": preprice1,
        "preprice2": preprice2,
        "preprice3": preprice3,
        "preprice4": preprice4,
        "preprice5": preprice5,
        "preprice6": preprice6,
        "preprice7": preprice7,
        "preprice8": preprice8,
        "preprice9": preprice9,
        "preprice10": preprice10,
        "buyPrice": buyPrice,
        "minSalePrice": minSalePrice,
        "createTime": createTime,
        "updateTime": updateTime,
        "lastSalePrice": lastSalePrice,
        "lastBuyPrice": lastBuyPrice,
        "lastSaleTime": lastSaleTime,
        "lastBuyTime": lastBuyTime,
      };
}

/// id : "589408601262886695"
/// profileId : null
/// ptypeId : "589408601262870701"
/// fullbarcode : null
/// stoped : false
/// propId1 : "589408605557837997"
/// propName1 : "材质"
/// propvalueId1 : "589408601262870674"
/// propvalueName1 : "金"
/// propId2 : null
/// propName2 : null
/// propvalueId2 : null
/// propvalueName2 : null
/// propId3 : null
/// propName3 : null
/// propvalueId3 : null
/// propvalueName3 : null
/// propId4 : null
/// propName4 : null
/// propvalueId4 : null
/// propvalueName4 : null
/// propId5 : null
/// propName5 : null
/// propvalueId5 : null
/// propvalueName5 : null
/// propId6 : null
/// propName6 : null
/// propvalueId6 : null
/// propvalueName6 : null
/// createTime : null
/// updateTime : null

class UnitSkuBean {
  String? picUrl;
  String? id;
  String? profileId;
  String? ptypeId;
  String? fullbarcode;
  bool? stoped;
  String? propId1;
  String? propName1;
  String? propvalueId1;
  String? propvalueName1;
  String? propId2;
  String? propName2;
  String? propvalueId2;
  String? propvalueName2;
  String? propId3;
  String? propName3;
  String? propvalueId3;
  String? propvalueName3;
  String? propId4;
  String? propName4;
  String? propvalueId4;
  String? propvalueName4;
  String? propId5;
  String? propName5;
  String? propvalueId5;
  String? propvalueName5;
  String? propId6;
  String? propName6;
  String? propvalueId6;
  String? propvalueName6;
  String? createTime;
  String? updateTime;
  String? stockQty; //账面库存数量
  String? qty; //数量
  bool? checked; //是否盘点

  static UnitSkuBean fromMap(Map<dynamic, dynamic>? map) {
    if (map == null) return UnitSkuBean();
    UnitSkuBean skuBean = UnitSkuBean();
    skuBean.id = map['id'];
    skuBean.picUrl = map['picUrl'] ?? "";
    skuBean.profileId = map['profileId'];
    skuBean.ptypeId = map['ptypeId'];
    skuBean.fullbarcode = map['fullbarcode'];
    skuBean.stoped = map['stoped'];
    skuBean.propId1 = map['propId1'];
    skuBean.propName1 = map['propName1'];
    skuBean.propvalueId1 = map['propvalueId1'];
    skuBean.propvalueName1 = map['propvalueName1'];
    skuBean.propId2 = map['propId2'];
    skuBean.propName2 = map['propName2'];
    skuBean.propvalueId2 = map['propvalueId2'];
    skuBean.propvalueName2 = map['propvalueName2'];
    skuBean.propId3 = map['propId3'];
    skuBean.propName3 = map['propName3'];
    skuBean.propvalueId3 = map['propvalueId3'];
    skuBean.propvalueName3 = map['propvalueName3'];
    skuBean.propId4 = map['propId4'];
    skuBean.propName4 = map['propName4'];
    skuBean.propvalueId4 = map['propvalueId4'];
    skuBean.propvalueName4 = map['propvalueName4'];
    skuBean.propId5 = map['propId5'];
    skuBean.propName5 = map['propName5'];
    skuBean.propvalueId5 = map['propvalueId5'];
    skuBean.propvalueName5 = map['propvalueName5'];
    skuBean.propId6 = map['propId6'];
    skuBean.propName6 = map['propName6'];
    skuBean.propvalueId6 = map['propvalueId6'];
    skuBean.propvalueName6 = map['propvalueName6'];
    skuBean.createTime = map['createTime'];
    skuBean.updateTime = map['updateTime'];
    skuBean.stockQty = map['stockQty'];
    skuBean.qty = map['qty'];
    skuBean.checked = map['checked'];
    return skuBean;
  }

  Map<String?, dynamic> toJson() => {
        "picUrl": picUrl,
        "id": id,
        "profileId": profileId,
        "ptypeId": ptypeId,
        "fullbarcode": fullbarcode,
        "stoped": stoped,
        "propId1": propId1,
        "propName1": propName1,
        "propvalueId1": propvalueId1,
        "propvalueName1": propvalueName1,
        "propId2": propId2,
        "propName2": propName2,
        "propvalueId2": propvalueId2,
        "propvalueName2": propvalueName2,
        "propId3": propId3,
        "propName3": propName3,
        "propvalueId3": propvalueId3,
        "propvalueName3": propvalueName3,
        "propId4": propId4,
        "propName4": propName4,
        "propvalueId4": propvalueId4,
        "propvalueName4": propvalueName4,
        "propId5": propId5,
        "propName5": propName5,
        "propvalueId5": propvalueId5,
        "propvalueName5": propvalueName5,
        "propId6": propId6,
        "propName6": propName6,
        "propvalueId6": propvalueId6,
        "propvalueName6": propvalueName6,
        "createTime": createTime,
        "updateTime": updateTime,
        "stockQty": stockQty,
        "qty": qty,
        "checked": checked,
      };
}

/// id : "0"
/// profileId : null
/// brandName : null
/// stoped : null
/// createTime : null
/// updateTime : null

class BrandtypeBean {
  String? id;
  String? profileId;
  String? brandName;
  String? stoped;
  String? createTime;
  String? updateTime;

  static BrandtypeBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return BrandtypeBean();
    BrandtypeBean brandtypeBean = BrandtypeBean();
    brandtypeBean.id = map['id'];
    brandtypeBean.profileId = map['profileId'];
    brandtypeBean.brandName = map['brandName'];
    brandtypeBean.stoped = map['stoped'];
    brandtypeBean.createTime = map['createTime'];
    brandtypeBean.updateTime = map['updateTime'];
    return brandtypeBean;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "brandName": brandName,
        "stoped": stoped,
        "createTime": createTime,
        "updateTime": updateTime,
      };
}

/// id : "589408601262870701"
/// profileId : null
/// typeid : null
/// partypeid : null
/// usercode : "dsxsp"
/// fullname : "单属性商品"
/// shortname : "单属性商"
/// namepy : "dsxsp"
/// classed : null
/// stoped : false
/// deleted : null
/// rowindex : null
/// barcode : null
/// standard : ""
/// ptypeType : ""
/// ptypeArea : ""
/// memo : ""
/// createType : null
/// costMode : null
/// pcategory : null
/// taxNumber : ""
/// taxRate : 0
/// costPrice : 0
/// supplyInfo : null
/// brandId : "0"
/// ktypeLimit : null
/// snenabled : 0
/// propenabled : true
/// batchenabled : false
/// protectDays : null
/// protectDaysUnit : null
/// protectWarndays : null
/// protectWarndaysUnit : null
/// weight : 0
/// weightUnit : 1
/// lithiumBattery : null
/// solid : null
/// difficultyLevel : null
/// weighted : null
/// retailDefaultUnit : null
/// saleDefaultUnit : null
/// buyDefaultUnit : null
/// stockDefaultUnit : null
/// syncStock : null
/// ptypeLength : 0
/// ptypeWidth : 0
/// ptypeHeight : 0
/// lengthUnit : 0
/// createTime : null
/// updateTime : null

class PtypeBean {
  String? id;
  String? profileId;
  String? typeid;
  String? partypeid;
  String? usercode;
  String? fullname;
  String? shortname;
  String? namepy;
  String? classed;
  bool? stoped;
  String? deleted;
  String? rowindex;
  String? barcode;
  String? standard;
  String? ptypeType;
  String? ptypeArea;
  String? memo;
  String? createType;
  String? costMode;
  String? pcategory;
  String? taxNumber;
  String? taxRate;
  String? costPrice;
  String? supplyInfo;
  String? brandId;
  String? ktypeLimit;
  int? snenabled;
  bool? propenabled;
  bool? batchenabled;
  int? protectDays;
  String? protectDaysUnit;
  int? protectWarndays;
  String? protectWarndaysUnit;
  String? weight;
  int? weightUnit;
  String? lithiumBattery;
  String? solid;
  String? difficultyLevel;
  String? weighted;
  String? retailDefaultUnit;
  String? saleDefaultUnit;
  String? buyDefaultUnit;
  String? stockDefaultUnit;
  String? syncStock;
  String? ptypeLength;
  String? ptypeWidth;
  String? ptypeHeight;
  int? lengthUnit;
  String? createTime;
  String? updateTime;

  static PtypeBean fromMap(Map<String?, dynamic>? map) {
    if (map == null) return PtypeBean();
    PtypeBean ptypeBean = PtypeBean();
    ptypeBean.id = map['id'];
    ptypeBean.profileId = map['profileId'];
    ptypeBean.typeid = map['typeid'];
    ptypeBean.partypeid = map['partypeid'];
    ptypeBean.usercode = map['usercode'];
    ptypeBean.fullname = map['fullname'];
    ptypeBean.shortname = map['shortname'];
    ptypeBean.namepy = map['namepy'];
    ptypeBean.classed = map['classed'];
    ptypeBean.stoped = map['stoped'];
    ptypeBean.deleted = map['deleted'];
    ptypeBean.rowindex = map['rowindex'];
    ptypeBean.barcode = map['barcode'];
    ptypeBean.standard = map['standard'];
    ptypeBean.ptypeType = map['ptypeType'];
    ptypeBean.ptypeArea = map['ptypeArea'];
    ptypeBean.memo = map['memo'];
    ptypeBean.createType = map['createType'];
    ptypeBean.costMode = map['costMode'];
    ptypeBean.pcategory = map['pcategory'];
    ptypeBean.taxNumber = map['taxNumber'];
    ptypeBean.taxRate = map['taxRate'];
    ptypeBean.costPrice = map['costPrice'];
    ptypeBean.supplyInfo = map['supplyInfo'];
    ptypeBean.brandId = map['brandId'];
    ptypeBean.ktypeLimit = map['ktypeLimit'];
    ptypeBean.snenabled = map['snenabled'];
    ptypeBean.propenabled = map['propenabled'];
    ptypeBean.batchenabled = map['batchenabled'];
    ptypeBean.protectDays = map['protectDays'];
    ptypeBean.protectDaysUnit = map['protectDaysUnit'];
    ptypeBean.protectWarndays = map['protectWarndays'];
    ptypeBean.protectWarndaysUnit = map['protectWarndaysUnit'];
    ptypeBean.weight = map['weight'];
    ptypeBean.weightUnit = map['weightUnit'];
    ptypeBean.lithiumBattery = map['lithiumBattery'];
    ptypeBean.solid = map['solid'];
    ptypeBean.difficultyLevel = map['difficultyLevel'];
    ptypeBean.weighted = map['weighted'];
    ptypeBean.retailDefaultUnit = map['retailDefaultUnit'];
    ptypeBean.saleDefaultUnit = map['saleDefaultUnit'];
    ptypeBean.buyDefaultUnit = map['buyDefaultUnit'];
    ptypeBean.stockDefaultUnit = map['stockDefaultUnit'];
    ptypeBean.syncStock = map['syncStock'];
    ptypeBean.ptypeLength = map['ptypeLength'];
    ptypeBean.ptypeWidth = map['ptypeWidth'];
    ptypeBean.ptypeHeight = map['ptypeHeight'];
    ptypeBean.lengthUnit = map['lengthUnit'];
    ptypeBean.createTime = map['createTime'];
    ptypeBean.updateTime = map['updateTime'];
    return ptypeBean;
  }

  Map toJson() => {
        "id": id,
        "profileId": profileId,
        "typeid": typeid,
        "partypeid": partypeid,
        "usercode": usercode,
        "fullname": fullname,
        "shortname": shortname,
        "namepy": namepy,
        "classed": classed,
        "stoped": stoped,
        "deleted": deleted,
        "rowindex": rowindex,
        "barcode": barcode,
        "standard": standard,
        "ptypeType": ptypeType,
        "ptypeArea": ptypeArea,
        "memo": memo,
        "createType": createType,
        "costMode": costMode,
        "pcategory": pcategory,
        "taxNumber": taxNumber,
        "taxRate": taxRate,
        "costPrice": costPrice,
        "supplyInfo": supplyInfo,
        "brandId": brandId,
        "ktypeLimit": ktypeLimit,
        "snenabled": snenabled,
        "propenabled": propenabled,
        "batchenabled": batchenabled,
        "protectDays": protectDays,
        "protectDaysUnit": protectDaysUnit,
        "protectWarndays": protectWarndays,
        "protectWarndaysUnit": protectWarndaysUnit,
        "weight": weight,
        "weightUnit": weightUnit,
        "lithiumBattery": lithiumBattery,
        "solid": solid,
        "difficultyLevel": difficultyLevel,
        "weighted": weighted,
        "retailDefaultUnit": retailDefaultUnit,
        "saleDefaultUnit": saleDefaultUnit,
        "buyDefaultUnit": buyDefaultUnit,
        "stockDefaultUnit": stockDefaultUnit,
        "syncStock": syncStock,
        "ptypeLength": ptypeLength,
        "ptypeWidth": ptypeWidth,
        "ptypeHeight": ptypeHeight,
        "lengthUnit": lengthUnit,
        "createTime": createTime,
        "updateTime": updateTime,
      };
}
