
class PtypeFullbarcodeDto {
  String? createTime;
  bool? defaulted;
  String? fullbarcode;
  int? id;
  int? profileId;
  int? ptypeId;
  int? skuId;
  int? unitId;
  String? updateTime;

  static PtypeFullbarcodeDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return PtypeFullbarcodeDto();
    PtypeFullbarcodeDto ptypeFullbarcodeDtoBean = PtypeFullbarcodeDto();
    ptypeFullbarcodeDtoBean.createTime = map['createTime'];
    ptypeFullbarcodeDtoBean.defaulted = map['defaulted'];
    ptypeFullbarcodeDtoBean.fullbarcode = map['fullbarcode'];
    ptypeFullbarcodeDtoBean.id = map['id'];
    ptypeFullbarcodeDtoBean.profileId = map['profileId'];
    ptypeFullbarcodeDtoBean.ptypeId = map['ptypeId'];
    ptypeFullbarcodeDtoBean.skuId = map['skuId'];
    ptypeFullbarcodeDtoBean.unitId = map['unitId'];
    ptypeFullbarcodeDtoBean.updateTime = map['updateTime'];
    return ptypeFullbarcodeDtoBean;
  }

  Map toJson() => {
    "createTime": createTime,
    "defaulted": defaulted,
    "fullbarcode": fullbarcode,
    "id": id,
    "profileId": profileId,
    "ptypeId": ptypeId,
    "skuId": skuId,
    "unitId": unitId,
    "updateTime": updateTime,
  };
}