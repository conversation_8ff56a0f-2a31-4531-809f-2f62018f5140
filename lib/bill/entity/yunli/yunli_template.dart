///
///@ClassName: yunli_platform
///@Description:
///@Author: tanglan
///@Date: 2023/10/26
///
class YunLiTemplate {
  String? ylName;
  String? ylCode;
  String? ylPrice;
  String? ylRemark;
  String? vchcode;
  String? ylChannel;

  static YunLiTemplate fromJson(Map<String?, dynamic>? map) {
    if (map == null) return YunLiTemplate();
    YunLiTemplate feeDto = YunLiTemplate();
    feeDto.ylName = map['ylName'];
    feeDto.ylCode = map['ylCode'];
    feeDto.ylPrice = map['ylPrice'];
    feeDto.ylRemark = map['ylRemark'];
    feeDto.vchcode = map['vchcode'];
    feeDto.ylChannel = map['ylChannel'];

    return feeDto;
  }

  Map<String?, dynamic> toJson() => {
        "ylName": ylName,
        "ylCode": ylCode,
        "ylPrice": ylPrice,
        "ylCode": ylCode,
        "ylRemark": ylRemark,
        "vchcode": vchcode,
        "ylChannel": ylChannel,
      };
}
