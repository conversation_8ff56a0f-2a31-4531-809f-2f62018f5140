///
///@ClassName: yunli_cancel_reason
///@Description:  运力呼叫取消原因
///@Author: tanglan
///@Date: 2023/10/27
class YunliCancelReason {
  String? fullname;
  String? id;

  static YunliCancelReason fromMap(Map<String?, dynamic>? map) {
    if (map == null) return YunliCancelReason();
    YunliCancelReason feeDto = YunliCancelReason();
    feeDto.fullname = map['fullname'];
    feeDto.id = map['id'];

    return feeDto;
  }

  Map<String?, dynamic> toJson() => {
    "fullname": fullname,
    "id": id,
  };
}