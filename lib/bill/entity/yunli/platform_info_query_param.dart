///
///@ClassName: platform_info_query_param
///@Description: 运力请求参数
///@Author: tanglan
///@Date: 2023/10/29
class PlatformInfoQueryParam {
  String? printTemplateType;
  int? tokenType;
  String? freightUserCode;
  String? onlineTemplateId;
  String? customerCode;
  String? vchcode;
  String? warehouseTaskId;
  List<String?>? vchcodes;
  List<String?>? warehouseTaskIds;
  String? message;
  double? addPrice;

  static PlatformInfoQueryParam fromJson(Map<String?, dynamic>? map) {
    if (map == null) return PlatformInfoQueryParam();
    PlatformInfoQueryParam feeDto = PlatformInfoQueryParam();
    feeDto.printTemplateType = map['printTemplateType'];
    feeDto.tokenType = map['tokenType'];
    feeDto.freightUserCode = map['freightUserCode'];
    feeDto.onlineTemplateId = map['onlineTemplateId'];
    feeDto.vchcode = map['vchcode'];
    feeDto.customerCode = map['customerCode'];
    feeDto.vchcodes = map['vchcodes'];
    feeDto.warehouseTaskId = map['warehouseTaskId'];
    feeDto.warehouseTaskIds = map['warehouseTaskIds'];
    feeDto.message = map['message'];
    feeDto.addPrice = map['addPrice'];
    return feeDto;
  }

  Map<String?, dynamic> toJson() => {
        "printTemplateType": printTemplateType,
        "tokenType": tokenType,
        "freightUserCode": freightUserCode,
        "onlineTemplateId": onlineTemplateId,
        "vchcode": vchcode,
        "customerCode": customerCode,
        "vchcodes": vchcodes,
        "warehouseTaskId": warehouseTaskId,
        "warehouseTaskIds": warehouseTaskIds,
        "message": message,
        "addPrice": addPrice
      };
}
