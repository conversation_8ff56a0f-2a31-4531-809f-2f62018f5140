import '../entity/goods_detail_dto.dart';
import '../entity/payment_dto.dart';
import 'back_goods_bill_list_bean.dart';
import 'goods_bill.dto.dart';
import '../enums/assert_change_type.dart';

/// vipBillInfo : {"vipGrowthValue":220,"phone":"12586965855","vipScore":810}
/// vipAssertsBillDto : {"id":"927057068666719485","etypeId":"768214958239395840","vchtype":"EshopSale","vchcode":"927057240458792319","memo":"门店零售","statused":false,"sourceOperation":null,"assertsBillDetailDtoList":[{"id":"927057068666785021","cardAssertBillId":"927057068666719485","qty":"810","typed":"0","memo":"积分","assertId":"0"},{"id":"927057068666850557","cardAssertBillId":"927057068666719485","qty":"-818","typed":"1","memo":"储值","assertId":"0"},{"id":"927057068666916093","cardAssertBillId":"927057068666719485","qty":"-81","typed":"2","memo":"储值","assertId":"0"},{"id":"927057068666981629","cardAssertBillId":"927057068666719485","qty":"220","typed":"3","memo":"成长值","assertId":"0"},{"id":"927057068667047165","cardAssertBillId":"927057068666719485","qty":"1","typed":"4","memo":"赠送优惠券","assertId":"927057068664491261"},{"id":"927057068667112701","cardAssertBillId":"927057068666719485","qty":"1","typed":"4","memo":"赠送优惠券","assertId":"927057068666064125"},{"id":"927057068667178237","cardAssertBillId":"927057068666719485","qty":"1","typed":"4","memo":"赠送优惠券","assertId":"927057068666326269"},{"id":"927057068667243773","cardAssertBillId":"927057068666719485","qty":"1","typed":"4","memo":"赠送优惠券","assertId":"927057068666588413"}]}
/// preferentialBills : [{"update_time":"2022-11-11T06:15:38.503+00:00","preferential_type":6,"preferential_value":0.0,"deleted":false,"create_time":"2022-11-11T06:15:38.503+00:00","preferential_type_id":"0","profile_id":"768214957044019201","memo":"","preferential_total":81.88,"id":"927057068660231421","vchcode":"927057240458792319"}]
/// preferentialGoodsDetails : [{"update_time":"2022-11-11T06:15:38.605+00:00","preferential_type":6,"deleted":false,"create_time":"2022-11-11T06:15:38.605+00:00","preferential_type_id":"0","profile_id":"768214957044019201","memo":"","statistics_total":false,"preferential_total":81.88,"id":"927057068661542141","detail_id":"927057068661476605","vchcode":"927057240458792319"}]
/// billDetail : {"inDetail":[],"outDetail":[{"vchcode":"927057240458792319","billNumber":null,"shortname":"属性商品","ptypeArea":"","serviceStartTime":null,"serviceEndTime":null,"detailId":"927057060083404029","oldDetailId":null,"saveDetailId":null,"profileId":"768214957044019201","rowIndex":0,"__rowIndex":0,"vchtype":null,"shareTotal":null,"ktypeId":"768214957861908480","detailKtypeId":null,"ktypePointId":null,"ktypePointType":null,"carNumber":null,"drfullname":null,"extKtypeId":"0","kfullname":"默认仓库","scategory":0,"stockType":0,"stockState":0,"comboId":"0","comboDetailId":"0","ptypeId":"910791108387147135","pFullName":"属性商品001","pUserCode":"","fullbarcode":"7771","baseFullBarcode":"7771","skuId":"910791108388261247","skuName":"浅灰色_HB：米白色","xcode":null,"prop":[{"propIndex":1,"propValueName":"浅灰色_HB","propValueId":"890027784779533695"},{"propIndex":2,"propValueName":"米白色","propValueId":"890027759010319743"}],"propValueId1":"890027784779533695","propValueName1":"浅灰色_HB","propValueId2":"890027759010319743","propValueName2":"米白色","propValues":"浅灰色_HB：米白色","propFormat":"颜色：尺码","forecastCostPrice":0,"forecastCostTotal":0,"costPrice":1.0,"costTotal":10.0,"unitQty":10.0,"oldUnitQty":null,"qty":-10.0,"requestQty":0.0,"requestSubQty":0.0,"requestUnitQty":0.0,"requestBackQty":-10.0,"needQty":null,"addStockQty":0,"subQty":0.0,"currencyPrice":90.0,"currencyTotal":900.0,"discount":1.0,"lastDiscount":0.91,"currencyDisedPrice":81.81,"currencyDisedTotal":818.12,"taxRate":0.0,"currencyTaxTotal":0.0,"currencyDisedTaxedPrice":81.81,"currencyDisedTaxedTotal":818.12,"currencyOrderFeeAllotTotal":0.0,"currencyOrderPreferentialAllotTotal":81.88,"currencyPreferentialTotal":81.88,"preferentialDiscount":0,"retailPrice":null,"picUrl":"http://picture.qiniu.mygjp.com.cn/768214957044019201/910789648098266495.png!200x200","skuPicUrl":"","batchNo":"","produceDate":null,"expireDate":null,"expireDay":null,"position":null,"inPositionStrText":null,"outPositionStrText":null,"memo":"","serialNoList":[],"unitRate":1.0,"unitId":"910791108387343743","unitName":"位","subUnitName":"","completedSubQty":0.0,"snenabled":0,"pcategory":0,"propenabled":true,"batchenabled":false,"gift":false,"inoutType":0,"sourceVchtype":0,"sourceDetailId":"0","sourceVchcode":"0","sourceBillNumber":null,"sourceBill":null,"targetBusinessType":null,"sourceBusinessType":null,"snnoStrButton":"","protectDays":0,"completedQty":0.0,"unitCompletedQty":0,"currencyCompletedPreferentialShare":0.0,"refundQty":0.0,"refundSubQty":0.0,"refundOrderPreferentialAllotTotal":0.0,"isShare":3,"completedTotal":0,"overQty":null,"overTotal":null,"currencyBuyerTotal":null,"shareTotalColumn":null,"unOverQty":null,"unOverTotal":null,"diffQty":null,"diffSubQty":null,"validatorIndex":0,"diffTotal":null,"standard":"","ptypetype":"","ptypeLength":0.0,"ptypeWidth":0.0,"ptypeHeight":0.0,"ptypeWeight":null,"ptypeVolume":null,"weight":0.0,"lengthUnit":0,"weightUnit":1,"sourceBillDate":null,"sourceDetailPeriod":null,"brandName":null,"weightTotal":null,"volumeTotal":null,"comboRowId":"927114363523894655","comboRowParId":"0","comboRow":false,"comboQtyRate":1,"comboShareScale":0.0,"buyPrice":null,"errorInfo":null,"relationIndex":null,"objectKtypeId":null,"needPrintName":0,"logisticsPrintName":null,"skuBarcode":null,"skuPrice":0,"unit":null,"bUserCode":null,"customField01":null,"customField02":null,"customField03":null,"customField04":null,"customField05":null,"customField06":null,"customField07":null,"customField08":null,"customField09":null,"customField10":null,"customField11":null,"customField12":null,"customField13":null,"customHead01":null,"customHead02":null,"customHead03":null,"customHead04":null,"customHead05":null,"customHead06":null,"customHead07":null,"customHead08":null,"customHead09":null,"customHead10":null,"customHead11":null,"customHead12":null,"customHead13":null,"customHead14":null,"createZero":false,"orderUnitId":null,"orderUnitRate":null,"orderUnitQty":null,"orderSubQty":null,"orderCompletedSubQty":null,"orderCompletedQty":null,"differenceQty":null,"costId":"919656281665113471","costMode":0,"inPositionList":null,"outPositionList":null,"wmsRealityQty":null,"referenceQty":null,"platformPtype":null,"invoiceTotal":null,"unInvoiceTotal":null,"inOutQty":90.0,"purchasePrice":null,"purchaseTotal":null,"newCostTotal":null,"stockQty":-55.0,"saleQty":-57.0}],"fee":null,"payment":[],"currencyAdvanceTotal":818.12,"currencyOrderFeeAllotTotal":null,"freight":{"freightFee":null,"freightaTypeId":null,"freightaTypeTotal":null,"freightAtypeName":null,"freightCollectType":false,"freightShareType":0,"freightBtypeId":"0","freightBillNo":null,"freightBtypeName":null},"isShare":3,"stockInOutDetail":[{"inoutId":"927057240458792319","vchcode":"927057240458792319","profileId":"768214957044019201","recordType":0,"inoutState":100,"inoutTime":"2022-11-11T06:16:29.000+00:00","etypeId":"768214958239395840","efullname":"管理员","summary":null,"inoutNumber":"CK-20221111-00006","sourceId":null,"carId":"0","carNumber":null,"failureInfo":"","vchtype":null,"billNumber":null,"bfullname":null,"kfullname":null,"drfullname":null,"memo":null,"dfullname":null,"eefullname":null,"vchtypeName":null}]}
/// payOutNo : null

class BillSaleBillDetailDto {
  VipBillInfoBean? vipBillInfo;
  VipAssertsBillDtoBean? vipAssertsBillDto;
  List<PreferentialBillsBean>? preferentialBills;
  List<PreferentialGoodsDetailsBean>? preferentialGoodsDetails;
  GoodsBillDto? billDetail;
  dynamic payOutNo;
  //已退的单据列表
  List<BackGoodsBillListBean>? backGoodsBillList;

  static BillSaleBillDetailDto fromMap(Map<dynamic, dynamic>? map) {
    if (map == null) return BillSaleBillDetailDto();
    BillSaleBillDetailDto billSaleBillDetailDtoBean = BillSaleBillDetailDto();
    billSaleBillDetailDtoBean.vipBillInfo = VipBillInfoBean.fromMap(
      map['vipBillInfo'],
    );
    billSaleBillDetailDtoBean.vipAssertsBillDto = VipAssertsBillDtoBean.fromMap(
      map['vipAssertsBillDto'],
    );
    billSaleBillDetailDtoBean.preferentialBills =
        (map['preferentialBills'] as List ?? [])
            .map((o) => PreferentialBillsBean.fromMap(o))
            .toList();
    billSaleBillDetailDtoBean.preferentialGoodsDetails =
        (map['preferentialGoodsDetails'] as List ?? [])
            .map((o) => PreferentialGoodsDetailsBean.fromMap(o))
            .toList();
    billSaleBillDetailDtoBean.billDetail = GoodsBillDto.fromMap(
      map['billDetail'],
    );
    billSaleBillDetailDtoBean.payOutNo = map['payOutNo'];
    billSaleBillDetailDtoBean.backGoodsBillList =
        (map['backGoodsBillList'] as List ?? [])
            .map((o) => BackGoodsBillListBean.fromMap(o))
            .toList();
    return billSaleBillDetailDtoBean;
  }

  Map toJson() => {
    "vipBillInfo": vipBillInfo,
    "vipAssertsBillDto": vipAssertsBillDto,
    "preferentialBills": preferentialBills,
    "preferentialGoodsDetails": preferentialGoodsDetails,
    "billDetail": billDetail,
    "payOutNo": payOutNo,
    "backGoodsBillList": backGoodsBillList,
  };
}

/// inDetail : []
/// outDetail : [{"vchcode":"927057240458792319","billNumber":null,"shortname":"属性商品","ptypeArea":"","serviceStartTime":null,"serviceEndTime":null,"detailId":"927057060083404029","oldDetailId":null,"saveDetailId":null,"profileId":"768214957044019201","rowIndex":0,"__rowIndex":0,"vchtype":null,"shareTotal":null,"ktypeId":"768214957861908480","detailKtypeId":null,"ktypePointId":null,"ktypePointType":null,"carNumber":null,"drfullname":null,"extKtypeId":"0","kfullname":"默认仓库","scategory":0,"stockType":0,"stockState":0,"comboId":"0","comboDetailId":"0","ptypeId":"910791108387147135","pFullName":"属性商品001","pUserCode":"","fullbarcode":"7771","baseFullBarcode":"7771","skuId":"910791108388261247","skuName":"浅灰色_HB：米白色","xcode":null,"prop":[{"propIndex":1,"propValueName":"浅灰色_HB","propValueId":"890027784779533695"},{"propIndex":2,"propValueName":"米白色","propValueId":"890027759010319743"}],"propValueId1":"890027784779533695","propValueName1":"浅灰色_HB","propValueId2":"890027759010319743","propValueName2":"米白色","propValues":"浅灰色_HB：米白色","propFormat":"颜色：尺码","forecastCostPrice":0,"forecastCostTotal":0,"costPrice":1.0,"costTotal":10.0,"unitQty":10.0,"oldUnitQty":null,"qty":-10.0,"requestQty":0.0,"requestSubQty":0.0,"requestUnitQty":0.0,"requestBackQty":-10.0,"needQty":null,"addStockQty":0,"subQty":0.0,"currencyPrice":90.0,"currencyTotal":900.0,"discount":1.0,"lastDiscount":0.91,"currencyDisedPrice":81.81,"currencyDisedTotal":818.12,"taxRate":0.0,"currencyTaxTotal":0.0,"currencyDisedTaxedPrice":81.81,"currencyDisedTaxedTotal":818.12,"currencyOrderFeeAllotTotal":0.0,"currencyOrderPreferentialAllotTotal":81.88,"currencyPreferentialTotal":81.88,"preferentialDiscount":0,"retailPrice":null,"picUrl":"http://picture.qiniu.mygjp.com.cn/768214957044019201/910789648098266495.png!200x200","skuPicUrl":"","batchNo":"","produceDate":null,"expireDate":null,"expireDay":null,"position":null,"inPositionStrText":null,"outPositionStrText":null,"memo":"","serialNoList":[],"unitRate":1.0,"unitId":"910791108387343743","unitName":"位","subUnitName":"","completedSubQty":0.0,"snenabled":0,"pcategory":0,"propenabled":true,"batchenabled":false,"gift":false,"inoutType":0,"sourceVchtype":0,"sourceDetailId":"0","sourceVchcode":"0","sourceBillNumber":null,"sourceBill":null,"targetBusinessType":null,"sourceBusinessType":null,"snnoStrButton":"","protectDays":0,"completedQty":0.0,"unitCompletedQty":0,"currencyCompletedPreferentialShare":0.0,"refundQty":0.0,"refundSubQty":0.0,"refundOrderPreferentialAllotTotal":0.0,"isShare":3,"completedTotal":0,"overQty":null,"overTotal":null,"currencyBuyerTotal":null,"shareTotalColumn":null,"unOverQty":null,"unOverTotal":null,"diffQty":null,"diffSubQty":null,"validatorIndex":0,"diffTotal":null,"standard":"","ptypetype":"","ptypeLength":0.0,"ptypeWidth":0.0,"ptypeHeight":0.0,"ptypeWeight":null,"ptypeVolume":null,"weight":0.0,"lengthUnit":0,"weightUnit":1,"sourceBillDate":null,"sourceDetailPeriod":null,"brandName":null,"weightTotal":null,"volumeTotal":null,"comboRowId":"927114363523894655","comboRowParId":"0","comboRow":false,"comboQtyRate":1,"comboShareScale":0.0,"buyPrice":null,"errorInfo":null,"relationIndex":null,"objectKtypeId":null,"needPrintName":0,"logisticsPrintName":null,"skuBarcode":null,"skuPrice":0,"unit":null,"bUserCode":null,"customField01":null,"customField02":null,"customField03":null,"customField04":null,"customField05":null,"customField06":null,"customField07":null,"customField08":null,"customField09":null,"customField10":null,"customField11":null,"customField12":null,"customField13":null,"customHead01":null,"customHead02":null,"customHead03":null,"customHead04":null,"customHead05":null,"customHead06":null,"customHead07":null,"customHead08":null,"customHead09":null,"customHead10":null,"customHead11":null,"customHead12":null,"customHead13":null,"customHead14":null,"createZero":false,"orderUnitId":null,"orderUnitRate":null,"orderUnitQty":null,"orderSubQty":null,"orderCompletedSubQty":null,"orderCompletedQty":null,"differenceQty":null,"costId":"919656281665113471","costMode":0,"inPositionList":null,"outPositionList":null,"wmsRealityQty":null,"referenceQty":null,"platformPtype":null,"invoiceTotal":null,"unInvoiceTotal":null,"inOutQty":90.0,"purchasePrice":null,"purchaseTotal":null,"newCostTotal":null,"stockQty":-55.0,"saleQty":-57.0}]
/// fee : null
/// payment : []
/// currencyAdvanceTotal : 818.12
/// currencyOrderFeeAllotTotal : null
/// freight : {"freightFee":null,"freightaTypeId":null,"freightaTypeTotal":null,"freightAtypeName":null,"freightCollectType":false,"freightShareType":0,"freightBtypeId":"0","freightBillNo":null,"freightBtypeName":null}
/// isShare : 3
/// stockInOutDetail : [{"inoutId":"927057240458792319","vchcode":"927057240458792319","profileId":"768214957044019201","recordType":0,"inoutState":100,"inoutTime":"2022-11-11T06:16:29.000+00:00","etypeId":"768214958239395840","efullname":"管理员","summary":null,"inoutNumber":"CK-20221111-00006","sourceId":null,"carId":"0","carNumber":null,"failureInfo":"","vchtype":null,"billNumber":null,"bfullname":null,"kfullname":null,"drfullname":null,"memo":null,"dfullname":null,"eefullname":null,"vchtypeName":null}]

class BillDetailBean {
  List<GoodsDetailDto>? inDetail;
  List<GoodsDetailDto>? outDetail;
  dynamic fee;
  List<PaymentDto>? payment;
  double currencyAdvanceTotal = 0;
  dynamic currencyOrderFeeAllotTotal;
  FreightBean? freight;
  int? isShare;
  List<StockInOutDetailBean>? stockInOutDetail;

  static BillDetailBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return BillDetailBean();
    BillDetailBean billDetailBean = BillDetailBean();
    billDetailBean.inDetail =
        ((map['inDetail'] ?? []) as List)
            .map((o) => GoodsDetailDto.fromMap(o))
            .toList();
    billDetailBean.outDetail =
        ((map['outDetail'] ?? []) as List)
            .map((o) => GoodsDetailDto.fromMap(o))
            .toList();
    billDetailBean.fee = map['fee'];
    billDetailBean.payment =
        ((map['payment'] ?? []) as List)
            .map((o) => PaymentDto.fromMap(o))
            .toList();
    billDetailBean.currencyAdvanceTotal = map['currencyAdvanceTotal'] ?? 0;
    billDetailBean.currencyOrderFeeAllotTotal =
        map['currencyOrderFeeAllotTotal'];
    billDetailBean.freight = FreightBean.fromMap(map['freight']);
    billDetailBean.isShare = map['isShare'];
    billDetailBean.stockInOutDetail =
        ((map['stockInOutDetail'] ?? []) as List)
            .map((o) => StockInOutDetailBean.fromMap(o))
            .toList();
    return billDetailBean;
  }

  Map toJson() => {
    "inDetail": inDetail?.map((e) => e.toJson()),
    "outDetail": outDetail?.map((e) => e.toJson()),
    "fee": fee,
    "payment": payment?.map((e) => e.toJson()),
    "currencyAdvanceTotal": currencyAdvanceTotal,
    "currencyOrderFeeAllotTotal": currencyOrderFeeAllotTotal,
    "freight": freight,
    "isShare": isShare,
    "stockInOutDetail": stockInOutDetail,
  };
}

/// inoutId : "927057240458792319"
/// vchcode : "927057240458792319"
/// profileId : "768214957044019201"
/// recordType : 0
/// inoutState : 100
/// inoutTime : "2022-11-11T06:16:29.000+00:00"
/// etypeId : "768214958239395840"
/// efullname : "管理员"
/// summary : null
/// inoutNumber : "CK-20221111-00006"
/// sourceId : null
/// carId : "0"
/// carNumber : null
/// failureInfo : ""
/// vchtype : null
/// billNumber : null
/// bfullname : null
/// kfullname : null
/// drfullname : null
/// memo : null
/// dfullname : null
/// eefullname : null
/// vchtypeName : null

class StockInOutDetailBean {
  String? inoutId;
  String? vchcode;
  String? profileId;
  int? recordType;
  int? inoutState;
  String? inoutTime;
  String? etypeId;
  String? efullname;
  dynamic summary;
  String? inoutNumber;
  dynamic sourceId;
  String? carId;
  dynamic carNumber;
  String? failureInfo;
  dynamic vchtype;
  dynamic billNumber;
  dynamic bfullname;
  dynamic kfullname;
  dynamic drfullname;
  dynamic memo;
  dynamic dfullname;
  dynamic eefullname;
  dynamic vchtypeName;

  static StockInOutDetailBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return StockInOutDetailBean();
    StockInOutDetailBean stockInOutDetailBean = StockInOutDetailBean();
    stockInOutDetailBean.inoutId = map['inoutId'];
    stockInOutDetailBean.vchcode = map['vchcode'];
    stockInOutDetailBean.profileId = map['profileId'];
    stockInOutDetailBean.recordType = map['recordType'];
    stockInOutDetailBean.inoutState = map['inoutState'];
    stockInOutDetailBean.inoutTime = map['inoutTime'];
    stockInOutDetailBean.etypeId = map['etypeId'];
    stockInOutDetailBean.efullname = map['efullname'];
    stockInOutDetailBean.summary = map['summary'];
    stockInOutDetailBean.inoutNumber = map['inoutNumber'];
    stockInOutDetailBean.sourceId = map['sourceId'];
    stockInOutDetailBean.carId = map['carId'];
    stockInOutDetailBean.carNumber = map['carNumber'];
    stockInOutDetailBean.failureInfo = map['failureInfo'];
    stockInOutDetailBean.vchtype = map['vchtype'];
    stockInOutDetailBean.billNumber = map['billNumber'];
    stockInOutDetailBean.bfullname = map['bfullname'];
    stockInOutDetailBean.kfullname = map['kfullname'];
    stockInOutDetailBean.drfullname = map['drfullname'];
    stockInOutDetailBean.memo = map['memo'];
    stockInOutDetailBean.dfullname = map['dfullname'];
    stockInOutDetailBean.eefullname = map['eefullname'];
    stockInOutDetailBean.vchtypeName = map['vchtypeName'];
    return stockInOutDetailBean;
  }

  Map toJson() => {
    "inoutId": inoutId,
    "vchcode": vchcode,
    "profileId": profileId,
    "recordType": recordType,
    "inoutState": inoutState,
    "inoutTime": inoutTime,
    "etypeId": etypeId,
    "efullname": efullname,
    "summary": summary,
    "inoutNumber": inoutNumber,
    "sourceId": sourceId,
    "carId": carId,
    "carNumber": carNumber,
    "failureInfo": failureInfo,
    "vchtype": vchtype,
    "billNumber": billNumber,
    "bfullname": bfullname,
    "kfullname": kfullname,
    "drfullname": drfullname,
    "memo": memo,
    "dfullname": dfullname,
    "eefullname": eefullname,
    "vchtypeName": vchtypeName,
  };
}

/// freightFee : null
/// freightaTypeId : null
/// freightaTypeTotal : null
/// freightAtypeName : null
/// freightCollectType : false
/// freightShareType : 0
/// freightBtypeId : "0"
/// freightBillNo : null
/// freightBtypeName : null

class FreightBean {
  dynamic freightFee;
  dynamic freightaTypeId;
  dynamic freightaTypeTotal;
  dynamic freightAtypeName;
  int? freightCollectType;
  int? freightShareType;
  String? freightBtypeId;
  dynamic freightBillNo;
  dynamic freightBtypeName;

  static FreightBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return FreightBean();
    FreightBean freightBean = FreightBean();
    freightBean.freightFee = map['freightFee'];
    freightBean.freightaTypeId = map['freightaTypeId'];
    freightBean.freightaTypeTotal = map['freightaTypeTotal'];
    freightBean.freightAtypeName = map['freightAtypeName'];
    freightBean.freightCollectType = map['freightCollectType'];
    freightBean.freightShareType = map['freightShareType'];
    freightBean.freightBtypeId = map['freightBtypeId'];
    freightBean.freightBillNo = map['freightBillNo'];
    freightBean.freightBtypeName = map['freightBtypeName'];
    return freightBean;
  }

  Map toJson() => {
    "freightFee": freightFee,
    "freightaTypeId": freightaTypeId,
    "freightaTypeTotal": freightaTypeTotal,
    "freightAtypeName": freightAtypeName,
    "freightCollectType": freightCollectType,
    "freightShareType": freightShareType,
    "freightBtypeId": freightBtypeId,
    "freightBillNo": freightBillNo,
    "freightBtypeName": freightBtypeName,
  };
}

/// vchcode : "927057240458792319"
/// billNumber : null
/// shortname : "属性商品"
/// ptypeArea : ""
/// serviceStartTime : null
/// serviceEndTime : null
/// detailId : "927057060083404029"
/// oldDetailId : null
/// saveDetailId : null
/// profileId : "768214957044019201"
/// rowIndex : 0
/// __rowIndex : 0
/// vchtype : null
/// shareTotal : null
/// ktypeId : "768214957861908480"
/// detailKtypeId : null
/// ktypePointId : null
/// ktypePointType : null
/// carNumber : null
/// drfullname : null
/// extKtypeId : "0"
/// kfullname : "默认仓库"
/// scategory : 0
/// stockType : 0
/// stockState : 0
/// comboId : "0"
/// comboDetailId : "0"
/// ptypeId : "910791108387147135"
/// pFullName : "属性商品001"
/// pUserCode : ""
/// fullbarcode : "7771"
/// baseFullBarcode : "7771"
/// skuId : "910791108388261247"
/// skuName : "浅灰色_HB：米白色"
/// xcode : null
/// prop : [{"propIndex":1,"propValueName":"浅灰色_HB","propValueId":"890027784779533695"},{"propIndex":2,"propValueName":"米白色","propValueId":"890027759010319743"}]
/// propValueId1 : "890027784779533695"
/// propValueName1 : "浅灰色_HB"
/// propValueId2 : "890027759010319743"
/// propValueName2 : "米白色"
/// propValues : "浅灰色_HB：米白色"
/// propFormat : "颜色：尺码"
/// forecastCostPrice : 0
/// forecastCostTotal : 0
/// costPrice : 1.0
/// costTotal : 10.0
/// unitQty : 10.0
/// oldUnitQty : null
/// qty : -10.0
/// requestQty : 0.0
/// requestSubQty : 0.0
/// requestUnitQty : 0.0
/// requestBackQty : -10.0
/// needQty : null
/// addStockQty : 0
/// subQty : 0.0
/// currencyPrice : 90.0
/// currencyTotal : 900.0
/// discount : 1.0
/// lastDiscount : 0.91
/// currencyDisedPrice : 81.81
/// currencyDisedTotal : 818.12
/// taxRate : 0.0
/// currencyTaxTotal : 0.0
/// currencyDisedTaxedPrice : 81.81
/// currencyDisedTaxedTotal : 818.12
/// currencyOrderFeeAllotTotal : 0.0
/// currencyOrderPreferentialAllotTotal : 81.88
/// currencyPreferentialTotal : 81.88
/// preferentialDiscount : 0
/// retailPrice : null
/// picUrl : "http://picture.qiniu.mygjp.com.cn/768214957044019201/910789648098266495.png!200x200"
/// skuPicUrl : ""
/// batchNo : ""
/// produceDate : null
/// expireDate : null
/// expireDay : null
/// position : null
/// inPositionStrText : null
/// outPositionStrText : null
/// memo : ""
/// serialNoList : []
/// unitRate : 1.0
/// unitId : "910791108387343743"
/// unitName : "位"
/// subUnitName : ""
/// completedSubQty : 0.0
/// snenabled : 0
/// pcategory : 0
/// propenabled : true
/// batchenabled : false
/// gift : false
/// inoutType : 0
/// sourceVchtype : 0
/// sourceDetailId : "0"
/// sourceVchcode : "0"
/// sourceBillNumber : null
/// sourceBill : null
/// targetBusinessType : null
/// sourceBusinessType : null
/// snnoStrButton : ""
/// protectDays : 0
/// completedQty : 0.0
/// unitCompletedQty : 0
/// currencyCompletedPreferentialShare : 0.0
/// refundQty : 0.0
/// refundSubQty : 0.0
/// refundOrderPreferentialAllotTotal : 0.0
/// isShare : 3
/// completedTotal : 0
/// overQty : null
/// overTotal : null
/// currencyBuyerTotal : null
/// shareTotalColumn : null
/// unOverQty : null
/// unOverTotal : null
/// diffQty : null
/// diffSubQty : null
/// validatorIndex : 0
/// diffTotal : null
/// standard : ""
/// ptypetype : ""
/// ptypeLength : 0.0
/// ptypeWidth : 0.0
/// ptypeHeight : 0.0
/// ptypeWeight : null
/// ptypeVolume : null
/// weight : 0.0
/// lengthUnit : 0
/// weightUnit : 1
/// sourceBillDate : null
/// sourceDetailPeriod : null
/// brandName : null
/// weightTotal : null
/// volumeTotal : null
/// comboRowId : "927114363523894655"
/// comboRowParId : "0"
/// comboRow : false
/// comboQtyRate : 1
/// comboShareScale : 0.0
/// buyPrice : null
/// errorInfo : null
/// relationIndex : null
/// objectKtypeId : null
/// needPrintName : 0
/// logisticsPrintName : null
/// skuBarcode : null
/// skuPrice : 0
/// unit : null
/// bUserCode : null
/// customField01 : null
/// customField02 : null
/// customField03 : null
/// customField04 : null
/// customField05 : null
/// customField06 : null
/// customField07 : null
/// customField08 : null
/// customField09 : null
/// customField10 : null
/// customField11 : null
/// customField12 : null
/// customField13 : null
/// customHead01 : null
/// customHead02 : null
/// customHead03 : null
/// customHead04 : null
/// customHead05 : null
/// customHead06 : null
/// customHead07 : null
/// customHead08 : null
/// customHead09 : null
/// customHead10 : null
/// customHead11 : null
/// customHead12 : null
/// customHead13 : null
/// customHead14 : null
/// createZero : false
/// orderUnitId : null
/// orderUnitRate : null
/// orderUnitQty : null
/// orderSubQty : null
/// orderCompletedSubQty : null
/// orderCompletedQty : null
/// differenceQty : null
/// costId : "919656281665113471"
/// costMode : 0
/// inPositionList : null
/// outPositionList : null
/// wmsRealityQty : null
/// referenceQty : null
/// platformPtype : null
/// invoiceTotal : null
/// unInvoiceTotal : null
/// inOutQty : 90.0
/// purchasePrice : null
/// purchaseTotal : null
/// newCostTotal : null
/// stockQty : -55.0
/// saleQty : -57.0

class OutDetailBean {
  String? vchcode;
  dynamic billNumber;
  String? shortname;
  String? ptypeArea;
  dynamic serviceStartTime;
  dynamic serviceEndTime;
  String? detailId;
  dynamic oldDetailId;
  dynamic saveDetailId;
  String? profileId;
  int? rowIndex;
  int? RowIndex;
  dynamic vchtype;
  dynamic shareTotal;
  String? ktypeId;
  dynamic detailKtypeId;
  dynamic ktypePointId;
  dynamic ktypePointType;
  dynamic carNumber;
  dynamic drfullname;
  String? extKtypeId;
  String? kfullname;
  int? scategory;
  int? stockType;
  int? stockState;
  String? comboId;
  String? comboDetailId;
  String? ptypeId;
  String? pFullName;
  String? pUserCode;
  String? fullbarcode;
  String? baseFullBarcode;
  String? skuId;
  String? skuName;
  dynamic xcode;
  List<PropBean>? prop;
  String? propValueId1;
  String? propValueName1;
  String? propValueId2;
  String? propValueName2;
  String? propValues;
  String? propFormat;
  int? forecastCostPrice;
  int? forecastCostTotal;
  double? costPrice;
  double? costTotal;
  double? unitQty;
  dynamic oldUnitQty;
  double? qty;
  double? requestQty;
  double? requestSubQty;
  double? requestUnitQty;
  double? requestBackQty;
  dynamic needQty;
  int? addStockQty;
  double? subQty;
  double? currencyPrice;
  double? currencyTotal;
  double? discount;
  double? lastDiscount;
  double? currencyDisedPrice;
  double? currencyDisedTotal;
  double? taxRate;
  double? currencyTaxTotal;
  double? currencyDisedTaxedPrice;
  double? currencyDisedTaxedTotal;
  double? currencyOrderFeeAllotTotal;
  double? currencyOrderPreferentialAllotTotal;
  double? currencyPreferentialTotal;
  int? preferentialDiscount;
  dynamic retailPrice;
  String? picUrl;
  String? skuPicUrl;
  String? batchNo;
  dynamic produceDate;
  dynamic expireDate;
  dynamic expireDay;
  dynamic position;
  dynamic inPositionStrText;
  dynamic outPositionStrText;
  String? memo;
  List<dynamic>? serialNoList;
  double? unitRate;
  String? unitId;
  String? unitName;
  String? subUnitName;
  double? completedSubQty;
  int? snenabled;
  int? pcategory;
  bool? propenabled;
  bool? batchenabled;
  bool? gift;
  int? inoutType;
  int? sourceVchtype;
  String? sourceDetailId;
  String? sourceVchcode;
  dynamic sourceBillNumber;
  dynamic sourceBill;
  dynamic targetBusinessType;
  dynamic sourceBusinessType;
  String? snnoStrButton;
  int? protectDays;
  double? completedQty;
  int? unitCompletedQty;
  double? currencyCompletedPreferentialShare;
  double? refundQty;
  double? refundSubQty;
  double? refundOrderPreferentialAllotTotal;
  int? isShare;
  int? completedTotal;
  dynamic overQty;
  dynamic overTotal;
  dynamic currencyBuyerTotal;
  dynamic shareTotalColumn;
  dynamic unOverQty;
  dynamic unOverTotal;
  dynamic diffQty;
  dynamic diffSubQty;
  int? validatorIndex;
  dynamic diffTotal;
  String? standard;
  String? ptypetype;
  double? ptypeLength;
  double? ptypeWidth;
  double? ptypeHeight;
  dynamic ptypeWeight;
  dynamic ptypeVolume;
  double? weight;
  int? lengthUnit;
  int? weightUnit;
  dynamic sourceBillDate;
  dynamic sourceDetailPeriod;
  dynamic brandName;
  dynamic weightTotal;
  dynamic volumeTotal;
  String? comboRowId;
  String? comboRowParId;
  bool? comboRow;
  int? comboQtyRate;
  double? comboShareScale;
  dynamic buyPrice;
  dynamic errorInfo;
  dynamic relationIndex;
  dynamic objectKtypeId;
  int? needPrintName;
  dynamic logisticsPrintName;
  dynamic skuBarcode;
  int? skuPrice;
  dynamic unit;
  dynamic bUserCode;
  dynamic customField01;
  dynamic customField02;
  dynamic customField03;
  dynamic customField04;
  dynamic customField05;
  dynamic customField06;
  dynamic customField07;
  dynamic customField08;
  dynamic customField09;
  dynamic customField10;
  dynamic customField11;
  dynamic customField12;
  dynamic customField13;
  dynamic customHead01;
  dynamic customHead02;
  dynamic customHead03;
  dynamic customHead04;
  dynamic customHead05;
  dynamic customHead06;
  dynamic customHead07;
  dynamic customHead08;
  dynamic customHead09;
  dynamic customHead10;
  dynamic customHead11;
  dynamic customHead12;
  dynamic customHead13;
  dynamic customHead14;
  bool? createZero;
  dynamic orderUnitId;
  dynamic orderUnitRate;
  dynamic orderUnitQty;
  dynamic orderSubQty;
  dynamic orderCompletedSubQty;
  dynamic orderCompletedQty;
  dynamic differenceQty;
  String? costId;
  int? costMode;
  dynamic inPositionList;
  dynamic outPositionList;
  dynamic wmsRealityQty;
  dynamic referenceQty;
  dynamic platformPtype;
  dynamic invoiceTotal;
  dynamic unInvoiceTotal;
  double? inOutQty;
  dynamic purchasePrice;
  dynamic purchaseTotal;
  dynamic newCostTotal;
  double? stockQty;

  static OutDetailBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return OutDetailBean();
    OutDetailBean outDetailBean = OutDetailBean();
    outDetailBean.vchcode = map['vchcode'];
    outDetailBean.billNumber = map['billNumber'];
    outDetailBean.shortname = map['shortname'];
    outDetailBean.ptypeArea = map['ptypeArea'];
    outDetailBean.serviceStartTime = map['serviceStartTime'];
    outDetailBean.serviceEndTime = map['serviceEndTime'];
    outDetailBean.detailId = map['detailId'];
    outDetailBean.oldDetailId = map['oldDetailId'];
    outDetailBean.saveDetailId = map['saveDetailId'];
    outDetailBean.profileId = map['profileId'];
    outDetailBean.rowIndex = map['rowIndex'];
    outDetailBean.RowIndex = map['__rowIndex'];
    outDetailBean.vchtype = map['vchtype'];
    outDetailBean.shareTotal = map['shareTotal'];
    outDetailBean.ktypeId = map['ktypeId'];
    outDetailBean.detailKtypeId = map['detailKtypeId'];
    outDetailBean.ktypePointId = map['ktypePointId'];
    outDetailBean.ktypePointType = map['ktypePointType'];
    outDetailBean.carNumber = map['carNumber'];
    outDetailBean.drfullname = map['drfullname'];
    outDetailBean.extKtypeId = map['extKtypeId'];
    outDetailBean.kfullname = map['kfullname'];
    outDetailBean.scategory = map['scategory'];
    outDetailBean.stockType = map['stockType'];
    outDetailBean.stockState = map['stockState'];
    outDetailBean.comboId = map['comboId'];
    outDetailBean.comboDetailId = map['comboDetailId'];
    outDetailBean.ptypeId = map['ptypeId'];
    outDetailBean.pFullName = map['pFullName'];
    outDetailBean.pUserCode = map['pUserCode'];
    outDetailBean.fullbarcode = map['fullbarcode'];
    outDetailBean.baseFullBarcode = map['baseFullBarcode'];
    outDetailBean.skuId = map['skuId'];
    outDetailBean.skuName = map['skuName'];
    outDetailBean.xcode = map['xcode'];
    outDetailBean.prop =
        (map['prop'] as List ?? []).map((o) => PropBean.fromMap(o)).toList();
    outDetailBean.propValueId1 = map['propValueId1'];
    outDetailBean.propValueName1 = map['propValueName1'];
    outDetailBean.propValueId2 = map['propValueId2'];
    outDetailBean.propValueName2 = map['propValueName2'];
    outDetailBean.propValues = map['propValues'];
    outDetailBean.propFormat = map['propFormat'];
    outDetailBean.forecastCostPrice = map['forecastCostPrice'];
    outDetailBean.forecastCostTotal = map['forecastCostTotal'];
    outDetailBean.costPrice = map['costPrice'];
    outDetailBean.costTotal = map['costTotal'];
    outDetailBean.unitQty = map['unitQty'];
    outDetailBean.oldUnitQty = map['oldUnitQty'];
    outDetailBean.qty = map['qty'];
    outDetailBean.requestQty = map['requestQty'];
    outDetailBean.requestSubQty = map['requestSubQty'];
    outDetailBean.requestUnitQty = map['requestUnitQty'];
    outDetailBean.requestBackQty = map['requestBackQty'];
    outDetailBean.needQty = map['needQty'];
    outDetailBean.addStockQty = map['addStockQty'];
    outDetailBean.subQty = map['subQty'];
    outDetailBean.currencyPrice = map['currencyPrice'];
    outDetailBean.currencyTotal = map['currencyTotal'];
    outDetailBean.discount = map['discount'];
    outDetailBean.lastDiscount = map['lastDiscount'];
    outDetailBean.currencyDisedPrice = map['currencyDisedPrice'];
    outDetailBean.currencyDisedTotal = map['currencyDisedTotal'];
    outDetailBean.taxRate = map['taxRate'];
    outDetailBean.currencyTaxTotal = map['currencyTaxTotal'];
    outDetailBean.currencyDisedTaxedPrice = map['currencyDisedTaxedPrice'];
    outDetailBean.currencyDisedTaxedTotal = map['currencyDisedTaxedTotal'];
    outDetailBean.currencyOrderFeeAllotTotal =
        map['currencyOrderFeeAllotTotal'];
    outDetailBean.currencyOrderPreferentialAllotTotal =
        map['currencyOrderPreferentialAllotTotal'];
    outDetailBean.currencyPreferentialTotal = map['currencyPreferentialTotal'];
    outDetailBean.preferentialDiscount = map['preferentialDiscount'];
    outDetailBean.retailPrice = map['retailPrice'];
    outDetailBean.picUrl = map['picUrl'];
    outDetailBean.skuPicUrl = map['skuPicUrl'];
    outDetailBean.batchNo = map['batchNo'];
    outDetailBean.produceDate = map['produceDate'];
    outDetailBean.expireDate = map['expireDate'];
    outDetailBean.expireDay = map['expireDay'];
    outDetailBean.position = map['position'];
    outDetailBean.inPositionStrText = map['inPositionStrText'];
    outDetailBean.outPositionStrText = map['outPositionStrText'];
    outDetailBean.memo = map['memo'];
    outDetailBean.serialNoList = map['serialNoList'];
    outDetailBean.unitRate = map['unitRate'];
    outDetailBean.unitId = map['unitId'];
    outDetailBean.unitName = map['unitName'];
    outDetailBean.subUnitName = map['subUnitName'];
    outDetailBean.completedSubQty = map['completedSubQty'];
    outDetailBean.snenabled = map['snenabled'];
    outDetailBean.pcategory = map['pcategory'];
    outDetailBean.propenabled = map['propenabled'];
    outDetailBean.batchenabled = map['batchenabled'];
    outDetailBean.gift = map['gift'];
    outDetailBean.inoutType = map['inoutType'];
    outDetailBean.sourceVchtype = map['sourceVchtype'];
    outDetailBean.sourceDetailId = map['sourceDetailId'];
    outDetailBean.sourceVchcode = map['sourceVchcode'];
    outDetailBean.sourceBillNumber = map['sourceBillNumber'];
    outDetailBean.sourceBill = map['sourceBill'];
    outDetailBean.targetBusinessType = map['targetBusinessType'];
    outDetailBean.sourceBusinessType = map['sourceBusinessType'];
    outDetailBean.snnoStrButton = map['snnoStrButton'];
    outDetailBean.protectDays = map['protectDays'];
    outDetailBean.completedQty = map['completedQty'];
    outDetailBean.unitCompletedQty = map['unitCompletedQty'];
    outDetailBean.currencyCompletedPreferentialShare =
        map['currencyCompletedPreferentialShare'];
    outDetailBean.refundQty = map['refundQty'];
    outDetailBean.refundSubQty = map['refundSubQty'];
    outDetailBean.refundOrderPreferentialAllotTotal =
        map['refundOrderPreferentialAllotTotal'];
    outDetailBean.isShare = map['isShare'];
    outDetailBean.completedTotal = map['completedTotal'];
    outDetailBean.overQty = map['overQty'];
    outDetailBean.overTotal = map['overTotal'];
    outDetailBean.currencyBuyerTotal = map['currencyBuyerTotal'];
    outDetailBean.shareTotalColumn = map['shareTotalColumn'];
    outDetailBean.unOverQty = map['unOverQty'];
    outDetailBean.unOverTotal = map['unOverTotal'];
    outDetailBean.diffQty = map['diffQty'];
    outDetailBean.diffSubQty = map['diffSubQty'];
    outDetailBean.validatorIndex = map['validatorIndex'];
    outDetailBean.diffTotal = map['diffTotal'];
    outDetailBean.standard = map['standard'];
    outDetailBean.ptypetype = map['ptypetype'];
    outDetailBean.ptypeLength = map['ptypeLength'];
    outDetailBean.ptypeWidth = map['ptypeWidth'];
    outDetailBean.ptypeHeight = map['ptypeHeight'];
    outDetailBean.ptypeWeight = map['ptypeWeight'];
    outDetailBean.ptypeVolume = map['ptypeVolume'];
    outDetailBean.weight = map['weight'];
    outDetailBean.lengthUnit = map['lengthUnit'];
    outDetailBean.weightUnit = map['weightUnit'];
    outDetailBean.sourceBillDate = map['sourceBillDate'];
    outDetailBean.sourceDetailPeriod = map['sourceDetailPeriod'];
    outDetailBean.brandName = map['brandName'];
    outDetailBean.weightTotal = map['weightTotal'];
    outDetailBean.volumeTotal = map['volumeTotal'];
    outDetailBean.comboRowId = map['comboRowId'];
    outDetailBean.comboRowParId = map['comboRowParId'];
    outDetailBean.comboRow = map['comboRow'];
    outDetailBean.comboQtyRate = map['comboQtyRate'];
    outDetailBean.comboShareScale = map['comboShareScale'];
    outDetailBean.buyPrice = map['buyPrice'];
    outDetailBean.errorInfo = map['errorInfo'];
    outDetailBean.relationIndex = map['relationIndex'];
    outDetailBean.objectKtypeId = map['objectKtypeId'];
    outDetailBean.needPrintName = map['needPrintName'];
    outDetailBean.logisticsPrintName = map['logisticsPrintName'];
    outDetailBean.skuBarcode = map['skuBarcode'];
    outDetailBean.skuPrice = map['skuPrice'];
    outDetailBean.unit = map['unit'];
    outDetailBean.bUserCode = map['bUserCode'];
    outDetailBean.customField01 = map['customField01'];
    outDetailBean.customField02 = map['customField02'];
    outDetailBean.customField03 = map['customField03'];
    outDetailBean.customField04 = map['customField04'];
    outDetailBean.customField05 = map['customField05'];
    outDetailBean.customField06 = map['customField06'];
    outDetailBean.customField07 = map['customField07'];
    outDetailBean.customField08 = map['customField08'];
    outDetailBean.customField09 = map['customField09'];
    outDetailBean.customField10 = map['customField10'];
    outDetailBean.customField11 = map['customField11'];
    outDetailBean.customField12 = map['customField12'];
    outDetailBean.customField13 = map['customField13'];
    outDetailBean.customHead01 = map['customHead01'];
    outDetailBean.customHead02 = map['customHead02'];
    outDetailBean.customHead03 = map['customHead03'];
    outDetailBean.customHead04 = map['customHead04'];
    outDetailBean.customHead05 = map['customHead05'];
    outDetailBean.customHead06 = map['customHead06'];
    outDetailBean.customHead07 = map['customHead07'];
    outDetailBean.customHead08 = map['customHead08'];
    outDetailBean.customHead09 = map['customHead09'];
    outDetailBean.customHead10 = map['customHead10'];
    outDetailBean.customHead11 = map['customHead11'];
    outDetailBean.customHead12 = map['customHead12'];
    outDetailBean.customHead13 = map['customHead13'];
    outDetailBean.customHead14 = map['customHead14'];
    outDetailBean.createZero = map['createZero'];
    outDetailBean.orderUnitId = map['orderUnitId'];
    outDetailBean.orderUnitRate = map['orderUnitRate'];
    outDetailBean.orderUnitQty = map['orderUnitQty'];
    outDetailBean.orderSubQty = map['orderSubQty'];
    outDetailBean.orderCompletedSubQty = map['orderCompletedSubQty'];
    outDetailBean.orderCompletedQty = map['orderCompletedQty'];
    outDetailBean.differenceQty = map['differenceQty'];
    outDetailBean.costId = map['costId'];
    outDetailBean.costMode = map['costMode'];
    outDetailBean.inPositionList = map['inPositionList'];
    outDetailBean.outPositionList = map['outPositionList'];
    outDetailBean.wmsRealityQty = map['wmsRealityQty'];
    outDetailBean.referenceQty = map['referenceQty'];
    outDetailBean.platformPtype = map['platformPtype'];
    outDetailBean.invoiceTotal = map['invoiceTotal'];
    outDetailBean.unInvoiceTotal = map['unInvoiceTotal'];
    outDetailBean.inOutQty = map['inOutQty'];
    outDetailBean.purchasePrice = map['purchasePrice'];
    outDetailBean.purchaseTotal = map['purchaseTotal'];
    outDetailBean.newCostTotal = map['newCostTotal'];
    outDetailBean.stockQty = map['stockQty'];
    return outDetailBean;
  }

  Map toJson() => {
    "vchcode": vchcode,
    "billNumber": billNumber,
    "shortname": shortname,
    "ptypeArea": ptypeArea,
    "serviceStartTime": serviceStartTime,
    "serviceEndTime": serviceEndTime,
    "detailId": detailId,
    "oldDetailId": oldDetailId,
    "saveDetailId": saveDetailId,
    "profileId": profileId,
    "rowIndex": rowIndex,
    "__rowIndex": RowIndex,
    "vchtype": vchtype,
    "shareTotal": shareTotal,
    "ktypeId": ktypeId,
    "detailKtypeId": detailKtypeId,
    "ktypePointId": ktypePointId,
    "ktypePointType": ktypePointType,
    "carNumber": carNumber,
    "drfullname": drfullname,
    "extKtypeId": extKtypeId,
    "kfullname": kfullname,
    "scategory": scategory,
    "stockType": stockType,
    "stockState": stockState,
    "comboId": comboId,
    "comboDetailId": comboDetailId,
    "ptypeId": ptypeId,
    "pFullName": pFullName,
    "pUserCode": pUserCode,
    "fullbarcode": fullbarcode,
    "baseFullBarcode": baseFullBarcode,
    "skuId": skuId,
    "skuName": skuName,
    "xcode": xcode,
    "prop": prop,
    "propValueId1": propValueId1,
    "propValueName1": propValueName1,
    "propValueId2": propValueId2,
    "propValueName2": propValueName2,
    "propValues": propValues,
    "propFormat": propFormat,
    "forecastCostPrice": forecastCostPrice,
    "forecastCostTotal": forecastCostTotal,
    "costPrice": costPrice,
    "costTotal": costTotal,
    "unitQty": unitQty,
    "oldUnitQty": oldUnitQty,
    "qty": qty,
    "requestQty": requestQty,
    "requestSubQty": requestSubQty,
    "requestUnitQty": requestUnitQty,
    "requestBackQty": requestBackQty,
    "needQty": needQty,
    "addStockQty": addStockQty,
    "subQty": subQty,
    "currencyPrice": currencyPrice,
    "currencyTotal": currencyTotal,
    "discount": discount,
    "lastDiscount": lastDiscount,
    "currencyDisedPrice": currencyDisedPrice,
    "currencyDisedTotal": currencyDisedTotal,
    "taxRate": taxRate,
    "currencyTaxTotal": currencyTaxTotal,
    "currencyDisedTaxedPrice": currencyDisedTaxedPrice,
    "currencyDisedTaxedTotal": currencyDisedTaxedTotal,
    "currencyOrderFeeAllotTotal": currencyOrderFeeAllotTotal,
    "currencyOrderPreferentialAllotTotal": currencyOrderPreferentialAllotTotal,
    "currencyPreferentialTotal": currencyPreferentialTotal,
    "preferentialDiscount": preferentialDiscount,
    "retailPrice": retailPrice,
    "picUrl": picUrl,
    "skuPicUrl": skuPicUrl,
    "batchNo": batchNo,
    "produceDate": produceDate,
    "expireDate": expireDate,
    "expireDay": expireDay,
    "position": position,
    "inPositionStrText": inPositionStrText,
    "outPositionStrText": outPositionStrText,
    "memo": memo,
    "serialNoList": serialNoList,
    "unitRate": unitRate,
    "unitId": unitId,
    "unitName": unitName,
    "subUnitName": subUnitName,
    "completedSubQty": completedSubQty,
    "snenabled": snenabled,
    "pcategory": pcategory,
    "propenabled": propenabled,
    "batchenabled": batchenabled,
    "gift": gift,
    "inoutType": inoutType,
    "sourceVchtype": sourceVchtype,
    "sourceDetailId": sourceDetailId,
    "sourceVchcode": sourceVchcode,
    "sourceBillNumber": sourceBillNumber,
    "sourceBill": sourceBill,
    "targetBusinessType": targetBusinessType,
    "sourceBusinessType": sourceBusinessType,
    "snnoStrButton": snnoStrButton,
    "protectDays": protectDays,
    "completedQty": completedQty,
    "unitCompletedQty": unitCompletedQty,
    "currencyCompletedPreferentialShare": currencyCompletedPreferentialShare,
    "refundQty": refundQty,
    "refundSubQty": refundSubQty,
    "refundOrderPreferentialAllotTotal": refundOrderPreferentialAllotTotal,
    "isShare": isShare,
    "completedTotal": completedTotal,
    "overQty": overQty,
    "overTotal": overTotal,
    "currencyBuyerTotal": currencyBuyerTotal,
    "shareTotalColumn": shareTotalColumn,
    "unOverQty": unOverQty,
    "unOverTotal": unOverTotal,
    "diffQty": diffQty,
    "diffSubQty": diffSubQty,
    "validatorIndex": validatorIndex,
    "diffTotal": diffTotal,
    "standard": standard,
    "ptypetype": ptypetype,
    "ptypeLength": ptypeLength,
    "ptypeWidth": ptypeWidth,
    "ptypeHeight": ptypeHeight,
    "ptypeWeight": ptypeWeight,
    "ptypeVolume": ptypeVolume,
    "weight": weight,
    "lengthUnit": lengthUnit,
    "weightUnit": weightUnit,
    "sourceBillDate": sourceBillDate,
    "sourceDetailPeriod": sourceDetailPeriod,
    "brandName": brandName,
    "weightTotal": weightTotal,
    "volumeTotal": volumeTotal,
    "comboRowId": comboRowId,
    "comboRowParId": comboRowParId,
    "comboRow": comboRow,
    "comboQtyRate": comboQtyRate,
    "comboShareScale": comboShareScale,
    "buyPrice": buyPrice,
    "errorInfo": errorInfo,
    "relationIndex": relationIndex,
    "objectKtypeId": objectKtypeId,
    "needPrintName": needPrintName,
    "logisticsPrintName": logisticsPrintName,
    "skuBarcode": skuBarcode,
    "skuPrice": skuPrice,
    "unit": unit,
    "bUserCode": bUserCode,
    "customField01": customField01,
    "customField02": customField02,
    "customField03": customField03,
    "customField04": customField04,
    "customField05": customField05,
    "customField06": customField06,
    "customField07": customField07,
    "customField08": customField08,
    "customField09": customField09,
    "customField10": customField10,
    "customField11": customField11,
    "customField12": customField12,
    "customField13": customField13,
    "customHead01": customHead01,
    "customHead02": customHead02,
    "customHead03": customHead03,
    "customHead04": customHead04,
    "customHead05": customHead05,
    "customHead06": customHead06,
    "customHead07": customHead07,
    "customHead08": customHead08,
    "customHead09": customHead09,
    "customHead10": customHead10,
    "customHead11": customHead11,
    "customHead12": customHead12,
    "customHead13": customHead13,
    "customHead14": customHead14,
    "createZero": createZero,
    "orderUnitId": orderUnitId,
    "orderUnitRate": orderUnitRate,
    "orderUnitQty": orderUnitQty,
    "orderSubQty": orderSubQty,
    "orderCompletedSubQty": orderCompletedSubQty,
    "orderCompletedQty": orderCompletedQty,
    "differenceQty": differenceQty,
    "costId": costId,
    "costMode": costMode,
    "inPositionList": inPositionList,
    "outPositionList": outPositionList,
    "wmsRealityQty": wmsRealityQty,
    "referenceQty": referenceQty,
    "platformPtype": platformPtype,
    "invoiceTotal": invoiceTotal,
    "unInvoiceTotal": unInvoiceTotal,
    "inOutQty": inOutQty,
    "purchasePrice": purchasePrice,
    "purchaseTotal": purchaseTotal,
    "newCostTotal": newCostTotal,
    "stockQty": stockQty,
  };
}

/// propIndex : 1
/// propValueName : "浅灰色_HB"
/// propValueId : "890027784779533695"

class PropBean {
  int? propIndex;
  String? propValueName;
  String? propValueId;

  static PropBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return PropBean();
    PropBean propBean = PropBean();
    propBean.propIndex = map['propIndex'];
    propBean.propValueName = map['propValueName'];
    propBean.propValueId = map['propValueId'];
    return propBean;
  }

  Map toJson() => {
    "propIndex": propIndex,
    "propValueName": propValueName,
    "propValueId": propValueId,
  };
}

/// update_time : "2022-11-11T06:15:38.605+00:00"
/// preferential_type : 6
/// deleted : false
/// create_time : "2022-11-11T06:15:38.605+00:00"
/// preferential_type_id : "0"
/// profile_id : "768214957044019201"
/// memo : ""
/// statistics_total : false
/// preferential_total : 81.88
/// id : "927057068661542141"
/// detail_id : "927057068661476605"
/// vchcode : "927057240458792319"

class PreferentialGoodsDetailsBean {
  String? updateTime;
  int? preferentialType;
  bool? deleted;
  String? createTime;
  String? preferentialTypeId;
  String? profileId;
  String? memo;
  bool? statisticsTotal;
  double? preferentialTotal;
  String? id;
  String? detailId;
  String? vchcode;

  static PreferentialGoodsDetailsBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return PreferentialGoodsDetailsBean();
    PreferentialGoodsDetailsBean preferentialGoodsDetailsBean =
        PreferentialGoodsDetailsBean();
    preferentialGoodsDetailsBean.updateTime = map[toCamelCase('update_time')];
    preferentialGoodsDetailsBean.preferentialType =
        map[toCamelCase('preferential_type')];
    preferentialGoodsDetailsBean.deleted = map['deleted'];
    preferentialGoodsDetailsBean.createTime = map[toCamelCase('create_time')];
    preferentialGoodsDetailsBean.preferentialTypeId =
        map[toCamelCase('preferential_type_id')];
    preferentialGoodsDetailsBean.profileId = map[toCamelCase('profile_id')];
    preferentialGoodsDetailsBean.memo = map['memo'];
    preferentialGoodsDetailsBean.statisticsTotal =
        map[toCamelCase('statistics_total')] is int
            ? (map[toCamelCase('statistics_total')] == 0 ? false : true)
            : map[toCamelCase('statistics_total')];
    preferentialGoodsDetailsBean.preferentialTotal =
        map[toCamelCase('preferential_total')];
    preferentialGoodsDetailsBean.id = map['id'];
    preferentialGoodsDetailsBean.detailId = map[toCamelCase('detail_id')];
    preferentialGoodsDetailsBean.vchcode = map['vchcode'];
    return preferentialGoodsDetailsBean;
  }

  Map toJson() => {
    toCamelCase('update_time'): updateTime,
    toCamelCase("preferential_type"): preferentialType,
    "deleted": deleted,
    toCamelCase("create_time"): createTime,
    toCamelCase("preferential_type_id"): preferentialTypeId,
    toCamelCase("profile_id"): profileId,
    "memo": memo,
    toCamelCase("statistics_total"): statisticsTotal,
    toCamelCase("preferential_total"): preferentialTotal,
    "id": id,
    toCamelCase("detail_id"): detailId,
    "vchcode": vchcode,
  };
}

/// update_time : "2022-11-11T06:15:38.503+00:00"
/// preferential_type : 6
/// preferential_value : 0.0
/// deleted : false
/// create_time : "2022-11-11T06:15:38.503+00:00"
/// preferential_type_id : "0"
/// profile_id : "768214957044019201"
/// memo : ""
/// preferential_total : 81.88
/// id : "927057068660231421"
/// vchcode : "927057240458792319"

class PreferentialBillsBean {
  String? updateTime;
  int? preferentialType;
  double? preferentialValue;
  bool? deleted;
  String? createTime;
  String? preferentialTypeId;
  String? profileId;
  String? memo;
  double preferentialTotal = 0;
  String? id;
  String? vchcode;

  static PreferentialBillsBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return PreferentialBillsBean();
    PreferentialBillsBean preferentialBillsBean = PreferentialBillsBean();
    preferentialBillsBean.updateTime = map[toCamelCase('update_time')];
    preferentialBillsBean.preferentialType =
        map[toCamelCase('preferential_type')];
    preferentialBillsBean.preferentialValue =
        map[toCamelCase('preferential_value')];
    preferentialBillsBean.deleted = map['deleted'];
    preferentialBillsBean.createTime = map[toCamelCase('create_time')];
    preferentialBillsBean.preferentialTypeId =
        map[toCamelCase('preferential_type_id')];
    preferentialBillsBean.profileId = map[toCamelCase('profile_id')];
    preferentialBillsBean.memo = map['memo'];
    preferentialBillsBean.preferentialTotal =
        map[toCamelCase('preferential_total')] ?? 0;
    preferentialBillsBean.id = map['id'];
    preferentialBillsBean.vchcode = map['vchcode'];
    return preferentialBillsBean;
  }

  Map toJson() => {
    toCamelCase("update_time"): updateTime,
    toCamelCase("preferential_type"): preferentialType,
    toCamelCase("preferential_value"): preferentialValue,
    "deleted": deleted,
    toCamelCase("create_time"): createTime,
    toCamelCase("preferential_type_id"): preferentialTypeId,
    toCamelCase("profile_id"): profileId,
    "memo": memo,
    toCamelCase("preferential_total"): preferentialTotal,
    "id": id,
    "vchcode": vchcode,
  };
}

/// id : "927057068666719485"
/// etypeId : "768214958239395840"
/// vchtype : "EshopSale"
/// vchcode : "927057240458792319"
/// memo : "门店零售"
/// statused : false
/// sourceOperation : null
/// assertsBillDetailDtoList : [{"id":"927057068666785021","cardAssertBillId":"927057068666719485","qty":"810","typed":"0","memo":"积分","assertId":"0"},{"id":"927057068666850557","cardAssertBillId":"927057068666719485","qty":"-818","typed":"1","memo":"储值","assertId":"0"},{"id":"927057068666916093","cardAssertBillId":"927057068666719485","qty":"-81","typed":"2","memo":"储值","assertId":"0"},{"id":"927057068666981629","cardAssertBillId":"927057068666719485","qty":"220","typed":"3","memo":"成长值","assertId":"0"},{"id":"927057068667047165","cardAssertBillId":"927057068666719485","qty":"1","typed":"4","memo":"赠送优惠券","assertId":"927057068664491261"},{"id":"927057068667112701","cardAssertBillId":"927057068666719485","qty":"1","typed":"4","memo":"赠送优惠券","assertId":"927057068666064125"},{"id":"927057068667178237","cardAssertBillId":"927057068666719485","qty":"1","typed":"4","memo":"赠送优惠券","assertId":"927057068666326269"},{"id":"927057068667243773","cardAssertBillId":"927057068666719485","qty":"1","typed":"4","memo":"赠送优惠券","assertId":"927057068666588413"}]

class VipAssertsBillDtoBean {
  String? id;
  String? etypeId;
  String? vchtype;
  String? vchcode;
  String? memo;
  dynamic statused;
  dynamic sourceOperation;
  List<AssertsBillDetailDtoListBean> assertsBillDetailDtoList = [];

  static VipAssertsBillDtoBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    VipAssertsBillDtoBean vipAssertsBillDtoBean = VipAssertsBillDtoBean();
    vipAssertsBillDtoBean.id = map['id'];
    vipAssertsBillDtoBean.etypeId = map['etypeId'];
    vipAssertsBillDtoBean.vchtype = map['vchtype'];
    vipAssertsBillDtoBean.vchcode = map['vchcode'];
    vipAssertsBillDtoBean.memo = map['memo'];
    vipAssertsBillDtoBean.statused = map['statused'];
    vipAssertsBillDtoBean.sourceOperation = map['sourceOperation'];
    vipAssertsBillDtoBean.assertsBillDetailDtoList =
        (map['assertsBillDetailDtoList'] as List ?? [])
            .map((o) => AssertsBillDetailDtoListBean.fromMap(o))
            .toList();
    return vipAssertsBillDtoBean;
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "etypeId": etypeId,
    "vchtype": vchtype,
    "vchcode": vchcode,
    "memo": memo,
    "statused": statused,
    "sourceOperation": sourceOperation,
    "assertsBillDetailDtoList":
        assertsBillDetailDtoList?.map((e) => e.toJson()).toList(),
  };
}

/// id : "927057068666785021"
/// cardAssertBillId : "927057068666719485"
/// qty : "810"
/// typed : "0"
/// memo : "积分"
/// assertId : "0"
/// cardType : 0
/// changeType : 0

class AssertsBillDetailDtoListBean {
  String? id;
  String? cardAssertBillId;
  String? qty;
  String? typed;
  String? memo;
  String? assertId;

  ///原单卡券资产变动中的卡券类型，2：代金券 3：折扣券 4：礼品券
  int? cardType;

  ///资产变更类型
  AssertChangeType? changeType;

  static AssertsBillDetailDtoListBean fromMap(Map<String, dynamic>? map) {
    if (map == null) return AssertsBillDetailDtoListBean();
    AssertsBillDetailDtoListBean assertsBillDetailDtoListBean =
        AssertsBillDetailDtoListBean();
    assertsBillDetailDtoListBean.id = map['id'];
    assertsBillDetailDtoListBean.cardAssertBillId = map['cardAssertBillId'];
    assertsBillDetailDtoListBean.qty =
        map['qty'] is double ? map['qty'].toString() : map['qty'];
    assertsBillDetailDtoListBean.typed =
        map['typed'] is int ? map['typed'].toString() : map['typed'];
    assertsBillDetailDtoListBean.memo = map['memo'];
    assertsBillDetailDtoListBean.assertId = map['assertId'];
    assertsBillDetailDtoListBean.cardType = map['cardType'];
    // 处理changeType字段，支持数字和字符串两种格式
    final changeType = map['changeType'];
    assertsBillDetailDtoListBean.changeType =
        changeType is int
            ? AssertChangeType.fromCode(changeType)
            : AssertChangeType.fromString(changeType?.toString());
    return assertsBillDetailDtoListBean;
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "cardAssertBillId": cardAssertBillId,
    "qty": qty,
    "typed": typed,
    "memo": memo,
    "assertId": assertId,
    "cardType": cardType,
    "changeType": changeType?.code,
  };
}

/// vipGrowthValue : 220
/// phone : "12586965855"
/// vipScore : 810

class VipBillInfoBean {
  int? vipGrowthValue;
  String? phone;
  String? vipId;
  String? name;
  int? vipScore;

  ///积分余额
  int? scoreTotal;

  ///本次积分
  int? saleScore;

  ///储值余额
  double? chargeTotal;

  static VipBillInfoBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    VipBillInfoBean vipBillInfoBean = VipBillInfoBean();
    vipBillInfoBean.vipGrowthValue = map['vipGrowthValue'];
    vipBillInfoBean.phone = map['phone'];
    vipBillInfoBean.vipId = map['vipId'];
    vipBillInfoBean.name = map['name'];
    vipBillInfoBean.vipScore = map['vipScore'];
    vipBillInfoBean.scoreTotal = map['scoreTotal'];
    vipBillInfoBean.saleScore = map['saleScore'];
    vipBillInfoBean.chargeTotal = map['chargeTotal'];
    return vipBillInfoBean;
  }

  Map<String, dynamic> toJson() => {
    "vipGrowthValue": vipGrowthValue,
    "vipId": vipId,
    "name": name,
    "vipScore": vipScore,
    "scoreTotal": scoreTotal,
    "saleScore": saleScore,
    "chargeTotal": chargeTotal,
  };
}

String toCamelCase(String str) {
  var words = str.split('_');
  var result = '';
  for (var i = 0; i < words.length; i++) {
    var word = words[i];
    if (i > 0) {
      word = word[0].toUpperCase() + word.substring(1);
    }
    result += word;
  }
  return result;
}
