import './../../../generated/json/base/json_field.dart';
import './../../../generated/json/pay_result_dto_entity.g.dart';
import 'dart:convert';

import 'bill_save_reslut_dto.dart';
export './../../../generated/json/pay_result_dto_entity.g.dart';

@JsonSerializable()
class PayResultDtoEntity {
  String? ordNo = '';
  int? ordMctId = 0;
  int? ordShopId = 0;
  dynamic ordCurrency;
  String? currencySign = '';
  String? pmtTag = '';
  String? pmtName = '';
  String? tradeNo = '';
  int? tradeAmount = 0;
  dynamic tradeQrcode;
  dynamic tradeAccount;
  PayResultDtoTradeResult? tradeResult;
  dynamic tradePayTime;
  int? tradeDiscoutAmount = 0;
  String? status = '';
  String? outNo = '';
  int? ordType = 0;
  String? ordName = '';
  String? addTime = '';
  dynamic tradeTime;
  dynamic remark;
  int? originalAmount = 0;
  int? discountAmount = 0;
  int? ignoreAmount = 0;
  dynamic originalOrdNo;
  dynamic tag;
  int? scrId = 0;
  int? shopNo = 0;
  BillSaveResultDto? billSaveResultDto;

  PayResultDtoEntity();

  factory PayResultDtoEntity.fromJson(Map<String, dynamic> json) =>
      $PayResultDtoEntityFromJson(json);

  Map<String, dynamic> toJson() => $PayResultDtoEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PayResultDtoTradeResult {
  @JSONField(name: "resp_code")
  String? respCode = '';
  String? royalty = '';
  @JSONField(name: "channel_flag")
  String? channelFlag = '';
  String? sign = '';
  @JSONField(name: "result_code")
  String? resultCode = '';
  @JSONField(name: "third_order_id")
  String? thirdOrderId = '';
  @JSONField(name: "nonce_str")
  String? nonceStr = '';
  @JSONField(name: "pay_way")
  String? payWay = '';
  @JSONField(name: "leshua_order_id")
  String? leshuaOrderId = '';
  @JSONField(name: "merchant_id")
  String? merchantId = '';
  @JSONField(name: "trade_type")
  String? tradeType = '';
  @JSONField(name: "cost_time")
  String? costTime = '';
  @JSONField(name: "sign_type")
  String? signType = '';
  String? amount = '';
  @JSONField(name: "error_msg")
  String? errorMsg = '';
  @JSONField(name: "sub_merchant_id")
  String? subMerchantId = '';
  @JSONField(name: "refund_amount")
  String? refundAmount = '';
  @JSONField(name: "error_code")
  String? errorCode = '';
  String? status = '';

  PayResultDtoTradeResult();

  factory PayResultDtoTradeResult.fromJson(Map<String, dynamic> json) =>
      $PayResultDtoTradeResultFromJson(json);

  Map<String, dynamic> toJson() => $PayResultDtoTradeResultToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
