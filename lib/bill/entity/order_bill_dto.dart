import '../entity/abstract_bill_dto.dart';
import '../entity/goods_detail_dto.dart';
import '../../common/standard.dart';

import 'buyer_dto.dart';
import 'finance_detail_dto.dart';
import 'payment_dto.dart';
import 'sender_dto.dart';
import 'td_bill_core.dart';

class OrderBillDTO extends AbstractBillDto {
  /// 仓库ID
  String? ktypeId;

  /// 预估运费本币
  num? estimateFreightFee;

  /// 收货仓库ID
  String? ktypeId2;

  /// 仓库全名
  String? kfullname;

  /// 收货仓库全名
  String? kfullname2;

  /// 发货类型
  String? billDeliverType;

  /// 用于权限展示
  bool? show = false;

  /// 买家运费
  num? currencyOrderBuyerFreightFee;

  /// 入库类单据明细
  List<GoodsDetailDto>? detail;

  String? deliveryType;

  /// 账户结算信息
  List<FinanceDetailDTO>? accountDetail;

  /// 买家信息
  BuyerDto? buyer;

  /// 发货信息
  SenderDTO? senderInfo;

  int? intSubmitState;

  /// 提交状态
  String? submitState;

  int? intAuditState;

  /// 审核状态
  String? auditState;

  int? intOrderOverState;

  /// 单据完成状态
  String? orderOverState;

  /// 是否物流代收
  int? freightCollectType;

  /// 订金金额
  num? currencyDepositTotal;

  /// 收付款到期日
  String? paymentDate;

  /// 交货日期
  String? toDate;

  /// 未完成金额
  num? uncompleteTotal;

  /// 未完成数量
  num? uncompleteQty;

  num? uncompleteSubQty;

  /// wms处理状态：0待处理 ，1处理中，2处理完毕，3取消
  num? wmsRealityQty;

  /// 活动订单
  String? activityNo;

  /// 商品对应是否完成
  int? mappingState;

  /// 交易id
  String? tradeId;

  /// 是否是来自元气的请求
  bool? yuanqiRequestFlag = false;

  /// 是否为平台订单
  bool? platformOrderFlag = false;

  String? platformOrderType;

  /// 有效开始时间
  String? validityStartDate;

  /// 有效结束时间
  String? validityEndDate;

  String? carId;
  String? driverId;
  String? carNumber;
  String? drfullname;

  /// 交货方式 对应枚举
  /// package com.wsgjp.ct.bill.core.handle.entity.enums.SelfDeliveryModeEnum
  int? selfDeliveryMode;

  ///订单关联的单据
  List<TdBillCoreDTO>? goodBillList;

  List<TdBillCoreDTO>? accountBillList;

  int? intVchtype;

  String? vchtypeName;

  OrderBillDTO({
    this.ktypeId,
    this.estimateFreightFee,
    this.ktypeId2,
    this.kfullname,
    this.kfullname2,
    this.billDeliverType,
    this.show,
    this.currencyOrderBuyerFreightFee,
    this.detail,
    this.deliveryType,
    this.accountDetail,
    this.buyer,
    this.senderInfo,
    this.intSubmitState,
    this.submitState,
    this.intAuditState,
    this.auditState,
    this.intOrderOverState,
    this.orderOverState,
    this.freightCollectType,
    this.currencyDepositTotal,
    this.paymentDate,
    this.toDate,
    this.uncompleteTotal,
    this.uncompleteQty,
    this.uncompleteSubQty,
    this.wmsRealityQty,
    this.activityNo,
    this.mappingState,
    this.tradeId,
    this.yuanqiRequestFlag,
    this.platformOrderFlag,
    this.platformOrderType,
    this.validityStartDate,
    this.validityEndDate,
    this.carId,
    this.driverId,
    this.carNumber,
    this.drfullname,
    this.selfDeliveryMode,
    this.goodBillList,
    this.accountBillList,
    this.intVchtype,
    this.vchtypeName,
  });

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = super.toJson();
    map.addAll({
      'ktypeId': ktypeId,
      'estimateFreightFee': estimateFreightFee,
      'ktypeId2': ktypeId2,
      'kfullname': kfullname,
      'kfullname2': kfullname2,
      'billDeliverType': billDeliverType,
      'show': show,
      'currencyOrderBuyerFreightFee': currencyOrderBuyerFreightFee,
      'detail': detail?.map((e) => e.toJson()).toList(),
      'deliveryType': deliveryType,
      'accountDetail': accountDetail?.map((e) => e.toJson()).toList(),
      'buyer': buyer?.toJson(),
      'senderInfo': senderInfo?.toJson(),
      'intSubmitState': intSubmitState,
      'submitState': submitState,
      'intAuditState': intAuditState,
      'auditState': auditState,
      'intOrderOverState': intOrderOverState,
      'orderOverState': orderOverState,
      'freightCollectType': freightCollectType,
      'currencyDepositTotal': currencyDepositTotal,
      'paymentDate': paymentDate,
      'toDate': toDate,
      'uncompleteTotal': uncompleteTotal,
      'uncompleteQty': uncompleteQty,
      'uncompleteSubQty': uncompleteSubQty,
      'wmsRealityQty': wmsRealityQty,
      'activityNo': activityNo,
      'mappingState': mappingState,
      'tradeId': tradeId,
      'yuanqiRequestFlag': yuanqiRequestFlag,
      'platformOrderFlag': platformOrderFlag,
      'platformOrderType': platformOrderType,
      'validityStartDate': validityStartDate,
      'validityEndDate': validityEndDate,
      'carId': carId,
      'driverId': driverId,
      'carNumber': carNumber,
      'drfullname': drfullname,
      'selfDeliveryMode': selfDeliveryMode,
      'goodBillList': goodBillList?.map((e) => e.toJson()).toList(),
      'accountBillList': accountBillList?.map((e) => e.toJson()).toList(),
      'intVchtype': intVchtype,
      'vchtypeName': vchtypeName,
    });
    return map;
  }

  OrderBillDTO.fromMap(Map<String, dynamic> map) : super.fromMap(map) {
    ktypeId = map['ktypeId'];
    estimateFreightFee = map['estimateFreightFee'];
    ktypeId2 = map['ktypeId2'];
    kfullname = map['kfullname'];
    kfullname2 = map['kfullname2'];
    billDeliverType = map['billDeliverType'];
    show = map['show'];
    currencyOrderBuyerFreightFee = map['currencyOrderBuyerFreightFee'];
    detail = (map['detail'] as List?)
        ?.map((e) => GoodsDetailDto.fromMap(e))
        .toList();
    deliveryType = map['deliveryType'];
    accountDetail = (map['accountDetail'] as List?)
        ?.map((e) => FinanceDetailDTO.fromMap(e))
        .toList();
    buyer = (map['buyer'] as Map<String, dynamic>?)
        ?.let<BuyerDto>((map) => BuyerDto.fromMap(map));
    senderInfo = (map['senderInfo'] as Map<String, dynamic>?)
        ?.let((value) => SenderDTO.fromMap(value));
    businessType = map['businessType'];
    intSubmitState = map['intSubmitState'];
    submitState = map['submitState'];
    intAuditState = map['intAuditState'];
    auditState = map['auditState'];
    intOrderOverState = map['intOrderOverState'];
    orderOverState = map['orderOverState'];
    freightCollectType = map['freightCollectType'];
    currencyDepositTotal = map['currencyDepositTotal'];
    paymentDate = map['paymentDate'];
    toDate = map['toDate'];
    uncompleteTotal = map['uncompleteTotal'];
    uncompleteQty = map['uncompleteQty'];
    uncompleteSubQty = map['uncompleteSubQty'];
    wmsRealityQty = map['wmsRealityQty'];
    activityNo = map['activityNo'];
    mappingState = map['mappingState'];
    tradeId = map['tradeId'];
    yuanqiRequestFlag = map['yuanqiRequestFlag'];
    platformOrderFlag = map['platformOrderFlag'];
    platformOrderType = map['platformOrderType'];
    validityStartDate = map['validityStartDate'];
    validityEndDate = map['validityEndDate'];
    carId = map['carId'];
    driverId = map['driverId'];
    carNumber = map['carNumber'];
    drfullname = map['drfullname'];
    selfDeliveryMode = map['selfDeliveryMode'];
    goodBillList = (map['goodBillList'] as List?)
        ?.map((e) => TdBillCoreDTO.fromMap(e))
        .toList();
    accountBillList = (map['accountBillList'] as List?)
        ?.map((e) => TdBillCoreDTO.fromMap(e))
        .toList();
    intVchtype = map['intVchtype'];
    vchtypeName = map['vchtypeName'];
  }
}
