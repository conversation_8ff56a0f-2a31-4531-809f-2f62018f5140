import 'bill_save_exception_detail_dto.dart';

/// @ClassName:      bill_save_exception_dto.dart
/// @CreateDate:    2020/3/30 14:44
/// @Author:         tlan
/// @Description:单据保存异常对象
class BillSaveExceptionDto {
  List<BillSaveExceptionDetailDTO>? detailList;
  String? message;

  static BillSaveExceptionDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return BillSaveExceptionDto();
    BillSaveExceptionDto saveExceptionDto = BillSaveExceptionDto();
    saveExceptionDto.detailList = (map['detailList'] as List?)
            ?.map((o) => BillSaveExceptionDetailDTO.fromMap(o))
            .toList() ??
        [];
    saveExceptionDto.message = map['message'];
    return saveExceptionDto;
  }

  Map toJson() => {
        "detailList": detailList,
        "message": message,
      };
}
