import 'order_bill_item_entity.dart';

class OrderBillQueryListDto {
  List<OrderBillItem> list = [];
  String? total;

  static OrderBillQueryListDto fromModel(Map<String, dynamic>? map) {
    OrderBillQueryListDto billListQueryDtoBean = OrderBillQueryListDto();
    if (map == null) return billListQueryDtoBean;
    billListQueryDtoBean.total = map['total'];
    billListQueryDtoBean.list = []..addAll(
        (map['list'] as List ?? []).map((o) => OrderBillItem.fromMap(o)));
    return billListQueryDtoBean;
  }

  Map toJson() => {
        "total": total,
        "list": list,
      };
}
