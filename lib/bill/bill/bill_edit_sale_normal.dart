import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';

import '../../bill/widget/mixin/sale_business_mixin.dart';
import '../../bill/widget/mixin/sale_normal_mixin.dart';
import '../../bill/widget/mixin/select_vip_mixin.dart';
import '../../bill/widget/sale/sale_coupon_view.dart';
import '../../common/style/app_colors.dart';
import '../../common/style/app_pos_size.dart';
import '../../common/tool/payment_orders_mixin.dart';
import '../../common/tool/sp_tool.dart';
import '../../hotkey/hotkey.dart';
import '../../hotkey/hotkey_manager.dart';
import '../../iconfont/icon_font.dart';
import '../../login/entity/store/barcode_scale_config.dart';
import '../../login/entity/store/barcode_scale_type_enum.dart';
import '../../offline/offline_tool.dart';
import '../../vip/utils/vip_util.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../entity/barcode_scale_scan.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/ss_card_dto.dart';
import '../tool/promotion/promotion.dart';
import '../tool/promotion/score.dart';
import '../widget/bill_edit_sale_table.dart';
import '../widget/mixin/bill_mixin.dart';
import '../widget/mixin/hotkey_mixin.dart';

///普通开单页面（非选品开单页面）
class BillEditSaleNormalPage extends BaseStatefulPage {
  final Function() submitCallback;

  const BillEditSaleNormalPage({Key? key, required this.submitCallback})
    : super(key: key, showAppBar: false);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      BillEditSaleNormalPageState();
}

class BillEditSaleNormalPageState
    extends BaseStatefulPageState<BillEditSaleNormalPage>
    with
        PaymentOrdersMixin,
        SelectVipMixin<BillEditSaleNormalPage>,
        BillMixin<BillEditSaleNormalPage>,
        SaleBusinessMixin<BillEditSaleNormalPage>,
        HotkeyMixin<BillEditSaleNormalPage>,
        SaleNormalMixin<BillEditSaleNormalPage> {
  final double singleButtonWidth = 150;
  final double singleButtonFontSize = 28;

  ///打印预结单弹窗的key
  final GlobalKey _printPreviewKey = GlobalKey();

  bool get enableBarcodeScale =>
      SpTool.getSetting().enableBarcodeScale ?? false;

  BarcodeScaleConfig? get barcodeScaleConfig =>
      SpTool.getStoreInfo()?.barcodeScaleConfig;

  @override
  String getActionBarTitle() => "开单";

  @override
  Future<void> onInitState() {
    addVipListener();
    setScanPaySuccess();
    return loadData();
  }

  @override
  void registerHotkeyCallback() {
    super.registerHotkeyCallback();
    HotkeyManager.registerCallback(HotkeyType.selectGoods, onHotkeyCall);
  }

  ///当回调无法触发，返回false
  @override
  bool onHotkeyCall(HotkeyType hotkeyType) {
    if (super.onHotkeyCall(hotkeyType)) return true;
    if (hotkeyType == HotkeyType.selectGoods) {
      showSearchGoodsDialog();
      return true;
    }
    return false;
  }

  @override
  Widget buildTable() {
    return BillEditSaleTable(
      indexScrollerController: indexScrollerController,
      billType: billType,
      goodsBillDto: goodsBillDto,
      dataChangeCallBack: (GoodsBillDto goodsBill, bool resetFocus) {
        onGoodsListDataChange(goodsBill, resetFocus);
      },
      onSelectPromotionGift: (String promotionGift) {
        selectPromotionGift(promotionGift);
      },
    );
  }

  ///单据汇总信息
  @override
  Widget buildSubDetail() {
    return HaloContainer(
      height: 74.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      children: [
        Expanded(
          child: Align(
            alignment: Alignment.centerLeft,
            child: buildBillPromotionView(showCorner: true),
          ),
        ),
        Text(
          "共计${sumAllQty.toString()}件，原金额¥${sumAllTotal.toString()}，已优惠 ",
          style: TextStyle(
            color: AppColors.secondaryFontColor,
            fontSize: AppPosSize.secondaryTitleFontSize.sp,
          ),
        ),
        Text(
          "¥${sumAllPreferential.toString()}",
          style: TextStyle(
            color: AppColors.totalFontColor,
            fontSize: AppPosSize.secondaryTitleFontSize.sp,
          ),
        ),
        SizedBox(width: 12.h),
        buildPreferentialView(),
      ],
    );
  }

  ///最底部ui
  @override
  Widget buildVipBottom(BuildContext context) {
    // List<Widget> children = [];
    // children.add();

    List<Widget> childrenRow = [];

    childrenRow.add(
      SizedBox(
        width: 450.w,
        child: buildVipInfoView(
          context,
          showNewRights: showGiftCoupon,
          tapNewRights: checkGiftCoupon,
        ),
      ),
    );
    childrenRow.add(buildDivider());

    childrenRow.add(
      Expanded(
        child: Visibility(
          visible: selectedCardList.isNotEmpty,
          child: SizedBox(
            height: 80.h,
            child: SaleCouponView(
              selectedCardList,
              deleteCallback: (index) {
                List<SsCardDto> cardList = List.from(selectedCardList);
                cardList.removeAt(index);
                onCardChanged(selectedCardList: cardList, chooseGift: false);
              },
            ),
          ),
        ),
      ),
    );

    // //预结单按钮
    // childrenRow.add(_buildPrintPreview());
    childrenRow.add(_buildDivisionView());
    //扫码支付
    _buildScanPayView(childrenRow);
    bool isVip = isValidVip(vipInfo);
    bool allowScore =
        scoreConfiguration != null
            ? ScoreDiscountUtil.allowScoreDiscount(
              goodsBillDto,
              scoreConfiguration!,
            )
            : false;

    childrenRow.addAll([
      if (vipInfo != null) ...[
        buildSingleButton(
          "优惠券",
          width: singleButtonWidth.h,
          height: singleButtonHeight.h,
          fontSize: singleButtonFontSize.sp,
          enable: isVip,
          tips: couponCardCount,
          action: () {
            showCouponCardView();
          },
        ),
        _buildDivisionView(),
      ],
      if (billDiscountPermission) _buildBillDiscount(),
      _buildDivisionView(),
      if (manualPreferentialView) _buildManualPreferentialView(),
      if (vipInfo != null) ...[
        _buildDivisionView(),
        buildSingleButton(
          "积分抵现",
          width: singleButtonWidth.h,
          height: singleButtonHeight.h,
          enable: isVip && allowScore,
          fontSize: singleButtonFontSize.sp,
          action: showScoreView,
        ),
      ],
      buildTotalPriceView(context),
    ]);

    return HaloContainer(children: childrenRow);
    //
    // return Row(
    //     mainAxisAlignment: MainAxisAlignment.spaceBetween, children: children);
  }

  ///扫码支付
  void _buildScanPayView(List<Widget> children) {
    if (OffLineTool().isOfflineLogin) {
      return;
    }
    children.add(
      buildSingleButton(
        "扫码支付",
        width: singleButtonWidth.h,
        height: singleButtonHeight.h,
        fontSize: singleButtonFontSize.sp,
        action: () {
          quickScanPayBill();
        },
      ),
    );
    children.add(_buildDivisionView());
  }

  ///总额优惠
  Widget _buildManualPreferentialView() {
    return buildSingleButton(
      "总额优惠",
      width: singleButtonWidth.h,
      height: singleButtonHeight.h,
      fontSize: singleButtonFontSize.sp,
      action: () {
        showManualPreferentialDialog();
      },
    );
  }

  ///整单折扣
  Widget _buildBillDiscount() {
    return buildSingleButton(
      "整单折扣",
      width: singleButtonWidth.h,
      height: singleButtonHeight.h,
      fontSize: singleButtonFontSize.sp,
      action: () {
        showBillDiscountDialog();
      },
    );
  }

  _buildDivisionView() {
    return SizedBox(width: 10.w);
  }

  ///顶部的搜索框、选择商品、挂单、打印等
  @override
  Widget buildTopBody(BuildContext context) {
    return hasSalePermission
        ? Container(
          color: Colors.white,
          height: 100.h,
          padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
          child: Row(
            children: [
              buildSearch(),
              _buildDivisionView(),
              //添加商品
              buildAddGoodsProp(),
              Expanded(child: Container()),
              buildOrderBillView(),
              buildClearBtn(),
              buildPrint(),
            ],
          ),
        )
        : Container();
  }

  ///顶部清空按钮
  Widget buildClearBtn() {
    return buildTopButton("清空", IconNames.shanchu, onTap: doClear);
  }

  ///顶部备注按钮
  Widget buildMemoBtn() {
    return buildTopButton("备注", IconNames.xiaoshoudingdan, onTap: memoBill);
  }

  ///顶部打印上单按钮/打印预结单按钮
  Widget buildPrint() {
    String iconColorStr = ColorUtil.color2String(const Color(0xFF3478FF));
    return HaloButton(
      key: _printPreviewKey,
      icon: IconFont(IconNames.dayinshangdan, color: iconColorStr, size: 22.w),
      foregroundColor: const Color(0xFF3478FF),
      backgroundColor: const Color(0xFFE8F1FF),
      onPressed: doBeforeBill,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text("打印上单", style: TextStyle(fontSize: 26.sp)),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              RenderBox? r =
                  _printPreviewKey.currentContext?.findRenderObject()
                      as RenderBox?;
              double width = r?.size.width ?? 100.w;
              double height = r?.size.height ?? 80.h;
              HaloPopWindow().show(
                _printPreviewKey,
                gravity: PopWindowGravity.bottom,
                intervalTop: 4.h,
                backgroundColor: Colors.transparent,
                child: SizedBox(
                  height: height,
                  width: width,
                  child: buildTopButton(
                    "打印预结单",
                    IconNames.dayinshangdan,
                    margin: EdgeInsets.zero,
                    onTap: () {
                      printPreview();
                      HaloPopWindow().disMiss();
                    },
                  ),
                ),
              );
            },
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
              child: IconFont(
                IconNames.jiantou,
                color: iconColorStr,
                size: 12.w,
              ),
            ),
          ),
        ],
      ),
    );
    // return buildTopButton("打印上单", IconNames.dayinshangdan, onTap: () {
    //   doBeforeBill();
    // });
  }

  ///构建构建顶部右侧蓝底蓝字的按钮
  Widget buildTopButton(
    String title,
    IconNames name, {
    Widget? hasIcon,
    Function? onTap,
    Color foregroundColor = const Color(0xFF3478FF),
    Color backgroundColor = const Color(0xFFE8F1FF),
    EdgeInsets? margin,
  }) {
    margin ??= EdgeInsets.only(right: 12.w);
    Widget icon =
        hasIcon ??
        IconFont(
          name,
          color: ColorUtil.color2String(foregroundColor),
          size: 22.w,
        );
    return Container(
      margin: margin,
      child: HaloButton(
        icon: icon,
        foregroundColor: foregroundColor,
        backgroundColor: backgroundColor,
        fontSize: 26.sp,
        text: title,
        onPressed: () => onTap?.call(),
      ),
    );
  }

  ///添加挂单、取单
  Widget buildOrderBillView() {
    return Row(
      children: [
        buildChoseGiftButton(),
        buildMemoBtn(),
        // buildTopButton("退货", IconNames.guadan, onTap: () async {
        //   doSaleBack();
        //   return;
        // }),
        buildTopButton(
          draftString,
          IconNames.guadan,
          onTap: () {
            doDraft();
          },
        ),
      ],
    );
  }

  ///选择赠品按钮
  Widget buildChoseGiftButton() {
    Widget choseGift;
    if (PromotionUtil.canSelectGift(goodsBillDto)) {
      choseGift = buildTopButton(
        "选择赠品",
        IconNames.guadan,
        hasIcon: Image.asset('assets/images/xuanzezengpin.png', width: 28.w),
        foregroundColor: const Color(0xFFFF2626),
        backgroundColor: const Color(0xFFFFEDE3),
        onTap: () {
          selectPromotionGift();
        },
      );
    } else {
      choseGift = buildTopButton(
        "选择赠品",
        IconNames.guadan,
        hasIcon: Image.asset(
          'assets/images/xuanzezengpin.png',
          width: 28.w,
          color: ColorUtil.stringColor("#bbbbbb"),
        ),
        foregroundColor: const Color(0xFFbbbbbb),
        backgroundColor: const Color(0xFFEEEEEE),
      );
    }
    return choseGift;
  }

  @override
  void doScanCode(
    String text, {
    BarcodeScaleScan? barcodeScaleScan,
    BarcodeScaleConfig? barcodeScaleConfig,
  }) {
    searchString = text;

    ///条码秤解析，未开启不会解析，内部处理
    BarcodeScaleScan? barcodeScaleScan = pTypeBarCodeScale(text);
    super.doScanCode(
      searchString,
      barcodeScaleScan: barcodeScaleScan,
      barcodeScaleConfig: this.barcodeScaleConfig,
    );
  }

  ///获取条码秤的解析信息
  BarcodeScaleScan? pTypeBarCodeScale(String scanMessage) {
    ///无需验证条码称条码验证的条件：
    ///1： 未开启条码称开关
    ///2：未配置条码称配置
    ///3：配置为13位扫码结果不为13位 （兼容部分扫码设备，因首位为0被截断，导致扫码位数为12位）
    ///4：配置为18位扫码结果不为18位（兼容部分扫码设备，因首位为0被截断，导致扫码位数17位）
    if (!enableBarcodeScale ||
        null == barcodeScaleConfig ||
        (BarcodeScaleTypeTool.isBarcodeScale13(
              barcodeScaleConfig?.outputFormatType,
            ) &&
            (searchString.length != 13 && searchString.length != 12)) ||
        (BarcodeScaleTypeTool.isBarcodeScale18(
              barcodeScaleConfig?.outputFormatType,
            ) &&
            searchString.length != 18 &&
            searchString.length != 17)) {
      return null;
    }

    ///  F：标识位 W:商品代码 E：金额 N：重量 C：正校验码 O：反校验码
    ///  条码配置位
    ///  18位重量+金额码，1F+6W+5N+5E+C(O)；2F+5W+5N+5E+C(O）
    ///  18位金额+重量码，1F+6W+5E+5N+C(O)；2F+5W+5E+5N+C(O)，
    ///  13位金额码，1F+6W+5E+C(O）；2F+5W+5E+C(O），
    ///  13位重量码，1F+6W+5N+C(O）；2F+5W+5N+C(O），

    BarcodeScaleScan barcodeScaleScan = BarcodeScaleScan();
    searchString = StringUtil.trim(searchString);

    ///兼容部分扫码设备首位为0时，被自动截断自动在首位补充0，首位为标识位，不影响扫码结果
    if (searchString.length == 12 || searchString.length == 17) {
      searchString = "0$searchString";
    }
    if (searchString.length != 13 && searchString.length != 18) {
      return null;
    }

    ///标识位即F（占位1或者2位）
    barcodeScaleScan.markPosition = searchString.substring(
      0,
      barcodeScaleConfig?.markDigit ?? 1,
    );

    ///条码占位即W（5位或者6位，根据标识的占位定 标识位+条码位总共7位）
    barcodeScaleScan.pTypePosition = StringUtil.trimLeft(
      searchString.substring(barcodeScaleScan.markPosition.length, 7),
      trimStr: "0",
    );

    ///第三部分，根据条码秤配置区分可能是金额位也可能是重量位，位数都为5位，
    String thirdPart = StringUtil.trimLeft(
      searchString.substring(7, 12),
      trimStr: "0",
    );

    ///第四部分，仅18位的条码才支持，可能是金额位也可能是重量位
    String fourPart = "";
    if (BarcodeScaleTypeTool.isBarcodeScale18(
          barcodeScaleConfig?.outputFormatType,
        ) &&
        searchString.length == 18) {
      fourPart = StringUtil.trimLeft(
        searchString.substring(12, searchString.length - 1),
        trimStr: "0",
      );
    }

    ///第三位为金额码的配置解析
    if (BarcodeScaleTypeTool.isFirstTotal(
      barcodeScaleConfig?.outputFormatType,
    )) {
      barcodeScaleScan.totalPosition = handleDigital(
        thirdPart,
        barcodeScaleConfig?.totalDigit ?? 0,
      );
      barcodeScaleScan.weightPosition = handleDigital(
        fourPart,
        barcodeScaleConfig?.weightDigit ?? 0,
      );
    }
    ///第三位为重量码配置解析
    else {
      barcodeScaleScan.weightPosition = handleDigital(
        thirdPart,
        barcodeScaleConfig?.weightDigit ?? 0,
      );

      barcodeScaleScan.totalPosition = handleDigital(
        fourPart,
        barcodeScaleConfig?.totalDigit ?? 0,
      );
    }
    return barcodeScaleScan;
  }

  ///将字符串按照小数位数增加小数点
  ///主要用于对条码秤返回的重量和金额的值处理
  ///例如：字符串str 2901,小数位数digit为1，则返回为290.1
  String handleDigital(String str, int digit) {
    if (StringUtil.isEmpty(str) || digit == 0) {
      return str;
    }

    ///字符长度小于小数位数时，前面补零。例如：str=1 小数位数为2时，需要不成01，便于后续变成0.01
    if (str.length < digit) {
      while (str.length < digit) {
        str = "0$str";
      }
    }

    ///整数位
    String intStr = str.substring(0, str.length - digit);

    ///小数位
    String digitStr = str.substring(str.length - digit);

    ///整数位为空，整数位补0
    if (StringUtil.isEmpty(intStr)) {
      intStr = "0";
    }

    ///小数位为空，直接返回结果
    if (StringUtil.isEmpty(digitStr)) {
      return intStr;
    }
    return "$intStr.$digitStr";
  }
}
