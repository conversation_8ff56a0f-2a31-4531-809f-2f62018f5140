import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_container.dart';
import '../../../common/keyboard_hidden.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/selector/scan_select.dart';
import 'widget/base_common_dialog.dart';

typedef StringCallback = void Function(String);

class BillChannelScan extends StatefulWidget {
  final StringCallback callback;

  const BillChannelScan({required this.callback, Key? key}) : super(key: key);

  @override
  State<BillChannelScan> createState() => _BillChannelScanState();
}

class _BillChannelScanState extends BaseCommonDialogState<BillChannelScan> {
  @override
  // TODO: implement height
  double get height => 370.w;

  @override
  // TODO: implement title
  String get title => "输入核销码";

  @override
  // TODO: implement width
  double get width => 700.w;

  final KeyboardHiddenFocusNode searchFocusNode = KeyboardHiddenFocusNode();
  TextEditingController textEditingController = TextEditingController();

  @override
  Widget buildContent(BuildContext context) {
    // TODO: implement buildContent
    return HaloContainer(
      direction: Axis.vertical,
      children: [
        super.buildContent(context),
        ScanSelectWidget(searchFocusNode: searchFocusNode, width: double.infinity,controller: textEditingController, decoration: BoxDecoration(
          color: Colors.transparent,
          border:  Border.all(color: ColorUtil.stringColor("#DDDDDD"), width: 1.w),
          borderRadius: BorderRadius.all(Radius.circular(4.w)),
        ), margin:EdgeInsets.symmetric(horizontal: 23.w, vertical: 36.w),hint: "扫描核销码",onSubmitted: (text){
          textEditingController.text = text;
          onSubmittedButton(context);
        }),

        divider,
        Container(
          padding: EdgeInsets.only(bottom: 18.w, top: 18.w),
          child: HaloButton(
            backgroundColor: ColorUtil.stringColor("#4679FC"),
            width: 318.w,
            height: 66.w,
            onPressed: () {
              onSubmittedButton(context);
            },
            text: "确定",
          ),
        )
      ],
    );
    return super.buildContent(context);
  }

  onSubmittedButton(BuildContext context) {
    if(textEditingController.text.isEmpty) {
      HaloToast.showError(context,msg: "核销码不能为空");
      return;
    }
    widget.callback(textEditingController.text);
    searchFocusNode.unfocus();
    super.onCloseButtonClick(context);
  }

  @override
  void onCloseButtonClick(BuildContext context) {
    // TODO: implement onCloseButtonClick
    searchFocusNode.unfocus();
    super.onCloseButtonClick(context);
  }
}
