import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../../common/style/app_color_helper.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../iconfont/icon_font.dart';
import '../../../print/tool/print_tool.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../entity/goods_detail_dto.dart';
import 'bill_channel_complete_mixin.dart';
import 'model/bill_channel_detail.dart';
import 'model/bill_channel_detail_request.dart';
import 'model/bill_channel_model.dart';
import 'model/bill_channel_task_detail.dart';
import 'widget/bill_channel_order_snBatch_dialog.dart';

mixin BillChannelDetailPopMixin<T extends StatefulWidget>
    on State<T>, BillChannelCompleteMixin<T> {
  bool _isComplete = false;

  bool get isComplete => _isComplete;

  set isComplete(bool complete) => _isComplete = complete;
  String tradeCode = "";
  BillChannelDetailDto detailPopDto = BillChannelDetailDto();

  Widget buildDetailPop(BuildContext context, BillChannelDetailDto detailPopDto,
      String tradeCode) {
    this.detailPopDto = detailPopDto;
    this.tradeCode = tradeCode;
    List<BillChannelTaskDetail> taskDetails = detailPopDto.deliverDetail ?? [];
    return Drawer(
      width: 1044.w,
      child: Column(
        children: [
          Visibility(
            visible: !isComplete,
            child: Expanded(
              child: SingleChildScrollView(
                child: HaloContainer(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  padding: EdgeInsets.only(top: 15.w, bottom: 20.w),
                  direction: Axis.vertical,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    HaloContainer(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      padding: EdgeInsets.only(left: 30.w, right: 30.w),
                      children: [
                        HaloPosLabel(
                          "到店自提",
                          textStyle: TextStyle(
                              fontSize: 26.sp,
                              color: ColorUtil.stringColor("#333333"),
                              fontWeight: FontWeight.w600),
                        ),
                        IconButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            icon: IconFont(IconNames.close))
                      ],
                    ),
                    Divider(
                      color: ColorUtil.stringColor("#D0D0D0"),
                      height: 2.w,
                    ),
                    _buildLabTitle("购买信息"),
                    SizedBox(
                      height: 8.w,
                    ),
                    SizedBox(
                      height: 130.w * taskDetails.length,
                      child: ListView.builder(
                          itemCount: taskDetails.length,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (item, index) {
                            return _buildContentItem(
                                taskDetails[index]);
                          }),
                    ),
                    _buildLabTitle(
                        "提货地址：${detailPopDto.buyer?.customerReceiverFullAddress}"),
                    _buildLabTitle(
                        "提货时间：${detailPopDto.freightInfo?.deliverTime ?? ""}"),
                    // _buildLabTitle("提货时间：${DateUtil.DateUtil.getDateStrByDateTime(DateTime.now(),
                    //     format: DateUtil.DateFormat.NORMAL)}"),
                    _buildLabTitle(
                        "提货人员：${detailPopDto.buyer?.customerReceiver} ${detailPopDto.buyer?.customerReceiverMobile}"),
                    _buildLabTitle("支付金额：¥${detailPopDto.buyerTradeTotal}"),
                    _buildLabTitle("支付方式：微信支付"),
                    _buildLabTitle("买家留言：${detailPopDto.buyerMessage}"),
                  ],
                ),
              ),
            ),
          ),
          Visibility(
            visible: !isComplete,
            child: Divider(
              color: ColorUtil.stringColor("#DDDDDD"),
              height: 1,
            ),
          ),
          Visibility(
            visible: !isComplete,
            child: Container(
              height: 128.w,
              padding: EdgeInsets.only(top: 20.w, bottom: 18.w),
              child: HaloButton(
                backgroundColor: ColorUtil.stringColor("#4679FC"),
                width: 700.w,
                height: 90.w,
                text: "全部核销发货",
                onPressed: () {
                  verificationBtn(context);
                },
              ),
            ),
          ),
          Visibility(visible: isComplete, child: buildComplete(context))
        ],
      ),
    );
  }

  _buildContentItem(GoodsDetailDto outDetail) {
    return GestureDetector(
      onTap: (){
        if(outDetail.batchenabled || outDetail.snenabled>0){
          if(outDetail.costMode==1){
            HaloToast.showInfo(context,msg:"个别加价商品暂不支持修改批次号/序列号");
          }else{
            DialogUtil.showAlertDialog(context, child:  BillChannelOrderSnBatchDialog(detailDto: outDetail)).then((value){
              if(value != null){
                BillChannelTaskDetail taskDetail = value;
                taskDetail.serialNo = taskDetail.serialNoList;
                taskDetail.batchno = taskDetail.batchNo;
                BillChannelDetailRequest request = BillChannelDetailRequest();
                request.taskDetails = this.detailPopDto.deliverDetail!;
                request.taskId = this.detailPopDto.warehouseTaskId!;
                BillChannelModel.updateTaskDetails(context, request).then((value){
                  if(value){
                    HaloToast.showSuccess(context,msg: "编辑成功");
                  }
                });
              }else{
                BillChannelTaskDetail taskDetail = this.detailPopDto as BillChannelTaskDetail;
                taskDetail.serialNoList = taskDetail.serialNo??[];
                taskDetail.batchNo = taskDetail.batchno??"";
              }

            });
          }
        }
      },
      child: Container(
        margin: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 20.w),
        padding: EdgeInsets.only(left: 20.w, right: 20.w),
        height: 110.w,
        decoration: BoxDecoration(
            color: const Color(0xFFFCFCFC),
            borderRadius: const BorderRadius.all(Radius.circular(5.0)),
            border:
                Border.all(color: ColorUtil.stringColor("#DDDDDD"), width: 1.w)),
        child: HaloContainer(
          children: [
            HaloImage(
              outDetail.picUrl,
              width: 80.w,
              height: 80.w,
            ),
            Expanded(
              child: HaloContainer(
                height: 110.w,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                padding: EdgeInsets.only(top: 15.w, left: 15.w, right: 50.w),
                children:[
                  HaloPosLabel(
                    outDetail.pFullName ?? "",
                    maxLines: 2,
                    textStyle: TextStyle(
                        fontSize: 24.sp, color: ColorUtil.stringColor("#333333")),
                  ),
                  Visibility(
                      visible:outDetail.snenabled > 0,
                      child: Container(
                        width: 28.w,
                        height: 28.w,
                        margin: EdgeInsets.only(right: 8.w,top: 5.w),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: const Color(0xFFF9CD90),
                            border: Border.all(
                                color: const Color(0xFFF9CD90),
                                width: 1.h,
                                style: BorderStyle.solid),
                            borderRadius: const BorderRadius.all(Radius.circular(3))),
                        child: HaloPosLabel("序",
                            textStyle: TextStyle(
                                height: 1,
                                color:  Colors.white,
                                decoration: TextDecoration.none,
                                fontSize: 20.sp,
                                fontWeight: FontWeight.normal)),
                      )),
                  Visibility(
                      visible: outDetail.batchenabled,
                      child: Container(
                        width: 28.w,
                        height: 28.w,
                        margin: EdgeInsets.only(right: 8.w,top: 5.w),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: const Color(0xFF81D3F8),
                            border: Border.all(
                                color: const Color(0xFF81D3F8),
                                width: 1.h,
                                style: BorderStyle.solid),
                            borderRadius: const BorderRadius.all(Radius.circular(3))),
                        child: HaloPosLabel("批",
                            textStyle: TextStyle(
                                color:  Colors.white,
                                height: 1,
                                decoration: TextDecoration.none,
                                fontSize: 20.sp,
                                fontWeight: FontWeight.normal)),
                      )),
                ]
              ),
            ),
            HaloContainer(
              direction: Axis.vertical,
              crossAxisAlignment: CrossAxisAlignment.end,
              padding: EdgeInsets.only(top: 20.w, left: 15.w, right: 10.w),
              children: [
                Expanded(
                  child: HaloPosLabel(
                    "¥${outDetail.currencyPrice}",
                    textStyle: TextStyle(
                        fontSize: 22.sp,
                        color: AppColorHelper(context).getTitleBoldTextColor()),
                  ),
                ),
                SizedBox(
                  height: 8.w,
                ),
                Expanded(
                  child: HaloPosLabel(
                    "x${outDetail.unitQty}",
                    textStyle: TextStyle(
                        fontSize: 22.sp,
                        color: AppColorHelper(context).getTitleBoldTextColor()),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  _buildLabTitle(title) {
    return Container(
      padding: EdgeInsets.only(left: 30.w, top: 15.w, bottom: 0.w),
      margin: EdgeInsets.only(bottom: 10.w),
      child: HaloPosLabel(
        title,
        textStyle:
            TextStyle(fontSize: 26.w, color: ColorUtil.stringColor("#333333")),
      ),
    );
  }

  @override
  completeBtn() {
    Navigator.pop(context);
  }

  @override
  doPrintData() {
   //打印小票
    PrintTool.printChannelBill(context,detailPopDto);
  }

  verificationBtn(BuildContext context) {
    bool sendFlag = false;
    //SEND 待发货 ，SENDED已发货
    if (detailPopDto.state?.processState == "SENDED") {
      sendFlag = true;
    } else {
      sendFlag = false;
    }
    BillChannelModel.doVerificate(
            context, tradeCode, detailPopDto.warehouseTaskId ?? "", sendFlag)
        .then((value) {
      if (value == true) {
        setState(() {
          isComplete = true;
        });
      }
    });
  }
}
