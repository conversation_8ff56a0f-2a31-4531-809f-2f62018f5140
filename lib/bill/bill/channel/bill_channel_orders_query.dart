import 'package:flutter/material.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/halo_label.dart';
import 'package:haloui/widget/list/halo_list.dart';

import '../../../common/keyboard_hidden.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/performance_capture_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../stockcheck/widget/filter_box_widget.dart';
import '../../../widgets/base/base_list.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/selector/scan_select.dart';
import 'bill_channel_complete_mixin.dart';
import 'bill_channel_detail_pop_mixin.dart';
import 'bill_channel_scan.dart';
import 'model/bill_channel_detail.dart';
import 'model/bill_channel_filter_entity.dart';
import 'model/bill_channel_model.dart';
import 'model/bill_channel_request.dart';
import 'widget/bill_channel_filter_dialog.dart';
import 'widget/bill_channel_order_detail_widget.dart';

///全渠道订单
class BillChannelOrdersQuery extends BaseStatefulPage {
  const BillChannelOrdersQuery({Key? key})
      : super(key: key, rightFlex: 3, showEndDrawer: true);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _BillChannelOrdersQueryState();
}

class _BillChannelOrdersQueryState
    extends BaseStatefulPageState<BillChannelOrdersQuery>
    with BillChannelCompleteMixin, BillChannelDetailPopMixin {
  final KeyboardHiddenFocusNode searchFocusNode = KeyboardHiddenFocusNode();

  Map<String, dynamic> requestPageSize = {};
  bool loadMore = true;
  int pageIndex = 1;
  int pageSize = 15;
  int selectState = 0;
  int selectIndex = 0;
  List<BillChannelDetailDto> dataSource = [];
  TextEditingController editingController = TextEditingController();
  BillChannelDetailDto? detailDto;
  late BillChannelRequestPage billChannelRequest;

  // BillChannelDetailDto detailPopDto = BillChannelDetailDto();
  ScrollController scrollController = ScrollController();
  late BuildContext leftContext;
  late FilterData filterData;
  late FilterData oldFilterData;

  @override
  void initState() {
    initData();
    super.initState();
  }

  void initData({String? vchcode}) {
    editingController.text = "";
    pageIndex = 1;
    pageSize = 15;
    // isHasMoreData = true;
    //默认7天

    DateTime now = DateTime.now();
    // 创建一个新的DateTime对象，设置时间为23:59:59
    //默认为当前时间的23:59:59
    DateTime afterTime = DateTime(now.year, now.month, now.day, 23, 59, 59);
    //起始日期默认为起始日的前一个月
    DateTime nowDateTime = DateTime(now.year, now.month, now.day, 00, 00, 00);
    DateTime beforeTime = nowDateTime.add(const Duration(days: -30));

    BillChannelModel.getStatus(context).then((BillChannelFilterEntity value) {
      //获取筛选条件
      //平台
      List<FilterBoxItem>? platformBoxItems =
          value.eshopType?.map((BillChannelFilterEshopType e) {
        return FilterBoxItem(
            text: e.description ?? "未知平台${e.key ?? e.code}",
            value: e.key ?? e.code);
      }).toList();
      List<FilterBoxItem>? eshopBoxItems =
          value.eshops?.where((element) => element.enable ?? false).map((e) {
        return FilterBoxItem(text: e.fullname ?? "未知店铺${e.id}", value: e.id);
      }).toList();

      List<FilterBoxItem>? deliveryBoxItems =
          value.selfDeliveryMode?.map((BillChannelFilterSelfDeliveryMode e) {
        return FilterBoxItem(
            text: e.description ?? "未知平台${e.key}", value: e.key);
      }).toList();

      filterData = FilterData.build(
          beforeTime: beforeTime,
          afterTime: afterTime,
          platformBoxItems: platformBoxItems ?? [],
          eshopBoxItems: eshopBoxItems ?? [],
          deliveryBoxItems: deliveryBoxItems ?? [],
          selectPlatformBoxItems: [],
          selectEshopBoxItems: []);
      reloadRequestData(vchcode: vchcode);
    });
  }

  reloadRequestData({String? vchcode}) async {
    pageIndex == 1;
    selectIndex = 0;
    dataSource.clear();
    PerformanceCaptureUtil.start(PerformanceTimeName.channelBill);
    List<BillChannelDetailDto> response =
        await requestDetailData(vchcode: vchcode);
    setState(() {
      loadMore = response.length == pageSize;
      dataSource.addAll(response);
    });
    PerformanceCaptureUtil.end(PerformanceTimeName.channelBill);
    if (response.isNotEmpty) {
      getBillDetail(response[selectIndex].warehouseTaskId);
    } else {
      detailDto = null;
    }
  }

  Future<List<BillChannelDetailDto>> requestDetailData(
      {String? vchcode}) async {
    getParams(vchcode: vchcode);
    return BillChannelModel.getChannelInfoList(context, billChannelRequest);
  }

  getParams({String? vchcode}) {
    billChannelRequest = BillChannelRequestPage();
    billChannelRequest.pageIndex = pageIndex;
    billChannelRequest.pageSize = pageSize;
    billChannelRequest.queryParams.beginTime =
        formatDateStringToUtc(filterData.textStartTimeController.text);
    billChannelRequest.queryParams.endTime =
        formatDateStringToUtc(filterData.textEndTimeController.text);
    billChannelRequest.queryParams.stateEnum =
        selectState == 0 ? "WAITE" : "DONE";
    billChannelRequest.queryParams.ktypeId = SpTool.getStoreInfo()!.ktypeId;
    if (filterData.textBillNumberController.text.isNotEmpty) {
      billChannelRequest.queryParams.billNumber =
          filterData.textBillNumberController.text;
    }
    if (filterData.textDeliveryTypeController.text.isNotEmpty) {
      billChannelRequest.queryParams.selfDeliveryModeEnum =
          filterData.textDeliveryTypeController.text;
    }
    if (filterData.selectEshopBoxItems.isNotEmpty) {
      List<String> eShops =
          filterData.selectEshopBoxItems.map((FilterBoxItem e) {
        return e.value.toString();
      }).toList();
      billChannelRequest.queryParams.orgId = eShops;
    }

    if (filterData.selectPlatformBoxItems.isNotEmpty) {
      List<String> platform =
          filterData.selectPlatformBoxItems.map((FilterBoxItem e) {
        return e.value.toString();
      }).toList();
      billChannelRequest.queryParams.eshopType = platform;
    }
  }

  @override
  Widget buildRightBody(BuildContext context) {
    return BillChannelOrderDetailWidget(() {
      initData();
    }, detailDto);
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    leftContext = context;
    return HaloContainer(
      direction: Axis.vertical,
      margin: EdgeInsets.all(24.w),
      children: [
        Expanded(
          child: HaloContainer(
            direction: Axis.vertical,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              HaloContainer(
                border: Border(
                    bottom:
                        BorderSide(color: AppColors.dividerColor, width: 2.w)),
                children: [
                  Expanded(
                      child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            setState(() {
                              selectState = 0;
                            });
                            reloadRequestData();
                          },
                          child: HaloContainer(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                    decoration: BoxDecoration(
                                        border: Border(
                                            bottom: BorderSide(
                                                color: selectState == 0
                                                    ? AppColors.accentColor
                                                    : Colors.white,
                                                width: 4.w))),
                                    child: Text(
                                      "待发货",
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                          color: selectState == 0
                                              ? AppColors.accentColor
                                              : AppColors.normalTextColor),
                                    )),
                              ]))),
                  Expanded(
                      child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            setState(() {
                              selectState = 1;
                            });
                            reloadRequestData();
                          },
                          child: HaloContainer(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                    decoration: BoxDecoration(
                                        border: Border(
                                            bottom: BorderSide(
                                                color: selectState == 1
                                                    ? AppColors.accentColor
                                                    : Colors.white,
                                                width: 4.w))),
                                    child: Text(
                                      "已发货",
                                      style: TextStyle(
                                          color: selectState == 1
                                              ? AppColors.accentColor
                                              : AppColors.normalTextColor),
                                      textAlign: TextAlign.center,
                                    )),
                              ]))),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () => showFilterDialog(),
                    child: Padding(
                      padding: EdgeInsets.all(10.w),
                      child: IconFont(IconNames.gengduo, size: 25.w),
                    ),
                  ),
                ],
              ),
              ScanSelectWidget(
                  searchFocusNode: searchFocusNode,
                  hint: "扫描或搜索单据",
                  margin: EdgeInsets.only(top: 15.w),
                  onSubmitted: (text) {
                    filterData.textBillNumberController.text = text;
                    reloadRequestData(vchcode: text).then((value) {
                      setState(() {
                        dataSource = value;
                      });
                    });
                  }),
              Expanded(
                child: HaloList(
                  dataSource,
                  scrollController: scrollController,
                  buildItemContent: (buildContext, index) {
                    return GestureDetector(
                        onTap: () {
                          selectIndex = index;
                          getBillDetail(dataSource[index].warehouseTaskId);
                        },
                        child: HaloContainer(
                          padding: EdgeInsets.symmetric(
                              horizontal: 24.w, vertical: 24.h),
                          color: index == selectIndex
                              ? const Color(0xFFF4F7FF)
                              : AppColors.cardColor,
                          children: [
                            Expanded(
                                child: HaloLabel(
                              dataSource[index].taskNumber ?? "",
                              showFullText: false,
                            )),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 12.w),
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: _getSelfDeliver(
                                          dataSource[index].selfDeliveryMode),
                                      width: 1.w),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(2))),
                              child: Text(
                                dataSource[index].selfDeliveryModeEnumName ??
                                    "",
                                style: TextStyle(
                                    color: _getSelfDeliver(
                                        dataSource[index].selfDeliveryMode)),
                              ),
                            ),
                          ],
                        ));
                  },
                  hasMoreData: loadMore,
                  onLoadMore: () {
                    return onLoadData();
                  },
                ),
              )
            ],
          ),
        ),
        Container(
          padding: EdgeInsets.only(bottom: 18.w),
          child: HaloButton(
            backgroundColor: ColorUtil.stringColor("#4679FC"),
            width: 461.w,
            height: 70.w,
            onPressed: () {
              showChannelScanDialog(context);
            },
            text: "到店核销自提",
          ),
        )

        //
      ],
    );
  }

  Color _getSelfDeliver(String? selfDeliveryMode) {
    // NONE(0, ""),
    // EXPRESS_LOGISTICS(1, "快递物流"),
    // INTRA_CITY_DISTRIBUTION(2, "三方配送"),
    // CUSTOMER_COLLECT(3, "自提"),
    // DELIVERY_BY_SELF(4, "自己配送"),
    // CAR_SALE(5, "车销"),
    // DELIVERY_BY_MANUFACTURER(6, "厂家发货");
    if (selfDeliveryMode == "INTRA_CITY_DISTRIBUTION") {
      //三方配送
      return ColorUtil.stringColor("#FAAD14");
    }
    if (selfDeliveryMode == "CUSTOMER_COLLECT") {
      //自提
      return ColorUtil.stringColor("#53C31B");
    }
    if (selfDeliveryMode == "EXPRESS_LOGISTICS") {
      //快递物流
      return ColorUtil.stringColor("#A28FDF");
    }
    if (selfDeliveryMode == "DELIVERY_BY_SELF") {
      //自己配送
      return ColorUtil.stringColor("#FF4D4F");
    }
    return AppColors.accentColor;
  }

  void getBillDetail(String? warehouseTaskId) async {
    requestPageSize = {
      "pageIndex": 1,
      "pageSize": pageSize,
      "queryParams": {
        "warehouseTaskId": [warehouseTaskId],
      },
    };
    // getBillDetail(dataSource[index].warehouseTaskId).then((value) => {});
    return BillChannelModel.getChannelInfoDetail(context, requestPageSize)
        .then((value) => (setState(() {
              detailDto = value;
            })));
  }

  @override
  buildAppBar() {
    return PreferredSize(
        preferredSize: Size.fromHeight(80.h),
        child: AppBar(
          backgroundColor: AppColorHelper(context).getAppBarColor(),
          titleSpacing: 0.0,
          automaticallyImplyLeading: false,
          toolbarHeight: 80.h,
          title: Row(
            children: [
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => NavigateUtil.pop(context),
                child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 0.w),
                    width: 80.w,
                    height: 80.h,
                    alignment: Alignment.center,
                    child: IconFont(
                      IconNames.ngp_left_back,
                      size: 32.w,
                      color: ColorUtil.color2String(
                          AppColorHelper(context).getTitleBoldTextColor()),
                    )),
              ),
              Text(
                getActionBarTitle(),
                style: TextStyle(
                    color: AppColorHelper(context).getTitleBoldTextColor(),
                    fontSize: ScreenUtil().setSp(widget.titleTextSize)),
              ),
              // SizedBox(
              //   width: 100.w,
              // ),
              Expanded(child: Container()),
              GestureDetector(
                onTap: () {},
                child: HaloContainer(
                  children: [
                    IconFont(
                      IconNames.shuaxin,
                      size: 20.w,
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    GestureDetector(
                      child: HaloLabel(
                        "刷新订单",
                        textStyle: TextStyle(
                            color: AppColorHelper(context)
                                .getTitleBoldTextColor()),
                      ),
                      onTap: () {
                        initData();
                      },
                    )
                  ],
                ),
              )
            ],
          ),
          centerTitle: false,
        ));
  }

  @override
  doPrintData() {
    initData();
    super.doPrintData();
  }

  @override
  buildEndDrawer() {
    return buildDetailPop(context, detailPopDto, tradeCode);
  }

  Future<LoadState> onLoadData() async {
    return requestDetailData(vchcode: editingController.text).then((result) {
      if (null == result) {
        return LoadState.LoadFail;
      }
      if (pageIndex <= 1) {
        dataSource.clear();
      }
      setState(() {
        dataSource.addAll(result);
      });
      if (result.length < pageSize) {
        setState(() {
          loadMore = false;
        });
        return LoadState.AllFinish;
      }
      setState(() {
        loadMore = true;
      });
      return LoadState.LoadFinish;
    }).catchError((msg, stack) {
      return LoadState.LoadFinish;
    });
  }

  showFilterDialog() {
    Map<String, dynamic> old = filterData.toJson();
    oldFilterData = FilterData.fromJson(old);
    showDialog(
        context: context,
        builder: (context) =>
            BillChannelFilterDialog(filterData: filterData)).then((value) {
      if (value != null && value) {
        reloadRequestData();
      } else {
        //取消
        Map<String, dynamic> old = oldFilterData.toJson();
        filterData = FilterData.fromJson(old);
      }
    });
  }

  @override
  String getActionBarTitle() => "全渠道订单";

  @override
  Future<void>? onInitState() async {}

  void showChannelScanDialog(BuildContext context, {String? vchcode}) {
    DialogUtil.showAlertDialog(context,
        child: BillChannelScan(callback: (String text) {
      tradeCode = text;
      BillChannelModel.getChannelInfoForCode(context, text, vchcode: vchcode)
          .then((value) {
        detailPopDto = value;
        detailPopDto.pickupCode = text;
        isComplete = false;
        if (value.vchcode?.isNotEmpty == true) {
          setState(() {
            Scaffold.of(context).openEndDrawer();
          });
        }
      });
    }));
  }

// @override
// doItemSubmit(String? vchcode) {
//   showChannelScanDialog(leftContext, vchcode: vchcode);
// }
}

class QueryParams {
  ///查询标题
  String title = "";

  ///是否选中
  bool isSelect = true;

  ///数量
  int? number = 0;

  QueryParams(this.title, this.isSelect, this.number);
}
