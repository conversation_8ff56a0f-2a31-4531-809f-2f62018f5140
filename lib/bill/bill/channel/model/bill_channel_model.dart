import 'package:flutter/cupertino.dart';
import '../../../../bill/entity/goods_bill.dto.dart';
import '../../../../bill/entity/goods_detail_dto.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:haloui/haloui.dart';

import '../../../../common/net/http_util.dart';
import '../../../../common/net/request_method.dart';
import '../../../../common/tool/sp_tool.dart';
import '../bill_channel_orders_query.dart';
import 'bill_channel_count_entity.dart';
import 'bill_channel_detail.dart';
import 'bill_channel_detail_request.dart';
import 'bill_channel_filter_entity.dart';
import 'bill_channel_request.dart';
import 'bill_channel_task_detail.dart';

class BillChannelModel {
  ///获取各状态下的核销数量
  static Future<List<QueryParams>> getChannelCount(
      BuildContext context, requestParams,
      {bool waite = true, bool done = true}) async {
    requestParams["ktypeId"] = SpTool.getStoreInfo()!.ktypeId;
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VERIFICATE_QUERYVERIFICATESTATECOUNT,
        data: requestParams);
    if (response.code! < 0 && null == response.data) {
      return [];
    }

    BillChannelCountEntity billChannelCountEntity =
        BillChannelCountEntity.fromJson(response.data);
    List<QueryParams> queryParams = []
      ..add(QueryParams("待核销", waite, billChannelCountEntity.waiteCount))
      ..add(QueryParams("已核销", done, billChannelCountEntity.doneCount));

    return queryParams;
  }

  ///获取各状态下的核销详情列表
  static Future<BillChannelDetailDto> getChannelInfoDetail(
      BuildContext context, requestParams) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VERIFICATE_QUERYVERIFICATEBILLINFO,
        data: requestParams);
    if (response.code! < 0 && null == response.data) {
      return BillChannelDetailDto();
    }
    List<BillChannelDetailDto> list = [];
    response.data
        .forEach((item) => (list.add(BillChannelDetailDto.fromJson(item))));

    list.forEach((element) {
      List<BillChannelTaskDetail> detail = element.deliverDetail!;
      List<BillChannelTaskDetail> temp = [];

      for (var element in detail) {
        if (element.comboId != "0"|| element.comboRow) {
          element.comboDetailList = []..addAll(detail.where(
              (elementTwo) => elementTwo.comboRowParId == element.comboRowId));
          temp.add(element);
        } else if (element.comboRowParId != "0") {
        } else {
          temp.add(element);
        }
      }
      element.originalDetail = element.deliverDetail;
      element.deliverDetail = temp;
    });
    return list.first;
  }

  ///获取各状态下的核销详情列表
  static Future<List<BillChannelDetailDto>> getChannelInfoList(
      BuildContext context, BillChannelRequestPage requestParams) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.QUERY_VERIFICATE_HEADER_INFO,
        data: requestParams.toJson());
    if (response.code! < 0 && null == response.data) {
      return [];
    }
    if (null == response.data["list"]) {
      return [];
    }
    List? list = response.data["list"];
    return list?.map((o) => BillChannelDetailDto.fromJson(o)).toList() ?? [];
    // return list;
  }

  ///根据核销码获取核销详情
  static Future<BillChannelDetailDto> getChannelInfoForCode(
      BuildContext context, verificateNumber,
      {String? vchcode}) async {
    var requestParams = {"verificateNumber": verificateNumber};
    if (vchcode != null || vchcode?.isNotEmpty == true) {
      requestParams["vchcode"] = [vchcode];
    }
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VERIFICATE_QUERYBILLINFOBYNUMBER,
        data: requestParams);
    if (response.code! < 0 && null == response.data ||
        (response.data as List).length == 0) {
      HaloToast.showError(context, msg: "核销码不存在或核销码已核销");
      return BillChannelDetailDto();
    }
    List<BillChannelDetailDto> list = [];
    response.data
        .forEach((item) => (list.add(BillChannelDetailDto.fromJson(item))));
    return list.first;
  }

  ///核销
  static Future<bool> doVerificate(BuildContext context, String tradeCode,
      String warehouseTaskId, bool sendFlag) async {
    var requestParams = {
      "verificateNumber": tradeCode,
      "sendFlag": true,
      "warehouseTaskId": [warehouseTaskId]
    };
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VERIFICATE_DOVERIFICATE,
        data: requestParams);
    if (response.code == 200 && response.data["success"] == true) {
      return true;
    } else {
      HaloToast.showError(context, msg: response.data["errMsg"]);
      return false;
    }
  }

  ///选单核销
  static Future<bool> verifyWithOrder(BuildContext context, String tradeCode,
      String warehouseTaskId, bool sendFlag) async {
    var requestParams = {
      "verificateNumber": tradeCode,
      "sendFlag": true,
      "warehouseTaskId": [warehouseTaskId]
    };
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VERIFICATE_VERIFYWITHORDER,
        data: requestParams);
    if (response.code == 200 && response.data["success"] == true) {
      return true;
    } else {
      if(context.mounted){
        HaloToast.showError(context, msg: response.data["errMsg"]);
      }
      return false;
    }
  }

  ///更新全渠道订单商品批次和序列号
  static Future<bool> updateTaskDetails(BuildContext context,
      BillChannelDetailRequest request) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VERIFICATE_UPDATETASKDETAILS,
        data: request.toJson());
    if (response.code == 200 && response.data["success"] == true) {
      return true;
    } else {
      HaloToast.showError(context, msg: response.data["errMsg"]);
      return false;
    }
  }


  ///系统发货
  static Future<bool> doYunLiLocalSend(BuildContext context,
      String warehouseTaskId) async {
    var requestParams = {
      "warehouseTaskId": [warehouseTaskId]
    };
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_DOYUNLI_LOCALSEND,
        data: requestParams);
    if (response.code == 200 && response.data["success"] == true) {
      return true;
    } else {
      HaloToast.showError(context, msg: response.data["errMsg"]);
      return false;
    }
  }

  ///获取筛选条件
  static Future<BillChannelFilterEntity> getStatus(BuildContext context) async {
    var requestParams = {
      "pageName": "RetailPos"
    };
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_VERIFICATE_GETSTATUS,
        data: requestParams);
    if (response.code == 200 && response.data != null) {
      return BillChannelFilterEntity.fromJson(response.data);
    } else {
      HaloToast.showError(context, msg: response.data["errMsg"]);
      return BillChannelFilterEntity();
    }
  }
}
