

class BillChannelRequestPage{
  int? pageIndex;
  int? pageSize;
  BillChannelRequest queryParams = BillChannelRequest();

  BillChannelRequestPage();

  Map toJson() => {
    "pageIndex": pageIndex,
    "pageSize": pageSize,
    "queryParams": queryParams.toJson(),
  };
}



class BillChannelRequest {
  List<BigInt>? vchcode;
  List<BigInt>? warehouseTaskId;
  String? eshopId;
  String? beginTime;
  String? endTime;
  String? verificateNumber;
  String? billNumber;
  String? ktypeId;
  List<String>? stateEnums;
  bool sendFlag = true;
  String? stateEnum;

  //发货方式
  String? selfDeliveryModeEnum;
  //平台
  List<String>? eshopType;
  //网店名称-转对应id
  List<String>? orgId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['warehouseTaskId'] = warehouseTaskId;
    map['eshopId'] = eshopId;
    map['beginTime'] = beginTime;
    map['endTime'] = endTime;
    map['verificateNumber'] = verificateNumber;
    map['billNumber'] = billNumber;
    map['ktypeId'] = ktypeId;
    map['stateEnums'] = stateEnums;
    map['stateEnum'] = stateEnum;
    map['sendFlag'] = sendFlag;
    map['selfDeliveryModeEnum'] = selfDeliveryModeEnum;
    map['eshopType'] = eshopType;
    map['orgId'] = orgId;
    return map;
  }
}

enum VerificateStateEnum {
  WAITE,
  DONE,
}

const Map<VerificateStateEnum, String> VerificateStateEnumData = {
  VerificateStateEnum.WAITE: "WAITE",
  VerificateStateEnum.DONE: "DONE",
};


