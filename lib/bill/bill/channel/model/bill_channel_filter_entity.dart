
import 'dart:convert';

import '../../../../generated/json/base/json_field.dart';
import '../../../../generated/json/bill_channel_filter_entity.g.dart';

@JsonSerializable()
class BillChannelFilterEntity {
	List<BillChannelFilterEshopType>? eshopType = [];
	List<BillChannelFilterEshops>? eshops = [];
	List<BillChannelFilterSelfDeliveryMode>? selfDeliveryMode = [];

	BillChannelFilterEntity();

	factory BillChannelFilterEntity.fromJson(Map<String, dynamic> json) => $BillChannelFilterEntityFromJson(json);

	Map<String, dynamic> toJson() => $BillChannelFilterEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BillChannelFilterEshopType {
	int? code = 0;
	dynamic key;
	String? description = '';

	BillChannelFilterEshopType();

	factory BillChannelFilterEshopType.fromJson(Map<String, dynamic> json) => $BillChannelFilterEshopTypeFromJson(json);

	Map<String, dynamic> toJson() => $BillChannelFilterEshopTypeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BillChannelFilterEshops {
	String? id = '';
	dynamic profileId;
	String? fullname = '';
	int? eshopType = 0;
	String? platformName = '';
	bool? enable = false;
	int? ocategory = 0;
	int? storeType = 0;

	BillChannelFilterEshops();

	factory BillChannelFilterEshops.fromJson(Map<String, dynamic> json) => $BillChannelFilterEshopsFromJson(json);

	Map<String, dynamic> toJson() => $BillChannelFilterEshopsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BillChannelFilterSelfDeliveryMode {
	int? code = 0;
	String? key = '';
	String? description = '';

	BillChannelFilterSelfDeliveryMode();

	factory BillChannelFilterSelfDeliveryMode.fromJson(Map<String, dynamic> json) => $BillChannelFilterSelfDeliveryModeFromJson(json);

	Map<String, dynamic> toJson() => $BillChannelFilterSelfDeliveryModeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}