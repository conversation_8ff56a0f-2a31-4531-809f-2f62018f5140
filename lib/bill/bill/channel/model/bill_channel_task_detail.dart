import 'package:halo_utils/utils/date_util.dart';

import '../../../entity/goods_detail_dto.dart';
import '../../../entity/ptype/ptype_serial_no_dto.dart';

class BillChannelTaskDetail extends GoodsDetailDto {
   List<PtypeSerialNoDto>? serialNo;
   String? batchno;
   String? batchId;
   String? warehouseTaskDetailId;
   String? warehouseTaskId;
   String? billDate;
   dynamic checkQty;
   dynamic finishQty;

   BillChannelTaskDetailPtype? ptype;

   BillChannelTaskDetail();

   BillChannelTaskDetail.fromMap(Map<dynamic, dynamic> map):super.buildFromMap(map){
     batchno = map["batchno"]??"";
     batchNo = map["batchno"]??"";
     currencyDisedTaxedTotal = map["disedTaxedTotal"]??0.0;
     warehouseTaskDetailId = map["warehouseTaskDetailId"]??"0";
     warehouseTaskId = map["warehouseTaskId"]??"0";
     checkQty = map["checkQty"]??"0";
     finishQty = map["finishQty"]??"0";
     billDate = map["billDate"]??DateUtil.formatDate(DateTime.now());
     ptype = map['ptype'] == null
         ? null
         : BillChannelTaskDetailPtype.fromMap(map['ptype']);
     pFullName =ptype?.fullName??"";
     picUrl = ptype?.picUrl??"";
     batchenabled = ptype?.batchenabled??false;
     snenabled = ptype?.snEnabled??0;
     costMode = ptype?.costMode??0;
     comboRow = map['combo']??false;
     subQty = map['subQty']??0;
     currencyPrice = map['disedTaxedPrice']??0.0;
     serialNo = [
       ...((map['serialNo'] ?? []) as List).map((o) {
         if (o is PtypeSerialNoDto) {
           return o;
         }
         return PtypeSerialNoDto.fromMap(o);
       })
     ];
     serialNoList = [
       ...((map['serialNo'] ?? []) as List).map((o) {
         if (o is PtypeSerialNoDto) {
           return o;
         }
         return PtypeSerialNoDto.fromMap(o);
       })
     ];
     batchId = map["batchId"];
   }

   @override
  Map toJson() {
    // TODO: implement toJson
    Map map = super.toJson();
    List serialNoJson = [];
    for (PtypeSerialNoDto serialNo in serialNoList) {
      serialNoJson.add(serialNo.toJson());
    }
    map.addAll({
      "batchno": batchno,
      "serialNo": serialNoJson,
      "batchId": batchId,
      "warehouseTaskDetailId": warehouseTaskDetailId,
      "warehouseTaskId": warehouseTaskId,
      "checkQty":checkQty,
      "finishQty":finishQty,
      "billDate":billDate
    });
    return map;
  }
}

class BillChannelTaskDetailPtype{
  String fullName = "";
  String picUrl = "";
  int snEnabled = 0;
  bool batchenabled = false;
  int costMode = 0;
  BillChannelTaskDetailPtype.fromMap(Map<dynamic, dynamic> map){
    fullName = map["fullName"];
    picUrl = map["picUrl"];
    snEnabled = map["snEnabled"];
    batchenabled = map["batchenabled"];
    costMode = map["costMode"];
  }
}