import 'dart:convert';

import '../../../../generated/json/base/json_field.dart';
import '../../../../generated/json/bill_channel_count_entity.g.dart';

@JsonSerializable()
class BillChannelCountEntity {
  int? doneCount = 0;
  int? waiteCount = 0;

  BillChannelCountEntity();

  factory BillChannelCountEntity.fromJson(Map<String, dynamic> json) =>
      $BillChannelCountEntityFromJson(json);

  Map<String, dynamic> toJson() => $BillChannelCountEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
