import '../../../entity/goods_bill.dto.dart';
import 'bill_channel_task_detail.dart';

class BillChannelDetailDto {
  BillChannelDetailDto({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.ddBic,
    this.vchtype,
    this.businessType,
    this.confirmType,
    this.processType,
    this.deliverType,
    this.deliverProcessType,
    this.billNumber,
    this.taskNumber,
    this.taskCreateTime,
    this.taskUpdateTime,
    this.billDate,
    this.period,
    this.otypeId,
    this.onlineEshopId,
    this.btypeId,
    this.balanceBtypeId,
    this.ktypeId,
    this.ktypeId2,
    this.buyerId,
    this.etypeId,
    this.dtypeId,
    this.summary,
    this.memo,
    this.createType,
    this.invoiceType,
    this.currencyId,
    this.exchangeRate,
    this.postState,
    this.postStateShow,
    this.postTime,
    this.postEtypeId,
    this.createEtypeId,
    this.accountingType,
    this.deleted,
    this.supplierId,
    this.driverId,
    this.carId,
    this.billInoutState,
    this.failureInfo,
    this.submitTime,
    this.tradeId,
    this.tradeOrderId,
    this.mergeHash,
    this.orderId,
    this.hasSplit,
    this.hasMerge,
    this.originVchcode,
    this.sortField,
    this.ptypeKindCount,
    this.ptypeQty,
    this.unitQty,
    this.subQty,
    this.ptypeNogiftKindCount,
    this.ptypeNogiftQty,
    this.buyerMessage,
    this.sellerMemo,
    this.sellerFlag,
    this.tradeCreateTime,
    this.createTime,
    this.downloadTime,
    this.payTime,
    this.deliverTime,
    this.taskTime,
    this.freightSyncTime,
    this.assignEtypeId,
    this.assignEtypeName,
    this.checkEtypeId,
    this.eshopType,
    this.modifyTime,
    this.orderType,
    this.selfDeliveryMode,
    this.tradeType,
    this.sellerPreferentialTotal,
    this.platformPreferentialTotal,
    this.serviceFee,
    this.buyerFreightFee,
    this.sellerFreightFee,
    this.distributionFreightFee,
    this.disedTaxedTotal,
    this.taxTotal,
    this.distributionCommissionTotal,
    this.distributionBalanceTaxedTotal,
    this.buyerTradeTotal,
    this.mallFee,
    this.paymentFee,
    this.otherFee,
    this.refundAddressId,
    this.platformParentOrderId,
    this.extend,
    this.billEx,
    this.state,
    this.wmsBillDeliver,
    this.printBatch,
    this.billDeliverPrint,
     this.deliverDetail,
    this.detailDeliverCombo,
    this.freightInfo,
    this.detailsMd5,
    this.freightInfoList,
    this.freightInfoText,
    this.deliverPrintBatchDetail,
    this.buyer,
    this.sender,
    this.billDeliverMark,
    this.billDeliverMarkId,
    this.enabled,
    this.presetMarkShow,
    this.deliverMarkTextShow,
    this.deliverMarks,
    this.platformMarkShow,
    this.platformMarkTextShow,
    this.invoice,
    this.deliverTiming,
    this.detailInfo,
    this.allDetailInfo,
    this.eshopFullname,
    this.btypeFullName,
    this.packingDone,
    this.hasPacking,
    this.assignTime,
    this.ocategory,
    this.stock,
    this.accData,
    this.bigData,
    this.hasOtherBatchRelatedBill,
    this.relatedBillVchcodes,
    this.businessMarks,
    this.strategyMark,
    this.eshopTypeName,
    this.lockType,
    this.lockMemo,
    this.remainingDeliveryTime,
    this.lockReasonShow,
    this.returnReasonShow,
    this.ktypeName,
    this.eshopName,
    this.supplement,
    this.consumables,
    this.consumablesFullname,
    this.consumablesWeight,
    this.templateType,
    this.selfDeliveryModeEnumName,
    this.customerInfo,
    this.senderId,
    this.goodsBillDTO,
    this.tripartiteDeliveryState = 0,

    /**
     * 配送状态 0=待呼叫,1=已呼叫,2=呼叫成功（待取货）,3=配送中,4=已完成
     */
    this.yunliStatus = 0,
    //配送电话
    this.platformDispatcherMobile,
    //配送骑手名称
    this.platformDispatcherName,
    //提货码
    this.pickupCode,
  });

  BillChannelDetailDto.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    tripartiteDeliveryState = json['tripartiteDeliveryState'];
    yunliStatus = json['yunliStatus'];
    platformDispatcherMobile = json['platformDispatcherMobile'];
    platformDispatcherName = json['platformDispatcherName'];
    pickupCode = json['pickupCode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    ddBic = json['ddBic'];
    vchtype = json['vchtype'];
    businessType = json['businessType'];
    confirmType = json['confirmType'];
    processType = json['processType'];
    deliverType = json['deliverType'];
    deliverProcessType = json['deliverProcessType'];
    billNumber = json['billNumber'];
    taskNumber = json['taskNumber'];
    taskCreateTime = json['taskCreateTime'];
    taskUpdateTime = json['taskUpdateTime'];
    billDate = json['billDate'];
    period = json['period'];
    otypeId = json['otypeId'];
    onlineEshopId = json['onlineEshopId'];
    btypeId = json['btypeId'];
    balanceBtypeId = json['balanceBtypeId'];
    ktypeId = json['ktypeId'];
    ktypeId2 = json['ktypeId2'];
    buyerId = json['buyerId'];
    etypeId = json['etypeId'];
    dtypeId = json['dtypeId'];
    summary = json['summary'];
    memo = json['memo'];
    createType = json['createType'];
    invoiceType = json['invoiceType'];
    currencyId = json['currencyId'];
    exchangeRate = json['exchangeRate'];
    postState = json['postState'];
    postStateShow = json['postStateShow'];
    postTime = json['postTime'];
    postEtypeId = json['postEtypeId'];
    createEtypeId = json['createEtypeId'];
    accountingType = json['accountingType'];
    deleted = json['deleted'];
    supplierId = json['supplierId'];
    driverId = json['driverId'];
    carId = json['carId'];
    billInoutState = json['billInoutState'];
    failureInfo = json['failureInfo'];
    submitTime = json['submitTime'];
    tradeId = json['tradeId'];
    tradeOrderId = json['tradeOrderId'];
    mergeHash = json['mergeHash'];
    orderId = json['orderId'];
    hasSplit = json['hasSplit'];
    hasMerge = json['hasMerge'];
    originVchcode = json['originVchcode'];
    sortField = json['sortField'];
    ptypeKindCount = json['ptypeKindCount'];
    ptypeQty = json['ptypeQty'];
    unitQty = json['unitQty'];
    subQty = json['subQty'];
    ptypeNogiftKindCount = json['ptypeNogiftKindCount'];
    ptypeNogiftQty = json['ptypeNogiftQty'];
    buyerMessage = json['buyerMessage'];
    sellerMemo = json['sellerMemo'];
    sellerFlag = json['sellerFlag'];
    tradeCreateTime = json['tradeCreateTime'];
    createTime = json['createTime'];
    downloadTime = json['downloadTime'];
    payTime = json['payTime'];
    deliverTime = json['deliverTime'];
    taskTime = json['taskTime'];
    freightSyncTime = json['freightSyncTime'];
    assignEtypeId = json['assignEtypeId'];
    assignEtypeName = json['assignEtypeName'];
    checkEtypeId = json['checkEtypeId'];
    eshopType = json['eshopType'];
    modifyTime = json['modifyTime'];
    orderType = json['orderType'];
    selfDeliveryMode = json['selfDeliveryMode'];
    tradeType = json['tradeType'];
    sellerPreferentialTotal = json['sellerPreferentialTotal'];
    platformPreferentialTotal = json['platformPreferentialTotal'];
    serviceFee = json['serviceFee'];
    buyerFreightFee = json['buyerFreightFee'];
    sellerFreightFee = json['sellerFreightFee'];
    distributionFreightFee = json['distributionFreightFee'];
    disedTaxedTotal = json['disedTaxedTotal'];
    taxTotal = json['taxTotal'];
    distributionCommissionTotal = json['distributionCommissionTotal'];
    distributionBalanceTaxedTotal = json['distributionBalanceTaxedTotal'];
    buyerTradeTotal = json['buyerTradeTotal'];
    mallFee = json['mallFee'];
    paymentFee = json['paymentFee'];
    otherFee = json['otherFee'];
    refundAddressId = json['refundAddressId'];
    platformParentOrderId = json['platformParentOrderId'];
    extend = json['extend'] != null ? Extend.fromJson(json['extend']) : null;
    billEx = json['billEx'];
    state = json['state'] != null ? DetailState.fromJson(json['state']) : null;
    wmsBillDeliver = json['wmsBillDeliver'] != null
        ? WmsBillDeliver.fromJson(json['wmsBillDeliver'])
        : null;
    printBatch = json['printBatch'] != null
        ? PrintBatch.fromJson(json['printBatch'])
        : null;
    billDeliverPrint = json['billDeliverPrint'] != null
        ? BillDeliverPrint.fromJson(json['billDeliverPrint'])
        : null;
    if (json['deliverDetail'] != null) {
      deliverDetail = [];
      json['deliverDetail'].forEach((v) {
        deliverDetail?.add(BillChannelTaskDetail.fromMap(v));
      });
    }
    detailDeliverCombo = json['detailDeliverCombo'];
    freightInfo = json['freightInfo'] != null
        ? FreightInfo.fromJson(json['freightInfo'])
        : null;
    detailsMd5 = json['detailsMd5'];
    freightInfoList = json['freightInfoList'];
    freightInfoText = json['freightInfoText'];
    deliverPrintBatchDetail = json['deliverPrintBatchDetail'] != null
        ? DeliverPrintBatchDetail.fromJson(json['deliverPrintBatchDetail'])
        : null;
    buyer = json['buyer'] != null ? Buyer.fromJson(json['buyer']) : null;
    sender = json['sender'] != null ? Sender.fromJson(json['sender']) : null;
    billDeliverMark = json['billDeliverMark'];
    billDeliverMarkId = json['billDeliverMarkId'];
    enabled = json['enabled'];
    presetMarkShow = json['presetMarkShow'];
    deliverMarkTextShow = json['deliverMarkTextShow'];
    if (json['deliverMarks'] != null) {
      deliverMarks = [];
      json['deliverMarks'].forEach((v) {
        deliverMarks?.add(v);
      });
    }
    platformMarkShow = json['platformMarkShow'];
    platformMarkTextShow = json['platformMarkTextShow'];
    invoice =
    json['invoice'] != null ? Invoice.fromJson(json['invoice']) : null;
    deliverTiming = json['deliverTiming'] != null
        ? DeliverTiming.fromJson(json['deliverTiming'])
        : null;
    detailInfo = json['detailInfo'];
    allDetailInfo = json['allDetailInfo'];
    eshopFullname = json['eshopFullname'];
    btypeFullName = json['btypeFullName'];
    packingDone = json['packingDone'];
    hasPacking = json['hasPacking'];
    assignTime = json['assignTime'];
    ocategory = json['ocategory'];
    stock = json['stock'] != null ? Stock.fromJson(json['stock']) : null;
    accData = json['accData'];
    bigData = json['bigData'];
    hasOtherBatchRelatedBill = json['hasOtherBatchRelatedBill'];
    relatedBillVchcodes = json['relatedBillVchcodes'];
    if (json['businessMarks'] != null) {
      businessMarks = [];
      json['businessMarks'].forEach((v) {
        businessMarks?.add(v);
      });
    }
    strategyMark = json['strategyMark'];
    eshopTypeName = json['eshopTypeName'];
    lockType = json['lockType'];
    lockMemo = json['lockMemo'];
    remainingDeliveryTime = json['remainingDeliveryTime'];
    lockReasonShow = json['lockReasonShow'];
    returnReasonShow = json['returnReasonShow'];
    ktypeName = json['ktypeName'];
    eshopName = json['eshopName'];
    supplement = json['supplement'];
    consumables = json['consumables'];
    consumablesFullname = json['consumablesFullname'];
    consumablesWeight = json['consumablesWeight'];
    templateType = json['templateType'];
    selfDeliveryModeEnumName = json['selfDeliveryModeEnumName'];
    customerInfo = json['customerInfo'];
    senderId = json['senderId'];
    goodsBillDTO = json['goodsBillDTO'] != null
        ? GoodsBillDto.fromMap(json['goodsBillDTO'])
        : null;
    currencyDisedTaxedTotal = json['disedTaxedTotal'];
  }

  String? vchcode;
  String? profileId;
  String? warehouseTaskId;
  num? wholesaleBill;
  bool? ddBic;
  String? vchtype;
  String? businessType;
  String? confirmType;
  String? processType;
  String? deliverType;
  String? deliverProcessType;
  String? billNumber;
  String? taskNumber;
  String? taskCreateTime;
  String? taskUpdateTime;
  String? billDate;
  num? period;
  String? otypeId;
  String? onlineEshopId;
  String? btypeId;
  dynamic balanceBtypeId;
  String? ktypeId;
  String? ktypeId2;
  String? buyerId;
  String? etypeId;
  String? dtypeId;
  String? summary;
  String? memo;
  String? createType;
  dynamic invoiceType;
  String? currencyId;
  num? exchangeRate;
  String? postState;
  dynamic postStateShow;
  String? postTime;
  String? postEtypeId;
  String? createEtypeId;
  String? accountingType;
  String? deleted;
  String? supplierId;
  String? driverId;
  String? carId;
  String? billInoutState;
  String? failureInfo;
  String? submitTime;
  String? tradeId;
  String? tradeOrderId;
  String? mergeHash;
  String? orderId;
  bool? hasSplit;
  bool? hasMerge;
  String? originVchcode;
  String? sortField;
  num? ptypeKindCount;
  num? ptypeQty;
  num? unitQty;
  dynamic subQty;
  num? currencyDisedTaxedTotal;
  num? ptypeNogiftKindCount;
  num? ptypeNogiftQty;
  String? buyerMessage;
  String? sellerMemo;
  num? sellerFlag;
  String? tradeCreateTime;
  String? createTime;
  dynamic downloadTime;
  String? payTime;
  String? deliverTime;
  String? taskTime;
  dynamic freightSyncTime;
  String? assignEtypeId;
  dynamic assignEtypeName;
  String? checkEtypeId;
  String? eshopType;
  String? modifyTime;
  String? orderType;
  String? selfDeliveryMode;
  String? tradeType;
  num? sellerPreferentialTotal;
  num? platformPreferentialTotal;
  num? serviceFee;
  num? buyerFreightFee;
  num? sellerFreightFee;
  num? distributionFreightFee;
  num? disedTaxedTotal;
  num? taxTotal;
  num? distributionCommissionTotal;
  num? distributionBalanceTaxedTotal;
  num? buyerTradeTotal;
  num? mallFee;
  num? paymentFee;
  num? otherFee;
  String? refundAddressId;
  String? platformParentOrderId;
  Extend? extend;
  dynamic billEx;
  DetailState? state;
  WmsBillDeliver? wmsBillDeliver;
  PrintBatch? printBatch;
  BillDeliverPrint? billDeliverPrint;
  List<BillChannelTaskDetail>? deliverDetail = [];
  List<BillChannelTaskDetail>? originalDetail = [];

  dynamic detailDeliverCombo;
  FreightInfo? freightInfo;
  dynamic detailsMd5;
  dynamic freightInfoList;
  dynamic freightInfoText;
  DeliverPrintBatchDetail? deliverPrintBatchDetail;
  Buyer? buyer;
  Sender? sender;
  dynamic billDeliverMark;
  dynamic billDeliverMarkId;
  bool? enabled;
  dynamic presetMarkShow;
  dynamic deliverMarkTextShow;
  List<dynamic>? deliverMarks;
  dynamic platformMarkShow;
  dynamic platformMarkTextShow;
  Invoice? invoice;
  DeliverTiming? deliverTiming;
  dynamic detailInfo;
  dynamic allDetailInfo;
  String? eshopFullname;
  String? btypeFullName;
  bool? packingDone;
  bool? hasPacking;
  String? assignTime;
  String? ocategory;
  Stock? stock;
  bool? accData;
  dynamic bigData;
  dynamic hasOtherBatchRelatedBill;
  dynamic relatedBillVchcodes;
  List<dynamic>? businessMarks;
  dynamic strategyMark;
  dynamic eshopTypeName;
  num? lockType;
  String? lockMemo;
  dynamic remainingDeliveryTime;
  dynamic lockReasonShow;
  dynamic returnReasonShow;
  dynamic ktypeName;
  dynamic eshopName;
  bool? supplement;
  dynamic consumables;
  dynamic consumablesFullname;
  dynamic consumablesWeight;
  dynamic templateType;
  String? selfDeliveryModeEnumName;
  String? customerInfo;
  String? senderId;
  GoodsBillDto? goodsBillDTO;

  /**
   * 配送拣货回传状态0=未回传,1=回传失败,2=回传成功
   */
  int? tripartiteDeliveryState = 0;

  /**
   * 配送状态 0=待呼叫,1=已呼叫,2=呼叫成功（待取货）,3=配送中,4=已完成
   */
  int? yunliStatus = 0;

  //配送电话
  String? platformDispatcherMobile;

  //配送骑手名称
  String? platformDispatcherName;

  //提货码
  String? pickupCode;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['tripartiteDeliveryState'] = tripartiteDeliveryState;
    map['yunliStatus'] = yunliStatus;
    map['platformDispatcherMobile'] = platformDispatcherMobile;
    map['platformDispatcherName'] = platformDispatcherName;
    map['pickupCode'] = pickupCode;
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['ddBic'] = ddBic;
    map['vchtype'] = vchtype;
    map['businessType'] = businessType;
    map['confirmType'] = confirmType;
    map['processType'] = processType;
    map['deliverType'] = deliverType;
    map['deliverProcessType'] = deliverProcessType;
    map['billNumber'] = billNumber;
    map['taskNumber'] = taskNumber;
    map['taskCreateTime'] = taskCreateTime;
    map['taskUpdateTime'] = taskUpdateTime;
    map['billDate'] = billDate;
    map['period'] = period;
    map['otypeId'] = otypeId;
    map['onlineEshopId'] = onlineEshopId;
    map['btypeId'] = btypeId;
    map['balanceBtypeId'] = balanceBtypeId;
    map['ktypeId'] = ktypeId;
    map['ktypeId2'] = ktypeId2;
    map['buyerId'] = buyerId;
    map['etypeId'] = etypeId;
    map['dtypeId'] = dtypeId;
    map['summary'] = summary;
    map['memo'] = memo;
    map['createType'] = createType;
    map['invoiceType'] = invoiceType;
    map['currencyId'] = currencyId;
    map['exchangeRate'] = exchangeRate;
    map['postState'] = postState;
    map['postStateShow'] = postStateShow;
    map['postTime'] = postTime;
    map['postEtypeId'] = postEtypeId;
    map['createEtypeId'] = createEtypeId;
    map['accountingType'] = accountingType;
    map['deleted'] = deleted;
    map['supplierId'] = supplierId;
    map['driverId'] = driverId;
    map['carId'] = carId;
    map['billInoutState'] = billInoutState;
    map['failureInfo'] = failureInfo;
    map['submitTime'] = submitTime;
    map['tradeId'] = tradeId;
    map['tradeOrderId'] = tradeOrderId;
    map['mergeHash'] = mergeHash;
    map['orderId'] = orderId;
    map['hasSplit'] = hasSplit;
    map['hasMerge'] = hasMerge;
    map['originVchcode'] = originVchcode;
    map['sortField'] = sortField;
    map['ptypeKindCount'] = ptypeKindCount;
    map['ptypeQty'] = ptypeQty;
    map['unitQty'] = unitQty;
    map['subQty'] = subQty;
    map['ptypeNogiftKindCount'] = ptypeNogiftKindCount;
    map['ptypeNogiftQty'] = ptypeNogiftQty;
    map['buyerMessage'] = buyerMessage;
    map['sellerMemo'] = sellerMemo;
    map['sellerFlag'] = sellerFlag;
    map['tradeCreateTime'] = tradeCreateTime;
    map['createTime'] = createTime;
    map['downloadTime'] = downloadTime;
    map['payTime'] = payTime;
    map['deliverTime'] = deliverTime;
    map['taskTime'] = taskTime;
    map['freightSyncTime'] = freightSyncTime;
    map['assignEtypeId'] = assignEtypeId;
    map['assignEtypeName'] = assignEtypeName;
    map['checkEtypeId'] = checkEtypeId;
    map['eshopType'] = eshopType;
    map['modifyTime'] = modifyTime;
    map['orderType'] = orderType;
    map['selfDeliveryMode'] = selfDeliveryMode;
    map['tradeType'] = tradeType;
    map['sellerPreferentialTotal'] = sellerPreferentialTotal;
    map['platformPreferentialTotal'] = platformPreferentialTotal;
    map['serviceFee'] = serviceFee;
    map['buyerFreightFee'] = buyerFreightFee;
    map['sellerFreightFee'] = sellerFreightFee;
    map['distributionFreightFee'] = distributionFreightFee;
    map['disedTaxedTotal'] = disedTaxedTotal;
    map['taxTotal'] = taxTotal;
    map['distributionCommissionTotal'] = distributionCommissionTotal;
    map['distributionBalanceTaxedTotal'] = distributionBalanceTaxedTotal;
    map['buyerTradeTotal'] = buyerTradeTotal;
    map['mallFee'] = mallFee;
    map['paymentFee'] = paymentFee;
    map['otherFee'] = otherFee;
    map['refundAddressId'] = refundAddressId;
    map['platformParentOrderId'] = platformParentOrderId;
    map['disedTaxedTotal'] = currencyDisedTaxedTotal;

    if (extend != null) {
      map['extend'] = extend?.toJson();
    }
    map['billEx'] = billEx;
    if (state != null) {
      map['state'] = state?.toJson();
    }
    if (wmsBillDeliver != null) {
      map['wmsBillDeliver'] = wmsBillDeliver?.toJson();
    }
    if (printBatch != null) {
      map['printBatch'] = printBatch?.toJson();
    }
    if (billDeliverPrint != null) {
      map['billDeliverPrint'] = billDeliverPrint?.toJson();
    }
    if(deliverDetail != null){
      map['deliverDetail'] = deliverDetail?.map((v) => v.toJson()).toList();
    }
    map['detailDeliverCombo'] = detailDeliverCombo;
    if (freightInfo != null) {
      map['freightInfo'] = freightInfo?.toJson();
    }
    map['detailsMd5'] = detailsMd5;
    map['freightInfoList'] = freightInfoList;
    map['freightInfoText'] = freightInfoText;
    if (deliverPrintBatchDetail != null) {
      map['deliverPrintBatchDetail'] = deliverPrintBatchDetail?.toJson();
    }
    if (buyer != null) {
      map['buyer'] = buyer?.toJson();
    }
    if (sender != null) {
      map['sender'] = sender?.toJson();
    }
    map['billDeliverMark'] = billDeliverMark;
    map['billDeliverMarkId'] = billDeliverMarkId;
    map['enabled'] = enabled;
    map['presetMarkShow'] = presetMarkShow;
    map['deliverMarkTextShow'] = deliverMarkTextShow;
    if (deliverMarks != null) {
      map['deliverMarks'] = deliverMarks?.map((v) => v.toJson()).toList();
    }
    map['platformMarkShow'] = platformMarkShow;
    map['platformMarkTextShow'] = platformMarkTextShow;
    if (invoice != null) {
      map['invoice'] = invoice?.toJson();
    }
    if (deliverTiming != null) {
      map['deliverTiming'] = deliverTiming?.toJson();
    }
    map['detailInfo'] = detailInfo;
    map['allDetailInfo'] = allDetailInfo;
    map['eshopFullname'] = eshopFullname;
    map['btypeFullName'] = btypeFullName;
    map['packingDone'] = packingDone;
    map['hasPacking'] = hasPacking;
    map['assignTime'] = assignTime;
    map['ocategory'] = ocategory;
    if (stock != null) {
      map['stock'] = stock?.toJson();
    }
    map['accData'] = accData;
    map['bigData'] = bigData;
    map['hasOtherBatchRelatedBill'] = hasOtherBatchRelatedBill;
    map['relatedBillVchcodes'] = relatedBillVchcodes;
    if (businessMarks != null) {
      map['businessMarks'] = businessMarks?.map((v) => v.toJson()).toList();
    }
    map['strategyMark'] = strategyMark;
    map['eshopTypeName'] = eshopTypeName;
    map['lockType'] = lockType;
    map['lockMemo'] = lockMemo;
    map['remainingDeliveryTime'] = remainingDeliveryTime;
    map['lockReasonShow'] = lockReasonShow;
    map['returnReasonShow'] = returnReasonShow;
    map['ktypeName'] = ktypeName;
    map['eshopName'] = eshopName;
    map['supplement'] = supplement;
    map['consumables'] = consumables;
    map['consumablesFullname'] = consumablesFullname;
    map['consumablesWeight'] = consumablesWeight;
    map['templateType'] = templateType;
    map['selfDeliveryModeEnumName'] = selfDeliveryModeEnumName;
    map['customerInfo'] = customerInfo;
    map['senderId'] = senderId;
    if (goodsBillDTO != null) {
      map['goodsBillDTO'] = goodsBillDTO?.toJson();
    }
    return map;
  }
}

class BatchNoAndSericalNoDaoList {
  BatchNoAndSericalNoDaoList({
    this.detailId,
    this.snno,
    this.batchno,
    this.produceDate,
    this.expireDate,
    this.batchPrice,
    this.qty,
    this.protectDays,
  });

  BatchNoAndSericalNoDaoList.fromJson(dynamic json) {
    detailId = json['detailId'];
    snno = json['snno'];
    batchno = json['batchno'];
    produceDate = json['produceDate'];
    expireDate = json['expireDate'];
    batchPrice = json['batchPrice'];
    qty = json['qty'];
    protectDays = json['protectDays'];
  }

  String? detailId;
  dynamic snno;
  String? batchno;
  dynamic produceDate;
  dynamic expireDate;
  num? batchPrice;
  num? qty;
  dynamic protectDays;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['detailId'] = detailId;
    map['snno'] = snno;
    map['batchno'] = batchno;
    map['produceDate'] = produceDate;
    map['expireDate'] = expireDate;
    map['batchPrice'] = batchPrice;
    map['qty'] = qty;
    map['protectDays'] = protectDays;
    return map;
  }
}

class Unit {
  Unit({
    this.id,
    this.profileId,
    this.ptypeId,
    this.unitCode,
    this.unitName,
    this.unitRate,
    this.barcode,
    this.retailPrice,
    this.preprice1,
    this.preprice2,
    this.preprice3,
    this.preprice4,
    this.preprice5,
    this.preprice6,
    this.preprice7,
    this.preprice8,
    this.preprice9,
    this.preprice10,
    this.buyPrice,
    this.minSalePrice,
    this.createTime,
    this.updateTime,
    this.lastSalePrice,
    this.lastBuyPrice,
    this.lastSaleTime,
    this.lastBuyTime,
  });

  Unit.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    ptypeId = json['ptypeId'];
    unitCode = json['unitCode'];
    unitName = json['unitName'];
    unitRate = json['unitRate'];
    barcode = json['barcode'];
    retailPrice = json['retailPrice'];
    preprice1 = json['preprice1'];
    preprice2 = json['preprice2'];
    preprice3 = json['preprice3'];
    preprice4 = json['preprice4'];
    preprice5 = json['preprice5'];
    preprice6 = json['preprice6'];
    preprice7 = json['preprice7'];
    preprice8 = json['preprice8'];
    preprice9 = json['preprice9'];
    preprice10 = json['preprice10'];
    buyPrice = json['buyPrice'];
    minSalePrice = json['minSalePrice'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    lastSalePrice = json['lastSalePrice'];
    lastBuyPrice = json['lastBuyPrice'];
    lastSaleTime = json['lastSaleTime'];
    lastBuyTime = json['lastBuyTime'];
  }

  String? id;
  String? profileId;
  String? ptypeId;
  num? unitCode;
  String? unitName;
  num? unitRate;
  String? barcode;
  dynamic retailPrice;
  dynamic preprice1;
  dynamic preprice2;
  dynamic preprice3;
  dynamic preprice4;
  dynamic preprice5;
  dynamic preprice6;
  dynamic preprice7;
  dynamic preprice8;
  dynamic preprice9;
  dynamic preprice10;
  dynamic buyPrice;
  dynamic minSalePrice;
  String? createTime;
  String? updateTime;
  dynamic lastSalePrice;
  dynamic lastBuyPrice;
  dynamic lastSaleTime;
  dynamic lastBuyTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['ptypeId'] = ptypeId;
    map['unitCode'] = unitCode;
    map['unitName'] = unitName;
    map['unitRate'] = unitRate;
    map['barcode'] = barcode;
    map['retailPrice'] = retailPrice;
    map['preprice1'] = preprice1;
    map['preprice2'] = preprice2;
    map['preprice3'] = preprice3;
    map['preprice4'] = preprice4;
    map['preprice5'] = preprice5;
    map['preprice6'] = preprice6;
    map['preprice7'] = preprice7;
    map['preprice8'] = preprice8;
    map['preprice9'] = preprice9;
    map['preprice10'] = preprice10;
    map['buyPrice'] = buyPrice;
    map['minSalePrice'] = minSalePrice;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['lastSalePrice'] = lastSalePrice;
    map['lastBuyPrice'] = lastBuyPrice;
    map['lastSaleTime'] = lastSaleTime;
    map['lastBuyTime'] = lastBuyTime;
    return map;
  }
}

class SenderInfo {
  SenderInfo({
    this.senderId,
    this.sender,
    this.senderPhone,
    this.senderMobile,
    this.senderZipCode,
    this.senderProvince,
    this.senderCity,
    this.senderDistrict,
    this.senderAddress,
    this.senderStreet,
  });

  SenderInfo.fromJson(dynamic json) {
    senderId = json['senderId'];
    sender = json['sender'];
    senderPhone = json['senderPhone'];
    senderMobile = json['senderMobile'];
    senderZipCode = json['senderZipCode'];
    senderProvince = json['senderProvince'];
    senderCity = json['senderCity'];
    senderDistrict = json['senderDistrict'];
    senderAddress = json['senderAddress'];
    senderStreet = json['senderStreet'];
  }

  String? senderId;
  String? sender;
  String? senderPhone;
  String? senderMobile;
  dynamic senderZipCode;
  String? senderProvince;
  String? senderCity;
  String? senderDistrict;
  String? senderAddress;
  String? senderStreet;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['senderId'] = senderId;
    map['sender'] = sender;
    map['senderPhone'] = senderPhone;
    map['senderMobile'] = senderMobile;
    map['senderZipCode'] = senderZipCode;
    map['senderProvince'] = senderProvince;
    map['senderCity'] = senderCity;
    map['senderDistrict'] = senderDistrict;
    map['senderAddress'] = senderAddress;
    map['senderStreet'] = senderStreet;
    return map;
  }
}

class BillBuyer {
  BillBuyer({
    this.buyerId,
    this.receiver,
    this.receiverPhone,
    this.receiverMobile,
    this.receiverZipCode,
    this.receiverProvince,
    this.receiverCity,
    this.receiverDistrict,
    this.receiverAddress,
    this.receiverStreet,
  });

  BillBuyer.fromJson(dynamic json) {
    buyerId = json['buyerId'];
    receiver = json['receiver'];
    receiverPhone = json['receiverPhone'];
    receiverMobile = json['receiverMobile'];
    receiverZipCode = json['receiverZipCode'];
    receiverProvince = json['receiverProvince'];
    receiverCity = json['receiverCity'];
    receiverDistrict = json['receiverDistrict'];
    receiverAddress = json['receiverAddress'];
    receiverStreet = json['receiverStreet'];
  }

  String? buyerId;
  String? receiver;
  String? receiverPhone;
  String? receiverMobile;
  dynamic receiverZipCode;
  String? receiverProvince;
  String? receiverCity;
  String? receiverDistrict;
  String? receiverAddress;
  String? receiverStreet;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['buyerId'] = buyerId;
    map['receiver'] = receiver;
    map['receiverPhone'] = receiverPhone;
    map['receiverMobile'] = receiverMobile;
    map['receiverZipCode'] = receiverZipCode;
    map['receiverProvince'] = receiverProvince;
    map['receiverCity'] = receiverCity;
    map['receiverDistrict'] = receiverDistrict;
    map['receiverAddress'] = receiverAddress;
    map['receiverStreet'] = receiverStreet;
    return map;
  }
}

class BillAuditInfo {
  BillAuditInfo({
    this.auditWaitForMe,
    this.auditCompletedForMe,
    this.flowId,
    this.bill,
  });

  BillAuditInfo.fromJson(dynamic json) {
    auditWaitForMe = json['auditWaitForMe'];
    auditCompletedForMe = json['auditCompletedForMe'];
    flowId = json['flowId'];
    bill = json['bill'] != null ? Bill.fromJson(json['bill']) : null;
  }

  bool? auditWaitForMe;
  bool? auditCompletedForMe;
  String? flowId;
  Bill? bill;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['auditWaitForMe'] = auditWaitForMe;
    map['auditCompletedForMe'] = auditCompletedForMe;
    map['flowId'] = flowId;
    if (bill != null) {
      map['bill'] = bill?.toJson();
    }
    return map;
  }
}

class Bill {
  Bill({
    this.auditEnable,
    this.submitAuditEnabled,
    this.auditPass,
    this.checkAccounted,
    this.exsitChildBills,
    this.exsitParentBills,
    this.checkChildsAccounted,
    this.overState,
    this.order,
  });

  Bill.fromJson(dynamic json) {
    auditEnable = json['auditEnable'];
    submitAuditEnabled = json['submitAuditEnabled'];
    auditPass = json['auditPass'];
    checkAccounted = json['checkAccounted'];
    exsitChildBills = json['exsitChildBills'];
    exsitParentBills = json['exsitParentBills'];
    checkChildsAccounted = json['checkChildsAccounted'];
    overState = json['overState'];
    order = json['order'];
  }

  bool? auditEnable;
  bool? submitAuditEnabled;
  num? auditPass;
  bool? checkAccounted;
  dynamic exsitChildBills;
  dynamic exsitParentBills;
  dynamic checkChildsAccounted;
  String? overState;
  bool? order;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['auditEnable'] = auditEnable;
    map['submitAuditEnabled'] = submitAuditEnabled;
    map['auditPass'] = auditPass;
    map['checkAccounted'] = checkAccounted;
    map['exsitChildBills'] = exsitChildBills;
    map['exsitParentBills'] = exsitParentBills;
    map['checkChildsAccounted'] = checkChildsAccounted;
    map['overState'] = overState;
    map['order'] = order;
    return map;
  }
}

class Invoice {
  Invoice({
    this.id,
    this.vchcode,
    this.invoiceType,
    this.profileId,
    this.invoiceTitle,
    this.invoiceTax,
    this.invoiceUserType,
    this.needInvoice,
    this.isShow,
    this.invoiceState,
    this.address,
    this.bank,
    this.bankAccount,
    this.memo,
    this.phone,
    this.invoiceMemo,
  });

  Invoice.fromJson(dynamic json) {
    id = json['id'];
    vchcode = json['vchcode'];
    invoiceType = json['invoiceType'];
    profileId = json['profileId'];
    invoiceTitle = json['invoiceTitle'];
    invoiceTax = json['invoiceTax'];
    invoiceUserType = json['invoiceUserType'];
    needInvoice = json['needInvoice'];
    isShow = json['isShow'];
    invoiceState = json['invoiceState'];
    address = json['address'];
    bank = json['bank'];
    bankAccount = json['bankAccount'];
    memo = json['memo'];
    phone = json['phone'];
    invoiceMemo = json['invoiceMemo'];
  }

  String? id;
  String? vchcode;
  String? invoiceType;
  String? profileId;
  String? invoiceTitle;
  String? invoiceTax;
  String? invoiceUserType;
  bool? needInvoice;
  num? isShow;
  String? invoiceState;
  String? address;
  String? bank;
  String? bankAccount;
  String? memo;
  String? phone;
  String? invoiceMemo;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['vchcode'] = vchcode;
    map['invoiceType'] = invoiceType;
    map['profileId'] = profileId;
    map['invoiceTitle'] = invoiceTitle;
    map['invoiceTax'] = invoiceTax;
    map['invoiceUserType'] = invoiceUserType;
    map['needInvoice'] = needInvoice;
    map['isShow'] = isShow;
    map['invoiceState'] = invoiceState;
    map['address'] = address;
    map['bank'] = bank;
    map['bankAccount'] = bankAccount;
    map['memo'] = memo;
    map['phone'] = phone;
    map['invoiceMemo'] = invoiceMemo;
    return map;
  }
}

class Stock {
  Stock({
    this.id,
    this.profileid,
    this.typeid,
    this.partypeid,
    this.usercode,
    this.fullname,
    this.shortname,
    this.namepy,
    this.stockType,
    this.classed,
    this.scategory,
    this.stoped,
    this.deleted,
    this.rowindex,
    this.memo,
    this.province,
    this.city,
    this.section,
    this.address,
    this.provinceid,
    this.cityid,
    this.sectionid,
    this.zip,
    this.contactor,
    this.phone,
    this.sysrow,
    this.customsType,
    this.bondedType,
    this.createTime,
    this.updateTime,
  });

  Stock.fromJson(dynamic json) {
    id = json['id'];
    profileid = json['profileid'];
    typeid = json['typeid'];
    partypeid = json['partypeid'];
    usercode = json['usercode'];
    fullname = json['fullname'];
    shortname = json['shortname'];
    namepy = json['namepy'];
    stockType = json['stockType'];
    classed = json['classed'];
    scategory = json['scategory'];
    stoped = json['stoped'];
    deleted = json['deleted'];
    rowindex = json['rowindex'];
    memo = json['memo'];
    province = json['province'];
    city = json['city'];
    section = json['section'];
    address = json['address'];
    provinceid = json['provinceid'];
    cityid = json['cityid'];
    sectionid = json['sectionid'];
    zip = json['zip'];
    contactor = json['contactor'];
    phone = json['phone'];
    sysrow = json['sysrow'];
    customsType = json['customsType'];
    bondedType = json['bondedType'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
  }

  String? id;
  dynamic profileid;
  dynamic typeid;
  dynamic partypeid;
  dynamic usercode;
  dynamic fullname;
  dynamic shortname;
  dynamic namepy;
  String? stockType;
  dynamic classed;
  num? scategory;
  dynamic stoped;
  dynamic deleted;
  dynamic rowindex;
  dynamic memo;
  dynamic province;
  dynamic city;
  dynamic section;
  dynamic address;
  dynamic provinceid;
  dynamic cityid;
  dynamic sectionid;
  dynamic zip;
  dynamic contactor;
  dynamic phone;
  dynamic sysrow;
  dynamic customsType;
  dynamic bondedType;
  dynamic createTime;
  dynamic updateTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileid'] = profileid;
    map['typeid'] = typeid;
    map['partypeid'] = partypeid;
    map['usercode'] = usercode;
    map['fullname'] = fullname;
    map['shortname'] = shortname;
    map['namepy'] = namepy;
    map['stockType'] = stockType;
    map['classed'] = classed;
    map['scategory'] = scategory;
    map['stoped'] = stoped;
    map['deleted'] = deleted;
    map['rowindex'] = rowindex;
    map['memo'] = memo;
    map['province'] = province;
    map['city'] = city;
    map['section'] = section;
    map['address'] = address;
    map['provinceid'] = provinceid;
    map['cityid'] = cityid;
    map['sectionid'] = sectionid;
    map['zip'] = zip;
    map['contactor'] = contactor;
    map['phone'] = phone;
    map['sysrow'] = sysrow;
    map['customsType'] = customsType;
    map['bondedType'] = bondedType;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    return map;
  }
}

class DeliverTiming {
  DeliverTiming({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.id,
    this.cnService,
    this.planSendTime,
    this.planSendTimeShow,
    this.sendTime,
    this.systemTiming,
    this.timingType,
    this.createTime,
    this.updateTime,
  });

  DeliverTiming.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    id = json['id'];
    cnService = json['cnService'];
    planSendTime = json['planSendTime'];
    planSendTimeShow = json['planSendTimeShow'];
    sendTime = json['sendTime'];
    systemTiming = json['systemTiming'];
    timingType = json['timingType'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
  }

  String? vchcode;
  dynamic profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  dynamic id;
  num? cnService;
  dynamic planSendTime;
  String? planSendTimeShow;
  dynamic sendTime;
  num? systemTiming;
  String? timingType;
  dynamic createTime;
  dynamic updateTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['id'] = id;
    map['cnService'] = cnService;
    map['planSendTime'] = planSendTime;
    map['planSendTimeShow'] = planSendTimeShow;
    map['sendTime'] = sendTime;
    map['systemTiming'] = systemTiming;
    map['timingType'] = timingType;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    return map;
  }
}

class BillInvoice {
  BillInvoice({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.id,
    this.invoiceType,
    this.invoiceTitle,
    this.invoiceTax,
    this.invoiceUserType,
    this.needInvoice,
    this.invoiceState,
    this.memo,
    this.bank,
    this.bankAccount,
    this.phone,
    this.address,
    this.submit,
  });

  BillInvoice.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    id = json['id'];
    invoiceType = json['invoiceType'];
    invoiceTitle = json['invoiceTitle'];
    invoiceTax = json['invoiceTax'];
    invoiceUserType = json['invoiceUserType'];
    needInvoice = json['needInvoice'];
    invoiceState = json['invoiceState'];
    memo = json['memo'];
    bank = json['bank'];
    bankAccount = json['bankAccount'];
    phone = json['phone'];
    address = json['address'];
    submit = json['submit'];
  }

  String? vchcode;
  dynamic profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  String? id;
  num? invoiceType;
  String? invoiceTitle;
  String? invoiceTax;
  num? invoiceUserType;
  bool? needInvoice;
  num? invoiceState;
  String? memo;
  String? bank;
  String? bankAccount;
  String? phone;
  String? address;
  String? submit;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['id'] = id;
    map['invoiceType'] = invoiceType;
    map['invoiceTitle'] = invoiceTitle;
    map['invoiceTax'] = invoiceTax;
    map['invoiceUserType'] = invoiceUserType;
    map['needInvoice'] = needInvoice;
    map['invoiceState'] = invoiceState;
    map['memo'] = memo;
    map['bank'] = bank;
    map['bankAccount'] = bankAccount;
    map['phone'] = phone;
    map['address'] = address;
    map['submit'] = submit;
    return map;
  }
}

class Sender {
  Sender({
    this.id,
    this.profileId,
    this.senderName,
    this.senderMobile,
    this.senderPhone,
    this.senderCountry,
    this.senderProvince,
    this.senderCity,
    this.senderDistrict,
    this.senderTown,
    this.senderAddress,
    this.senderFullAddress,
    this.senderZipCode,
    this.hashKey,
    this.updateTime,
    this.pi,
    this.mi,
    this.createTime,
  });

  Sender.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    senderName = json['senderName'];
    senderMobile = json['senderMobile'];
    senderPhone = json['senderPhone'];
    senderCountry = json['senderCountry'];
    senderProvince = json['senderProvince'];
    senderCity = json['senderCity'];
    senderDistrict = json['senderDistrict'];
    senderTown = json['senderTown'];
    senderAddress = json['senderAddress'];
    senderFullAddress = json['senderFullAddress'];
    senderZipCode = json['senderZipCode'];
    hashKey = json['hashKey'];
    updateTime = json['updateTime'];
    pi = json['pi'];
    mi = json['mi'];
    createTime = json['createTime'];
  }

  dynamic id;
  dynamic profileId;
  String? senderName;
  String? senderMobile;
  String? senderPhone;
  String? senderCountry;
  String? senderProvince;
  String? senderCity;
  String? senderDistrict;
  String? senderTown;
  String? senderAddress;
  String? senderFullAddress;
  String? senderZipCode;
  String? hashKey;
  dynamic updateTime;
  dynamic pi;
  dynamic mi;
  dynamic createTime;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['senderName'] = senderName;
    map['senderMobile'] = senderMobile;
    map['senderPhone'] = senderPhone;
    map['senderCountry'] = senderCountry;
    map['senderProvince'] = senderProvince;
    map['senderCity'] = senderCity;
    map['senderDistrict'] = senderDistrict;
    map['senderTown'] = senderTown;
    map['senderAddress'] = senderAddress;
    map['senderFullAddress'] = senderFullAddress;
    map['senderZipCode'] = senderZipCode;
    map['hashKey'] = hashKey;
    map['updateTime'] = updateTime;
    map['pi'] = pi;
    map['mi'] = mi;
    map['createTime'] = createTime;
    return map;
  }
}

class Buyer {
  Buyer({
    this.buyerId,
    this.profileId,
    this.customerIdCard,
    this.customerIdCardName,
    this.customerReceiver,
    this.customerReceiverPhone,
    this.customerReceiverMobile,
    this.customerReceiverProvince,
    this.customerReceiverCity,
    this.customerReceiverDistrict,
    this.customerReceiverTown,
    this.customerReceiverAddress,
    this.customerReceiverCountry,
    this.customerEmail,
    this.customerReceiverFullAddress,
    this.customerReceiverZipCode,
    this.customerPayAccount,
    this.customerShopAccount,
    this.hashKey,
    this.eshopType,
    this.otypeId,
    this.di,
    this.ai,
    this.mi,
    this.pi,
    this.ri,
    this.pai,
    this.ici,
    this.ini,
    this.addri,
    this.orgId,
    this.tradeId,
    this.encryptInfoId,
  });

  Buyer.fromJson(dynamic json) {
    buyerId = json['buyerId'];
    profileId = json['profileId'];
    customerIdCard = json['customerIdCard'];
    customerIdCardName = json['customerIdCardName'];
    customerReceiver = json['customerReceiver'];
    customerReceiverPhone = json['customerReceiverPhone'];
    customerReceiverMobile = json['customerReceiverMobile'];
    customerReceiverProvince = json['customerReceiverProvince'];
    customerReceiverCity = json['customerReceiverCity'];
    customerReceiverDistrict = json['customerReceiverDistrict'];
    customerReceiverTown = json['customerReceiverTown'];
    customerReceiverAddress = json['customerReceiverAddress'];
    customerReceiverCountry = json['customerReceiverCountry'];
    customerEmail = json['customerEmail'];
    customerReceiverFullAddress = json['customerReceiverFullAddress'];
    customerReceiverZipCode = json['customerReceiverZipCode'];
    customerPayAccount = json['customerPayAccount'];
    customerShopAccount = json['customerShopAccount'];
    hashKey = json['hashKey'];
    eshopType = json['eshopType'];
    otypeId = json['otypeId'];
    di = json['di'];
    ai = json['ai'];
    mi = json['mi'];
    pi = json['pi'];
    ri = json['ri'];
    pai = json['pai'];
    ici = json['ici'];
    ini = json['ini'];
    addri = json['addri'];
    orgId = json['orgId'];
    tradeId = json['tradeId'];
    encryptInfoId = json['encryptInfoId'];
  }

  String? buyerId;
  dynamic profileId;
  String? customerIdCard;
  String? customerIdCardName;
  String? customerReceiver;
  String? customerReceiverPhone;
  String? customerReceiverMobile;
  String? customerReceiverProvince;
  String? customerReceiverCity;
  String? customerReceiverDistrict;
  String? customerReceiverTown;
  String? customerReceiverAddress;
  String? customerReceiverCountry;
  String? customerEmail;
  String? customerReceiverFullAddress;
  String? customerReceiverZipCode;
  dynamic customerPayAccount;
  String? customerShopAccount;
  dynamic hashKey;
  String? eshopType;
  dynamic otypeId;
  String? di;
  String? ai;
  String? mi;
  String? pi;
  String? ri;
  String? pai;
  String? ici;
  dynamic ini;
  String? addri;
  dynamic orgId;
  dynamic tradeId;
  String? encryptInfoId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['buyerId'] = buyerId;
    map['profileId'] = profileId;
    map['customerIdCard'] = customerIdCard;
    map['customerIdCardName'] = customerIdCardName;
    map['customerReceiver'] = customerReceiver;
    map['customerReceiverPhone'] = customerReceiverPhone;
    map['customerReceiverMobile'] = customerReceiverMobile;
    map['customerReceiverProvince'] = customerReceiverProvince;
    map['customerReceiverCity'] = customerReceiverCity;
    map['customerReceiverDistrict'] = customerReceiverDistrict;
    map['customerReceiverTown'] = customerReceiverTown;
    map['customerReceiverAddress'] = customerReceiverAddress;
    map['customerReceiverCountry'] = customerReceiverCountry;
    map['customerEmail'] = customerEmail;
    map['customerReceiverFullAddress'] = customerReceiverFullAddress;
    map['customerReceiverZipCode'] = customerReceiverZipCode;
    map['customerPayAccount'] = customerPayAccount;
    map['customerShopAccount'] = customerShopAccount;
    map['hashKey'] = hashKey;
    map['eshopType'] = eshopType;
    map['otypeId'] = otypeId;
    map['di'] = di;
    map['ai'] = ai;
    map['mi'] = mi;
    map['pi'] = pi;
    map['ri'] = ri;
    map['pai'] = pai;
    map['ici'] = ici;
    map['ini'] = ini;
    map['addri'] = addri;
    map['orgId'] = orgId;
    map['tradeId'] = tradeId;
    map['encryptInfoId'] = encryptInfoId;
    return map;
  }
}

class DeliverPrintBatchDetail {
  DeliverPrintBatchDetail({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.printBatchId,
    this.printSequenceIndex,
    this.printSequenceCode,
    this.deleted,
    this.id,
  });

  DeliverPrintBatchDetail.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    printBatchId = json['printBatchId'];
    printSequenceIndex = json['printSequenceIndex'];
    printSequenceCode = json['printSequenceCode'];
    deleted = json['deleted'];
    id = json['id'];
  }

  String? vchcode;
  dynamic profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  dynamic printBatchId;
  num? printSequenceIndex;
  String? printSequenceCode;
  dynamic deleted;
  dynamic id;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['printBatchId'] = printBatchId;
    map['printSequenceIndex'] = printSequenceIndex;
    map['printSequenceCode'] = printSequenceCode;
    map['deleted'] = deleted;
    map['id'] = id;
    return map;
  }
}

class FreightInfo {
  FreightInfo({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.grossFreightFee,
    this.buyerFreightFee,
    this.freightInfoId,
    this.freightBtypeId,
    this.freightBillno,
    this.freightBillNoShow,
    this.weighGoodsStateEnum,
    this.weighGoodsStateEnumDesc,
    this.freightTemplateId,
    this.freightPrintTime,
    this.freightPrintEtypeId,
    this.freightPrintState,
    this.insureTotal,
    this.inputWeight,
    this.inputWeightShow,
    this.inputFreightFee,
    this.weightEtypeId,
    this.weightTime,
    this.weightState,
    this.deliverEtypeId,
    this.deliverEtypeName,
    this.deliverTime,
    this.importWeight,
    this.importFreightFee,
    this.packageType,
    this.virtualPackage,
    this.parentId,
    this.needBalance,
    this.freightBalanceTime,
    this.freightBalanceState,
    this.fullName,
    this.templateName,
    this.templateFeatures,
    this.userCode,
    this.packEtypeId,
    this.ptypeWeight,
    this.ptypeFreightFee,
    this.ptypeVolume,
    this.freightFeeVchcode,
    this.freightRelationShow,
    this.original,
    this.freightBTypeName,
    this.editSyncedFreightNo,
    this.packageCount,
    this.uniqueId,
  });

  FreightInfo.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    grossFreightFee = json['grossFreightFee'];
    buyerFreightFee = json['buyerFreightFee'];
    freightInfoId = json['freightInfoId'];
    freightBtypeId = json['freightBtypeId'];
    freightBillno = json['freightBillno'];
    freightBillNoShow = json['freightBillNoShow'];
    weighGoodsStateEnum = json['weighGoodsStateEnum'];
    weighGoodsStateEnumDesc = json['weighGoodsStateEnumDesc'];
    freightTemplateId = json['freightTemplateId'];
    freightPrintTime = json['freightPrintTime'];
    freightPrintEtypeId = json['freightPrintEtypeId'];
    freightPrintState = json['freightPrintState'];
    insureTotal = json['insureTotal'];
    inputWeight = json['inputWeight'];
    inputWeightShow = json['inputWeightShow'];
    inputFreightFee = json['inputFreightFee'];
    weightEtypeId = json['weightEtypeId'];
    weightTime = json['weightTime'];
    weightState = json['weightState'];
    deliverEtypeId = json['deliverEtypeId'];
    deliverEtypeName=json['deliverEtypeName'];
    deliverTime = json['deliverTime'];
    importWeight = json['importWeight'];
    importFreightFee = json['importFreightFee'];
    packageType = json['packageType'];
    virtualPackage = json['virtualPackage'];
    parentId = json['parentId'];
    needBalance = json['needBalance'];
    freightBalanceTime = json['freightBalanceTime'];
    freightBalanceState = json['freightBalanceState'];
    fullName = json['fullName'];
    templateName = json['templateName'];
    templateFeatures = json['templateFeatures'];
    userCode = json['userCode'];
    packEtypeId = json['packEtypeId'];
    ptypeWeight = json['ptypeWeight'];
    ptypeFreightFee = json['ptypeFreightFee'];
    ptypeVolume = json['ptypeVolume'];
    freightFeeVchcode = json['freightFeeVchcode'];
    freightRelationShow = json['freightRelationShow'];
    original = json['original'];
    freightBTypeName = json['freightBTypeName'];
    editSyncedFreightNo = json['editSyncedFreightNo'];
    packageCount = json['packageCount'];
    uniqueId = json['uniqueId'];
  }

  String? vchcode;
  dynamic profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  dynamic grossFreightFee;
  dynamic buyerFreightFee;
  String? freightInfoId;
  String? freightBtypeId;
  String? freightBillno;
  dynamic freightBillNoShow;
  dynamic weighGoodsStateEnum;
  dynamic weighGoodsStateEnumDesc;
  String? freightTemplateId;
  dynamic freightPrintTime;
  dynamic freightPrintEtypeId;
  dynamic freightPrintState;
  num? insureTotal;
  num? inputWeight;
  String? inputWeightShow;
  dynamic inputFreightFee;
  String? weightEtypeId;
  dynamic weightTime;
  dynamic weightState;
  String? deliverEtypeId;
  String? deliverEtypeName;
  dynamic deliverTime;
  dynamic importWeight;
  dynamic importFreightFee;
  String? packageType;
  dynamic virtualPackage;
  dynamic parentId;
  dynamic needBalance;
  dynamic freightBalanceTime;
  dynamic freightBalanceState;
  dynamic fullName;
  dynamic templateName;
  dynamic templateFeatures;
  dynamic userCode;
  String? packEtypeId;
  num? ptypeWeight;
  dynamic ptypeFreightFee;
  num? ptypeVolume;
  dynamic freightFeeVchcode;
  dynamic freightRelationShow;
  bool? original;
  dynamic freightBTypeName;
  dynamic editSyncedFreightNo;
  dynamic packageCount;
  String? uniqueId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['grossFreightFee'] = grossFreightFee;
    map['buyerFreightFee'] = buyerFreightFee;
    map['freightInfoId'] = freightInfoId;
    map['freightBtypeId'] = freightBtypeId;
    map['freightBillno'] = freightBillno;
    map['freightBillNoShow'] = freightBillNoShow;
    map['weighGoodsStateEnum'] = weighGoodsStateEnum;
    map['weighGoodsStateEnumDesc'] = weighGoodsStateEnumDesc;
    map['freightTemplateId'] = freightTemplateId;
    map['freightPrintTime'] = freightPrintTime;
    map['freightPrintEtypeId'] = freightPrintEtypeId;
    map['freightPrintState'] = freightPrintState;
    map['insureTotal'] = insureTotal;
    map['inputWeight'] = inputWeight;
    map['inputWeightShow'] = inputWeightShow;
    map['inputFreightFee'] = inputFreightFee;
    map['weightEtypeId'] = weightEtypeId;
    map['weightTime'] = weightTime;
    map['weightState'] = weightState;
    map['deliverEtypeId'] = deliverEtypeId;
    map['deliverEtypeName']=deliverEtypeName;
    map['deliverTime'] = deliverTime;
    map['importWeight'] = importWeight;
    map['importFreightFee'] = importFreightFee;
    map['packageType'] = packageType;
    map['virtualPackage'] = virtualPackage;
    map['parentId'] = parentId;
    map['needBalance'] = needBalance;
    map['freightBalanceTime'] = freightBalanceTime;
    map['freightBalanceState'] = freightBalanceState;
    map['fullName'] = fullName;
    map['templateName'] = templateName;
    map['templateFeatures'] = templateFeatures;
    map['userCode'] = userCode;
    map['packEtypeId'] = packEtypeId;
    map['ptypeWeight'] = ptypeWeight;
    map['ptypeFreightFee'] = ptypeFreightFee;
    map['ptypeVolume'] = ptypeVolume;
    map['freightFeeVchcode'] = freightFeeVchcode;
    map['freightRelationShow'] = freightRelationShow;
    map['original'] = original;
    map['freightBTypeName'] = freightBTypeName;
    map['editSyncedFreightNo'] = editSyncedFreightNo;
    map['packageCount'] = packageCount;
    map['uniqueId'] = uniqueId;
    return map;
  }
}

class BillDeliverPrint {
  BillDeliverPrint({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.printBatchId,
    this.assignPrintState,
    this.assignPrintEtypeId,
    this.assignPrintTime,
    this.assignPrintCount,
    this.deliverPrintState,
    this.deliverPrintEtypeId,
    this.deliverPrintTime,
    this.deliverPrintCount,
    this.freightPrintState,
    this.freightPrintEtypeId,
    this.freightPrintTime,
    this.freightPrintCount,
  });

  BillDeliverPrint.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    printBatchId = json['printBatchId'];
    assignPrintState = json['assignPrintState'];
    assignPrintEtypeId = json['assignPrintEtypeId'];
    assignPrintTime = json['assignPrintTime'];
    assignPrintCount = json['assignPrintCount'];
    deliverPrintState = json['deliverPrintState'];
    deliverPrintEtypeId = json['deliverPrintEtypeId'];
    deliverPrintTime = json['deliverPrintTime'];
    deliverPrintCount = json['deliverPrintCount'];
    freightPrintState = json['freightPrintState'];
    freightPrintEtypeId = json['freightPrintEtypeId'];
    freightPrintTime = json['freightPrintTime'];
    freightPrintCount = json['freightPrintCount'];
  }

  String? vchcode;
  dynamic profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  dynamic printBatchId;
  num? assignPrintState;
  dynamic assignPrintEtypeId;
  dynamic assignPrintTime;
  dynamic assignPrintCount;
  num? deliverPrintState;
  dynamic deliverPrintEtypeId;
  dynamic deliverPrintTime;
  dynamic deliverPrintCount;
  dynamic freightPrintState;
  dynamic freightPrintEtypeId;
  dynamic freightPrintTime;
  dynamic freightPrintCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['printBatchId'] = printBatchId;
    map['assignPrintState'] = assignPrintState;
    map['assignPrintEtypeId'] = assignPrintEtypeId;
    map['assignPrintTime'] = assignPrintTime;
    map['assignPrintCount'] = assignPrintCount;
    map['deliverPrintState'] = deliverPrintState;
    map['deliverPrintEtypeId'] = deliverPrintEtypeId;
    map['deliverPrintTime'] = deliverPrintTime;
    map['deliverPrintCount'] = deliverPrintCount;
    map['freightPrintState'] = freightPrintState;
    map['freightPrintEtypeId'] = freightPrintEtypeId;
    map['freightPrintTime'] = freightPrintTime;
    map['freightPrintCount'] = freightPrintCount;
    return map;
  }
}

class PrintBatch {
  PrintBatch({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.printBatchId,
    this.billCount,
    this.createEtypeId,
    this.printBatchno,
    this.configName,
    this.deleted,
    this.assignState,
    this.batchConfigId,
    this.assignPrintState,
    this.assignPrint,
    this.deliverPrintState,
    this.deliverPrint,
    this.freightPrintState,
    this.freightPrint,
    this.createTime,
    this.ktypeId,
    this.freightBtypeId,
    this.normalBill,
    this.assignType,
    this.freightPrintStateCheck,
    this.deliverPrintStateCheck,
    this.assignPrintStateCheck,
    this.coordination,
    this.batchCoordinationNum,
    this.finishCoordinationNum,
    this.assignTime,
    this.coordinationBatch,
    this.warehouseUsed,
    this.batchType,
    this.picksecondEtypeId,
    this.picksecondTime,
    this.batchProcess,
    this.showBatchProcess,
    this.nextBatchProcess,
  });

  PrintBatch.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    printBatchId = json['printBatchId'];
    billCount = json['billCount'];
    createEtypeId = json['createEtypeId'];
    printBatchno = json['printBatchno'];
    configName = json['configName'];
    deleted = json['deleted'];
    assignState = json['assignState'];
    batchConfigId = json['batchConfigId'];
    assignPrintState = json['assignPrintState'];
    assignPrint = json['assignPrint'];
    deliverPrintState = json['deliverPrintState'];
    deliverPrint = json['deliverPrint'];
    freightPrintState = json['freightPrintState'];
    freightPrint = json['freightPrint'];
    createTime = json['createTime'];
    ktypeId = json['ktypeId'];
    freightBtypeId = json['freightBtypeId'];
    normalBill = json['normalBill'];
    assignType = json['assignType'];
    freightPrintStateCheck = json['freightPrintStateCheck'];
    deliverPrintStateCheck = json['deliverPrintStateCheck'];
    assignPrintStateCheck = json['assignPrintStateCheck'];
    coordination = json['coordination'];
    batchCoordinationNum = json['batchCoordinationNum'];
    finishCoordinationNum = json['finishCoordinationNum'];
    assignTime = json['assignTime'];
    coordinationBatch = json['coordinationBatch'];
    warehouseUsed = json['warehouseUsed'];
    batchType = json['batchType'];
    picksecondEtypeId = json['picksecondEtypeId'];
    picksecondTime = json['picksecondTime'];
    batchProcess = json['batchProcess'];
    showBatchProcess = json['showBatchProcess'];
    nextBatchProcess = json['nextBatchProcess'];
  }

  String? vchcode;
  dynamic profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  String? printBatchId;
  dynamic billCount;
  dynamic createEtypeId;
  String? printBatchno;
  dynamic configName;
  dynamic deleted;
  String? assignState;
  String? batchConfigId;
  dynamic assignPrintState;
  bool? assignPrint;
  dynamic deliverPrintState;
  bool? deliverPrint;
  String? freightPrintState;
  bool? freightPrint;
  String? createTime;
  dynamic ktypeId;
  dynamic freightBtypeId;
  dynamic normalBill;
  dynamic assignType;
  dynamic freightPrintStateCheck;
  dynamic deliverPrintStateCheck;
  dynamic assignPrintStateCheck;
  dynamic coordination;
  dynamic batchCoordinationNum;
  dynamic finishCoordinationNum;
  dynamic assignTime;
  dynamic coordinationBatch;
  dynamic warehouseUsed;
  dynamic batchType;
  String? picksecondEtypeId;
  dynamic picksecondTime;
  dynamic batchProcess;
  String? showBatchProcess;
  String? nextBatchProcess;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['printBatchId'] = printBatchId;
    map['billCount'] = billCount;
    map['createEtypeId'] = createEtypeId;
    map['printBatchno'] = printBatchno;
    map['configName'] = configName;
    map['deleted'] = deleted;
    map['assignState'] = assignState;
    map['batchConfigId'] = batchConfigId;
    map['assignPrintState'] = assignPrintState;
    map['assignPrint'] = assignPrint;
    map['deliverPrintState'] = deliverPrintState;
    map['deliverPrint'] = deliverPrint;
    map['freightPrintState'] = freightPrintState;
    map['freightPrint'] = freightPrint;
    map['createTime'] = createTime;
    map['ktypeId'] = ktypeId;
    map['freightBtypeId'] = freightBtypeId;
    map['normalBill'] = normalBill;
    map['assignType'] = assignType;
    map['freightPrintStateCheck'] = freightPrintStateCheck;
    map['deliverPrintStateCheck'] = deliverPrintStateCheck;
    map['assignPrintStateCheck'] = assignPrintStateCheck;
    map['coordination'] = coordination;
    map['batchCoordinationNum'] = batchCoordinationNum;
    map['finishCoordinationNum'] = finishCoordinationNum;
    map['assignTime'] = assignTime;
    map['coordinationBatch'] = coordinationBatch;
    map['warehouseUsed'] = warehouseUsed;
    map['batchType'] = batchType;
    map['picksecondEtypeId'] = picksecondEtypeId;
    map['picksecondTime'] = picksecondTime;
    map['batchProcess'] = batchProcess;
    map['showBatchProcess'] = showBatchProcess;
    map['nextBatchProcess'] = nextBatchProcess;
    return map;
  }
}

class WmsBillDeliver {
  WmsBillDeliver({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.checkGoodsState,
    this.checkGoodsTime,
    this.packingState,
    this.packingTime,
    this.weighGoodsState,
    this.packageCount,
  });

  WmsBillDeliver.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    checkGoodsState = json['checkGoodsState'];
    checkGoodsTime = json['checkGoodsTime'];
    packingState = json['packingState'];
    packingTime = json['packingTime'];
    weighGoodsState = json['weighGoodsState'];
    packageCount = json['packageCount'];
  }

  String? vchcode;
  dynamic profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  String? checkGoodsState;
  dynamic checkGoodsTime;
  String? packingState;
  dynamic packingTime;
  dynamic weighGoodsState;
  num? packageCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['checkGoodsState'] = checkGoodsState;
    map['checkGoodsTime'] = checkGoodsTime;
    map['packingState'] = packingState;
    map['packingTime'] = packingTime;
    map['weighGoodsState'] = weighGoodsState;
    map['packageCount'] = packageCount;
    return map;
  }
}

class DetailState {
  DetailState({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.plateformMark,
    this.tradeState,
    this.refundState,
    this.processState,
    this.lockType,
    this.lockMemo,
    this.freightSyncState,
    this.presetMark,
    this.usersetMark,
    this.platformMark,
    this.strategyMark,
  });

  DetailState.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    plateformMark = json['plateformMark'];
    tradeState = json['tradeState'];
    refundState = json['refundState'];
    processState = json['processState'];
    lockType = json['lockType'];
    lockMemo = json['lockMemo'];
    freightSyncState = json['freightSyncState'];
    presetMark = json['presetMark'];
    usersetMark = json['usersetMark'];
    platformMark = json['platformMark'];
    strategyMark = json['strategyMark'];
  }

  String? vchcode;
  dynamic profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  dynamic plateformMark;
  String? tradeState;
  String? refundState;
  String? processState;
  String? lockType;
  dynamic lockMemo;
  String? freightSyncState;
  dynamic presetMark;
  dynamic usersetMark;
  dynamic platformMark;
  String? strategyMark;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['plateformMark'] = plateformMark;
    map['tradeState'] = tradeState;
    map['refundState'] = refundState;
    map['processState'] = processState;
    map['lockType'] = lockType;
    map['lockMemo'] = lockMemo;
    map['freightSyncState'] = freightSyncState;
    map['presetMark'] = presetMark;
    map['usersetMark'] = usersetMark;
    map['platformMark'] = platformMark;
    map['strategyMark'] = strategyMark;
    return map;
  }
}

class Extend {
  Extend({
    this.vchcode,
    this.profileId,
    this.warehouseTaskId,
    this.wholesaleBill,
    this.platformRequired,
    this.settlementMethod,
    this.payNo,
    this.buyerFreightType,
    this.buyerFreightName,
    this.exportCount,
    this.tradeFinishTime,
    this.deliverTemplateId,
    this.buyerPayableTotal,
    this.platformStoreCode,
    this.platformStoreId,
    this.insured,
    this.payMethod,
    this.sortField,
    this.collectCustomer,
    this.installationServiceProvider,
    this.collectTime,
    this.salesman,
    this.physicalGoodsQty,
    this.physicalGoodsKindCount,
  });

  Extend.fromJson(dynamic json) {
    vchcode = json['vchcode'];
    profileId = json['profileId'];
    warehouseTaskId = json['warehouseTaskId'];
    wholesaleBill = json['wholesaleBill'];
    platformRequired = json['platformRequired'];
    settlementMethod = json['settlementMethod'];
    payNo = json['payNo'];
    buyerFreightType = json['buyerFreightType'];
    buyerFreightName = json['buyerFreightName'];
    exportCount = json['exportCount'];
    tradeFinishTime = json['tradeFinishTime'];
    deliverTemplateId = json['deliverTemplateId'];
    buyerPayableTotal = json['buyerPayableTotal'];
    platformStoreCode = json['platformStoreCode'];
    platformStoreId = json['platformStoreId'];
    insured = json['insured'];
    payMethod = json['payMethod'];
    sortField = json['sortField'];
    collectCustomer = json['collectCustomer'];
    installationServiceProvider = json['installationServiceProvider'];
    collectTime = json['collectTime'];
    salesman = json['salesman'];
    physicalGoodsQty = json['physicalGoodsQty'];
    physicalGoodsKindCount = json['physicalGoodsKindCount'];
  }

  String? vchcode;
  String? profileId;
  dynamic warehouseTaskId;
  dynamic wholesaleBill;
  dynamic platformRequired;
  dynamic settlementMethod;
  String? payNo;
  num? buyerFreightType;
  String? buyerFreightName;
  num? exportCount;
  dynamic tradeFinishTime;
  String? deliverTemplateId;
  dynamic buyerPayableTotal;
  String? platformStoreCode;
  String? platformStoreId;
  bool? insured;
  String? payMethod;
  String? sortField;
  String? collectCustomer;
  dynamic installationServiceProvider;
  dynamic collectTime;
  dynamic salesman;
  num? physicalGoodsQty;
  num? physicalGoodsKindCount;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['vchcode'] = vchcode;
    map['profileId'] = profileId;
    map['warehouseTaskId'] = warehouseTaskId;
    map['wholesaleBill'] = wholesaleBill;
    map['platformRequired'] = platformRequired;
    map['settlementMethod'] = settlementMethod;
    map['payNo'] = payNo;
    map['buyerFreightType'] = buyerFreightType;
    map['buyerFreightName'] = buyerFreightName;
    map['exportCount'] = exportCount;
    map['tradeFinishTime'] = tradeFinishTime;
    map['deliverTemplateId'] = deliverTemplateId;
    map['buyerPayableTotal'] = buyerPayableTotal;
    map['platformStoreCode'] = platformStoreCode;
    map['platformStoreId'] = platformStoreId;
    map['insured'] = insured;
    map['payMethod'] = payMethod;
    map['sortField'] = sortField;
    map['collectCustomer'] = collectCustomer;
    map['installationServiceProvider'] = installationServiceProvider;
    map['collectTime'] = collectTime;
    map['salesman'] = salesman;
    map['physicalGoodsQty'] = physicalGoodsQty;
    map['physicalGoodsKindCount'] = physicalGoodsKindCount;
    return map;
  }
}
