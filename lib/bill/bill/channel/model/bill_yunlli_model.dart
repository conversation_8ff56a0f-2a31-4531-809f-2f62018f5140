import 'package:flutter/material.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:haloui/haloui.dart';

import '../../../../common/net/http_util.dart';
import '../../../../common/net/request_method.dart';
import '../../../entity/yunli/yunli_template.dart';

///
///@ClassName: bill_yunlli_model
///@Description:
///@Author: tanglan
///@Date: 2023/10/29
class YunLiModel {
  ///获取运力
  static Future<List<YunLiTemplate>> getYunLiPlatFormList(
      BuildContext context, requestParams) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.GET_PLATFORM_TEMPLATE_LIST, data: requestParams);
    if (context.mounted) {
      if (response.code! < 0 && null == response.data) {
        HaloToast.show(context, msg: "获取运力失败：${response.message ?? ""}");
        return [];
      } else if (0 == response.data.length) {
        HaloToast.show(context, msg: "该订单平台未配置运力，请确认店铺三方配送授权信息");
        return [];
      }
    }
    List? list = response?.data;
    return list?.map((o) => YunLiTemplate.fromJson(o)).toList() ?? [];
  }

  ///保存运力
  static Future<ResponseModel> doSaveYunLiTemplate(
      BuildContext context, request) async {
    ResponseModel res = await HttpUtil.request(context,
        data: request, method: RequestMethod.DO_SAVE_YUN_LI_TEMPLATE);
    return res;
  }

  ///确认运力
  static Future<ResponseModel> doSubmitYunLiTemplate(
      BuildContext context, request) async {
    return await HttpUtil.request(context,
        data: request, method: RequestMethod.DO_SUBMIT_YUN_LI_TEMPLATE);
  }

  ///取消运力
  static Future<ResponseModel> cancelYunLi(
      BuildContext context, request) async {
    ResponseModel res = await HttpUtil.request(context,
        data: request, method: RequestMethod.CANCEL_YUN_LI);
    return res;
  }

  ///添加消费
  static Future<ResponseModel> addYunLiPrice(
      BuildContext context, request) async {
    ResponseModel res = await HttpUtil.request(context,
        data: request, method: RequestMethod.addYunLiPrice);
    return res;
  }

  ///查询运力状态
  static Future<ResponseModel> queryYunLiStatus(
      BuildContext context, request) async {
    ResponseModel res = await HttpUtil.request(context,
        data: request,
        method: RequestMethod.QUERY_YUN_LI_STATUS,
        isLoading: false);
    return res;
  }

  ///获取运力取消原因
  static Future<Map<String, dynamic>> queryYunLiCancelReason(
      BuildContext context) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.QUERY_YUN_LI_CANCEL_REASON);
    if (response.code! < 0 && null == response.data) {
      return {};
    }
    return response.data;
  }
}
