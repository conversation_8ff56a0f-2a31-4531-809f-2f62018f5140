import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';

mixin BillChannelCompleteMixin<T extends StatefulWidget> on State<T> {
  Widget buildComplete(BuildContext context) {
    return Expanded(
      child: HaloContainer(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        direction: Axis.vertical,
        mainAxisSize: MainAxisSize.max,
        children: [
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            padding: EdgeInsets.only(left: 30.w, right: 30.w),
            children: [
              HaloPosLabel(
                "到店自提/选择发货",
                textStyle: TextStyle(
                    fontSize: 26.sp,
                    color: ColorUtil.stringColor("#333333"),
                    fontWeight: FontWeight.w600),
              ),
              IconButton(onPressed: () {}, icon: IconFont(IconNames.close))
            ],
          ),
          Divider(
            color: ColorUtil.stringColor("#D0D0D0"),
            height: 2.w,
          ),
          Expanded(child: Container()),
          SvgPicture.asset(
            "assets/images/verificationsuccessful.svg",
            width: 230.w,
            height: 170.w,
          ),
          HaloPosLabel(
            "核销成功",
            textStyle: TextStyle(
                fontSize: 34.sp,
                color: ColorUtil.stringColor("#333333"),
                fontWeight: FontWeight.w600),
          ),
          HaloPosLabel(
            "可在核销记录查看相关验证信息",
            textStyle: TextStyle(
                fontSize: 28.sp, color: ColorUtil.stringColor("#666666")),
          ),
          Expanded(child: Container()),
          Divider(
            color: ColorUtil.stringColor("#DDDDDD"),
            height: 1,
          ),
          HaloContainer(
              height: 128.w,
              padding: EdgeInsets.only(top: 20.w, bottom: 18.w),
              children: [
                HaloButton(
                  buttonType: HaloButtonType.outlinedButton,
                  outLineWidth: ScreenUtil().setHeight(1.0),
                  width: 494.w,
                  height: 90.w,
                  onPressed: doPrintData,
                  text: "打印小票",
                ),
                SizedBox(
                  width: 15.w,
                ),
                HaloButton(
                  backgroundColor: ColorUtil.stringColor("#4679FC"),
                  width: 494.w,
                  height: 90.w,
                  onPressed: completeBtn,
                  text: "完成",
                ),
              ])
        ],
      ),
    );
  }

  completeBtn() {}

  doPrintData() {}
}
