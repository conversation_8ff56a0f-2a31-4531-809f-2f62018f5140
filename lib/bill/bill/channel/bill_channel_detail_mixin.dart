import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

import '../../../common/string_res.dart';
import '../../../common/style/app_colors.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/ptype/ptype_prop_dto.dart';
import '../../entity/yunli/platform_info_query_param.dart';
import '../../entity/yunli/yunli_template.dart';
import '../../tool/bill_tool.dart';
import 'model/bill_channel_detail.dart';
import 'model/bill_yunlli_model.dart';
import 'widget/yunli_add_tip_dialog.dart';
import 'widget/yunli_cancel_call_dialog.dart';
import 'widget/yunli_platform_dialog.dart';

mixin BillChannelDetailMixin<T extends StatefulWidget> on State<T> {
  bool isHasMoreData = true;
  List<String> yunliOperator = ["取消呼叫", "二次呼叫", "添加小费"];
  final GlobalKey _yunOperatorGlobalKey = GlobalKey();

  Widget buildDetailInfo(BuildContext context, BillChannelDetailDto entity) {
    return HaloContainer(
      color: Colors.white,
      mainAxisSize: MainAxisSize.max,
      borderRadius: BorderRadius.circular(5),
      padding:
          EdgeInsets.only(left: 18.w, right: 18.w, top: 25.w, bottom: 10.w),
      direction: Axis.vertical,
      margin: EdgeInsets.only(left: 21.w, right: 21.w, top: 21.w, bottom: 10.w),
      children: [
        HaloContainer(
          padding:
              EdgeInsets.only(left: 10.w, right: 100.w, top: 0.w, bottom: 20.w),
          color: Colors.white,
          borderRadius: const BorderRadius.all(Radius.circular(3)),
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: HaloContainer(
                direction: Axis.vertical,
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLab("订单时间：${_getTimeStr(entity.billDate!)}"),
                  SizedBox(
                    height: 10.w,
                  ),
                  _buildLab("核销人：${entity.buyer?.customerReceiver}"),
                ],
              ),
            ),
            Expanded(
              child: HaloContainer(
                direction: Axis.vertical,
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLab("订单号：${entity.billNumber}"),
                  SizedBox(
                    height: 10.w,
                  ),
                  _buildLab("核销状态：${_getFreightSyncState(entity)}"),
                ],
              ),
            ),
            Expanded(
              child: HaloContainer(
                direction: Axis.vertical,
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLab("核销店铺：${entity.eshopFullname}"),
                  SizedBox(
                    height: 10.w,
                  ),
                  _buildLab("核销人手机号：${entity.buyer?.customerReceiverMobile}"),
                ],
              ),
            ),
          ],
        ),
        _buildTop(),
        Expanded(
          child: ListView.builder(
              itemCount: entity.goodsBillDTO?.outDetail?.length ?? 0,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (buildContext, index) {
                GoodsDetailDto detail =
                    entity.goodsBillDTO?.outDetail?[index] ?? GoodsDetailDto();
                return _buildContent(detail);
              }),
        ),
        // _buildFooter(entity),
        _buildFooterWidget(entity)
      ],
    );
  }

  _buildTop() {
    return HaloContainer(
      padding: EdgeInsets.only(left: 28.w, right: 100.w),
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      height: 60.w,
      color: ColorUtil.stringColor("#F3F4F6"),
      children: [
        _buildTopTitle("图"),
        _buildTopTitle("商品名称", flex: 3),
        _buildTopTitle("属性"),
        _buildTopTitle("单价"),
        _buildTopTitle("数量"),
      ],
    );
  }

  _getTimeStr(String time) {
    DateTime beijingTime = DateTime.parse("${time.substring(0, 19)}-0800");
    return DateUtil.formatDateStr(beijingTime.toString(),
        format: DateFormats.full);
  }

  _buildContent(GoodsDetailDto detail) {
    ///商品属性
    String propName = BillTool.getPropName(detail);

    return GestureDetector(
      onTap: (){
        HaloToast.showInfo(context,msg: "点击");
      },
      child: Column(
        children: [
          HaloContainer(
            padding: EdgeInsets.only(left: 28.w, right: 100.w),
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            height: 60.w,
            color: Colors.white,
            children: [
              Expanded(
                  flex: 1,
                  child: Container(
                    alignment: Alignment.center,
                    child: HaloImage(detail.picUrl ?? ""),
                  )),
              _buildContentTitle(detail.pFullName ?? "",
                  flex: 3, isCombo: detail.comboId != "0", outDetail: detail),
              _buildContentTitle(propName),
              _buildContentTitle("¥${detail.currencyPrice.toString()}"),
              _buildContentTitle(
                  "x${detail.unitQty.toString()}" "${detail.unitName}"),
            ],
          ),
          Visibility(
            visible: detail.isShowCombo!,
            child: SizedBox(
              height: 61.w * detail.comboDetailList.length,
              child: ListView.builder(
                  itemCount: detail.comboDetailList.length,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (buildContext, index) {
                    GoodsDetailDto outdetail =
                        detail.comboDetailList[index] ?? GoodsDetailDto();
                    return _buildContent(outdetail);
                  }),
            ),
          ),
          Divider(
            color: ColorUtil.stringColor("#DDDDDD"),
            height: 1.w,
          ),
        ],
      ),
    );
  }

  Widget _buildFooterWidget(BillChannelDetailDto entity) {
    // NONE(0, ""),
    // EXPRESS_LOGISTICS(1, "快递物流"),
    // INTRA_CITY_DISTRIBUTION(2, "三方配送"),
    // CUSTOMER_COLLECT(3, "自提"),
    // DELIVERY_BY_SELF(4, "自己配送"),
    // CAR_SALE(5, "车销"),
    // DELIVERY_BY_MANUFACTURER(6, "厂家发货");
    if (entity.selfDeliveryMode == "CUSTOMER_COLLECT") {
      return _buildFooter(entity);
    } else if (entity.selfDeliveryMode == "INTRA_CITY_DISTRIBUTION") {
      return buildTripartiteDeliveryWidget(entity);
    }
    return Container();
  }

  _buildFooter(BillChannelDetailDto entity) {
    num unitQty = 0;
    for (int i = 0; i < (entity.originalDetail?.length??0); i++) {
      ///非套餐明细才需要加数量
      GoodsDetailDto outDetail = entity.originalDetail![i];
      if (outDetail.comboRow == false) {
        unitQty += outDetail.unitQty.toDouble() ;
      }
    }

    // String stateString = _getFreightSyncState(entity);
    return HaloContainer(
      height: 100.w,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      padding: EdgeInsets.only(left: 0.w),
      children: [
        Expanded(
          child: HaloPosLabel(
            "核销数量：$unitQty",
            textStyle: TextStyle(
                fontSize: 26.sp,
                fontFamily: 'PingFangSC-Medium',
                fontWeight: FontWeight.w600,
                color: ColorUtil.stringColor("#333333")),
          ),
        ),
        // HaloButton(
        //   width: 148.w,
        //   height: 60.w,
        //   enabled:(entity.state?.freightSyncState == "NONE" ||
        //       entity.state?.freightSyncState == "FAIL") ,
        //   backgroundColor: ColorUtil.stringColor("#4679FC"),
        //   text: "系统发货",
        //   onPressed: () {
        //     doItemSubmit(entity.vchcode);
        //   },
        // )
      ],
    );
  }

  Widget buildTripartiteDeliveryWidget(BillChannelDetailDto entity) {
    return HaloContainer(
      // height: 100.h,
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(StringRes.logisticsInfo.getText(context)),
        HaloContainer(
          border: Border(
              bottom: BorderSide(width: 1.w, color: AppColors.borderColor)),
          direction: Axis.vertical,
          children: [
            HaloContainer(
              direction: Axis.horizontal,
              children: [
                Expanded(
                    child: HaloLabel(
                        StringRes.LOGISTICS_NAME_STR.getText(context) +
                            (entity.freightInfo?.templateName ?? ""))),
                Expanded(
                    child: HaloLabel(
                        StringRes.CAPACITY_CALL_STATUS.getText(context) +
                            getCallState(entity.yunliStatus))),
                Expanded(
                  child: Container(),
                )
              ],
            ),
            HaloContainer(
              children: [
                Expanded(
                    child: HaloLabel(
                        StringRes.LOGISTICS_STATUS_STR.getText(context) +
                            getFeieghtState(entity.yunliStatus))),
                Expanded(
                    child: HaloLabel(StringRes.SHIPPING_FEE.getText(context) +
                        (entity.freightInfo?.inputFreightFee ?? "")
                            .toString())),
                Expanded(
                    child: HaloLabel(StringRes.FREIGH_TIPS.getText(context) +
                        (entity.freightInfo?.insureTotal ?? "").toString()))
              ],
            ),
            HaloContainer(
              children: [
                Expanded(
                    child: HaloLabel(
                        StringRes.LOGISTICS_DISPATCHER_NAME.getText(context) +
                            (entity.platformDispatcherName ?? ""))),
                Expanded(
                    child: HaloLabel(
                        StringRes.LOGISTICS_DISPATCHER_MOBILE.getText(context) +
                            (entity.platformDispatcherMobile ?? ""))),
                Expanded(
                    child: HaloLabel(StringRes.DELIVERY_CODE.getText(context) +
                        (entity.pickupCode ?? "")))
              ],
            )
          ],
        ),
        HaloContainer(
          height: 48.h,
          margin: EdgeInsets.symmetric(vertical: 12.h),
          direction: Axis.horizontal,
          children: [
            Expanded(
                child: HaloContainer(
              direction: Axis.horizontal,
              children: [
                HaloButton(
                  text: StringRes.YUNLI_GET_PLATFORM_LIST.getText(context),
                  outLineWidth: 1.w,
                  buttonType: HaloButtonType.outlinedButton,
                  onPressed: () {
                    _getYunLi(entity);
                  },
                ),
                SizedBox(
                  width: 28.w,
                ),
                HaloContainer(
                  key: _yunOperatorGlobalKey,
                  height: 48.h,
                  border: Border.all(color: AppColors.borderColor),
                  crossAxisAlignment: CrossAxisAlignment.center,
                  padding: EdgeInsets.symmetric(horizontal: 12.w),
                  borderRadius: BorderRadius.all(Radius.circular(8.w)),
                  children: [
                    GestureDetector(
                        onTap: () {
                          _callYunLI(entity);
                        },
                        child: Text(StringRes.YUNLI_CALL.getText(context))),
                    GestureDetector(
                        onTap: () {
                          _showYunLiOperator(entity);
                        },
                        child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16.w, vertical: 16.h),
                            child: IconFont(
                              IconNames.guolvxiala,
                              size: 12.w,
                            )))
                  ],
                ),
              ],
            )),
            HaloButton(
              text: "打印小票",
              outLineWidth: 1.w,
              buttonType: HaloButtonType.outlinedButton,
            ),
          ],
        )
      ],
    );
  }

  String getCallState(int? state) {
    if (state == 0) {
      return "待呼叫";
    }
    return "已呼叫";
  }

  String getFeieghtState(int? state) {
    if (state == 0) {
      return "未配送";
    }
    if (state == 1) {
      return "待配送";
    }
    if (state == 2) {
      return "待取货";
    }
    if (state == 3) {
      return "配送中";
    }

    return "已完成";
  }

  _getYunLi(BillChannelDetailDto entity) async {
    PlatformInfoQueryParam param = PlatformInfoQueryParam();
    param.vchcode = entity.vchcode;
    param.warehouseTaskId = entity.warehouseTaskId;
    param.printTemplateType = "TB_YL";
    List<YunLiTemplate> list =
        await YunLiModel.getYunLiPlatFormList(context, param.toJson());

    if (context.mounted) {
      if (list.isEmpty) {
        return;
      }
      HaloDialog(context,
          child: YunLiPlatFormDialog(
              billChannelDetailDto: entity,
              yunliPlatfomList: list,
              onSelected: (select) {
                setState(() {
                  entity.freightInfo?.templateName = select?.ylName ?? "";
                });
              })).show();
    }
  }

//呼叫运力
  _callYunLI(BillChannelDetailDto entity) async {
    PlatformInfoQueryParam param = PlatformInfoQueryParam();
    param.vchcode = entity.vchcode;
    param.warehouseTaskId = entity.warehouseTaskId;
    ResponseModel res =
        await YunLiModel.doSubmitYunLiTemplate(context, param.toJson());
    if (context.mounted) {
      if (res.code == 200) {
        HaloToast.show(context, msg: "呼叫运力成功");
        setState(() {
          entity.yunliStatus = 1;
        });
      } else {
        HaloToast.show(context, msg: res.message);
      }
    }
  }

//运力操作
  _showYunLiOperator(BillChannelDetailDto entity) {
    HaloPopWindow().show(_yunOperatorGlobalKey,
        gravity: PopWindowGravity.top,
        backgroundColor: Colors.transparent,
        intervalTop: -90,
        child: Container(
          width: 160.w,
          height: 180.h,
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppColors.dividerColor),
              borderRadius: BorderRadius.all(Radius.circular(8.w))),
          child: ListView.builder(
              padding: const EdgeInsets.all(0),
              itemCount: yunliOperator.length,
              itemBuilder: (buildContext, index) {
                return GestureDetector(
                  onTap: () {
                    _selectYunLiOperator(entity, index);
                  },
                  child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.all(14.w),
                      child: Text(yunliOperator[index])),
                );
              }),
        ));
  }

  _selectYunLiOperator(BillChannelDetailDto entity, int index) {
    HaloPopWindow().disMiss();
    //取消呼叫
    if (index == 0) {
      HaloDialog(context,
          child: YunLiCancelCallDialog(
            billChannelDetailDto: entity,
          )).show();
    } else if (index == 1) {
      //二次呼叫（二次呼叫与呼叫运力调用同一个接口处理）
      _callYunLI(entity);
    } else if (index == 2) {
      //添加消费
      HaloDialog(context,
          child: YunLiAddTipDialog(
              billChannelDetailDto: entity,
              onSuccess: (price) {
                setState(() {
                  entity.freightInfo?.insureTotal = num.parse(price ?? "0");
                });
              })).show();
    }
  }

  _buildLab(String title) {
    return HaloPosLabel(
      title,
      textStyle: TextStyle(
        fontSize: 22.sp,
        color: ColorUtil.stringColor("#666666"),
      ),
    );
  }

  _buildTopTitle(String title, {flex = 1}) {
    return Expanded(
      flex: flex,
      child: HaloPosLabel(
        title,
        textAlign: TextAlign.center,
        textStyle: TextStyle(
          fontSize: 24.sp,
          fontFamily: 'PingFangSC-Medium',
          fontWeight: FontWeight.w600,
          color: ColorUtil.stringColor("#333333"),
        ),
      ),
    );
  }

  _buildContentTitle(String title,
      {flex = 1, isCombo = false, GoodsDetailDto? outDetail}) {
    return Expanded(
      flex: flex,
      child: HaloContainer(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Visibility(
              visible: isCombo,
              child: Container(
                width: 28.w,
                height: 28.w,
                margin: EdgeInsets.only(right: 8.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                        color: const Color(0xFFCA12BC),
                        width: 1.h,
                        style: BorderStyle.solid),
                    borderRadius: const BorderRadius.all(Radius.circular(3))),
                child: HaloPosLabel("套",
                    textStyle: TextStyle(
                        color: const Color(0xFFCA12BC),
                        decoration: TextDecoration.none,
                        fontSize: 20.sp,
                        fontWeight: FontWeight.normal)),
              )),
          Flexible(
            child: HaloPosLabel(
              title,
              textAlign: TextAlign.center,
              textStyle: TextStyle(
                fontSize: 22.sp,
                color: ColorUtil.stringColor("#333333"),
              ),
            ),
          ),
          Visibility(
            visible: isCombo,
            child: Flexible(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    outDetail?.isShowCombo = !outDetail.isShowCombo!;
                  });
                },
                child: HaloPosLabel(
                  (outDetail?.isShowCombo ?? false) ? " 收起" : " 展开",
                  textAlign: TextAlign.center,
                  textStyle: TextStyle(
                    fontSize: 22.sp,
                    color: AppColors.accentColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// WAIT_SELLER_SEND_GOODS 待核销
  /// TRADE_FINISHED 已核销
  String _getFreightSyncState(BillChannelDetailDto entity) {
    if (entity.state?.freightSyncState == "NONE" ||
        entity.state?.freightSyncState == "FAIL") {
      return "待核销";
    } else if (entity.state?.freightSyncState == "SUCCESS") {
      return "已核销";
    } else {
      return "无需核销";
    }
  }

  doItemSubmit(String? vchcode) {}

}
