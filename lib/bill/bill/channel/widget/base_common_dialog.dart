import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../../../iconfont/icon_font.dart';

abstract class BaseCommonDialogState<T extends StatefulWidget>
    extends State<T> {
  ///分割线
  final divider = Divider(height: 1.w, color: ColorUtil.stringColor("#D0D0D0"));

  ///宽
  double get width;

  ///高
  double get height;

  ///标题
  String get title;

  ///标题
  bool get showCloseBtn => true;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if(didPop) return;
        onCloseButtonClick(context);
      },
      child: UnconstrainedBox(
        child: SizedBox(
          child: Material(
            borderRadius: BorderRadius.circular(10.w),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: HaloContainer(
                width: width,
                height: height,
                color: Colors.white,
                direction: Axis.vertical,
                children: [
                  //标题
                  Visibility(
                    visible: title.isNotEmpty,
                    child: buildTitle(context),
                  ),
                  //内容，如列表和搜索框
                  Expanded(child: buildContent(context)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  ///标题栏和关闭按钮
  Widget buildTitle(BuildContext context) {
    return Column(
      children: [
        HaloContainer(
          height: 72.h,
          padding: const EdgeInsets.only(left: 20, right: 0),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: const Color(0xFF333333),
                fontSize: 28.sp,
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: Visibility(
                visible: showCloseBtn,
                child: Container(
                  width: 60,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10.0),
                    ),
                    color: Colors.white,
                  ),
                  child: IconFont(IconNames.close, size: 25.w),
                ),
              ),
              onTap: () => onCloseButtonClick(context),
            ),
          ],
        ),
        //分割线
        divider,
      ],
    );
  }

  ///构建内容
  Widget buildContent(BuildContext context) => Container();

  ///当点击了关闭按钮
  void onCloseButtonClick(BuildContext context) => Navigator.pop(context);
}
