import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../../../../common/style/app_colors.dart';
import '../../../../widgets/halo_pos_label.dart';
import '../../../entity/goods_detail_dto.dart';
import '../../../entity/ptype/ptype_serial_no_dto.dart';
import '../../../tool/goods_tool.dart';
import '../../../widget/ptype/goods_bind_batch_page.dart';
import '../../../widget/ptype/goods_bind_sn_page.dart';
import 'base_common_dialog.dart';

class BillChannelOrderSnBatchDialog  extends StatefulWidget {
   final GoodsDetailDto detailDto;
   const BillChannelOrderSnBatchDialog({Key? key,required this.detailDto}) : super(key: key);

  @override
  State<BillChannelOrderSnBatchDialog> createState() => _BillChannelOrderSnBatchDialogState();
}

class _BillChannelOrderSnBatchDialogState extends BaseCommonDialogState<BillChannelOrderSnBatchDialog> {

  late TextEditingController editingControllerSn;
  late TextEditingController editingControllerBatch;

  @override
  // TODO: implement height
  double get height {
    return (widget.detailDto.batchenabled &&  widget.detailDto.snenabled>0)? 370.w: 270.w;
  }

  @override
  // TODO: implement title
  String get title => "批次/序列号编辑";

  @override
  // TODO: implement width
  double get width => 600.w;

  @override
  void initState() {
    // TODO: implement initState

    super.initState();
  }



  @override
  Widget buildContent(BuildContext context) {
    editingControllerBatch = TextEditingController(text: widget.detailDto.batchNo);
    editingControllerSn = TextEditingController(text: getSnStr(widget.detailDto.serialNoList));
    return HaloContainer(
      direction: Axis.vertical,
      children: [
        Visibility(
          visible: widget.detailDto.batchenabled,
          child: _buildEditContent(context, "批次号：", editingControllerBatch,(){
            _btnBatch(widget.detailDto);
          }),
        ),
        Visibility(
          visible: widget.detailDto.snenabled>0,
          child: _buildEditContent(context, "序列号：", editingControllerSn,(){
            _btnSn(widget.detailDto);
          }),
        ),
        _buildBottomBody(context)
      ],
    );
  }

  Widget _buildEditContent(BuildContext context,String text,TextEditingController controller,Function() tap){
    return GestureDetector(
      onTap: tap,
      child: HaloContainer(
        margin: EdgeInsets.only(left: 30.w,right: 30.w),
        children: [
          HaloPosLabel(text),
          Flexible(
            child: TextField(
              enabled: false,
              decoration: const InputDecoration(
                border: InputBorder.none,
                counterText: "",
              ),
              controller: controller,
              textAlign: TextAlign.start,
              style: TextStyle(fontSize: 24.sp),
              maxLines: 1,
              onChanged: (text){

              },
            ),
          ),
          Image.asset('assets/images/edit.png')
        ],
      ),
    );
  }

  Widget _buildBottomBody(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 100.w,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          HaloButton(
            buttonType: HaloButtonType.elevatedButton,
            outLineWidth: 1.w,
            foregroundColor: AppColors.normalTextColor,
            borderColor: AppColors.btnBorderColor,
            height: 50.h,
            width: 190.w,
            text: "确定",
            textColor: Colors.white,
            fontSize: 26.sp,
            onPressed: () {
              Navigator.pop(context,widget.detailDto);
            },
          ),
          SizedBox(
            width: 40.w,
          ),
          HaloButton(
            buttonType: HaloButtonType.outlinedButton,
            outLineWidth: 1.w,
            foregroundColor: AppColors.normalTextColor,
            borderColor: AppColors.btnBorderColor,
            height: 50.h,
            width: 190.w,
            text: "取消",
            fontSize: 26.sp,
            onPressed: () {
              Navigator.pop(context);
            },
          ),

        ],
      ),
    );
  }

  String getSnStr(List<PtypeSerialNoDto>snList){
    return snList.map((e) => e.snno).toList().join(",");
  }

  ///批次号
  _btnBatch(GoodsDetailDto goods){
    showDialog(
        context: context,
        builder: (context) => GoodsBindBatchPage(goods: goods)).then((value) {
          setState(() {

          });
    });
  }

  ///序列号选择
  _btnSn(GoodsDetailDto goods) {
    //如果是批次号商品，且没有选择批次号，那么需要先选择批次号
    if (goods.batchenabled && !GoodsTool.isGoodsBindWithBatch(goods)) {
      // showDialog(
      //     context: context,
      //     builder: (context) => GoodsBindBatchPage(goods: goods));
      HaloToast.showInfo(context,msg: "请先选择批次号");
      return;
    }
    //点击跳转到选择商品序列号弹窗
    showDialog(
        context: context,
        builder: (context) => GoodsBindSnPage(
          goods: goods,
          existSN: [
            ...goods.serialNoList.map((e) => e.snno??"")
          ],
          limitMaxCount: true,
        )).then((value) {
      if (value != null) {
        setState(() {});
      }
    });
  }

}
