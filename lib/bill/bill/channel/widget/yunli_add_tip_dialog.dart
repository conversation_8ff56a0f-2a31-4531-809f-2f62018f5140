import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';

import '../../../../common/string_res.dart';
import '../../../../common/style/app_colors.dart';
import '../../../entity/yunli/platform_info_query_param.dart';
import '../model/bill_channel_detail.dart';
import '../model/bill_yunlli_model.dart';

///
///@ClassName: yunli_add_tip_dialog
///@Description: 运力添加小票
///@Author: tanglan
///@Date: 2023/10/27
class YunLiAddTipDialog extends StatefulWidget {
  final BillChannelDetailDto? billChannelDetailDto;
  final Function(String?)? onSuccess;

  const YunLiAddTipDialog(
      {Key? key, this.billChannelDetailDto, this.onSuccess})
      : super(key: key);

  @override
  State<YunLiAddTipDialog> createState() {
    return _YunLiAddTipState();
  }
}

class _YunLiAddTipState extends State<YunLiAddTipDialog> {
  TextEditingController txtEditTipController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
            child: HaloContainer(
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          color: AppColors.cardColor,
          width: 450.w,
          height: 300.w,
          direction: Axis.vertical,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              child: Text(StringRes.ADD_TIP.getText(context),
                  style: TextStyle(
                      color: AppColors.normalTextColor,
                      fontSize: 24.sp,
                      decoration: TextDecoration.none)),
            ),
            HaloContainer(
              direction: Axis.vertical,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.w),
              crossAxisAlignment: CrossAxisAlignment.start,
              border: Border(
                  top: BorderSide(color: AppColors.dividerColor, width: 2.w)),
              children: [
                Text(
                  StringRes.LOGISTICS_NAME_STR.getText(context) +
                      (widget.billChannelDetailDto?.freightInfo?.templateName ??
                          ""),
                  style: TextStyle(
                      fontSize: 20.sp,
                      color: AppColors.normalTextColor,
                      decoration: TextDecoration.none),
                ),
                HaloContainer(
                    margin: EdgeInsets.only(top: 20.w),
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        StringRes.FREIGH_TIPS.getText(context),
                        style: TextStyle(
                            fontSize: 20.sp,
                            color: AppColors.normalTextColor,
                            decoration: TextDecoration.none),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 4.h),
                        margin: EdgeInsets.only(right: 8.w),
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: AppColors.dividerColor, width: 2.w),
                        ),
                        child: HaloNumberTextField(
                          controller: txtEditTipController,
                          width: 150.w,
                          decimalCount: 0,
                          maxValue: 1000,
                          minWidth: 150.w,
                          fontSize: 24.sp,
                          maxLines: 1,
                        ),
                      ),

                      // TextField(),
                      Text("元")
                    ]),
                HaloContainer(
                    mainAxisSize: MainAxisSize.max,
                    margin: EdgeInsets.only(top: 24.w),
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      HaloButton(
                        height: 48.w,
                        text: "确定",
                        onPressed: () {
                          doSave(context);
                        },
                      ),
                      SizedBox(
                        width: 28.w,
                      ),
                      HaloButton(
                        buttonType: HaloButtonType.outlinedButton,
                        outLineWidth: 2.w,
                        foregroundColor: AppColors.normalTextColor,
                        borderColor: AppColors.btnBorderColor,
                        height: 48.w,
                        text: "取消",
                        onPressed: () {
                          NavigateUtil.pop(context);
                        },
                      ),
                    ])
              ],
            )
          ],
        )));
  }

  doSave(BuildContext context) async {
    if (StringUtil.isZeroOrEmpty(txtEditTipController.text)) {
      HaloToast.show(context, msg: "请输入小费金额");
      return;
    }
    PlatformInfoQueryParam param = PlatformInfoQueryParam();
    param.warehouseTaskId = widget.billChannelDetailDto?.warehouseTaskId;
    param.addPrice = double.parse(txtEditTipController.text);
    ResponseModel res =
        await YunLiModel.addYunLiPrice(context, [param.toJson()]);
    if (context.mounted) {
      if (res.code == 200) {
        HaloToast.show(context, msg: "添加小费成功");
        NavigateUtil.pop(context);
        if (null != widget.onSuccess) {
          widget.onSuccess!(txtEditTipController.text ?? "");
        }
      } else {
        HaloToast.show(context, msg: res.message);
      }
    }
  }
}
