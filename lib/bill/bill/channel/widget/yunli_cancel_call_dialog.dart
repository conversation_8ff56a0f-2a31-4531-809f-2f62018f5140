import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/haloui.dart';

import '../../../../common/string_res.dart';
import '../../../../common/style/app_color_helper.dart';
import '../../../../common/style/app_colors.dart';
import '../../../../iconfont/icon_font.dart';
import '../../../entity/yunli/platform_info_query_param.dart';
import '../model/bill_channel_detail.dart';
import '../model/bill_yunlli_model.dart';

///
///@ClassName: yunli_cancel_call_dialog
///@Description:运力取消呼叫弹框
///@Author: tanglan
///@Date: 2023/10/27
class YunLiCancelCallDialog extends StatefulWidget {
  final BillChannelDetailDto? billChannelDetailDto;
  final Function? onSuccess;

  const YunLiCancelCallDialog(
      {Key? key, this.billChannelDetailDto, this.onSuccess})
      : super(key: key);

  @override
  State<YunLiCancelCallDialog> createState() {
    return _YunLiCancelCallState();
  }
}

class _YunLiCancelCallState extends State<YunLiCancelCallDialog> {
  final _YunLiCancelCallController _yunLiCancelCallController =
      Get.put(_YunLiCancelCallController());
  Map<String, dynamic>? _yunLiCancelReason;
  final GlobalKey _yunLiReasonKey = GlobalKey(); //取消原因

  @override
  void initState() {
    _loadYunLiReason();
    super.initState();
  }

  @override
  void dispose() {
    HaloPopWindow().disMiss();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
            child: HaloContainer(
          direction: Axis.vertical,
          color: AppColors.cardColor,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          width: 600.w,
          height: 300.w,
          // margin: EdgeInsets.symmetric(horizontal: 700.w, vertical: 380.w),
          children: [
            Container(
                padding: EdgeInsets.all(8.w),
                child: Text(StringRes.CANCEL_CALL.getText(context),
                    style: TextStyle(
                        color: AppColors.normalTextColor,
                        fontSize: 24.sp,
                        decoration: TextDecoration.none))),
            HaloContainer(
              direction: Axis.vertical,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.w),
              crossAxisAlignment: CrossAxisAlignment.start,
              border: Border(
                  top: BorderSide(color: AppColors.dividerColor, width: 2.w)),
              children: [
                Text(
                  StringRes.LOGISTICS_NAME_STR.getText(context) +
                      (widget.billChannelDetailDto?.freightInfo?.templateName ??
                          ""),
                  style: TextStyle(
                      fontSize: 20.sp,
                      color: AppColors.normalTextColor,
                      decoration: TextDecoration.none),
                ),
                HaloContainer(
                    margin: EdgeInsets.only(top: 20.w),
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        StringRes.CANCEL_REASON.getText(context),
                        style: TextStyle(
                            fontSize: 20.sp,
                            color: AppColors.normalTextColor,
                            decoration: TextDecoration.none),
                      ),
                      Expanded(
                          child: GestureDetector(
                              onTap: () {
                                _showCancelReasonPopWindow(context);
                              },
                              behavior: HitTestBehavior.opaque,
                              child: HaloContainer(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  key: _yunLiReasonKey,
                                  mainAxisSize: MainAxisSize.max,
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 24.w),
                                  height: 48.w,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(6.w)),
                                  border:
                                      Border.all(color: AppColors.borderColor),
                                  children: [
                                    Expanded(
                                        child: Obx(() => Text(
                                              _yunLiCancelCallController
                                                  .message.value,
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: TextStyle(
                                                  color: AppColorHelper(context)
                                                      .getTitleBoldTextColor(),
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 20.sp),
                                            ))),
                                    SizedBox(
                                      width: 16.w,
                                    ),
                                    IconFont(
                                      IconNames.guolvxiala,
                                      size: 12.w,
                                    )
                                  ])))
                    ]),
                HaloContainer(
                    mainAxisSize: MainAxisSize.max,
                    margin: EdgeInsets.only(top: 24.w),
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      HaloButton(
                        height: 48.w,
                        text: "确定",
                        onPressed: () {
                          doSave(context);
                        },
                      ),
                      SizedBox(
                        width: 28.w,
                      ),
                      HaloButton(
                        buttonType: HaloButtonType.outlinedButton,
                        outLineWidth: 2.w,
                        foregroundColor: AppColors.normalTextColor,
                        borderColor: AppColors.btnBorderColor,
                        height: 48.w,
                        text: "取消",
                        onPressed: () {
                          NavigateUtil.pop(context);
                        },
                      ),
                    ])
              ],
            )
          ],
        )));
  }

  _loadYunLiReason() async {
    _yunLiCancelReason = await YunLiModel.queryYunLiCancelReason(context);
    _yunLiCancelCallController.setYunliName(_yunLiCancelReason?["1"]);
  }

  //取消原因列表
  _showCancelReasonPopWindow(BuildContext context) {
    HaloPopWindow().show(_yunLiReasonKey,
        gravity: PopWindowGravity.bottom,
        backgroundColor: Colors.transparent,
        // intervalTop: 10,
        child: Container(
          width: 450.w,
          height: _yunLiCancelReason!.keys.length * 60.w,
          constraints: BoxConstraints(maxHeight: 500.w),
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppColors.dividerColor),
              borderRadius: BorderRadius.all(Radius.circular(8.w))),
          child: ListView.builder(
              padding: const EdgeInsets.all(0),
              itemCount: _yunLiCancelReason!.keys.length,
              itemBuilder: (buildContext, index) {
                return _ItemWidget(
                  name: _yunLiCancelReason![(index + 1).toString()],
                  index: index,
                  onSelectItem: (selectName) {
                    _yunLiCancelCallController.setYunliName(selectName ?? "");
                    HaloPopWindow().disMiss();
                  },
                );
              }),
        ));
  }

  doSave(BuildContext context) async {
    PlatformInfoQueryParam param = PlatformInfoQueryParam();
    param.warehouseTaskId = widget.billChannelDetailDto?.warehouseTaskId;
    param.message = _yunLiCancelCallController.message.value;
    ResponseModel responseModel =
        await YunLiModel.cancelYunLi(context, [param.toJson()]);
    if (context.mounted) {
      if (responseModel.code == 200) {
        NavigateUtil.pop(context);
        HaloToast.show(context, msg: "运力呼叫取消成功");
        if (null == widget.onSuccess) {
          widget.onSuccess!();
        }
        return;
      }
      HaloToast.show(context, msg: responseModel.message);
    }
  }
}

class _ItemWidget extends StatelessWidget {
  final Function(String?)? onSelectItem;
  final String? name;
  final int? index;

  const _ItemWidget({this.name, this.index, this.onSelectItem});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          if (null != onSelectItem) {
            onSelectItem!(name);
          }
        },
        child: Container(
          height: 60.w,
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: AppColors.dividerColor))),
          child: Text(
            name ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: 20.sp,
                color: AppColorHelper(context).getTitleBoldTextColor()),
          ),
        ));
  }
}

class _YunLiCancelCallController extends GetxController {
  var message = "".obs;

  void setYunliName(String name) {
    message.value = name;
  }
}
