import 'dart:async';

import 'package:bluetooth_print/dto/print_data_dto.dart';
import 'package:bluetooth_print/enum/print_align_enum.dart';
import 'package:bluetooth_print/enum/print_font_size_enum.dart';
import 'package:bluetooth_print/enum/print_type_enum.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../../common/login/login_center.dart';
import '../../../../common/standard.dart';
import '../../../../common/string_res.dart';
import '../../../../common/style/app_colors.dart';
import '../../../../common/tool/dialog_util.dart';
import '../../../../common/tool/sp_tool.dart';
import '../../../../common/widget/ptype_note_richtext.dart';
import '../../../../enum/setting/print_width_type.dart';
import '../../../../login/entity/store/store_info.dart';
import '../../../../print/tool/print_config.dart';
import '../../../../print/tool/print_tool.dart';
import '../../../../widgets/halo_pos_label.dart';
import '../../../entity/goods_detail_dto.dart';
import '../../../entity/yunli/platform_info_query_param.dart';
import '../../../entity/yunli/yunli_template.dart';
import '../../../tool/bill_tool.dart';
import '../../../tool/decimal_display_helper.dart';
import '../bill_channel_scan.dart';
import '../model/bill_channel_detail.dart';
import '../model/bill_channel_detail_request.dart';
import '../model/bill_channel_model.dart';
import '../model/bill_channel_task_detail.dart';
import '../model/bill_yunlli_model.dart';
import 'bill_channel_order_snBatch_dialog.dart';
import 'yunli_add_tip_dialog.dart';
import 'yunli_cancel_call_dialog.dart';
import 'yunli_platform_dialog.dart';

///
///@ClassName: bill_channel_order_detail_widget
///@Description: 运力详情
///@Author: tanglan
///@Date: 2023/11/2
///
///
typedef RefreshCallBack = void Function();

class BillChannelOrderDetailWidget extends StatefulWidget {
  final BillChannelDetailDto? billChannelDetailDto;
  final RefreshCallBack refreshCallBack;

  const BillChannelOrderDetailWidget(
    this.refreshCallBack,
    this.billChannelDetailDto, {
    Key? key,
  }) : super(key: key);

  @override
  State<BillChannelOrderDetailWidget> createState() {
    return _BillChannelOrderDetailWidgetState();
  }
}

class _BillChannelOrderDetailWidgetState
    extends State<BillChannelOrderDetailWidget> {
  List<String> yunLiOperator = ["取消呼叫", "二次呼叫", "添加小费"];
  final GlobalKey _yunOperatorGlobalKey = GlobalKey();
  _EtypeName _etype = Get.put(_EtypeName());

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      color: Colors.white,
      mainAxisSize: MainAxisSize.max,
      borderRadius: BorderRadius.circular(5),
      margin: EdgeInsets.only(top: 24.h, bottom: 24.h, right: 24.w),
      direction: Axis.vertical,
      children: _buildDetail(),
    );
  }

  List<Widget> _buildDetail() {
    if (null == widget.billChannelDetailDto) {
      return [
        Text(
          "订单详情",
          style: TextStyle(
              fontSize: 24.sp,
              color: AppColors.normalTextColor,
              fontWeight: FontWeight.w500),
        ),
      ];
    }
    return [
      Text(
        "订单详情",
        style: TextStyle(
            fontSize: 24.sp,
            color: AppColors.normalTextColor,
            fontWeight: FontWeight.w500),
      ),
      HaloContainer(
        color: Colors.white,
        margin: EdgeInsets.only(top: 8.h),
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        borderRadius: const BorderRadius.all(Radius.circular(3)),
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: HaloContainer(
              direction: Axis.vertical,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildLab("订单编号：${widget.billChannelDetailDto?.billNumber}"),
                _buildLab("网店名称：${widget.billChannelDetailDto?.eshopFullname}"),
              ],
            ),
          ),
          Expanded(
            child: HaloContainer(
              direction: Axis.vertical,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildLab(
                    "订单时间：${_getTimeStr(widget.billChannelDetailDto?.payTime)}"),
                _buildLab(
                    "收货人姓名：${widget.billChannelDetailDto?.buyer?.customerReceiver}"),
              ],
            ),
          ),
          Expanded(
            child: HaloContainer(
              direction: Axis.vertical,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildLab(
                    "平台店铺类型：${widget.billChannelDetailDto?.eshopTypeName ?? ""}"),
                _buildLab(
                    "收货人手机号：${widget.billChannelDetailDto?.buyer?.customerReceiverMobile}"),
              ],
            ),
          ),
        ],
      ),
      HaloContainer(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
              child: _buildLab(
                  "收货人地址：${widget.billChannelDetailDto?.buyer?.customerReceiverFullAddress}")),
        ],
      ),
      HaloContainer(
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        mainAxisSize: MainAxisSize.max,
        margin: EdgeInsets.only(bottom: 8.h),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildLab("买家备注：${widget.billChannelDetailDto?.buyerMessage}"),
        ],
      ),
      _buildTop(),
      Expanded(
          child: ListView.builder(
              shrinkWrap: true,
              itemCount: widget.billChannelDetailDto?.deliverDetail!.length,
              itemBuilder: (buildContext, index) {
                BillChannelTaskDetail detail =
                    widget.billChannelDetailDto?.deliverDetail![index] ??
                        BillChannelTaskDetail();
                return _buildContent(detail);
              })),
      HaloContainer(
        padding: EdgeInsets.only(right: 160.w),
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
              "共${getSumQty(widget.billChannelDetailDto?.originalDetail ?? [])}件")
        ],
      ),
      HaloContainer(
        direction: Axis.vertical,
        children: [_buildFooterWidget(), _buildBottomButton()],
      )
    ];
  }

  _buildTop() {
    return HaloContainer(
      padding: EdgeInsets.only(left: 28.w, right: 100.w),
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      height: 60.w,
      color: ColorUtil.stringColor("#F3F4F6"),
      children: [
        _buildTopTitle("图"),
        _buildTopTitle("商品名称", flex: 3),
        _buildTopTitle("属性"),
        _buildTopTitle("单价"),
        _buildTopTitle("数量"),
      ],
    );
  }

  _getTimeStr(String? time) {
    if (time == null) return "";
    DateTime beijingTime = DateTime.parse("${time!.substring(0, 19)}-0800");
    return DateUtil.formatDateStr(beijingTime.toString(),
        format: DateFormats.full);
  }

  _buildContent(GoodsDetailDto detail) {
    ///商品属性
    String propName = BillTool.getPropName(detail);

    return GestureDetector(
      onTap: () {
        if (getIsHeXiao() ||
            widget.billChannelDetailDto?.state?.freightSyncState == "FAIL")
          return;
        if (detail.batchenabled || detail.snenabled > 0) {
          if (detail.costMode == 1) {
            HaloToast.showInfo(context, msg: "个别加价商品暂不支持修改批次号/序列号");
          } else {
            DialogUtil.showAlertDialog(context,
                    child: BillChannelOrderSnBatchDialog(detailDto: detail))
                .then((value) {
              if (value != null) {
                BillChannelTaskDetail taskDetail = value;
                taskDetail.serialNo = taskDetail.serialNoList;
                taskDetail.batchno = taskDetail.batchNo;
                BillChannelDetailRequest request = BillChannelDetailRequest();
                request.taskDetails =
                    (widget.billChannelDetailDto!.originalDetail!.isNotEmpty
                        ? widget.billChannelDetailDto!.originalDetail
                        : widget.billChannelDetailDto!.deliverDetail!)!;
                request.taskId = widget.billChannelDetailDto!.warehouseTaskId!;
                BillChannelModel.updateTaskDetails(context, request)
                    .then((value) {
                  if (value) {
                    HaloToast.showSuccess(context, msg: "编辑成功");
                  }
                });
              } else {
                BillChannelTaskDetail taskDetail =
                    detail as BillChannelTaskDetail;
                taskDetail.serialNoList = taskDetail.serialNo ?? [];
                taskDetail.batchNo = taskDetail.batchno ?? "";
              }
            });
          }
        }
      },
      child: Column(
        children: [
          HaloContainer(
            padding: EdgeInsets.only(left: 28.w, right: 100.w),
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            height: 60.w,
            color: Colors.white,
            children: [
              Expanded(
                  flex: 1,
                  child: Container(
                    alignment: Alignment.center,
                    child: HaloImage(detail.picUrl ?? ""),
                  )),
              _buildContentTitle(detail.pFullName ?? "",
                  flex: 3,
                  isCombo: (detail.comboId != "0" || detail.comboRow),
                  outDetail: detail),
              _buildContentTitle(propName),
              _buildContentTitle("¥${detail.currencyPrice.toString()}"),
              _buildContentTitle(
                  "x${detail.unitQty.toString()}" "${detail.unitName}"),
            ],
          ),
          Visibility(
            visible: detail.isShowCombo!,
            child: SizedBox(
              height: 61.w * detail.comboDetailList.length,
              child: ListView.builder(
                  itemCount: detail.comboDetailList.length,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (buildContext, index) {
                    GoodsDetailDto outdetail =
                        detail.comboDetailList[index] ?? GoodsDetailDto();
                    return _buildContent(outdetail);
                  }),
            ),
          ),
          Divider(
            color: ColorUtil.stringColor("#DDDDDD"),
            height: 1.w,
          ),
        ],
      ),
    );
  }

  Widget _buildFooterWidget() {
    // NONE(0, ""),
    // EXPRESS_LOGISTICS(1, "快递物流"),
    // INTRA_CITY_DISTRIBUTION(2, "三方配送"),
    // CUSTOMER_COLLECT(3, "自提"),
    // DELIVERY_BY_SELF(4, "自己配送"),
    // CAR_SALE(5, "车销"),
    // DELIVERY_BY_MANUFACTURER(6, "厂家发货");
    if (widget.billChannelDetailDto?.selfDeliveryMode == "CUSTOMER_COLLECT") {
      return getIsHeXiao() ? _buildYiHeXiaoFoot() : _buildFooter();
    }
    //TODO 因仓储需求延期，屏蔽三方运力
    // else if (widget.billChannelDetailDto?.selfDeliveryMode ==
    //     "INTRA_CITY_DISTRIBUTION") {
    //   return buildTripartiteDeliveryWidget();
    // }
    return Container();
  }

  bool getIsHeXiao() {
    return ((widget.billChannelDetailDto?.state?.freightSyncState == "NONE" &&
            widget.billChannelDetailDto?.state?.processState == "SENDED") ||
        widget.billChannelDetailDto?.state?.freightSyncState == "SUCCESS" &&
            widget.billChannelDetailDto?.state?.processState == "SENDED" ||
        widget.billChannelDetailDto?.state?.freightSyncState == "NOT_NEED");
  }

  Widget _buildYiHeXiaoFoot() {
    return HaloContainer(
      color: ColorUtil.stringColor("#ffffff"),
      height: 100,
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
      children: [
        Expanded(
            child: Text(
                "${StringRes.senderName.getText(context)}:${(widget.billChannelDetailDto?.freightInfo?.deliverEtypeName ?? "")}",
                style: TextStyle(
                    color: AppColors.normalTextColor,
                    fontSize: 22.sp,
                    fontWeight: FontWeight.w400))),
        Expanded(
            child: Text(
                "${StringRes.senderDate.getText(context)}:${widget.billChannelDetailDto != null ? (DateUtil.formatDateStr(widget.billChannelDetailDto!.deliverTime.toString(), isUtc: false)) : ""}",
                style: TextStyle(
                    color: AppColors.normalTextColor,
                    fontSize: 22.sp,
                    fontWeight: FontWeight.w400))),
        Expanded(child: HaloPosLabel("核销状态：已核销")),
        HaloButton(
          buttonType: HaloButtonType.outlinedButton,
          outLineWidth: ScreenUtil().setHeight(1.0),
          width: 200.w,
          height: 40.w,
          onPressed: doPrintData,
          text: "打印小票",
        ),
      ],
    );
  }

  doPrintData() {
    //打印小票
    PrintTool.printChannelBill(context, widget.billChannelDetailDto!);
  }

  _buildFooter() {
    num unitQty = 0;
    widget.billChannelDetailDto?.originalDetail?.forEach((element) {
      if (element.comboId == "0" || !element.comboRow) {
        unitQty += element.unitQty;
      }
    });
    return HaloContainer(
      height: 100.w,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      padding: EdgeInsets.only(left: 15.w,right: 20.w),
      children: [
        Padding(
          padding:  EdgeInsets.only(right: 20.w),
          child: HaloButton(
            width: 188.w,
            height: 60.w,
            buttonType: HaloButtonType.outlinedButton,
            outLineWidth: ScreenUtil().setHeight(1.0),
            borderColor: !getIsHeXiao()?ColorUtil.stringColor("#4679FC"):ColorUtil.stringColor("#999999"),
            enabled:!getIsHeXiao() ,
            text: "核销",
            onPressed: () {
              showChannelScanDialog(context,warehouseTaskId: widget.billChannelDetailDto?.warehouseTaskId);
            },
          ),
        ),
        Expanded(
          child: HaloPosLabel(
            "核销数量：$unitQty",
            textStyle: TextStyle(
                fontSize: 26.sp,
                fontFamily: 'PingFangSC-Medium',
                fontWeight: FontWeight.w600,
                color: ColorUtil.stringColor("#333333")),
          ),
        ),
        HaloButton(
          width: 188.w,
          height: 60.w,
          buttonType: HaloButtonType.outlinedButton,
          outLineWidth: ScreenUtil().setHeight(1.0),
          borderColor: !getIsHeXiao()
              ? ColorUtil.stringColor("#4679FC")
              : ColorUtil.stringColor("#999999"),
          enabled: !getIsHeXiao(),
          text: "系统发货",
          onPressed: () {
            doItemSubmit(widget.billChannelDetailDto?.warehouseTaskId);
          },
        )
      ],
    );
  }

  Widget buildTripartiteDeliveryWidget() {
    return HaloContainer(
        direction: Axis.vertical,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          HaloContainer(
            color: ColorUtil.stringColor("#f4f4f4"),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            border: Border(
                bottom: BorderSide(width: 1.w, color: AppColors.dividerColor)),
            direction: Axis.vertical,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                StringRes.logisticsInfo.getText(context),
                style: TextStyle(
                    color: AppColors.normalTextColor,
                    fontSize: 22.sp,
                    fontWeight: FontWeight.w500),
              ),
              HaloContainer(
                direction: Axis.vertical,
                children: [
                  HaloContainer(
                    direction: Axis.horizontal,
                    children: [
                      Expanded(
                          child: _buildLab(
                              "${StringRes.LOGISTICS_NAME_STR.getText(context)}${(widget.billChannelDetailDto?.freightInfo?.templateName ?? "")}")),
                      Expanded(
                          child: _buildLab(
                              "${StringRes.CAPACITY_CALL_STATUS.getText(context)}${getCallState(widget.billChannelDetailDto?.yunliStatus)}")),
                      Expanded(
                          child: _buildLab(
                              "${StringRes.LOGISTICS_STATUS_STR.getText(context)}${getFeieghtState(widget.billChannelDetailDto?.yunliStatus)}")),
                    ],
                  ),
                  HaloContainer(
                    children: [
                      Expanded(
                          child: _buildLab(
                              "${StringRes.SHIPPING_FEE.getText(context)}${(widget.billChannelDetailDto?.freightInfo?.inputFreightFee ?? "")}")),
                      Expanded(
                          child: _buildLab(
                              "${StringRes.FREIGH_TIPS.getText(context)}${(widget.billChannelDetailDto?.freightInfo?.insureTotal ?? "")}")),
                      Expanded(
                          child: _buildLab(
                              "${StringRes.LOGISTICS_DISPATCHER_NAME.getText(context)}${widget.billChannelDetailDto?.platformDispatcherName ?? ""}")),
                    ],
                  ),
                  HaloContainer(
                    children: [
                      Expanded(
                          child: _buildLab(
                              "${StringRes.LOGISTICS_DISPATCHER_MOBILE.getText(context)}${(widget.billChannelDetailDto?.platformDispatcherMobile ?? "")}")),
                      Expanded(
                          child: _buildLab(
                              "${StringRes.DELIVERY_CODE.getText(context)}${(widget.billChannelDetailDto?.pickupCode ?? "")}")),
                      Expanded(child: Container())
                    ],
                  )
                ],
              ),
            ],
          ),
          HaloContainer(
            color: ColorUtil.stringColor("#f4f4f4"),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            children: [
              Expanded(
                  child: Text(
                      "${StringRes.senderName.getText(context)}:${(widget.billChannelDetailDto?.freightInfo?.deliverEtypeName ?? "")}",
                      style: TextStyle(
                          color: AppColors.normalTextColor,
                          fontSize: 22.sp,
                          fontWeight: FontWeight.w400))),
              Expanded(
                  child: Text(
                      "${StringRes.senderDate.getText(context)}:${(widget.billChannelDetailDto?.freightInfo?.deliverTime ?? "")}",
                      style: TextStyle(
                          color: AppColors.normalTextColor,
                          fontSize: 22.sp,
                          fontWeight: FontWeight.w400))),
              Expanded(child: Container()),
            ],
          ),
        ]);
  }

  Widget _buildBottomButton() {
    //TODO 因仓储需求延期，屏蔽三方运力
    // if (widget.billChannelDetailDto!.selfDeliveryMode ==
    //     "INTRA_CITY_DISTRIBUTION") {
    //   return HaloContainer(
    //     border:
    //         Border(top: BorderSide(width: 1.w, color: AppColors.dividerColor)),
    //     margin: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.h),
    //     direction: Axis.horizontal,
    //     children: [
    //       Expanded(
    //           child: HaloContainer(
    //         direction: Axis.horizontal,
    //         children: [
    //           HaloButton(
    //             text: StringRes.YUNLI_GET_PLATFORM_LIST.getText(context),
    //             height: 48.h,
    //             outLineWidth: 1.w,
    //             borderColor: ColorUtil.stringColor("#4679FC"),
    //             textColor: ColorUtil.stringColor("#4679FC"),
    //             buttonType: HaloButtonType.outlinedButton,
    //             onPressed: () {
    //               _getYunLi();
    //             },
    //           ),
    //           SizedBox(
    //             width: 28.w,
    //           ),
    //           HaloContainer(
    //             key: _yunOperatorGlobalKey,
    //             border: Border.all(color: ColorUtil.stringColor("#4679FC")),
    //             crossAxisAlignment: CrossAxisAlignment.center,
    //             padding: EdgeInsets.symmetric(horizontal: 16.w),
    //             borderRadius: BorderRadius.all(Radius.circular(8.w)),
    //             children: [
    //               GestureDetector(
    //                   onTap: () {
    //                     _callYunLI();
    //                   },
    //                   child: Text(
    //                     StringRes.YUNLI_CALL.getText(context),
    //                     style: TextStyle(
    //                         color: ColorUtil.stringColor("#4679FC"),
    //                         fontSize: 26.sp),
    //                   )),
    //               GestureDetector(
    //                   onTap: () {
    //                     _showYunLiOperator();
    //                   },
    //                   child: Container(
    //                       height: 48.h,
    //                       margin: EdgeInsets.only(left: 12.w),
    //                       padding: EdgeInsets.only(left: 20.w, right: 8.w),
    //                       decoration: BoxDecoration(
    //                           border: Border(
    //                               left: BorderSide(
    //                                   color:
    //                                       ColorUtil.stringColor("#4679FC")))),
    //                       child: IconFont(
    //                         IconNames.unfold,
    //                         color: "#4679FC",
    //                         size: 24.w,
    //                       )))
    //             ],
    //           ),
    //         ],
    //       )),
    //       HaloButton(
    //         text: "打印小票",
    //         height: 48.h,
    //         outLineWidth: 1.w,
    //         borderColor: AppColors.txtBorderColor,
    //         textColor: AppColors.secondTextColor,
    //         buttonType: HaloButtonType.outlinedButton,
    //         onPressed: () {
    //           _doPrintData();
    //         },
    //       ),
    //     ],
    //   );
    // }
    return Container();
  }

  String getCallState(int? state) {
    if (state == 0) {
      return "待呼叫";
    }
    return "已呼叫";
  }

  String getFeieghtState(int? state) {
    if (state == 0) {
      return "未配送";
    }
    if (state == 1) {
      return "待配送";
    }
    if (state == 2) {
      return "待取货";
    }
    if (state == 3) {
      return "配送中";
    }

    return "已完成";
  }

  _getYunLi() async {
    PlatformInfoQueryParam param = PlatformInfoQueryParam();
    param.vchcode = widget.billChannelDetailDto?.vchcode;
    param.warehouseTaskId = widget.billChannelDetailDto?.warehouseTaskId;
    param.printTemplateType = "TB_YL";
    List<YunLiTemplate> list =
        await YunLiModel.getYunLiPlatFormList(context, param.toJson());

    if (context.mounted) {
      if (list.isEmpty) {
        return;
      }
      HaloDialog(context,
          child: YunLiPlatFormDialog(
              billChannelDetailDto: widget.billChannelDetailDto,
              yunliPlatfomList: list,
              onSelected: (select) {
                setState(() {
                  widget.billChannelDetailDto?.freightInfo?.templateName =
                      select?.ylName ?? "";
                });
              })).show();
    }
  }

//呼叫运力
  _callYunLI() async {
    PlatformInfoQueryParam param = PlatformInfoQueryParam();
    param.vchcode = widget.billChannelDetailDto?.vchcode;
    param.warehouseTaskId = widget.billChannelDetailDto?.warehouseTaskId;
    ResponseModel res =
        await YunLiModel.doSubmitYunLiTemplate(context, param.toJson());
    if (context.mounted) {
      if (res.code == 200) {
        HaloToast.show(context, msg: "呼叫运力成功");
        setState(() {
          widget.billChannelDetailDto?.yunliStatus = 1;
        });
      } else {
        HaloToast.show(context, msg: res.message);
      }
    }
  }

//运力操作
  _showYunLiOperator() {
    HaloPopWindow().show(_yunOperatorGlobalKey,
        gravity: PopWindowGravity.top,
        backgroundColor: Colors.transparent,
        intervalTop: -110,
        intervalLeft: 30,
        child: Container(
          width: 160.w,
          height: 200.h,
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppColors.dividerColor),
              borderRadius: BorderRadius.all(Radius.circular(8.w))),
          child: ListView.builder(
              padding: const EdgeInsets.all(0),
              itemCount: yunLiOperator.length,
              itemBuilder: (buildContext, index) {
                return GestureDetector(
                  onTap: () {
                    _selectYunLiOperator(index);
                  },
                  child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.all(14.w),
                      child: Text(yunLiOperator[index])),
                );
              }),
        ));
  }

  _selectYunLiOperator(int index) {
    HaloPopWindow().disMiss();
    //取消呼叫
    if (index == 0) {
      HaloDialog(context,
          child: YunLiCancelCallDialog(
            billChannelDetailDto: widget.billChannelDetailDto,
            onSuccess: () {
              setState(() {
                widget.billChannelDetailDto?.yunliStatus = 0;
              });
            },
          )).show();
    } else if (index == 1) {
      //二次呼叫（二次呼叫与呼叫运力调用同一个接口处理）
      _callYunLI();
    } else if (index == 2) {
      //添加消费
      HaloDialog(context,
          child: YunLiAddTipDialog(
              billChannelDetailDto: widget.billChannelDetailDto,
              onSuccess: (price) {
                setState(() {
                  widget.billChannelDetailDto?.freightInfo?.insureTotal =
                      num.parse(price ?? "0");
                });
              })).show();
    }
  }

  _buildLab(String title) {
    return HaloPosLabel(
      title,
      textStyle: TextStyle(
          fontSize: 20.sp,
          color: ColorUtil.stringColor("#5d5d5d"),
          fontWeight: FontWeight.w400),
    );
  }

  _buildTopTitle(String title, {flex = 1}) {
    return Expanded(
      flex: flex,
      child: HaloPosLabel(
        title,
        textAlign: TextAlign.center,
        textStyle: TextStyle(
          fontSize: 24.sp,
          fontFamily: 'PingFangSC-Medium',
          fontWeight: FontWeight.w600,
          color: ColorUtil.stringColor("#333333"),
        ),
      ),
    );
  }

  _buildContentTitle(String title,
      {flex = 1, isCombo = false, GoodsDetailDto? outDetail}) {
    return Expanded(
      flex: flex,
      child: HaloContainer(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Visibility(
              visible: isCombo,
              child: Container(
                width: 28.w,
                height: 28.w,
                margin: EdgeInsets.only(right: 8.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                        color: const Color(0xFFCA12BC),
                        width: 1.h,
                        style: BorderStyle.solid),
                    borderRadius: const BorderRadius.all(Radius.circular(3))),
                child: HaloPosLabel("套",
                    textStyle: TextStyle(
                        color: const Color(0xFFCA12BC),
                        decoration: TextDecoration.none,
                        fontSize: 20.sp,
                        fontWeight: FontWeight.normal)),
              )),
          Flexible(
            child:PtypeNoteRichText(goodsDetailDto: outDetail??GoodsDetailDto(),isOnlyType: true, textStyle: TextStyle(
                fontSize: 22.sp,
                color: ColorUtil.stringColor("#333333")),
            )
          ),
          Visibility(
            visible: isCombo,
            child: Flexible(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    outDetail?.isShowCombo = !outDetail.isShowCombo!;
                  });
                },
                child: HaloPosLabel(
                  (outDetail?.isShowCombo ?? false) ? " 收起" : " 展开",
                  textAlign: TextAlign.center,
                  textStyle: TextStyle(
                    fontSize: 22.sp,
                    color: AppColors.accentColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// WAIT_SELLER_SEND_GOODS 待核销
  /// TRADE_FINISHED 已核销
  String _getFreightSyncState() {
    if (widget.billChannelDetailDto?.state?.freightSyncState == "NONE" ||
        widget.billChannelDetailDto?.state?.freightSyncState == "FAIL") {
      return "待核销";
    } else if (widget.billChannelDetailDto?.state?.freightSyncState ==
        "SUCCESS") {
      return "已核销";
    } else {
      return "无需核销";
    }
  }

  void showChannelScanDialog(BuildContext context, {String? warehouseTaskId}) {
    DialogUtil.showAlertDialog(context,
        child: BillChannelScan(callback: (String tradeCode) {
          BillChannelModel.verifyWithOrder(
              context, tradeCode,warehouseTaskId ?? "", true)
              .then((value) {
            if (value == true) {
              setState(() {
                widget.refreshCallBack();
              });
            }
          });
        }));
  }

  doItemSubmit(String? warehouseTaskId) {
    BillChannelModel.doYunLiLocalSend(context, warehouseTaskId ?? "")
        .then((value) {
      if (value == true) {
        widget.refreshCallBack();
      }
    });
  }

  _doPrintData() {
    if (null == widget.billChannelDetailDto) {
      HaloToast.show(context, msg: "请获取单据后再执行打印");
    }
    PrintTool.printChannelBill(context, widget.billChannelDetailDto!);
  }

  static String getSumQty(List<GoodsDetailDto> details) {
    double subQty = 0;
    for (int i = 0; i < details.length; i++) {
      ///非套餐明细才需要加数量
      GoodsDetailDto outDetail = details[i];
      if (outDetail.comboRow == false && BillTool.comboDetailRow(outDetail)) {
       ///套餐行
      }else{
        subQty += details[i].unitQty.toDouble();
      }
    }
    return subQty.toString();
  }
}

class _EtypeName extends GetxController {
  var etypeFullanme = "".obs;

  void setEtypeFullanme(String name) {
    etypeFullanme.value = name;
  }
}
