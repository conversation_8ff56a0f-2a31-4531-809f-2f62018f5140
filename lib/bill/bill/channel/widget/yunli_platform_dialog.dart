import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/haloui.dart';

import '../../../../common/string_res.dart';
import '../../../../common/style/app_color_helper.dart';
import '../../../../common/style/app_colors.dart';
import '../../../../iconfont/icon_font.dart';
import '../../../entity/yunli/yunli_template.dart';
import '../model/bill_channel_detail.dart';
import '../model/bill_yunlli_model.dart';

///
///@ClassName: yunli_platform_dialog
///@Description:
///@Author: tanglan
///@Date: 2023/10/26
class YunLiPlatFormDialog extends StatefulWidget {
  final BillChannelDetailDto? billChannelDetailDto;
  final List<YunLiTemplate>? yunliPlatfomList;
  final Function(YunLiTemplate?)? onSelected;

  const YunLiPlatFormDialog(
      {Key? key,
      this.billChannelDetailDto,
      this.yunliPlatfomList,
      this.onSelected})
      : super(key: key);

  @override
  State<YunLiPlatFormDialog> createState() {
    return _YunLiPlatFormDialogState();
  }
}

class _YunLiPlatFormDialogState extends State<YunLiPlatFormDialog> {
  final _YunliPlatFormController _yunliPlatformController =
      Get.put(_YunliPlatFormController());

  YunLiTemplate? selectYunLi;
  final GlobalKey _yunliGlobalKey = GlobalKey(); //门店选择框key

  @override
  void initState() {
    selectYunLi = widget.yunliPlatfomList!.first;
    super.initState();
  }

  @override
  void dispose() {
    HaloPopWindow().disMiss();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
            child: HaloContainer(
          direction: Axis.vertical,
          color: AppColors.cardColor,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          width: 450.w,
          height: 240.w,
          children: [
            Container(
              padding: EdgeInsets.all(8.w),
              child: Text(StringRes.YUNLI_GET_PLATFORM_LIST.getText(context),
                  style: TextStyle(
                      color: AppColors.normalTextColor,
                      fontSize: 24.sp,
                      decoration: TextDecoration.none)),
            ),
            HaloContainer(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.w),
                crossAxisAlignment: CrossAxisAlignment.center,
                border: Border(
                    top: BorderSide(color: AppColors.dividerColor, width: 2.w)),
                children: [
                  Text(
                    StringRes.LOGISTICS_NAME_STR.getText(context),
                    style: TextStyle(
                        fontSize: 20.sp,
                        color: AppColors.normalTextColor,
                        decoration: TextDecoration.none),
                  ),
                  Expanded(
                      child: GestureDetector(
                          onTap: () {
                            _showYunliPlatformWindow(context);
                          },
                          behavior: HitTestBehavior.opaque,
                          child: HaloContainer(
                              key: _yunliGlobalKey,
                              mainAxisSize: MainAxisSize.max,
                              padding: EdgeInsets.symmetric(horizontal: 24.w),
                              height: 48.w,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(6.w)),
                              border: Border.all(color: AppColors.borderColor),
                              children: [
                                Expanded(
                                    child: Obx(() => Text(
                                          _yunliPlatformController
                                              .yunliName.value,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                              color: AppColorHelper(context)
                                                  .getTitleBoldTextColor(),
                                              fontWeight: FontWeight.w500,
                                              fontSize: 26.sp),
                                        ))),
                                SizedBox(
                                  width: 16.w,
                                ),
                                IconFont(
                                  IconNames.guolvxiala,
                                  size: 12.w,
                                )
                              ])))
                ]),
            HaloContainer(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  HaloButton(
                    height: 48.w,
                    text: "确定",
                    onPressed: () {
                      doSave(context);
                    },
                  ),
                  SizedBox(
                    width: 28.w,
                  ),
                  HaloButton(
                    buttonType: HaloButtonType.outlinedButton,
                    outLineWidth: 2.w,
                    foregroundColor: AppColors.normalTextColor,
                    borderColor: AppColors.btnBorderColor,
                    height: 48.w,
                    text: "取消",
                    onPressed: () {
                      NavigateUtil.pop(context);
                    },
                  ),
                ])
          ],
        )));
  }

  _showYunliPlatformWindow(BuildContext context) {
    HaloPopWindow().show(_yunliGlobalKey,
        gravity: PopWindowGravity.bottom,
        backgroundColor: Colors.transparent,
        // intervalTop: 10,
        child: Container(
          width: 300.w,
          height: widget.yunliPlatfomList!.length * 65.h,
          constraints: BoxConstraints(maxHeight: 500.w),
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppColors.dividerColor),
              borderRadius: BorderRadius.all(Radius.circular(8.w))),
          child: ListView.builder(
              padding: const EdgeInsets.all(0),
              itemCount: widget.yunliPlatfomList!.length,
              itemBuilder: (buildContext, index) {
                return _ItemWidget(
                  name: widget.yunliPlatfomList![index].ylName,
                  index: index,
                  onSelectItem: (selectIndex) {
                    _yunliPlatformController.setYunliName(
                        widget.yunliPlatfomList?[selectIndex ?? 0].ylName);
                    selectYunLi = widget.yunliPlatfomList?[selectIndex ?? 0];
                    HaloPopWindow().disMiss();
                  },
                );
              }),
        ));
  }

  doSave(BuildContext context) async {
    await YunLiModel.doSaveYunLiTemplate(context, selectYunLi?.toJson());
    if (null != widget.onSelected) {
      widget.onSelected!(selectYunLi);
    }
    if (context.mounted) {
      NavigateUtil.pop(context);
    }
  }
}

class _ItemWidget extends StatelessWidget {
  final Function(int?)? onSelectItem;
  final String? name;
  final int? index;

  const _ItemWidget({this.name, this.index, this.onSelectItem});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          if (null != onSelectItem) {
            onSelectItem!(index);
          }
        },
        child: Container(
          height: 60.w,
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          decoration: const BoxDecoration(
              border:
                  Border(bottom: BorderSide(color: AppColors.dividerColor))),
          child: Text(
            name ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: 24.sp,
                color: AppColorHelper(context).getTitleBoldTextColor()),
          ),
        ));
  }
}

class _YunliPlatFormController extends GetxController {
  var yunliName = "".obs;

  void setYunliName(String? name) {
    yunliName.value = name ?? "";
  }
}
