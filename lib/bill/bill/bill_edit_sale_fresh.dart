import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import 'package:haloui/haloui.dart';

import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/tool/bill_tool.dart';
import '../../bill/tool/scale_tool.dart';
import '../../bill/tool/scan_tool.dart';
import '../../bill/widget/bill_edit_sale_fresh_table.dart';
import '../../bill/widget/bill_keyboard_page.dart';
import '../../bill/widget/mixin/sale_business_mixin.dart';
import '../../bill/widget/mixin/select_vip_mixin.dart';
import '../../common/keyboard_hidden.dart';
import '../../common/style/app_colors.dart';
import '../../common/tool/payment_orders_mixin.dart';
import '../../common/tool/sp_tool.dart';
import '../../enum/bill_type.dart';
import '../../offline/offline_tool.dart';
import '../../vip/utils/svip_util.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/halo_pos_label.dart';
import '../tool/promotion/promotion.dart';
import '../tool/promotion/score.dart';
import '../widget/mixin/bill_mixin.dart';
import '../widget/mixin/hotkey_mixin.dart';

/// 创建时间：2/2/23
/// 作者：xiaotiaochong
/// 描述：

class BillEditSaleFreshPage extends BaseStatefulPage {
  final Function() submitCallback;

  const BillEditSaleFreshPage({Key? key, required this.submitCallback})
      : super(key: key, showAppBar: false, showEndDrawer: true);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      BillEditSaleFreshPageState();
}

class BillEditSaleFreshPageState
    extends BaseStatefulPageState<BillEditSaleFreshPage>
    with
        PaymentOrdersMixin,
        SelectVipMixin<BillEditSaleFreshPage>,
        BillMixin<BillEditSaleFreshPage>,
        SaleBusinessMixin<BillEditSaleFreshPage>,
        HotkeyMixin<BillEditSaleFreshPage> {
  final double singleButtonHeight = 50;
  final double singleButtonFontSize = 22;

  ///覆写父mixin中的焦点
  @override
  final KeyboardHiddenFocusNode searchFocusNode = KeyboardHiddenFocusNode();

  final ScaleController scaleController = Get.put(ScaleController());

  @override
  Future<void> onInitState() {
    addVipListener();
    ScaleTool.addScale(context, scaleController);
    ScaleTool.onConnection(scaleController);
    ScaleTool.onWeight(scaleController);
    ScaleTool.onState(scaleController);
    setScanPaySuccess();
    return loadData();
  }

  @override
  void dispose() {
    ScaleTool.removeScale();
    super.dispose();
  }

  @override
  int getLeftFlex() {
    return 1;
  }

  @override
  int getRightFlex() {
    return 2;
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    //无开单权限
    if (!hasSalePermission) {
      return Center(
        child: Text("此账号没有${getActionBarTitle()}权限"),
      );
    }

    return HaloContainer(
      color: Colors.white,
      children: [
        Expanded(
          child: Column(
            children: [
              //电子秤称重数据
              HaloContainer(
                visible: SpTool.getSetting().openFreshWight,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                color: AppColors.weightBackgroundFontColor,
                children: [
                  _buildWeight(),
                  Container(
                    padding: EdgeInsets.only(top: 18.h),
                    child: HaloPosLabel(
                      "kg",
                      textStyle: TextStyle(
                          fontSize: 20.sp, fontWeight: FontWeight.bold),
                    ),
                  ),
                  const Expanded(child: SizedBox()),
                  HaloContainer(
                    direction: Axis.vertical,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [_buildScaleState(), _buildScaleWeightState()],
                  )
                ],
              ),

              //已选商品列表
              Expanded(
                child: Container(
                  color: Colors.white,
                  margin: EdgeInsets.only(bottom: 2.w),
                  child: BillEditSaleFreshTable(
                    indexScrollerController: indexScrollerController,
                    goodsBillDto: goodsBillDto,
                    billType: BillType.SaleBill,
                    dataChangeCallBack:
                        (GoodsBillDto goodsBill, bool resetFocus) {
                      onGoodsListDataChange(goodsBill, resetFocus);
                    },
                    onSelectPromotionGift: (String promotionGift) {
                      selectPromotionGift(promotionGift);
                    },
                  ),
                ),
              ),

              //订单满减促销提示
              buildBillPromotionView() ?? const SizedBox(),
              //底部会员、优惠、收银按钮
              HaloContainer(
                color: Colors.white,
                direction: Axis.vertical,
                children: [
                  Container(
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                      decoration: BoxDecoration(
                          border: Border.symmetric(
                              horizontal: BorderSide(
                                  color: AppColors.pageBackgroundColor,
                                  width: 2.h))),
                      child: buildVipInfoView(context,
                          showNewRights: showGiftCoupon, tapNewRights: () {
                        checkGiftCoupon();
                      })),
                  _buildLeftBottom()
                ],
              ),
            ],
          ),
        ),
        _buildButtons(),
      ],
    );

    // return Container(
    //   color: backgroundColor,
    //   child: Row(
    //     children: [
    //
    //     ],
    //   ),
    // );
  }

  @override
  Widget? buildBillPromotionView({bool showCorner = false}) {
    Widget? result = super.buildBillPromotionView();
    if(result != null) {
      result = SizedBox(
        height: 60.h,
        width: double.infinity,
        child: result,
      );
    }
    return result;
  }

  _buildWeight() {
    return GetBuilder<ScaleController>(
      id: ScaleTool.weight,
      tag: ScaleTool.scaleTag,
      init: scaleController,
      builder: (_) {
        return Obx(
          () => HaloPosLabel(
            "${scaleController.weight}",
            textStyle:
                TextStyle(fontSize: 70.sp, fontWeight: FontWeight.normal),
          ),
        );
      },
    );
  }

  _buildScaleState() {
    bool connection = false;
    String title = "";
    Color color = Colors.transparent;
    return GetBuilder<ScaleController>(
      id: ScaleTool.connection,
      tag: ScaleTool.scaleTag,
      init: scaleController,
      builder: (_) {
        return Obx(() {
          if (scaleController.isConnected.value &&
              !scaleController.overload.value &&
              !scaleController.clearZeroErr.value &&
              !scaleController.calibrationErr.value) {
            connection = true;
          } else {
            connection = false;
          }
          title = connection ? "正常" : "异常";
          color = connection ? Colors.green : Colors.red;
          return _buildIndicatorLightView(title, color);
        });
      },
    );
  }

  _buildScaleWeightState() {
    bool normal = false;
    String title = "";
    Color color = Colors.transparent;
    return GetBuilder<ScaleController>(
      id: ScaleTool.state,
      tag: ScaleTool.scaleTag,
      init: scaleController,
      builder: (_) {
        return Obx(() {
          normal = scaleController.isStable.value;
          title = normal ? "稳定" : "浮动";
          color = normal ? Colors.green : Colors.yellow;
          return _buildIndicatorLightView(title, color);
        });
      },
    );
  }

  _buildIndicatorLightView(String text, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 16.h,
          height: 16.h,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.all(Radius.circular(20.sp)),
          ),
        ),
        SizedBox(
          width: 10.w,
        ),
        HaloPosLabel(
          text,
          textStyle: TextStyle(
              fontSize: AppPosSize.secondaryTitleFontSize.sp,
              fontWeight: FontWeight.normal),
        ),
      ],
    );
  }

  _buildLeftBottom() {
    return HaloContainer(
      direction: Axis.vertical,
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 20.w),
      children: [
        ///优惠信息
        HaloContainer(
          margin: EdgeInsets.only(bottom: 12.w),
          children: [
            Text(
              "优惠:",
              style: TextStyle(
                  color: AppColors.normalFontColor,
                  fontSize: AppPosSize.secondaryTitleFontSize.sp),
            ),
            Expanded(
              child: Text(
                "¥$sumAllPreferential",
                style: TextStyle(
                    color: AppColors.totalFontColor,
                    fontSize: AppPosSize.secondaryTitleFontSize.sp),
              ),
            ),
            buildPreferentialView()
          ],
        ),

        ///构建提交单据按钮，收银
        buildTotalPriceView(context),
      ],
    );

    // return Container(
    //     color: Colors.white,
    //     child: Column(
    //       children: [
    //
    //       ],
    //     ));
  }

  ///打印预结单按钮
  Widget _buildPrintPreview() {
    return buildSingleButton(
      "打印预结单",
      height: singleButtonHeight.h,
      fontSize: singleButtonFontSize.sp,
      action: printPreview,
    );
  }

  _buildScanPayView(List<Widget> bottomWidget) {
    if (OffLineTool().isOfflineLogin) {
      return;
    }
    bottomWidget.add(buildSingleButton("扫码支付",
        height: singleButtonHeight.h,
        fontSize: AppPosSize.totalFontSize.sp, action: () {
      quickScanPayBill();
    }));
    bottomWidget.add(_buildDivisionView());
  }

  _buildManualPreferentialView(List<Widget> bottomWidget,
      {bool division = false}) {
    if (manualPreferentialView) {
      bottomWidget.add(buildSingleButton("总额优惠",
          height: singleButtonHeight.h,
          fontSize: AppPosSize.totalFontSize.sp, action: () {
        showManualPreferentialDialog();
      }));
      if (division) {
        bottomWidget.add(_buildDivisionView());
      }
    }
  }

  ///整单折扣
  _buildBillDiscount(List<Widget> bottomWidget) {
    if (billDiscountPermission) {
      bottomWidget.add(buildSingleButton("整单折扣",
          height: singleButtonHeight.h,
          fontSize: AppPosSize.totalFontSize.sp, action: () {
        showBillDiscountDialog();
      }));
      bottomWidget.add(_buildDivisionView());
    }
  }

  _buildDivisionView() => SizedBox(height: 12.h);

  ///构建中间的按钮操作栏
  Widget _buildButtons() {
    List<Widget> bottomWidget = [];
    //预结单按钮
    bottomWidget.add(_buildPrintPreview());
    bottomWidget.add(_buildDivisionView());
    if (vipInfo != null) {
      bool vipExpired = isVipExpired(vipInfo!.vip!.validDate,
          vipType: vipInfo!.level!.vipType == true ? 1 : 0);
      bool allowScore = scoreConfiguration != null
          ? ScoreDiscountUtil.allowScoreDiscount(
              goodsBillDto, scoreConfiguration!)
          : false;
      _buildScanPayView(bottomWidget);
      bottomWidget.add(buildSingleButton("优惠券",
          height: singleButtonHeight.h,
          fontSize: AppPosSize.totalFontSize.sp,
          enable: !vipExpired,
          tips: couponCardCount, action: () {
        showCouponCardView();
      }));
      bottomWidget.add(_buildDivisionView());
      _buildBillDiscount(bottomWidget);
      _buildManualPreferentialView(bottomWidget, division: true);
      bottomWidget.add(buildSingleButton("积分抵现",
          height: singleButtonHeight.h,
          fontSize: AppPosSize.totalFontSize.sp,
          enable: !vipExpired && allowScore, action: () {
        showScoreView();
      }));
    } else {
      _buildScanPayView(bottomWidget);
      _buildBillDiscount(bottomWidget);
      _buildManualPreferentialView(bottomWidget);
    }

    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      direction: Axis.vertical,
      width: 150.w,
      border: Border.symmetric(
          vertical:
              BorderSide(color: AppColors.pageBackgroundColor, width: 2.w)),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        HaloContainer(
          direction: Axis.vertical,
          children: [
            buildSingleButton("清空",
                height: singleButtonHeight.h,
                fontSize: AppPosSize.totalFontSize.sp, action: () {
              doClear();
            }),
            _buildDivisionView(),
            buildSingleButton(draftString,
                height: singleButtonHeight.h,
                fontSize: AppPosSize.totalFontSize.sp, action: () {
              doDraft();
            }),
            _buildDivisionView(),
            // buildSingleButton("退货",
            //     height: singleButtonHeight.h,
            //     fontSize: singleButtonFontSize.sp, action: () {
            //   doSaleBack();
            // }),
            // _buildDivisionView(),
            buildSingleButton("备注",
                height: singleButtonHeight.h,
                fontSize: AppPosSize.totalFontSize.sp,
                action: memoBill),
            _buildDivisionView(),
            buildSingleButton("打印上单",
                height: singleButtonHeight.h,
                fontSize: AppPosSize.totalFontSize.sp, action: () {
              doBeforeBill();
            }),
            _buildDivisionView(),
            buildChoseGiftButton(),
          ],
        ),
        HaloContainer(
          direction: Axis.vertical,
          children: bottomWidget,
        )
      ],
    );
  }

  @override
  Widget buildRightBody(BuildContext context) {
    return BillGoodsAndKeyboardWidget(
        searchByPtype: SpTool.getSetting().searchByPtype,
        focusNode: searchFocusNode,
        goodsClickCallback: ((goods) {
          ScanTool.handleScanResult(context, goods, goodsList, billType)
              .then((list) {
            if (list != null) {
              String vchType = BillTypeData[billType]!;
              for (var element in list) {
                element.vchtype = vchType;
                if (element.comboRow == false &&
                    !BillTool.comboDetailRow(element)) {
                  ScaleTool.calculateUnityQty(element);
                }
              }
              handleSearchResult(list);
            }
          });
        }));
  }

  @override
  String getActionBarTitle() {
    return "开单";
  }

  ///选择赠品按钮
  Widget buildChoseGiftButton() {
    Widget choseGift;
    if (PromotionUtil.canSelectGift(goodsBillDto)) {
      choseGift = buildSingleButton("选择赠品",
          foregroundColor: AppColors.accentColor,
          height: singleButtonHeight.h,
          fontSize: AppPosSize.totalFontSize.sp, action: () {
        selectPromotionGift();
      });
    } else {
      choseGift = buildSingleButton("选择赠品",
          enable: false,
          foregroundColor: AppColors.accentColor.withOpacity(0.3),
          backgroundColor: Colors.white,
          height: singleButtonHeight.h,
          fontSize: AppPosSize.totalFontSize.sp);
    }
    return choseGift;
  }

  ///构建提交单据按钮，收银
  Widget buildTotalPriceView(BuildContext context) {
    return GestureDetector(
      child: Container(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          decoration: const BoxDecoration(
            color: AppColors.accentColor,
            borderRadius: BorderRadius.all(Radius.circular(6.0)),
          ),
          alignment: Alignment.center,
          child: buildTotalPriceDetailView()),
      onTap: () {
        doSubmit(context);
      },
    );
  }

  @override
  Widget buildTotalPriceDetailViewCustomer() {
    return Row(
      children: [
        Padding(
          padding: EdgeInsets.only(left: 20.w),
          child: Text(
            billTotalTitle,
            textAlign: TextAlign.center,
            maxLines: 1,
            style: TextStyle(
                color: Colors.white,
                decoration: TextDecoration.none,
                fontSize: 32.sp,
                fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: Container(
              alignment: Alignment.centerRight,
              margin: EdgeInsets.only(left: 10.w, right: 20.w),
              child: HaloPosLabel(
                "¥$sumAllFinalTotal",
                textStyle: TextStyle(
                    color: Colors.white,
                    decoration: TextDecoration.none,
                    fontSize: 32.sp,
                    fontWeight: FontWeight.bold),
              )),
        ),
      ],
    );
  }
}
