import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

import '../../bill/entity/bill_sale_bill_detail_dto.dart';
import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/entity/order_bill_item_entity.dart';
import '../../bill/entity/payment_dto.dart';
import '../../bill/entity/preferential_dto.dart';
import '../../bill/tool/bill_tool.dart';
import '../../bill/tool/decimal_display_helper.dart';
import '../../bill/widget/bill_edit_sale_table.dart';
import '../../bill/widget/bill_edit_sale_table_select.dart';
import '../../bill/widget/mixin/sale_business_mixin.dart';
import '../../bill/widget/mixin/select_vip_mixin.dart';
import '../../common/style/app_pos_size.dart';
import '../../common/tool/payment_orders_mixin.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/tool/system_config_tool.dart';
import '../../enum/bill_decimal_type.dart';
import '../../enum/bill_type.dart';
import '../../login/entity/store/store_info.dart';
import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/dotted_line.dart';
import '../../widgets/halo_pos_label.dart';
import '../entity/bill_vip_assert_change.dart';
import '../settlement/widget/preferential_detail_dialog.dart';
import '../tool/goods_tool.dart';
import '../tool/index_scroll_controller.dart';
import '../tool/promotion/discount.dart';
import '../tool/promotion/preferential.dart';
import '../widget/mixin/bill_mixin.dart';
import '../widget/mixin/sale_back_mixin.dart';
import '../widget/mixin/sale_normal_mixin.dart';

///换货单
class BillEditSaleChangePage extends BaseStatefulPage {
  final OrderBillItem saleBill;

  const BillEditSaleChangePage({Key? key, required this.saleBill})
    : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() {
    return _BillEditSaleChangePageState();
  }
}

class _BillEditSaleChangePageState
    extends BaseStatefulPageState<BillEditSaleChangePage>
    with
        PaymentOrdersMixin,
        SelectVipMixin<BillEditSaleChangePage>,
        BillMixin<BillEditSaleChangePage>,
        SaleBusinessMixin<BillEditSaleChangePage>,
        SaleNormalMixin<BillEditSaleChangePage>,
        SaleBackMixin<BillEditSaleChangePage> {
  @override
  BillType get billType => BillType.SaleChangeBill;

  @override
  OrderBillItem get selectBillItem => widget.saleBill;

  @override
  String get billTotalTitle => num.parse(sumAllFinalTotal) >= 0 ? "收银" : "退款";

  bool isExtend = true;

  ///总储值(本金、赠金) 和 单据总金额 比例
  double storedProportion = 0;

  BackSumAllTotal outSumAllTotal = BackSumAllTotal();
  BackSumAllTotal inSumAllTotal = BackSumAllTotal();

  @override
  String getActionBarTitle() => "换货单";

  //region override

  ///执行打折-》促销-》优惠分摊
  @override
  void startGoodsPreferential({bool requestSearchFocus = true}) {
    DiscountUtil.startDiscount(
      bill: goodsBillDto,
      vipInfo: null,
      isVip: false,
      discountCoupon: null,
      rightsCards: null,
      onCouponNotUse: null,
    );
    startBillPreferential();
  }

  ///执行打折-》促销-》优惠分摊
  @override
  void startBillPreferential({bool requestSearchFocus = true}) {
    //执行优惠分摊
    BillPreferentialTool.startBillPreferential(
      bill: goodsBillDto,
      isVip: false,
      vipInfo: null,
      manualPreferential: manualPreferential,
      selectedCouponList: [],
      enableScore: false,
      useMaxScore: false,
      scoreUse: 0,
      scoreConfiguration: null,
      onCouponNotUse: (_) {},
      onManualPreferentialChange: (m) => manualPreferential = m,
      onScoreUseChange: (_) {},
      resetToPromoted: false,
      executePreferential: {Preferential.billPreferential},
    ); //只执行总额优惠
    //汇总单据
    PreferentialUtil.collectBill(goodsBillDto, billType: billType);
    setState(() {
      reCalcStatistic();
    });
    if (requestSearchFocus) {
      searchFocusNode.requestFocus();
    }
  }

  ///重算单据相关金额
  @override
  void reCalcStatistic() {
    Map back = BillTool.reCalcStatisticChangeBill(goodsBillDto);
    outSumAllTotal = back["outDetail"];
    inSumAllTotal = back["inDetail"];
    sumAllFinalTotal = goodsBillDto.posCurrencyBillTotal.toString();
  }

  //endregion

  //region initData

  @override
  void setPayment() {
    StoreInfo storeInfo = SpTool.getStoreInfo()!;
    goodsBillDto.payment = billDetailDto.billDetail!.payment;
    goodsBillDto.sourcePayment =
        billDetailDto.billDetail!.payment
            .map((e) => PaymentDto.fromMap(e.toJson()))
            .toList();
    PreferentialBillsBean preferentialBillsBean =
        billDetailDto.preferentialBills?.firstWhere(
          (element) => element.preferentialType == 6,
          orElse: () => PreferentialBillsBean(),
        ) ??
        PreferentialBillsBean();

    ///有储值的赠金 payment要重新计算,加回赠金
    if (preferentialBillsBean.preferentialTotal != 0) {
      PaymentDto storeMoneyPayment = PaymentDto.fromMap(
        goodsBillDto.sourcePayment
            .firstWhere(
              (element) => element.atypeId == storeInfo.storedMoneyAtypeId,
            )
            .toJson(),
      );
      goodsBillDto.sourcePayment.removeWhere(
        (element) => element.atypeId == storeInfo.storedMoneyAtypeId,
      );
      storeMoneyPayment.atypeFullName = storeInfo.storedMoneyAtypeName;
      storeMoneyPayment.atypeId = storeInfo.storedMoneyAtypeId; //储值
      if (preferentialBillsBean.id != null) {
        storeMoneyPayment
            .currencyAtypeTotal = DecimalDisplayHelper.getTotalFixed(
          (billDetailDto.billDetail!.currencyAdvanceTotal +
                  preferentialBillsBean.preferentialTotal)
              .toString(),
        );
      } else {
        storeMoneyPayment
            .currencyAtypeTotal = DecimalDisplayHelper.getTotalFixed(
          billDetailDto.billDetail!.currencyAdvanceTotal.toString(),
        );
      }
      if (storeMoneyPayment.currencyAtypeTotal != "0") {
        goodsBillDto.sourcePayment.add(storeMoneyPayment);
      }
    }
  }

  @override
  Future<VipWithLevelAssertsRightsCardDTO?> getVipData() {
    goodsBillDto.vipBillInfo = billDetailDto.vipBillInfo;
    num sumStoreTotal = billDetailDto.billDetail?.currencyAdvanceTotal ?? 0;
    for (PreferentialBillsBean element
        in billDetailDto.preferentialBills ?? []) {
      if (element.preferentialType == 6) {
        sumStoreTotal += element.preferentialTotal;
      }
    }
    //有储值消费
    if (sumStoreTotal != 0) {
      PreferentialBillsBean billsBean = billDetailDto.preferentialBills!
          .firstWhere(
            (element) => element.preferentialType == 6,
            orElse: () => PreferentialBillsBean()..preferentialTotal = 0.0,
          );
      num currencyDisedTaxedTotal = 0;
      for (var element in billDetailDto.billDetail!.outDetail) {
        if (!BillTool.comboDetailRow(element)) {
          currencyDisedTaxedTotal += element.currencyDisedTaxedTotal;
        }
      }
      if (currencyDisedTaxedTotal == 0 && billsBean.preferentialTotal == 0) {
        storedProportion = 0;
      } else {
        ///储值消费 和 总金额消费 比例
        storedProportion =
            (billDetailDto.billDetail!.currencyAdvanceTotal +
                billsBean.preferentialTotal) /
            (currencyDisedTaxedTotal + billsBean.preferentialTotal);
      }
    }
    return super.getVipData();
  }

  @override
  Future<void> onInitState() async {
    await loadData();
    await getScoreConfiguration();
  }

  //endregion

  //region view
  ///软键盘是否改变布局
  @override
  bool resizeToAvoidBottomPadding() => true;

  @override
  Widget buildTable() {
    return Container(
      color: const Color(0xFFE0E0E0),
      child: Column(
        children: [
          buildInDetailView(),
          buildDottedLine(),
          buildOutDetailView(),
        ],
      ),
    );
  }

  ///入库商品列表
  Widget buildInDetailView() {
    return Flexible(
      flex: isExtend ? 1 : 0,
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            buildSumDetailView(false),
            Visibility(
              visible: isExtend,
              child: Expanded(
                child: BillEditSaleTableSelect(
                  indexScrollerController: IndexScrollerController(),
                  billType: BillType.SaleBackBill,
                  goodsBillDto: goodsBillDto,
                  dataChangeCallBack: (
                    GoodsBillDto goodsBill,
                    bool resetFocus,
                  ) {
                    onBackGoodsListDataChange(goodsBill, resetFocus);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildOutDetailView() {
    return Flexible(
      flex: 1,
      child: Container(
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildSumDetailView(true),
            Container(
              height: 90.w,
              margin: EdgeInsets.symmetric(horizontal: 30.w),
              color: Colors.white,
              alignment: Alignment.topLeft,
              child: Row(
                children: [
                  buildSearch(),
                  SizedBox(width: 10.w),
                  buildAddGoodsProp(),
                ],
              ),
            ),
            Expanded(
              child: BillEditSaleTable(
                indexScrollerController: indexScrollerController,
                billType: BillType.SaleChangeBill,
                goodsBillDto: goodsBillDto,
                dataChangeCallBack: (GoodsBillDto goodsBill, bool resetFocus) {
                  onGoodsListDataChange(goodsBill, resetFocus);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///各项数据和收银按钮
  @override
  Widget buildBottomBody(BuildContext context) {
    if (hasSalePermission
    // && goodsBillDto.vchcode != null
    ) {
      return Column(
        children: [
          DottedLine(
            axis: Axis.horizontal,
            strokeWidth: 1.w,
            color: const Color(0xFF979797),
            dashPattern: const [3, 3],
          ),
          buildVipBottom(context),
        ],
      );
    } else {
      return Container();
    }
  }

  @override
  Widget buildVipBottom(BuildContext context) {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      color: Colors.white,
      children: [
        SizedBox(
          width: 450.w,
          child: buildVipInfoView(context, showNewRights: false),
        ),
        buildDivider(),
        Expanded(child: Container()),
        if (manualPreferentialView)
          buildSingleButton(
            "总额优惠",
            width: 160.h,
            height: 60.h,
            fontSize: 24.sp,
            action: () {
              showManualPreferentialDialog();
            },
          ),
        buildTotalPriceView(context),
      ],
    );
  }

  @override //enableClearVip （flutter不建议重写时直接写为 enableClearVip = false）
  Widget buildVipInfo(
    BuildContext context, {
    bool enableClearVip = true,
    bool showNewRights = false,
    VoidCallback? tapNewRights,
  }) {
    return super.buildVipInfo(context, enableClearVip: false);
  }

  Widget buildSumDetailView(bool isOutDetail) {
    String sumCount;
    String sumTotal;
    String sumRealTotal;
    String sumPreferential;
    if (!isOutDetail) {
      sumCount = DecimalDisplayHelper.getQtyFixed(
        inSumAllTotal.sumQty.toString(),
      );
      sumTotal = DecimalDisplayHelper.getTotalFixed(
        inSumAllTotal.sumTotal.toString(),
      );
      sumRealTotal = DecimalDisplayHelper.getTotalFixed(
        inSumAllTotal.sumPosDisedTaxedTotal.toString(),
      );
      sumPreferential =
          MathUtil.subtraction(
            inSumAllTotal.sumFinalPreferential.toString(),
            inSumAllTotal.sumGivePreferentialTotal.toString(),
          ).toString();
    } else {
      sumCount = DecimalDisplayHelper.getQtyFixed(
        outSumAllTotal.sumQty.toString(),
      );
      sumTotal = DecimalDisplayHelper.getTotalFixed(
        outSumAllTotal.sumTotal.toString(),
      );
      sumRealTotal = DecimalDisplayHelper.getTotalFixed(
        outSumAllTotal.sumPosDisedTaxedTotal.toString(),
      );
      sumPreferential =
          MathUtil.subtraction(
            outSumAllTotal.sumFinalPreferential.toString(),
            outSumAllTotal.sumGivePreferentialTotal.toString(),
          ).toString();
    }
    List<Widget> children = [
      HaloPosLabel(
        isOutDetail ? "请选择换出商品" : "请选择换入商品",
        textStyle: TextStyle(
          color: AppColors.normalFontColor,
          fontSize: AppPosSize.secondaryTitleFontSize.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
      SizedBox(width: 30.w),
      HaloPosLabel(
        "已选 $sumCount 件，原金额¥$sumTotal，实付金额¥$sumRealTotal，优惠¥$sumPreferential",
        textStyle: TextStyle(
          color: AppColors.normalFontColor,
          fontSize: AppPosSize.secondaryTitleFontSize.sp,
        ),
      ),
      if (!isOutDetail) ...[
        SizedBox(width: 30.w),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap:
              () => showDialog(
                context: context,
                builder:
                    (context) => PreferentialDetailDialog(
                      isBack: true,
                      dataSource:
                          goodsBillDto.inDetail
                              .where(
                                (goods) =>
                                    goods.unitQty > 0 &&
                                    !GoodsTool.isComboDetail(goods),
                              )
                              .toList(),
                    ),
              ),
          child: Text(
            "优惠明细 >",
            style: TextStyle(
              color: AppColors.describeFontColor,
              fontSize: AppPosSize.secondaryTitleFontSize.sp,
            ),
          ),
        ),
      ],
    ];
    if (!isOutDetail) {
      children.add(const Expanded(child: SizedBox()));
      children.add(
        GestureDetector(
          child: Container(
            width: 100.w,
            height: 80.h,
            color: Colors.transparent,
            alignment: Alignment.centerRight,
            child: Row(
              children: [
                HaloPosLabel(
                  isExtend ? "收起" : "展开",
                  textStyle: TextStyle(
                    color: AppColors.describeFontColor,
                    fontSize: AppPosSize.secondaryTitleFontSize.sp,
                  ),
                ),
                Icon(
                  isExtend
                      ? Icons.keyboard_arrow_up_rounded
                      : Icons.keyboard_arrow_down_rounded,
                  color: AppColors.describeFontColor,
                ),
              ],
            ),
          ),
          onTap: () {
            setState(() {
              isExtend = !isExtend;
            });
          },
        ),
      );
    }
    return Container(
      height: 80.w,
      width: double.maxFinite,
      alignment: Alignment.center,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Row(children: children),
    );
  }

  // ///竖直分割线
  // Widget buildDivider() {
  //   return Container(
  //     margin: EdgeInsets.symmetric(horizontal: 20.w),
  //     color: ColorUtil.stringColor("979797"),
  //     width: 1.w,
  //     height: 22.h,
  //   );
  // }

  //endregion

  //region action

  @override
  bool checkNextStep(GoodsBillDto goodsBillDto) {
    if (checkDetailIsEmpty(goodsBillDto.inDetail)) {
      HaloToast.showMsg(context, msg: "换入商品为空");
      return false;
    }
    if (checkDetailIsEmpty(goodsBillDto.outDetail)) {
      HaloToast.showMsg(context, msg: "换出商品为空");
      return false;
    }
    for (GoodsDetailDto data in goodsBillDto.inDetail) {
      if (data.currencyDisedTotal < 0) {
        HaloToast.showMsg(context, msg: "有商品价格为负");
        return false;
      }
    }
    for (GoodsDetailDto data in goodsBillDto.outDetail) {
      if (data.currencyDisedTotal < 0) {
        HaloToast.showMsg(context, msg: "有商品价格为负");
        return false;
      }
    }
    return true;
  }

  @override
  void calcVipStoreInfo(GoodsBillDto goodsBill) {
    bool allBack = true;
    bool lastSaleBackBill = true;
    //已退数量和
    Decimal sumRefundQty = Decimal.zero;
    //最大可退数量
    Decimal sumMaxQty = Decimal.zero;
    //本地退货数量
    Decimal unitQty = Decimal.zero;

    ///数据库中refundQty为基本单位，换算为辅助单位计算
    for (var element in goodsBill.inDetail) {
      sumRefundQty = MathUtil.addDec(
        sumRefundQty.toDouble(),
        element.getRefundUnitQty(),
      );
      sumMaxQty += Decimal.parse(element.originalMaxQty.toString());
      unitQty += Decimal.parse(element.unitQty.toString());
    }

    //已经存在退货数量，或者本次退货数量不等于最大可退货数量，都为非全退
    if (sumRefundQty != Decimal.zero || unitQty != sumMaxQty) {
      allBack = false;
    }

    //已退数量+本次退货数量 ！=原单总数量和，则不是最后一次退货
    if (sumMaxQty != unitQty + sumRefundQty) {
      lastSaleBackBill = false;
    }
    //获取资产变动
    VipAssertsBillDtoBean vipAssertsBillDto =
        billDetailDto.vipAssertsBillDto ?? VipAssertsBillDtoBean();
    vipAssertsBillDtoBean = VipAssertsBillDtoBean.fromMap(
      vipAssertsBillDto.toJson(),
    );
    //储值变动
    AssertsBillDetailDtoListBean storedValue =
        AssertsBillDetailDtoListBean()..typed = "1";
    //赠金变动
    AssertsBillDetailDtoListBean giveStoredValue =
        AssertsBillDetailDtoListBean()..typed = "2";

    //获取储值支付的支付信息
    PaymentDto? paymentDto = billDetailDto.billDetail!.payment.firstWhereOrNull(
      (element) => element.paywayType == 3,
    );
    if (allBack) {
      vipAssertsBillDtoBean!.assertsBillDetailDtoList.removeWhere(
        (element) => element.typed == "1" || element.typed == "2",
      );
      storedValue.qty = DecimalDisplayHelper.getTotalFixed(
        paymentDto?.currencyAtypeTotal ?? "0",
      );
      giveStoredValue.qty =
          "-${DecimalDisplayHelper.getTotalFixed(goodsBill.currencyGivePreferentialTotal.toString())}";
      for (var goodsDetail in goodsBill.inDetail) {
        if (goodsDetail.unitQty != 0) {
          goodsDetail.preferentialHelp = {
            if (goodsDetail.givePreferentialTotal != 0)
              Preferential.giftStore.name:
                  PreferentialDto()
                    ..total = goodsDetail.givePreferentialTotal.abs()
                    ..type = PreferentialType.GiftTotal.value,
          };
        }
      }
      storedValue.memo = "pos换货:全换";
      giveStoredValue.memo = "pos换货:全换";
    } else {
      vipAssertsBillDtoBean!.assertsBillDetailDtoList.clear();

      //需退储值赠金汇总
      num sumGiveStored = 0;
      //需退储值本金汇总
      num sumStored = 0;

      for (GoodsDetailDto goodsDetail in goodsBill.inDetail) {
        //退款数量为0不计算
        if (goodsDetail.unitQty == 0) {
          continue;
        }

        //需退储值金额
        num goodsStored = SystemConfigTool.doubleMultipleToDecimal(
          goodsDetail.posCurrencyDisedTaxedTotal,
          storedProportion.toDouble(),
          BillDecimalType.TOTAL,
        );

        //商品明细全部退完
        if (MathUtil.addDec(
              goodsDetail.unitQty,
              goodsDetail.getRefundUnitQty(),
            ) ==
            Decimal.parse(goodsDetail.originalMaxQty.toString())) {
          //需退赠金=明细总赠金优惠-已退明细赠金优惠
          goodsDetail.givePreferentialTotal =
              goodsDetail.saleBackHelper.remainGivePreferentialTotal();
          goodsDetail.currencyGivePreferentialTotal =
              goodsDetail.givePreferentialTotal;
        } else {
          //需退赠金=（明细退货数量/总明细数据）*明细总赠金优惠
          goodsDetail
              .givePreferentialTotal = SystemConfigTool.doubleMultipleToDecimal(
            MathUtil.divideDec(
              goodsDetail.unitQty,
              goodsDetail.originalMaxQty,
            ).toDouble(),
            goodsDetail.saleBackHelper.originalGivePreferentialTotal,
            BillDecimalType.TOTAL,
          );
          goodsDetail.currencyGivePreferentialTotal =
              goodsDetail.givePreferentialTotal;
        }

        //明细行不汇总，需排除
        if (!BillTool.comboDetailRow(goodsDetail)) {
          //需退赠金汇总
          sumGiveStored = SystemConfigTool.doubleAddToDecimal(
            sumGiveStored,
            goodsDetail.givePreferentialTotal,
            BillDecimalType.TOTAL,
          );

          //需退本金汇总：需退本金 =需退储值金额-需退赠金
          sumStored = SystemConfigTool.doubleAddToDecimal(
            sumStored,
            SystemConfigTool.doubleSubtractionToDecimal(
              goodsStored,
              goodsDetail.givePreferentialTotal,
              BillDecimalType.TOTAL,
            ),
            BillDecimalType.TOTAL,
          );
        }
        goodsDetail.preferentialHelp = {
          Preferential.giftStore.name:
              PreferentialDto()
                ..total = goodsDetail.givePreferentialTotal
                ..type = PreferentialType.GiftTotal.value,
        };
      }

      storedValue.qty = sumStored.toString();
      giveStoredValue.qty = "-${sumGiveStored.toString()}";
      storedValue.memo = "pos换货:部分换";
      giveStoredValue.memo = "pos换货:部分换";

      //最后一次退货 磨平误差信息
      if (lastSaleBackBill) {
        handleLastBack(paymentDto, storedValue, giveStoredValue);
      }
    }
    num backGiveTotal = 0;
    if (num.parse(sumAllFinalTotal) < 0) {
      //原单储值支付金额
      num originStoreTotal =
          MathUtil.addDec(
            goodsBill.originCurrencyAdvanceTotal,
            goodsBill.originalCurrencyGivePreferentialTotal,
          ).toDouble();
      if (originStoreTotal > 0) {
        //原单储值占比
        num storeRate = SystemConfigTool.doubleDivisionToDecimal(
          originStoreTotal,
          SystemConfigTool.doubleAddToDecimal(
            goodsBill.originalTotal,
            goodsBill.originalCurrencyGivePreferentialTotal,
            BillDecimalType.TOTAL,
          ),
          BillDecimalType.TOTAL,
        );

        //需退储值金额
        num storeBack =
            SystemConfigTool.doubleMultipleToDecimal(
              num.parse(sumAllFinalTotal),
              storeRate,
              BillDecimalType.TOTAL,
            ).abs();

        //原单含金量
        num proportion = SystemConfigTool.doubleDivisionToDecimal(
          goodsBill.originCurrencyAdvanceTotal,
          originStoreTotal,
          BillDecimalType.TOTAL,
        );

        storedValue.qty =
            SystemConfigTool.doubleMultipleToDecimal(
              storeBack,
              proportion,
              BillDecimalType.TOTAL,
            ).abs().toString();
        backGiveTotal = SystemConfigTool.doubleSubtractionToDecimal(
          storeBack,
          num.parse(storedValue.qty ?? "0"),
          BillDecimalType.TOTAL,
        );
        giveStoredValue.qty = (-backGiveTotal).toString();
      }

      if (storedValue.qty != "0") {
        vipAssertsBillDtoBean!.assertsBillDetailDtoList.add(storedValue);
      }
      if (giveStoredValue.qty != "-0") {
        vipAssertsBillDtoBean!.assertsBillDetailDtoList.add(giveStoredValue);
      }
    } else {
      //当换货单金额>=0时，积分和成长值也要返还
      vipAssertsBillDtoBean!.assertsBillDetailDtoList =
          vipAssertsBillDtoBean!.assertsBillDetailDtoList
              .where((element) => element.typed == "3")
              .toList();
    }
    //处理积分
    handleScore(handleDiscountScore: false);
    if (!allBack) {
      //处理成长值
      handleGrowth();
    }
    //换出分摊的换入的赠金优惠
    num outGiveShareGive = SystemConfigTool.doubleSubtractionToDecimal(
      goodsBill.sumInDetailGiveTotal,
      backGiveTotal,
      BillDecimalType.TOTAL,
    );
    if (outGiveShareGive > 0) {
      BillTool.setStoreTotalGiftTotal(goodsBill, outGiveShareGive, billType);
    }
  }

  @override
  void buildBackAssertsInfo(GoodsBillDto bill) {
    buildSettlementInfo(bill);
    List<BillVipAssertChange> vipAsserts = bill.vipAsserts ?? [];
    super.buildBackAssertsInfo(bill);
    bill.vipAsserts = [...vipAsserts, ...bill.vipAsserts ?? []];
  }

  //endregion
}
