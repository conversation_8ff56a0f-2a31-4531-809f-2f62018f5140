import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

import '../../common/tool/system_config_tool.dart';
import '../../enum/bill_decimal_type.dart';
import '../../enum/bill_type.dart';
import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/halo_pos_label.dart';
import '../entity/bill_sale_bill_detail_dto.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/goods_detail_dto.dart';
import '../entity/order_bill_item_entity.dart';
import '../entity/payment_dto.dart';
import '../entity/preferential_dto.dart';
import '../tool/bill_tool.dart';
import '../tool/decimal_display_helper.dart';
import '../widget/bill_edit_sale_table_select.dart';
import '../widget/mixin/bill_mixin.dart';
import '../widget/mixin/sale_back_mixin.dart';
import '../widget/mixin/sale_normal_mixin.dart';
import '../widget/mixin/select_vip_mixin.dart';

///退货单页面
class BillEditSaleBackPage extends BaseStatefulPage {
  final OrderBillItem saleBill;

  const BillEditSaleBackPage({Key? key, required this.saleBill})
      : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _BillEditSaleBackPageState();
}

class _BillEditSaleBackPageState
    extends BaseStatefulPageState<BillEditSaleBackPage>
    with
        SelectVipMixin<BillEditSaleBackPage>,
        BillMixin<BillEditSaleBackPage>,
        SaleNormalMixin<BillEditSaleBackPage>,
        SaleBackMixin<BillEditSaleBackPage> {
  @override
  BillType get billType => BillType.SaleBackBill;

  @override
  OrderBillItem get selectBillItem => widget.saleBill;

  ///总储值(本金、赠金) 和 单据总金额 比例
  Decimal storedProportion = Decimal.zero;

  @override
  String getActionBarTitle() => "退货单";

  @override
  String get billTotalTitle => "退款";

  @override
  Future<void> onInitState() {
    return loadData();
  }

  @override
  void setPayment() {
    goodsBillDto.payment = billDetailDto.billDetail!.payment;
    goodsBillDto.sourcePayment = [];
    for (PaymentDto item in billDetailDto.billDetail!.payment) {
      PaymentDto sourceItem = PaymentDto.fromMap(item.toJson());
      sourceItem.posCurrencyAtypeTotal = sourceItem.currencyAtypeTotal ?? "0";

      ///支付方式为储值，将赠金优惠加上
      if (sourceItem.paywayType == 3) {
        sourceItem.posCurrencyAtypeTotal = MathUtil.add(
                sourceItem.currencyAtypeTotal,
                billDetailDto.billDetail!.originalCurrencyGivePreferentialTotal
                    .toString())
            .toString();
      }
      goodsBillDto.sourcePayment.add(sourceItem);
    }
  }

  @override
  Future<VipWithLevelAssertsRightsCardDTO?> getVipData() {
    goodsBillDto.vipBillInfo = billDetailDto.vipBillInfo;

    //原单储值支付 =储值本金+储值赠金
    num sumStoreTotal = SystemConfigTool.doubleAddToDecimal(
        billDetailDto.billDetail?.currencyAdvanceTotal ?? 0,
        billDetailDto.billDetail?.currencyGivePreferentialTotal ?? 0,
        BillDecimalType.TOTAL);

    //有储值消费
    if (sumStoreTotal != 0) {
      // 退款储值比例=储值支付/(单据金额+单据赠金）
      storedProportion = MathUtil.divideDec(
          sumStoreTotal,
          SystemConfigTool.doubleAddToDecimal(
              num.parse(billDetailDto.billDetail!.currencyBillTotal),
              billDetailDto.billDetail!.currencyGivePreferentialTotal,
              BillDecimalType.TOTAL));
    }
    return super.getVipData();
  }

  @override
  bool checkNextStep(GoodsBillDto goodsBillDto) {
    if (goodsBillDto.inDetail.isEmpty) {
      HaloToast.showMsg(context, msg: "退货商品为空");
      return false;
    }
    if (!goodsBillDto.inDetail.any((element) => element.unitQty != 0)) {
      HaloToast.showMsg(context, msg: "无可退商品");
      return false;
    }
    if (goodsBillDto.inDetail
        .any((element) => element.currencyDisedTotal < 0)) {
      HaloToast.showMsg(context, msg: "有商品价格为负");
      return false;
    }

    if (num.parse(goodsBillDto.currencyBillTotal) < 0) {
      HaloToast.showMsg(context, msg: "$billTotalTitle必须大于0");
      return false;
    }
    return true;
  }

  @override
  Widget buildTable() {
    return BillEditSaleTableSelect(
      indexScrollerController: indexScrollerController,
      billType: billType,
      goodsBillDto: goodsBillDto,
      dataChangeCallBack: (GoodsBillDto goodsBill, bool resetFocus) {
        onBackGoodsListDataChange(goodsBill, resetFocus);
      },
    );
  }

  @override //enableClearVip （flutter不建议重写时直接写为 enableClearVip = false）
  Widget buildVipInfo(BuildContext context,
      {bool enableClearVip = true,
      bool showNewRights = false,
      VoidCallback? tapNewRights}) {
    return super.buildVipInfo(context, enableClearVip: false);
  }

  @override
  Widget buildSubDetail() {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              "共计${sumAllQty.toString()}件，退款金额¥${sumAllFinalTotal.toString()} ",
              style: TextStyle(color: const Color(0xFF666666), fontSize: 24.sp),
            ),
          ],
        ));
  }

  @override
  Widget buildVipBottom(BuildContext context) {
    return HaloContainer(
        mainAxisSize: MainAxisSize.max,
        color: Colors.white,
        children: [
          SizedBox(
              width: 450.w,
              child: buildVipInfoView(context, showNewRights: false)),
          buildDivider(),
          Expanded(child: buildPayType()),
          buildTotalPriceView(context)
        ]);
  }

  @override
  Widget buildTotalPriceDetailViewCustomer() {
    return Text(
      billTotalTitle,
      textAlign: TextAlign.center,
      maxLines: 1,
      style: TextStyle(
          color: Colors.white,
          decoration: TextDecoration.none,
          fontSize: 30.sp,
          fontWeight: FontWeight.bold),
    );
  }

  //支付方式金额
  buildPayType() {
    List<Widget> children = [];
    for (var element in goodsBillDto.sourcePayment) {
      children.add(_payWayItem(element));
    }

    return HaloContainer(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      height: 100.h,
      children: [
        HaloPosLabel("原单支付方式",
            textStyle: TextStyle(
                color: AppColors.accentColor,
                fontSize: AppPosSize.totalFontSize.sp)),
        Expanded(
            child: GridView.count(
          reverse: true,
          childAspectRatio: 0.2,
          crossAxisSpacing: 4.w,
          mainAxisSpacing: 4.w,
          crossAxisCount: 2,
          scrollDirection: Axis.horizontal,
          children: children,
        )),
      ],
    );
  }

  _payWayItem(PaymentDto paymentDto) {
    String name = paymentDto.paywayFullname ?? paymentDto.atypeFullName ?? "";

    String total = paymentDto.currencyAtypeTotal!;

    ///如果支付方式为储值，则需加加上赠金
    if (paymentDto.paywayType == 3) {
      total = MathUtil.add(total,
              goodsBillDto.originalCurrencyGivePreferentialTotal.toString())
          .toString();
    }
    return Container(
        alignment: Alignment.centerRight,
        child: HaloPosLabel(
          "$name：$total",
          textStyle: TextStyle(
              fontSize: AppPosSize.contentFontSize.sp,
              color: AppColors.normalFontColor),
        ));
  }

  @override
  void calcVipStoreInfo(GoodsBillDto goodsBill) {
    //是否是单次全退
    bool allBack = true;
    //是否是最后一次退款
    bool lastSaleBackBill = true;

    //已退数量和（辅助单位）
    Decimal sumRefundQty = Decimal.zero;
    //最大可退数量
    Decimal sumMaxQty = Decimal.zero;
    //本地退货数量
    Decimal unitQty = Decimal.zero;

    for (var element in goodsBill.inDetail) {
      ///数据库中refundQty为基本单位，换算为辅助单位计算
      sumRefundQty =
          MathUtil.addDec(sumRefundQty.toDouble(), element.getRefundUnitQty());
      sumMaxQty += Decimal.parse(element.originalMaxQty.toString());
      unitQty += Decimal.parse(element.unitQty.toString());
    }

    //已经存在退货数量，或者本次退货数量不等于最大可退货数量，都为非全退
    if (sumRefundQty != Decimal.zero || unitQty != sumMaxQty) {
      allBack = false;
    }

    //已退数量+本次退货数量 ！=原单总数量和，则不是最后一次退货
    if (sumMaxQty != unitQty + sumRefundQty) {
      lastSaleBackBill = false;
    }
    //获取资产变动
    VipAssertsBillDtoBean vipAssertsBillDto =
        billDetailDto.vipAssertsBillDto ?? VipAssertsBillDtoBean();
    vipAssertsBillDtoBean =
        VipAssertsBillDtoBean.fromMap(vipAssertsBillDto.toJson());
    //储值变动
    AssertsBillDetailDtoListBean storedValue = AssertsBillDetailDtoListBean()
      ..typed = "1";
    //赠金变动
    AssertsBillDetailDtoListBean giveStoredValue =
        AssertsBillDetailDtoListBean()..typed = "2";
    // PreferentialBillsBean billsBean = billDetailDto.preferentialBills!
    //     .firstWhere((element) => element.preferentialType == 6,
    //         orElse: () => PreferentialBillsBean()..preferentialTotal = 0.0);

    //获取储值支付的支付信息
    PaymentDto? paymentDto = billDetailDto.billDetail?.payment
        .firstWhereOrNull((element) => element.paywayType == 3);
    // ///含金量
    // num proportion;
    // if (paymentDto.paywayId == null &&goodsBill.originalCurrencyGivePreferentialTotal == 0) {
    //   proportion = 0;
    // } else {
    //   num sumTotal =SystemConfigTool.doubleAddToDecimal( num.parse(paymentDto.currencyAtypeTotal!) ,goodsBill.originalCurrencyGivePreferentialTotal,BillDecimalType.TOTAL);
    //   proportion =SystemConfigTool.
    //   sumTotal == 0
    //       ? proportion = 0
    //       : proportion = num.parse(paymentDto.currencyAtypeTotal!) / sumTotal;
    // }
    //单次全退
    if (allBack) {
      //单次全退，除了单独处理储值和赠金，其他的如优惠券，积分等直接全退
      vipAssertsBillDtoBean!.assertsBillDetailDtoList.removeWhere(
          (element) => element.typed == "1" || element.typed == "2");
      storedValue.qty = DecimalDisplayHelper.getTotalFixed(
          paymentDto?.currencyAtypeTotal ?? "0");
      giveStoredValue.qty =
          "-${DecimalDisplayHelper.getTotalFixed(goodsBill.currencyGivePreferentialTotal.toString())}";
      //遍历入库明细行，记录赠金优惠辅助
      for (var goodsDetail in goodsBill.inDetail) {
        if (goodsDetail.unitQty != 0) {
          goodsDetail.preferentialHelp = {
            if (goodsDetail.givePreferentialTotal > 0)
              Preferential.giftStore.name: PreferentialDto()
                ..total = goodsDetail.givePreferentialTotal
                ..type = PreferentialType.GiftTotal.value,
          };
        }
      }
      storedValue.memo = "pos退货:全退";
      giveStoredValue.memo = "pos退货:全退";
    } else {
      vipAssertsBillDtoBean!.assertsBillDetailDtoList.clear();

      //需退储值赠金汇总
      num sumGiveStored = 0;
      //需退储值本金汇总
      num sumStored = 0;

      for (GoodsDetailDto goodsDetail in goodsBill.inDetail) {
        //退款数量为0不计算
        if (goodsDetail.unitQty == 0) {
          continue;
        }
        //需退储值金额
        num goodsStored = SystemConfigTool.doubleMultipleToDecimal(
            goodsDetail.posCurrencyDisedTaxedTotal,
            storedProportion.toDouble(),
            BillDecimalType.TOTAL);

        //单个商品明细行全部退完
        //refundQty数据库中存储为基本单位，换算成辅助单位进行
        if ((MathUtil.addDec(
                goodsDetail.unitQty, goodsDetail.getRefundUnitQty())) ==
            Decimal.parse(goodsDetail.originalMaxQty.toString())) {
          //需退赠金=明细总赠金优惠-已退明细赠金优惠
          goodsDetail.givePreferentialTotal =
              goodsDetail.saleBackHelper.remainGivePreferentialTotal();
          goodsDetail.currencyGivePreferentialTotal =
              goodsDetail.givePreferentialTotal;
        } else {
          //需退赠金=（明细退货数量/总明细数据）*明细总赠金优惠
          goodsDetail.givePreferentialTotal =
              SystemConfigTool.doubleMultipleToDecimal(
                  MathUtil.divideDec(
                          goodsDetail.unitQty, goodsDetail.originalMaxQty)
                      .toDouble(),
                  goodsDetail.saleBackHelper.originalGivePreferentialTotal,
                  BillDecimalType.TOTAL);
          goodsDetail.currencyGivePreferentialTotal =
              goodsDetail.givePreferentialTotal;
        }

        //套餐明细行不汇总，需排除
        if (!BillTool.comboDetailRow(goodsDetail)) {
          //需退赠金汇总
          sumGiveStored = SystemConfigTool.doubleAddToDecimal(sumGiveStored,
              goodsDetail.givePreferentialTotal, BillDecimalType.TOTAL);

          //需退本金汇总：需退本金 =需退储值金额-需退赠金
          sumStored = SystemConfigTool.doubleAddToDecimal(
              sumStored,
              SystemConfigTool.doubleSubtractionToDecimal(goodsStored,
                  goodsDetail.givePreferentialTotal, BillDecimalType.TOTAL),
              BillDecimalType.TOTAL);
        }
        goodsDetail.preferentialHelp = {
          Preferential.giftStore.name: PreferentialDto()
            ..total = goodsDetail.givePreferentialTotal
            ..type = PreferentialType.GiftTotal.value,
        };
      }

      storedValue.qty = sumStored.toString();
      giveStoredValue.qty = "-${sumGiveStored.toString()}";
      storedValue.memo = "pos退货:部分退";
      giveStoredValue.memo = "pos退货:部分退";
    }

    if (storedValue.qty != "0") {
      vipAssertsBillDtoBean!.assertsBillDetailDtoList.add(storedValue);
    }
    if (giveStoredValue.qty != "-0") {
      vipAssertsBillDtoBean!.assertsBillDetailDtoList.add(giveStoredValue);
    }
    if (!allBack) {
      //处理积分
      handleScore();
      //处理成长值
      handleGrowth();
    }
  }

  @override
  void changeVip(VipWithLevelAssertsRightsCardDTO? vipInfo) {}
}
