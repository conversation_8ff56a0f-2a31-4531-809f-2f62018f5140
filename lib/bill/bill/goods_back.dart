import 'package:flutter/material.dart';
import 'package:halo_pos/bill/entity/goods_bill.dto.dart';
import 'package:halo_pos/bill/settlement/back_goods_settlement.dart';
import 'package:halo_pos/bill/tool/promotion/discount.dart';
import 'package:halo_pos/bill/tool/promotion/preferential.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_dialog.dart';
import 'package:haloui/widget/halo_toast.dart';
import '../../enum/bill_type.dart';
import '../../login/entity/store/store_payment.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../entity/goods_detail_dto.dart';
import '../settlement/settlement_mixin.dart';
import '../widget/bill_edit_sale_table.dart';
import '../widget/mixin/bill_mixin.dart';
import '../widget/mixin/sale_normal_mixin.dart';
import '../widget/mixin/select_vip_mixin.dart';

///按商品退货
class BillBackGoodsPage extends BaseStatefulPage {
  const BillBackGoodsPage({Key? key}) : super(key: key);

  @override
  BaseStatefulPageState<BillBackGoodsPage> createState() =>
      _BillBackGoodsPageState();
}

class _BillBackGoodsPageState extends BaseStatefulPageState<BillBackGoodsPage>
    with SelectVipMixin, BillMixin, SaleNormalMixin {
  @override
  BillType get billType => BillType.SaleBackBill;

  @override
  String get billTotalTitle => "退款";

  @override
  String getActionBarTitle() => "退货单";

  ///按商品退货，不支持会员，不支持储值
  ///不支持扫码支付
  ///即和离线支付时一样
  @override
  List<StorePayway> get storePaywayList => filterPayWayList(isOffline: true);

  @override
  Future<void>? onInitState() {
    return loadData();
  }

  ///顶部的搜索框、选择商品、挂单、打印等
  @override
  Widget buildTopBody(BuildContext context) {
    return hasSalePermission
        ? Container(
            color: Colors.white,
            height: 100.h,
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
            child: Row(
              children: [
                buildSearch(),
                SizedBox(width: 10.w),
                buildAddGoodsProp(),
              ],
            ),
          )
        : Container();
  }

  @override
  Widget buildTable() {
    return BillEditSaleTable(
      indexScrollerController: indexScrollerController,
      billType: billType,
      backByGoods: true,
      goodsBillDto: goodsBillDto,
      dataChangeCallBack: (GoodsBillDto goodsBill, bool resetFocus) {
        onBackGoodsListDataChange(goodsBill, resetFocus);
      },
    );
  }

  @override
  Widget buildSubDetail() {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              "共计${sumAllQty.toString()}件，退款金额¥${sumAllFinalTotal.toString()} ",
              style: TextStyle(color: const Color(0xFF666666), fontSize: 24.sp),
            ),
          ],
        ));
  }

  ///退货商品发生变动，重新计算
  void onBackGoodsListDataChange(GoodsBillDto goodsBill, bool resetFocus) {
    updateGoodsDetail(requestSearchFocus: resetFocus);
  }

  ///添加商品
  @override
  void addGoodsDetails(List<GoodsDetailDto> details) {
    super.addGoodsDetails(details);
    calculateGoods(requestSearchFocus: true);
  }

  ///刷新商品列表
  @override
  void updateGoodsDetail({bool requestSearchFocus = true}) {
    super.updateGoodsDetail(requestSearchFocus: requestSearchFocus);
    calculateGoods(requestSearchFocus: requestSearchFocus);
  }

  @override
  Widget buildVipBottom(BuildContext context) {
    return Align(
        alignment: Alignment.centerRight, child: buildTotalPriceView(context));
  }

  ///汇总单据
  void calculateGoods({bool requestSearchFocus = true}) {
    ///计算商品金额
    VipPriceHandler.handleVipPrice(goodsBillDto, false,
        goodsList: goodsBillDto.inDetail);
    PreferentialUtil.cleanPreferentialHelper(goodsBillDto);
    PreferentialUtil.collectBill(goodsBillDto, billType: billType);
    setState(() => reCalcStatistic());
    if (requestSearchFocus) {
      searchFocusNode.requestFocus();
    }
  }

  @override
  bool checkNextStep(GoodsBillDto goodsBillDto) {
    if (goodsBillDto.inDetail.isEmpty) {
      HaloToast.showMsg(context, msg: "退货商品为空");
      return false;
    }
    if (!goodsBillDto.inDetail.any((element) => element.unitQty != 0)) {
      HaloToast.showMsg(context, msg: "无可退商品");
      return false;
    }
    if (goodsBillDto.inDetail
        .any((element) => element.currencyDisedTotal < 0)) {
      HaloToast.showMsg(context, msg: "有商品价格为负");
      return false;
    }
    if (num.parse(goodsBillDto.currencyBillTotal) < 0) {
      HaloToast.showMsg(context, msg: "$billTotalTitle必须大于0");
      return false;
    }
    return true;
  }

  @override
  void onPaySuccess() {
    Navigator.pop(context);
  }

  @override
  Future showSettlementDialog({required GoodsBillDto bill}) {
    GoodsBillDto goodsBill = GoodsBillDto.fromMap(bill.toJson());
    goodsBill.inDetail.removeWhere((element) => element.unitQty == 0);
    return HaloDialog(context,
        dismissOnTouchOutside: true,
        child: BackGoodsSettlementPage(
          goodsBillDto: goodsBill,
          successCallback: onPaySuccess,
        )).show().then((value) {
      if (value is String) {
        goodsBillDto.tips = value;
      }
    });
  }
}
