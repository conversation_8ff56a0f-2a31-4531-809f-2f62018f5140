import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import '../../common/login/login_center.dart';
import '../../common/tool/performance_capture_util.dart';
import '../../db/database_helper.dart';
import '../../db/ptype_db_manager.dart';
import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../../bill/entity/ptype/ptype_otype_price_dto.dart';
import '../../bill/entity/ptype_suit_model.dart';
import '../../bill/tool/bill_tool.dart';
import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../common/tool/sp_tool.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';

import '../entity/bill_ptype_request.dart';
import '../entity/ptype/goods_type.dart';
import '../entity/ptype/ptype_price_dto.dart';
import '../entity/ptype/ptype_unit_dto.dart';
import '../../entity/page_info.dart';
import '../../db/entity/ptype_label.dart';
import '../tool/goods_scope_util.dart';

///
///@ClassName: ptype_model
///@Description: 类作用描述
///@Author: tanglan
///@Date: 8/11/21 9:45 AM
class PtypeModel {
  ///获取商品价格信息
  static Future<List<PtypePriceDto>> getPtypePrice(
    BuildContext context,
    List<GoodsDetailDto> goods,
  ) async {
    if (goods.isEmpty) {
      return [];
    }
    String otypeId = SpTool.getStoreInfo()!.otypeId!;
    if (_useLocal) {
      List<Map<String, dynamic>> list = await PtypeDbManager.selectPriceList(
        list: goods
            .map(
              (e) => {
                "ptypeId": e.ptypeId,
                "unitId": e.unitId,
                "skuId": e.skuId,
                "skuPrice": e.skuPrice,
              },
            )
            .toList(growable: false),
        otypeId: otypeId,
      );
      return list
          .map(
            (e) => PtypePriceDto.fromMap(
              DatabaseHelper.changeUnderscoreCaseMapToCamelCaseMap(e),
            ),
          )
          .toList();
    }
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.GET_PTYPE_PRICE,
      data: {
        "goodsList":
            goods
                .where((element) => !BillTool.comboDetailRow(element))
                .toList()
                .map(
                  (e) => {
                    "ptypeId": e.ptypeId,
                    "skuId": e.skuId,
                    "unitId": e.unitId,
                  },
                )
                .toList(),
        "otypeId": otypeId,
        //搜索零售价
        "searchRetailPrice": true,
      },
    );
    List? data = response.data;
    return data?.map((e) => PtypePriceDto.fromMap(e)).toList() ?? [];
  }

  ///根据ptypId、ktypeId、skuId、unitId获取商品的库存、价格和条码
  static Future<Map?> getPtypePriceStockAndBarcode(
    BuildContext context, {
    @required String? ptypeId,
    @required String? skuId,
    @required String? unitId,
    @required int? skuPrice,
    @required String? ktypeId,
    @required String? otypeId,
  }) async {
    if (_useLocal) {
      Map<String, dynamic>? map =
          await PtypeDbManager.selectPriceStockAndBarcode(
            ptypeId: ptypeId ?? "",
            skuId: skuId ?? "",
            unitId: unitId ?? "",
            skuPrice: skuPrice ?? 1,
            otypeId: otypeId ?? "",
            ktypeId: ktypeId,
          );
      if (map != null) {
        map["price"] = map["currencyPrice"];
        map["stock"] = map["stockQty"];
        map["barcode"] = map["fullbarcode"];
      }
      return map;
    } else {
      //todo 这里后端接口之前用的是jxc的接口，现在已经删除，后续需要可以按照pos这版的逻辑重写接口
      return null;
    }
  }

  ///获取套餐列表
  ///请求参数[params]格式如下:
  ///```java
  ///    {
  /// 		  "refresh":true,
  /// 		  "pageIndex":1,
  /// 		  "pageSize":20,
  /// 		  "queryParams":{
  /// 			  "stoped":false,
  /// 			  "ktypeId":"仓库id",
  /// 			  "filtervalue":"名称/id模糊查询",
  /// 			  "btypeId":"店铺id",
  /// 			  "filterkey":"quick",
  /// 			  "partypeid":"分类id:null为全部分类，00000为未分类",
  /// 			  "lefttypeid":"未知，现在不传"
  /// 		  }
  ///    }
  ///```
  @Deprecated("已废弃，可以删除，删除同时建议删除后端接口")
  static Future<List<PtypeSuitModel>> getComboList(
    BuildContext context,
    Map<String, dynamic> params,
  ) async {
    ResponseModel responseDto = await HttpUtil.request(
      context,
      method: RequestMethod.GET_COMBO_LIST,
      data: params,
    );
    Map? data = responseDto.data;
    List? list = data?["list"];
    return list?.map((e) => PtypeSuitModel.fromMap(e)).toList() ?? [];
  }

  ///获取商品列表
  @Deprecated("已废弃，可以删除，删除同时建议删除后端接口")
  static Future<List<GoodsDetailDto>> getGoodsList(
    BuildContext context,
    Map<String, dynamic> params,
  ) async {
    ResponseModel responseDto = await HttpUtil.request(
      context,
      method: RequestMethod.GET_GOODS_LIST,
      data: params,
    );
    Map? data = responseDto.data;
    List? list = data?["list"];
    return list
            ?.map((i) => PtypeListModel.fromMap(i as Map<String, dynamic>))
            .map((e) => e.changeModel())
            .toList() ??
        [];
  }

  ///查询商品和套餐
  ///当传入页码为1时，会额外根据序列号精确查询商品，最后将查到的商品插入到表头
  static Future<List<PtypeListModel>> selectPtypeAndCombo(
    BuildContext context, {
    required BillPtypeRequest queryParam,
    int pageIndex = 1,
    int pageSize = 20,
  }) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.pTypeSelect);
    if (_useLocal) {
      final loginUser = LoginCenter.getLoginUser();
      final userInfo = SpTool.getSystemConfig().userInfo;
      queryParam.etypeId = loginUser.employeeId;

      if (queryParam.searchByPtype) {
        List<PtypeListModel> result = await PtypeDbManager.searchPtype(
          pageIndex: pageIndex,
          pageSize: pageSize,
          queryParam: queryParam,
          ptypeLimited: userInfo?.ptypeLimited,
          isAdmin: loginUser.adminStatus == true,
        );

        // 填充商品标签信息
        await GoodsScopeUtil.fillPtypeListLabelIds(result);

        return result;
      }
      List<PtypeListModel> result = await PtypeDbManager.searchUnitSku(
        pageIndex: pageIndex,
        pageSize: pageSize,
        queryParam: queryParam,
        isAdmin: loginUser.adminStatus == true,
        ptypeLimited: userInfo?.ptypeLimited,
      );

      // 填充商品标签信息
      await GoodsScopeUtil.fillPtypeListLabelIds(result);

      return result;
    }
    DioConfig config = DioConfig();
    if (queryParam.fullBarCode == true) {
      config.CONNECT_TIMEOUT = 5000;
      config.RECEIVE_TIMEOUT = 5000;
    }
    ResponseModel responseDto = await HttpUtil.request(
      context,
      config: config,
      method: RequestMethod.POST_SELECT_PTYPE_AND_COMBO,
      data: {
        "pageIndex": pageIndex,
        "pageSize": pageSize,
        "queryParams": queryParam.toJson(),
      },
    );
    if (responseDto.code != 200 || null == responseDto.data) {
      return [];
    }
    PerformanceCaptureUtil.end(PerformanceTimeName.pTypeSelect);
    Map data = responseDto.data;
    return (data["list"] as List?)
            ?.map((e) => PtypeListModel.fromMap(e))
            .toList() ??
        [];
  }

  // ///查询商品和套餐
  // ///当传入页码为1时，会额外根据序列号精确查询商品，最后将查到的商品插入到表头
  // static Future<Map?> selectPtypeAndComboDemo(
  //   BuildContext context, {
  //   required String? btypeId,
  //   required String? otypeId,
  //   required String? filterValue,
  //   required String? ktypeId,
  //   String? typeId,
  //   bool searchBySn = true,
  //   bool fullBarCode = false,
  //   bool stockSelect = false,
  //   int pageIndex = 1,
  //   int pageSize = 20,
  //   bool searchByPtype = false,
  // }) async {
  //   DioConfig config = DioConfig();
  //   if (fullBarCode == true) {
  //     config.CONNECT_TIMEOUT = 5000;
  //     config.RECEIVE_TIMEOUT = 5000;
  //   }
  //   ResponseModel responseDto = await HttpUtil.request(context,
  //       config: config,
  //       isLoading: false,
  //       method: RequestMethod.POST_SELECT_PTYPE_AND_COMBO,
  //       data: {
  //         "pageIndex": pageIndex,
  //         "pageSize": pageSize,
  //         "queryParams": {
  //           "btypeId": btypeId,
  //           "otypeId": otypeId,
  //           "ktypeId": ktypeId,
  //           "searchBySn": searchBySn,
  //           "filterValue": filterValue,
  //           "fullBarCode": fullBarCode,
  //           "stockSelect": stockSelect,
  //           "typeId": typeId,
  //           "searchByPtype": searchByPtype,
  //         }
  //       });
  //   if (responseDto.code != 200 || null == responseDto.data) {
  //     return null;
  //   }
  //   return responseDto.data;
  // }

  ///获取所有商品一二级分类
  static Future<List<GoodsType>> getPtypeAllRootType(
    BuildContext context,
  ) async {
    if (_useLocal) {
      return await PtypeDbManager.getPtypeAllRootType();
    }
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.getPtypeAllRootType,
    );
    return (response.data as List?)
            ?.map((e) => GoodsType.fromMap(e))
            .toList() ??
        [];
  }

  ///查询商品单位
  static Future<List<PtypeUnitDto>> getPtypeUnitList(
    BuildContext context,
    String ptypeId,
    String skuId,
  ) async {
    if (_useLocal) {
      return PtypeDbManager.getPtypeUnitList(ptypeId, skuId);
    }
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GET_PTYPE_UNIT,
      data: {"ptypeId": ptypeId, "skuId": skuId},
    );
    if (response.code != 200) {
      throw Exception(response.message);
    }
    List<dynamic>? list = response.data;
    return list?.map((e) => PtypeUnitDto.fromJson(e)).toList() ?? [];
  }

  ///批量更改商品的库存
  static Future<void> changePtypeStockQtyByGoodsBill({
    List<GoodsDetailDto> outDetail = const [],
    List<GoodsDetailDto> inDetail = const [],
  }) async {
    if (outDetail.isEmpty && inDetail.isEmpty) return;
    if (!_useLocal) return;
    try {
      List<Map<String, dynamic>> list = [];
      String ktypeId = SpTool.getStoreInfo()!.ktypeId!;
      //实体类转换
      changeGoodsToMap(List<GoodsDetailDto> goods, bool out) {
        if (goods.isEmpty) return <Map<String, dynamic>>[];
        //排除套餐行
        return goods
            .where(
              (element) => element.pcategory != 2 && (element.unitQty ?? 0) > 0,
            )
            .map<Map<String, dynamic>>((e) {
              num qty = e.unitQty;
              if (e.unitRate != 1) {
                qty =
                    (Decimal.parse(qty.toString()) *
                            Decimal.parse((e.unitRate ?? 1).toString()))
                        .toDouble();
              }
              qty = qty.abs();
              //出库减库存
              if (out) {
                qty = -qty;
              }
              return {
                "ptypeId": e.ptypeId,
                "skuId": e.skuId,
                "ktypeId": ktypeId,
                "qty": qty,
              };
            })
            .toList();
      }

      //合并商品
      mergeList(List<Map<String, dynamic>> mergeList) {
        for (var value in mergeList) {
          Map<String, dynamic>? existItem = list
              .cast<Map<String, dynamic>?>()
              .firstWhere(
                (element) =>
                    element?["ptypeId"] == value["ptypeId"] &&
                    element?["skuId"] == value["skuId"] &&
                    element?["ktypeId"] == value["ktypeId"],
                orElse: () => null,
              );
          if (existItem == null) {
            list.add(value);
          } else {
            num existQty = existItem["qty"];
            num qty = value["qty"];
            existItem["qty"] = existQty + qty;
          }
        }
      }

      mergeList(changeGoodsToMap(outDetail, true));
      mergeList(changeGoodsToMap(inDetail, false));

      await PtypeDbManager.changePtypeStockQty(list);
    } catch (e) {
      debugPrint("更改本地库存失败：$e");
    }
  }

  ///修改商品零售价
  static Future<void> addOrUpdateOtypePrice(
    BuildContext context,
    GoodsDetailDto goods,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_ADD_OR_UPDATE_OTYPE_PRICE,
      isLoading: false,
      data: {
        "ptypeId": goods.ptypeId,
        "skuId": goods.skuId,
        "unitId": goods.unitId,
        "otypeId": SpTool.getStoreInfo()?.otypeId,
        "price": goods.currencyPrice,
      },
    );
    if (response.data is String) {
      if (_useLocal) {
        await PtypeDbManager.addOrUpdateOtypePrice(goods, response.data);
      }
    }
  }

  ///获取商品标签关系数据（分页）
  static Future<PageInfo<PtypeLabelDO>> getPtypeLabel(
    BuildContext context, {
    int pageIndex = 1,
    int pageSize = 1000,
  }) async {
    try {
      ResponseModel response = await HttpUtil.request(
        context,
        method: RequestMethod.getPosPtypeLabelList,
        data: {"pageIndex": pageIndex, "pageSize": pageSize},
      );

      if (response.code != 200 || response.data == null) {
        return PageInfo<PtypeLabelDO>(
          list: [],
          pageNum: pageIndex,
          pageSize: pageSize,
          total: 0,
          pages: 0,
          hasNextPage: false,
        );
      }

      return PageInfo<PtypeLabelDO>.fromMap(
        response.data,
        mapper: (json) => PtypeLabelDO.fromJson(json),
      );
    } catch (e) {
      debugPrint("获取商品标签关系数据失败：$e");
      // 返回空数据，避免影响其他功能
      return PageInfo<PtypeLabelDO>(
        list: [],
        pageNum: pageIndex,
        pageSize: pageSize,
        total: 0,
        pages: 0,
        hasNextPage: false,
      );
    }
  }

  static bool get _useLocal {
    final config = SpTool.getDatabaseConfig();
    return config.enableLocalPtype && config.hasPtypeData;
  }
}
