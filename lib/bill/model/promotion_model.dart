import 'dart:convert';

import 'package:flutter/cupertino.dart';
import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../common/tool/sp_tool.dart';
import '../../bill/entity/bill_promotion_info_dto.dart';

///
///@ClassName: promotion_model
///@Description: 促销操作
///@Author: tanglan
///@Date: 2022/1/21
class PromotionModel {
  static Future<List<BillPromotionInfoDto>?> getFullPromotionList(
      BuildContext context,
      {isLoading = true}) {
    return HttpUtil.request(
      context,
      isLoading: isLoading,
      method: RequestMethod.GET_FULL_PROMOTION_LIST,
      data: SpTool.getStoreInfo()!.otypeId,
    ).then((value) {
      List? list = value.data;
      return list
          ?.map((e) => BillPromotionInfoDto.fromMap(e))
          //不是积分兑换促销
          .where((element) => element.promotionType != 4 && element.state!=2)
          .toList();
    });
  }
}
