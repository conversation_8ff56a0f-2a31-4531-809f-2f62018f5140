import 'package:flutter/cupertino.dart';
import 'package:halo_utils/http/base_model.dart';

import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../login/entity/store/store_ktype.dart';

class TenderBillModel {

  /*获取公司仓库列表*/
  static Future<List<StoreKtype>> getKtypeList(BuildContext context) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GETKTYPELIST);
    List<dynamic>? ktypeList = response.data;
    List<StoreKtype> ktypeData = ktypeList?.map((element) {
      return StoreKtype.fromJson(element);
    }).toList() ?? [];
    return ktypeData;
  }
}
