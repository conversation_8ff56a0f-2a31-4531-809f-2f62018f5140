import 'package:flutter/cupertino.dart';
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_utils/http/base_model.dart';

import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../report/entity/store_statistics_dto.dart';

class StatisticsModel {
  static Future<StoreStatisticsDto> getStoreIncomeStatisticsListForPOS(BuildContext context,String startTime,String endTime) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_getStoreIncomeStatisticsListForPOS,data:{
          "page": 1,
          "pageSize": 40,
          "startTime": startTime,
          "endTime": endTime,
          "otypeId":SpTool.getStoreInfo()?.otypeId??"0"
        });
    return StoreStatisticsDto.fromJson(response.data);
  }
}