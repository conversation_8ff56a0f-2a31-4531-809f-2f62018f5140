import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';

import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../vip/entity/card_coupon_card_template.dart';
import '../entity/ss_card_dto.dart';

/// 创建时间：5/26/22
/// 作者：xiaotiaochong
/// 描述：

class CardTemplateModel {
  ///根据会员获取卡券信息
  ///[vipId] 会员id
  ///[otypeId] 门店id，用于筛选这个门店可用的优惠券
  ///[cardType] 卡券类型 0：等级权益卡，1：普通权益卡，2：代金券 3：折扣券 4：礼品券
  static Future<List<SsCardDto>?> getCardByVipId(BuildContext context,
      {@required String? vipId}) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_CARD_BY_VIP_ID,
        data: {
          "vipId": vipId,
          "cardType": [0, 1]
        });
    return (response.data as List?)
        ?.map((json) => SsCardDto.fromMap(json))
        .toList();
  }

  //优惠券
  static Future<List<CardCouponCardTemplate>?> getCardTemplateListByIds(
      BuildContext context, List<String> cardModelIds) {
    return HttpUtil.request(
      context,
      method: RequestMethod.POST_GET_CARD_TEMPLATE_LIST_IDS,
      data: cardModelIds,
    ).then((value) {
      List? list = value.data;
      return list?.map((e) {
        return CardCouponCardTemplate.fromJson(e);
      }).toList();
    });
  }

  //优惠券
  static Future<CardCouponCardTemplate?> getCardTemplateById(
      BuildContext context, String id) {
    String method = RequestMethod.POST_CARD_TEMPLATE_BY_ID + id;
    return HttpUtil.request(
      context,
      requestModel: NetMethod.GET,
      method: method,
    ).then((value) {
      if (null == value.code || null == value.data) {
        return null;
      }
      return CardCouponCardTemplate.fromJson(value.data);
    });
  }
}
