import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import '../../bill/entity/ptype/ptype_unit_dto.dart';
import '../../entity/otype.dart';
import 'package:halo_utils/http/base_model.dart';
import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../bill/entity/etype_list_dto.dart';
import '../../entity/upload_qiniu_dto.dart';

import '../entity/otype_with_ktype.dart';
import '../entity/pos_encrypt_info.dart';

///
///@ClassName: base_info_model
///@Description: 类作用描述
///@Author: tanglan
///@Date: 8/5/21 11:37 AM

class BaseInfoModel {
  ///更新会员密码
  static Future<bool> validVipPassword(
      BuildContext context, String vipCardId, String pwd) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.VALID_VIP_PASSWORD,
        data: {"vipCardId": vipCardId, "password": pwd});
    return response.data ?? false;
  }

  ///更新会员密码
  static Future<String> updateVipPassword(
      BuildContext context, String vipCardId, String pwd) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.UP_DATE_VIP_PASSWORD,
        data: {"vipCardId": vipCardId, "password": pwd});
    return response.data["message"] ?? "";
  }

  ///职员列表
  static Future<List<EtypeItemDto>> getEtypeList(
      BuildContext context, requestParams) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.GET_ETYPE__LIST, data: requestParams);
    if (response.code! < 0 && null == response.data) {
      return [];
    }
    return EtypeListDto.fromMap(response.data).list ?? [];
  }

  ///
  ///
  ///上传图片到七牛
  ///[context]上下文对象，用于展示/隐藏Loading
  ///[file]图片文件
  static Future<UploadQiniuDto> uploadImages(
      BuildContext context, File file) async {
    String path = file.path;
    String fileName = path.substring(path.lastIndexOf("/") + 1, path.length);
    MultipartFile part = await MultipartFile.fromFile(path, filename: fileName);
    FormData data = FormData.fromMap({"file": part});
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_UPLOAD_FILE_TO_QINIU,
      data: data,
    );
    if (response.data == null || response.code != 200) {
      throw Exception(response.message);
    }
    return UploadQiniuDto.fromMap(response.data);
  }

  ///获取所有的门店列表
  static Future<List<Otype>?> getOtypeList(BuildContext context) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_OTYPE_LIST, data: {"stoped": 0});
    return (response.data as List?)
        ?.map((json) => Otype.fromJson(json))
        .toList();
  }

  ///查询门店以及其关联的仓库
  static Future<List<OtypeWithKtypeDTO>> selectOtypeWithKtype(
      BuildContext context) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_SELECT_OTYPE_WITH_OTYPE);
    return (response.data as List?)
            ?.map((json) => OtypeWithKtypeDTO.fromMap(json))
            .toList() ??
        [];
  }

  ///解密接口
  static Future<List<PosBuyer>> batchDecryptBuyers(BuildContext context, List<String> buyerIds) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_batchDecryptBuyers,data:buyerIds);
    return (response.data as List?)
        ?.map((json) => PosBuyer.fromMap(json))
        .toList() ??
        [];
  }
}
