import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:halo_pos/common/tool/UUID.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/dto/halo_tree_node_dto.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../cashierbox/entity/cash_box_payment_request_dto.dart';
import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../common/standard.dart';
import '../../common/tool/performance_capture_util.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/tool/tts_util.dart';
import '../../enum/bill_type.dart';
import '../../enum/payment_enum.dart';
import '../../enum/price_type_enum.dart';
import '../../report/entity/stock_statistics_dto.dart';
import '../entity/bill_load_request.dart';
import '../entity/bill_sale_bill_detail_dto.dart';
import '../entity/bill_save_reslut_dto.dart';
import '../entity/bill_stock_qty_dto.dart';
import '../entity/bill_stock_qty_respond.dart';
import '../entity/btype_list_dto.dart';
import '../entity/check_detail_bill_request.dart';
import '../entity/create_bill_response.dart';
import '../entity/delete_bill_request.dart';
import '../entity/goods_batch_dto.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/goods_detail_dto.dart';
import '../entity/ktype_list_dto.dart';
import '../entity/operation_bill_response.dart';
import '../entity/order_bill_dto.dart';
import '../entity/order_bill_item_entity.dart';
import '../entity/order_bill_query_entity.dart';
import '../entity/pay_result_dto.dart';
import '../entity/pay_result_dto_entity.dart';
import '../entity/payment_dto.dart';
import '../entity/ptype/ptype_and_sku_dto.dart';
import '../entity/ptype/ptype_batch_dto.dart';
import '../entity/ptype/ptype_batch_param_qty_detail_dto.dart';
import '../entity/ptype_suit_model.dart';
import '../entity/select_detail_create_bill_request.dart';
import '../settlement/entity/atype_info_bean.dart';
import '../settlement/entity/payment_goodsbill_dto.dart';
import '../settlement/entity/score_configuration.dart';
import '../transfer_and_allocate/entity/transfer_order_list_item.dart';

class BillModel {
  BillModel._();

  ///获取商品库存
  static Future<List<BillStockQtyRespond>> getStockQty(
    BuildContext context,
    List<BillStockQtyDto> list,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GETSTOCKQTY,
      data: {"ptypeList": list.map((e) => e.toJson()).toList()},
    );
    List? data = response.data;
    return data?.map((e) => BillStockQtyRespond.fromJson(e)).toList() ?? [];
  }

  static Future<String> sendMsg(
    BuildContext context,
    String phone,
    String code,
  ) async {
    ResponseModel res = await HttpUtil.request(
      context,
      data: {"phone": phone, "code": code},
      method: RequestMethod.POST_VIP_SEND_SMS,
    );
    return res.data.toString();
  }

  static Future<PaymentGoodsBillDto> getPaymentListByOtypeId(
    BuildContext context,
    String? otypeId,
    String? vchcode,
    String? vchtype,
    String? businessType,
  ) async {
    ResponseModel res = await HttpUtil.request(
      context,
      data: {
        "otypeId": otypeId,
        "vchcode": vchcode,
        "vchtype": vchtype,
        "businessType": businessType,
        "orderSaleMode": OrderBillModelData[OrderBillModel.STORE_SALE],
        "customType": 0,
      },
      method: RequestMethod.GET_PAYMENT_LIST_BY_OTYPEID,
    );
    return PaymentGoodsBillDto.fromJson(res.data);
  }

  static Future<List<OrderBillItem>> orderBillCoreList(
    BuildContext context,
    requestParams,
  ) async {
    ResponseModel res = await HttpUtil.request(
      context,
      data: requestParams,
      method: RequestMethod.POST_ORDER_LIST,
    );
    return OrderBillQueryListDto.fromModel(res.data).list;
  }

  static Future<List<HaloTreeNodeDto>> getBaseInfoClass(
    BuildContext context,
  ) async {
    ResponseModel res = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GETBASEINFOCLASS,
    );
    List data = res.data;
    return data.map((e) => HaloTreeNodeDto.fromJson(e)).toList();
  }

  static Future<ResponseModel> saveBaseInfoPtype(
    BuildContext context,
    requestParams,
  ) async {
    ResponseModel res = await HttpUtil.request(
      context,
      data: requestParams,
      method: RequestMethod.POST_SAVEBASEINFOPTYPE,
    );
    return res;
  }

  static Future<List<OrderBillItem>> orderBillAllList(
    BuildContext context,
    requestParams,
  ) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.billQuery);
    ResponseModel res = await HttpUtil.request(
      context,
      data: requestParams,
      method: RequestMethod.POST_ORDER_ALL_LIST,
    );
    PerformanceCaptureUtil.end(PerformanceTimeName.billQuery);
    return OrderBillQueryListDto.fromModel(res.data).list;
  }

  static Future<List<OrderBillItem>> orderBillCoreListAndPreferential(
    BuildContext context,
    requestParams,
  ) async {
    ResponseModel res = await HttpUtil.request(
      context,
      data: requestParams,
      method: RequestMethod.POST_ORDER_LIST_PREFERENTIAL,
    );
    return OrderBillQueryListDto.fromModel(res.data).list;
  }

  static Future<ResponseModel?> writeBatchPrintCount(
    BuildContext context,
    String? vchcode,
    String vchtype,
    int times,
  ) async {
    if (vchcode == null) return null;
    Map request = {
      "clientParams": {"vchcode": vchcode, "vchtype": vchtype},
      "printTimes": times,
    };
    ResponseModel res = await HttpUtil.request(
      context,
      data: request,
      method: RequestMethod.WRITE_PRINT_COUNT,
      isLoading: false,
    );
    return res;
  }

  ///商品库存查询
  static Future<List<StockStatisticsDto>> getStockStatistics(
    BuildContext context,
    int pageIndex,
    String filterValue,
  ) async {
    List<StockStatisticsDto> list = [];
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GET_STOCK_STATISTICS,
      data: {
        "pageIndex": 1,
        "pageSize": 10000,
        "queryParams": {
          "filterValue": filterValue,
          "partypeid": "",
          "ktypeIdss": [SpTool.getStoreInfo()!.ktypeId],
          "ktypeIds": [SpTool.getStoreInfo()!.ktypeId],
        },
      },
    );
    var map = response.data['list'];
    map.forEach((item) => (list.add(StockStatisticsDto.fromMap(item))));
    return list;
  }

  ///根据id获取套餐信息
  static Future<PtypeSuitModel?> getComboById(
    BuildContext context,
    String id,
  ) async {
    ResponseModel responseDto = await HttpUtil.request(
      context,
      method: RequestMethod.GET_COMBO_BY_ID,
      data: id,
    );
    Map? data = responseDto.data;
    return data?.let((data) => PtypeSuitModel.fromMap(data));
  }

  static bool checkPaymentList(
    BuildContext context,
    List<AtypeInfoBean> payment,
  ) {
    for (AtypeInfoBean item in payment) {
      if (item.storePayway.paywayType != 3 &&
          StringUtil.isZeroOrEmpty(item.storePayway.atypeId)) {
        HaloToast.show(
          context,
          msg:
              "支付方式【${item.storePayway.paywayName}】绑定的现金银行为空，请在资料-科目-支付方式 中进行编辑",
        );
        return false;
      }
    }

    return true;
  }

  ///获取促销赠品配置
  static Future<bool> getPromotionAutomation(BuildContext context) async {
    PerformanceCaptureUtil.start(PerformanceTimeName.promotionSync);
    ResponseModel responseDto = await HttpUtil.request(
      context,
      method: RequestMethod.GET_PROMOTION_AUTOMATION,
      isLoading: false,
    );
    PerformanceCaptureUtil.end(PerformanceTimeName.promotionSync);
    //"0"，自动,"1"手工
    return responseDto.data == "1" ? false : true;
  }

  static Future<GoodsBillDto?> getGoodsBill(
    BuildContext context,
    String? vchcode,
    String? vchtype,
    String? businessType, {
    int customType = 0,
  }) async {
    if (StringUtil.isEmpty(vchtype)) {
      return GoodsBillDto();
    }
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GOODS_BILL,
      onErrorCallBack: (dynamic error) {
        if (error.data["code"] == "202") {
          HaloToast.showError(context, msg: "没有权限访问");
        }

        if (error.data["code"] == "500") {
          HaloToast.showError(context, msg: "未获取到单据信息");
        }
        return true;
      },
      data: {
        "vchcode": vchcode,
        "businessType": businessType,
        "vchtype": vchtype,
        "orderSaleMode": OrderBillModelData[OrderBillModel.STORE_SALE],
        "customType": customType,
      },
    ).onError((error, stackTrace) {
      return ResponseModel(code: 500, message: "没有权限访问", data: null);
    });

    if (response.data == null) return null;
    GoodsBillDto goodsBillDto = GoodsBillDto.fromMap(response.data);
    //todo 由于单据组这个接口暂不支持SaleNormal,而开单现在必须传这个，所以先手动赋值
    if (businessType == BillBusinessTypeString[BillBusinessType.SaleNormal]) {
      goodsBillDto.businessType = "SaleNormal";
    }
    return goodsBillDto;
  }

  static Future<BillSaleBillDetailDto> getGoodsDetailsAndPreferential(
    BuildContext context,
    String vchcode,
    String vchtype,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GOODS_DETAILS_AND_PREFERENTIAL,
      onErrorCallBack: (dynamic error) {
        if (error.data["code"] == "202") {
          HaloToast.showError(context, msg: "没有调拨单据明细查看权限");
        }
        // if (error.data["code"] == "500") {
        //   HaloToast.showError(context, msg: error.data["message"]);
        // }
        return false;
      },
      data: vchcode,
    );
    Map? data = response.data;

    ///todo 这里的明细金额需要回填赠金 不然导致退货换货的实收金额有差 暂时处理在model里 不侵入工具类
    BillSaleBillDetailDto detailDto = BillSaleBillDetailDto.fromMap(data);
    // if (detailDto.billDetail?.inDetail?.isNotEmpty ?? false) {
    //   detailDto.preferentialGoodsDetails?.forEach((elementDetails) {
    //     for (var element in detailDto.billDetail!.inDetail!) {
    //       if (element.detailId! == elementDetails.detailId &&
    //           elementDetails.preferentialType! == 6) {
    //         element.currencyDisedTaxedTotal +=
    //             num.parse(elementDetails.preferentialTotal.toString());
    //       }
    //     }
    //   });
    // }
    //
    // if (detailDto.billDetail?.outDetail?.isNotEmpty ?? false) {
    //   detailDto.preferentialGoodsDetails?.forEach((elementDetails) {
    //     for (var element in detailDto.billDetail!.outDetail!) {
    //       if (element.detailId! == elementDetails.detailId &&
    //           elementDetails.preferentialType! == 6) {
    //         element.currencyDisedTaxedTotal +=
    //             num.parse(elementDetails.preferentialTotal.toString());
    //       }
    //     }
    //   });
    // }

    return detailDto;
  }

  ///删除单据
  static Future<OperationBillResponse?> deleteBill(
    BuildContext context,
    DeleteBillRequest params,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_DELETE_ORDER,
      data: params.toJson(),
    );
    return OperationBillResponse.fromMap(response.data);
  }

  static Future<void> logicalDeleteBill(
    BuildContext context,
    String vchcode,
  ) async {
    await HttpUtil.request(
      context,
      method: RequestMethod.POST_logicalDeleteBill,
      data: {"vchcode": vchcode},
    );
    return Future.value();
  }

  static Future<void> updateBillMemo(
    BuildContext context,
    String memo,
    String vchcode,
    String etypeId,
    String dtypeId,
    String summary,
  ) async {
    await HttpUtil.request(
      context,
      method: RequestMethod.POST_updateBillMemo,
      data: {
        "vchcodes": [vchcode],
        "memo": memo,
        "etypeId": etypeId,
        "dtypeId": dtypeId,
        "summary": summary,
      },
    );
    return Future.value();
  }

  //TODO 后台接口实现
  static Future<Map<String, dynamic>?> getDetailComplete(
    BuildContext context,
    String detailId,
    int vchtype,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.GET_DETAIL_COMPLETE,
      data: {"detailId": detailId, "vchtype": vchtype},
    );

    return response.data;
  }

  ///提交单据
  ///[vipId] 会员id，销售单积分抵扣资产变更用
  ///[scoreDiscount] 使用的积分数，销售单积分抵扣资产变更用
  ///[usedCardList] 会员使用的会员劵
  ///[storedValue] 储值本金
  ///[giveStoredValue] 储值赠金
  static Future<PayResult?> saveGoodsBill(
    BuildContext context,
    GoodsBillDto goodsBillDto, {
    Map? payInfo,
    required PaySaveBillEnum paySaveBillEnum,
  }) async {
    Map billDto = goodsBillDto.toJson();
    billDto["sourceTag"] = "POS";
    billDto["storeManagerId"] = SpTool.getStoreInfo()?.ownerId ?? "";

    if (paySaveBillEnum == PaySaveBillEnum.COMMON) {
      PerformanceCaptureUtil.start(PerformanceTimeName.settlement);
    } else {
      PerformanceCaptureUtil.start(PerformanceTimeName.scanPay);
    }

    ResponseModel response = await HttpUtil.request(
      context,
      method:
          paySaveBillEnum == PaySaveBillEnum.COMMON
              ? RequestMethod.SUBMIT_GOODS_BILL
              : RequestMethod.POST_PAYORDERANDSAVEBILL,
      data: {"goodsBill": billDto, "payInfo": payInfo},
      autoHandleError: false,
    );
    if (paySaveBillEnum == PaySaveBillEnum.COMMON) {
      PerformanceCaptureUtil.end(PerformanceTimeName.settlement);
    } else {
      PerformanceCaptureUtil.end(PerformanceTimeName.scanPay);
    }
    PayResult result = PayResult();
    if (paySaveBillEnum == PaySaveBillEnum.COMMON) {
      BillSaveResultDto saveResultDto = BillSaveResultDto.fromMap(
        response.data,
      );
      result.resultDTO = saveResultDto;
    } else {
      result = PayResult.fromJson(response.data);
      result.payInfo = payInfo;
      if (paySaveBillEnum == PaySaveBillEnum.SCANPAY &&
          SpTool.getSetting().openTTS &&
          (goodsBillDto.vchtype != BillTypeData[BillType.SaleBackBill] ||
              (goodsBillDto.vchtype == BillTypeData[BillType.SaleChangeBill] &&
                  num.parse(goodsBillDto.currencyBillTotal) > 0))) {
        if (PayResultType.SUCCEEDED == PayResultTypeData[result.status]) {
          PaymentDto paymentDto = goodsBillDto.payment.firstWhere(
            (element) => element.paywayType == 2,
          );
          TTSUtil()
            ..initTTS()
            ..speak("扫码支付成功收款${paymentDto.currencyAtypeTotal}元");
        }
      }
    }
    return result;
  }

  static Future<List<String>> saveGoodsBillList(
    BuildContext context,
    List<Map> request,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.SUBMIT_GOODS_BILL_LIST,
      data: request,
    );
    List data = response.data;
    return data.map((e) => e.toString()).toList();
  }

  ///单次查询支付状态
  static Future<PayResult> queryPayResult(
    BuildContext context,
    Map payInfo,
    Map<String, dynamic> billDto,
    String vchcode,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_PAYQUERYPAYRESULT,
      isLoading: false,
      data: {
        "vchcode": vchcode,
        "paywayId": payInfo["paywayId"],
        "outNo": payInfo["outNo"],
        "requestParams": {"goodsBill": billDto},
        "queryType": payInfo["queryTypeEnum"],
      },
    );
    PayResult entity = PayResult.fromJson(response.data);
    if (response.data["tradeResult"] != null) {
      Map<String, dynamic> map = json.decode(response.data["tradeResult"]);
      PayResultDtoTradeResult result = PayResultDtoTradeResult.fromJson(map);
      entity.tradeResult = result;
    }
    if (PayResultType.SUCCEEDED == PayResultTypeData[entity.status] &&
        SpTool.getSetting().openTTS &&
        payInfo["queryTypeEnum"] == "PAY") {
      TTSUtil()
        ..initTTS()
        ..speak("扫码支付成功收款${payInfo["tradeAmount"]}元");
    }
    return entity;
  }

  ///状态非1or4的时候使用
  static Future<PayResult> queryPayStatus(
    BuildContext context,
    Map payInfo,
    Map<String, dynamic> billDto,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_PAYQUERYPAYSTATUSNew,
      data: {
        "paywayId": payInfo["paywayId"],
        "outNo": payInfo["outNo"],
        "requestParams": {"goodsBill": billDto},
        "queryType": payInfo["queryTypeEnum"],
      },
    );
    PayResult entity = PayResult.fromJson(response.data);
    if (response.data["tradeResult"] != null) {
      Map<String, dynamic> map = json.decode(response.data["tradeResult"]);
      PayResultDtoTradeResult result = PayResultDtoTradeResult.fromJson(map);
      entity.tradeResult = result;
    }
    if (PayResultType.SUCCEEDED == PayResultTypeData[entity.status] &&
        SpTool.getSetting().openTTS &&
        payInfo["queryTypeEnum"] == "PAY") {
      TTSUtil()
        ..initTTS()
        ..speak("扫码支付成功收款${payInfo["tradeAmount"]}元");
    }
    return entity;
  }

  static Future<void> updateBillPayState(
    BuildContext context,
    GoodsBillDto goodsBillDto,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_UPDATEBILLPAYSTATE,
      data: goodsBillDto.toJson(),
    );
    return Future.value();
  }

  static Future<BillSaveResultDto> validaGoodsBill(
    BuildContext context,
    Map billDto,
  ) async {
    billDto["sourceTag"] = "POS";
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.VALIDA_GOODS_BILL,
      data: {"goodsBill": billDto},
    );
    return BillSaveResultDto.fromMap(response.data);
  }

  //提交商品类单据（调用的是JXC的接口）提交商品类单据（调用的是JXC的接口）
  //主要是调拨单用，因为调拨单比较特殊，不能核算，而sale中的提交单据接口中，只有在核算才校验批次和序列号。
  static Future<BillSaveResultDto> jxcSaveGoodsBill(
    BuildContext context,
    GoodsBillDto billDto,
  ) async {
    billDto.onlyPost = false;
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.JXC_SUBMIT_GOODS_BILL,
      data: billDto.toJson(),
    );
    return BillSaveResultDto.fromMap(response.data);
  }

  static Future<List<KtypeItemDto>> getKtypeList(
    //仓库
    BuildContext context,
    requestParams,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.GET_KTYPE_LIST,
      data: requestParams,
    );
    if (response.code! < 0 && null == response.data) {
      return [];
    }
    return KtypeListDto.fromMap(response.data).list ?? [];
  }

  //往来单位列表
  static Future<List<BtypeItemDto>> getBtypeList(
    BuildContext context,
    requestParams, {
    isShowProgress = true,
  }) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.GET_BTYPE_LIST,
      data: requestParams,
    );
    if (response.code! < 0 && null == response.data) {
      return [];
    }
    return BtypeListDto.fromJson(response.data).list ?? [];
  }

  ///获取商品关联的批次列表
  ///[ktypeId] 仓库id
  ///[ptypeId] 商品id
  ///[skuId] sku的id
  static Future<List<GoodsBatchDto>> getGoodsBatchList(
    BuildContext context, {
    @required String? ktypeId,
    @required String? ptypeId,
    @required String? skuId,
    @required int? costMode,
  }) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.GET_GOODS_BATCH_LIST,
      data: {
        "ktypeId": ktypeId,
        "ptypeId": ptypeId,
        "skuId": skuId,
        "costMode": costMode,
        "searchInventryBatch": costMode == 1,
        //当商品costMode为1时，此时为个别计价，需要传true，拿到批次的成本
        "showZone": false,
        "pageIndex": 1,
        "pageSize": 99999,
        "searchOut": true,
      },
    );
    return (response.data?["list"] as List?)
            ?.map((e) => GoodsBatchDto.fromJson(e))
            .toList() ??
        [];
  }

  ///根据序列号获取商品详情(不含价格等信息)
  ///用于校验输入的序列号是否已经被使用
  static Future<GoodsDetailDto?> getGoodsWithoutPriceBySn(
    BuildContext context, {
    String? scanCode,
    BillType? billType,
    String? date,
  }) {
    return HttpUtil.request(
      context,
      method: RequestMethod.GET_PTYPE_DETAIL_BY_SN_WITHOUT_PRICE,
      data: {
        "ktypeId": SpTool.getStoreInfo()!.ktypeId,
        "code": scanCode,
        "priceTypeEnum": PriceTypeEnumData[PriceTypeEnum.SALE],
        "btypeId": SpTool.getStoreInfo()!.btypeId,
        "billDate": date,
      },
    ).then((value) {
      if (null == value.code || null == value.data) {
        return null;
      }
      PtypeListModel ptype = PtypeListModel.fromMap(value.data);
      GoodsDetailDto goods = ptype.changeModel(vchtype: BillTypeData[billType]);
      if (goods.serialNoList != null && goods.serialNoList!.length > 0) {
        goods.batchNo = goods.serialNoList![0].batchNo ?? "";
      }
      return goods;
    });
  }

  ///上传钱箱存取数据
  static Future<bool> uploadCashBoxPayment(
    BuildContext context,
    CashBoxPaymentRequestDto payment,
  ) {
    if (payment.paymentType == 1 || payment.paymentType == 3) {
      payment.amount = payment.amount * -1;
    }
    payment.id = UUID.getUUid();
    return HttpUtil.request(
          context,
          method: RequestMethod.POST_CASH_BOX_PAYMENT,
          data: payment.toJson(),
        )
        .then((value) => value?.data == true)
        .onError((error, stackTrace) => false);
  }

  //验证促销是否有效
  static Future<List?> validPromotionId(
    BuildContext context,
    List<String> promotionId,
  ) {
    return HttpUtil.request(
      context,
      method: RequestMethod.GET_VALID_PROMOTION_ID,
      data: promotionId,
    ).then((value) {
      if (null == value.code || null == value.data) {
        return null;
      }
      return value.data as List?;
    });
  }

  /// 聚合支付
  /// {
  ///   "atypeId": 657040334550791333,
  ///   "authCode": "136292109991522392",
  ///   "originalAmount": 0.01,
  ///   "tradeAmount": 0.01,
  ///   "ignoreAmount": 0,
  ///   "vchcode": 682182282827576722
  ///   }
  static Future<PayResult> payOrder(BuildContext context, Map params) {
    return HttpUtil.request(
      context,
      method: RequestMethod.POST_PAYORDER,
      data: params,
      onErrorCallBack: (error) {
        return false;
      },
    ).then((value) {
      return PayResult.fromJson(value.data);
    });
  }

  static Future<PayResult> payRefund(BuildContext context, Map params) async {
    ResponseModel responseModel = await HttpUtil.request(
      context,
      method: RequestMethod.POST_PAYREFUND,
      data: params,
      onErrorCallBack: (error) {
        return false;
      },
    );
    return PayResult.fromJson(responseModel.data);
  }

  /// 聚合支付查询
  /// 参数 outNo
  static Future<Map?> payQueryPayStatus(BuildContext context, BigInt outNo) {
    return HttpUtil.request(
      context,
      method: RequestMethod.POST_PAYQUERYPAYSTATUS,
      data: outNo,
    ).then((value) {
      return value.data;
    });
  }

  ///库存查询new
  static Future<List<StockStatisticsDto>> getNventoryGoodsDistList(
    BuildContext context,
    String filterValue,
    String skuId, {
    List<String>? ktypeIds,
  }) async {
    List<StockStatisticsDto> list = [];
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_NventoryGoodsDistList,
      data: {
        "pageIndex": 1,
        "pageSize": 10000,
        "queryParams": {
          "filterValue": filterValue,
          "filterKey": "quick",
          "partypeid": "",
          "stopType": 1,
          "showSkuStop": 0,
          "priceType": 13,
          "isPC": "true",
          "ktypeIds": ktypeIds,
          "stockColumnsShowtype": [0, 2, 1, 3],
          "showStockPtype": false,
          "skuId": skuId,
        },
      },
    );
    var map = response.data['list'];
    map.forEach((item) {
      StockStatisticsDto dto = StockStatisticsDto.fromMap(item);
      dto.beforeData = item;
      list.add(dto);
    });

    return list;
  }

  ///获取积分策略
  static Future<ScoreConfiguration?> getScoreConfiguration(
    BuildContext context, {
    bool showLoading = true,
  }) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.GET_GET_SCORE_CONFIGURATION,
      requestModel: NetMethod.GET,
      isLoading: showLoading,
    );
    if (response.data == null) {
      return null;
    }
    ScoreConfiguration scoreConfiguration = ScoreConfiguration.fromJson(
      response.data,
    );
    if (scoreConfiguration.discountingModel == 1 &&
        (scoreConfiguration.ssVipScoreConfigurationFixedRule?.isNotEmpty ??
            false)) {
      ///对ssVipScoreConfigurationFixedRule的discountingProportion进行从小到大排序
      scoreConfiguration.ssVipScoreConfigurationFixedRule?.sort((a, b) {
        return a.discountingValue.compareTo(b.discountingValue);
      });
    }
    return scoreConfiguration;
  }

  ///批量自动获取商品的批次号
  static Future<List<PtypeBatchDto>> getPtypeBatch(
    BuildContext context,
    String ktypeId,
    List<PtypeBatchParamQtyDetailDto> ptypeList,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GET_PTYPE_BATCH_QTY,
      data: {
        "ktypeId": ktypeId,
        //存货点id
        "ktypePointId": 0,
        //存货点类型
        "ktypePointType": 0,
        "ptypeList": ptypeList,
      },
    );
    return (response?.data as List?)
            ?.map((e) => PtypeBatchDto.fromJson(e))
            ?.toList() ??
        [];
  }

  ///调拨入库
  ///入参见sale项目中的[GoodsTransInRequest]
  static Future<BillSaveResultDto?> goodsTransInStock(
    BuildContext context,
    Map<String, dynamic> request,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GOODS_TRANS_IN_STOCK,
      data: request,
    );
    if (response.data == null) {
      return null;
    }
    return BillSaveResultDto.fromMap(response.data);
  }

  ///获取调拨订单列表
  ///[ktypeId] 发货仓库
  ///[ktypeId2] 收货仓库
  ///[postStateList] 单据已确认=500|600 ，未确认=草稿=0
  ///[deliveryState] 发货状态，1已发货，0未发货
  static Future<List<TransferOrderDTO>> getTransferOrderList(
    BuildContext context, {
    int pageIndex = 1,
    int pageSize = 20,
    String? startTime,
    String? endTime,
    String? billNumber,
    String? ktypeId,
    String? ktypeId2,
    List<int>? postStateList,
    int? deliveryState,
  }) async {
    ResponseModel response = await HttpUtil.request(
      context,
      data: {
        "pageSize": pageSize,
        "pageIndex": pageIndex,
        "queryParams": {
          "startTime": startTime,
          "endTime": endTime,
          "billNumber": billNumber,
          "ktypeId": ktypeId,
          "ktypeId2": ktypeId2,
          "postStateList": postStateList,
          "deliveryState": deliveryState,
        },
      },
      method: RequestMethod.POST_GET_TRANSFER_ORDER_LIST,
    );
    List? list = response.data?["list"];
    return list?.map((e) => TransferOrderDTO.fromMap(e)).toList() ?? [];
  }

  ///获取订单详情/拉取新订单
  static Future<OrderBillDTO?> getOrderBill(
    BuildContext context,
    BillLoadRequest request,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      data: request.toJson(),
      method: RequestMethod.POST_GET_ORDER_BILL,
    );
    if (response.data == null) {
      return Future.value(null);
    }
    return OrderBillDTO.fromMap(response.data);
  }

  ///提交订单
  static Future<BillSaveResultDto?> submitOrderBill(
    BuildContext context,
    OrderBillDTO billDto,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_SUBMIT_ORDER_BILL,
      data: billDto.toJson(),
    );
    if (response.data == null) {
      return null;
    }
    return BillSaveResultDto.fromMap(response.data);
  }

  ///删除订单
  static Future<OperationBillResponse?> deleteOrderBill(
    BuildContext context,
    String vchcode,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_DELETE_ORDER_BILL,
      data: {"vchcode": vchcode},
    );
    if (response.data == null) {
      return null;
    }
    return OperationBillResponse.fromMap(response.data);
  }

  ///校验订单是否满足生单条件(明细生单)
  static Future<ResponseModel> checkBillByDetail(
    BuildContext context,
    CheckDetailBillRequest request,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_CHECK_BILL_BY_DETAIL,
      data: request.toJson(),
      autoHandleError: false,
    ).onError((DioError error, stackTrace) {
      if (error.response?.data != null) {
        ResponseModel entity = ResponseModel.fromJson(
          error.response!.data,
          DioManager().config,
        );
        return entity;
      }
      throw error;
    });
    return response;
  }

  ///勾选订单明细生成单据
  static Future<CreteBillResponse?> selectDetailCreateBill(
    BuildContext context,
    SelectDetailCreateBillRequest request,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_SELECT_DELETE_CREATE_BILL,
      data: request.toJson(),
    );
    if (response.data == null) {
      return null;
    }
    return CreteBillResponse.fromMap(response.data);
  }

  ///获取记录的单据消费后关联的剩余会员储值信息
  static Future<num> getVipBillStoreByVchcode(
    BuildContext context,
    String vchcode,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.POST_GET_VIP_BILL_BY_VCHCODE,
      data: vchcode,
    );
    return (response.data as Map?)?["assertTotal"] ?? 0;
  }

  static Future<Map<String, dynamic>> getBillVipScoreInfo(
    BuildContext context,
    String? vchcode,
    String? vipId,
  ) async {
    ResponseModel response = await HttpUtil.request(
      context,
      isLoading: false,
      method: RequestMethod.getBillVipScoreInfo,
      data: {"vchcode": vchcode, "vipId": vipId},
    );
    if (response.data == null) {
      return {};
    }
    return response.data;
  }
}
