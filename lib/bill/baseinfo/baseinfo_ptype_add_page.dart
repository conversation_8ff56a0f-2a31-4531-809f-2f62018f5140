import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:haloui/dto/halo_tree_node_dto.dart';
import 'package:haloui/haloui.dart';

import '../../bill/baseinfo/baseinfo_class_page.dart';
import '../../bill/entity/base_info_otype_request.dart';
import '../../bill/entity/baseinfo_request_dto.dart';
import '../../bill/model/bill_model.dart';
import '../../common/style/app_colors.dart';
import '../../common/tool/sp_tool.dart';
import '../../db/database_config.dart';
import '../../db/database_helper.dart';
import '../../db/entity/ptype.dart';
import '../../db/model/ptype_model.dart';
import '../../db/ptype_db_manager.dart';
import '../../iconfont/icon_font.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/halo_pos_label.dart';

///新增商品页面
class AddGoodsPage extends BaseStatefulPage {
  const AddGoodsPage({Key? key}) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() {
    return _AddGoodsPageState();
  }
}

class _AddGoodsPageState extends BaseStatefulPageState<AddGoodsPage> {
  List<HaloTreeNodeDto> baseInfoClass = [];

  BaseInfoPtypeAddModel model = BaseInfoPtypeAddModel();

  double viewWidth = 0;
  Alignment titleAlignment = Alignment.centerLeft;

  EdgeInsets padding = EdgeInsets.only(left: 40.w);

  TextEditingController retailPriceController = TextEditingController();
  TextEditingController vipPriceController = TextEditingController();

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 200.w, vertical: 0.h),
        child: SingleChildScrollView(
          child: Wrap(
            alignment: WrapAlignment.spaceEvenly,
            crossAxisAlignment: WrapCrossAlignment.center,
            runSpacing: 0.h,
            children: _buildContentPay(),
          ),
        ));
  }

  ///构建选中的各种支付渠道的金额输入框
  List<Widget> _buildContentPay() {
    viewWidth = ((MediaQuery.of(context).size.width - 400.w)) / 2;
    List<Widget> widgets = [
      _buildEditRow(
          required: true,
          diyWidth: viewWidth * 2,
          title: "商品名称",
          onTextChanged: (text) {
            model.name = text;
          }),
      _buildEditRow(
          title: "商品编号",
          onTextChanged: (text) {
            model.number = text;
          }),
      _buildChoseRow(title: "分类"),
      _buildEditRow(
          title: "单位",
          maxLength: 8,
          onTextChanged: (text) {
            model.unit = text;
          }),
      _buildEditNumberRow(
          title: "零售价",
          leftDivider: true,
          controller: retailPriceController,
          onTextChanged: (text) {
            model.retailPrice = text;
          }),
      _buildEditNumberRow(
          title: "会员价",
          diyWidth: viewWidth * 2,
          controller: vipPriceController,
          onTextChanged: (text) {
            model.vipPrice = text;
          }),
    ];
    //warp 需要一个垫底view做拉伸
    widgets.add(Container(
      width: double.infinity,
      height: 0.1.h,
      color: Colors.transparent,
    ));
    return widgets;
  }

  Widget _buildChoseRow({
    String title = "",
  }) {
    final height = 80.h;
    return Container(
      padding: padding,
      width: viewWidth,
      height: height,
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
              left: BorderSide(width: 1.w, color: const Color(0xFFCFCFCF)),
              bottom: BorderSide(width: 1.h, color: const Color(0xFFCFCFCF)))),
      child: GestureDetector(
        child: Row(
          children: [
            Container(
                alignment: titleAlignment,
                color: Colors.white,
                width: 140.w,
                height: height,
                child: RichText(
                    textAlign: TextAlign.end,
                    text: TextSpan(
                        text: "",
                        style: TextStyle(
                            fontSize: 20.sp,
                            color: Colors.red,
                            fontWeight: FontWeight.normal),
                        children: [
                          TextSpan(
                              text: "$title：",
                              style: TextStyle(
                                  fontSize: 24.sp,
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold)),
                        ]))),
            Expanded(
              child: Container(
                  height: height,
                  alignment: Alignment.centerLeft,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: HaloPosLabel(
                    model.classDto.fullname ?? "",
                    textAlign: TextAlign.start,
                    textStyle: TextStyle(fontSize: 24.sp),
                    maxLines: 1,
                  )),
            ),
            Container(
              width: 24.h,
              height: height,
              color: Colors.white,
              margin: EdgeInsets.only(right: 40.w),
              child: IconFont(
                IconNames.guolvxiala,
              ),
            )
          ],
        ),
        onTap: () {
          _showChoseClass();
        },
      ),
    );
  }

  Widget _buildEditRow({
    double? diyWidth,
    bool required = false,
    String title = "",
    bool leftDivider = false,
    int maxLength = 30,
    Function(String text)? onTextChanged,
  }) {
    double width = diyWidth ?? viewWidth;
    final height = 80.h;
    return Container(
        padding: padding,
        width: width,
        height: height,
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
                left: leftDivider
                    ? BorderSide(width: 1.w, color: const Color(0xFFCFCFCF))
                    : BorderSide.none,
                bottom:
                    BorderSide(width: 1.h, color: const Color(0xFFCFCFCF)))),
        child: Row(
          children: [
            Container(
                alignment: titleAlignment,
                color: Colors.white,
                height: height,
                width: 140.w,
                child: RichText(
                    textAlign: TextAlign.end,
                    text: TextSpan(
                        text: title,
                        style: TextStyle(
                            fontSize: 24.sp,
                            color: Colors.black,
                            fontWeight: FontWeight.bold),
                        children: [
                          TextSpan(
                              text: required ? " *" : "",
                              style: TextStyle(
                                  fontSize: 20.sp,
                                  color: Colors.red,
                                  fontWeight: FontWeight.normal)),
                          TextSpan(
                              text: "：",
                              style: TextStyle(
                                  fontSize: 24.sp,
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold)),
                        ]))),
            Expanded(
              child: Container(
                  width: width,
                  height: height,
                  alignment: Alignment.centerLeft,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: TextField(
                    // controller: textEditingController,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      counterText: "",
                    ),
                    textAlign: TextAlign.start,
                    style: TextStyle(fontSize: 24.sp),
                    maxLines: 1,
                    maxLength: maxLength,
                    onChanged: (text) {
                      if (onTextChanged != null) {
                        onTextChanged(text);
                      }
                    },
                  )),
            )
          ],
        ));
  }

  Widget _buildEditNumberRow({
    double? diyWidth,
    String title = "",
    bool leftDivider = false,
    TextEditingController? controller,
    Function(String text)? onTextChanged,
  }) {
    final width = diyWidth ?? viewWidth;
    final height = 80.h;
    return Container(
        padding: padding,
        width: width,
        height: height,
        alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
              left: leftDivider
                  ? BorderSide(width: 1.w, color: const Color(0xFFCFCFCF))
                  : BorderSide.none,
              bottom: BorderSide(width: 1.h, color: const Color(0xFFCFCFCF))),
        ),
        child: Row(
          children: [
            Container(
                alignment: titleAlignment,
                color: Colors.white,
                height: height,
                width: 140.w,
                child: RichText(
                    textAlign: TextAlign.end,
                    text: TextSpan(
                        text: "",
                        style: TextStyle(
                            fontSize: 20.sp,
                            color: Colors.red,
                            fontWeight: FontWeight.normal),
                        children: [
                          TextSpan(
                              text: "$title：",
                              style: TextStyle(
                                  fontSize: 24.sp,
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold)),
                        ]))),
            Expanded(
              child: Container(
                  width: width,
                  height: height,
                  alignment: Alignment.centerLeft,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: HaloNumberTextField(
                    controller: controller,
                    minWidth: width,
                    readOnly: false,
                    decimalCount: SpTool.getSystemConfig().sysDigitalPrice,
                    maxValue: SpTool.getSystemConfig().sysGlobalDecimalMax,
                    textAlign: TextAlign.start,
                    fontSize: 24.sp,
                    maxLines: 1,
                    onChanged: (text) {
                      if (onTextChanged != null) {
                        onTextChanged(text);
                      }
                    },
                  )),
            )
          ],
        ));
  }

  @override
  Widget buildBottomBody(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 200.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        border:
            Border(top: BorderSide(width: 1.h, color: const Color(0xFFCFCFCF))),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          HaloButton(
            buttonType: HaloButtonType.outlinedButton,
            outLineWidth: 2.w,
            foregroundColor: AppColors.normalTextColor,
            borderColor: AppColors.btnBorderColor,
            height: 70.h,
            width: 200.w,
            text: "取消",
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          SizedBox(
            width: 40.w,
          ),
          HaloButton(
            buttonType: HaloButtonType.elevatedButton,
            outLineWidth: 2.w,
            foregroundColor: AppColors.normalTextColor,
            borderColor: AppColors.btnBorderColor,
            height: 70.h,
            width: 200.w,
            text: "保存",
            textColor: Colors.white,
            onPressed: () {
              _doSave();
            },
          ),
        ],
      ),
    );
  }

  @override
  String getActionBarTitle() {
    return "新增商品";
  }

  @override
  Future<void>? onInitState() async {
    retailPriceController.text = model.retailPrice;
    vipPriceController.text = model.vipPrice;
    baseInfoClass = await BillModel.getBaseInfoClass(context);
    return;
  }

  bool _checkSave() {
    if (model.name.isEmpty) {
      HaloToast.showMsg(context, msg: "商品名称为空");
      return true;
    }
    return false;
  }

  Future<void> _doSave() async {
    if (_checkSave()) {
      return;
    }
    BaseInfoRequestDto baseInfoRequestDto = initBaseInfoRequestDto();
    BaseInfoOtypeRequest baseInfoOtypeRequest = initBaseInfoOtypeRequest();
    Map request = {
      "ptype": baseInfoRequestDto.toJson(),
      "otype": baseInfoOtypeRequest.toJson()
    };
    ResponseModel? responseModel;
    try {
      responseModel = await BillModel.saveBaseInfoPtype(context, request);
      if (mounted) {
        HaloToast.showMsg(context, msg: "新增商品成功");
      }
    } catch (e) {
      if (mounted) {
        HaloToast.showMsg(context, msg: responseModel?.message ?? "新增商品失败");
        setState(() {
          retailPriceController.text = model.retailPrice;
          vipPriceController.text = model.vipPrice;
        });
      }
      return;
    }
    if (responseModel.data["id"] != null) {
      await syanGoodsInfo([responseModel.data["id"]]);
    }
    if (mounted) {
      Navigator.pop(context);
    }
  }

  ///将添加的商品同步到本地
  Future<void> syanGoodsInfo(List<String> ptypeIds) async {
    PtypeAllInfo? ptypeAllInfo =
        await PtypeModel.getPtypeAllInfoByIds(context, ptypeIds);
    if (ptypeAllInfo == null) return;
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.enableLocalPtype || !dbConfig.hasPtypeData) return;
    //商品
    if (ptypeAllInfo.ptypeList?.isNotEmpty == true) {
      await PtypeDbManager.insertPtype(
          ptypeAllInfo.ptypeList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toJson()))
              .toList(growable: false),
          dbConfig.ptypeTableTimestamp);
    }
    //价格
    if (ptypeAllInfo.priceList?.isNotEmpty == true) {
      await PtypeDbManager.insertPtypePrice(
          ptypeAllInfo.priceList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toPtypePriceJson()))
              .toList(growable: false),
          dbConfig.ptypePriceTableTimestamp);
    }
    //门店价格
    if (ptypeAllInfo.otypePriceList?.isNotEmpty == true) {
      await PtypeDbManager.insertOtypePrice(
          ptypeAllInfo.otypePriceList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toOtypePriceJson()))
              .toList(growable: false),
          dbConfig.otypePriceTableTimestamp);
    }
    //权限
    if (ptypeAllInfo.ptypeLimitList?.isNotEmpty == true) {
      await PtypeDbManager.insertPtypeLimit(
          ptypeAllInfo.ptypeLimitList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toJson()))
              .toList(growable: false),
          dbConfig.ptypeLimitTableTimestamp);
    }
    //库存
    if (ptypeAllInfo.stockList?.isNotEmpty == true) {
      await PtypeDbManager.insertPtypeStock(
          ptypeAllInfo.stockList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toJson()))
              .toList(growable: false),
          dbConfig.stockTableTimestamp);
    }
    //sku
    if (ptypeAllInfo.skuList?.isNotEmpty == true) {
      await PtypeDbManager.insertPtypeSku(
          ptypeAllInfo.skuList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toJson()))
              .toList(growable: false),
          dbConfig.skuTableTimestamp);
    }
    //unit
    if (ptypeAllInfo.unitList?.isNotEmpty == true) {
      await PtypeDbManager.insertPtypeUnit(
          ptypeAllInfo.unitList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toJson()))
              .toList(growable: false),
          dbConfig.unitTableTimestamp);
    }
    //套餐
    if (ptypeAllInfo.comboDetailList?.isNotEmpty == true) {
      await PtypeDbManager.insertComboDetail(
          ptypeAllInfo.comboDetailList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toJson()))
              .toList(growable: false),
          dbConfig.comboDetailTableTimestamp);
    }
    //条码
    if (ptypeAllInfo.fullBarcodeList?.isNotEmpty == true) {
      await PtypeDbManager.insertFullBarcode(
          ptypeAllInfo.fullBarcodeList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toJson()))
              .toList(growable: false),
          dbConfig.fullBarcodeTableTimestamp);
    }
    //编码
    if (ptypeAllInfo.ptypeXcodeList?.isNotEmpty == true) {
      await PtypeDbManager.insertXcode(
          ptypeAllInfo.ptypeXcodeList!
              .map((e) => DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                  e.toJson()))
              .toList(growable: false),
          dbConfig.xcodeTableTimestamp);
    }
  }

  ///初始化店铺价格表数据
  BaseInfoOtypeRequest initBaseInfoOtypeRequest() {
    BaseInfoOtypeRequest baseInfoOtypeRequest = BaseInfoOtypeRequest();
    baseInfoOtypeRequest.type = "OtypeRetailSale";
    baseInfoOtypeRequest.priceType = 0;
    baseInfoOtypeRequest.logs = ["pos手工新增店铺零售价格本"];
    UndefinedList undefined = UndefinedList();
    undefined.createType = 4;
    undefined.fullbarcode = "";
    undefined.propFormat = "";
    undefined.propValues = "";
    undefined.skuPrice = 0;
    undefined.xcode = "";
    Unit unit = Unit();
    unit.barcode = "";
    unit.unitCode = 1;
    unit.unitRate = 1;
    unit.unitName = model.unit;

    ///上面是默认数据
    ///下面是pos添加
    undefined.pFullName = model.name;
    undefined.pUserCode = model.number.isEmpty ? null : model.number;
    undefined.saleDiscount = model.getVipPrice();
    undefined.saleOtypeVipPrice = model.getVipPrice();
    undefined.salePrice = model.getRetailPrice();
    undefined.unitRate = 1;
    undefined.unitName = model.unit;
    undefined.xtypeId = SpTool.getStoreInfo()!.otypeId;

    ///要注意此时并没有 id 、skuid ,unitid ，在商品新增成功后返回。
    undefined.ptypeId = "0";
    undefined.skuId = "0";
    undefined.unitId = "0";
    unit.id = "0";
    unit.ptypeId = "0";
    undefined.unit = unit;
    baseInfoOtypeRequest.undefinedList = [undefined];
    return baseInfoOtypeRequest;
  }

  ///初始化添加商品的一些数据
  BaseInfoRequestDto initBaseInfoRequestDto() {
    BaseInfoRequestDto baseInfoRequestDto = BaseInfoRequestDto();
    baseInfoRequestDto.allowDelUnusedSKU = false;
    baseInfoRequestDto.shortname = "";
    baseInfoRequestDto.auditState = 0;
    baseInfoRequestDto.barcode = "";
    baseInfoRequestDto.batchenabled = false;
    baseInfoRequestDto.brandId = "0";
    baseInfoRequestDto.buyDays = 0;
    baseInfoRequestDto.buyDefaultUnitCode = 1;
    baseInfoRequestDto.costMode = 0;
    baseInfoRequestDto.costPrice = 0;
    baseInfoRequestDto.fullbarcodeRuleId = "0";
    baseInfoRequestDto.fullbarcodeRuleName = "";
    Fullbarcodes fullbarcodes = Fullbarcodes();
    fullbarcodes.defaulted = true;
    fullbarcodes.id = "0";
    fullbarcodes.unitCode = 1;
    baseInfoRequestDto.fullbarcodes = [fullbarcodes];
    baseInfoRequestDto.id = "0";
    baseInfoRequestDto.invoiceFullname = "";
    baseInfoRequestDto.ktypeLimit = false;
    baseInfoRequestDto.lengthUnit = 0;
    baseInfoRequestDto.memo = "";
    baseInfoRequestDto.namepy = "";
    baseInfoRequestDto.pcategory = 0;
    baseInfoRequestDto.pics = [];
    baseInfoRequestDto.propenabled = false;
    baseInfoRequestDto.props = [];
    baseInfoRequestDto.propvalues = [];
    baseInfoRequestDto.protectDaysUnit = 0;
    baseInfoRequestDto.protectDaysView = 0;
    baseInfoRequestDto.protectWarndaysUnit = 0;
    baseInfoRequestDto.protectWarndaysView = 0;
    baseInfoRequestDto.ptypeArea = "";
    baseInfoRequestDto.ptypeBtypeRelations = [];
    baseInfoRequestDto.ptypeHeight = 0;
    baseInfoRequestDto.ptypeKtypeRelations = [];
    baseInfoRequestDto.ptypeLabels = [];
    baseInfoRequestDto.ptypeRelations = [];
    baseInfoRequestDto.ptypeType = "";
    baseInfoRequestDto.ptypeWidth = 0;
    baseInfoRequestDto.retailDefaultUnitCode = 1;
    baseInfoRequestDto.rowindex =
        DateTime.now().millisecondsSinceEpoch.toString();
    baseInfoRequestDto.saleDefaultUnitCode = 1;
    baseInfoRequestDto.skuPrice = 0;
    baseInfoRequestDto.skus = [];
    baseInfoRequestDto.snenabled = 0;
    baseInfoRequestDto.standard = "";
    baseInfoRequestDto.stockDefaultUnitCode = 1;
    baseInfoRequestDto.taxNumber = "";
    baseInfoRequestDto.taxRate = 0;
    baseInfoRequestDto.weight = 0;
    baseInfoRequestDto.weightUnit = 1;

    ///上面是默认数据
    ///下面是pos填入数据
    baseInfoRequestDto.fullname = model.name;
    baseInfoRequestDto.usercode = model.number.isNotEmpty ? model.number : null;
    baseInfoRequestDto.parFullname = model.classDto.fullname;
    baseInfoRequestDto.partypeid = model.classDto.typeid;
    Units units = Units();
    units.barcode = "";
    units.ptypeLengthUnit = 0;
    units.ptypeWeight = 0;
    units.ptypeWeightUnit = 1;
    units.retailPrice = model.getRetailPrice();
    units.unitCode = 1;
    units.unitName = model.unit;
    units.unitRate = 1;
    units.weight = 0;
    baseInfoRequestDto.units = [units];
    PriceList priceList = PriceList();
    priceList.id = "0";
    priceList.retailPrice = model.getRetailPrice();
    priceList.unitCode = 1;
    baseInfoRequestDto.priceList = [priceList];
    return baseInfoRequestDto;
  }

  _showChoseClass() {
    showDialog(
        context: context,
        builder: (context) {
          return BaeInfoClassPage(
            baseInfoClass,
            selectTypeId: model.classDto.typeid,
            onItemClick: (HaloTreeNodeDto data) {
              setState(() {
                model.classDto = data;
              });
              Navigator.pop(context);
            },
          );
        });
  }
}

class BaseInfoPtypeAddModel {
  String name = "";
  String number = "";
  HaloTreeNodeDto classDto = HaloTreeNodeDto();
  String unit = "";
  String retailPrice = "0";
  String vipPrice = "0";

  String getVipPrice() {
    return vipPrice.isEmpty ? "0" : vipPrice;
  }

  String getRetailPrice() {
    return retailPrice.isEmpty ? "0" : retailPrice;
  }
}
