import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:haloui/dto/halo_tree_node_dto.dart';
import 'package:haloui/iconfont/icon_font.dart';
import 'package:haloui/utils/color_util.dart';
import 'dart:math' as math;

/// author : huang bo
/// time   : 5/10/21 5:14 PM
/// email  : <EMAIL>
/// desc   : 树形结构组件

const Color defaultBackgroundColor = Color(0xFFFFFFFF); //默认背景色
const Color defaultSelectedColor = Color(0xFF3189FF); //默认选中颜色
const Color defaultTextColor = Color(0xFF333333); //默认字体颜色
const double defaultFontSize = 15; //默认字体大小

class BaeInfoClassPage<T> extends StatefulWidget {
  final String? selectTypeId; //选中项id
  final Color backgroundColor; //背景色
  final Color selectedColor; //选中时颜色
  final Color textColor; //默认文字颜色
  final double? fontSize; //字体大小
  final String defaultParTypeId; //默认最外层父节点id
  final Function(HaloTreeNodeDto node)? onItemClick; //点击回调
  List<HaloTreeNodeDto> nodes; //未做树形处理前所有节点
  HaloTreeNodeDto? selectedNode; //选中节点

  BaeInfoClassPage(this.nodes,
      {this.backgroundColor = defaultBackgroundColor,
        this.onItemClick,
        this.selectTypeId,
        this.defaultParTypeId = "00000",
        this.selectedColor = defaultSelectedColor,
        this.textColor = defaultTextColor,
        this.fontSize}) {
    this.nodes = createNode(nodes, defaultParTypeId);
  }

  ///创建节点
  List<HaloTreeNodeDto> createNode(List<HaloTreeNodeDto> classList, parId) {
    HaloTreeNodeDto defaultNode = HaloTreeNodeDto()
      ..fullname = "全部分类"
      ..typeid = parId
      ..id = "0"
      ..expand = true
      ..leveal = 0
      ..children = createChildNode(classList, parId);

    //获取默认选中节点
    selectedNode = (nodes.firstWhereOrNull((element) => selectTypeId == element.typeid) ??
        defaultNode);

    //根据默认选中节点展开视图
    expandedSelectedNode(selectedNode!.partypeid);

    return []..add(defaultNode);
  }

  ///创建子节点
  List<HaloTreeNodeDto> createChildNode(
      List<HaloTreeNodeDto> classList, String? parTypeId) {
    List<HaloTreeNodeDto> subList =
    classList.where((item) => item.partypeid == parTypeId).toList();
    return subList
        .map((e) => e..children = createChildNode(classList, e.typeid))
        .toList();
  }

  ///展开选中节点的父节点
  void expandedSelectedNode(String? parTypeId) {
    //选中项的父级
    HaloTreeNodeDto? partNode = nodes.firstWhereOrNull(
            (element) => element.typeid == parTypeId);
    if (partNode != null) {
      partNode.expand = true;
      expandedSelectedNode(partNode.partypeid);
    }
  }

  @override
  State<StatefulWidget> createState() {
    return _BaeInfoClassPageState(nodes, selectedNode);
  }
}

class _BaeInfoClassPageState extends State<BaeInfoClassPage> {
  List<HaloTreeNodeDto> nodes;
  HaloTreeNodeDto? selectedNode;

  _BaeInfoClassPageState(this.nodes, this.selectedNode);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.transparent,
        body: GestureDetector(
          child: Container(
            alignment: Alignment.center,
            color: Colors.transparent,
            child: Container(
              width: MediaQuery.of(context).size.width / 2,
              height: MediaQuery.of(context).size.height / 2,
              color: widget.backgroundColor,
              child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: nodes.length,
                  itemBuilder: (context, index) {
                    return buildNodeWidget(nodes[index]);
                  }),
            )
          ),
          onTap: () {
            Navigator.pop(context);
          },
        ));
  }

  ///构建节点组件
  Widget buildNodeWidget(HaloTreeNodeDto item) {
    return _HaloTreeNodeWidget(
        item, selectedNode, widget.selectedColor, widget.textColor, widget.fontSize,
            (node) {
          selectedNode = node;
          widget.onItemClick?.call(node);
          setState(() {});
        });
  }
}

class _HaloTreeNodeWidget<T> extends StatefulWidget {
  final HaloTreeNodeDto? selectedNode;
  final HaloTreeNodeDto node;
  final Color selectedColor;
  final Color textColor;
  final double? fontSize;
  final Function(HaloTreeNodeDto node) onItemClick;

  const _HaloTreeNodeWidget(this.node, this.selectedNode, this.selectedColor,
      this.textColor, this.fontSize, this.onItemClick);

  @override
  State<StatefulWidget> createState() {
    return _HaloTreeNodeWidgetState();
  }
}

class _HaloTreeNodeWidgetState extends State<_HaloTreeNodeWidget> {
  _HaloTreeNodeWidgetState();

  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
          children: <Widget>[
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                widget.onItemClick?.call(widget.node);
              },
              child: Row(children: <Widget>[
                GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      expandChild();
                    },
                    child: Container(
                      height: 36,
                      width: (widget.node.leveal! * 20 + 30).toDouble(),
                    )),
                AnimatedOpacity(
                    duration: new Duration(microseconds: 0),
                    opacity: (widget.node.children?.length ?? 0) > 0 ? 1 : 0,
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        expandChild();
                      },
                      child: Container(
                          height: 48,
                          padding: EdgeInsets.fromLTRB(0, 0, 8, 0),
                          child: Transform.rotate(
                            angle: widget.node.expand ? math.pi / 2 : 0,
                            child: HaloIconFont(
                              HaloIconNames.fenleijiantou,
                              size: 11,
                              color:
                              widget.node.typeid == widget.selectedNode!.typeid
                                  ? ColorUtil.color2String(widget.selectedColor)
                                  : "#BCBCBC",
                            ),
                          )),
                    )),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerLeft,
                    height: widget.fontSize,
                    child: Text(
                      widget.node.fullname!,
                      style: widget.node.typeid == widget.selectedNode!.typeid
                          ? TextStyle(
                          fontSize: widget.fontSize,
                          color: widget.selectedColor)
                          : TextStyle(
                          fontSize: widget.fontSize, color: widget.textColor),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 30),
                  child: HaloIconFont(
                    widget.node.typeid == widget.selectedNode!.typeid
                        ? HaloIconNames.fuxuankuangliang
                        : HaloIconNames.fuxuankuanghui,
                    size: 16,
                    color: widget.node.typeid == widget.selectedNode!.typeid
                        ? ColorUtil.color2String(widget.selectedColor)
                        : "#D5D6D7",
                  ),
                )
              ]),
            ),
            Container(
                margin: EdgeInsets.symmetric(horizontal: 30),
                height: 1,
                color: Theme.of(context).dividerColor),
            Visibility(
              visible: widget.node?.expand ?? false,
              child: Column(
                children: buildChildWidget(widget.node.children!),
              ),
            )
//        ThinDivider(),
          ],
        ));
  }

  ///展开节点
  expandChild() {
    setState(() {
      widget.node.expand = !widget.node.expand;
    });
  }

  List<Widget> buildChildWidget(List<HaloTreeNodeDto> nodes) {
    List<Widget> list = [];
    for (HaloTreeNodeDto haloNote in nodes) {
      list.add(_HaloTreeNodeWidget(
        haloNote,
        widget.selectedNode,
        widget.selectedColor,
        widget.textColor,
        widget.fontSize,
        widget.onItemClick,
      ));
    }
    return list;
  }
}
