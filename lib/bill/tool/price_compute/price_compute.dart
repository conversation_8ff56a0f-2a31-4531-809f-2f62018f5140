import '../../../common/tool/system_config_tool.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../enum/bill_ptype_pop_value_change_type.dart';
import '../decimal_display_helper.dart';

abstract class PriceCompute {
  onValueChange(GoodsDetailDto goodsDetail, PtypePopValueChangeType changeType,
      String changeValue) {
    switch (changeType) {
      case PtypePopValueChangeType.Gift:
        goodsDetail.currencyDisedTaxedPrice = 0;
        onDiscountChange(goodsDetail, "0");
        break;
      case PtypePopValueChangeType.Qty:
        onQtyChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.PRICE:
        onPriceChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.TOTAL:
        onTotalChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.DISCOUNT:
        onDiscountChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.DISCOUNT_TAXED_TOTAL:
        onDisedTaxedTotalChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.DISCOUNT_TAXED_PRICE: //折后含税单价
        onDisedTaxedPriceChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.PREFERENTIAL_SHARE:
        onPreferentialShareChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.PREFERENTIAL_DISCOUNT:
        onPreferentialDiscountChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.DISCOUNT_PRICE:
        onDiscountPriceChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.DISCOUNT_TOTAL:
        onDiscountTotalChange(goodsDetail, changeValue);
        break;
      case PtypePopValueChangeType.giftTotal:
        onGiftTotalChange(goodsDetail, changeValue);
        break;
      default:
        break;
    }
    //计算最终优惠
    onCurrencyPreferentialTotal(goodsDetail);
    //修正小数位数
    //TODO 这个地方有点问题，为什么会在最后去修复小数位数，这个时候会产生精度问题，且无法磨平
    onFixedDecimals(goodsDetail);
  }

  //不包含优惠分摊的折后单价
  onDiscountTotalChange(GoodsDetailDto goodsDetail, String changeValue);

  //不包含优惠分摊的折后单价
  onDiscountPriceChange(GoodsDetailDto goodsDetail, String changeValue);

  //总额改变
  onTotalChange(GoodsDetailDto goodsDetail, String changeValue);

  //数量变更
  onQtyChange(GoodsDetailDto goodsDetail, String changeValue);

  //界面单价变更
  onPriceChange(GoodsDetailDto goodsDetail, String changeValue);

  //折扣变更
  onDiscountChange(GoodsDetailDto goodsDetail, String changeValue);

  //优惠前优惠
  onPreferentialDiscountChange(GoodsDetailDto goodsDetail, String changeValue);

  //折后含税单价
  onDisedTaxedPriceChange(GoodsDetailDto goodsDetail, String changeValue);

  //折后含税总额
  onDisedTaxedTotalChange(GoodsDetailDto goodsDetail, String changeValue);

  //优惠分摊
  onPreferentialShareChange(GoodsDetailDto goodsDetail, String changeValue);

  //赠金优惠变更
  onGiftTotalChange(GoodsDetailDto goodsDetail, String changeValue);

  //计算最终优惠
  onCurrencyPreferentialTotal(GoodsDetailDto goodsDetail) {
    goodsDetail.currencyPreferentialTotal =
        SystemConfigTool.doubleSubtractionToDecimal(goodsDetail.currencyTotal,
            goodsDetail.currencyDisedTaxedTotal, BillDecimalType.TOTAL);
  }

  //修正小数位数，添加自定义修复位数
  onFixedDecimals(GoodsDetailDto goodsDetail,
      {int? priceFixed, int? discountFixed, int? totalFixed, int? qtyFixed}) {
    goodsDetail.costPrice = num.parse(
        DecimalDisplayHelper.getPriceFixed(goodsDetail.costPrice.toString()));
    goodsDetail.currencyPrice = num.parse(DecimalDisplayHelper.getPriceFixed(
        goodsDetail.currencyPrice.toString()));
    goodsDetail.discount = num.parse(
        DecimalDisplayHelper.getDiscountFixed(goodsDetail.discount.toString()));
    goodsDetail.preferentialDiscount = num.parse(
        DecimalDisplayHelper.getPriceFixed(
            goodsDetail.preferentialDiscount.toString()));
    goodsDetail.currencyDisedTaxedPrice = num.parse(
        DecimalDisplayHelper.getPriceFixed(
            goodsDetail.currencyDisedTaxedPrice.toString()));
    goodsDetail.currencyDisedTaxedTotal = num.parse(
        DecimalDisplayHelper.getTotalFixed(
            goodsDetail.currencyDisedTaxedTotal.toString()));
    goodsDetail.unitQty = num.parse(
        DecimalDisplayHelper.getQtyFixed(goodsDetail.unitQty.toString()));
    goodsDetail.discountPrice = num.parse(
        DecimalDisplayHelper.getPriceFixed(
            goodsDetail.discountPrice.toString()));
    goodsDetail.discountTotal = num.parse(
        DecimalDisplayHelper.getTotalFixed(
            goodsDetail.discountTotal.toString()));
    if (priceFixed != null) {
      goodsDetail.costPrice = onDecimalDisplayHelperFixed(
          goodsDetail.costPrice.toString(), priceFixed);
      goodsDetail.currencyPrice = onDecimalDisplayHelperFixed(
          goodsDetail.currencyPrice.toString(), priceFixed);
      goodsDetail.preferentialDiscount = onDecimalDisplayHelperFixed(
          goodsDetail.preferentialDiscount.toString(), priceFixed);
      goodsDetail.currencyDisedTaxedPrice = onDecimalDisplayHelperFixed(
          goodsDetail.currencyDisedTaxedPrice.toString(), priceFixed);
      goodsDetail.discountPrice = onDecimalDisplayHelperFixed(
          goodsDetail.discountPrice.toString(), priceFixed);
    }
    if (discountFixed != null) {
      goodsDetail.discount = onDecimalDisplayHelperFixed(
          goodsDetail.discount.toString(), discountFixed);
    }
    if (totalFixed != null) {
      goodsDetail.currencyDisedTaxedTotal = onDecimalDisplayHelperFixed(
          goodsDetail.currencyDisedTaxedTotal.toString(), totalFixed);
      goodsDetail.discountTotal = onDecimalDisplayHelperFixed(
          goodsDetail.discountTotal.toString(), totalFixed);
    }
    if (qtyFixed != null) {
      goodsDetail.unitQty =
          onDecimalDisplayHelperFixed(goodsDetail.unitQty.toString(), qtyFixed);
    }
  }

  num onDecimalDisplayHelperFixed(String value, int fixed) {
    return num.parse(DecimalDisplayHelper.removeTrailingZero(
        DecimalDisplayHelper.formatDecimal(value, fixed)));
  }
}
