//价格计算-启用税率 -商品含税

import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/tool/price_compute/price_compute_with_no_tax.dart';
import '../promotion/price.dart';

/// Copyright (C), 2019-2020, wsgjp.com
/// FileName:PriceComputeWithNoTax 单据价格计算类 - 商品含税并启用税率
/// Author: lidingwen
/// Date: 2020/11/13 3:38 PM
/// Description:
///

class PriceComputeWithTaxAndPtypeInTax extends PriceComputeWithNoTax {
  ///折后单价，不含税
  @override
  void calculateDisedPrice(GoodsDetailDto goodsDetail) {
    // //税率为0时，直接=折后含税单价
    // if (MathUtil.equal(goodsDetail.taxRate.toString(), "0")) {
    //   goodsDetail.currencyDisedPrice = goodsDetail.currencyDisedTaxedPrice;
    //   return;
    // }
    // //启用税率 且商品价格含税
    // goodsDetail.currencyDisedPrice = SystemConfigTool.doubleDivisionToDecimal(
    //     goodsDetail.currencyDisedTaxedPrice,
    //     MathUtil.add(
    //             "1",
    //             MathUtil.division(goodsDetail.taxRate.toString(), "100")
    //                 .toString())
    //         .toDouble(),
    //     BillDecimalType.PRICE);
    // goodsDetail.currencyDisedPrice = num.parse(DecimalDisplayHelper
    //         .getPriceFixed(goodsDetail.currencyDisedPrice.toString()));
    TaxUtil.calculateDisedPriceAndTotal(goodsDetail);
  }

  @override
  void calculateDisedTotal(GoodsDetailDto goodsDetail) {
    // if (MathUtil.equal(goodsDetail.taxRate.toString(), "0")) {
    //   //当税率为0时，直接=折后含税金额
    //   goodsDetail.currencyDisedTotal = goodsDetail.currencyDisedTaxedTotal;
    //   return;
    // }
    // goodsDetail.currencyDisedTotal = SystemConfigTool.doubleMultipleToDecimal(
    //     goodsDetail.currencyDisedPrice,
    //     goodsDetail.unitQty,
    //     BillDecimalType.TOTAL);
    TaxUtil.calculateDisedPriceAndTotal(goodsDetail);
  }
}
