import 'package:haloui/utils/math_util.dart';

import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../enum/bill_ptype_discount_tax_total_type.dart';
import '../../entity/goods_detail_dto.dart';
import '../decimal_display_helper.dart';
import 'price_compute.dart';

/// Copyright (C), 2019-2020, wsgjp.com
/// FileName:PriceComputeWithNoTax 单据价格计算类 - 不启用税率
/// Author: lidingwen
/// Date: 2020/11/13 3:38 PM
/// Description:
///
///
///

class PriceComputeWithNoTax extends PriceCompute {
  @override
  onDiscountPriceChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.discountPrice = num.parse(changeValue);
    calculateDiscountTotal(goodsDetail);
    goodsDetail.discount = SystemConfigTool.doubleDivisionToDecimal(
        goodsDetail.discountTotal,
        goodsDetail.currencyTotal,
        BillDecimalType.DISCOUNT);
    goodsDetail.preferentialDiscount =
        SystemConfigTool.doubleSubtractionToDecimal(goodsDetail.currencyTotal,
            goodsDetail.discountTotal, BillDecimalType.TOTAL);
    _calculateDiscount(goodsDetail);
  }

  ///套餐明细最后一条专用
  @override
  onDiscountTotalChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.discountTotal = num.parse(changeValue);
    goodsDetail.discountPrice = SystemConfigTool.doubleDivisionToDecimal(
        goodsDetail.discountTotal, goodsDetail.unitQty, BillDecimalType.TOTAL);
    goodsDetail.preferentialDiscount =
        SystemConfigTool.doubleSubtractionToDecimal(goodsDetail.currencyTotal,
            goodsDetail.discountTotal, BillDecimalType.TOTAL);
  }

  @override
  onTotalChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.currencyTotal = num.parse(changeValue);
    calculatePrice(goodsDetail);
  }

  @override
  onQtyChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.unitQty = num.parse(changeValue);
    calculateDiscountTotal(goodsDetail);
    //总金额
    _calculateTotal(goodsDetail);
    //优惠前优惠
    _calculatePreferentialDiscount(goodsDetail);
    //现价不变，计算小计
    _calculateDisedTaxedTotal(goodsDetail, CalculateType.QTY);
    //折后不含税单价
    calculateDisedPrice(goodsDetail);
    //折后不含税金额
    calculateDisedTotal(goodsDetail);
    //税额
    _calculateTaxTotal(goodsDetail);
    //其他----成本总价
    _calculateCostTotal(goodsDetail);
    //折扣
    // _calculateDiscount(goodsDetail);
    //最终折扣
    _calculateLastDiscount(goodsDetail);
  }

  @override
  onDiscountChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.discount = num.parse(changeValue);
    goodsDetail.orgDiscount = goodsDetail.discount;
    //界面金额
    _calculateTotal(goodsDetail);

    calculateDiscountTotal(goodsDetail, calculateType: CalculateType.DISCOUNT);

    calculateDiscountPrice(goodsDetail);
    //小计
    _calculateDisedTaxedTotal(goodsDetail, CalculateType.DISCOUNT);
    //现价
    _calculateDisedTaxedPrice(goodsDetail);
    // 折后不含税
    calculateDisedPrice(goodsDetail);
    calculateDisedTotal(goodsDetail);
    //税额
    _calculateTaxTotal(goodsDetail);
    //优惠前优惠
    _calculatePreferentialDiscount(goodsDetail);
    //最终折扣
    _calculateLastDiscount(goodsDetail);
    onQtyChange(goodsDetail, goodsDetail.unitQty.toString());
  }

  @override
  onDisedTaxedPriceChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.currencyDisedTaxedPrice = num.parse(changeValue);
    goodsDetail.discountPrice = goodsDetail.currencyDisedTaxedPrice;
    calculateDiscountTotal(goodsDetail);
    goodsDetail.discount = SystemConfigTool.doubleDivisionToDecimal(
        goodsDetail.discountTotal,
        goodsDetail.currencyTotal,
        BillDecimalType.DISCOUNT);
    //界面总额
    _calculateTotal(goodsDetail);
    //小计
    _calculateDisedTaxedTotal(goodsDetail, CalculateType.DISCOUNT_PRICE);
    //折扣
    _calculateDiscount(goodsDetail);
    //折后不含税
    calculateDisedPrice(goodsDetail);
    calculateDisedTotal(goodsDetail);
    //税额
    _calculateTaxTotal(goodsDetail);
    //优惠前优惠
    _calculatePreferentialDiscount(goodsDetail);
    //最终折扣
    _calculateLastDiscount(goodsDetail);
  }

  @override
  onDisedTaxedTotalChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.currencyDisedTaxedTotal = num.parse(changeValue);
    //折后含税
    _calculateDisedTaxedPrice(goodsDetail, price: true);
    //折扣
    // _calculateDiscount(goodsDetail);
    if (MathUtil.compare(goodsDetail.discount.toString(), "10")) {
      //还原数据
      _resetMaxDiscount(goodsDetail);
      _calculatePreferentialDiscount(goodsDetail);
      return;
    }
    //折后不含税
    calculateDisedPrice(goodsDetail);
    calculateDisedTotal(goodsDetail);
    //税额
    _calculateTaxTotal(goodsDetail);

    _calculatePreferentialDiscount(goodsDetail);
    _calculateLastDiscount(goodsDetail);
  }

  ///优惠分摊被更改
  @override
  void onPreferentialShareChange(
      GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.currencyOrderPreferentialAllotTotal = num.parse(changeValue);
    //小计
    _calculateDisedTaxedTotal(goodsDetail, CalculateType.PREFERENTIAL_SHARE);
    //现价
    _calculateDisedTaxedPrice(goodsDetail);
    //折后不含税
    calculateDisedPrice(goodsDetail);
    calculateDisedTotal(goodsDetail);
    //税额
    _calculateTaxTotal(goodsDetail);
    //最终折扣
    _calculateLastDiscount(goodsDetail);
  }

  ///赠金更改
  @override
  onGiftTotalChange(GoodsDetailDto goodsDetail, String changeValue) {
    ///赠金优惠金额（本币）
    goodsDetail.givePreferentialTotal = num.parse(changeValue);

    ///赠金优惠金额（外币） 目前保持和本币一致
    goodsDetail.currencyGivePreferentialTotal = num.parse(changeValue);

    ///小计
    _calculateDisedTaxedTotal(goodsDetail, CalculateType.giftTotal);

    ///现价
    _calculateDisedTaxedPrice(goodsDetail);

    ///折后不含税单价
    calculateDisedPrice(goodsDetail);

    ///折后不含税金额
    calculateDisedTotal(goodsDetail);

    ///税额
    _calculateTaxTotal(goodsDetail);

    //最终折扣
    _calculateLastDiscount(goodsDetail);
  }

  @override
  onPriceChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.currencyPrice =
        num.parse(DecimalDisplayHelper.getPriceFixed(changeValue));
    _calculateTotal(goodsDetail);
    goodsDetail.discountPrice = goodsDetail.currencyPrice;
    calculateDiscountTotal(goodsDetail);
    _calculateDiscount(goodsDetail);
    _calculateDisedTaxedPrice(goodsDetail, price: true);
    onQtyChange(goodsDetail, goodsDetail.unitQty.toString());
  }

  ///优惠前优惠(打折优惠) 被更改
  @override
  onPreferentialDiscountChange(GoodsDetailDto goodsDetail, String changeValue) {
    goodsDetail.preferentialDiscount = MathUtil.stringToDouble(
        DecimalDisplayHelper.getTotalFixed(changeValue))!;
    //界面总额
    _calculateTotal(goodsDetail);
    //优惠分摊改变，
    //折后含税总额 = 总额 - 优惠前优惠 - 优惠分摊总和
    //           = 总额 - 最终优惠
    //todo 这里总金额不变，但是优惠前优惠更改，那么优惠分摊总和难道不会改吗？这里计算下去结果是不是有问题？
    _calculateDisedTaxedTotal(goodsDetail, CalculateType.PREFERENTIAL_DISCOUNT);
    //现价
    _calculateDisedTaxedPrice(goodsDetail);
    //折后不含税单价
    calculateDisedPrice(goodsDetail);
    //折后不含税金额
    calculateDisedTotal(goodsDetail);
    //税额
    _calculateTaxTotal(goodsDetail);
    //折扣
    _calculateDiscount(goodsDetail);
    //最终折扣
    _calculateLastDiscount(goodsDetail);
  }

  ///计算折后金额
  void calculateDiscountTotal(GoodsDetailDto goodsDetail,
      {CalculateType? calculateType}) {
    //折扣发生改变
    if (calculateType == CalculateType.DISCOUNT) {
      goodsDetail.discountTotal = SystemConfigTool.doubleMultipleToDecimal(
          goodsDetail.currencyTotal,
          goodsDetail.discount,
          BillDecimalType.TOTAL);
    } else {
      goodsDetail.discountTotal = SystemConfigTool.doubleMultipleToDecimal(
          goodsDetail.discountPrice,
          goodsDetail.unitQty,
          BillDecimalType.TOTAL);
    }
  }

  ///当精度特别小，且位数不一致导致有些数据已取舍，会让单价出现波动
  static calculateDiscountPrice(GoodsDetailDto goods) {
    if (goods.discount == 1) {
      goods.discountPrice = goods.currencyPrice;
    } else {
      goods.discountPrice = SystemConfigTool.doubleDivisionToDecimal(
          goods.discountTotal, goods.unitQty, BillDecimalType.PRICE);
    }
  }

  //重设折扣最大值为10
  _resetMaxDiscount(GoodsDetailDto goodsDetail) {
    //Fluttertoast.showToast(msg: "折扣不能大于10");
    String maxDiscountDisedTaxedTotal =
        SystemConfigTool.doubleSubtractionToDecimal(
                SystemConfigTool.doubleMultipleToDecimal(
                    goodsDetail.currencyTotal, 10, BillDecimalType.TOTAL),
                goodsDetail.currencyOrderPreferentialAllotTotal,
                BillDecimalType.TOTAL)
            .toString();
    onDisedTaxedTotalChange(goodsDetail, maxDiscountDisedTaxedTotal);
  }

  ///界面总额
  ///总额 = [currencyPrice] 单价 * [unitQty] 单位数量
  void _calculateTotal(GoodsDetailDto goodsDetail) {
    goodsDetail.currencyTotal = SystemConfigTool.doubleMultipleToDecimal(
        goodsDetail.currencyPrice,
        goodsDetail.unitQty.toDouble(),
        BillDecimalType.TOTAL);
  }

  //成本
  void _calculateCostTotal(GoodsDetailDto goodsDetail) {
    goodsDetail.costTotal = SystemConfigTool.doubleMultipleToDecimal(
        goodsDetail.costPrice, goodsDetail.unitQty, BillDecimalType.TOTAL);
  }

  ///折后单价，不含税
  ///对于不含税商品，就是就是折后含税单价(现价)
  void calculateDisedPrice(GoodsDetailDto goodsDetail) {
    goodsDetail.currencyDisedPrice = goodsDetail.currencyDisedTaxedPrice;
  }

  void calculateDisedTotal(GoodsDetailDto goodsDetail) {
    goodsDetail.currencyDisedTotal = goodsDetail.currencyDisedTaxedTotal;
  }

  ///折后含税金额
  void _calculateDisedTaxedTotal(
      GoodsDetailDto goodsDetail, CalculateType type) {
    //todo 数量/折后单价/折扣发生改变，计算都是不考虑后续其他优惠的。
    switch (type) {
      //数量或折后单价发生改变
      case CalculateType.QTY:
      case CalculateType.DISCOUNT_PRICE:
        goodsDetail.currencyDisedTaxedTotal =
            SystemConfigTool.doubleMultipleToDecimal(
                goodsDetail.currencyDisedTaxedPrice,
                goodsDetail.unitQty,
                BillDecimalType.TOTAL);
        break;
      //分摊前优惠改变
      case CalculateType.PREFERENTIAL_DISCOUNT:
        goodsDetail.currencyDisedTaxedTotal =
            SystemConfigTool.doubleSubtractionToDecimal(
                goodsDetail.currencyTotal,
                goodsDetail.preferentialDiscount,
                BillDecimalType.TOTAL);
        break;
      //折扣发生改变
      case CalculateType.DISCOUNT:
        goodsDetail.currencyDisedTaxedTotal =
            SystemConfigTool.doubleMultipleToDecimal(goodsDetail.currencyTotal,
                goodsDetail.discount, BillDecimalType.TOTAL);
        break;
      case CalculateType.PREFERENTIAL_SHARE:
      case CalculateType.giftTotal:
        //优惠分摊改变，
        //折后含税总额 = 总额 - 优惠前优惠 - 优惠分摊总和-赠金优惠
        //           = 总额 - 最终优惠

        //最终优惠 = 分摊前优惠 + 优惠分摊总和 + 赠金优惠
        goodsDetail.currencyPreferentialTotal = (MathUtil.addDec(
                    goodsDetail.preferentialDiscount,
                    goodsDetail.currencyOrderPreferentialAllotTotal) +
                MathUtil.parseToDecimal(
                    goodsDetail.currencyGivePreferentialTotal.abs().toString()))
            .toDouble();

        // //不包含赠金的优惠 = 分摊前优惠 + 优惠分摊总和
        // goodsDetail.posPreferentialTotalExcludeGiveTotal = MathUtil.addDec(
        //         goodsDetail.preferentialDiscount,
        //         goodsDetail.currencyOrderPreferentialAllotTotal)
        //     .toDouble();

        //不含赠金的小计
        goodsDetail.currencyDisedTaxedTotal =
            SystemConfigTool.doubleSubtractionToDecimal(
                goodsDetail.currencyTotal,
                goodsDetail.currencyPreferentialTotal,
                BillDecimalType.TOTAL);

        //包含赠金的小计
        goodsDetail.posCurrencyDisedTaxedTotal =
            SystemConfigTool.doubleAddToDecimal(
                goodsDetail.currencyDisedTaxedTotal,
                goodsDetail.currencyGivePreferentialTotal,
                BillDecimalType.TOTAL);
        break;
    }
  }

  ///计算折后含税单价
  ///[price] 是否直接根据折扣来计算现价
  void _calculateDisedTaxedPrice(GoodsDetailDto goodsDetail,
      {bool price = false}) {
    if (price) {
      //单价 * 折扣，这种情况下，是不考虑优惠分摊金额的
      goodsDetail.currencyDisedTaxedPrice =
          SystemConfigTool.doubleMultipleToDecimal(goodsDetail.currencyPrice,
              goodsDetail.discount, BillDecimalType.PRICE);
    } else {
      //没有折扣和最终折扣为0，则单价就是折后含税单价
      if (goodsDetail.discount == 1 &&
          goodsDetail.currencyPreferentialTotal == 0) {
        goodsDetail.currencyDisedTaxedPrice = goodsDetail.currencyPrice;
      }
      //有折扣，则由 折后含税总额 / 单位数量 得到折后含税单价
      else {
        goodsDetail.currencyDisedTaxedPrice =
            SystemConfigTool.doubleDivisionToDecimal(
                goodsDetail.currencyDisedTaxedTotal,
                goodsDetail.unitQty,
                BillDecimalType.PRICE);
        goodsDetail.posCurrencyDisedTaxedPrice =
            SystemConfigTool.doubleDivisionToDecimal(
                goodsDetail.posCurrencyDisedTaxedTotal,
                goodsDetail.unitQty,
                BillDecimalType.PRICE);
      }
    }
    //小数位数
    goodsDetail.currencyDisedTaxedPrice = num.parse(
        DecimalDisplayHelper.getPriceFixed(
            goodsDetail.currencyDisedTaxedPrice.toString()));
  }

  ///计算税额
  ///折后含税总额 - 折后总额
  ///对于不含税的计算，则为0（因为两者一样）
  void _calculateTaxTotal(GoodsDetailDto goodsDetail) {
    goodsDetail.currencyTaxTotal = SystemConfigTool.doubleSubtractionToDecimal(
        goodsDetail.currencyDisedTaxedTotal,
        goodsDetail.currencyDisedTotal,
        BillDecimalType.TOTAL);
  }

  ///折扣
  ///[preferentialDiscount] 是否根据优惠前优惠（打折的优惠金额）来计算折扣
  void _calculateDiscount(GoodsDetailDto goodsDetail,
      {bool preferentialDiscount = false}) {
    //由折后含税金额 / 总金额 得到折扣
    //这里如果金额直接为0，计算结果如何？是NaN还是抛异常？ 看代码可知结果为0
    goodsDetail.discount = SystemConfigTool.doubleDivisionToDecimal(
        goodsDetail.discountTotal,
        goodsDetail.currencyTotal,
        BillDecimalType.DISCOUNT);
    //如果金额为0，则上面计算出来折扣为0，则此时将折扣为0，
    //还有另外一种情况，就是现价为0，但是单价不为0，算出来折扣也是0
    if (goodsDetail.discount == 0 && goodsDetail.currencyTotal == 0) {
      goodsDetail.discount = 1;
    }
    //若优惠前优惠（打折的优惠金额）为0，则认为没有打折
    if (preferentialDiscount && goodsDetail.preferentialDiscount == 0) {
      goodsDetail.discount = 1;
    }
    goodsDetail.discount = num.parse(
        DecimalDisplayHelper.getDiscountFixed(goodsDetail.discount.toString()));
    goodsDetail.orgDiscount = goodsDetail.discount;
  }

  ///最终折扣
  @Deprecated("前后端无需展示最终折扣，去掉")
  void _calculateLastDiscount(GoodsDetailDto goodsDetail) {
    //todo 这里计算折扣的方式和[_calculateDiscount()]中的是一样的，有什么意义？
    goodsDetail.lastDiscount = SystemConfigTool.doubleDivisionToDecimal(
        goodsDetail.posCurrencyDisedTaxedTotal,
        goodsDetail.currencyTotal,
        BillDecimalType.DISCOUNT);
    goodsDetail.lastDiscount = num.parse(DecimalDisplayHelper.getDiscountFixed(
        goodsDetail.lastDiscount.toString()));
  }

  //优惠前优惠
  void _calculatePreferentialDiscount(GoodsDetailDto goodsDetail) {
    goodsDetail.preferentialDiscount =
        SystemConfigTool.doubleSubtractionToDecimal(goodsDetail.currencyTotal,
            goodsDetail.discountTotal, BillDecimalType.TOTAL);
  }

  //界面单价
  void calculatePrice(GoodsDetailDto goodsDetail) {
    goodsDetail.currencyPrice = SystemConfigTool.doubleDivisionToDecimal(
        goodsDetail.currencyTotal, goodsDetail.unitQty, BillDecimalType.TOTAL);
  }
}
