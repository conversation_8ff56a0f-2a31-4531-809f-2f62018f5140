import 'package:event_bus/event_bus.dart';

/// 创建时间：2/2/23
/// 作者：xiaotiaochong
/// 描述：

class SaleEventBus {
  static String updateSaleBillView = "updateSaleBillView";
  static String updateVip = "updateVip";
  static String updateGoodsDetail = "updateGoodsDetail";
  static String doClear = "doClear";

  ///针对搜索商品开单页面，刷新搜索列表
  static String refreshSearchedGoods = "refreshSearchedGoods";

  static final EventBus _eventBus = EventBus();

  static EventBus getInstance() {
    return _eventBus;
  }
}
