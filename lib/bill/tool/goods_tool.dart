import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';

import '../entity/goods_detail_dto.dart';

///商品工具类
class GoodsTool {
  GoodsTool._();

  ///商品是否是套餐
  static bool isComboRow(GoodsDetailDto goods) {
    return goods.comboRow || goods.pcategory == 2;
  }

  ///是否是序列号商品
  static bool isSerialGoods(GoodsDetailDto goodsDetailDto) {
    return goodsDetailDto.snenabled == 1 || //非严格
        goodsDetailDto.snenabled == 2; //严格
  }

  ///是否是套餐明细
  static bool isComboDetail(GoodsDetailDto goods) {
    return (StringUtil.isNotZeroOrEmpty(goods.comboId) && !isComboRow(goods)) ||
        StringUtil.isNotZeroOrEmpty(goods.comboRowParId);
  }

  ///检查商品是否关联批次
  ///对于批次分为两种情况，
  ///1.批次号不为空
  ///2.批次号为空，但是生产日期，保质期，过期日期不为空
  static bool isGoodsBindWithBatch(GoodsDetailDto goods) {
    if (goods.batchenabled != true) {
      return false;
    }
    //批次号为空
    if (TextUtil.isEmpty(goods.batchNo) ) {
      if (TextUtil.isEmpty(goods.produceDate) ||
          TextUtil.isEmpty(goods.expireDate) ||
          (goods.protectDays ?? 0) <= 0) {
        return !TextUtil.isEmpty(goods.batchId);
      }
    }
    return true;
  }

  ///获取套餐明细行
  static List<GoodsDetailDto> getComboDetailsFromGoodsList(
    List<GoodsDetailDto> goodsList,
    GoodsDetailDto combo,
  ) {
    if (isComboRow(combo)) {
      return goodsList
          .where((goods) => goods.comboRowParId == combo.comboRowId)
          .toList();
    }
    return [];
  }

  static List<GoodsDetailDto> getComboDetailsFromMap(
    GoodsDetailDto goods,
    Map<String, List<GoodsDetailDto>> comboDetailsMap,
  ) {
    if (isComboRow(goods)) {
      return comboDetailsMap[goods.comboRowId] ?? [];
    }
    return [];
  }

  static bool compareUnitSkuById({
    required String ptypeId1,
    required String skuId1,
    required String unitId1,
    required String ptypeId2,
    required String skuId2,
    required String unitId2,
    required bool isCombo,
    bool compareSku = true,
  }) {
    if (!StringUtil.equal(ptypeId1, ptypeId2)) {
      return false;
    }
    if (!isCombo) {
      if (!StringUtil.equal(unitId1, unitId2)) {
        return false;
      }
      if (compareSku && !StringUtil.equal(skuId1, skuId2)) {
        return false;
      }
    }
    return true;
  }

  ///判断unitSku是否一致
  ///1.ptypeId、unitId一致
  ///2.对于非套餐商品，还需要判断skuId一致
  ///3.比较单价
  ///[comparePrice] 是否比较单价，对于套餐明细，应该比较他们的比例而非单价
  static bool compareUnitSku(
    GoodsDetailDto goods1,
    GoodsDetailDto goods2, {
    bool comparePrice = true,
    bool compareSku = true,
  }) {
    if (!compareUnitSkuById(
      ptypeId1: goods1.ptypeId,
      skuId1: goods1.skuId ?? "",
      unitId1: goods1.unitId,
      ptypeId2: goods2.ptypeId,
      skuId2: goods2.skuId ?? "",
      unitId2: goods2.unitId,
      isCombo: isComboRow(goods1) && isComboRow(goods2),
      compareSku: compareSku,
    )) {
      return false;
    }
    if (comparePrice && goods1.currencyPrice != goods2.currencyPrice) {
      return false;
    }
    return true;
  }

  ///判断是否是同一批次的商品
  ///同一个商品，则ptypeId相等，skuId相等，unitId相等，并且属于同一批次
  ///若二者都不是批次商品，则无需判断批次
  ///对于批次判断，分为两种情况，
  ///1.批次号为空，此时判断生产日期，保质期，过期日期，对于个别计价批次还需要判断批次成本是否相等
  ///2.批次号不为空，则直接判断批次号
  ///[isCompareUnitSku] 是否判断unitSku一致，批次仅绑定了sku，和unit无关
  ///[comparePrice] 是否比较单价，对于套餐明细，应该比较他们的比例而非单价
  static bool compareBatch(
    GoodsDetailDto goods1,
    GoodsDetailDto goods2, {
    bool isCompareUnitSku = true,
    bool comparePrice = true,
  }) {
    if (isCompareUnitSku &&
        !compareUnitSku(goods1, goods2, comparePrice: comparePrice)) {
      return false;
    }
    if (goods1.batchenabled != goods2.batchenabled) {
      return false;
    }
    if (goods1.batchenabled && goods2.batchenabled) {
      //保质期批次允许无批次号
      if (StringUtil.isZeroOrEmpty(goods1.batchNo) &&
          StringUtil.isZeroOrEmpty(goods2.batchNo)) {
        //生产日期不同,由于保质期挂在商品表中，意思就是同一个商品保质期必定一样，所以只需要判断生产日期
        if (!StringUtil.equal(
          goods1.produceDate ?? "",
          goods2.produceDate ?? "",
        )) {
          return false;
        }
        //批次和sku绑定，无批次号，所以需要判断sku
        if (!StringUtil.equal(goods1.ptypeId, goods2.ptypeId) ||
            !StringUtil.equal(goods1.skuId ?? "", goods2.skuId ?? "")) {
          return false;
        }
      }
      if (!StringUtil.equal(goods1.batchNo, goods2.batchNo)) {
        return false;
      }
      //若商品为个别计价商品，还需要判断batchPrice批次单价(成本)
      if (goods1.costMode == 1 && goods2.costMode == 1) {
        return goods1.batchPrice == goods2.batchPrice;
      } else if (goods1.costMode == 1 || goods2.costMode == 1) {
        return false;
      }
    }
    return true;
  }

  ///是否是同一套餐
  ///套餐id相同
  ///[isCompareUnitSku] 是否判断unitSku一致
  static bool compareComboRow(
    GoodsDetailDto goods1,
    GoodsDetailDto goods2, {
    bool isCompareUnitSku = true,
  }) {
    if (isComboRow(goods1) && isComboRow(goods2)) {
      //若套餐id不同，则必定不是同一个商品或套餐
      if (!StringUtil.equal(goods1.comboId, goods2.comboId)) {
        return false;
      }
      if (isCompareUnitSku && !compareUnitSku(goods1, goods2)) {
        return false;
      }
    }
    return isComboRow(goods1) == isComboRow(goods2);
  }

  ///判断是否是同一套餐明细
  ///1.untiSku相同
  ///2.分摊比例相同
  ///[isCompareUnitSku] 是否判断unitSku一致
  static bool compareComboDetail(
    GoodsDetailDto goods1,
    GoodsDetailDto goods2, {
    bool isCompareUnitSku = true,
  }) {
    if (isComboDetail(goods1) && isComboDetail(goods2)) {
      if (!StringUtil.equal(goods1.comboId, goods2.comboId)) {
        return false;
      }
      if (isCompareUnitSku &&
          !compareUnitSku(goods1, goods2, comparePrice: false)) {
        return false;
      }
      if (goods1.comboShareScale != goods2.comboShareScale) {
        return false;
      }
    }
    return isComboDetail(goods1) == isComboDetail(goods2);
  }

  ///筛选商品列表，并且拿到套餐和套餐明细
  ///addWhenFalse: 当filter返回false时，是否将其添加到套餐/套餐明细/普通商品中
  static List<GoodsDetailDto> filterGoodsListAndGetCombo(
    List<GoodsDetailDto> goodsList, {
    Map<String, GoodsDetailDto>? comboMap,
    Map<String, List<GoodsDetailDto>>? comboDetailsMap,
    List<GoodsDetailDto>? normalGoodsList,
    bool Function(GoodsDetailDto)? filter,
    bool addWhenFalse = false,
  }) {
    return goodsList.where((goods) {
      bool result = true;
      if (filter != null) {
        result = filter(goods);
      }
      if (result || addWhenFalse) {
        //套餐行
        if (comboMap != null && GoodsTool.isComboRow(goods)) {
          comboMap[goods.comboRowId] = goods;
        }
        //明细行
        else if (comboDetailsMap != null && GoodsTool.isComboDetail(goods)) {
          comboDetailsMap.putIfAbsent(goods.comboRowParId, () => []).add(goods);
        }
        //普通商品
        else if (normalGoodsList != null) {
          normalGoodsList.add(goods);
        }
      }
      return result;
    }).toList();
  }
}
