import 'dart:math';

import 'package:decimal/decimal.dart';

import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/preferential_dto.dart';
import '../../settlement/entity/score_configuration.dart';
import 'preferential.dart';

//region 枚举
//region 门槛类型
///门槛类型
enum ScoreDiscountThresholdType {
  ///无门槛
  none,

  ///最低消费门槛
  threshold,
}

extension ScoreDiscountThresholdTypeExtension on ScoreDiscountThresholdType {
  int get value {
    switch (this) {
      case ScoreDiscountThresholdType.none:
        return 1;
      case ScoreDiscountThresholdType.threshold:
        return 2;
    }
  }
}

//endregion 门槛类型
//region 积分抵扣上限类型
///积分抵扣上限类型
enum ScoreDiscountLimitType {
  ///无限制
  none,

  ///每笔交易最多抵扣x元
  maxRmb,

  ///每笔交易最多抵扣x%
  maxPercent,
}

extension ScoreDiscountLimitTypeExtension on ScoreDiscountLimitType {
  int get value {
    switch (this) {
      case ScoreDiscountLimitType.none:
        return 1;
      case ScoreDiscountLimitType.maxRmb:
        return 2;
      case ScoreDiscountLimitType.maxPercent:
        return 3;
    }
  }
}
//endregion 积分抵扣上限类型
//endregion 枚举

//region 积分抵扣处理类
///积分抵扣处理类
class ScoreDiscountHandler with BillPreferentialMixin {
  @override
  Preferential get preferential => Preferential.scoreDiscount;

  ///处理积分抵扣
  ///[useMaxScore] 是否使用最大积分数量
  ///[enableScore] 是否开启积分抵扣
  ///[scoreUse] 使用积分数量
  ///返回使用的积分数量
  int handle(
    VipWithLevelAssertsRightsCardDTO? vipInfo,
    GoodsBillDto bill,
    bool enableScore,
    bool useMaxScore,
    int scoreUse,
    ScoreConfiguration? scoreConfiguration,
  ) {
    bill.preferentialHelp.remove(preferential.name);
    //套餐明细
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    //要执行分摊的商品列表
    List<GoodsDetailDto> goodsList = [];
    //执行积分抵扣前的金额
    num total = filterGoodsListAndCalculateTotal(
        bill.outDetail, goodsList, comboDetailsMap);
    if (vipInfo == null ||
        !enableScore ||
        scoreConfiguration == null ||
        total <= 0) return 0;
    //未达到门槛
    if (!ScoreDiscountUtil.allowScoreDiscountByTotal(
        total, scoreConfiguration)) {
      return 0;
    }
    num money = 0;
    if (scoreConfiguration.discountingModel == 1) {
      //最大可抵扣金额
      num scoreMoney = ScoreDiscountUtil.getUserMoney(
          vipInfo.asserts?.availableScore ?? 0,
          Decimal.parse(total.toString()),
          scoreConfiguration);
      if (scoreUse == 0) {
        money = scoreMoney;
      } else {
        //获取积分抵扣金额
        money = ScoreDiscountUtil.getFixedRatioMoneyForScore(
            scoreUse, scoreConfiguration);
        money = min(money, scoreMoney);
      }
      //获取积分抵扣金额
      scoreUse = ScoreDiscountUtil.getFixedRatioScoreForMoney(
          money, scoreConfiguration);
    } else {
      // //获取当前能使用的最大积分数量
      // int maxScore = ScoreDiscountUtil.getMaxUsableScore(enableScore,
      //     Decimal.parse(total.toString()), scoreConfiguration, vipInfo);
      // if (useMaxScore) {
      //   scoreUse = maxScore;
      // } else {
      //   scoreUse = min(scoreUse, maxScore);
      // }
      if (scoreUse > 0) {
        //获取积分抵扣金额
        money = ScoreDiscountUtil.getMoneyByScore(scoreUse, scoreConfiguration);
      }
    }
    money = min(money, total);
    if (money > 0) {
      sharePreferentialToGoods(
          bill: bill,
          goodsList: goodsList,
          billPreferential: money,
          goodsListTotal: total,
          comboDetailsMap: comboDetailsMap);
      bill.preferentialHelp[preferential.name]?.value = scoreUse;
    }

    return scoreUse;
  }
}

//endregion 积分抵扣处理类

//region 积分抵扣工具类
///积分抵扣工具类
class ScoreDiscountUtil {
  ScoreDiscountUtil._();

  ///通过抵扣金额计算积分(向下取整)
  static int getScoreByMoney(
      Decimal total, ScoreConfiguration scoreConfiguration) {
    try {
      return (total *
              Decimal.fromInt(scoreConfiguration.discountingProportion!) /
              Decimal.parse(scoreConfiguration.discountingValue!.toString()))
          .floor()
          .toInt();
    } catch (e) {
      return 0;
    }
  }

  ///通过积分计算抵扣金额
  static num getMoneyByScore(int score, ScoreConfiguration scoreConfiguration) {
    try {
      return SystemConfigTool.doubleDivisionToDecimal(
          (Decimal.fromInt(score) *
                  Decimal.parse(scoreConfiguration.discountingValue.toString()))
              .toDouble(),
          scoreConfiguration.discountingProportion!,
          BillDecimalType.TOTAL);
    } catch (e) {
      return 0;
    }
  }

  ///获取当前能使用的最大积分
  ///[checkScoreEnable] 是否
  static int getMaxUsableScore(
      bool checkScoreEnable,
      Decimal billTotal,
      ScoreConfiguration scoreConfiguration,
      VipWithLevelAssertsRightsCardDTO vipInfo) {
    //如果没有达到积分抵扣的最低消费门槛
    if (!checkScoreEnable) {
      return 0;
    }
    //积分抵现金额上限类型
    // 1.无限制；
    // 2.每笔交易最多抵扣x元；
    // 3.每笔交易最多抵扣x%
    if (scoreConfiguration.discountingLimitType ==
        ScoreDiscountLimitType.maxRmb.value) {
      //固定抵扣的最多金额
      Decimal temp = Decimal.parse(
          (scoreConfiguration.discountingLimitRmb ?? 0).toString());
      //两者取最小值
      if (billTotal > temp) {
        billTotal = temp;
      }
    } else if (scoreConfiguration.discountingLimitType ==
        ScoreDiscountLimitType.maxPercent.value) {
      //按百分比取值
      billTotal = billTotal *
          (Decimal.parse(
                      scoreConfiguration.discountingLimitPercent?.toString() ??
                          "0") /
                  Decimal.fromInt(100))
              .toDecimal();
    }
    //将最大抵扣金额转换成使用的积分数
    int maxScoreCount = getScoreByMoney(billTotal, scoreConfiguration);
    //和会员现在的可用积分数量作比较，取最小值
    //将得到的符合条件的积分数 转换为钱 是否满足最小小数位数
    int score = min(maxScoreCount, (vipInfo.asserts?.availableScore ?? 0));
    num money = ScoreDiscountUtil.getMoneyByScore(score, scoreConfiguration);
    return money > 0 ? score : 0;
  }

  ///是否达到积分抵扣最低消费门槛
  ///根据打折->促销->抵扣券->手工优惠分摊后的金额，来判断是否大于门槛
  static bool allowScoreDiscount(
      GoodsBillDto goodsBillDto, ScoreConfiguration scoreConfiguration) {
    Decimal totalBeforeScoreDiscount =
        BillPreferentialTool.getBillTotalBeforeBillPreferential(
            goodsBillDto, Preferential.scoreDiscount);
    return allowScoreDiscountByTotal(
        totalBeforeScoreDiscount.toDouble(), scoreConfiguration);
  }

  ///根据打折->促销->抵扣券->手工优惠分摊后的金额，来判断是否大于门槛
  static bool allowScoreDiscountByTotal(
      num total, ScoreConfiguration scoreConfiguration) {
    return total >= getScoreDiscountThreshold(scoreConfiguration);
  }

  ///获取折扣最低门槛金额
  static num getScoreDiscountThreshold(ScoreConfiguration scoreConfiguration) {
    if (scoreConfiguration.discountingThresholdType ==
        ScoreDiscountThresholdType.threshold.value) {
      //最低消费门槛
      return scoreConfiguration.discountingThresholdNum ?? 0;
    } else {
      //无门槛
      return 0;
    }
  }

  ///获取积分配置的限制金额
  static Decimal getScoreConfigurationMinMoney(
      Decimal billTotal, ScoreConfiguration scoreConfiguration) {
    //积分抵现金额上限类型
    // 1.无限制；
    // 2.每笔交易最多抵扣x元；
    // 3.每笔交易最多抵扣x%
    if (scoreConfiguration.discountingLimitType ==
        ScoreDiscountLimitType.maxRmb.value) {
      //固定抵扣的最多金额
      Decimal temp = Decimal.parse(
          (scoreConfiguration.discountingLimitRmb ?? 0).toString());
      //两者取最小值
      if (billTotal > temp) {
        billTotal = temp;
      }
    } else if (scoreConfiguration.discountingLimitType ==
        ScoreDiscountLimitType.maxPercent.value) {
      //按百分比取值
      billTotal = billTotal *
          (Decimal.parse(
                      scoreConfiguration.discountingLimitPercent?.toString() ??
                          "0") /
                  Decimal.fromInt(100))
              .toDecimal();
    }
    return billTotal;
  }

  ///获取固定比例抵扣的最大抵扣积分
  static int getUserScore(
      int vipLeftScore, Decimal total, ScoreConfiguration scoreConfiguration) {
    List<SsVipScoreConfigurationFixedRule>? list =
        getFixedRatioList(scoreConfiguration, vipLeftScore, total.toDouble());
    if (list.isEmpty) {
      return 0;
    } else {
      return list.last.discountingProportion;
    }
  }

  static num getUserMoney(
      int vipLeftScore, Decimal total, ScoreConfiguration scoreConfiguration) {
    Decimal scoreTotal = ScoreDiscountUtil.getScoreConfigurationMinMoney(
        total, scoreConfiguration);
    List<SsVipScoreConfigurationFixedRule>? list = scoreConfiguration
        .ssVipScoreConfigurationFixedRule
        ?.where((element) =>
            element.discountingValue <= scoreTotal.toDouble() &&
            element.discountingProportion <= vipLeftScore)
        .toList();
    if (list != null && list.isEmpty) {
      return 0;
    } else {
      return list!.last.discountingValue;
    }
  }

  ///获取满足金额和剩余会员积分所对应的符合条件的固定积分抵扣比例数组
  static List<SsVipScoreConfigurationFixedRule> getFixedRatioList(
      ScoreConfiguration scoreConfiguration, int vipLeftScore, num money) {
    List<SsVipScoreConfigurationFixedRule>? list = scoreConfiguration
        .ssVipScoreConfigurationFixedRule
        ?.where((element) =>
            element.discountingValue <= money &&
            element.discountingProportion <= vipLeftScore)
        .toList();

    return list ?? [];
  }

  ///获取固定比例 积分所对应的抵扣金额
  static num getFixedRatioMoneyForScore(
      int score, ScoreConfiguration? scoreConfiguration) {
    if (scoreConfiguration == null) return 0;

    ///找到scoreConfiguration中ssVipScoreConfigurationFixedRule数组里discountingProportion和score的相同数据的discountingValue
    List<SsVipScoreConfigurationFixedRule>? list = scoreConfiguration
        .ssVipScoreConfigurationFixedRule
        ?.where((element) => element.discountingProportion == score)
        .toList();

    if (list == null || list.isEmpty) {
      return 0;
    } else {
      // 获取该规则的抵扣金额
      num ruleValue = list.last.discountingValue ?? 0;

      // 确保不超过交易抵扣限制
      if (scoreConfiguration.discountingLimitType ==
          ScoreDiscountLimitType.maxRmb.value) {
        // 如果限制了最大金额，则返回较小的值
        return min(ruleValue,
            scoreConfiguration.discountingLimitRmb ?? double.infinity);
      } else if (scoreConfiguration.discountingLimitType ==
              ScoreDiscountLimitType.maxPercent.value &&
          scoreConfiguration.discountingLimitPercent != null) {
        // 如果限制了最大百分比，需要计算该百分比对应的金额
        // 这里需要订单总金额，但方法中没有传入，需要在调用处处理
        // 返回较小的值
        return ruleValue;
      }

      return ruleValue;
    }
  }

  ///获取固定比例 金额所对应的抵扣积分
  static int getFixedRatioScoreForMoney(
      num money, ScoreConfiguration? scoreConfiguration) {
    ///找到scoreConfiguration中ssVipScoreConfigurationFixedRule数组里discountingProportion和score的相同数据的discountingValue
    List<SsVipScoreConfigurationFixedRule>? list = scoreConfiguration
        ?.ssVipScoreConfigurationFixedRule
        ?.where((element) => element.discountingValue == money)
        .toList();
    if (list == null || list.isEmpty) {
      return 0;
    } else {
      return list.last.discountingProportion;
    }
  }
}
//endregion 积分抵扣工具类
