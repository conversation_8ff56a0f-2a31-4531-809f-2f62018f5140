import '../../../common/base_type_util.dart';
import '../../entity/bill_promotion_info_dto.dart';

/// conditionList : [{"conditionTotal":"3","preferential":"1"},{"conditionTotal":"5","preferential":"2"},{"conditionTotal":"7","preferential":"3"}]
/// type : 0
/// doubleSend : false

///促销策略下的规则,对应[StrategyBean]中的[StrategyBean.rule]字段
class RuleModel {
  ///当策略类型[type]为[促销条件(2)]时，此字段生效
  List<ConditionListBean> conditionList = [];

  ///规则类型
  ///当策略类型为[促销商品(0)] 代表商品范围, 0=全部商品,1=部分商品,2=商品分类,3=排除商品,4=权益卡,5=优惠券,6=储值
  ///对于特价商品：
  ///当策略类型为[促销方式（1）] 代表特价类型, 0=促销价,1=促销折扣
  ///对于满件赠/满额赠：
  ///当策略类型为[促销条件(2)] 代表赠品类型, 1=商品,6=优惠券
  ///对于订单满减：
  ///当策略类型为[促销方式(1)] 代表计数方式，0=满金额，1=满件
  ///当策略类型为[促销条件(2)] 代表满件方式，0=金额，1=折扣
  ///对于组合购：
  ///当策略类型为[促销条件(2)] 即组合购条件，明细在[conditionList]中
  int? type;

  ///对于满件赠/满额赠:
  ///当策略类型为[促销条件(2)] 代表规则计数方式，0为按活动商品总数量计算，1为按单个商品计算
  int? typeWay;

  ///对于满件赠/满额赠/订单满减(仅满金额):
  ///当策略类型为[促销条件(2)] 代表是否开启倍送
  ///开启了倍送之后,[conditionList] 将之后有一条数据，根据这个条件翻倍
  ///开启了倍送之后，[maxTotal] 将会生效
  bool doubleSend = false;

  ///对于满件赠/满额赠/订单满减(仅满金额):
  ///当策略类型为[促销条件(2)] 代表赠品数量上限/满减金额上限
  ///只有[doubleSend] 为true，开启倍送，才会生效
  num maxTotal = 0;

  ///特价商品，当策略类型为[促销条件(2)]
  ///且商品范围为全部商品时
  ///代表折扣，即给所有商品打折
  num? discount;

  static RuleModel fromMap(Map<String, dynamic>? map) {
    if (map == null) return RuleModel();
    RuleModel giftRuleBean = RuleModel();
    giftRuleBean.conditionList = ((map['conditionList'] ?? []) as List?)
            ?.map((o) => ConditionListBean.fromMap(o))
            .toList() ??
        [];
    giftRuleBean.type = BaseTypeUtil.toInt(map['type'],0);
    giftRuleBean.typeWay = map['typeWay'] ?? 1;
    giftRuleBean.doubleSend = map['doubleSend'] ?? false;
    giftRuleBean.discount = BaseTypeUtil.toNum(map['discount']);
    giftRuleBean.maxTotal = BaseTypeUtil.toNum(map['maxTotal'], 0)!;
    return giftRuleBean;
  }

  Map toJson() => {
        "maxTotal": maxTotal,
        "conditionList": conditionList,
        "type": type,
        "typeWay": typeWay,
        "doubleSend": doubleSend,
        "discount": discount,
      };
}

/// conditionTotal : "3"
/// preferential : "1"

class ConditionListBean {
  //每满xx件/元
  num conditionTotal = 0;

  //赠品数量/满减金额
  num preferential = 0;

  ConditionListBean.fromMap(Map<String, dynamic> map)
      : conditionTotal = BaseTypeUtil.toNum(map['conditionTotal'], 0)!,
        preferential = BaseTypeUtil.toNum(map['preferential'], 0)!;

  Map toJson() => {
        "conditionTotal": conditionTotal,
        "preferential": preferential,
      };
}
