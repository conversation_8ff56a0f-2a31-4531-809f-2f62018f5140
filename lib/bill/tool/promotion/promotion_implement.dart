import 'package:collection/collection.dart';
import 'package:halo_pos/bill/entity/goods_bill.dto.dart';
import 'package:halo_pos/common/tool/system_config_tool.dart';
import 'package:halo_pos/enum/bill_decimal_type.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../common/num_extension.dart';
import '../../entity/bill_promotion_info_dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/preferential_dto.dart';
import '../goods_tool.dart';
import 'discount.dart';
import 'preferential.dart';
import 'price.dart';
import 'promotion.dart';
import 'promotion_base.dart';
import 'rule_model.dart';

//region 促销类实现

//region 商品级促销

//region 商品特价
///商品特价
///特价商品分为指定价格和指定折扣两种类型
///计算出商品价格之后，需要和折后价格比较，如果价格小于折后价格，则执行特价促销（叠加折扣优惠）
///例如：
///打5折，商品原金额100，则折后金额50，而特价金额为40，执行特价促销，促销优惠记10
///同上，若特价类型为打折，则应该在折后金额50的基础上，折上折，若促销折扣为6折，则最终金额50*0.6=30，促销优惠记20
class SpecialPricePromotionHandler extends PromotionHandler {
  const SpecialPricePromotionHandler({
    required List<BillPromotionInfoDto> promotionList,
    required GoodsBillDto billDto,
    required bool isVip,
    required List<GoodsDetailDto> originalGoodsList,
    required Map<String, List<GoodsDetailDto>> comboDetailsMap,
  }) : super(
            promotionList: promotionList,
            promotionType: PromotionType.specialPrice,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);

  @override
  void handlePromotion(
      {required List<GoodsDetailDto> goodsList,
      required BillPromotionInfoDto promotion}) {
    //商品范围
    int? goodsScope;
    //特价方式
    int? specialPriceType;
    //当商品范围为全部商品时，使用的折扣
    num? discount;
    for (var strategy in promotion.strategy) {
      //找出类型为商品范围的促销策略
      if (strategy.strategyType == PromotionStrategyType.promotionGoods.value) {
        goodsScope ??= parseStrategyRule(strategy)?.type;
      }
      //找出类型为特价类型的促销策略
      else if (strategy.strategyType ==
          PromotionStrategyType.promotionWay.value) {
        final rule = parseStrategyRule(strategy);
        specialPriceType ??= rule?.type;
        discount ??= rule?.discount;
      }
      if (goodsScope != null && specialPriceType != null) break;
    }

    if (specialPriceType != SpecialPriceType.price.value &&
        specialPriceType != SpecialPriceType.discount.value) return;

    //未触发促销的商品，将参与下一轮促销
    List<GoodsDetailDto> reserveGoodsList = [];

    //执行商品特价，若是未执行则添加到保留列表
    bool execute(GoodsDetailDto goods, num? preferential) {
      return _executeGoodsSpecialPrice(
        goods: goods,
        preferential: preferential,
        specialPriceType: specialPriceType!,
        promotion: promotion,
        comboDetails: goods.comboRow ? comboDetailsMap[goods.comboRowId] : null,
      );
    }

    for (var goods in goodsList) {
      bool executed = false;
      if (isGoodsInPromotion(
          goods: goods,
          goodsScope: goodsScope,
          promotion: promotion,
          //特价商品需要校验批次号
          validateBatchNo: true,
          //当商品在促销中
          onGoodsInPromotion: (ptype, goods, goodsRuleType) {
            //只有促销商品为部分商品时，才执行
            if (goodsRuleType == PromotionGoodsScope.partGoods.value) {
              executed =
                  execute(goods, num.tryParse(ptype.preferential ?? "0"));
            }
          })) {
        //促销折扣时，商品范围将会有全部，部分，排除。其中全部商品和排除商品时，折扣不会再在折扣商品列表里面去取值，需要单独处理
        if (goodsScope == PromotionGoodsScope.allGoods.value
            // || goodsScope == PromotionGoodsScope.excludeGoods.value
            // || goodsScope == PromotionGoodsScope.goodsCategory.value
            ) {
          if (specialPriceType == SpecialPriceType.discount.value) {
            executed = execute(goods, discount);
          }
        }
      }
      //对于未执行的商品则保留参与别的促销
      if (!executed) {
        reserveGoodsList.add(goods);
      }
    }
    goodsList.clear();
    goodsList.addAll(reserveGoodsList);
  }

  ///执行商品特价
  ///[goods] 当前商品
  ///[preferential] 促销价或者促销折扣
  ///[specialPriceType] 特价类型
  ///当特价>=折后价格，或<=0 则不触发促销
  ///扣价<=0,或>=1,则不触发促销
  ///计算出商品价格之后，需要和折后价格比较，如果价格小于折后价格，则执行特价促销（叠加折扣优惠）
  /// 例如：
  /// 打5折，商品原金额100，则折后金额50，而特价金额为40，执行特价促销，促销优惠记10
  /// 同上，若特价类型为打折，则应该在折后金额50的基础上，折上折，若促销折扣为6折，则最终金额50*0.6=30，促销优惠记20
  bool _executeGoodsSpecialPrice({
    required GoodsDetailDto goods,
    required num? preferential,
    required int specialPriceType,
    required BillPromotionInfoDto promotion,
    List<GoodsDetailDto>? comboDetails,
  }) {
    if (preferential == null || preferential <= 0) return false;
    //促销价
    if (specialPriceType == SpecialPriceType.price.value) {
      num specialPrice = preferential;
      //当促销不与会员权益叠加时，将折扣还原成1折，然后执行促销
      // if (!promotion.vipRights) {
      if (specialPrice >= goods.currencyPrice || specialPrice <= 0) {
        return false;
      }
      DiscountUtil.resetDiscountTo1(goods, comboDetails: comboDetails ?? []);
      // } else {
      //特价>=折后单价，或<=0 不触发促销
      // if (specialPrice >= goods.discountPrice || specialPrice <= 0) {
      //   return false;
      // }
      // }
      //重新计算商品并且记录优惠辅助信息
      PromotionUtil.setPriceAndSetPreferential(goods, promotion, specialPrice,
          comboDetails: comboDetails);
    }
    //促销折扣
    else if (specialPriceType == SpecialPriceType.discount.value) {
      //扣价<=0,或>=1,不触发促销
      if (preferential <= 0 || preferential >= 1) {
        return false;
      }
      // 当促销不和权益卡打折叠加时，需要移除权益卡打折，重新执行打折
      // handleJoinRightsCardDiscount(
      //     goods: goods,
      //     joinVipRights: promotion.vipRights,
      //     comboDetails: comboDetails ?? []);

      // 不叠加权益卡时，移除会员折扣
      handleJoinVipRights(
          goods: goods,
          joinVipRights: promotion.vipRights,
          comboDetails: comboDetails ?? []);
      //在折后价格上折上折
      num promotedTotal = SystemConfigTool.doubleMultipleToDecimal(
          goods.discountTotal, preferential, BillDecimalType.TOTAL);
      //重新计算商品并且记录优惠辅助信息
      PromotionUtil.setTotalAndSetPreferential(goods, promotion, promotedTotal,
          comboDetails: comboDetails);
    } else {
      return false;
    }
    return true;
  }
}
//endregion 商品特价

//region 第二件半价
///第二件半价
class HalfPricePromotionHandler extends PromotionHandler
    with PromotionGoodsGiftHandlerMixin, PromotionGoodsSplitMixin {
  HalfPricePromotionHandler({
    required List<BillPromotionInfoDto> promotionList,
    required GoodsBillDto billDto,
    required bool isVip,
    required List<GoodsDetailDto> originalGoodsList,
    required Map<String, List<GoodsDetailDto>> comboDetailsMap,
  }) : super(
            promotionList: promotionList,
            promotionType: PromotionType.halfPrice,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);

  ///没有执行的第二件半价赠品,将其还原成折后状态
  @override
  void removeNotExecutedGoodsGift() {
    if (currentPromotionTypeGoodsGiftList != null) {
      for (var goods in currentPromotionTypeGoodsGiftList!) {
        if (!executedGoodsGiftSet.contains(goods)) {
          resetToDiscountPriceAndCleanPromotion(goods,
              comboDetails:
                  goods.comboRow ? comboDetailsMap[goods.comboRowId] : null);
        }
      }
    }
  }

  ///将商品或套餐促销价还原到折扣价并且清除促销信息(保留hint)
  void resetToDiscountPriceAndCleanPromotion(GoodsDetailDto goods,
      {List<GoodsDetailDto>? comboDetails}) {
    PromotionUtil.cleanGoodsPromotion(goods, cleanHints: false);
    //将价格还原成折后价格
    GoodsPriceUtil.onPromotedPriceChange(goods, goods.discountPrice,
        total: goods.discountTotal, discount: goods.discount);
    if (goods.comboRow) {
      // ComboPreferentialTool.handleComboDetail(
      //     goods, comboDetailMap[goods.comboRowId] ?? [],
      //     totalGetter: (goods) => goods.promotedTotal,
      //     totalSetter: (goods, total) {
      //       //清除促销信息
      //       PromotionUtil.cleanGoodsPromotion(goods);
      //       GoodsTotalUtil.onPromotedTotalChange(goods, total); //不用重新分摊计算金额
      //     });
      comboDetails?.forEach((goods) {
        //清除促销信息
        PromotionUtil.cleanGoodsPromotion(goods, cleanHints: false);
        //将价格还原成折后价格
        GoodsPriceUtil.onPromotedPriceChange(goods, goods.discountPrice,
            total: goods.discountTotal, discount: goods.discount);
      });
    }
  }

  @override
  void handlePromotion(
      {required List<GoodsDetailDto> goodsList,
      required BillPromotionInfoDto promotion}) {
    StrategyBean? goodsScopeStrategy = promotion.strategy.firstWhereOrNull(
        (s) => s.strategyType == PromotionStrategyType.promotionGoods.value);
    int? goodsScope = parseStrategyRule(goodsScopeStrategy)?.type;
    _handlePromotionWithGoodsScope(
        goodsList: goodsList, promotion: promotion, goodsScope: goodsScope);
  }

  ///根据商品范围，执行促销
  ///[goodsList] 当前商品列表
  ///[promotion] 当前促销
  ///[goodsScope] 参加促销的商品范围
  void _handlePromotionWithGoodsScope({
    required List<GoodsDetailDto> goodsList,
    required BillPromotionInfoDto promotion,
    required int? goodsScope,
  }) {
    if (goodsList.isEmpty) return;
    //触发了促销的商品，将不再参与下一轮促销
    Set<GoodsDetailDto> executedGoodsSet = {};
    //当前要执行第二件半价的商品列表，对于批次商品，需要找到同商品不同批次的商品，对于一般商品，只有一条数据
    List<GoodsDetailDto> currentGoodsList = [];
    //参加过遍历的商品，包括内部循环过的商品，避免重复判断
    Set<GoodsDetailDto> loopedSet = {};
    for (var goods in goodsList) {
      //该商品已经和之前遍历到的商品组合，不需要再进行遍历找同商品不同批次的商品
      if (loopedSet.add(goods)) {
        currentGoodsList.add(goods);
        for (var goods1 in goodsList) {
          if (goods != goods1 &&
              GoodsTool.compareUnitSku(goods, goods1, comparePrice: false)) {
            if (loopedSet.add(goods1)) {
              currentGoodsList.add(goods1);
            }
          }
        }
      }
      //过滤商品
      _filterGoodsList(
          promotion: promotion,
          goodsScope: goodsScope,
          currentGoodsList: currentGoodsList);
      //执行第二件半价促销，未执行，则保留，参与其他促销判断
      if (currentGoodsList.isNotEmpty) {
        if (_executeHalfPricePromotion(
          goodsList: currentGoodsList.toList(),
          promotion: promotion,
        )) {
          executedGoodsSet.addAll(currentGoodsList);
        }
        currentGoodsList.clear();
      }
    }
    goodsList.removeWhere((element) => executedGoodsSet.contains(element));
  }

  ///过滤商品列表，判断是否可以参加促销
  ///当有任意商品数量为小数，则全部不执行
  void _filterGoodsList({
    required BillPromotionInfoDto promotion,
    required int? goodsScope,
    required List<GoodsDetailDto> currentGoodsList,
  }) {
    //遍历同商品不同批次的商品列表，判断是否需要执行促销
    Set<GoodsDetailDto> temp = {};
    for (var element in currentGoodsList) {
      //如果有同一商品列表中如果有任意商品数量为小数，则全部不执行促销
      if (!element.unitQty.isInteger) {
        currentGoodsList.clear();
        return;
      }
      if (isGoodsInPromotion(
        goods: element,
        goodsScope: goodsScope,
        promotion: promotion,
      )) {
        temp.add(element);
      }
    }
    currentGoodsList.clear();
    currentGoodsList.addAll(temp);
  }

  ///执行第二件半价促销
  ///[goods] 当前商品
  ///[promotion] 当前促销
  ///遍历源商品列表，移除第二件半价商品
  ///若当前商品数量>=2,则执行促销，添加第二件半价的商品
  ///若当前商品数量<2,则不执行促销
  ///这里第二件半价的是根据折后单价折上折。
  bool _executeHalfPricePromotion({
    required List<GoodsDetailDto> goodsList,
    required BillPromotionInfoDto promotion,
  }) {
    if (goodsList.isEmpty) {
      return false;
    }
    bool executed = false;
    //赠品折扣
    const num giftDiscount = 0.5;
    //触发促销需要的商品数量
    const num promotionGoodsUnitQty = 2;
    num sumQty = 0;
    //找出最低零售价
    GoodsDetailDto? minPriceGoods;
    //找出之前的半价赠品
    GoodsDetailDto? gift;
    for (var i = goodsList.length - 1; i >= 0; i--) {
      GoodsDetailDto goods = goodsList[i];
      minPriceGoods ??= goods;
      if (goods.currencyPrice < minPriceGoods.currencyPrice) {
        minPriceGoods = goods;
      }
      if (goods.promotionGift) {
        gift = goods;
      }
      sumQty = MathUtil.addDec(sumQty, goods.unitQty).toDouble();
    }
    if (sumQty >= promotionGoodsUnitQty) {
      executed = true;
      if (gift != null && gift.currencyPrice != minPriceGoods!.currencyPrice) {
        resetToDiscountPriceAndCleanPromotion(gift,
            comboDetails:
                GoodsTool.getComboDetailsFromMap(gift, comboDetailsMap));
        //添加到执行赠品集合中，避免被清除信息
        executedGoodsGiftSet.add(gift);
        gift = null;
      }
      //商品关联促销
      for (var goods in goodsList) {
        setGoodsPromotion(promotion, goods);
        //当促销不和权益卡打折叠加时，需要移除权益卡打折，重新执行打折
        // handleJoinRightsCardDiscount(
        //     goods: goods, joinVipRights: promotion.vipRights);
        //第二件半价不与权益叠加
        List<GoodsDetailDto> comboDetails =
            GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap);
        DiscountUtil.resetDiscountTo1(goods, comboDetails: comboDetails);
        if (goods != gift) {
          PromotionUtil.setPreferential(goods, promotion, 0,
              comboDetails: comboDetails);
        }
      }
      //赠品套餐明细行
      List<GoodsDetailDto> giftComboDetails = [];
      if (gift != null) {
        giftComboDetails =
            GoodsTool.getComboDetailsFromMap(gift, comboDetailsMap);
      } else {
        //获取赠品
        _getOrGenerateGift(
            promotion: promotion,
            minPriceGoods: minPriceGoods!,
            onGenerateGift: (g, comboDetails) {
              gift = g;
              giftComboDetails = comboDetails;
            });
      }
      if (gift == null) {
        return false;
      }
      //在折后价格上折上折
      num promotedTotal = SystemConfigTool.doubleMultipleToDecimal(
          gift!.discountTotal, giftDiscount, BillDecimalType.TOTAL);
      //重新计算并且记录优惠辅助信息
      PromotionUtil.setTotalAndSetPreferential(gift!, promotion, promotedTotal,
          comboDetails: giftComboDetails);
      //将赠品添加到已执行列表
      executedGoodsGiftSet.add(gift!);
    } else {
      //添加促销提示
      addGoodsListHint(
          goodsList: goodsList,
          executed: executed,
          hint: PromotionHints(
              promotionId: promotion.id,
              promotionType: promotion.promotionType,
              typeName: "半价",
              hints: "该商品第二件可享受半价"));
    }
    return executed;
  }

  ///生成赠品
  ///分为2种情况：
  ///1.没有赠品，且商品列表最后一个商品数量为1，则直接将其作为赠品
  ///2.没有赠品，且最后一个最低价商品数量大于1，则将其拆分：
  ///  1.将原商品数量-1
  ///  2.生成新的赠品
  void _getOrGenerateGift({
    required BillPromotionInfoDto promotion,
    required GoodsDetailDto minPriceGoods,
    required void Function(
            GoodsDetailDto gift, List<GoodsDetailDto> giftComboDetails)
        onGenerateGift,
  }) {
    //同商品不同批次的商品列表，参加第二件半价，默认使用最后一个且价格最低的商品来拆出半价
    GoodsDetailDto goods = minPriceGoods;
    List<GoodsDetailDto> comboDetails =
        GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap);
    GoodsDetailDto? gift;
    List<GoodsDetailDto> giftComboDetails = [];
    //如果该商品数量大于1，则拆出一个赠品
    if (goods.unitQty > 1) {
      splitGoods(
          goods: goods,
          promotion: promotion,
          splitQty: 1,
          onGenerateGift: (g, gc) {
            gift = g;
            giftComboDetails = gc;
            //将赠品添加到促销商品后面
            insertGiftAfterGoods(
                isAdd: true,
                giftList: [gift!, ...giftComboDetails],
                goods: goods);
          });
    }
    //商品数量正好等于1，则将该商品作为赠品
    else {
      gift = goods;
      gift.promotionGift = true;
      giftComboDetails = comboDetails;
    }
    //返回赠品
    onGenerateGift(gift!, giftComboDetails);
  }
}
//endregion 第二件半价

//region 满件赠
///满件赠
class FullCountGiftPromotionHandler extends PromotionHandler
    with
        PromotionGoodsGiftHandlerMixin,
        PromotionCouponGiftHandlerMixin,
        PromotionConditionMixin,
        MultiGoodsAndGiftHandlerMixin {
  FullCountGiftPromotionHandler({
    required List<BillPromotionInfoDto> promotionList,
    required GoodsBillDto billDto,
    required bool isVip,
    required List<GoodsDetailDto> originalGoodsList,
    required Map<String, List<GoodsDetailDto>> comboDetailsMap,
  }) : super(
            promotionList: promotionList,
            promotionType: PromotionType.fullCountGift,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);

  @override
  num getGoodsCount(GoodsDetailDto goods, bool joinVipRights) => goods.unitQty;

  @override
  String get goodsCountUnitName => "件";
}
//endregion 满件赠

//region 满额赠
///满额赠
class FullAmountGiftPromotionHandler extends PromotionHandler
    with
        PromotionGoodsGiftHandlerMixin,
        PromotionCouponGiftHandlerMixin,
        PromotionConditionMixin,
        MultiGoodsAndGiftHandlerMixin {
  FullAmountGiftPromotionHandler({
    required List<BillPromotionInfoDto> promotionList,
    required GoodsBillDto billDto,
    required bool isVip,
    required List<GoodsDetailDto> originalGoodsList,
    required Map<String, List<GoodsDetailDto>> comboDetailsMap,
  }) : super(
            promotionList: promotionList,
            promotionType: PromotionType.fullAmountGift,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);

  @override
  num getGoodsCount(GoodsDetailDto goods, bool joinVipRights) {
    if (!joinVipRights) {
      return goods.currencyTotal;
    }
    return goods.promotedTotal;
  }

  @override
  String get goodsCountUnitName => "元";
}
//endregion 满额赠

//region 组合购
///组合购
class CombinationBuyPromotionHandler extends PromotionHandler
    with PromotionConditionMixin, PromotionGoodsSplitMixin {
  CombinationBuyPromotionHandler(
      {required List<BillPromotionInfoDto> promotionList,
      required GoodsBillDto billDto,
      required bool isVip,
      required List<GoodsDetailDto> originalGoodsList,
      required Map<String, List<GoodsDetailDto>> comboDetailsMap})
      : super(
            promotionList: promotionList,
            promotionType: PromotionType.combinationBuy,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);

  @override
  void handlePromotion(
      {required List<GoodsDetailDto> goodsList,
      required BillPromotionInfoDto promotion}) {
    if (goodsList.isEmpty) return;
    //商品范围
    StrategyBean? goodsScopeStrategy;
    //促销规则
    StrategyBean? conditionStrategy;
    for (var strategy in promotion.strategy) {
      if (strategy.strategyType == PromotionStrategyType.promotionGoods.value) {
        goodsScopeStrategy ??= strategy;
      } else if (strategy.strategyType ==
          PromotionStrategyType.promotionCondition.value) {
        conditionStrategy ??= strategy;
      }
      if (goodsScopeStrategy != null && conditionStrategy != null) break;
    }
    //参加促销的商品范围
    int? goodsScope = parseStrategyRule(goodsScopeStrategy)?.type;
    //促销规则
    RuleModel? conditionRule = parseStrategyRule(conditionStrategy);
    if (goodsScope == null || conditionRule == null) return;
    //此促销是否满足条件被执行
    bool executed = false;
    //未触发促销的商品，将参与下一轮促销
    List<GoodsDetailDto> reserveGoodsList = [];
    //参加促销的商品列表
    List<GoodsDetailDto> executedGoodsList = [];
    //促销内商品的的数量
    num sumCount = 0;
    //促销规则，按照规则中的商品数量进行排序
    conditionRule.conditionList.sortBy((element) => element.conditionTotal);
    //找出第一个条件，要满足促销至少有一个商品满足这个条件
    final firstCondition = conditionRule.conditionList.first;
    //找到此促销内的商品，排除数量为小数的商品
    for (var goods in goodsList) {
      if (!_isGoodsInPromotion(
          goods: goods, goodsScope: goodsScope, promotion: promotion)) {
        reserveGoodsList.add(goods);
        continue;
      }
      executedGoodsList.add(goods);
      sumCount = MathUtil.addDec(sumCount, goods.unitQty).toDouble();
      if (!executed && sumCount >= firstCondition.conditionTotal) {
        executed = true;
      }
    }
    if (executedGoodsList.isNotEmpty) {
      _execute(
          goodsList: executedGoodsList,
          promotion: promotion,
          conditionRule: conditionRule,
          sumCount: sumCount);
    }
    //未触发促销的商品，将参与下一轮促销
    if (executed) {
      goodsList.clear();
      goodsList.addAll(reserveGoodsList);
    }
  }

  ///执行促销
  void _execute({
    required List<GoodsDetailDto> goodsList,
    required BillPromotionInfoDto promotion,
    required RuleModel conditionRule,
    required num sumCount,
  }) {
    //促销提示
    PromotionHints? hints;
    //找到满足的和未满足的条件
    Map<String, ConditionListBean> map =
        checkConditionList(conditionRule.conditionList, sumCount);
    //当前满足的条件
    ConditionListBean? metCondition =
        map[PromotionConditionMixin.metConditionKey];
    //未满足的条件，即满足条件的下一个条件
    ConditionListBean? unmetCondition =
        map[PromotionConditionMixin.unmetConditionKey];
    if (unmetCondition != null) {
      hints = generateHints(
          unmetCondition: unmetCondition,
          promotion: promotion,
          sumCount: sumCount);
    }
    //执行促销，递减，直到促销完全执行
    if (metCondition != null) {
      //真正被选中参加n元购的商品列表
      List<GoodsDetailDto> selectedGoodsList = [];
      num remain = metCondition.conditionTotal;
      num sumTotal = 0;
      for (var goods in goodsList) {
        //关联促销
        setGoodsPromotion(promotion, goods);
        //参加组合购的商品不与权益叠加
        DiscountUtil.resetDiscountTo1(goods,
            comboDetails:
                GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap));
        // PromotionUtil.setPreferential(goods, promotion, 0,
        //     comboDetails:
        //         GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap));
        //已经执行促销，并且没有未满足的条件，说明已经达到最大，则移除此类型的hints
        if (hints == null) {
          removeCurrentPromotionTypeHintFromHintList(goods.promotionHints);
        } else {
          addGoodsHint(goods: goods, hint: hints, executed: true);
        }
        //已经满足n元购的数量，剩下的商品不需要再参与分摊
        if (remain <= 0) continue;
        num unitQty = goods.unitQty;
        //如果剩余数量>=商品的数量，则不需要拆分
        if (remain >= unitQty) {
          selectedGoodsList.add(goods);
          sumTotal = MathUtil.addDec(sumTotal, goods.discountTotal).toDouble();
        }
        //拆分商品
        else {
          splitGoods(
              goods: goods,
              promotion: promotion,
              splitQty: remain,
              // handleGiftDiscount: false,
              //参加组合购的商品，不需要执行打折
              onGenerateGift:
                  (GoodsDetailDto gift, List<GoodsDetailDto> giftComboDetails) {
                //将分割出的商品加入到参加分摊的列表中
                selectedGoodsList.add(gift);
                sumTotal =
                    MathUtil.addDec(sumTotal, gift.discountTotal).toDouble();
                //记录套餐明细
                if (gift.comboRow) {
                  comboDetailsMap[gift.comboRowId] = giftComboDetails;
                }
                //将分割出的商品和套餐明细加入到原商品列表中
                _insertGoodsListBeforeGoods([gift, ...giftComboDetails], goods);
              });
        }
        remain = MathUtil.subtractDec(remain, unitQty).toDouble();
      }
      //执行组合购
      _executeCombinationBuyPromotion(
          promotion: promotion,
          goodsList: selectedGoodsList,
          preferentialTotal: metCondition.preferential,
          sumTotal: sumTotal);
    }
    //未执行促销，添加提示
    else {
      addGoodsListHint(goodsList: goodsList, hint: hints, executed: false);
    }
  }

  ///执行，根据金额分摊优惠
  ///[preferentialTotal] n件xx元的xx元
  ///[sumTotal] 列表中的商品总金额
  void _executeCombinationBuyPromotion({
    required BillPromotionInfoDto promotion,
    required List<GoodsDetailDto> goodsList,
    required num preferentialTotal,
    required num sumTotal,
  }) {
    //遍历商品，按比例分摊，最后一个用减法
    num total = 0;
    for (var i = 0; i < goodsList.length; ++i) {
      var goods = goodsList[i];
      num promotedTotal = 0;
      if (i < goodsList.length - 1) {
        num proportion =
            MathUtil.divideDec(goods.discountTotal, sumTotal).toDouble();
        promotedTotal = SystemConfigTool.doubleMultipleToDecimal(
            preferentialTotal, proportion, BillDecimalType.TOTAL);
        total = MathUtil.addDec(total, promotedTotal).toDouble();
      } else {
        promotedTotal =
            MathUtil.subtractDec(preferentialTotal, total).toDouble();
      }
      PromotionUtil.setTotalAndSetPreferential(goods, promotion, promotedTotal,
          comboDetails:
              GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap));
    }
  }

  ///商品是否在促销中
  bool _isGoodsInPromotion(
      {required GoodsDetailDto goods,
      required int? goodsScope,
      required BillPromotionInfoDto promotion}) {
    //数量为小数的商品部参加组合购
    if (!goods.unitQty.isInteger) {
      return false;
    }
    return isGoodsInPromotion(
        goods: goods, goodsScope: goodsScope, promotion: promotion);
  }

  ///创建一个促销提示
  PromotionHints? generateHints({
    required BillPromotionInfoDto promotion,
    required num sumCount,
    required ConditionListBean? unmetCondition,
  }) {
    if (unmetCondition == null) {
      return null;
    }
    num remain = MathUtil.subtractDec(unmetCondition.conditionTotal, sumCount)
        .toDouble()
        .getIntWhenInteger;
    PromotionHints hints = PromotionHints(
      promotionId: promotion.id,
      promotionType: promotion.promotionType,
      typeName: "组合购",
      hints:
          "再买$remain件即可享${unmetCondition.preferential}元任选${unmetCondition.conditionTotal}件活动",
    );
    return hints;
  }

  ///将分割出的商品添加到原商品前面
  void _insertGoodsListBeforeGoods(
      List<GoodsDetailDto> goodsList, GoodsDetailDto goods) {
    int index = originalGoodsList.indexOf(goods);
    if (index == -1) {
      index == 0;
    }
    originalGoodsList.insertAll(index, goodsList);
  }
}
//endregion

//endregion 商品级促销

//region 单据级别促销(订单满减)
///订单满减
class OrderFullReductionPromotionHandler extends PromotionHandler
    with BillPreferentialMixin, PromotionConditionMixin {
  @override
  final Preferential preferential = Preferential.billPromotion;

  const OrderFullReductionPromotionHandler({
    required List<BillPromotionInfoDto> promotionList,
    required GoodsBillDto billDto,
    required bool isVip,
    required List<GoodsDetailDto> originalGoodsList,
    required Map<String, List<GoodsDetailDto>> comboDetailsMap,
  }) : super(
            promotionList: promotionList,
            promotionType: PromotionType.orderFullReduction,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);

  @override
  void handlePromotion(
      {required List<GoodsDetailDto> goodsList,
      required BillPromotionInfoDto promotion}) {
    //商品范围
    StrategyBean? goodsScopeStrategy;
    //计数类型
    StrategyBean? countTypeStrategy;
    //促销规则
    StrategyBean? conditionStrategy;
    for (var strategy in promotion.strategy) {
      if (strategy.strategyType == PromotionStrategyType.promotionGoods.value) {
        goodsScopeStrategy ??= strategy;
      } else if (strategy.strategyType ==
          PromotionStrategyType.promotionWay.value) {
        countTypeStrategy ??= strategy;
      } else if (strategy.strategyType ==
          PromotionStrategyType.promotionCondition.value) {
        conditionStrategy ??= strategy;
      }
      if (goodsScopeStrategy != null &&
          countTypeStrategy != null &&
          conditionStrategy != null) break;
    }
    //参加促销的商品范围
    int? goodsScope = parseStrategyRule(goodsScopeStrategy)?.type;
    //计数类型(满金额/满件)
    int? countType = parseStrategyRule(countTypeStrategy)?.type;
    //促销规则
    RuleModel? conditionRule = parseStrategyRule(conditionStrategy);

    if (goodsScope == null ||
        countType == null ||
        conditionRule?.conditionList.isNotEmpty != true ||
        conditionRule?.type == null) return;

    final firstCondition = conditionRule!.conditionList.first;
    //不在促销内的商品列表
    Set<GoodsDetailDto> excludeGoodsList = {};
    //参加促销的商品
    List<GoodsDetailDto> executeGoodsList = [];
    //参加促销的商品的累计金额
    num goodsAmountTotal = 0;
    //参加促销的商品的累计数量
    num goodsQtyCount = 0;
    for (var goods in goodsList) {
      if (isGoodsInPromotion(
        goods: goods,
        goodsScope: goodsScope,
        promotion: promotion,
      )) {
        if (goods.promotedTotal >= 0) {
          executeGoodsList.add(goods);
          goodsAmountTotal = SystemConfigTool.doubleAddToDecimal(
              goodsAmountTotal, goods.promotedTotal, BillDecimalType.TOTAL);
        }
        goodsQtyCount = SystemConfigTool.doubleAddToDecimal(
            goodsQtyCount, goods.unitQty, BillDecimalType.QTY);
      } else {
        excludeGoodsList.add(goods);
      }
    }
    //满件还是满额
    num count = goodsAmountTotal;
    if (countType == BillPromotionCountType.qty.value) {
      count = goodsQtyCount;
    }
    //是否执行
    bool executed = count >= firstCondition.conditionTotal;
    //促销提示
    PromotionHints? hints;
    //获取满减金额/折扣
    num preferential = getGiftCountByMultipleGoods(
        sumCount: count,
        conditionRule: conditionRule,
        firstCondition: firstCondition,
        generateHints: (unmetRemainCount, unmetGiftCount) {
          hints = generateHints(
            promotion: promotion,
            countRemain: unmetRemainCount,
            giftCount: unmetGiftCount,
            countType: countType,
            preferentialType: conditionRule.type!,
          );
        });
    //添加提示
    if (hints != null) {
      //范围为全部商品的订单满减，将促销提示添加到商品列表底部
      if (goodsScope == PromotionGoodsScope.allGoods.value) {
        billDto.promotionHints ??= [];
        addHint(
            hintList: billDto.promotionHints!,
            hint: hints!,
            executed: executed);
      }
      //其他情况，将提示添加到商品明细行中
      else {
        for (var goods in executeGoodsList) {
          addGoodsHint(goods: goods, hint: hints!, executed: executed);
        }
      }
    }
    if (executed) {
      goodsList.clear();
      goodsList.addAll(excludeGoodsList);
      //打折
      if (conditionRule.type == BillPromotionPreferentialType.discount.value) {
        num discountTotal = SystemConfigTool.doubleMultipleToDecimal(
            goodsAmountTotal, preferential, BillDecimalType.TOTAL);
        preferential = SystemConfigTool.doubleSubtractionToDecimal(
            goodsAmountTotal, discountTotal, BillDecimalType.TOTAL);
      }
      //减少金额
      else {
        if (preferential > goodsAmountTotal) {
          preferential = goodsAmountTotal;
        }
      }
      if (preferential > 0) {
        for (var goods in executeGoodsList) {
          if (StringUtil.isZeroOrEmpty(goods.promotionId)) {
            goods.calIntegral = promotion.calIntegral;
          } else {
            goods.calIntegral = goods.calIntegral && promotion.calIntegral;
          }
        }
        sharePreferentialToGoods(
            bill: billDto,
            goodsList: executeGoodsList,
            billPreferential: preferential,
            goodsListTotal: goodsAmountTotal,
            comboDetailsMap: comboDetailsMap,
            preferentialTypeId: promotion.id);
      }
    }
  }

  ///生成提示
  ///[preferentialType] 优惠类型 满减/满折
  ///[countType] 计数类型，满金/满件
  PromotionHints? generateHints({
    required BillPromotionInfoDto promotion,
    required num countRemain,
    required num giftCount,
    required int preferentialType,
    required num countType,
  }) {
    if (countRemain <= 0 || giftCount <= 0) {
      return null;
    }
    String countUnit =
        countType == BillPromotionCountType.amount.value ? "元" : "件";
    String preferentialStr =
        preferentialType == BillPromotionPreferentialType.discount.value
            ? "可享$giftCount折"
            : "可减$giftCount元";
    PromotionHints hints = PromotionHints(
      promotionId: promotion.id,
      promotionType: promotion.promotionType,
      typeName: "订单满减",
      hints: "再买$countRemain$countUnit,$preferentialStr",
    );
    return hints;
  }

  @override
  void setGoodsTotal(GoodsDetailDto goods, num total) {
    GoodsTotalUtil.onPromotedTotalChange(goods, total);
  }

  @override
  num getGoodsTotal(GoodsDetailDto goods) {
    return goods.promotedTotal;
  }
}

//endregion 单据级别促销(订单满减)

//endregion 促销类实现
