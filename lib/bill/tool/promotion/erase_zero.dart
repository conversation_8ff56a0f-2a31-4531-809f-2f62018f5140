import 'package:decimal/decimal.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../common/tool/sp_tool.dart';
import '../../../common/tool/system_config_tool.dart';
import '../../../entity/system/system_config_dto.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/preferential_dto.dart';
import 'preferential.dart';

//region 枚举
///抹零位数
enum EraseZeroScale {
  ///整数
  zero,

  ///保留一位小数
  one
}

extension EraseZeroScaleExtension on EraseZeroScale {
  static EraseZeroScale? valueOf(int value) {
    if (value == EraseZeroScale.zero.value) {
      return EraseZeroScale.zero;
    } else if (value == EraseZeroScale.one.value) {
      return EraseZeroScale.one;
    }
    return null;
  }

  int get value {
    switch (this) {
      case EraseZeroScale.zero:
        return 1;
      case EraseZeroScale.one:
        return 0;
    }
  }

  int get scale {
    switch (this) {
      case EraseZeroScale.zero:
        return 0;
      case EraseZeroScale.one:
        return 1;
    }
  }
}

enum EraseZeroType {
  ///只舍不入
  floor,

  ///只入不舍
  ceil,
}

extension EraseZeroTypeExtension on EraseZeroType {
  int get value {
    switch (this) {
      case EraseZeroType.floor:
        return 0;
      case EraseZeroType.ceil:
        return 1;
    }
  }
}
//endregion 枚举

///抹零分摊
class EraseZeroHandler with BillPreferentialMixin {
  @override
  Preferential get preferential => Preferential.eraseZero;

  void handle(GoodsBillDto bill) {
    bill.preferentialHelp.remove(preferential.name);
    //套餐明细
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    //要执行分摊的商品列表
    List<GoodsDetailDto> goodsList = [];
    //商品合计金额
    num goodsSumTotal = filterGoodsListAndCalculateTotal(
        bill.outDetail, goodsList, comboDetailsMap);
    //换货，总金额需要减去换入的商品的退款总金额（含赠金部分）
    if (bill.inDetail.isNotEmpty) {
      Decimal refundTotal = Decimal.zero;
      refundTotal = bill.inDetail.fold<Decimal>(
          refundTotal,
          (previousValue, element) =>
              previousValue +
              Decimal.parse(element.posCurrencyDisedTaxedTotal.toString()));
      goodsSumTotal = SystemConfigTool.doubleSubtractionToDecimal(
          goodsSumTotal, refundTotal.toDouble(), BillDecimalType.TOTAL);
    }
    if (goodsSumTotal <= 0) return;
    SystemConfigDto config = SpTool.getSystemConfig();
    if (!config.recordsheetSaleEshopEraseZeroEnable) {
      return;
    }
    //获取抹零位数
    EraseZeroScale? scale = EraseZeroScaleExtension.valueOf(
        config.recordsheetSaleEshopEraseZeroDecimalRule);
    if (scale == null) return;
    //计算抹零后金额
    num totalAfterDiscount = goodsSumTotal;
    if (config.recordsheetSaleEshopEraseZeroRoundingOffRule ==
        EraseZeroType.ceil.value) {
      totalAfterDiscount = Decimal.parse(goodsSumTotal.toString())
          .ceil(scale: scale.scale)
          .toDouble();
    } else if (config.recordsheetSaleEshopEraseZeroRoundingOffRule ==
        EraseZeroType.floor.value) {
      totalAfterDiscount = Decimal.parse(goodsSumTotal.toString())
          .floor(scale: scale.scale)
          .toDouble();
    }
    //计算优惠金额
    num eraseZeroPreferential =
        MathUtil.subtractDec(goodsSumTotal, totalAfterDiscount).toDouble();
    if (eraseZeroPreferential != 0) {
      sharePreferentialToGoods(
          bill: bill,
          goodsList: goodsList,
          billPreferential: eraseZeroPreferential,
          goodsListTotal: goodsSumTotal,
          comboDetailsMap: comboDetailsMap);
    }
  }
}
