import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/preferential_dto.dart';
import '../bill_goods_util.dart';
import '../goods_tool.dart';
import 'preferential.dart';
import 'price.dart';
import 'promotion.dart';

///手工改价
class ManualPriceUtil {
  ManualPriceUtil._();

  ///整单折扣
  static void setBillDiscount(GoodsBillDto billDto, num discount) {
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    List<GoodsDetailDto> goodsList = GoodsTool.filterGoodsListAndGetCombo(
        billDto.outDetail,
        comboDetailsMap: comboDetailsMap, filter: (goods) {
      if (GoodsTool.isComboDetail(goods)) return false;
      //促销赠品不执行手工改价
      if (BillGoodsUtil.isPromotionGift(goods)) return false;
      //提货券赠品不执行手工改价
      if (goods.gift &&
          goods.preferentialHelp.containsKey(Preferential.giftCoupon.name)) {
        return false;
      }
      return true;
    }, addWhenFalse: true);
    for (var goods in goodsList) {
      List<GoodsDetailDto>? comboDetails;
      if (GoodsTool.isComboRow(goods)) {
        comboDetails = comboDetailsMap[goods.comboRowId];
      }
      onDiscountChange(goods, discount, comboDetails: comboDetails ?? []);
    }
  }

  ///计算手工改价优惠，并记录优惠辅助
  static void _setPreferential(GoodsDetailDto goods) {
    if (goods.manualPrice) {
      PromotionUtil.cleanGoodsPromotion(goods);
      goods.preferentialHelp.clear();
      if (!GoodsTool.isComboRow(goods)) {
        num preferentialTotal = SystemConfigTool.doubleSubtractionToDecimal(
            goods.currencyTotal, goods.discountTotal, BillDecimalType.TOTAL);
        PreferentialHelper.setGoodsPreferential(
            goods, Preferential.manualPrice, preferentialTotal);
      }
    } else {
      goods.preferentialHelp.remove(Preferential.manualPrice.name);
    }
  }

  ///价格发生改变
  static void onPriceChange(GoodsDetailDto goods, num price,
      {bool markGift = true,
      bool markManualPrice = true,
      List<GoodsDetailDto> comboDetails = const []}) {
    goods.isManualDiscount = false;
    GoodsPriceUtil.onDiscountPriceChange(goods, price);
    setManualPrice(goods,
        markGift: markGift,
        markManualPrice: markManualPrice,
        comboDetails: comboDetails);
  }

  ///折扣发生改变
  static void onDiscountChange(GoodsDetailDto goods, num discount,
      {bool markGift = true,
      bool markManualPrice = true,
      List<GoodsDetailDto> comboDetails = const []}) {
    goods.isManualDiscount = true;
    GoodsPriceUtil.onDiscountChange(goods, discount);
    setManualPrice(goods,
        markGift: markGift,
        comboDetails: comboDetails,
        markManualPrice: markManualPrice);
  }

  ///数量发生改变
  static void onQtyChange(GoodsDetailDto goods, num qty,
      {List<GoodsDetailDto> comboDetails = const []}) {
    GoodsQtyUtil.onQtyChange(goods, qty, keepDiscount: goods.isManualDiscount);
    setManualPrice(goods, comboDetails: comboDetails);
  }

  ///设置手工改价
  ///若折扣不为1，则标记手工改价
  ///[markManualPrice] 是否标记手工改价
  ///[markGift] 是否标记赠品
  ///[setPreferential] 是否记录优惠辅助信息
  static void setManualPrice(GoodsDetailDto goods,
      {bool markGift = true,
      bool markManualPrice = true,
      bool setPreferential = true,
      List<GoodsDetailDto> comboDetails = const []}) {
    //对于促销赠品，折扣为1，最终金额为0，在商品详情页面修改批次等之后，在这里拦截，避免变成非赠品
    if (BillGoodsUtil.isPromotionGift(goods)) {
      goods.gift = true;
      goods.manualPrice = false;
      return;
    }
    // goods.manualPrice = markManualPrice;
    ///#43958 POS--录入会员信息后支持商品折扣改为1折 #3  这里修改为1折也算手工改价，并且价格可能等于原来的价格
    if (!goods.manualPrice && markManualPrice) {
      goods.manualPrice =
          goods.discount != 1 || goods.discountPrice != goods.currencyPrice;
    }
    if (markGift) {
      goods.gift = goods.discount == 0 && goods.discountTotal == 0;
    }
    if (GoodsTool.isComboRow(goods)) {
      //计算套餐明细行的金额
      ComboPreferentialTool.handleComboDetail(goods, comboDetails,
          totalGetter: (g) => g.currencyTotal,
          totalSetter: (g, total) =>
              GoodsTotalUtil.onCurrencyTotalChange(g, total));
      //计算明细行的手工改价
      ComboPreferentialTool.handleComboDetail(goods, comboDetails,
          totalGetter: (g) => g.discountTotal,
          totalSetter: (g, total) {
            GoodsTotalUtil.onDiscountTotalChange(g, total);
            g.manualPrice = goods.manualPrice;
            g.gift = goods.gift;
            if (setPreferential) {
              _setPreferential(g);
            }
          });
    }
    if (setPreferential) {
      _setPreferential(goods);
    }
  }
}
