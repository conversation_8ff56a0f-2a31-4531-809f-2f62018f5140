import 'package:decimal/decimal.dart';
import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/settlement/entity/score_configuration.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import 'package:halo_utils/utils/math_util.dart';

import '../decimal_display_helper.dart';

/// 创建时间：2/8/23
/// 作者：xiaotiaochong
/// 描述：

class ScoreTool {
  ///使用积分进行抵现
  ///会根据总金额-各种优惠金额，得到能使用的最大积分数，并将其转换为金额
  ///此步骤在填写各种付款方式之前
  ///当未开启积分抵扣，或者未达到积分抵扣门槛时，则积分抵扣0元
  ///当更改了优惠券/整单优惠等优惠金额时，应该重新调用此方法
  static void useScore(
      {required bool checkScoreEnable,
      required int scoreUsed,
      required String sumAllTotal,
      required String scoreDiscountMoney,
      required GoodsBillDto goodsBillDto,
      required ScoreConfiguration scoreConfiguration,
      required VipWithLevelAssertsRightsCardDTO vipInfo,
      required Function(int scoreUsed, String scoreDiscountMoney) callback}) {
    //是否抵达门槛
    bool allow = ScoreTool.allowScoreDiscount(
        sumAllTotal: sumAllTotal,
        scoreDiscountMoney: scoreDiscountMoney,
        goodsBillDto: goodsBillDto,
        scoreConfiguration: scoreConfiguration);
    //如果不支持积分抵扣则不进行
    if (checkScoreEnable && allow) {
      //使用积分数量
      int scoreUsedTemp = getMaxUsableScore(checkScoreEnable, sumAllTotal,
          scoreDiscountMoney, goodsBillDto, scoreConfiguration, vipInfo);
      //这里要求积分抵现，必须是抵现比例的整数倍，也就是说，抵现金额不能是小数（避免出现精度问题）
      //在dart中int之间/符号相除，结果是有小数点的。而~/符号才是java中的/，用来取商
      scoreUsedTemp = scoreUsedTemp ~/
          scoreConfiguration.discountingProportion! *
          scoreConfiguration.discountingProportion!;
      scoreUsed = scoreUsedTemp;
      Decimal discountMoney = MathUtil.division(scoreUsed.toString(),
          scoreConfiguration.discountingProportion.toString());
      scoreDiscountMoney =
          DecimalDisplayHelper.getTotalFixed(discountMoney.toString());
    } else {
      scoreUsed = 0;
      scoreDiscountMoney = "0";
    }
    callback(scoreUsed, scoreDiscountMoney);
  }

  ///获取当前能使用的最大积分
  ///总金额-各种优惠金额
  static int getMaxUsableScore(
      bool checkScoreEnable,
      String sumAllTotal,
      String scoreDiscountMoney,
      GoodsBillDto goodsBillDto,
      ScoreConfiguration scoreConfiguration,
      VipWithLevelAssertsRightsCardDTO vipInfo) {
    //如果没有达到积分抵扣的最低消费门槛
    if (!checkScoreEnable) {
      return 0;
    }
    //减去优惠之后的应收金额
    Decimal maxValue = getMoneyAfterPreferential(
        sumAllTotal, scoreDiscountMoney, goodsBillDto);
    //积分抵现金额上限类型
    // 1.无限制；
    // 2.每笔交易最多抵扣x元；
    // 3.每笔交易最多抵扣x%
    if (scoreConfiguration.discountingLimitType == 2) {
      //固定抵扣的最多金额
      Decimal temp = Decimal.parse(
          (scoreConfiguration.discountingLimitRmb ?? 0).toString());
      //两者取最小值
      if (maxValue.compareTo(temp) > 0) {
        maxValue = temp;
      }
    } else if (scoreConfiguration.discountingLimitType == 3) {
      //按百分比取值
      maxValue = maxValue *
          (Decimal.parse(
                      scoreConfiguration.discountingLimitPercent?.toString() ??
                          "0") /
                  Decimal.fromInt(100))
              .toDecimal();
    }
    //将最大抵扣金额转换成使用的积分数
    int maxScoreCount = maxValue.toBigInt().toInt() *
        (scoreConfiguration.discountingProportion ?? 0);
    //和会员现在的可用积分数量作比较，取最小值
    return (maxScoreCount > vipInfo.asserts!.availableScore!)
        ? vipInfo.asserts?.availableScore ?? 0
        : maxScoreCount;
  }

  ///获取（订单总价-总优惠） 的应收金额（此应收包含积分抵扣的金额）
  static Decimal getMoneyAfterPreferential(
    String sumAllTotal,
    String scoreDiscountMoney,
    GoodsBillDto goodsBillDto,
  ) {
    return MathUtil.add(
        MathUtil.subtraction(
                sumAllTotal,
                MathUtil.add(
                        goodsBillDto.currencyOrderPreferentialAllotTotal
                            .toString(),
                        goodsBillDto.discountPreferential.toString())
                    .toString())
            .toString(),
        scoreDiscountMoney);
  }

  ///是否达到积分抵扣最低消费门槛
  static bool allowScoreDiscount(
      {required String sumAllTotal,
      required String scoreDiscountMoney,
      required GoodsBillDto goodsBillDto,
      required ScoreConfiguration scoreConfiguration}) {
    Decimal total = getMoneyAfterPreferential(
        sumAllTotal, scoreDiscountMoney, goodsBillDto);
    Decimal threshold =
        Decimal.parse(getScoreDiscountThreshold(scoreConfiguration).toString());
    return total >= threshold;
  }

  ///获取折扣最低门槛金额
  static Decimal getScoreDiscountThreshold(
      ScoreConfiguration scoreConfiguration) {
    if (scoreConfiguration.discountingThresholdType == 2) {
      //最低消费门槛
      return Decimal.parse(
          scoreConfiguration.discountingThresholdNum.toString());
    } else {
      //无门槛
      return Decimal.parse(0.toString());
    }
  }
}
