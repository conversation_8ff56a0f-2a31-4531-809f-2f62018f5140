import 'package:decimal/decimal.dart';

import '../../../common/tool/sp_tool.dart';
import '../../../common/tool/system_config_tool.dart';
import '../../../entity/system/system_config_dto.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../entity/goods_detail_dto.dart';
import '../goods_tool.dart';

///商品价格工具类
class GoodsPriceUtil {
  GoodsPriceUtil._();

  ///折后单价改变
  ///先根据折后单价计算成折后金额（单价*数量）
  ///再计算折扣（折后金额/金额）
  ///[price] 折后单价,>=0
  static void onDiscountPriceChange(GoodsDetailDto goods, num price) {
    goods.discountPrice = price;
    if (price == goods.currencyPrice) {
      goods.discountTotal = goods.currencyTotal;
      goods.discount = 1;
    } else {
      goods.discountTotal = SystemConfigTool.doubleMultipleToDecimal(
          goods.discountPrice, goods.unitQty, BillDecimalType.TOTAL);
      goods.discount = SystemConfigTool.doubleDivisionToDecimal(
          goods.discountTotal, goods.currencyTotal, BillDecimalType.DISCOUNT);
    }
    onPromotedPriceChange(goods, price,
        total: goods.discountTotal, discount: goods.discount);
  }

  ///折扣改变
  ///先根据折扣算出折后金额(金额*折扣)
  ///再根据折后金额算出折后单价(折后金额/数量)
  ///[discount] 折扣，>=0,<=1
  static void onDiscountChange(GoodsDetailDto goods, num discount) {
    goods.discount = discount;
    if (goods.discount == 1) {
      goods.discountTotal = goods.currencyTotal;
      goods.discountPrice = goods.currencyPrice;
    } else {
      goods.discountTotal = SystemConfigTool.doubleMultipleToDecimal(
          goods.currencyTotal, goods.discount, BillDecimalType.TOTAL);
      goods.discountPrice = SystemConfigTool.doubleDivisionToDecimal(
          goods.discountTotal, goods.unitQty, BillDecimalType.PRICE);
    }
    onPromotedPriceChange(goods, goods.discountPrice,
        total: goods.discountTotal, discount: goods.discount);
  }

  ///设置优惠分摊前(促销后)的价格
  ///即[GoodsDetailDto.promotedPrice]改变
  ///[price] 优惠分摊前价格
  ///[total] 优惠分摊前金额，传了之后就不会进行计算
  ///[discount] 最终折扣，传了之后就不会进行计算
  static void onPromotedPriceChange(GoodsDetailDto goods, num price,
      {num? total, num? discount}) {
    //优惠分摊前单价
    goods.promotedPrice = price;
    //优惠分摊前金额
    goods.promotedTotal = total ??
        SystemConfigTool.doubleMultipleToDecimal(
            goods.promotedPrice, goods.unitQty, BillDecimalType.TOTAL);
    //优惠分摊前优惠
    goods.preferentialDiscount = SystemConfigTool.doubleSubtractionToDecimal(
        goods.currencyTotal, goods.promotedTotal, BillDecimalType.TOTAL);
    //单品优惠
    goods.currencyPtypePreferentialTotal =
        SystemConfigTool.doubleSubtractionToDecimal(
            goods.discountTotal, goods.promotedTotal, BillDecimalType.TOTAL);
    onFinalPriceChange(goods, price,
        total: goods.promotedTotal,
        discount: discount,
        billPreferentialTotal: 0,
        allPreferentialTotal: goods.preferentialDiscount);
  }

  ///最终价格改变(含赠金)
  ///[price] 最终价格
  ///[total] 最终金额，传了之后就不会进行计算
  ///[discount] 最终折扣，传了之后就不会进行计算
  ///[billPreferentialTotal] 优惠分摊金额，传了之后就不会进行计算
  ///[allPreferentialTotal] 最终优惠金额，传了之后就不会进行计算
  static void onFinalPriceChange(
    GoodsDetailDto goods,
    num price, {
    num? total,
    num? discount,
    num? billPreferentialTotal,
    num? allPreferentialTotal,
  }) {
    //最终单价（含赠金）
    goods.posCurrencyDisedTaxedPrice = price;
    //最终金额（含赠金）
    goods.posCurrencyDisedTaxedTotal = total ??
        SystemConfigTool.doubleMultipleToDecimal(
            goods.posCurrencyDisedTaxedPrice,
            goods.unitQty,
            BillDecimalType.TOTAL);
    //赠金为0两者相同
    if (goods.givePreferentialTotal == 0) {
      goods.currencyDisedTaxedTotal = goods.posCurrencyDisedTaxedTotal;
      goods.currencyDisedTaxedPrice = goods.posCurrencyDisedTaxedPrice;
    } else {
      goods.currencyDisedTaxedTotal =
          SystemConfigTool.doubleSubtractionToDecimal(
            goods.posCurrencyDisedTaxedTotal,
            goods.givePreferentialTotal,
            BillDecimalType.TOTAL,
          );
      goods.currencyDisedTaxedPrice = SystemConfigTool.doubleDivisionToDecimal(
        goods.currencyDisedTaxedTotal,
        goods.unitQty,
        BillDecimalType.PRICE,
      );
    }
    //最终折扣
    goods.lastDiscount =
        discount ??
        SystemConfigTool.doubleDivisionToDecimal(
          goods.currencyDisedTaxedTotal,
          goods.currencyTotal,
          BillDecimalType.DISCOUNT,
        );
    //优惠分摊金额
    goods.currencyOrderPreferentialAllotTotal =
        billPreferentialTotal ??
        SystemConfigTool.doubleSubtractionToDecimal(
          goods.promotedTotal,
          goods.posCurrencyDisedTaxedTotal,
          BillDecimalType.TOTAL,
        );
    //最终优惠
    goods.currencyPreferentialTotal =
        allPreferentialTotal ??
        SystemConfigTool.doubleSubtractionToDecimal(
          goods.currencyTotal,
          goods.currencyDisedTaxedTotal,
          BillDecimalType.TOTAL,
        );
    //计算不含税单价和金额
    TaxUtil.calculateDisedPriceAndTotal(goods);
  }
}

class GoodsQtyUtil {
  GoodsQtyUtil._();

  ///数量发生改变
  ///[keepDiscount] true=>折扣保持不变，由折扣计算单价，false=>单价保持不变，由单价计算折扣
  static void onQtyChange(GoodsDetailDto goods, num qty,
      {bool keepDiscount = true}) {
    goods.unitQty = qty;

    // 对于手工改价的商品（discountSourceType = 3），保持原有价格和折扣不变
    if (goods.discountSourceType == 3) {
      // 设置手工改价标记
      goods.manualPrice = true;
      // 更新基础金额
      goods.currencyTotal = SystemConfigTool.doubleMultipleToDecimal(
        goods.currencyPrice,
        goods.unitQty,
        BillDecimalType.TOTAL,
      );
      goods.costTotal = SystemConfigTool.doubleMultipleToDecimal(
        goods.costPrice,
        goods.unitQty,
        BillDecimalType.TOTAL,
      );

      // 从 currencyDisedPrice 和 currencyDisedTotal 恢复正确的折后价格
      // 这些字段通常保存了正确的手工改价数据
      if (goods.currencyDisedPrice > 0 && goods.currencyDisedTotal > 0) {
        goods.discountPrice = goods.currencyDisedPrice;
        goods.discountTotal = goods.currencyDisedTotal;
      } else {
        // 如果没有，则根据折扣计算
        goods.discountTotal = SystemConfigTool.doubleMultipleToDecimal(
          goods.currencyTotal,
          goods.discount,
          BillDecimalType.TOTAL,
        );
        goods.discountPrice = SystemConfigTool.doubleDivisionToDecimal(
          goods.discountTotal,
          goods.unitQty,
          BillDecimalType.PRICE,
        );
      }

      // 调用完整的价格计算链，确保所有相关字段正确计算
      GoodsPriceUtil.onPromotedPriceChange(
        goods,
        goods.discountPrice,
        total: goods.discountTotal,
        discount: goods.discount,
      );
      return;
    }

    //数量发生改变，需要重新计算金额
    goods.currencyTotal = SystemConfigTool.doubleMultipleToDecimal(
      goods.currencyPrice,
      goods.unitQty,
      BillDecimalType.TOTAL,
    );
    goods.costTotal = SystemConfigTool.doubleMultipleToDecimal(
      goods.costPrice,
      goods.unitQty,
      BillDecimalType.TOTAL,
    );
    if (keepDiscount) {
      GoodsPriceUtil.onDiscountChange(goods, goods.discount);
    } else {
      GoodsPriceUtil.onDiscountPriceChange(goods, goods.discountPrice);
    }
  }
}

///处理商品total发生改变，
///例如：
///优惠分摊时，改变了total，需要反算单价和折扣
///在更改了套餐的价格时，对于套餐明细其实是改变了total，需要反算单价和折扣
class GoodsTotalUtil {
  GoodsTotalUtil._();

  ///金额发生改变（计算套餐明细行单价用）
  static void onCurrencyTotalChange(
    GoodsDetailDto goods,
    num total, {
    bool calculateDiscount = true,
  }) {
    goods.currencyTotal = total;
    num price = SystemConfigTool.doubleDivisionToDecimal(
      goods.currencyTotal,
      goods.unitQty,
      BillDecimalType.PRICE,
    );
    goods.currencyPrice = price;
    if (calculateDiscount) {
      onDiscountTotalChange(goods, total);
    }
  }

  ///折后金额发生改变
  static void onDiscountTotalChange(GoodsDetailDto goods, num total) {
    goods.discountTotal = total;
    if (goods.discountTotal == goods.currencyTotal) {
      goods.discount = 1;
      goods.discountPrice = goods.currencyPrice;
    } else {
      goods.discount = SystemConfigTool.doubleDivisionToDecimal(
        goods.discountTotal,
        goods.currencyTotal,
        BillDecimalType.DISCOUNT,
      );
      goods.discountPrice = SystemConfigTool.doubleDivisionToDecimal(
        goods.discountTotal,
        goods.unitQty,
        BillDecimalType.PRICE,
      );
    }
    GoodsPriceUtil.onPromotedPriceChange(
      goods,
      goods.discountPrice,
      total: goods.discountTotal,
      discount: goods.discount,
    );
  }

  ///促销后金额发生改变(订单满减)
  static void onPromotedTotalChange(GoodsDetailDto goods, num total) {
    goods.promotedTotal = total;
    num price = SystemConfigTool.doubleDivisionToDecimal(
      goods.promotedTotal,
      goods.unitQty,
      BillDecimalType.PRICE,
    );
    GoodsPriceUtil.onPromotedPriceChange(goods, price, total: total);
  }

  ///最终金额发生改变(优惠分摊)含赠金
  static void onFinalTotalChange(GoodsDetailDto goods, num total) {
    goods.posCurrencyDisedTaxedTotal = total;
    num price = SystemConfigTool.doubleDivisionToDecimal(
      goods.posCurrencyDisedTaxedTotal,
      goods.unitQty,
      BillDecimalType.PRICE,
    );
    GoodsPriceUtil.onFinalPriceChange(goods, price, total: total);
  }
}

///处理商品不含税金额的工具类
///对于商品，不含税单价= 含税单价（现价） / （1+税率）
class TaxUtil {
  const TaxUtil._();

  ///根据商品的税率和含税单价，计算商品的不含税单价和不含税金额
  static void calculateDisedPriceAndTotal(GoodsDetailDto goods) {
    SystemConfigDto sysConfig = SpTool.getSystemConfig();
    //开启计税和销售计税
    if (sysConfig.sysGlobalEnabledTax && sysConfig.sysGlobalEnabledSaleTax) {
      if (goods.taxRate > 0) {
        Decimal temp =
            Decimal.fromInt(1) +
            (Decimal.parse(goods.taxRate.toString()) / Decimal.fromInt(100))
                .toDecimal(scaleOnInfinitePrecision: 8);
        goods.currencyDisedTotal = SystemConfigTool.doubleDivisionToDecimal(
          goods.currencyDisedTaxedTotal,
          temp.toDouble(),
          BillDecimalType.TOTAL,
        );
        goods.currencyDisedPrice = SystemConfigTool.doubleDivisionToDecimal(
          goods.currencyDisedTotal,
          goods.unitQty,
          BillDecimalType.PRICE,
        );
        goods.currencyTaxTotal = SystemConfigTool.doubleSubtractionToDecimal(
          goods.currencyDisedTaxedTotal,
          goods.currencyDisedTotal,
          BillDecimalType.TOTAL,
        );
        return;
      }
    }
    //不计税
    goods.currencyDisedPrice = goods.currencyDisedTaxedPrice;
    goods.currencyDisedTotal = goods.currencyDisedTaxedTotal;
    goods.currencyTaxTotal = 0;
  }

  static void handleComboTaxByGoodsList(List<GoodsDetailDto> goodsList) {
    Map<String, GoodsDetailDto> comboMap = {};
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    GoodsTool.filterGoodsListAndGetCombo(
      goodsList,
      comboMap: comboMap,
      comboDetailsMap: comboDetailsMap,
    );
    for (var combo in comboMap.values) {
      handleComboTax(combo, comboDetailsMap[combo.comboRowId] ?? []);
    }
  }

  ///套餐行的税额需要明细行税额累加
  static void handleComboTax(
    GoodsDetailDto combo,
    List<GoodsDetailDto> comboDetails,
  ) {
    combo.currencyTaxTotal =
        comboDetails
            .fold<Decimal>(
              Decimal.zero,
              (previousValue, element) =>
                  previousValue +
                  (Decimal.tryParse(element.currencyTaxTotal.toString()) ??
                      Decimal.zero),
            )
            .toDouble();
  }
}
