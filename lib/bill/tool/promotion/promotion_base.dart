import 'dart:collection';
import 'dart:convert';
import 'dart:math';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import 'package:halo_pos/common/tool/system_config_tool.dart';
import 'package:halo_pos/enum/bill_decimal_type.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../../entity/bill_promotion_info_dto.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../goods_tool.dart';
import 'discount.dart';
import 'preferential.dart';
import 'price.dart';
import 'promotion.dart';
import 'promotion_implement.dart';
import 'rule_model.dart';

//region 促销基类
///促销处理类基类
abstract class PromotionHandler {
  ///当前类型
  final PromotionType promotionType;

  ///当前handler下的促销列表
  final List<BillPromotionInfoDto> promotionList;

  ///单据
  final GoodsBillDto billDto;

  ///是否是有效会员
  final bool isVip;

  ///原始商品列表
  final List<GoodsDetailDto> originalGoodsList;

  ///套餐明细行,key为comboRowParId
  final Map<String, List<GoodsDetailDto>> comboDetailsMap;

  const PromotionHandler(
      {required this.promotionList,
        required this.promotionType,
        required this.billDto,
        required this.isVip,
        required this.originalGoodsList,
        required this.comboDetailsMap});

  factory PromotionHandler.create(
      {required List<BillPromotionInfoDto> promotionList,
        required PromotionType promotionType,
        required GoodsBillDto billDto,
        required bool isVip,
        required List<GoodsDetailDto> originalGoodsList,
        required Map<String, List<GoodsDetailDto>> comboDetailsMap,
        filter = false}) {
    //根据促销类型筛选出促销列表
    if (filter) {
      promotionList = promotionList
          .where((element) => element.promotionType == promotionType.value)
          .toList();
    }
    //按照优先级排序，需要注意，优先级高的值更大
    promotionList.sort((a, b) => b.priority.compareTo(a.priority));
    //根据促销类型创建handler
    switch (promotionType) {
      case PromotionType.specialPrice:
        return SpecialPricePromotionHandler(
            promotionList: promotionList,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);
      case PromotionType.halfPrice:
        return HalfPricePromotionHandler(
            promotionList: promotionList,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);
      case PromotionType.fullCountGift:
        return FullCountGiftPromotionHandler(
            promotionList: promotionList,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);
      case PromotionType.fullAmountGift:
        return FullAmountGiftPromotionHandler(
            promotionList: promotionList,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);
      case PromotionType.orderFullReduction:
        return OrderFullReductionPromotionHandler(
            promotionList: promotionList,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);
      case PromotionType.combinationBuy:
        return CombinationBuyPromotionHandler(
            promotionList: promotionList,
            billDto: billDto,
            isVip: isVip,
            originalGoodsList: originalGoodsList,
            comboDetailsMap: comboDetailsMap);
      default:
        throw Exception("促销类型错误");
    }
  }

  ///处理促销
  ///[goodsList] 商品列表
  ///返回处理后的商品列表
  List<GoodsDetailDto> handle(
      List<GoodsDetailDto> goodsList,
      ) {
    for (var promotion in promotionList) {
      if (goodsList.isNotEmpty) {
        handlePromotion(
          goodsList: goodsList,
          promotion: promotion,
        );
      }
    }
    return goodsList;
  }

  ///处理单个促销
  ///
  ///[goodsList] 商品列表
  ///[promotion] 当前促销
  void handlePromotion(
      {required List<GoodsDetailDto> goodsList,
        required BillPromotionInfoDto promotion});

  ///判断商品是否在促销中
  ///[goods] 当前商品
  ///[goodsScope] 当前促销的商品范围规则
  ///[promotion] 当前促销
  ///[validateBatchNo] 是否校验批次号，5.3中对特价商品促销增加了批次号的校验
  ///[onGoodsInPromotion] 如果商品在促销的[选中的商品范围]中，则执行回调
  ///[选中的商品范围]即，部分商品，或排除部分商品时，会选中具体的商品
  ///因为先执行了权益卡打折，若促销设置了不和权益卡同时执行，则不执行此促销，返回false
  bool isGoodsInPromotion({
    required GoodsDetailDto goods,
    required int? goodsScope,
    required BillPromotionInfoDto promotion,
    bool validateBatchNo = false,
    //[promotionPtype] 促销中的商品，[goods]当前商品,[goodsRuleType] 当前促销的商品范围规则
    void Function(PromotionPtype promotionPtype, GoodsDetailDto goods,
        int goodsRuleType)?
    onGoodsInPromotion,
  }) {
    //全部商品
    if (goodsScope == PromotionGoodsScope.allGoods.value) {
      return true;
    }
    //条件内是否包含该商品
    bool contains = false;
    //部分商品和排除部分商品
    if (goodsScope == PromotionGoodsScope.partGoods.value ||
        goodsScope == PromotionGoodsScope.excludeGoods.value) {
      //匹配到的规则商品
      PromotionPtype? ptype = promotion.ptypeList.firstWhereOrNull((element) =>
      GoodsTool.compareUnitSkuById(
          ptypeId1: goods.ptypeId,
          skuId1: goods.skuId ?? "",
          unitId1: goods.unitId,
          ptypeId2: element.ptypeId ?? "",
          skuId2: element.skuId ?? "",
          unitId2: element.unitId ?? "",
          isCombo: GoodsTool.isComboRow(goods),
          compareSku: StringUtil.isNotZeroOrEmpty(element.skuId)) &&
          (!validateBatchNo ||
              element.batchEnabled != true ||
              StringUtil.isEmpty(element.batchNo) ||
              element.batchNo == goods.batchNo ||
              element.batchNos?.isNotEmpty != true ||
              element.batchNos?.any((element) => element == goods.batchNo) ==
                  true));
      contains = ptype != null;
      //触发找到商品的回调,方便外部对读取商品的折扣或者价格
      if (contains) {
        //记录商品在促销中的信息
        onGoodsInPromotion?.call(ptype, goods, goodsScope!);
      }
      //部分商品
      if (goodsScope == PromotionGoodsScope.partGoods.value) {
        return contains;
      }
      //排除部分商品
      else if (goodsScope == PromotionGoodsScope.excludeGoods.value) {
        return !contains;
      }
      return false;
    }
    //暂未支持商品分类
    return false;
  }

  ///将促销规则转化为实体类
  RuleModel? parseStrategyRule(StrategyBean? strategy, {String? ruleString}) {
    ruleString ??= strategy?.rule;
    RuleModel? rule;
    if (ruleString != null) {
      try {
        rule = RuleModel.fromMap(jsonDecode(ruleString));
      } catch (e) {
        debugPrint("促销规则解析失败$e,$ruleString");
      }
    }
    return rule;
  }

  ///添加促销提示
  ///同一个促销类型只能允许一个促销提示
  ///当添加执行时的促销提示，需要清除之前此种类的提示，再添加现在的提示
  ///当添加未执行时的促销提示，若之前已经有此种类提示，则不添加
  ///[isSameGoods] 是否是买赠同品的提示
  ///对于满件赠和满额赠，允许同一个促销，两种促销方式的提示同时存在。所以需要传入[currentPromotionId]和[isSameGoods]
  void addGoodsListHint({
    required List<GoodsDetailDto> goodsList,
    required PromotionHints? hint,
    required bool executed,
    int? giftScope,
    String? currentPromotionId,
  }) {
    if (hint == null) {
      return;
    }
    for (var goods in goodsList) {
      addGoodsHint(
          goods: goods,
          hint: hint,
          executed: executed,
          giftScope: giftScope,
          currentPromotionId: currentPromotionId);
    }
  }

  ///添加促销提示
  ///同一个促销类型只能允许一个促销提示
  ///当添加执行时的促销提示，需要清除之前此种类的提示，再添加现在的提示
  ///当添加未执行时的促销提示，若之前已经有此种类提示，则不添加
  ///[isSameGoods] 是否是买赠同品的提示
  ///对于满件赠和满额赠，允许同一个促销，3种促销方式的提示同时存在
  void addGoodsHint({
    required GoodsDetailDto goods,
    required PromotionHints hint,
    required bool executed,
    int? giftScope,
    String? currentPromotionId,
  }) {
    goods.promotionHints ??= [];
    addHint(
        hintList: goods.promotionHints!,
        hint: hint,
        executed: executed,
        giftScope: giftScope,
        currentPromotionId: currentPromotionId);
  }

  ///添加促销提示
  ///同一个促销类型只能允许一个促销提示
  ///当添加执行时的促销提示，需要清除之前此种类的提示，再添加现在的提示
  ///当添加未执行时的促销提示，若之前已经有此种类提示，则不添加
  ///对于满件赠和满额赠，允许同一个促销，3种促销方式的提示同时存在。
  void addHint({
    required List<PromotionHints> hintList,
    required PromotionHints hint,
    required bool executed,
    int? giftScope,
    String? currentPromotionId,
  }) {
    if (executed) {
      removeCurrentPromotionTypeHintFromHintList(hintList,
          giftScope: giftScope, currentPromotionId: currentPromotionId);
      hintList.add(hint);
    } else {
      if (!hintList.any((element) {
        if (element.promotionType != promotionType.value) {
          return false;
        }
        //满件赠满额赠，允许买赠同品和指定赠品的提示同时出现
        if (promotionType == PromotionType.fullAmountGift ||
            promotionType == PromotionType.fullCountGift) {
          return element.promotionGiftScope == giftScope;
        }
        return true;
      })) {
        hintList.add(hint);
      }
    }
  }

  ///移除商品中当前促销类型的所有促销提示
  void removeCurrentPromotionTypeHint(List<GoodsDetailDto> goodsList,
      {int? giftScope, String? currentPromotionId}) {
    for (var goods in goodsList) {
      removeCurrentPromotionTypeHintFromHintList(goods.promotionHints,
          giftScope: giftScope, currentPromotionId: currentPromotionId);
    }
  }

  ///移除促销提示中中当前促销类型的所有促销提示
  void removeCurrentPromotionTypeHintFromHintList(
      List<PromotionHints>? hintList,
      {int? giftScope,
        String? currentPromotionId}) {
    hintList?.removeWhere((element) {
      if (element.promotionType != promotionType.value) {
        return false;
      }
      //满件赠满额赠，允许买赠同品和指定赠品的提示同时出现
      if (promotionType == PromotionType.fullAmountGift ||
          promotionType == PromotionType.fullCountGift) {
        return element.promotionGiftScope == giftScope &&
            element.promotionId == currentPromotionId;
      }
      return true;
    });
  }

  ///商品关联促销
  void setGoodsPromotion(BillPromotionInfoDto promotion, GoodsDetailDto goods) {
    PromotionUtil.setGoodsPromotion(goods: goods, promotion: promotion);
    if (goods.comboRow) {
      comboDetailsMap[goods.comboRowId]?.forEach((detail) {
        PromotionUtil.setGoodsPromotion(goods: detail, promotion: promotion);
      });
    }
  }

// ///当促销不和权益卡打折叠加时，需要移除权益卡打折，重新执行打折
// void handleJoinRightsCardDiscount(
//     {required GoodsDetailDto goods,
//     required bool joinVipRights,
//     List<GoodsDetailDto>? comboDetails}) {
//   if (isVip && !joinVipRights) {
//     DiscountHandler.handleGoodsDiscountByPreferentialInfo(
//         goods: goods,
//         isVip: isVip,
//         comboDetails: comboDetails ??
//             GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap),
//         executeRightsCard: false);
//   }
// }

  ///当促销不与会员权益叠加时，优先执行促销，将折扣还原成10折
  void handleJoinVipRights(
      {required GoodsDetailDto goods,
        required bool joinVipRights,
        List<GoodsDetailDto>? comboDetails}) {
    if (isVip && !joinVipRights) {
      // DiscountHandler.handleGoodsDiscountByPreferentialInfo(
      //     goods: goods,
      //     isVip: isVip,
      //     comboDetails: comboDetails ??
      //         GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap),
      //     executeRightsCard: false);
      DiscountUtil.resetDiscountTo1(
        goods,
        comboDetails: comboDetails ??
            GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap),
      );
    }
  }
}

///处理带商品赠品的促销的mixin
mixin PromotionGoodsGiftHandlerMixin on PromotionHandler {
  ///当前促销种类下的赠品列表
  List<GoodsDetailDto>? currentPromotionTypeGoodsGiftList;

  ///参与促销的赠品列表
  final Set<GoodsDetailDto> executedGoodsGiftSet = {};

  @override
  List<GoodsDetailDto> handle(List<GoodsDetailDto> goodsList) {
    //初始化赠品列表
    initGoodsGiftList();
    List<GoodsDetailDto> result = super.handle(goodsList);
    //移除没有被执行的赠品
    removeNotExecutedGoodsGift();
    return result;
  }

  ///初始化赠品列表
  void initGoodsGiftList() {
    //找出现在商品列表里面已经有的促销赠品
    currentPromotionTypeGoodsGiftList ??= originalGoodsList
        .where((goods) =>
    goods.promotionGift && goods.promotionType == promotionType.value)
        .toList();
    executedGoodsGiftSet.clear();
  }

  ///移除没有被执行的赠品
  void removeNotExecutedGoodsGift() {
    if (currentPromotionTypeGoodsGiftList != null) {
      for (var goods in currentPromotionTypeGoodsGiftList!) {
        if (!executedGoodsGiftSet.contains(goods)) {
          originalGoodsList.remove(goods);
          if (goods.comboRow) {
            originalGoodsList.removeWhere(
                    (element) => element.comboRowParId == goods.comboRowId);
          }
        }
      }
    }
  }

  ///将赠品添加到促销商品后面
  /// [isAdd] 是否是新增的赠品
  /// [giftList] 赠品
  /// [goods] 当前促销商品
  void insertGiftAfterGoods(
      {required bool isAdd,
        required List<GoodsDetailDto> giftList,
        required GoodsDetailDto goods}) {
    int index = -1;
    //对于套餐，需要添加到最后一个套餐明细行后面
    if (goods.comboRow) {
      index = originalGoodsList.lastIndexWhere(
              (element) => element.comboRowParId == goods.comboRowId);
    }
    if (index < 0) {
      index = originalGoodsList.indexOf(goods);
    }
    if (index < 0) {
      index = originalGoodsList.length - 1;
    }
    PromotionUtil.insertGiftAfterIndex(
        isAdd: isAdd,
        originalGoodsList: originalGoodsList,
        giftList: giftList,
        index: index);
  }

  ///根据商品和促销id找到赠品
  ///[promotionGiftScope] 赠品范围
  GoodsDetailDto? findSameGift(GoodsDetailDto goods, String promotionId,
      {int? promotionGiftScope}) {
    promotionGiftScope ??= PromotionGiftScope.chooseGoods.value;
    return currentPromotionTypeGoodsGiftList?.firstWhereOrNull((element) =>
    element.promotionId == promotionId &&
        GoodsTool.compareUnitSku(goods, element, comparePrice: false) &&
        element.promotionGiftScope == promotionGiftScope);
  }
}

///处理带优惠券赠品的促销的mixin
mixin PromotionCouponGiftHandlerMixin on PromotionHandler {
  ///当前促销类型下的优惠券赠品
  List<CouponGift> currentPromotionTypeCouponGiftList = [];

  ///当前促销执行后的优惠券赠品
  Set<CouponGift> executedCouponGiftList = {};

  @override
  List<GoodsDetailDto> handle(List<GoodsDetailDto> goodsList) {
    //初始化赠品列表
    initCouponGiftList();
    List<GoodsDetailDto> result = super.handle(goodsList);
    //移除没有被执行的赠品
    removeNotExecutedCouponGift();
    return result;
  }

  ///初始化赠品列表
  void initCouponGiftList() {
    //找出现在商品列表里面已经有的促销赠品
    currentPromotionTypeCouponGiftList = billDto.giftCouponList
        .where((coupon) => coupon.promotionType == promotionType.value)
        .toList();
    executedCouponGiftList.clear();
  }

  ///移除没有被执行的赠品
  void removeNotExecutedCouponGift() {
    if (currentPromotionTypeCouponGiftList.isNotEmpty) {
      for (var gift in currentPromotionTypeCouponGiftList) {
        if (!executedCouponGiftList.contains(gift)) {
          billDto.giftCouponList.remove(gift);
        }
      }
    }
  }
}

///处理满件赠，满额赠等多个商品，和多个赠品的促销mixin
mixin MultiGoodsAndGiftHandlerMixin
on
    PromotionGoodsGiftHandlerMixin,
    PromotionCouponGiftHandlerMixin,
    PromotionConditionMixin {
  ///已经执行的促销id
  final Set<String> executedPromotionIdSet = {};

  @override
  List<GoodsDetailDto> handle(List<GoodsDetailDto> goodsList) {
    List<GoodsDetailDto> result = super.handle(goodsList);
    //移除没有被执行的促销的赠品记录
    billDto.promotionGiftRecord?.removeWhere((element) =>
    !executedPromotionIdSet.contains(element.promotionId) &&
        element.promotionType == promotionType.value);
    return result;
  }

  ///处理单个促销
  ///[goodsList] 商品列表
  ///[promotion] 当前促销
  /// 对于满件赠促销
  /// strategy有3种
  /// strategyType=0，即商品范围，rule和其他规则一样，都是{"type":1},即部分商品
  /// strategyType=1, 赠品类型，rule为{"type":1}，其中type 1 为商品，6为优惠券
  /// strategyType=2,即促销条件，rule比较复杂:
  /// {"conditionList":[{"conditionTotal":12,"preferential":1}],"typeWay":1,"type":1,"maxTotal":1,"doubleSend":false}
  /// conditionList 促销阶梯条件列表 其中conditionTotal为条件，例如满xx件，preferential为赠品数量
  /// typeWay 规则计算法方式，0为按活动商品总数量计算，1为按单个商品计算
  /// maxTotal 此促销最大赠品数量，只有开启了倍送之后才会生效
  /// doubleSend 是否开启倍送
  /// type 赠品范围，0为买赠同品，1为指定赠品
  ///
  /// 对于满额赠，大部分和满件赠一致，只有conditionTotal代表金额，如满xx元
  @override
  void handlePromotion({
    required List<GoodsDetailDto> goodsList,
    required BillPromotionInfoDto promotion,
  }) {
    //商品范围
    StrategyBean? goodsScopeStrategy;
    //赠品类型
    StrategyBean? giftTypeStrategy;
    //促销规则
    StrategyBean? conditionStrategy;
    for (var strategy in promotion.strategy) {
      if (strategy.strategyType == PromotionStrategyType.promotionGoods.value) {
        goodsScopeStrategy ??= strategy;
      } else if (strategy.strategyType ==
          PromotionStrategyType.promotionWay.value) {
        giftTypeStrategy ??= strategy;
      } else if (strategy.strategyType ==
          PromotionStrategyType.promotionCondition.value) {
        conditionStrategy ??= strategy;
      }
      if (goodsScopeStrategy != null &&
          giftTypeStrategy != null &&
          conditionStrategy != null) break;
    }
    //参加促销的商品范围
    int? goodsScope = parseStrategyRule(goodsScopeStrategy)?.type;
    //促销规则
    //买赠同品
    RuleModel? sameGoodsConditionRule =
    _parseCondition(conditionStrategy, PromotionGiftScope.sameGoods);
    //自选赠品
    RuleModel? chooseGoodsConditionRule =
    _parseCondition(conditionStrategy, PromotionGiftScope.chooseGoods);
    //指定赠品
    RuleModel? specifiedGoodsConditionRule =
    _parseCondition(conditionStrategy, PromotionGiftScope.specifiedGoods);
    if (goodsScope == null) return;

    if (specifiedGoodsConditionRule?.conditionList.isEmpty == true &&
        chooseGoodsConditionRule?.conditionList.isEmpty == true &&
        sameGoodsConditionRule?.conditionList.isEmpty == true) return;

    //促销是否执行
    bool executed = false;
    //此促销内的商品
    LinkedHashSet<GoodsDetailDto>? promotionGoodsSet;

    //这里需要调整，现在促销规则允许3套,且3套规则不同
    //1.买赠同品（绑定按照单品计数）
    //2.选赠品
    //3.指定赠品

    //选择赠品
    if (chooseGoodsConditionRule?.conditionList.isNotEmpty == true) {
      executeChooseGoods(
          goodsList: goodsList,
          promotion: promotion,
          goodsScope: goodsScope,
          conditionRule: chooseGoodsConditionRule!,
          onResult: (executed_, goodsSet_) {
            executed = executed_;
            promotionGoodsSet = goodsSet_;
          });
    }
    if (!executed) {
      //未执行指定赠品，移除赠品记录
      billDto.promotionGiftRecord
          ?.removeWhere((element) => element.promotionId == promotion.id);
    }
    //指定赠品
    if (specifiedGoodsConditionRule?.conditionList.isNotEmpty == true) {
      executeSpecifiedGoods(
          goodsList: goodsList,
          promotion: promotion,
          goodsScope: goodsScope,
          conditionRule: specifiedGoodsConditionRule!,
          onResult: (executed_, goodsSet_) {
            executed = executed_;
            promotionGoodsSet = goodsSet_;
          });
    }
    //买赠同品
    if (sameGoodsConditionRule?.conditionList.isNotEmpty == true) {
      executeSameGoods(
          goodsList: goodsList,
          promotion: promotion,
          goodsScope: goodsScope,
          conditionRule: sameGoodsConditionRule!,
          promotionGoodsSet: promotionGoodsSet,
          onResult: (executed_, goodsSet_) {
            executed |= executed_;
            promotionGoodsSet = goodsSet_;
          });
    }
    if (executed) {
      executedPromotionIdSet.add(promotion.id);
      if (promotionGoodsSet != null) {
        for (var goods in promotionGoodsSet!) {
          List<GoodsDetailDto>? comboDetails;
          if (goods.comboRow) {
            comboDetails = comboDetailsMap[goods.comboRowId];
          }
          //当不叠加权益时，将折扣还原
          handleJoinVipRights(
              goods: goods,
              joinVipRights: promotion.vipRights,
              comboDetails: comboDetails);
          PromotionUtil.setPreferential(goods, promotion, 0,
              comboDetails: comboDetails);
        }
        goodsList
            .removeWhere((element) => promotionGoodsSet!.contains(element));
      }
    }
  }

  ///解析满件赠/满额赠 规则
  RuleModel? _parseCondition(
      StrategyBean? conditionStrategy, PromotionGiftScope giftScope) {
    Map<String, dynamic>? ruleMap;
    RuleModel? rule;
    if (conditionStrategy != null) {
      try {
        ruleMap = jsonDecode(conditionStrategy.rule);
        ruleMap = ruleMap?[giftScope.key];
        rule = RuleModel.fromMap(ruleMap);
      } catch (e) {
        debugPrint("促销规则解析失败$e");
      }
    }
    return rule;
  }

  void _handleGoods({
    required List<GoodsDetailDto> goodsList,
    required int goodsScope,
    required BillPromotionInfoDto promotion,
    required RuleModel conditionRule,
    required Function(num giftCount, bool executed,
        LinkedHashSet<GoodsDetailDto> promotionGoodsSet)
    onResult,
    required PromotionGiftScope giftScope,
    LinkedHashSet<GoodsDetailDto>? promotionGoodsSet,
  }) {
    //按单个商品计算
    if (conditionRule.typeWay == PromotionGoodsCountType.singleGoods.value) {
      handleSingleGoods(
          goodsList: goodsList,
          goodsScope: goodsScope,
          promotion: promotion,
          conditionRule: conditionRule,
          promotionGoodsSet: promotionGoodsSet,
          giftScope: giftScope,
          onResult: onResult);
    } else {
      handleMultipleGoods(
          goodsList: goodsList,
          goodsScope: goodsScope,
          giftScope: giftScope,
          promotion: promotion,
          conditionRule: conditionRule,
          promotionGoodsSet: promotionGoodsSet,
          onResult: onResult);
    }
  }

  ///处理按单个商品计数
  ///包括买赠同品和指定赠品
  ///[promotionGoodsSet] 先执行的指定赠品中，已经获取到了商品列表中在此促销中的商品，所以不需要再去挨个遍历判断。
  void handleSingleGoods({
    required List<GoodsDetailDto> goodsList,
    required int goodsScope,
    required BillPromotionInfoDto promotion,
    required RuleModel conditionRule,
    required Function(num giftCount, bool executed,
        LinkedHashSet<GoodsDetailDto> promotionGoodsSet)
    onResult,
    required PromotionGiftScope giftScope,
    LinkedHashSet<GoodsDetailDto>? promotionGoodsSet,
  }) {
    //保持商品顺序的促销商品列表
    List<GoodsDetailDto?>? sortedPromotionGoodsList;
    //先执行的指定赠品中，已经获取到了商品列表中在此促销中的商品，所以不需要再去挨个遍历判断。
    if (promotionGoodsSet != null) {
      goodsList = promotionGoodsSet.toList();
    } else {
      //记录促销中的商品
      sortedPromotionGoodsList = List.filled(goodsList.length, null);
    }
    //找出第一个条件，要满足促销至少有一个商品满足这个条件
    final firstCondition = conditionRule.conditionList.first;
    //赠品数量
    num giftCount = 0;
    //此促销是否满足条件被执行
    bool executed = false;
    //当前要执行促销的商品列表，对于批次商品，需要找到同商品不同批次的商品，对于一般商品，只有一条数据
    List<List<GoodsDetailDto>> promotionGoodsList = [];
    //参加过遍历的商品，包括内部循环过的商品，避免重复判断
    Set<GoodsDetailDto> loopedSet = {};
    //同商品不同批次的商品
    List<GoodsDetailDto> currentGoodsList = [];
    num handleGoods(GoodsDetailDto goods, int index, num sumCount) {
      if (promotionGoodsSet == null) {
        if (!isGoodsInPromotion(
          goods: goods,
          goodsScope: goodsScope,
          promotion: promotion,
        )) {
          return sumCount;
        }
        sortedPromotionGoodsList?[index] = goods;
      }
      currentGoodsList.add(goods);
      //如果之前遍历的商品都没有满足促销门槛，则累加数量，判断是否满足
      if (!executed) {
        num count = getGoodsCount(goods, promotion.vipRights);
        sumCount = MathUtil.addDec(sumCount, count).toDouble();
      }
      return sumCount;
    }

    //遍历商品列表，找到参加此促销的商品，判断促销是否被执行
    for (var i = 0; i < goodsList.length; i++) {
      //当前相同商品的数量/金额之和
      num sumCount = 0;
      var goods = goodsList[i];
      currentGoodsList = [];
      if (loopedSet.add(goods)) {
        sumCount = handleGoods(goods, i, sumCount);
        for (var j = 0; j < goodsList.length; j++) {
          var goods1 = goodsList[j];
          if (goods != goods1 &&
              GoodsTool.compareUnitSku(goods, goods1, comparePrice: false)) {
            if (loopedSet.add(goods1)) {
              sumCount = handleGoods(goods1, j, sumCount);
            }
          }
        }
      }

      if (currentGoodsList.isNotEmpty) {
        promotionGoodsList.add(currentGoodsList);
        //满足条件，执行促销
        if (!executed && sumCount >= firstCondition.conditionTotal) {
          executed = true;
        }
      }
    }

    if (promotionGoodsList.isNotEmpty) {
      //赠品剩余数量,开启了倍送之后，会对赠品数量进行限制
      giftCount = handleBySingleGoods(
          promotionGoodsList: promotionGoodsList,
          promotion: promotion,
          conditionRule: conditionRule,
          executed: executed,
          giftScope: giftScope,
          firstCondition: firstCondition);
    }
    //返回促销中的商品，供买赠同品使用
    if (promotionGoodsSet == null) {
      promotionGoodsSet = LinkedHashSet.identity();
      if (sortedPromotionGoodsList != null) {
        for (var i = 0; i < sortedPromotionGoodsList.length; i++) {
          var goods = sortedPromotionGoodsList[i];
          if (goods != null) {
            promotionGoodsSet.add(goods);
          }
        }
      }
    }
    onResult(giftCount, executed, promotionGoodsSet);
  }

  ///处理按多个商品计数
  ///只有指定赠品
  void handleMultipleGoods({
    required RuleModel conditionRule,
    required List<GoodsDetailDto> goodsList,
    required int goodsScope,
    required BillPromotionInfoDto promotion,
    required Function(num giftCount, bool executed,
        LinkedHashSet<GoodsDetailDto> promotionGoodsSet)
    onResult,
    required PromotionGiftScope giftScope,
    LinkedHashSet<GoodsDetailDto>? promotionGoodsSet,
  }) {
    bool isFindPromotionGoods = false;
    //先执行的指定赠品中，已经获取到了商品列表中在此促销中的商品，所以不需要再去挨个遍历判断。
    if (promotionGoodsSet != null) {
      goodsList = promotionGoodsSet.toList();
      isFindPromotionGoods = true;
    } else {
      promotionGoodsSet = LinkedHashSet.identity();
    }
    //找出第一个条件，要满足促销至少有一个商品满足这个条件
    final firstCondition = conditionRule.conditionList.first;
    //赠品数量
    num giftCount = 0;
    //此促销是否满足条件被执行
    bool executed = false;
    //参与条件判断的总数量/金额
    num sumCount = 0;
    //遍历商品列表，找到参加此促销的商品，判断促销是否被执行，若是按多个商品来计算，则统计总的数量/金额
    for (var goods in goodsList) {
      //这里如果在此促销其他赠送方式中已经获取到了促销内的商品，则无需再去判断
      if (!isFindPromotionGoods) {
        if (!isGoodsInPromotion(
          goods: goods,
          goodsScope: goodsScope,
          promotion: promotion,
        )) {
          continue;
        } else {
          promotionGoodsSet.add(goods);
        }
      }
      num count = getGoodsCount(goods, promotion.vipRights);
      sumCount = MathUtil.addDec(sumCount, count).toDouble();
      //如果之前遍历的商品都没有满足促销门槛，则累加数量，判断是否满足
      if (!executed) {
        if (sumCount >= firstCondition.conditionTotal) {
          executed = true;
        }
      }
    }
    if (promotionGoodsSet.isNotEmpty) {
      //促销提示
      PromotionHints? hints;
      giftCount = getGiftCountByMultipleGoods(
          sumCount: sumCount,
          conditionRule: conditionRule,
          firstCondition: firstCondition,
          generateHints: (unmetRemainCount, unmetGiftCount) {
            hints = generateHints(
              promotion: promotion,
              countRemain: unmetRemainCount,
              giftCount: unmetGiftCount,
              giftScope: giftScope,
            );
          });

      for (var goods in promotionGoodsSet) {
        //商品关联促销
        if (executed) {
          setGoodsPromotion(promotion, goods);
        }
        if (hints != null) {
          addGoodsHint(goods: goods, hint: hints!, executed: executed);
        }
        //没有促销提示，但是执行了促销，说明已经达到最高层级，或者达到最大赠品数量
        //需要移除此类型的所有提示
        else if (executed) {
          //在5.9中，满件赠/满额赠允许三种提示同时存在
          removeCurrentPromotionTypeHintFromHintList(goods.promotionHints,
              giftScope: giftScope.value, currentPromotionId: promotion.id);
        }
      }
    }

    onResult(giftCount, executed, promotionGoodsSet);
  }

  ///执行指定赠品
  void executeSpecifiedGoods({
    required List<GoodsDetailDto> goodsList,
    required BillPromotionInfoDto promotion,
    required int goodsScope,
    required RuleModel conditionRule,
    required Function(
        bool executed, LinkedHashSet<GoodsDetailDto>? promotionGoodsSet)
    onResult,
    LinkedHashSet<GoodsDetailDto>? promotionGoodsSet,
  }) {
    final int? giftType = conditionRule.type;
    if (giftType == null) return;
    //此促销是否满足条件被执行
    bool executed = false;
    //赠品数量
    num giftCount = 0;
    _handleGoods(
        goodsList: goodsList,
        goodsScope: goodsScope,
        promotion: promotion,
        conditionRule: conditionRule,
        giftScope: PromotionGiftScope.specifiedGoods,
        onResult: (count_, executed_, goodsSet_) {
          giftCount = count_;
          executed = executed_;
          promotionGoodsSet = goodsSet_;
        });
    //返回是否执行，和赠品中的商品列表
    onResult(executed, promotionGoodsSet);
    if (executed) {
      List<PromotionGift> existedGiftSet = getExistedGiftListWithGiftScope(
          promotionId: promotion.id,
          giftType: giftType,
          promotionGiftScope: PromotionGiftScope.specifiedGoods.value);
      List<GoodsDetailDto> goodsGiftList = [];
      for (var giftPtype in promotion.specifiedGiftPtypeList) {
        num giftQty = giftPtype.giftQty ?? 0;
        if (giftQty <= 0) continue;
        PromotionGift? gift = existedGiftSet.firstWhereOrNull((element) =>
            PromotionUtil.compareGift(element, giftPtype, giftType));
        bool isAdd = gift == null;
        num qty = SystemConfigTool.doubleMultipleToDecimal(
            giftQty, giftCount, BillDecimalType.QTY);
        if (giftType == PromotionGiftType.goods.value) {
          List<GoodsDetailDto> comboDetails = []; //如果之前没有这个赠品，则生成新的赠品
          if (isAdd) {
            gift = giftPtype.changeToGoodsDetailDto(
                promotion,
                promotion.promotionType,
                PromotionGiftScope.specifiedGoods.value);
          }
          GoodsDetailDto goodsGift = gift as GoodsDetailDto;
          goodsGift!.unitQty = qty;
          goodsGiftList.add(goodsGift);
          //处理套餐明细行，若没有该赠品的套餐明细，则生成明细
          if (isAdd) {
            comboDetails = PromotionUtil.generatePromotionComboDetailGift(
                giftPtype, goodsGift);
            comboDetailsMap[goodsGift.comboRowId] = comboDetails;
          } else {
            comboDetails = comboDetailsMap[goodsGift.comboRowId] ?? [];
          }
        } else if (giftType == PromotionGiftType.coupon.value) {
          if (isAdd) {
            gift = giftPtype.changeToCouponGift(
                promotion, PromotionGiftScope.specifiedGoods.value);
            billDto.giftCouponList.add(gift as CouponGift);
          }
          gift.promotionGiftQty = qty;
        }
        getExecuteGiftSet(giftType)?.add(gift!);
        if (goodsGiftList.isNotEmpty) {
          PromotionUtil.addMultipleGoodsGift(
              giftList: goodsGiftList,
              originalGoodsList: originalGoodsList,
              promotion: promotion,
              comboDetailsMap: comboDetailsMap);
        }
      }
    }
  }

  ///执行自选赠品
  void executeChooseGoods({
    required List<GoodsDetailDto> goodsList,
    required BillPromotionInfoDto promotion,
    required int goodsScope,
    required RuleModel conditionRule,
    required Function(
        bool executed, LinkedHashSet<GoodsDetailDto>? promotionGoodsSet)
    onResult,
    LinkedHashSet<GoodsDetailDto>? promotionGoodsSet,
  }) {
    final int? giftType = conditionRule.type;
    if (giftType == null) return;
    //此促销是否满足条件被执行
    bool executed = false;
    //赠品数量
    num giftCount = 0;
    _handleGoods(
        goodsList: goodsList,
        goodsScope: goodsScope,
        promotion: promotion,
        conditionRule: conditionRule,
        giftScope: PromotionGiftScope.chooseGoods,
        onResult: (count_, executed_, goodsSet_) {
          giftCount = count_;
          executed = executed_;
          promotionGoodsSet = goodsSet_;
        });
    //返回是否执行，和赠品中的商品列表
    onResult(executed, promotionGoodsSet);
    //选择赠品，需要处理赠品和赠品记录
    //找到之前的赠品记录
    PromotionGiftRecord? record = billDto.promotionGiftRecord
        ?.firstWhereOrNull((element) => element.promotionId == promotion.id);
    if (executed) {
      //这里找到已有赠品，对应的促销中的赠品类，减少在自动执行赠品时的遍历次数
      Map<PromotionPtype, List<PromotionGift>> giftMap = {};
      final List<PromotionGift> existedGiftList =
      getExistedGiftListWithGiftScope(
          promotionId: promotion.id,
          giftType: giftType,
          promotionGiftScope: PromotionGiftScope.chooseGoods.value);
      //遍历促销赠品列表，累加计算出赠品数量，如果超了，则调整赠品数量，移除多出来的赠品
      num sumQty = 0;
      for (var gift in existedGiftList) {
        //找到已有赠品，统计其数量
        if (gift.promotionGiftQty > 0) {
          //校验已有赠品是否还在此促销内
          final giftBean =
          PromotionUtil.findGiftListBeanByGift(gift, promotion, giftType);
          if (giftBean == null) continue;
          //记录要保留的赠品
          giftMap.putIfAbsent(giftBean, () => []).add(gift);
          num lastSum = sumQty;
          sumQty = MathUtil.addDec(sumQty, gift.promotionGiftQty).toDouble();
          //赠品数量超了
          if (sumQty >= giftCount) {
            //加上这个赠品，数量已经超过最大数量，则将次赠品数量直接调整为最大数量，和累加到上一个赠品是的数量的差值。
            if (sumQty > giftCount) {
              gift.promotionGiftQty =
                  MathUtil.subtractDec(giftCount, lastSum).toDouble();
            }
            break;
          }
        }
      }
      List<PromotionGift>? executedGift;
      //自动执行赠品
      if (autoChoosePromotionGift) {
        //本次赠品数量和之前记录的赠品数量一致，说明促销没有发生变化，无需执行自动选择赠品
        //已有赠品数量，超过最大赠品数量，则无需执行自动选择赠品
        if (giftCount != record?.giftCount && sumQty < giftCount) {
          executedGift = PromotionUtil.randomChooseGift(
              promotion: promotion,
              maxQty: MathUtil.subtractDec(giftCount, sumQty).toDouble(),
              giftType: giftType,
              comboDetailsMap: comboDetailsMap,
              giftMap: giftMap);
          //添加赠品
          if (giftType == PromotionGiftType.goods.value) {
            PromotionUtil.addMultipleGoodsGift(
                giftList: executedGift.cast<GoodsDetailDto>(),
                originalGoodsList: originalGoodsList,
                promotion: promotion,
                comboDetailsMap: comboDetailsMap);
          } else if (giftType == PromotionGiftType.coupon.value) {
            addMultipleCouponGift(
                billDto.giftCouponList,
                executedGift.cast<CouponGift>(),
                promotion.id,
                PromotionGiftScope.chooseGoods.value);
          }
        }
      }
      executedGift ??= giftMap.values.expand((element) => element).toList();
      //记录已执行的赠品
      Set<PromotionGift> set = getExecuteGiftSet(giftType) ?? {};
      for (var element in executedGift) {
        set.add(element);
      }
      billDto.promotionGiftRecord ??= {};
      if (record == null) {
        record = PromotionGiftRecord(
            promotionId: promotion.id,
            promotionType: promotion.promotionType,
            giftCount: giftCount,
            giftType: giftType);
        billDto.promotionGiftRecord!.add(record);
      } else {
        record
          ..giftCount = giftCount
          ..giftType = giftType;
      }
    }
  }

  ///执行买赠同品
  ///[promotionGoodsSet] 若先执行的指定赠品，则商品已经被过滤一次，直接传入，不需要再过滤
  void executeSameGoods({
    required List<GoodsDetailDto> goodsList,
    required BillPromotionInfoDto promotion,
    required int goodsScope,
    required RuleModel conditionRule,
    required LinkedHashSet<GoodsDetailDto>? promotionGoodsSet,
    required Function(
        bool executed, LinkedHashSet<GoodsDetailDto> promotionGoodsSet)
    onResult,
  }) {
    handleSingleGoods(
        goodsList: goodsList,
        goodsScope: goodsScope,
        promotion: promotion,
        conditionRule: conditionRule,
        promotionGoodsSet: promotionGoodsSet,
        giftScope: PromotionGiftScope.sameGoods,
        onResult: (count_, executed_, goodsSet_) {
          onResult(executed_, goodsSet_);
        });
  }

  ///添加多个赠品
  ///[couponGiftList] 原始优惠券赠品列表
  ///[goods] 要插入赠品的优惠券赠品
  ///[giftList] 赠品列表
  ///需要先移除再重新添加
  void addMultipleCouponGift(List<CouponGift> couponGiftList,
      List<CouponGift> giftList, String promotionId, int promotionGiftScope) {
    couponGiftList.removeWhere((element) =>
    element.promotionId == promotionId &&
        element.promotionGiftScope == promotionGiftScope);
    couponGiftList.addAll(giftList);
  }

  ///根据赠品类型找出之前已存在的赠品列表
  List<PromotionGift>? getExistedGiftList(int giftType) {
    List<PromotionGift>? existedGiftList;
    if (giftType == PromotionGiftType.goods.value) {
      existedGiftList = currentPromotionTypeGoodsGiftList;
    } else if (giftType == PromotionGiftType.coupon.value) {
      existedGiftList = currentPromotionTypeCouponGiftList;
    }
    return existedGiftList;
  }

  ///找到已存在的且属于当前赠品范围类型的赠品
  List<PromotionGift> getExistedGiftListWithGiftScope({
    required promotionId,
    required int giftType,
    required int promotionGiftScope,
  }) {
    return getExistedGiftList(giftType)
        ?.where((element) =>
    element.promotionGiftScope == promotionGiftScope &&
        element.promotionId == promotionId)
        .toList() ??
        [];
  }

  ///根据赠品类型找出要执行的赠品列表
  Set<PromotionGift>? getExecuteGiftSet(int giftType) {
    Set<PromotionGift>? executeGiftList;
    if (giftType == PromotionGiftType.goods.value) {
      executeGiftList = executedGoodsGiftSet;
    } else if (giftType == PromotionGiftType.coupon.value) {
      executeGiftList = executedCouponGiftList;
    }
    return executeGiftList;
  }

  ///根据单个商品计算来处理促销
  ///[promotionGoodsList] 参加促销的商品列表
  ///[promotion] 当前促销
  ///[conditionRule] 促销规则
  ///[executed] 是否执行促销
  ///[firstCondition] 促销阶梯条件列表中的第一个条件
  ///[giftScope] 促销赠品范围
  num handleBySingleGoods({
    required List<List<GoodsDetailDto>> promotionGoodsList,
    required BillPromotionInfoDto promotion,
    required RuleModel conditionRule,
    required bool executed,
    required ConditionListBean firstCondition,
    required PromotionGiftScope giftScope,
  }) {
    bool isSameGoodsGift = giftScope == PromotionGiftScope.sameGoods;
    //累计赠品数量，如果不是买赠同品，则需要累加每个商品能得到的赠品数量
    num giftTotalCount = 0;
    for (var goodsList in promotionGoodsList) {
      //当前商品的金额/数量
      num goodsCount = 0;
      //条件中的赠品数量
      num currentGiftCount = 0;
      //当前数量与未满足的下一个条件的数量的差值，即再买xx可送
      num? unmetRemainCount;
      //未满足的下一个条件的赠品数量
      num? unmetGiftCount;
      //遍历商品，计算数量
      for (var goods in goodsList) {
        //关联促销
        if (executed) {
          setGoodsPromotion(promotion, goods);
        }
        goodsCount = MathUtil.addDec(
            goodsCount, getGoodsCount(goods, promotion.vipRights))
            .toDouble();
      }

      //开启倍送
      if (conditionRule.doubleSend) {
        //赠品剩余数量,开启了倍送之后，会对赠品数量进行限制
        num? giftRemain = conditionRule.maxTotal;
        //没有赠品可以赠送
        if (giftRemain <= 0) {
          continue;
        }
        //计算得到满足条件时的倍率
        checkConditionDoubleSend(
          condition: firstCondition,
          count: goodsCount,
          giftMaxCount: giftRemain,
          result: (num giftCount, num unmetRemainCount_, num unmetGiftCount_) {
            currentGiftCount = giftCount;
            if (unmetRemainCount_ > 0 && unmetGiftCount_ > 0) {
              unmetRemainCount = unmetRemainCount_;
              unmetGiftCount = unmetGiftCount_;
            }
          },
        );
        //在开启倍送，且赠品数量达到最大的情况下，再增加购买数量也不会再有促销变化，所以需要移除此类型促销提示
        if (executed && (unmetGiftCount ?? 0) <= 0) {
          removeCurrentPromotionTypeHint(goodsList,
              giftScope: giftScope.value, currentPromotionId: promotion.id);
        }
      }
      //未开启倍送
      else {
        Map<String, ConditionListBean> map =
        checkConditionList(conditionRule.conditionList, goodsCount);
        //当前满足的条件
        ConditionListBean? metCondition =
        map[PromotionConditionMixin.metConditionKey];
        //未满足的条件，即满足条件的下一个条件
        ConditionListBean? unmetCondition =
        map[PromotionConditionMixin.unmetConditionKey];
        //满足条件，说明执行促销
        if (metCondition != null) {
          currentGiftCount = metCondition.preferential;
        }
        //有未满足的条件，添加促销提示
        if (unmetCondition != null) {
          unmetRemainCount =
              MathUtil.subtractDec(unmetCondition.conditionTotal, goodsCount)
                  .toDouble();
          unmetGiftCount = unmetCondition.preferential;
        }
        //所有条件都满足，说明已经达到最大层级，移除此类型的提示
        else {
          removeCurrentPromotionTypeHint(goodsList,
              giftScope: giftScope.value, currentPromotionId: promotion.id);
        }
      }
      //处理赠品
      if (currentGiftCount > 0) {
        //如果是买赠同品，则直接将赠品添加到商品后面
        if (isSameGoodsGift) {
          addSameGift(goodsList.last, promotion, currentGiftCount);
        } else {
          //非买赠同品，需要累加各商品的赠品数量，最后同一处理
          giftTotalCount =
              MathUtil.addDec(giftTotalCount, currentGiftCount).toDouble();
        }
      }
      //添加促销提示
      if (unmetRemainCount != null &&
          unmetRemainCount! > 0 &&
          unmetGiftCount != null &&
          unmetGiftCount! > 0) {
        addGoodsListHint(
            goodsList: goodsList,
            hint: generateHints(
              promotion: promotion,
              countRemain: unmetRemainCount!,
              giftCount: unmetGiftCount!,
              giftScope: giftScope,
            ),
            giftScope: giftScope.value,
            executed: executed);
      }
    }
    return giftTotalCount;
  }

  ///添加买赠同品赠品
  void addSameGift(
      GoodsDetailDto goods, BillPromotionInfoDto promotion, num giftCount) {
    GoodsDetailDto? gift = findSameGift(goods, promotion.id,
        promotionGiftScope: PromotionGiftScope.sameGoods.value);
    List<GoodsDetailDto> comboDetails = [];
    bool isAdd = gift == null;
    //如果之前没有这个赠品，则生成新的赠品
    if (isAdd) {
      gift = PromotionUtil.generateGiftFromGoods(goods,
          promotion: promotion,
          unitQty: giftCount,
          promotionGiftScope: PromotionGiftScope.sameGoods.value);
    }
    gift.unitQty = giftCount;
    if (goods.comboRow) {
      if (isAdd) {
        comboDetails = PromotionUtil.generateGiftComboDetailsFromGoods(
            gift, comboDetailsMap[goods.comboRowId] ?? [], promotion);
      } else {
        comboDetails = comboDetailsMap[gift.comboRowId] ?? [];
      }
    }
    PromotionUtil.setGiftPreferential(promotion, gift, comboDetails);
    //将赠品添加到已执行列表
    executedGoodsGiftSet.add(gift);
    //将赠品添加到促销商品后面
    insertGiftAfterGoods(
        isAdd: isAdd, giftList: [gift, ...comboDetails], goods: goods);
  }

  ///创建一个促销提示
  ///[promotion] 促销信息
  ///[countRemain] 再买xx
  ///[giftCount] 赠品数量
  ///[giftScope] 赠品范围,买赠同品或指定赠品
  PromotionHints? generateHints({
    required BillPromotionInfoDto promotion,
    required num countRemain,
    required num giftCount,
    required PromotionGiftScope giftScope,
  }) {
    if (giftCount <= 0 || countRemain <= 0) {
      return null;
    }
    String scope = "";
    String unit = "件";
    switch (giftScope) {
      case PromotionGiftScope.chooseGoods:
        scope = "自选赠品";
        break;
      case PromotionGiftScope.sameGoods:
        scope = "赠送同商品";
        break;
      case PromotionGiftScope.specifiedGoods:
        scope = "赠送指定赠品";
        unit = "组";
        break;
    }
    PromotionHints hints = PromotionHints(
      promotionId: promotion.id,
      promotionType: promotion.promotionType,
      typeName: "赠品",
      promotionGiftScope: giftScope.value,
      hints: "再买$countRemain$goodsCountUnitName,可$scope$giftCount$unit",
      //买赠同品不展示查看详情
      showDetail: giftScope != PromotionGiftScope.sameGoods,
    );
    return hints;
  }

  ///商品计数单位名称，例如元或件
  String get goodsCountUnitName;

  ///根据满件/满额赠，来获取商品的数量/金额
  num getGoodsCount(GoodsDetailDto goods, bool joinVipRights);
}

///处理满件赠/满额赠/订单满减中的促销阶梯条件的mixin，计算条件是否满足
mixin PromotionConditionMixin on PromotionHandler {
  ///对应[checkConditionList]的返回的map中，以满足条件的key
  static const metConditionKey = "metCondition";

  ///对应[checkConditionList]的返回的map中，未满足条件的key
  static const unmetConditionKey = "unmetCondition";

  ///根据递归，计算开启倍送时，当前商品数量/金额能满足的倍送倍数
  ///[condition] 基础倍率条件，第一层的条件
  ///[count] 当前商品数量/金额
  ///[giftMaxCount] 赠品最大数量
  ///即在n倍率下，赠品数量>=最大数量。
  ///[multiple] 当前倍数
  ///返回当前数量可以满足条件的最大倍率
  void checkConditionDoubleSend(
      {required ConditionListBean condition,
        required num count,
        required num giftMaxCount,
        required void Function(
            num giftCount, num unmetRemainCount, num unmetGiftCount)
        result}) {
    //条件中的商品数量/金额
    num conditionTotal = condition.conditionTotal;
    //条件中的赠品数量
    num preferential = condition.preferential;
    num giftCount = 0;
    num unmetRemainCount = 0;
    num unmetGiftCount = 0;
    int multiple = MathUtil.divideDec(count, conditionTotal).toDouble().toInt();
    if (multiple > 0) {
      //计算赠品数量，赠品数量不得大于最大数量
      giftCount = MathUtil.multiplyDec(preferential, multiple).toDouble();
      if (giftCount >= giftMaxCount) {
        giftCount = giftMaxCount;
        result(giftCount, unmetRemainCount, unmetGiftCount);
        return;
      }
    }
    //赠品数量未达到最大数量，则将倍数+1，计算此时的商品数量和赠品数量
    multiple++;
    //再买xx
    unmetRemainCount = (MathUtil.multiplyDec(conditionTotal, multiple) -
        Decimal.parse(count.toString()))
        .toDouble();
    //赠送xx件
    unmetGiftCount = min(
        giftMaxCount, MathUtil.multiplyDec(preferential, multiple).toDouble());
    result(giftCount, unmetRemainCount, unmetGiftCount);
  }

  ///检查条件列表，返回满足条件的条件，以及不满足条件的条件（用于添加促销提示）
  ///[conditionList] 条件列表
  ///[count] 当前商品数量/金额
  ///返回一个Map,包含[metConditionKey]已满足的条件，[unmetConditionKey]不满足条件的条件
  Map<String, ConditionListBean> checkConditionList(
      List<ConditionListBean> conditionList, num count) {
    //当前满足的条件
    ConditionListBean? metCondition;
    //未满足的条件，即满足条件的下一个条件
    ConditionListBean? unmetCondition;
    for (var condition in conditionList) {
      if (count >= condition.conditionTotal) {
        metCondition = condition;
      } else {
        unmetCondition = condition;
        break;
      }
    }
    Map<String, ConditionListBean> result = {};
    if (metCondition != null) {
      result[metConditionKey] = metCondition;
    }
    if (unmetCondition != null) {
      result[unmetConditionKey] = unmetCondition;
    }
    return result;
  }

  ///根据多个商品计算来处理促销，获取赠品数量，赠品可以是商品/优惠券/金额（订单满减）
  ///[promotionGoodsList] 参加促销的商品列表
  ///[promotion] 当前促销
  ///[conditionRule] 促销规则
  ///[firstCondition] 促销阶梯条件列表中的第一个条件
  num getGiftCountByMultipleGoods(
      {required num sumCount,
        required RuleModel conditionRule,
        required ConditionListBean firstCondition,
        required void Function(num countRemain, num giftCount) generateHints}) {
    //条件中的赠品数量
    num? currentGiftCount;
    //当前数量与未满足的下一个条件的数量的差值，即再买xx可送
    num? unmetRemainCount;
    //未满足的下一个条件的赠品数量
    num? unmetGiftCount;
    //开启了倍送
    if (conditionRule.doubleSend) {
      //赠品剩余数量,开启了倍送之后，会对赠品数量进行限制
      final num maxCount = conditionRule.maxTotal;
      //计算得到满足条件时的倍率
      checkConditionDoubleSend(
        condition: firstCondition,
        count: sumCount,
        giftMaxCount: maxCount,
        result: (num giftCount, num unmetRemainCount_, num unmetGiftCount_) {
          currentGiftCount = giftCount;
          if (unmetRemainCount_ > 0 && unmetGiftCount_ > 0) {
            unmetRemainCount = unmetRemainCount_;
            unmetGiftCount = unmetGiftCount_;
          }
        },
      );
    }
    //未开启倍送
    else {
      Map<String, ConditionListBean> map =
      checkConditionList(conditionRule.conditionList, sumCount);
      //当前满足的条件
      ConditionListBean? metCondition =
      map[PromotionConditionMixin.metConditionKey];
      //未满足的条件，即满足条件的下一个条件
      ConditionListBean? unmetCondition =
      map[PromotionConditionMixin.unmetConditionKey];
      //满足条件，说明执行促销
      if (metCondition != null) {
        currentGiftCount = metCondition.preferential;
      }
      //有未满足的条件，添加促销提示
      if (unmetCondition != null) {
        unmetRemainCount =
            MathUtil.subtractDec(unmetCondition.conditionTotal, sumCount)
                .toDouble();
        unmetGiftCount = unmetCondition.preferential;
      }
    }
    if (unmetRemainCount != null && unmetGiftCount != null) {
      generateHints(unmetRemainCount!, unmetGiftCount!);
    }
    return currentGiftCount ?? 0;
  }
}

///处理需要将原商品拆分成两个的促销(第二件半价，组合购)
mixin PromotionGoodsSplitMixin on PromotionHandler {
  //处理序列号，当拆分的原商品有序列号，且序列号数量和商品数量一致时，拆分之后需要将最后一条序列号添加到赠品中
  void handlerSerialNo(GoodsDetailDto goods, GoodsDetailDto gift) {
    if (goods.snenabled == 1) {
      gift.serialNoList.clear();
      if (goods.serialNoList.length > goods.unitQty) {
        gift.serialNoList.add(goods.serialNoList.removeLast());
      }
    }
  }

  ///拆分商品，n元组合购、第二件半价拆分
  void splitGoods({
    required GoodsDetailDto goods,
    required BillPromotionInfoDto promotion,
    required num splitQty,
    required void Function(
        GoodsDetailDto gift, List<GoodsDetailDto> giftComboDetails)
    onGenerateGift,
  }) {
    GoodsDetailDto gift;
    List<GoodsDetailDto> giftComboDetails = [];
    List<GoodsDetailDto> comboDetails =
    GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap);
    if (splitQty >= goods.unitQty) {
      gift = goods;
      giftComboDetails = comboDetails;
    } else {
      //数量减少
      goods.unitQty = MathUtil.subtractDec(goods.unitQty, splitQty).toDouble();
      //折扣还原为1
      // DiscountUtil.resetDiscountTo1(goods,
      //     comboDetails: comboDetails, cleanPreferentialHelper: false);
      DiscountUtil.resetDiscountTo1(goods, comboDetails: comboDetails);

      //生成赠品
      gift = PromotionUtil.generateGiftFromGoods(goods,
          promotion: promotion,
          isGift: false,
          cleanBatchInfo: false,
          unitQty: splitQty);

      handlerSerialNo(goods, gift);

      //处理套餐明细行
      if (goods.comboRow) {
        //重算套餐下的套餐明细行，数量发生了改变
        ComboPreferentialTool.handleComboDetail(goods, comboDetails,
            calculateQty: true,
            totalGetter: (g) => g.currencyTotal,
            totalSetter: (g, total) =>
                GoodsTotalUtil.onCurrencyTotalChange(g, total));
        //如果是套餐，还需要生成套餐明细行
        giftComboDetails = PromotionUtil.generateGiftComboDetailsFromGoods(
            gift, comboDetails, promotion,
            cleanBatchInfo: false, onGenerateGift: handlerSerialNo);
      }

      // //商品执行打折流程
      // PreferentialDto? rightsCardPreferential; //权益卡打折
      // PreferentialDto? couponPreferential; //优惠券打折
      // if (isVip) {
      //   final discountInfoMap =
      //       DiscountHandler.getDiscountFromPreferentialHelper(
      //           goods: goods, comboDetails: comboDetails);
      //   rightsCardPreferential =
      //       discountInfoMap[Preferential.discountRightsCard.name];
      //   couponPreferential = discountInfoMap[Preferential.discountCoupon.name];
      // }
      // //重新执行打折
      // DiscountHandler.handleGoodsDiscountByPreferentialInfo(
      //   goods: goods,
      //   isVip: isVip,
      //   comboDetails: comboDetails,
      //   executeRightsCard: promotion.vipRights,
      //   getDiscountFromPreferentialHelper: false,
      //   rightsCardPreferential: rightsCardPreferential,
      //   couponPreferential: couponPreferential,
      // );
      // DiscountHandler.handleGoodsDiscountByPreferentialInfo(
      //   goods: gift,
      //   isVip: isVip && handleGiftDiscount,
      //   comboDetails: giftComboDetails,
      //   executeRightsCard: promotion.vipRights,
      //   getDiscountFromPreferentialHelper: false,
      //   rightsCardPreferential: rightsCardPreferential,
      //   couponPreferential: couponPreferential,
      // );
    }
    onGenerateGift(gift, giftComboDetails);
  }
}

//endregion 促销基类
