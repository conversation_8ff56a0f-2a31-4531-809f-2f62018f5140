import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/preferential_dto.dart';
import '../../entity/ss_card_dto.dart';
import '../../widget/sale/coupon_gift_select_page.dart';
import '../bill_goods_util.dart';
import '../goods_scope_util.dart';
import 'discount.dart';
import 'preferential.dart';

typedef CardCallback = void Function(SsCardDto? card);

//region 枚举

//region 权益价值类型

///权益价值类型
enum EquityValueType {
  //包邮
  mail,
  //折扣
  discount,
  //抵扣
  deduction,
  //优先发货
  priorityGoods,
  //积分倍率
  scoreMultiple,
  //礼品兑换
  giftConvert,
  //自定义
  DIY,
}

//endregion 权益价值类型

//region 卡券类型
///卡券类型 0：等级权益卡，1：普通权益卡，2：代金券 3 折扣券 4礼品券
enum CardType {
  ///等级权益卡
  levelRightsCard,

  ///普通权益卡
  rightsCard,

  ///代金券
  amountCoupon,

  ///折扣券
  discountCoupon,

  ///礼品券
  giftCoupon,
}

extension CardTypeExtension on CardType {
  int get value {
    switch (this) {
      //等级权益卡
      case CardType.levelRightsCard:
        return 0;
      //普通权益卡
      case CardType.rightsCard:
        return 1;
      //代金券
      case CardType.amountCoupon:
        return 2;
      //折扣券
      case CardType.discountCoupon:
        return 3;
      //礼品券
      case CardType.giftCoupon:
        return 4;
    }
  }
}

//endregion 卡券类型

//region 卡券商品范围
///卡券商品范围
enum CardGoodsScope {
  ///商品范围-全部商品
  allGoods,

  ///商品范围-部分商品
  partGoods,

  ///商品范围-排除商品
  excludeGoods,

  ///商品范围-商品分类
  goodsCategory,

  ///商品范围-商品标签
  goodsLabel,
}

extension CardGoodsScopeExtension on CardGoodsScope {
  int get value {
    switch (this) {
      //商品范围-全部商品
      case CardGoodsScope.allGoods:
        return 0;
      //商品范围-部分商品
      case CardGoodsScope.partGoods:
        return 1;
      //商品范围-排除商品
      case CardGoodsScope.excludeGoods:
        return 3;
      //商品范围-商品分类
      case CardGoodsScope.goodsCategory:
        return 4;
      //商品范围-商品标签
      case CardGoodsScope.goodsLabel:
        return 5;
    }
  }
}

//endregion 卡券商品范围

//endregion 枚举

const String discountZero = "礼品券";
const String creditsExchange = "积分兑换";

//region 优惠券处理
///折扣券处理
class DiscountCouponHandler {
  DiscountCouponHandler._();

  static bool handle({
    required GoodsBillDto bill,
    required SsCardDto? discountCoupon,
    required bool isVip,
  }) {
    bool executed = false;
    //折扣券权益价值
    MemberEquityValuesBean? couponEquityValue;
    num discount = 1;
    String? typeId;
    //折扣券最低消费门槛
    num couponMiniCost = 0;
    if (isVip && discountCoupon != null) {
      if (discountCoupon.cardType == CardType.discountCoupon.value) {
        if (discountCoupon.cardDetails.isNotEmpty) {
          CardDetailsBean cardDetail = discountCoupon.cardDetails.first;
          typeId = cardDetail.id;
        }
        couponEquityValue = CardUtil.getDiscountEquity(
          discountCoupon.memberEquityValues,
        );
        if (couponEquityValue?.detailList.isNotEmpty == true) {
          final valueDetail = couponEquityValue!.detailList.first;
          discount = valueDetail.valueDetail ?? 1;
          //有最低消费门槛
          if (valueDetail.miniCostType == 0) {
            couponMiniCost = valueDetail.valueCondition;
          }
        }
      }
      bool vaild =
          couponEquityValue != null &&
          discount >= 0 &&
          discount < 1 &&
          typeId != null;
      if (vaild) {
        //计算是否达到门槛
        if (couponMiniCost > 0) {
          //参加打折的商品总金额，用于判断是否达到门槛
          num couponSumMoney = 0;
          for (var goods in bill.outDetail) {
            //排除赠品和手工改价
            if (goods.gift || goods.manualPrice) {
              continue;
            }
            //排除套餐明细行
            if (StringUtil.isNotZeroOrEmpty(goods.comboRowParId)) {
              continue;
            }
            //促销赠品不执行打折
            if (BillGoodsUtil.isPromotionGift(goods)) {
              continue;
            }
            //判断商品是否在折扣券中
            if (CardUtil.isGoodsInMemberEquityValue(
              goods,
              equityValue: couponEquityValue,
            )) {
              couponSumMoney =
                  MathUtil.addDec(
                    couponSumMoney,
                    goods.discountTotal,
                  ).toDouble();
              //达到门槛
              if (couponSumMoney >= couponMiniCost) {
                executed = true;
                break;
              }
            }
          }
        } else {
          executed = true;
        }
      }
    }
    DiscountHandler.handle(
      isVip: isVip,
      bill: bill,
      preferential: Preferential.discountCoupon,
      typeId: typeId,
      memberEquityValues: [if (executed) couponEquityValue!],
    );
    return executed;
  }
}

///抵扣券处理
class AmountCouponHandler with BillPreferentialMixin {
  @override
  Preferential get preferential => Preferential.moneyCoupon;

  ///返回抵扣券是否执行
  bool handle({
    required GoodsBillDto bill,
    required SsCardDto? reductionCoupon,
    required bool isVip,
  }) {
    //清除单据中记录的优惠辅助
    bill.preferentialHelp.remove(preferential.name);

    //门槛，为0则是无门槛
    num? condition;
    //抵扣金额
    num? reduction;
    String? typeId;
    if (isVip && reductionCoupon != null) {
      if (reductionCoupon.memberEquityValues.isNotEmpty) {
        MemberEquityValuesBean equityValues =
            reductionCoupon.memberEquityValues.first;
        if (equityValues.detailList.isNotEmpty) {
          DetailListBean valuesDetail = equityValues.detailList.first;
          condition = valuesDetail.valueCondition;
          reduction = valuesDetail.valueDetail;
        }
      }
      if (reductionCoupon.cardDetails.isNotEmpty) {
        CardDetailsBean cardDetail = reductionCoupon.cardDetails.first;
        typeId = cardDetail.id;
      }
    }
    //是否有效
    bool valid =
        condition != null &&
        typeId != null &&
        reduction != null &&
        reduction > 0;
    //是否执行抵扣
    bool executed = false;
    //套餐明细
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    //要执行分摊的商品列表
    List<GoodsDetailDto> goodsList = [];
    //商品合计金额
    num goodsSumTotal = filterGoodsListAndCalculateTotal(
      bill.outDetail,
      goodsList,
      comboDetailsMap,
    );
    //合计金额为0，无需执行
    if (valid && goodsSumTotal > 0 && goodsSumTotal >= condition) {
      executed = true;
    }
    if (executed) {
      if (reduction! > goodsSumTotal) {
        reduction = goodsSumTotal;
      }
      //执行优惠分摊
      sharePreferentialToGoods(
        bill: bill,
        goodsList: goodsList,
        billPreferential: reduction,
        goodsListTotal: goodsSumTotal,
        comboDetailsMap: comboDetailsMap,
        preferentialTypeId: typeId,
      );
      return true;
    }
    return false;
  }
}

//endregion 优惠券处理

///卡券工具类
class CardUtil {
  CardUtil._();

  ///当卡券被选中
  void onCardSelected({
    required BuildContext context,
    required GoodsBillDto bill,
    required List<SsCardDto> existCardList,
    required List<SsCardDto> cardList,
    required bool isVip,
  }) {}

  ///根据卡券类型获取卡券
  static SsCardDto? getCardFromCardType(
    List<SsCardDto>? cardList,
    int cardType,
  ) {
    return cardList?.firstWhereOrNull(
      (element) => element.cardType == cardType,
    );
  }

  ///根据权益价值类型获取权益价值
  static MemberEquityValuesBean? getEquityValueByType(
    List<MemberEquityValuesBean>? memberEquityValues,
    EquityValueType valueType,
  ) {
    return memberEquityValues?.firstWhereOrNull(
      (element) => element.valueType == valueType.name,
    );
  }

  ///获取赠品数量
  static num getGiftCount(List<MemberEquityValuesBean>? memberEquityValues) {
    final detailList = memberEquityValues?.first.detailList;
    // getEquityValueByType(memberEquityValues, EquityValueType.giftConvert) // todo 这里提货券的权益价值类型为discount?
    // getEquityValueByType(memberEquityValues, EquityValueType.discount)
    //     ?.detailList;
    if (detailList == null || detailList.isEmpty) return 0;
    return detailList.first.valueDetail ?? 0;
  }

  ///找出卡券中的打折权益价值
  static MemberEquityValuesBean? getDiscountEquity(
    List<MemberEquityValuesBean> memberEquityValues,
  ) {
    return memberEquityValues.cast<MemberEquityValuesBean?>().firstWhere(
      (values) => getDiscountEquityValue(values!) < 1,
      orElse: () => null,
    );
  }

  ///找出打折权益价值中的折扣值
  ///非打折权益，默认返回1
  static num getDiscountEquityValue(MemberEquityValuesBean equityValue) {
    if (equityValue.valueType != EquityValueType.discount.name ||
        equityValue.detailList.isEmpty) {
      return 1;
    }
    return equityValue.detailList.first.valueDetail ?? 1;
  }

  ///判断商品是否在权益价值中
  static bool isGoodsInMemberEquityValue(
    GoodsDetailDto goods, {
    MemberEquityValuesBean? equityValue,
    DetailListBean? valueDetail,
  }) {
    if (valueDetail == null) {
      if (equityValue == null) return false;
      if (equityValue.detailList.isEmpty) return false;
      valueDetail = equityValue.detailList.first;
    }
    int? goodsScope = valueDetail.ptypeRang;
    //全部商品
    if (goodsScope == CardGoodsScope.allGoods.value) {
      return true;
    }
    //部分商品和排除部分商品
    else if (goodsScope == CardGoodsScope.partGoods.value ||
        goodsScope == CardGoodsScope.excludeGoods.value) {
      //匹配到的规则商品
      PtypeListBean? ptype = valueDetail.ptypeList
          .cast<PtypeListBean?>()
          .firstWhere(
            (element) =>
                element?.ptypeId == goods.ptypeId &&
                element?.skuId == goods.skuId &&
                element?.unitId == goods.unitId,
            orElse: () => null,
          );
      bool contains = ptype != null;
      //部分商品
      if (goodsScope == CardGoodsScope.partGoods.value) {
        return contains;
      }
      //排除部分商品
      else if (goodsScope == CardGoodsScope.excludeGoods.value) {
        return !contains;
      }
    }
    //商品分类
    else if (goodsScope == CardGoodsScope.goodsCategory.value) {
      return _isGoodsInEquityValueByCategory(goods, valueDetail);
    }
    //商品标签
    else if (goodsScope == CardGoodsScope.goodsLabel.value) {
      return _isGoodsInEquityValueByLabel(goods, valueDetail);
    }
    return false;
  }

  ///判断商品是否在权益价值的商品分类范围内
  static bool _isGoodsInEquityValueByCategory(
    GoodsDetailDto goods,
    DetailListBean valueDetail,
  ) {
    if (valueDetail.ptypeList.isEmpty) {
      return false;
    }

    // 获取配置的分类ID列表
    // 当商品范围为分类时，优先使用ptypeTypeId字段，如果为空则使用ptypeId字段
    List<String> categoryIds =
        valueDetail.ptypeList
            .map((element) {
              String? typeId = element.ptypeTypeId;
              if (typeId != null && typeId.isNotEmpty) {
                return typeId;
              }
              return element.ptypeId;
            }) // 优先使用ptypeTypeId作为分类ID
            .where((id) => id.isNotEmpty)
            .toList();

    if (categoryIds.isEmpty) {
      return false;
    }

    // 使用工具类判断商品是否在分类范围内
    return GoodsScopeUtil.isGoodsInCategoryList(goods, categoryIds);
  }

  ///判断商品是否在权益价值的商品标签范围内
  static bool _isGoodsInEquityValueByLabel(
    GoodsDetailDto goods,
    DetailListBean valueDetail,
  ) {
    if (valueDetail.ptypeList.isEmpty) {
      return false;
    }

    // 获取配置的标签ID列表
    // 当商品范围为标签时，ptypeId字段存储的是标签ID
    List<String> labelIds =
        valueDetail.ptypeList
            .map((element) => element.ptypeId ?? "") // ptypeId作为标签ID
            .where((id) => id.isNotEmpty)
            .toList();

    if (labelIds.isEmpty) {
      return false;
    }

    // 使用工具类判断商品是否包含标签
    return GoodsScopeUtil.isGoodsHasAnyLabel(goods, labelIds);
  }
}

///优惠券工具类
class CouponUtil {
  CouponUtil._();

  ///获取单据中已有提货券赠品数量
  static Decimal getExistGiftCount(GoodsBillDto bill) {
    return bill.outDetail
        .where((element) => BillGoodsUtil.isCouponGift(element))
        .fold(
          Decimal.zero,
          (previousValue, element) =>
              previousValue + Decimal.parse(element.unitQty.toString()),
        );
  }

  ///清除单据中所有提货券赠品
  static void cleanAllGiftCouponGift(GoodsBillDto bill) {
    bill.outDetail.removeWhere(
      (goods) =>
          goods.gift &&
          !goods.promotionGift &&
          goods.preferentialHelp.containsKey(Preferential.giftCoupon.name),
    );
    bill.preferentialHelp.remove(Preferential.giftCoupon.name);
  }

  ///提货券执行
  static Future<List<GoodsDetailDto>?> chooseGiftByGiftCoupon(
    BuildContext context,
    GoodsBillDto bill,
    SsCardDto? giftCoupon, {
    SsCardDto? oldGiftCoupon,
    bool chooseGift = true,
  }) async {
    //之前选择的礼品券，和现在选的礼品券，做比对，若发生改变，则删除之前的礼品券对应的商品
    if (oldGiftCoupon != null && oldGiftCoupon.id != giftCoupon?.id) {
      CouponUtil.cleanAllGiftCouponGift(bill);
    }
    if (!chooseGift) {
      return null;
    }
    //若新的礼品券不为空，则弹窗选赠品
    if (giftCoupon == null) return null;
    //最大赠品数量
    num maxQty = CardUtil.getGiftCount(giftCoupon.memberEquityValues);
    if (maxQty <= 0) return null;
    num qty = maxQty;
    //若提货券不变，则减去之前的赠品数量
    if (oldGiftCoupon?.id == giftCoupon.id) {
      qty =
          (Decimal.parse(maxQty.toString()) - getExistGiftCount(bill))
              .toDouble();
    }
    if (qty == 0) return null;
    //若赠品数量小于0，则清空所有赠品，重新选赠品
    if (qty < 0) {
      CouponUtil.cleanAllGiftCouponGift(bill);
      qty = maxQty;
    }
    List<GoodsDetailDto>? giftList = await showDialog<List<GoodsDetailDto>>(
      context: context,
      builder:
          (_) => CouponGiftSelectPage(couponEntity: giftCoupon, maxQty: qty),
    );
    return giftList;
  }

  ///获取单据中使用的优惠券的id
  static List<String> getUsedCouponIds(
    GoodsBillDto bill,
    List<SsCardDto> selectedCardList,
  ) {
    return selectedCardList
        .where((coupon) {
          if (coupon.cardDetails.isEmpty) return false;
          Preferential preferential;
          if (coupon.cardType == CardType.discountCoupon.value) {
            preferential = Preferential.discountCoupon;
          } else if (coupon.cardType == CardType.amountCoupon.value) {
            preferential = Preferential.moneyCoupon;
          } else if (coupon.cardType == CardType.giftCoupon.value) {
            preferential = Preferential.giftCoupon;
          } else {
            return false;
          }
          final preferentialDto = bill.preferentialHelp[preferential.name];
          if (preferentialDto == null ||
              !coupon.cardDetails.any(
                (element) => element.id == preferentialDto.typeId,
              )) {
            return false;
          }
          if (preferential != Preferential.giftCoupon &&
              preferentialDto.total <= 0) {
            return false;
          }
          return true;
        })
        .map((e) => e.id)
        .cast<String>()
        .toList();
  }
}
