import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../vip/utils/vip_util.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/preferential_dto.dart';
import '../../entity/ss_card_dto.dart';
import '../bill_goods_util.dart';
import '../goods_tool.dart';
import 'card.dart';
import 'preferential.dart';
import 'price.dart';
import 'promotion.dart';

///对商品执行打折优惠
///分为两种：
///1.执行会员价，再执行权益卡折扣，优惠券折扣
///2.手工改价，手工改价后不会再执行其他折扣，也不会执行后续商品级别的促销。
class DiscountUtil {
  ///开始打折
  static void startDiscount({
    required GoodsBillDto bill,
    required VipWithLevelAssertsRightsCardDTO? vipInfo,
    required SsCardDto? discountCoupon,
    required List<SsCardDto>? rightsCards,
    bool? isVip,
    CardCallback? onCouponNotUse,
  }) {
    //会员是否有效
    isVip ??= isValidVip(vipInfo);
    //处理会员价
    VipPriceHandler.handleVipPrice(bill, isVip);
    //处理权益卡
    RightsCardHandler.handle(
        bill: bill, rightsCards: rightsCards, isVip: isVip);
    //处理折扣券
    bool isCouponExecuted = DiscountCouponHandler.handle(
        bill: bill, discountCoupon: discountCoupon, isVip: isVip);
    //如果未执行折扣券，则移除外部缓存的折扣券
    if (!isCouponExecuted) {
      onCouponNotUse?.call(discountCoupon);
    }
  }

  ///根据套餐处理套餐明细行的会员价优惠
  ///对于套餐中非最后一个明细行，用比例分别计算套餐明细行金额，
  ///对于套餐中最后一个明细行，为避免精度问题，需要用套餐金额-之前套餐明细行的金额之和
  static void _handleComboDetail(
      GoodsDetailDto combo, List<GoodsDetailDto> comboDetails,
      {Preferential? preferential, String? typeId, num? discount}) {
    ComboPreferentialTool.handleComboDetail(combo, comboDetails,
        preferential: preferential,
        typeId: typeId,
        discount: discount,
        totalGetter: (goods) => goods.discountTotal,
        totalSetter: GoodsTotalUtil.onDiscountTotalChange);
  }

  ///重置折扣为1
  static void resetDiscountTo1(
    GoodsDetailDto goods, {
    List<GoodsDetailDto> comboDetails = const [],
    bool cleanPreferentialHelper = true,
  }) {
    if (cleanPreferentialHelper) {
      goods.preferentialHelp.clear();
      for (var element in comboDetails) {
        element.preferentialHelp.clear();
      }
    }
    goods.discount = 1;
    GoodsQtyUtil.onQtyChange(goods, goods.unitQty);
    if (comboDetails.isNotEmpty) {
      ComboPreferentialTool.handleComboDetail(goods, comboDetails,
          totalGetter: (g) => g.currencyTotal,
          totalSetter: (g, total) =>
              GoodsTotalUtil.onCurrencyTotalChange(g, total));
    }
  }
}

///会员价优惠处理
class VipPriceHandler {
  VipPriceHandler._();

  ///处理商品的会员价优惠
  static void handleVipPrice(GoodsBillDto bill, bool isVip,
      {List<GoodsDetailDto>? goodsList}) {
    bill.preferentialHelp.remove(Preferential.vipPrice.name);
    goodsList ??= bill.outDetail;
    if (goodsList.isEmpty) return;
    //套餐行
    Map<String, GoodsDetailDto> comboMap = {};
    //套餐明细
    Map<String, List<GoodsDetailDto>> comboDetailMap = {};
    for (var goods in goodsList) {
      goods.preferentialHelp.remove(Preferential.vipPrice.name);
      //排除赠品和手工改价
      if (goods.gift || goods.manualPrice) {
        continue;
      }
      //促销赠品不执行打折
      if (BillGoodsUtil.isPromotionGift(goods)) {
        continue;
      }
      //排除套餐明细行
      if (!GoodsTool.isComboDetail(goods)) {
        //重新计算商品金额
        GoodsQtyUtil.onQtyChange(goods, goods.unitQty);
        //记录套餐行
        if (GoodsTool.isComboRow(goods)) {
          comboMap[goods.comboRowId] = goods;
        }
        handleSingleGoods(goods, isVip);
      } else {
        //将折扣还原成1，给折后价格赋值,处理套餐明细时需要
        GoodsPriceUtil.onDiscountChange(goods, 1);
        //记录套餐明细行
        List<GoodsDetailDto> details =
            comboDetailMap.putIfAbsent(goods.comboRowParId, () => []);
        details.add(goods);
      }
    }
    //处理套餐明细行价格，用比例分别计算套餐明细行价格，对于套餐中最后一个明细行，为避免精度问题，需要用套餐价格-之前套餐明细行的价格之和
    for (var entry in comboDetailMap.entries) {
      final combo = comboMap[entry.key];
      if (combo != null) {
        final details = entry.value;
        handleComboDetails(combo, details);
      }
    }
  }

  ///处理套餐行明细会员价
  static void handleComboDetails(
      GoodsDetailDto combo, List<GoodsDetailDto> details) {
    //重新计算套餐明细的金额
    ComboPreferentialTool.handleComboDetail(combo, details,
        totalGetter: (g) => g.currencyTotal,
        totalSetter: (g, total) =>
            GoodsTotalUtil.onCurrencyTotalChange(g, total));
    DiscountUtil._handleComboDetail(combo, details,
        preferential: combo.discountPrice < combo.currencyPrice
            ? Preferential.vipPrice
            : null);
  }

  ///执行单个商品的会员价
  static void handleSingleGoods(GoodsDetailDto goods, bool isVip) {
    num price = getVipPrice(goods, isVip: isVip);
    //重新计算价格
    GoodsPriceUtil.onDiscountPriceChange(goods, price);
    //价格比零售价低，则说明执行了会员价优惠
    if (price < goods.currencyPrice) {
      //套餐不记录优惠辅助
      if (!goods.comboRow) {
        setVipPricePreferential(goods);
      }
    }
  }

  ///获取单个商品会员价优惠金额
  static num getVipPricePreferentialTotal(GoodsDetailDto goods) {
    return SystemConfigTool.doubleSubtractionToDecimal(
        goods.currencyTotal, goods.discountTotal, BillDecimalType.TOTAL);
  }

  ///添加会员价优惠辅助
  static PreferentialDto setVipPricePreferential(GoodsDetailDto goods) {
    num preferentialTotal = getVipPricePreferentialTotal(goods);
    return PreferentialHelper.setGoodsPreferential(
        goods, Preferential.vipPrice, preferentialTotal);
  }
}

///处理折扣优惠
///包括:
///1.会员权益折扣，权益卡折扣
///2.折扣券
class DiscountHandler {
  DiscountHandler._();

  ///执行打折优惠
  static void handle(
      {required bool isVip,
      required GoodsBillDto bill,
      required Preferential preferential,
      String? typeId,
      required List<MemberEquityValuesBean> memberEquityValues}) {
    bill.preferentialHelp.remove(preferential.name);
    //按照折扣，从小到大排序
    memberEquityValues.sort((a, b) => CardUtil.getDiscountEquityValue(a)
        .compareTo(CardUtil.getDiscountEquityValue(b)));
    //套餐行,key为comboRowId
    Map<String, GoodsDetailDto> comboMap = {};
    Map<String, num> comboDiscountMap = {};
    Map<String, String?> comboTypeIdMap = {};
    //套餐明细
    Map<String, List<GoodsDetailDto>> comboDetailMap = {};
    //遍历商品列表，依次处理折扣
    for (var goods in bill.outDetail) {
      goods.preferentialHelp.remove(preferential.name);
      //非有效会员，不执行打折
      if (!isVip) {
        continue;
      }
      if (memberEquityValues.isEmpty) {
        continue;
      }
      //排除手工改价和赠品
      if (goods.gift || goods.manualPrice) {
        continue;
      }
      //促销赠品不执行打折
      if (BillGoodsUtil.isPromotionGift(goods)) {
        continue;
      }
      //记录套餐明细行，等全部商品处理完之后，再处理套餐明细行
      if (GoodsTool.isComboDetail(goods)) {
        //记录套餐明细行
        List<GoodsDetailDto> details =
            comboDetailMap.putIfAbsent(goods.comboRowParId, () => []);
        details.add(goods);
        continue;
      }
      //遍历打折权益价值，执行打折
      for (var value in memberEquityValues) {
        final valueDetail = value.detailList.first;
        //找出折扣，折扣为1 不执行
        num discount = valueDetail.valueDetail ?? 1;
        if (discount == 1) {
          continue;
        }
        //商品在权益价值中，执行打折
        if (CardUtil.isGoodsInMemberEquityValue(goods,
            valueDetail: valueDetail)) {
          String? typeId1 = typeId ?? value.id;
          if (goods.comboRow) {
            //记录套餐行
            comboMap[goods.comboRowId] = goods;
            comboDiscountMap[goods.comboRowId] = discount;
            comboTypeIdMap[goods.comboRowId] = typeId1;
          }
          handleSingleGoods(goods, preferential, discount, typeId1);
          break;
        }
      }
    }
    //处理套餐明细行价格，用比例分别计算套餐明细行价格，对于套餐中最后一个明细行，为避免精度问题，需要用套餐价格-之前套餐明细行的价格之和
    for (var entry in comboMap.entries) {
      final combo = entry.value;
      final discount = comboDiscountMap[entry.key];
      final typedId = comboTypeIdMap[entry.key];
      final comboDetails = comboDetailMap[entry.key];
      if (comboDetails != null && comboDetails.isNotEmpty) {
        handleComboDetail(
          combo,
          comboDetails,
          preferential,
          typeId: typedId,
          discount: discount,
        );
      }
    }
  }

  ///从商品优惠辅助表中获取打折信息
  static Map<String, PreferentialDto> getDiscountFromPreferentialHelper({
    required GoodsDetailDto goods,
    List<GoodsDetailDto> comboDetails = const [],
  }) {
    //权益卡打折
    PreferentialDto? rightsCardPreferential;
    //优惠券打折
    PreferentialDto? couponPreferential;
    //对于套餐，优惠辅助信息记录在套餐明细行中
    if (goods.comboRow) {
      for (var detail in comboDetails) {
        rightsCardPreferential ??=
            detail.preferentialHelp[Preferential.discountRightsCard.name];
        couponPreferential ??=
            goods.preferentialHelp[Preferential.discountCoupon.name];
        if (rightsCardPreferential != null && couponPreferential != null) {
          break;
        }
      }
    } else {
      rightsCardPreferential =
          goods.preferentialHelp[Preferential.discountRightsCard.name];
      couponPreferential =
          goods.preferentialHelp[Preferential.discountCoupon.name];
    }
    Map<String, PreferentialDto> result = {};
    if (rightsCardPreferential != null) {
      result[Preferential.discountRightsCard.name] = rightsCardPreferential;
    }
    if (couponPreferential != null) {
      result[Preferential.discountCoupon.name] = couponPreferential;
    }
    return result;
  }

  ///使用商品中记录的折扣信息，重新执行打折流程
  ///处理单个商品的折扣，在促销过程中：
  ///1.当拆分商品时，商品数量会发生改变，需要重新执行打折
  ///2.促销中有一个选项是是否和权益卡叠加，如果不叠加，需要重新执行打折流程(不含权益卡)
  static void handleGoodsDiscountByPreferentialInfo({
    required GoodsDetailDto goods,
    required bool isVip,
    List<GoodsDetailDto> comboDetails = const [],
    bool executeRightsCard = true,
    bool getDiscountFromPreferentialHelper = true,
    //权益卡打折
    PreferentialDto? rightsCardPreferential,
    //优惠券打折
    PreferentialDto? couponPreferential,
  }) {
    //获取优惠辅助表中记录的打折信息
    if (getDiscountFromPreferentialHelper && isVip) {
      final discountInfoMap = DiscountHandler.getDiscountFromPreferentialHelper(
          goods: goods, comboDetails: comboDetails);
      rightsCardPreferential =
          discountInfoMap[Preferential.discountRightsCard.name];
      couponPreferential = discountInfoMap[Preferential.discountCoupon.name];
    }
    //重置折扣为1，清除优惠辅助信息
    DiscountUtil.resetDiscountTo1(goods, comboDetails: comboDetails);
    //执行会员价
    VipPriceHandler.handleSingleGoods(goods, isVip);
    if (goods.comboRow) {
      VipPriceHandler.handleComboDetails(goods, comboDetails);
    }
    //权益卡打折
    if (rightsCardPreferential != null && executeRightsCard && isVip) {
      handleSingleGoodsWithComboDetails(goods, Preferential.discountRightsCard,
          rightsCardPreferential.discount, rightsCardPreferential.typeId,
          comboDetails: comboDetails);
    }
    //优惠券打折
    if (couponPreferential != null && isVip) {
      handleSingleGoodsWithComboDetails(goods, Preferential.discountCoupon,
          couponPreferential.discount, couponPreferential.typeId,
          comboDetails: comboDetails);
    }
  }

  ///执行单个商品打折包含套餐明细的处理
  static void handleSingleGoodsWithComboDetails(GoodsDetailDto goods,
      Preferential preferential, num discount, String? typeId,
      {List<GoodsDetailDto> comboDetails = const []}) {
    DiscountHandler.handleSingleGoods(goods, preferential, discount, typeId);
    if (goods.comboRow) {
      DiscountHandler.handleComboDetail(goods, comboDetails, preferential,
          typeId: typeId, discount: discount);
    }
  }

  ///执行单个商品打折
  static void handleSingleGoods(GoodsDetailDto goods, Preferential preferential,
      num discount, String? typeId) {
    //记录此次打折之前的价格
    num beforeDiscountTotal = goods.discountTotal;
    //执行打折
    goods.discountTotal = SystemConfigTool.doubleMultipleToDecimal(
        beforeDiscountTotal, discount, BillDecimalType.TOTAL);
    GoodsTotalUtil.onDiscountTotalChange(goods, goods.discountTotal);
    //打折优惠金额
    num discountPreferential = SystemConfigTool.doubleSubtractionToDecimal(
        beforeDiscountTotal, goods.discountTotal, BillDecimalType.TOTAL);
    if (!goods.comboRow) {
      //记录优惠辅助信息
      PreferentialHelper.setGoodsPreferential(
              goods, preferential, discountPreferential,
              typeId: typeId)
          .discount = discount; //记录折扣，执行第二件半价时需要重新计算优惠
    }
  }

  ///执行套餐明细打折
  static void handleComboDetail(GoodsDetailDto combo,
      List<GoodsDetailDto> comboDetails, Preferential preferential,
      {String? typeId, num? discount}) {
    DiscountUtil._handleComboDetail(combo, comboDetails,
        preferential: preferential, typeId: typeId, discount: discount);
  }
}

///权益卡打折
class RightsCardHandler {
  RightsCardHandler._();

  ///执行权益卡优惠
  static void handle(
      {required GoodsBillDto bill,
      required bool isVip,
      required List<SsCardDto>? rightsCards}) {
    List<MemberEquityValuesBean>? rightsCardEquityValues;
    if (isVip) {
      //找出权益卡打折的权益
      rightsCardEquityValues = rightsCards?.expand((rightsCard) {
        final discountEquity =
            CardUtil.getDiscountEquity(rightsCard.memberEquityValues);
        if (discountEquity != null) {
          return [discountEquity];
        }
        return List<MemberEquityValuesBean>.empty(growable: false);
      }).toList();
    }
    DiscountHandler.handle(
        isVip: isVip,
        bill: bill,
        preferential: Preferential.discountRightsCard,
        memberEquityValues: rightsCardEquityValues ?? []);
  }
}

///计算打折后价格
class DiscountPriceUtil {
  DiscountPriceUtil._();
}
