import 'dart:math';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../enum/bill_type.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../vip/utils/vip_util.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/goods_detail_dto.dart';
import '../../entity/preferential_dto.dart';
import '../../entity/ss_card_dto.dart';
import '../../settlement/entity/score_configuration.dart';
import '../bill_goods_util.dart';
import '../bill_tool.dart';
import '../goods_tool.dart';
import 'card.dart';
import 'discount.dart';
import 'erase_zero.dart';
import 'price.dart';
import 'promotion.dart';
import 'score.dart';

///优惠工具类
class PreferentialUtil {
  PreferentialUtil._();

  ///从打折开始执行优惠
  static void startAtDiscount({
    required GoodsBillDto bill,
    required BillType billType,
    required VipWithLevelAssertsRightsCardDTO? vipInfo,
    required List<SsCardDto>? rightsCards,
    required List<SsCardDto> selectedCouponList,
    required num manualPreferential,
    required bool enableScore,
    required bool useMaxScore,
    required int scoreUse,
    required ScoreConfiguration? scoreConfiguration,
    required CardCallback onCouponNotUse,
    required ValueChanged<num> onManualPreferentialChange,
    required ValueChanged<int> onScoreUseChange,
  }) {
    bool isVip = isValidVip(vipInfo);
    //执行打折流程
    DiscountUtil.startDiscount(
        bill: bill,
        vipInfo: vipInfo,
        isVip: isVip,
        discountCoupon: selectedCouponList.firstWhereOrNull(
            (element) => element.cardType == CardType.discountCoupon.value),
        rightsCards: rightsCards,
        onCouponNotUse: onCouponNotUse);
    //执行促销
    PromotionUtil.startPromotion(bill, vipInfo, isVip: isVip);
    //执行优惠分摊
    BillPreferentialTool.startBillPreferential(
        bill: bill,
        isVip: isVip,
        vipInfo: vipInfo,
        manualPreferential: manualPreferential,
        selectedCouponList: selectedCouponList,
        enableScore: enableScore,
        useMaxScore: useMaxScore,
        scoreUse: scoreUse,
        scoreConfiguration: scoreConfiguration,
        onCouponNotUse: onCouponNotUse,
        onManualPreferentialChange: onManualPreferentialChange,
        onScoreUseChange: onScoreUseChange,
        resetToPromoted: false);
    //汇总单据
    collectBill(bill, billType: billType);
  }

  ///执行优惠分摊
  static void startBillPreferential({
    required GoodsBillDto bill,
    required BillType billType,
    required VipWithLevelAssertsRightsCardDTO? vipInfo,
    required num manualPreferential,
    required List<SsCardDto> selectedCouponList,
    required bool enableScore,
    required bool useMaxScore,
    required int scoreUse,
    required ScoreConfiguration? scoreConfiguration,
    required CardCallback onCouponNotUse,
    required ValueChanged<num> onManualPreferentialChange,
    required ValueChanged<int> onScoreUseChange,
  }) {
    bool isVip = isValidVip(vipInfo);
    //执行优惠分摊
    BillPreferentialTool.startBillPreferential(
      bill: bill,
      isVip: isVip,
      vipInfo: vipInfo,
      manualPreferential: manualPreferential,
      selectedCouponList: selectedCouponList,
      enableScore: enableScore,
      useMaxScore: useMaxScore,
      scoreUse: scoreUse,
      scoreConfiguration: scoreConfiguration,
      onCouponNotUse: onCouponNotUse,
      onManualPreferentialChange: onManualPreferentialChange,
      onScoreUseChange: onScoreUseChange,
    );
    //汇总单据
    collectBill(bill, billType: billType);
  }

  ///汇总
  static void collectBill(GoodsBillDto bill, {required BillType billType}) {
    BillTool.reCalcStatistic(bill, billType: billType);
    Map<String, PreferentialDto> preferentialHelp = {};
    for (var goods in bill.outDetail) {
      for (var value in goodsPreferentialSet) {
        collectPreferential(preferentialHelp, goods, value);
      }
    }
    for (var value in goodsPreferentialSet) {
      final preferential = preferentialHelp[value.name];
      if (preferential != null) {
        bill.preferentialHelp[value.name] = preferential;
      }
    }
    //处理套餐行税额
    TaxUtil.handleComboTaxByGoodsList(BillTool.getGoodsDetails(bill, billType));
  }

  ///汇总优惠辅助信息
  static void collectPreferential(Map<String, PreferentialDto> preferentialHelp,
      GoodsDetailDto goods, Preferential preferential) {
    final goodsPreferential = goods.preferentialHelp[preferential.name];
    if (goodsPreferential != null) {
      final billPreferential = preferentialHelp.putIfAbsent(
          preferential.name, () => PreferentialDto());
      //优惠券记录id
      if (preferential == Preferential.discountCoupon ||
          preferential == Preferential.giftCoupon ||
          preferential == Preferential.moneyCoupon) {
        billPreferential.typeId = goodsPreferential.typeId;
      }
      billPreferential.type = preferential.type.value;
      billPreferential.total =
          MathUtil.addDec(billPreferential.total, goodsPreferential.total)
              .toDouble();
    }
  }

  static void cleanPreferentialHelper(GoodsBillDto bill) {
    bill.preferentialHelp.clear();
    for (var goods in bill.outDetail) {
      goods.preferentialHelp.clear();
    }
    for (var goods in bill.inDetail) {
      goods.preferentialHelp.clear();
    }
  }
}

///整单优惠工具类
///包括：抵扣券、手工优惠分摊（总额优惠）、积分抵扣、抹零
class BillPreferentialTool {
  ///开始执行整单优惠
  ///[resetToPromoted] 是否将单据状态还原至促销后，对于直接执行此流程，需要重置
  ///
  ///包括：
  ///1.抵扣券
  ///[selectedCouponList] 已选优惠券列表，筛选出抵扣券
  ///[onCouponNotUse] 抵扣券没有使用的回调
  ///2.手工优惠分摊（总额优惠）
  ///[manualPreferential] 手工优惠金额
  ///[onManualPreferentialChange] 手工优惠金额大于金额，发生改变的回调
  ///3.积分抵扣
  ///[vipInfo] 获取会员剩余积分
  ///[enableScore] 是否启用积分抵扣
  ///[useMaxScore] 是否使用当前单据允许的最大积分
  ///[scoreUse] 页面中记录的积分使用数量
  ///4.抹零
  ///
  /// [executePreferential] 要执行的整单优惠，默认全部执行，换货单只执行总额优惠
  static void startBillPreferential({
    required GoodsBillDto bill,
    required VipWithLevelAssertsRightsCardDTO? vipInfo,
    required num manualPreferential,
    required List<SsCardDto> selectedCouponList,
    required bool enableScore,
    required bool useMaxScore,
    required int scoreUse,
    required ScoreConfiguration? scoreConfiguration,
    required CardCallback onCouponNotUse,
    required ValueChanged<num> onManualPreferentialChange,
    required ValueChanged<int> onScoreUseChange,
    bool resetToPromoted = true,
    Set<Preferential> executePreferential = billPreferentialSet,
    bool? isVip,
  }) {
    bool isVip = isValidVip(vipInfo);
    for (var goods in bill.outDetail) {
      GoodsTotalUtil.onPromotedTotalChange(goods, goods.promotedTotal);
      for (var value in billPreferentialSet) {
        goods.preferentialHelp.remove(value.name);
      }
    }
    for (var preferential in sortedBillPreferentialList) {
      bill.preferentialHelp.remove(preferential.name);
      if (!billPreferentialSet.contains(preferential)) continue;
      //抵扣券
      if (preferential == Preferential.moneyCoupon) {
        SsCardDto? coupon = CardUtil.getCardFromCardType(
            selectedCouponList, CardType.amountCoupon.value);
        bool executed = AmountCouponHandler()
            .handle(bill: bill, reductionCoupon: coupon, isVip: isVip);
        if (!executed && coupon != null) {
          onCouponNotUse(coupon);
        }
      }
      //手工改价
      else if (preferential == Preferential.billPreferential) {
        num total =
            ManualPreferentialHandler().handle(bill, manualPreferential);
        if (total != manualPreferential) {
          onManualPreferentialChange(total);
        }
      }
      //积分抵扣
      else if (preferential == Preferential.scoreDiscount) {
        int score = ScoreDiscountHandler().handle(vipInfo, bill,
            enableScore && isVip, useMaxScore, scoreUse, scoreConfiguration);
        if (score != scoreUse) {
          onScoreUseChange(score);
        }
      }
      //抹零
      else if (preferential == Preferential.eraseZero) {
        EraseZeroHandler().handle(bill);
      }
    }
  }

  ///获取指定整单优惠前的整单优惠类型
  static Preferential? getBillPreferentialBeforePreferential(
      Preferential preferential) {
    if (!billPreferentialSet.contains(preferential)) {
      throw Exception("${preferential.name}不在billPreferential中");
    }
    return sortedBillPreferentialList.lastWhereOrNull((element) =>
        element.billPreferentialPriority <
        preferential.billPreferentialPriority);
  }

  ///获取指定整单优惠前的单据金额
  static Decimal getBillTotalBeforeBillPreferential(
      GoodsBillDto bill, Preferential preferential) {
    return bill.outDetail
        //排除套餐行和赠品
        .where((element) => !element.gift && !GoodsTool.isComboRow(element))
        .fold<Decimal>(
          Decimal.zero,
          (previousValue, goods) =>
              previousValue +
              getGoodsTotalBeforeBillPreferential(preferential, goods),
        );
  }

  ///获取指定整单优惠后的单据金额
  static Decimal getBillTotalAfterBillPreferential(
      GoodsBillDto bill, Preferential preferential) {
    return bill.outDetail
        //排除套餐行和赠品
        .where((element) => !element.gift && !GoodsTool.isComboRow(element))
        .fold<Decimal>(
          Decimal.zero,
          (previousValue, goods) =>
              previousValue +
              getGoodsTotalAfterBillPreferential(preferential, goods),
        );
  }

  ///获取单个商品在某个整单促销之前的金额
  static Decimal getGoodsTotalBeforeBillPreferential(
      Preferential preferential, GoodsDetailDto goods) {
    final before = getBillPreferentialBeforePreferential(preferential);
    if (before == null) {
      if (goods.promotedTotal <= 0) {
        return Decimal.zero;
      } else {
        return Decimal.parse(goods.promotedTotal.toString());
      }
    }
    return getGoodsTotalAfterBillPreferential(before, goods);
  }

  ///获取单个商品在某个整单促销之后的金额
  static Decimal getGoodsTotalAfterBillPreferential(
      Preferential preferential, GoodsDetailDto goods) {
    if (!billPreferentialSet.contains(preferential)) {
      throw Exception("${preferential.name}不在billPreferential中");
    }
    //促销后金额
    if (goods.promotedTotal <= 0) return Decimal.zero;
    Decimal value = Decimal.parse(goods.promotedTotal.toString());
    for (var p in sortedBillPreferentialList) {
      if (p.billPreferentialPriority > preferential.billPreferentialPriority) {
        break;
      }
      PreferentialDto? preferentialDto = goods.preferentialHelp[p.name];
      if (preferentialDto != null && preferentialDto.total > 0) {
        value -= Decimal.parse(preferentialDto.total.toString());
        if (value <= Decimal.zero) return Decimal.zero;
      }
    }
    return value;
  }
}

///处理套餐优惠的工具类
class ComboPreferentialTool {
  ComboPreferentialTool._();

  ///根据套餐处理套餐明细行的优惠和价格、金额
  ///对于套餐中非最后一个明细行，用比例分别计算套餐明细行金额，
  ///对于套餐中最后一个明细行，为避免精度问题，需要用套餐金额-之前套餐明细行的金额之和
  ///[totalGetter] 获取商品的total
  ///[totalSetter] 给套餐明细设置优惠后的total，也可以计算此时的价格和折扣
  ///[preferential] 优惠类型，用来记录优惠辅助信息
  ///[typeId] 优惠类型id
  ///[discount] 优惠折扣
  static void handleComboDetail(
      GoodsDetailDto combo, List<GoodsDetailDto> comboDetails,
      {required num Function(GoodsDetailDto goods) totalGetter,
      required void Function(GoodsDetailDto goods, num total) totalSetter,
      Preferential? preferential,
      String? typeId,
      num? discount,
      bool calculateQty = true}) {
    comboDetails = comboDetails.toList()
      ..sort((a, b) => a.comboShareScale.compareTo(b.comboShareScale));
    //套餐优惠后金额
    num comboTotal = totalGetter(combo);
    Decimal detailTotalSum = Decimal.zero;
    for (int i = 0; i < comboDetails.length; i++) {
      GoodsDetailDto comboDetail = comboDetails[i];
      if (calculateQty) {
        num unitQty = SystemConfigTool.doubleMultipleToDecimal(
            combo.unitQty, comboDetail.comboQtyRate, BillDecimalType.QTY);
        comboDetail.unitQty = unitQty;
      }
      num comboDetailTotal;
      if (comboTotal == 0) {
        comboDetailTotal = 0;
      } else {
        //非套餐中最后一个商品，直接按 套餐金额x比例 换算
        if (i < comboDetails.length - 1) {
          num scale =
              MathUtil.divideDec(comboDetail.comboShareScale, 100).toDouble();
          comboDetailTotal = SystemConfigTool.doubleMultipleToDecimal(
              comboTotal, scale, BillDecimalType.TOTAL);
          detailTotalSum += Decimal.parse(comboDetailTotal.toString());
        } else {
          comboDetailTotal = SystemConfigTool.doubleSubtractionToDecimal(
              comboTotal, detailTotalSum.toDouble(), BillDecimalType.TOTAL);
        }
      }
      //记录优惠辅助信息
      if (preferential != null) {
        num preferentialTotal = SystemConfigTool.doubleSubtractionToDecimal(
            totalGetter(comboDetail), comboDetailTotal, BillDecimalType.TOTAL);
        final preferentialDto = PreferentialHelper.setGoodsPreferential(
            comboDetail, preferential, preferentialTotal,
            typeId: typeId);
        if (discount != null) {
          preferentialDto.discount = discount;
        }
      }
      //重新计算价格total
      totalSetter(comboDetail, comboDetailTotal);
    }
  }
}

///商品优惠辅助表工具
class PreferentialHelper {
  PreferentialHelper._();

  ///给商品添加优惠辅助信息
  ///返回该辅助信息，根据具体业务填入其他参数
  static PreferentialDto setGoodsPreferential(
      GoodsDetailDto goods, Preferential preferential, num total,
      {String? typeId}) {
    PreferentialDto preferentialDto = PreferentialDto();
    preferentialDto.type = preferential.type.value;
    preferentialDto.total = total;
    if (typeId != null) {
      preferentialDto.typeId = typeId;
    }
    goods.preferentialHelp[preferential.name] = preferentialDto;
    return preferentialDto;
  }

  ///给单据添加优惠辅助信息
  ///返回该辅助信息，根据具体业务填入其他参数
  static PreferentialDto setBillPreferential(
      GoodsBillDto bill, Preferential preferential, num total,
      {String? typeId}) {
    PreferentialDto preferentialDto = PreferentialDto();
    preferentialDto.type = preferential.type.value;
    preferentialDto.total = total;
    if (typeId != null) {
      preferentialDto.typeId = typeId;
    }
    bill.preferentialHelp[preferential.name] = preferentialDto;
    return preferentialDto;
  }
}

///单据级别优惠处理基类
///包含：
///1.抵扣券
///2.整单优惠分摊
///3.积分抵扣
///4.抹零
///5.订单满减(特殊，处理方式是整单优惠然后分摊，但是优惠金额记录到明细行优惠里，而不是记录到优惠分摊中。而且排除手工改价)
mixin BillPreferentialMixin {
  abstract final Preferential preferential;

  ///执行优惠分摊
  ///[goodsList] 商品列表（需要排除套餐明细、赠品、和金额为0的商品）
  ///[goodsListTotal] 商品总金额
  ///[billPreferential] 优惠金额，不得大于商品总金额
  ///[comboDetailsMap] 套餐明细
  ///对于套餐，应当先将金额按比例分摊到套餐上，再分摊给套餐明细
  void sharePreferentialToGoods({
    required GoodsBillDto bill,
    required List<GoodsDetailDto> goodsList,
    required num billPreferential,
    required num goodsListTotal,
    required Map<String, List<GoodsDetailDto>> comboDetailsMap,
    String? preferentialTypeId,
  }) {
    if (billPreferential > goodsListTotal) {
      billPreferential = goodsListTotal;
    }
    if (billPreferentialSet.contains(preferential)) {
      PreferentialHelper.setBillPreferential(
          bill, preferential, billPreferential,
          typeId: preferentialTypeId);
    }
    goodsList = goodsList.toList()
      ..sort((a, b) => getGoodsTotal(a).compareTo(getGoodsTotal(b)));
    num preferentialTotal = billPreferential;
    for (var i = 0; i < goodsList.length; ++i) {
      final goods = goodsList[i];
      //获取商品金额
      num goodsTotal = getGoodsTotal(goods);
      //分摊金额
      num preferential = 0;
      //对于非最后一个商品，则根据比例分摊
      if (i < goodsList.length - 1) {
        if (goodsTotal > 0) {
          //计算商品金额占比
          num scale = MathUtil.divideDec(goodsTotal, goodsListTotal).toDouble();
          //计算分摊的金额
          preferential = SystemConfigTool.doubleMultipleToDecimal(
              billPreferential, scale, BillDecimalType.TOTAL);
          //递减
          preferentialTotal = SystemConfigTool.doubleSubtractionToDecimal(
              preferentialTotal, preferential, BillDecimalType.TOTAL);
        }
      }
      //最后一个商品的分摊金额是剩余的优惠金额
      else {
        preferential = preferentialTotal;
      }
      if (preferential > goodsTotal) {
        preferential = goodsTotal;
      }
      //减少商品的金额（分摊）
      goodsTotal = SystemConfigTool.doubleSubtractionToDecimal(
          goodsTotal, preferential, BillDecimalType.TOTAL);
      setGoodsTotal(goods, goodsTotal);
      //记录优惠辅助信息
      if (goods.comboRow) {
        //对于套餐，优惠金额记录到套餐明细中
        ComboPreferentialTool.handleComboDetail(
            goods, comboDetailsMap[goods.comboRowId] ?? [],
            preferential: this.preferential,
            totalGetter: getGoodsTotal,
            totalSetter: setGoodsTotal,
            typeId: preferentialTypeId);
      } else {
        PreferentialHelper.setGoodsPreferential(
            goods, this.preferential, preferential,
            typeId: preferentialTypeId);
      }
    }
  }

  ///过滤商品，计算商品合计金额
  ///[originalGoodsList] 源商品列表
  ///[goodsList] 传入一个空数组，过滤后要参加优惠分摊的商品会添加到该数组中
  ///[comboDetailsMap] 传入一个空map，过滤后套餐明细会添加到其中
  num filterGoodsListAndCalculateTotal(
    List<GoodsDetailDto> originalGoodsList,
    List<GoodsDetailDto> goodsList,
    Map<String, List<GoodsDetailDto>> comboDetailsMap,
  ) {
    //商品合计金额
    num goodsSumTotal = 0;
    for (var goods in originalGoodsList) {
      //清除商品中记录的优惠辅助
      goods.preferentialHelp.remove(preferential.name);
      //排除赠品
      if (goods.gift) {
        continue;
      }
      //排除套餐明细行,并记录
      if (StringUtil.isNotZeroOrEmpty(goods.comboRowParId)) {
        comboDetailsMap.putIfAbsent(goods.comboRowParId, () => []).add(goods);
        continue;
      }
      //促销赠品不执行打折
      if (BillGoodsUtil.isPromotionGift(goods)) {
        continue;
      }
      //排除金额<0的商品
      if (getGoodsTotal(goods) < 0) {
        continue;
      }
      goodsList.add(goods);
      goodsSumTotal =
          MathUtil.addDec(goodsSumTotal, getGoodsTotal(goods)).toDouble();
    }
    return goodsSumTotal;
  }

  ///获取商品的金额
  num getGoodsTotal(GoodsDetailDto goods) {
    //由于是一起执行，顺序执行，所以无需特意去计算，而是根据当前的最终金额来累加即可
    // return BillPreferentialTool.getGoodsTotalBeforeBillPreferential(preferential, goods).toDouble();
    return goods.posCurrencyDisedTaxedTotal;
  }

  ///设置商品的金额，根据金额重新计算价格
  void setGoodsTotal(GoodsDetailDto goods, num total) {
    GoodsTotalUtil.onFinalTotalChange(goods, total);
  }
}

///手工优惠分摊
class ManualPreferentialHandler with BillPreferentialMixin {
  @override
  Preferential get preferential => Preferential.billPreferential;

  ///执行手工优惠分摊
  ///[preferentialTotal] 优惠金额
  num handle(GoodsBillDto bill, num preferentialTotal) {
    bill.preferentialHelp.remove(preferential.name);
    //套餐明细
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    //要执行分摊的商品列表
    List<GoodsDetailDto> goodsList = [];
    //商品合计金额
    num goodsSumTotal = filterGoodsListAndCalculateTotal(
        bill.outDetail, goodsList, comboDetailsMap);
    goodsSumTotal = max(0, goodsSumTotal);
    if (preferentialTotal > goodsSumTotal) {
      preferentialTotal = goodsSumTotal;
    }
    if (preferentialTotal > 0) {
      sharePreferentialToGoods(
          bill: bill,
          goodsList: goodsList,
          billPreferential: preferentialTotal,
          goodsListTotal: goodsSumTotal,
          comboDetailsMap: comboDetailsMap);
    }
    return preferentialTotal;
  }
}
