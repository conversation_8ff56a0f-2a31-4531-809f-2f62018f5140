import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';

import '../../common/login/login_center.dart';

/// 创建时间：2023/11/28 
/// 作者：xiaotiaochong
/// 描述：md5 加密 ；ASE 加密/解密

class EncryptionTool{
  //region MD5
  ///profileId 插眼
  static String enCodeMD5(String content) {
    String salt = LoginCenter.getLoginUser().profileId!;
    String data = salt + content;
    return md5FromString(data);
  }

  ///插眼 :前眼，后眼
  static String offlineEnCodeMD5(String content,String before,String after) {
    String data = before + content + after;
    return md5FromString(data);
  }

  ///原味 MD5
  static String md5FromString(String content) {
    var encoderContent = const Utf8Encoder().convert(content);
    var digest = md5.convert(encoderContent);
    return digest.toString();
  }
  //endregion
  //region RSA
  // 加密
  static String rsaEncodeString(String content,String key) {
    String publicKeyString = '-----BEGIN PUBLIC KEY-----\n$key\n-----END PUBLIC KEY-----';
    dynamic publicKey = RSAKeyParser().parse(publicKeyString);
    final encrypter = Encrypter(RSA(publicKey: publicKey));
    return encrypter.encrypt(content).base64; //返回加密后的base64格式文件
  }
  // 解密
  static String rsaDecodeString(String content,String key) {
    dynamic privateKey = RSAKeyParser().parse(key);
    final encrypter = Encrypter(RSA(privateKey: privateKey));
    return encrypter.decrypt(Encrypted.fromBase64(content));
  }

  //endregion
  //region AES

  ///加密向量
  static const int vector = 16;

  ///补足key string
  static String supplement = "0123456789012345";

  ///得到加密内容
  static String enCodeASE(String content,String keyString) {
    final iv = getInitializationVector(vector);
    final encrypter = getEncrypter(keyString);
    final encrypted = encrypter.encrypt(content, iv: iv);
    return encrypted.base64;
  }

  ///得到解密内容
  static String deCodeASE(String content64,String keyString) {
    final iv = getInitializationVector(vector);
    final encrypter = getEncrypter(keyString);
    final decrypted = encrypter.decrypt64(content64, iv: iv);
    return decrypted;
  }

  ///判断传入的key，
  ///如果大于16位，则截取前16位
  ///如果小于16位，用supplement补足
  static Encrypter getEncrypter(String originalKey) {
    String keyString = originalKey;
    if (originalKey.length > 16) {
      keyString = originalKey.substring(0,16);
    } else {
      keyString = "$originalKey$supplement";
      keyString = keyString.substring(0,16);
    }
    Key key = Key.fromUtf8(keyString);
    return Encrypter(AES(key,mode: AESMode.cbc));
  }

  static IV getInitializationVector(int vector) {
    return IV.fromLength(vector);
  }
  //ednnregion
}