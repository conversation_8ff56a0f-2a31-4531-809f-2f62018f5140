import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_button.dart';
import 'package:haloui/widget/halo_label.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../../bill/model/base_info_model.dart';
import '../../../common/keyboard_hidden.dart';
import '../../../widgets/halo_pos_label.dart';
import 'encryption_tool.dart';
import 'sms_verification_tool.dart';

/// 创建时间：2023/9/7
/// 作者：xiaotiaochong
/// 描述：

class StoreTool {
  static bool isValidPwd(String? pwd) {
    return RegExp(r"^\d{6}$").firstMatch(pwd ?? "") != null;
  }

  static showStorePageSet(
      BuildContext context, String vipCardId, Function(String value) callback) {
    showDialog(
        context: context,
        builder: (_) => StorePayDialog(
              vipCardId: vipCardId,
              type: StorePayType.setPwd,
            )).then((value) {
      callback(value);
    });
  }

  static showStorePageValid(
      BuildContext context, String vipCardId,String? vipPhone, Function(bool value) callback) {
    showDialog(
        context: context,
        builder: (_) => StorePayDialog(
              vipCardId: vipCardId,
              vipPhone: vipPhone,
              type: StorePayType.validPwd,
            )).then((value) {
      if (value is String && isValidPwd(value)) {
        callback(true);
      } else if (value is bool) {
        callback(value);
      }
    });
  }
}

enum StorePayType {
  setPwd, //设置密码
  validPwd, //验证密码
  resetPwd, //充值密码
}

class StorePayDialog extends StatefulWidget {
  StorePayDialog({Key? key, required this.vipCardId, required this.type, this.vipPhone})
      : super(key: key);

  StorePayType type;
  String vipCardId;
  String? vipPhone;

  @override
  State<StatefulWidget> createState() {
    return _StorePayDialogState();
  }
}

class _StorePayDialogState extends State<StorePayDialog> {
  @override
  late BuildContext context;
  bool tips = false;
  String tipsString = "";

  String pwd = "";
  String confirmPwd = "";

  late StorePayType type;

  KeyboardHiddenFocusNode pwdFocusNode = KeyboardHiddenFocusNode();
  KeyboardHiddenFocusNode confirmPwdFocusNode = KeyboardHiddenFocusNode();

  TextEditingController pwdController = TextEditingController();
  TextEditingController confirmPwdController = TextEditingController();

  String get title {
    String title = "储值支付密码";
    switch (type) {
      case StorePayType.setPwd:
        title = "请设置储值支付密码";
        break;
      case StorePayType.validPwd:
        title = "请输入储值支付密码";
        break;
      case StorePayType.resetPwd:
        title = "重置支付密码";
        break;
      default:
        title = "储值支付密码";
        break;
    }
    return title;
  }

  String get pwdHit {
    String string = "请输入6位支付密码";
    switch (type) {
      case StorePayType.resetPwd:
        string = "请输入6位新密码";
        break;
      default:
        string = "请输入6位支付密码";
        break;
    }
    return string;
  }

  String get confirmPwdHit {
    return "请再次确认密码";
  }

  @override
  void initState() {
    super.initState();
    type = widget.type;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      FocusScope.of(context).requestFocus(pwdFocusNode);
    });
  }

  @override
  Widget build(BuildContext context) {
    this.context = context;
    return Material(
        color: Colors.transparent,
        child: Container(
            alignment: Alignment.center,
            child: Container(
              height: 300.h,
              width: 500.w,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(8.h))),
              child: Column(
                children: [
                  _buildTitle(),
                  Container(
                    height: 1.h,
                    color: Colors.grey,
                  ),
                  _buildPwdView(),
                  _buildBottomBtn(),
                ],
              ),
            )));
  }

  _buildTitle() {
    return Container(
      height: 60.h,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(left: 20.w),
      child: HaloPosLabel(
        title,
        textStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 26.sp),
      ),
    );
  }

  _buildTextField(
      {String? hint,
        FocusNode? focusNode,
        TextEditingController? controller,
        required Function(String text) onChange,
        required Function(String text) onSubmit}) {
    return Container(
      height: 50.h,
      width: 400.w,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(left: 10.w),
      decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFD9D9D9), width: 1),
          borderRadius: BorderRadius.all(Radius.circular(4.h)),
          color: Colors.transparent),
      child: TextField(
        autofocus: true,
        controller: controller,
        focusNode: focusNode,
        obscureText: true,
        inputFormatters: [InputFormatter(r"^\d{0,6}$")],
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          hintText: hint ?? "请输入6位支付密码",
          hintStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),

          ///textField 下半部分被遮挡
          focusedBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 0, color: Colors.transparent)),
          disabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 0, color: Colors.transparent)),
          enabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 0, color: Colors.transparent)),
          border: const OutlineInputBorder(
              borderSide: BorderSide(width: 0, color: Colors.transparent)),
          contentPadding: EdgeInsets.zero,
        ),
        onChanged: (String text) {
          onChange(text);
        },
        onSubmitted: (String text) {
          onSubmit(text);
        },
      ),
    );
  }

  _buildBottomBtn() {
    return SizedBox(
        height: 80.h,
        child: Row(
          children: [
            const Expanded(child: SizedBox()),
            _buildBottomButton("取消",
                textColor: Colors.grey,
                buttonType: HaloButtonType.outlinedButton,
                borderColor: Colors.grey, onTap: () {
                  Navigator.pop(context);
                }),
            _buildBottomButton("确定", onTap: () {
              _doSubmit();
            }),
            SizedBox(width: 30.w)
          ],
        ));
  }

  _buildBottomButton(String text,
      {double margin = 20,
        Color? color,
        Color? textColor,
        Color? borderColor,
        HaloButtonType? buttonType,
        Function? onTap}) {
    return Container(
      margin: EdgeInsets.only(right: margin.w),
      child: HaloButton(
        buttonType: buttonType ?? HaloButtonType.elevatedButton,
        borderColor: borderColor ?? Colors.transparent,
        outLineWidth: 2.w,
        text: text,
        textColor: textColor ?? Colors.white,
        width: 100.w,
        height: 40.h,
        fontSize: 24.sp,
        backgroundColor: color,
        onPressed: () {
          if (onTap != null) {
            onTap();
          }
        },
      ),
    );
  }

  _buildPwdView() {
    List<Widget> children = [
      _buildTipsView(),
      _buildPwd(),
      _resetPwd(),
    ];

    if (type != StorePayType.validPwd) {
      children.addAll([
        SizedBox(
          height: 20.h,
        ),
        _buildConfirmPwd()
      ]);
    }

    return Expanded(
        child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children));
  }

  _buildTipsView() {
    return Container(
      padding: EdgeInsets.only(left: 10.w),
      child: Visibility(
          visible: tips,
          child: HaloLabel(
            tipsString,
            textStyle: TextStyle(
                color: Colors.red,
                fontSize: 20.sp,
                fontWeight: FontWeight.bold),
          )),
    );
  }

  _resetPwd() {
    return Visibility(
      visible: type == StorePayType.validPwd,
      child: GestureDetector(
        child: Container(
            padding: EdgeInsets.only(left: 10.w),
            height: 30.h,
            child: HaloLabel(
              "重置密码",
              textStyle: TextStyle(
                  color: Colors.blue,
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold),
            )),
        onTap: () {
          if (widget.vipPhone != null) {
            SMSTool.showSMSDialog(context, widget.vipPhone ?? "", (value) {
              if (value == true) {
                _doResetPwd();
              }
            });
          }
        },
      ),
    );
  }

  _buildPwd() {
    return _buildTextField(
        focusNode: pwdFocusNode,
        controller: pwdController,
        hint: pwdHit,
        onChange: (text) {
          pwd = text;
          if (type == StorePayType.validPwd) {
            confirmPwd = pwd;
          }
        },
        onSubmit: (text) {
          pwd = text;
          if (type == StorePayType.validPwd) {
            confirmPwd = pwd;
            _doSubmit();
          } else {
            confirmPwdFocusNode.requestFocus();
          }
        });
  }

  _buildConfirmPwd() {
    return _buildTextField(
        focusNode: confirmPwdFocusNode,
        controller: confirmPwdController,
        hint: confirmPwdHit,
        onChange: (text) {
          confirmPwd = text;
        },
        onSubmit: (text) {
          confirmPwd = text;
          _doSubmit();
        });
  }

  _doResetPwd() {
    Future.sync(() {
      pwd = "";
      confirmPwd = "";
      type = StorePayType.resetPwd;
      confirmPwdController.text = "";
      pwdController.text = "";
      ///flutter 这个地方真垃圾，设置text 为 "",光标位置会到负位置，导致获取到了焦点，但是无法输入
      ///下面因为调用的submit，submit返回了""，所以获取焦点后又可以用了，真垃圾
      ///因此在下面设置了controller 的光标位置
      pwdController.selection = TextSelection.fromPosition(const TextPosition(
          affinity: TextAffinity.downstream,
          offset: 0));
    }).then((value) {
      setState(() {
        FocusScope.of(context).requestFocus(pwdFocusNode);
      });
    });
  }

  _doSubmit() {
    if (StoreTool.isValidPwd(confirmPwd) && pwd == confirmPwd) {
      if (type == StorePayType.setPwd || type == StorePayType.resetPwd) {
        String md5Pwd = EncryptionTool.enCodeMD5(confirmPwd);
        BaseInfoModel.updateVipPassword(context, widget.vipCardId, md5Pwd)
            .then((value) {
          HaloToast.showMsg(context, msg: value);
          Navigator.pop(context, md5Pwd);
        });
      } else if (type == StorePayType.validPwd) {
        String md5Pwd = EncryptionTool.enCodeMD5(confirmPwd);
        BaseInfoModel.validVipPassword(context, widget.vipCardId, md5Pwd)
            .then((value) {
          if (value) {
            Navigator.pop(context, value);
          } else {
            setState(() {
              tips = true;
              tipsString = "密码错误请重输";
              pwdController.text = "";
              FocusScope.of(context).requestFocus(pwdFocusNode);
            });
          }
        });
      }
    } else {
      Future.sync(() {
        tips = true;
        tipsString = "无效的密码或两次密码不一致";
        if (pwd != confirmPwd) {
          tipsString = "或两次密码不一致";
        }
        if (!StoreTool.isValidPwd(confirmPwd)) {
          tipsString = "无效的密码(密码是6位数字密码)";
        }
        pwdController.text = "";
        confirmPwdController.text = "";
      }).then((value) {
        setState(() {
          FocusScope.of(context).requestFocus(pwdFocusNode);
        });
      });
    }
  }
}


class InputFormatter extends TextInputFormatter {
  final String regExp;

  InputFormatter(this.regExp);

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isNotEmpty) {
      if (RegExp(regExp).firstMatch(newValue.text) != null) {
        return newValue;
      }
      return oldValue;
    }
    return newValue;
  }
}