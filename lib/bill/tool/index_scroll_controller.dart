import 'package:flutter/material.dart';

mixin IndexScrollWidgetMixin on StatefulWidget {
  abstract final IndexScrollerController indexScrollerController;
}

mixin IndexScrollStateMixin<T extends IndexScrollWidgetMixin> on State<T> {
  @override
  void initState() {
    widget.indexScrollerController.state = this;
    super.initState();
  }

  @override
  void didUpdateWidget(T oldWidget) {
    if (oldWidget.indexScrollerController != widget.indexScrollerController) {
      oldWidget.indexScrollerController.dispose();
      widget.indexScrollerController.state = this;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.indexScrollerController.dispose();
    super.dispose();
  }

  //滑动到添加行
  void scrollToIndex(int index);
}

class IndexScrollerController {
  IndexScrollStateMixin? state;

  void scrollToIndex(int index) {
    if (state?.mounted == true) {
      state?.scrollToIndex(index);
    }
  }

  void dispose() {
    state = null;
  }
}
