import 'package:flutter/foundation.dart';

import '../entity/goods_detail_dto.dart';
import '../../db/ptype_db_manager.dart';

///商品范围匹配工具类
class GoodsScopeUtil {
  GoodsScopeUtil._();

  ///判断商品是否属于指定分类
  ///支持分类的层级匹配，即如果商品属于子分类，也会匹配父分类
  static bool isGoodsInCategory(GoodsDetailDto goods, String categoryId) {
    if (goods.typeid?.isEmpty != false || categoryId.isEmpty) {
      return false;
    }

    String goodsCategoryId = goods.typeid!;

    // 直接匹配
    if (goodsCategoryId == categoryId) {
      return true;
    }

    // 层级匹配：检查商品分类是否是指定分类的子分类
    return _isSubCategory(goodsCategoryId, categoryId);
  }

  ///判断商品是否包含指定标签
  static bool isGoodsHasLabel(GoodsDetailDto goods, String labelId) {
    if (goods.labelIds?.isEmpty != false || labelId.isEmpty) {
      return false;
    }

    return goods.labelIds!.contains(labelId);
  }

  ///判断商品是否在指定分类列表中
  static bool isGoodsInCategoryList(
    GoodsDetailDto goods,
    List<String> categoryIds,
  ) {
    if (categoryIds.isEmpty) {
      return false;
    }

    return categoryIds.any(
      (categoryId) => isGoodsInCategory(goods, categoryId),
    );
  }

  ///判断商品是否包含指定标签列表中的任一标签
  static bool isGoodsHasAnyLabel(GoodsDetailDto goods, List<String> labelIds) {
    if (labelIds.isEmpty) {
      return false;
    }

    return labelIds.any((labelId) => isGoodsHasLabel(goods, labelId));
  }

  ///判断商品是否包含指定标签列表中的所有标签
  static bool isGoodsHasAllLabels(GoodsDetailDto goods, List<String> labelIds) {
    if (labelIds.isEmpty) {
      return true; // 空列表认为匹配
    }

    if (goods.labelIds?.isEmpty != false) {
      return false;
    }

    return labelIds.every((labelId) => goods.labelIds!.contains(labelId));
  }

  ///检查分类层级关系
  ///判断childCategoryId是否是parentCategoryId的子分类
  static bool _isSubCategory(String childCategoryId, String parentCategoryId) {
    // 这里需要实现分类层级检查逻辑
    // 由于分类ID通常有层级结构，比如：
    // 父分类：001
    // 子分类：001001, 001002
    // 孙分类：001001001, 001001002

    // 简单的层级匹配：检查子分类ID是否以父分类ID开头
    if (childCategoryId.startsWith(parentCategoryId)) {
      return true;
    }

    return false;
  }

  ///从数据库获取商品的标签信息
  ///这个方法用于在商品数据中没有标签信息时，从数据库中获取
  static Future<List<String>> getGoodsLabelIds(String ptypeId) async {
    try {
      return await PtypeDbManager.getPtypeLabelIds(ptypeId);
    } catch (e) {
      // 如果获取失败，返回空列表
      return [];
    }
  }

  ///为商品填充标签信息
  ///如果商品的labelIds为空，则从数据库中获取
  static Future<void> fillGoodsLabelIds(GoodsDetailDto goods) async {
    if (goods.labelIds?.isNotEmpty == true) {
      return; // 已经有标签信息，无需重新获取
    }

    if (goods.ptypeId.isEmpty) {
      return; // 没有商品ID，无法获取标签
    }

    goods.labelIds = await getGoodsLabelIds(goods.ptypeId);
  }

  ///批量为商品列表填充标签信息
  static Future<void> fillGoodsListLabelIds(
    List<GoodsDetailDto> goodsList,
  ) async {
    for (var goods in goodsList) {
      await fillGoodsLabelIds(goods);
    }
  }

  ///为PtypeListModel列表填充标签信息
  static Future<void> fillPtypeListLabelIds(List<dynamic> ptypeList) async {
    if (ptypeList.isEmpty) return;

    for (var ptype in ptypeList) {
      if (ptype.id?.isNotEmpty == true) {
        try {
          List<String> labelIds = await getGoodsLabelIds(ptype.id);
          ptype.labelIds = labelIds;

          // 调试信息：打印标签填充结果
          if (labelIds.isNotEmpty) {
            debugPrint("商品 ${ptype.id} 填充标签: $labelIds");
          }
        } catch (e) {
          // 如果获取失败，设置为空列表
          ptype.labelIds = <String>[];
          debugPrint("获取商品 ${ptype.id} 标签失败: $e");
        }
      }
    }
  }
}
