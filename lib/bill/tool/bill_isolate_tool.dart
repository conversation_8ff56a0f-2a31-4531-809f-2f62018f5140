import 'dart:isolate';


/// 创建时间：2023/4/13
/// 作者：xiaotiaochong
/// 描述：

class BillIsolateTool {
  ///flutter 默认有iso主线程
  ///isolate 新线程和主线程不会共用内存，所以iso和port必须手动释放
  ///所有需要用的数据都必须从 主iso port 到 新iso
  //单例用以保证线程数量
  BillIsolateTool._internal();

  static final BillIsolateTool _instance = BillIsolateTool._internal();

  factory BillIsolateTool() => _instance;

  //isolate 必须kill ,port 必须close
  //否则会占用资源，导致程序越老越卡直到GG或remake，haha,0_0
  Isolate? iso;
  ReceivePort? port;
  ReceivePort? mainReceivePort;
  ReceivePort? newReceivePort;
  bool isIsoFinish = true;

  cleanIsolate() {
    port?.close();
    mainReceivePort?.close();
    //准备工作 0 暂停正在进行中的线程并释放
    iso?.pause(iso?.pauseCapability);
    iso?.kill(priority: Isolate.immediate);
  }
}

