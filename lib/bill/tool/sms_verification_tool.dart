import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:xml/xml.dart';

import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/halo_pos_label.dart';
import '../model/bill_model.dart';
import 'store_tool.dart';

/// 创建时间：2023/11/28
/// 作者：xiaotiaochong
/// 描述：

class SMSTool {
  //region 单例
  static final SMSTool _singletonPattern = SMSTool._internal();

  ///工厂构造函数
  factory SMSTool() {
    return _singletonPattern;
  }
  ///构造函数私有化，防止被误创建
  SMSTool._internal();
  ///
  Timer? timer;
  //endregion

  static showSMSDialog(BuildContext context, String vipPhone,
      Function(bool value) callback,{String? title}) {
    showDialog(context: context, builder: (context)=>  Scaffold(
      backgroundColor: Colors.transparent,
      body: SMSDialog(
        vipPhone: vipPhone,title: title,callback: (value){
        callback(value);
      },
      ),
    )
    );
  }
}

class SMSDialog extends BaseStatefulPage {

  SMSDialog({Key? key, this.vipPhone,this.title,this.callback}) : super(key: key);

  String? vipPhone;
  String? title;
  Function? callback;

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() {
    return _SMSDialogState();
  }
}

class _SMSDialogState extends BaseStatefulPageState<SMSDialog> {
  String vipPhone = "";
  String code = "";
  String noteString = "";

  static String smsTag = "sms_tag";
  static String smsId = "sms_id";

  TextEditingController phoneController = TextEditingController();

  final SmsController smsController = Get.put(SmsController());

  @override
  void initState() {
    super.initState();
    vipPhone = widget.vipPhone ?? "";
    if (smsController.time.value != 0) {
      SMSTool._singletonPattern.timer?.cancel();
      SMSTool._singletonPattern.timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        int time = smsController.time.toInt() - 1;
        if (time == 0) {
          timer.cancel();
        }
        smsController.timeChange(time);
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  void initTimer() {
    smsController.time = RxInt(60);
    SMSTool._singletonPattern.timer?.cancel();
    SMSTool._singletonPattern.timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      int time = smsController.time.toInt() - 1;
      if (time == 0) {
        timer.cancel();
      }
      smsController.timeChange(time);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        color: Colors.transparent,
        child:
        Container(
            alignment: Alignment.center,
            child: Container(
              height: 300.h,
              width: 600.w,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(8.h))),
              child: Column(
                children: [
                  _buildTitle(),
                  Container(
                    height: 1.h,
                    color: Colors.grey,
                  ),
                  _buildPwdView(),
                  _buildBottomBtn(),
                ],
              ),
            )));
  }

  _buildTitle() {
    String title = widget.title??"重置支付密码";
    return Container(
      height: 60.h,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(left: 20.w),
      child: HaloPosLabel(
        title,
        textStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 26.sp),
      ),
    );
  }

  _buildPwdView() {
    List<Widget> children = [
      _buildPhoneView(),
      SizedBox(
        height: 20.h,
      ),
      _buildNoteView(),
    ];
    return Expanded(
        child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: children)));
  }

  _buildPhoneView() {
    return Row(
      children: [
        _buildRowTitle("会员手机号："),
        Container(
          height: 50.h,
          width: 300.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFD9D9D9), width: 1),
              borderRadius: BorderRadius.all(Radius.circular(4.h)),
              color: const Color(0xFFD9D9D9)),
          child: HaloPosLabel(
            vipPhone,
          ),
        ),
      ],
    );
  }

  _buildRowTitle(String text) {
    return Container(
      height: 50.h,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(left: 10.w),
      decoration: const BoxDecoration(color: Colors.transparent),
      child: HaloPosLabel(
        text,
        textStyle: TextStyle(fontSize: 24.sp),
      ),
    );
  }

  _buildNoteView() {
    return Row(
      children: [
        _buildRowTitle("短信验证码："),
        _buildTextField(onChange: (text) {
          noteString = text;
        }, onSubmit: (text) {
          noteString = text;
        }),
        SizedBox(width: 30.w,),
        _buildGetCodeButton(),
      ],
    );
  }

  _buildGetCodeButton() {
    return GetBuilder<SmsController>(
      id: smsId,
      tag: smsTag,
      init: smsController,
      builder: (_) {
        return Obx(
              () {
            return GestureDetector(
                  child: Container(
                    width: 160.w,
                    height: 40.h,
                    alignment: Alignment.center,
                    child: Text(
                        smsController.title.value,
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: smsController.enable.value ? Colors.blue : Colors.grey
                      ),
                    ),
                  ),
                  onTap: (){
                    if (smsController.enable.value) {
                      _doSendSMS();
                      initTimer();
                    }
                  },
                );
          },
        );
      },
    );
  }

  _buildTextField({FocusNode? focusNode,
    TextEditingController? controller,
    required Function(String text) onChange,
    required Function(String text) onSubmit}) {
    return Container(
      height: 50.h,
      width: 150.w,
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(left: 10.w),
      decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFD9D9D9), width: 1),
          borderRadius: BorderRadius.all(Radius.circular(4.h)),
          color: Colors.transparent),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        // obscureText: true,
        inputFormatters: [InputFormatter(r"^\d{0,6}$")],
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          // hintText: hint ?? "请输入6位支付密码",
          hintStyle: TextStyle(color: Colors.grey, fontSize: 20.sp),

          ///textField 下半部分被遮挡
          focusedBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 0, color: Colors.transparent)),
          disabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 0, color: Colors.transparent)),
          enabledBorder: const OutlineInputBorder(
              borderSide: BorderSide(width: 0, color: Colors.transparent)),
          border: const OutlineInputBorder(
              borderSide: BorderSide(width: 0, color: Colors.transparent)),
          contentPadding: EdgeInsets.zero,
        ),
        onChanged: (String text) {
          onChange(text);
        },
        onSubmitted: (String text) {
          onSubmit(text);
        },
      ),
    );
  }

  _buildBottomBtn() {
    return SizedBox(
        height: 80.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildBottomButton("确定", onTap: () {
              _doSubmit();
            }),
            SizedBox(width: 40.w),
            _buildBottomButton("取消",
                textColor: Colors.grey,
                buttonType: HaloButtonType.outlinedButton,
                borderColor: Colors.grey, onTap: () {
              if(widget.callback!=null){
                widget.callback!(false);
              }
                  Navigator.pop(context, false);
                }),
          ],
        ));
  }

  _buildBottomButton(String text,
      {double margin = 0,
        Color? color,
        Color? textColor,
        Color? borderColor,
        HaloButtonType? buttonType,
        Function? onTap}) {
    return Container(
      margin: EdgeInsets.only(right: margin.w),
      child: HaloButton(
        buttonType: buttonType ?? HaloButtonType.elevatedButton,
        borderColor: borderColor ?? Colors.transparent,
        outLineWidth: 2.w,
        text: text,
        textColor: textColor ?? Colors.white,
        width: 100.w,
        height: 40.h,
        fontSize: 24.sp,
        backgroundColor: color,
        onPressed: () {
          if (onTap != null) {
            onTap();
          }
        },
      ),
    );
  }

  _doSubmit() {
    if (noteString == code && code != "") {
      if(widget.callback!=null){
        widget.callback!(true);
      }
      Navigator.pop(context,true);
    } else {
      HaloToast.showMsg(context, msg: "验证码无效");
    }
  }

  _doSendSMS() {
    Random random = Random.secure();
    code = "";
    for (var i = 0; i < 6; i++) {
      int randomString = random.nextInt(10); //0-9
      code = code + randomString.toString();
    }
    BillModel.sendMsg(context, vipPhone, code).then((value) {
      XmlDocument document =  XmlDocument.parse(value);
    final result = document.findAllElements('Result');
      final message = document.findAllElements('Message');
    if (result.isNotEmpty || message.isNotEmpty) {
      HaloToast.showMsg(context, msg: "${result.first.text} ${message.first.text}");
    } else {
      HaloToast.showMsg(context, msg: value.toString());
    }
    });
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    // TODO: implement buildLeftBody
    return Container();
  }

  @override
  String getActionBarTitle() {
    // TODO: implement getActionBarTitle
    return "";
  }

  @override
  Future<void>? onInitState() {
    // TODO: implement onInitState
  }
}

class SmsController extends GetxController {
  var time = 0.obs;
  var title = "获取验证码".obs;
  var enable = true.obs;

  void timeChange(int time) {
    this.time = RxInt(time);
    if (time == 0) {
      title = RxString("获取验证码");
      enable = RxBool(true);
    } else {
      title = RxString("重获验证码$time");
      enable = RxBool(false);
    }
    update([_SMSDialogState.smsId]);
  }
}