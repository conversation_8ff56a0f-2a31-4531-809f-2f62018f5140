
import 'dart:collection';

import 'package:flutter/material.dart';
import '../../../bill/tool/bill_tool.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../enum/bill_type.dart';
import 'package:halo_utils/utils/string_util.dart';

/// author : huang bo
/// time   : 2021/7/31 11:35
/// email  : <EMAIL>
/// desc   : 开单套餐处理工具类

class BillComboHelper {
  ///构建套餐明细map
  static Map<String, List<GoodsDetailDto>> getComboDetailMap(
      List<GoodsDetailDto> details) {
    Map<String, List<GoodsDetailDto>> map = LinkedHashMap();
    for (GoodsDetailDto detailItem in details) {
      if (StringUtil.isNotZeroOrEmpty(detailItem.comboRowParId)) {
        if (map.containsKey(detailItem.comboRowParId)) {
          map[detailItem.comboRowParId]!.add(detailItem);
        } else {
          map[detailItem.comboRowParId] = [detailItem];
        }
      }
    }
    return map;
  }

  ///构建新数据结构
  static List<GoodsDetailDto> getNewGoodsDetailList(
      List<GoodsDetailDto> details,
      Map<String, List<GoodsDetailDto>> comboMap) {
    List<GoodsDetailDto> newDetails = [];
    for (GoodsDetailDto dto in details) {
      //套餐明细已经在map中构建好，直接使用
      if (dto == null || StringUtil.isNotZeroOrEmpty(dto.comboRowParId)) {
        continue;
      }
      newDetails.add(dto);
      if (comboMap.containsKey(dto.comboRowId)) {
        newDetails.addAll(comboMap[dto.comboRowId]!);
      }
    }
    return newDetails;
  }

  ///价格相关是否可以编辑
  ///套餐行采购时不能编辑价格相关
  ///套餐明细销售时候不能编辑价格相关
  ///非套餐可以编辑
  static bool enableDetailPrice(BillType billType, GoodsDetailDto dto) =>
      (dto.comboRow == true) ||
      (StringUtil.isNotZeroOrEmpty(dto.comboRowParId)) ||
      (dto.comboRow != true && StringUtil.isZeroOrEmpty(dto.comboRowParId));

  ///赠品是否可以修改
  ///套餐行都可以修改
  ///采购类套餐明细允许修改
  static bool enableGift(BillType billType, GoodsDetailDto dto) =>
      dto.comboRow == true ||
      StringUtil.isZeroOrEmpty(dto.comboRowParId) ||
      (StringUtil.isNotZeroOrEmpty(dto.comboRowParId)
          );

  ///套餐相关是否允许录入备注
  static bool enableMemo(BillType billType, GoodsDetailDto dto) =>
      (dto.comboRow != true && StringUtil.isZeroOrEmpty(dto.comboRowParId)) ||
      (StringUtil.isNotZeroOrEmpty(dto.comboRowParId)
          );

  ///取消套餐行
  static List<GoodsDetailDto>? cancelCombo(
      int index, List<GoodsDetailDto> details, GoodsDetailDto dto) {
    if (dto.comboRow != true) {
      return null;
    }
    details.removeAt(index);

    List<GoodsDetailDto>? cDetails = details
        .where((element) => dto.comboRowId == element.comboRowParId)
        ?.toList();
    cDetails?.forEach((element) {
      element.comboRowParId = "0";
      element.comboId = "0";
      element.comboDetailId = "0";
      element.comboShareScale = 0;
    });
    return cDetails;
  }

  ///取消套餐行
  static List<GoodsDetailDto>? cancelComboByComboDetail(
      List<GoodsDetailDto> details, GoodsDetailDto dto) {
    if (dto.comboRow != true) {
      return null;
    }
    details.remove(dto);

    List<GoodsDetailDto>? cDetails = details
        .where((element) => dto.comboRowId == element.comboRowParId)
        ?.toList();
    cDetails?.forEach((element) {
      element.comboRowParId = "0";
      element.comboId = "0";
      element.comboDetailId = "0";
      element.comboShareScale = 0;
    });
    return cDetails;
  }

  ///同步套餐和套餐明细除价格外其它信息
  static void syncComboDetailKTypeByComboItem(
      BillType billType, List<GoodsDetailDto> details) {
    for (var element in details) {
      //套餐行改变
      if (element.comboRow == true) {
        details
            .where((e) => element.comboRowId == e.comboRowParId)
            ?.forEach((e) {
          //更新套餐明细 仓库、赠品信息
          e.ktypeId = element.ktypeId;
          e.kfullname = element.kfullname;
        });
      }
    }
  }

  /// 刷新套餐明细行的货位信息
  static void refreshComboDetailsStockPosition(
    BuildContext context, {
    bool? isInStock,
    List<GoodsDetailDto>? goodsDetails,
  }) {
    if (null == goodsDetails) {
      return;
    }
    List<GoodsDetailDto> neededRefreshStockPositionDetails = [];
    for (int i = 0; i < goodsDetails.length; i++) {
      GoodsDetailDto obj1 = goodsDetails[i];
      for (int j = 0; j < goodsDetails.length; j++) {
        GoodsDetailDto obj2 = goodsDetails[j];
        if (null != obj1.comboRow && obj1.comboRow) {
          if (obj1.comboRowId == obj2.comboRowParId) {
            neededRefreshStockPositionDetails.add(obj2);
          }
        }
      }
    }
  }

  ///同步套餐和套餐明细除价格外其它信息
  static void syncGiftComboItemAndComboDetail(BillType billType,
      List<GoodsDetailDto> details, GoodsDetailDto changedDto) {
    //套餐行改变
    if (changedDto.comboRow == true) {
      details
          .where((e) => changedDto.comboRowId == e.comboRowParId)
          .forEach((e) {
        if ((e.comboShareScale> 0)) {
          e.gift = changedDto.gift ?? false;
        }
      });
    }
    //套餐明细
    else if (StringUtil.isNotZeroOrEmpty(changedDto.comboRowParId)) {
      //套餐明细改变后，同步套餐行赠品信息
      GoodsDetailDto itemDto = details.firstWhere(
          (element) => changedDto.comboRowParId == element.comboRowId,
          orElse: () => GoodsDetailDto());

      if (itemDto.pFullName != null) {
        List<GoodsDetailDto> cDetails = details
            .where((element) => element.comboRowParId == itemDto.comboRowId)
            .toList();

        itemDto.gift = true;
        for (var element in cDetails) {
          if (itemDto.gift) {
            itemDto.gift = element?.gift ?? element.gift ?? false;
          }
        }
      }
    }
  }
}
