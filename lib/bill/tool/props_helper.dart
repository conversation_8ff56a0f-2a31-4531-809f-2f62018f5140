import '../entity/ptype/ptype_dto.dart';
import '../entity/ptype/ptype_prop_dto.dart';
import 'package:halo_utils/utils/string_util.dart';
import '../entity/goods_detail_dto.dart';
import '../entity/ptype/ptype_sku.dart';
import '../entity/ptype/ptype_unit_sku_po.dart';

/// Copyright (C), 2019-2020, wsgjp.com
/// FileName:props_helper
/// Author: lidingwen
/// Date: 6/25/21
/// Description:
class PropsHelper {
  static void setPropValueString(GoodsDetailDto? model) {
    if (model == null || StringUtil.isNotZeroOrEmpty(model.propValues)) {
      return;
    }
    model.propValues = buildGoodsPropertyString(model.prop ?? []);
  }

  static String getPropValueStringBySku(PtypeSku? skuDto) {
    String propValues = "";
    if (skuDto == null) {
      return propValues;
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName1)) {
      propValues = skuDto.propvalueName1!;
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName2)) {
      propValues += ":${skuDto.propvalueName2!}";
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName3)) {
      propValues += ":${skuDto.propvalueName3!}";
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName4)) {
      propValues += ":${skuDto.propvalueName4!}";
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName5)) {
      propValues += ":${skuDto.propvalueName5!}";
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName6)) {
      propValues += ":${skuDto.propvalueName6!}";
    }
    return propValues;
  }

  static String getPropValueStringByUnitSku(UnitSkuBean? skuDto) {
    String propValues = "";
    if (skuDto == null) {
      return propValues;
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName1)) {
      propValues = skuDto.propvalueName1!;
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName2)) {
      propValues += ":${skuDto.propvalueName2!}";
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName3)) {
      propValues += ":${skuDto.propvalueName3!}";
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName4)) {
      propValues += ":${skuDto.propvalueName4!}";
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName5)) {
      propValues += ":${skuDto.propvalueName5!}";
    }
    if (StringUtil.isNotEmpty(skuDto.propvalueName6)) {
      propValues += ":${skuDto.propvalueName6!}";
    }
    return propValues;
  }

  static String getPropValueIdStringByPropList(List<PtypePropDto>? propList) {
    if (propList == null || propList.isEmpty) {
      return "";
    }
    return propList
        .map((e) => e.propvalueId)
        .where((element) => StringUtil.isNotEmpty(element))
        .join(':');
  }

  static String getPropValueIdStringByPropValueList(
      List<PropvaluesBean>? propList) {
    if (propList == null || propList.isEmpty) {
      return "";
    }
    return propList
        .map((e) => e.id)
        .where((element) => StringUtil.isNotEmpty(element))
        .join(':');
  }

  /// 商品属性值转商品属性列表
  static List<PtypePropDto> getPropListBySkuProps(SkuBean? skuBean) {
    if (skuBean == null) {
      return [];
    }
    List<PtypePropDto> propsList = [];
    if (StringUtil.isNotZeroOrEmpty(skuBean.propId1)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propvalueId = skuBean.propvalueId1;
      propDto.propvalueName = skuBean.propvalueName1 ?? "";
      propDto.propIndex = 0;
      propsList.add(propDto);
    }
    if (StringUtil.isNotZeroOrEmpty(skuBean.propId2)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propvalueId = skuBean.propvalueId2;
      propDto.propvalueName = skuBean.propvalueName2 ?? "";
      propDto.propIndex = 1;
      propsList.add(propDto);
    }

    if (StringUtil.isNotZeroOrEmpty(skuBean.propId3)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propvalueId = skuBean.propvalueId3;
      propDto.propvalueName = skuBean.propvalueName3 ?? "";
      propDto.propIndex = 2;
      propsList.add(propDto);
    }
    if (StringUtil.isNotZeroOrEmpty(skuBean.propId4)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propvalueId = skuBean.propvalueId4;
      propDto.propvalueName = skuBean.propvalueName4 ?? "";
      propDto.propIndex = 3;
      propsList.add(propDto);
    }
    if (StringUtil.isNotZeroOrEmpty(skuBean.propId5)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propvalueId = skuBean.propvalueId5;
      propDto.propvalueName = skuBean.propvalueName5 ?? "";
      propDto.propIndex = 4;
      propsList.add(propDto);
    }
    if (StringUtil.isNotZeroOrEmpty(skuBean.propId6)) {
      PtypePropDto propDto = PtypePropDto();
      propDto.propvalueId = skuBean.propvalueId6;
      propDto.propvalueName = skuBean.propvalueName6 ?? "";
      propDto.propIndex = 5;
      propsList.add(propDto);
    }
    return propsList;
  }

  ///构建商品属性拼接字符串
  static String buildGoodsPropertyString(List<PtypePropDto> prop) {
    return prop
        .map((e) => e.propvalueName)
        .where((element) => StringUtil.isNotEmpty(element))
        .join(':');
  }
}
