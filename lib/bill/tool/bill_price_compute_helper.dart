import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/preferential_dto.dart';
import '../../../bill/tool/price_compute/price_compute.dart';
import '../../../bill/tool/price_compute/price_compute_with_no_tax.dart';
import '../../../bill/tool/price_compute/price_compute_with_tax_and_ptype_in_tax.dart';
import '../../../common/tool/system_config_tool.dart';
import '../../../entity/system/system_config_dto.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../enum/bill_ptype_pop_value_change_type.dart';
import '../../../enum/bill_type.dart';
import 'decimal_display_helper.dart';

/// Copyright (C), 2019-2020, wsgjp.com
/// FileName: bill price compute helper 单据价格计算类
/// Author: lidingwen
/// Date: 2020/5/12 3:38 PM
/// Description:

@Deprecated("后续考虑删除此类")
class BillPriceComputeHelper {
  late PriceCompute priceCompute;

  BillPriceComputeHelper() {
    SystemConfigDto systemConfigDto = DecimalDisplayHelper.instance.configDto;
    if (systemConfigDto.sysGlobalEnabledTax) {
      bool ptypePriceHasTax = systemConfigDto.sysGlobalEnabledSaleTax; //商品是否含税
      if (ptypePriceHasTax) {
        //管税，商品价格含税
        priceCompute = PriceComputeWithTaxAndPtypeInTax();
      } else {
        //管税，商品价格不含税
        throw Exception("暂不支持的价格计算");
      }
    } else {
      priceCompute = PriceComputeWithNoTax();
    }
  }

  //订单生单，原单退货：修改原单商品的数量，则优惠分摊需要重新计算。需求编号:13137
  ///orgQty 剩余未完成数量  orgSharePreferentialTotal 剩余优惠分摊金额
  void _orderAndBackCreateBillOnQtyChangeRefreshPreferentialTotal(
      GoodsDetailDto goodsDetail, String newQty) {
    String newPreferentialShare = "0";
    //赠品，无需计算分摊
    if (goodsDetail.gift) {
      return;
    }
    //剩余分摊<=0
    if (!MathUtil.compare(
        goodsDetail.remainCurrencyPreferentialShare ?? "0", "0")) {
      return;
    }

    //新qty>=剩余数量
    if (MathUtil.compare(newQty, goodsDetail.remainUnitQty ?? "0") ||
        MathUtil.equal(newQty, goodsDetail.remainUnitQty ?? "0")) {
      newPreferentialShare = goodsDetail.remainCurrencyPreferentialShare ?? "0";
    } else {
      //按照qty/剩余qty的比例，来计算优惠分摊
      newPreferentialShare = SystemConfigTool.doubleDivision(
          SystemConfigTool.doubleMultiple(
              goodsDetail.remainCurrencyPreferentialShare.toString(),
              newQty,
              BillDecimalType.PRICE),
          goodsDetail.remainUnitQty ?? "0",
          BillDecimalType.TOTAL);
    }
    priceCompute.onPreferentialShareChange(goodsDetail, newPreferentialShare);
  }

  //region 优惠分摊

  ///赠金分摊的计算,赠金分摊到明细，套餐分摊到套餐明细
  ///
  ///规则：根据明细行的小计的占比对单据赠金进行分摊，误差精度倒序磨平处理（原则：优惠金额不能大于明细小计）
  ///规则：根据明细行的小计的占比对单据赠金进行分摊，误差精度倒序磨平处理（原则：优惠金额不能大于明细小计）
  ///goodsDetailList分摊明细列表
  ///billTotal 分摊明细金额汇总
  ///shareTotal 分摊金额
  ///isRemoveComboDetail 是否不分摊到套餐明细上
  ///preferentialDto 优惠信息
  calGiftTotalShare(
      List<GoodsDetailDto> goodsDetailList, String billTotal, String shareTotal,
      {bool isRemoveComboDetail = true,
      PreferentialDto? preferentialDto,
      num backSumGiveTotal = 0}) {
    if (goodsDetailList.isEmpty) {
      return goodsDetailList;
    }
    if (backSumGiveTotal != 0) {
      shareTotal =
          MathUtil.add(shareTotal, backSumGiveTotal.toString()).toString();
    }

    ///排除套餐明细行的明细
    List<GoodsDetailDto> unContainComboDetailList = goodsDetailList
        .where((element) => StringUtil.isZeroOrEmpty(element.comboRowParId))
        .sortedBy((element) => element.posCurrencyDisedTaxedTotal);

    ///套餐明细行
    Map<String, List<GoodsDetailDto>> comboDetailList = groupBy(
        goodsDetailList.where(
            (element) => StringUtil.isNotZeroOrEmpty(element.comboRowParId)),
        (item) => item.comboRowParId);
    _shareGiveTotalToDetail(unContainComboDetailList, billTotal, shareTotal,
        preferentialDto: preferentialDto, comboDetailList: comboDetailList);
    return goodsDetailList;
  }

  _shareGiveTotalToDetail(
      List<GoodsDetailDto> goodsDetailList, String billTotal, String shareTotal,
      {PreferentialDto? preferentialDto,
      Map<String, List<GoodsDetailDto>>? comboDetailList}) {
    ///已分摊金额
    Decimal allocatedTotal = Decimal.fromInt(0);

    for (int i = 0; i < goodsDetailList.length; i++) {
      GoodsDetailDto currItem = goodsDetailList[i];

      ///分摊赠金
      String currentSplit = "0";

      /// 若为最后一条需磨平误差 赠金分摊金额 =总分摊赠金-已分摊赠金金额
      if (i == goodsDetailList.length - 1) {
        currentSplit =
            MathUtil.subtraction(shareTotal, allocatedTotal.toString())
                .toString();
      } else {
        ///明细赠金分摊比例=明细小计/单据金额，保留2位小数
        String allocationRatio = SystemConfigTool.doubleDivision(
            currItem.currencyDisedTaxedTotal.toString(),
            billTotal,
            BillDecimalType.TOTAL);

        ///明细赠金分摊金额 =赠金金额*明细赠金分摊比例
        currentSplit = DecimalDisplayHelper.getTotalFixed(
            MathUtil.multiplication(shareTotal, allocationRatio).toString());

        ///已分摊金额汇总
        allocatedTotal = MathUtil.add(allocatedTotal.toString(), currentSplit);
      }

      ///明细更改赠金分摊后的计算
      _calGoodsItemGiftShare(goodsDetailList, currItem, currentSplit,
          preferentialDto: preferentialDto,
          comboDetails: comboDetailList?[currItem.comboRowId]);
    }
  }

  ///明细更改赠金分摊后的计算
  _calGoodsItemGiftShare(List<GoodsDetailDto> goodsDetailList,
      GoodsDetailDto goodsDetailDto, String giftTotal,
      {PreferentialDto? preferentialDto, List<GoodsDetailDto>? comboDetails}) {
    if (null != preferentialDto) {
      ///记录优惠辅助信息
      _setPreferentialHelpTotal(goodsDetailDto, Preferential.giftStore.name,
          num.parse(giftTotal), preferentialDto);
    }

    ///明细为套餐行，需将套餐分摊的赠金分摊到套餐明细中
    if (null != comboDetails && comboDetails.isNotEmpty) {
      _shareGiveTotalToDetail(comboDetails,
          goodsDetailDto.currencyDisedTaxedTotal.toString(), giftTotal);
    }

    ///赠金变更 计算
    priceCompute.onValueChange(
        goodsDetailDto, PtypePopValueChangeType.giftTotal, giftTotal);
  }

  ///优惠分摊基础
  ///包含两种分摊逻辑
  ///1.订单满减：此逻辑会根据没有任何分摊的金额来进行分摊
  ///2.其他分摊：此逻辑分摊会根据已加入订单满减的分摊来分摊，因为订单满减会有促销不参与订单满减的情况
  ///如果不加入订单满减来计算，就会导致平均分摊，这样有些商品的小计为负数，有些小计为正数。
  ///单据的结果没有影响，但是商品的数据是不正确的。
//todo 此方式感觉还能优化
  _preferentialShareCalculate(List<GoodsDetailDto> goodsDetailList,
      PreferentialDto preferentialDto, String detailKey) {
    if (goodsDetailList.isEmpty) {
      return;
    }
    //是否是订单满减
    bool promotionOrder = detailKey == PreferentialDtoType.promotionBill;
    //先根据界面金额 还原折后含税金额
    preferentialTotalSplitCalculateBack(goodsDetailList,
        promotionOrder: promotionOrder);
    Map maxTotalAndIndexMap = _findMaxDisedTaxedTotalIndex(goodsDetailList,
        promotionOrder: promotionOrder);
    int maxTotalIndex = maxTotalAndIndexMap["maxIndex"];
    Decimal disedTotal = maxTotalAndIndexMap["total"];

    //计算分摊
    Decimal splitTotal = Decimal.fromInt(0);
    for (int i = 0; i < goodsDetailList.length; i++) {
      if (i == maxTotalIndex) {
        continue;
      }
      GoodsDetailDto currItem = goodsDetailList[i];
      if (currItem.comboRow) {
        continue;
      }

      ///商品优惠分摊 = 优惠分摊总金额 * (商品金额 / 单据折后含税金额)
      Decimal currentSplit = MathUtil.parseToDecimal(
          SystemConfigTool.doubleMultiple(
              preferentialDto.total.toString(),
              MathUtil.division(
                      promotionOrder
                          ? currItem.discountTotal.toString()
                          : currItem.currencyDisedTaxedTotal.toString(),
                      disedTotal.toString())
                  .toString(),
              BillDecimalType.TOTAL));
      splitTotal += currentSplit;
      _setPreferentialHelpTotal(
          currItem, detailKey, currentSplit.toDouble(), preferentialDto);
      _setGoodsPreferentialTotal(currItem);
    }

    //将差异折后含税金额记录在最大的项
    GoodsDetailDto maxItem = goodsDetailList[maxTotalIndex];
    //最新的优惠分摊金额
    var currencyPreferentialShare = MathUtil.subtraction(
            preferentialDto.total.toString(), splitTotal.toString())
        .toStringAsFixed(SystemConfigTool.getDigital(BillDecimalType.TOTAL));
    _setPreferentialHelpTotal(maxItem, detailKey,
        num.parse(currencyPreferentialShare), preferentialDto);
    _setGoodsPreferentialTotal(maxItem);
    return goodsDetailList;
  }

  ///为优惠性能做出的优惠分摊改动
  ///已做好订单满减的优惠分摊，在此基础上
  ///对优惠券、整单优惠、积分抵扣、抹零优惠做优惠分摊
  preferentialShareCalculate(
    List<GoodsDetailDto> goodsDetailList, {
    required PreferentialDto couponPreferential,
    required PreferentialDto manualPreferential,
    required PreferentialDto scorePreferential,
    required PreferentialDto eraseZeroPreferential,
  }) {
    if (goodsDetailList.isEmpty) {
      return;
    }
    bool promotionOrder = false;
    //先根据界面金额 还原折后含税金额
    //订单满减的优惠分摊需要包含订单满减分摊
    preferentialTotalSplitCalculateBack(goodsDetailList,
        promotionOrder: promotionOrder);
    //查找最大的折后含税金额（小计）
    Map maxTotalAndIndexMap = _findMaxDisedTaxedTotalIndex(goodsDetailList,
        promotionOrder: promotionOrder);
    int maxTotalIndex = maxTotalAndIndexMap["maxIndex"];
    Decimal disedTotal = maxTotalAndIndexMap["total"];

    ///计算分摊，每一部分的总优惠分摊
    ///代金券、整单优惠、积分、抹零
    Decimal couponTotal = Decimal.fromInt(0);
    Decimal manualTotal = Decimal.fromInt(0);
    Decimal scoreTotal = Decimal.fromInt(0);
    Decimal eraseZeroTotal = Decimal.fromInt(0);

    String couponKey = PreferentialDtoType.couponBill;
    String manualKey = PreferentialDtoType.manualBill;
    String scoreKey = PreferentialDtoType.scoreBill;
    String eraseZero = PreferentialDtoType.eraseZeroBill;

    for (int i = 0; i < goodsDetailList.length; i++) {
      if (i == maxTotalIndex) {
        continue;
      }
      GoodsDetailDto currItem = goodsDetailList[i];
      if (currItem.comboRow) {
        continue;
      }

      ///比例 = (商品金额 / 单据折后含税金额)
      ///商品优惠分摊 = 优惠分摊总金额 * 比例
      String ratio = MathUtil.division(
              currItem.currencyDisedTaxedTotal.toString(),
              disedTotal.toString())
          .toString();

      ///优惠券
      couponTotal = partCurrentPreferential(
          currItem, couponPreferential, couponKey, couponTotal, ratio);

      ///整单优惠
      manualTotal = partCurrentPreferential(
          currItem, manualPreferential, manualKey, manualTotal, ratio);

      ///积分抵扣
      scoreTotal = partCurrentPreferential(
          currItem, scorePreferential, scoreKey, scoreTotal, ratio);

      ///抹零优惠
      eraseZeroTotal = partCurrentPreferential(
          currItem, eraseZeroPreferential, eraseZero, eraseZeroTotal, ratio);

      ///商品设置总优惠分摊
      _setGoodsPreferentialTotal(currItem);
    }

    ///将差异折后含税金额记录在最大的项
    GoodsDetailDto maxItem = goodsDetailList[maxTotalIndex];

    ///优惠券
    maxPartCurrentPreferential(
        maxItem, couponPreferential, couponKey, couponTotal);

    ///整单优惠
    maxPartCurrentPreferential(
        maxItem, manualPreferential, manualKey, manualTotal);

    ///积分抵扣
    maxPartCurrentPreferential(
        maxItem, scorePreferential, scoreKey, scoreTotal);

    ///抹零优惠
    maxPartCurrentPreferential(
        maxItem, eraseZeroPreferential, eraseZero, eraseZeroTotal);

    ///最大项商品设置总优惠分摊
    _setGoodsPreferentialTotal(maxItem);
    return goodsDetailList;
  }

  ///比例 = (商品金额 / 单据折后含税金额)
  ///商品优惠分摊 = 优惠分摊总金额 * 比例
  ///比例:ratio ; PreferentialDtoType : detailKey ; 总优惠分摊(当前项) : preferential ; 总优惠分摊金额 : total
  Decimal partCurrentPreferential(
      GoodsDetailDto currItem,
      PreferentialDto preferential,
      String detailKey,
      Decimal total,
      String ratio) {
    ///当前计算出的优惠分摊
    Decimal couponCurrentPreferential = MathUtil.parseToDecimal(
        SystemConfigTool.doubleMultiple(
            preferential.total.toString(), ratio, BillDecimalType.TOTAL));
    total += couponCurrentPreferential;

    ///商品设置部分优惠分摊
    _setPreferentialHelpTotal(currItem, detailKey,
        couponCurrentPreferential.toDouble(), preferential);
    return total;
  }

  ///计算优惠分摊方式与上面不同，用最大减去其他的商品的此项优惠分摊
  ///otherTotal :其他的商品此项优惠分摊和
  void maxPartCurrentPreferential(GoodsDetailDto maxItem,
      PreferentialDto preferential, String detailKey, Decimal otherTotal) {
    var couponCurrentPreferential = MathUtil.subtraction(
            preferential.total.toString(), otherTotal.toString())
        .toStringAsFixed(SystemConfigTool.getDigital(BillDecimalType.TOTAL));

    _setPreferentialHelpTotal(
        maxItem, detailKey, num.parse(couponCurrentPreferential), preferential);
  }

  ///优惠分摊计算(储值赠金)
  List<GoodsDetailDto> giftTotalPreferentialShareCalculate(
      GoodsBillDto goodsBillDto,
      List<GoodsDetailDto> goodsDetailList,
      BillType billType,
      {num backSumGiveTotal = 0}) {
    if (goodsDetailList.isEmpty) {
      return goodsDetailList;
    }

    String currencyBillTotal = goodsBillDto.currencyBillTotal;

    ///销售换货单，需要将退回的金额和加上
    if (billType == BillType.SaleChangeBill) {
      currencyBillTotal = MathUtil.add(
              currencyBillTotal, goodsBillDto.sumPosInDetailTotal.toString())
          .toString();
    }
    PreferentialDto? billPreferential =
        goodsBillDto.preferentialHelp[Preferential.giftStore.name];

    return calGiftTotalShare(goodsDetailList, currencyBillTotal,
        null == billPreferential ? "0" : billPreferential.total.toString(),
        preferentialDto: billPreferential, backSumGiveTotal: backSumGiveTotal);
  }

  ///优惠分摊计算(抹零)
  List<GoodsDetailDto> eraseZeroCalculate(
      GoodsBillDto goodsBillDto, List<GoodsDetailDto> goodsDetailList) {
    if (goodsDetailList.isEmpty) {
      return goodsDetailList;
    }
    PreferentialDto billPreferential =
        goodsBillDto.preferentialHelp[PreferentialDtoType.eraseZeroBill]!;
    return _preferentialShareCalculate(
        goodsDetailList, billPreferential, PreferentialDtoType.eraseZeroBill);
  }

  ///设置商品各部分优惠分摊(促销、优惠券)
  ///detailKey 例：PreferentialDtoType.promotionBill
  void _setPreferentialHelpTotal(GoodsDetailDto goodsDetailDto,
      String detailKey, num total, PreferentialDto? billPreferential) {
    if (null == billPreferential) {
      return;
    }
    PreferentialDto detailPreferential = PreferentialDto();
    detailPreferential.total = total;
    detailPreferential.typeId = billPreferential.typeId;
    detailPreferential.type = billPreferential.type;
    goodsDetailDto.preferentialHelp[detailKey] = detailPreferential;
  }

  ///设置商品总优惠分摊
  void _setGoodsPreferentialTotal(GoodsDetailDto goodsDetailDto) {
    // reCalculate(
    //     goodsDetailDto, PtypePopValueChangeType.PREFERENTIAL_SHARE, "0");

    ///手工优惠分摊
    num manualPreferentialShare = goodsDetailDto
            .preferentialHelp[PreferentialDtoType.manualBill]?.total ??
        0;

    ///促销
    num promotionPreferentialShare = goodsDetailDto
            .preferentialHelp[PreferentialDtoType.promotionBill]?.total ??
        0;

    ///优惠券
    num couponPreferentialShare = goodsDetailDto
            .preferentialHelp[PreferentialDtoType.couponBill]?.total ??
        0;

    ///积分兑换
    num scorePreferentialShare =
        goodsDetailDto.preferentialHelp[PreferentialDtoType.scoreBill]?.total ??
            0;

    // ///储值
    // num storeGiftPreferentialShare = goodsDetailDto
    //         .preferentialHelp[PreferentialDtoType.storeGiftTotalBill]?.total ??
    //     0;

    ///抹零优惠
    num eraseZero = goodsDetailDto
            .preferentialHelp[PreferentialDtoType.eraseZeroBill]?.total ??
        0;

    ///总优惠
    num currencyPreferentialShare = manualPreferentialShare +
        promotionPreferentialShare +
        couponPreferentialShare +
        scorePreferentialShare +
        eraseZero;

    String preferentialShareString = DecimalDisplayHelper.getTotalFixed(
        currencyPreferentialShare.toString());
    reCalculate(goodsDetailDto, PtypePopValueChangeType.PREFERENTIAL_SHARE,
        preferentialShareString);
  }

//查找最大的折后含税金额（小计）
  Map<String, dynamic> _findMaxDisedTaxedTotalIndex(
      List<GoodsDetailDto> goodsDetailList,
      {required bool promotionOrder}) {
    //找到最大的优惠分摊行；
    int maxTotalIndex = 0;
    Decimal disedTotal = Decimal.fromInt(0);
    Decimal currentMaxTotal = Decimal.zero;
    int detailLength = goodsDetailList.length;

    for (int i = 0; i < detailLength; i++) {
      GoodsDetailDto currItem = goodsDetailList[i];
      if (currItem.comboRow) {
        continue;
      }
      if (currentMaxTotal <
          MathUtil.parseToDecimal(promotionOrder
              ? currItem.discountTotal.toString()
              : currItem.currencyDisedTaxedTotal.toString())) {
        maxTotalIndex = i;
        currentMaxTotal = MathUtil.parseToDecimal(promotionOrder
            ? currItem.discountTotal.toString()
            : currItem.currencyDisedTaxedTotal.toString());
      }
      //汇总总的折后含税金额 用于分摊到明细
      disedTotal += MathUtil.parseToDecimal(promotionOrder
          ? currItem.discountTotal.toString()
          : currItem.currencyDisedTaxedTotal.toString());
    }

    Map<String, dynamic> maxTotalAndIndexMap = {
      "maxIndex": maxTotalIndex,
      "total": disedTotal
    };
    return maxTotalAndIndexMap;
  }

  ///还原优惠分摊
  ///[promotionOrder] 是否更改的是订单满减金额
  ///如果是，则将商品的优惠分摊设置为0
  ///如果不是，则将商品的优惠分摊设置为原来优惠辅助map中记录的订单满减金额
  void preferentialTotalSplitCalculateBack(List<GoodsDetailDto> goodsDetailList,
      {required bool promotionOrder}) {
    for (int i = 0; i < goodsDetailList.length; i++) {
      GoodsDetailDto currItem = goodsDetailList[i];
      //如果是计算订单满减，则设置订单满减金额为0
      if (promotionOrder) {
        reCalculate(currItem, PtypePopValueChangeType.PREFERENTIAL_SHARE, "0");
      }
      //若不是计算订单满减，从商品的优惠辅助map中取出【订单满减】的金额
      else {
        String promotionBillTotal = currItem
                .preferentialHelp[PreferentialDtoType.promotionBill]?.total
                .toString() ??
            "0";
        reCalculate(currItem, PtypePopValueChangeType.PREFERENTIAL_SHARE,
            promotionBillTotal);
      }
    }
  }

//endregion

  ///重算商品价格
  void reCalculate(GoodsDetailDto goodsDetail, PtypePopValueChangeType type,
      String changeValue) {
    //todo 为什么所有计算都需要重新计算优惠分摊？？？
    //退货或者换货，需要根据原单商品剩余数量，和剩余优惠分摊重新来计算分摊
    if (StringUtil.isNotZeroOrEmpty(goodsDetail.sourceDetailId) &&
        StringUtil.isNotZeroOrEmpty(goodsDetail.remainUnitQty) &&
        StringUtil.isNotZeroOrEmpty(
            goodsDetail.remainCurrencyPreferentialShare) &&
        goodsDetail.unitId == goodsDetail.orgUnitId) {
      String unitQty = goodsDetail.unitQty.toString();
      if (type == PtypePopValueChangeType.Qty) {
        unitQty = changeValue;
      }
      //todo 如果是[type] = PREFERENTIAL_SHARE,则会触发两次 priceCompute.onPreferentialShareChange
      _orderAndBackCreateBillOnQtyChangeRefreshPreferentialTotal(
          goodsDetail, unitQty);
    }
    priceCompute.onValueChange(goodsDetail, type, changeValue);
  }

  ///重算套餐价格
  void recalculateCoboPrice(
      List<GoodsDetailDto> details, List<GoodsDetailDto> changedList,
      {bool isRecalculatePrice = false}) {
    for (GoodsDetailDto dto in changedList) {
      if (dto.comboRow != true && StringUtil.isZeroOrEmpty(dto.comboRowParId)) {
        return;
      }
      //修改套餐行重算套餐明细价格
      if (dto.comboRow) {
        recalculateComboDetail(details, dto, isRecalculatePrice);
      }
      //修改套餐明细重算套餐行价格
      else if (StringUtil.isNotZeroOrEmpty(dto.comboRowParId)) {
        recalculateComboRow(details, dto);
      }
    }
  }

  void recalculateComboDetail(List<GoodsDetailDto> details,
      GoodsDetailDto comboRowItem, bool isRecalculatePrice) {
    comboRowItem.currencyTaxTotal = 0;
    comboRowItem.currencyDisedTaxedPrice = 0;
    for (var comboDetail in details) {
      if (comboDetail.comboRowParId == comboRowItem.comboRowId) {
        if (comboDetail.gift == true) {
          comboDetail.currencyPrice = 0;
          comboDetail.discount = 1;
        }

        comboDetail.unitQty = num.parse(SystemConfigTool.doubleMultiple(
            comboRowItem.unitQty.toString(),
            comboDetail.comboQtyRate.toString(),
            BillDecimalType.QTY));
        if (isRecalculatePrice) {
          comboDetail.currencyPrice = num.parse(SystemConfigTool.doubleDivision(
              SystemConfigTool.doubleMultiple(
                  comboRowItem.currencyPrice.toString(),
                  (comboDetail.comboShareScale / 100).toString(),
                  BillDecimalType.PRICE),
              comboDetail.comboQtyRate.toString(),
              BillDecimalType.PRICE));
          comboDetail.discount = comboRowItem.discount;

          reCalculate(comboDetail, PtypePopValueChangeType.PRICE,
              comboDetail.currencyPrice.toString());
          reCalculate(comboDetail, PtypePopValueChangeType.DISCOUNT,
              comboDetail.discount.toString());
        } else {
          comboDetail.discount = comboRowItem.discount;

          reCalculate(comboDetail, PtypePopValueChangeType.DISCOUNT,
              comboDetail.discount.toString());

          reCalculate(comboDetail, PtypePopValueChangeType.Qty,
              comboDetail.unitQty.toString());
        }

        comboRowItem.currencyTaxTotal = num.parse(MathUtil.add(
                comboRowItem.currencyTaxTotal.toString(),
                comboDetail.currencyTaxTotal.toString())
            .toString());
        comboRowItem.currencyDisedTaxedPrice = num.parse(MathUtil.add(
                comboRowItem.currencyDisedTaxedPrice.toString(),
                comboDetail.currencyDisedTaxedPrice.toString())
            .toString());
      }
    }
  }

  ///重算套餐行金额
  void recalculateComboRow(
      List<GoodsDetailDto> details, GoodsDetailDto comboDetailItem) {
    GoodsDetailDto? cItem;
    for (var e in details) {
      if (e.comboRowId == comboDetailItem.comboRowParId) {
        cItem = e;
        break;
      }
    }
    if (cItem != null) {
      cItem.currencyPrice = 0;
      String currencyTaxTotal = "0";
      for (var e in details) {
        if (e.comboRowParId == cItem.comboRowId && e.comboRow != true) {
          if (e.ptypeId == comboDetailItem.ptypeId) {
            e.currencyPrice = comboDetailItem.currencyPrice;
            e.currencyTaxTotal = comboDetailItem.currencyTaxTotal;
          }
          cItem.currencyPrice = num.parse(MathUtil.add(
                  cItem.currencyPrice.toString(),
                  SystemConfigTool.doubleMultiple(e.currencyPrice.toString(),
                      e.comboQtyRate.toString(), BillDecimalType.PRICE))
              .toString());

          currencyTaxTotal =
              MathUtil.add(currencyTaxTotal, e.currencyTaxTotal.toString())
                  .toString();
        }
      }
      reCalculate(
          cItem, PtypePopValueChangeType.PRICE, cItem.currencyPrice.toString());
      cItem.currencyTaxTotal = num.parse(currencyTaxTotal);
    }
  }
}
