import '../../../bill/entity/preferential_dto.dart';
import '../../../bill/tool/bill_tool.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../common/tool/system_config_tool.dart';
import '../../../enum/bill_decimal_type.dart';
import '../../../enum/bill_ptype_pop_value_change_type.dart';
import '../../../enum/bill_type.dart';
import 'package:haloui/utils/math_util.dart';

import 'bill_price_compute_helper.dart';
import 'goods_tool.dart';

/// author : huang bo
/// time   : 2021/8/18 13:42
/// email  : <EMAIL>
/// desc   : 套餐价格计算相关

class BillComboPriceComputeHelper {
  BillType billType = BillType.SaleBill;

  late BillPriceComputeHelper billPriceComputeHelper;

  BillComboPriceComputeHelper(this.billType) {
    billPriceComputeHelper = BillPriceComputeHelper();
  }

  ///计算套餐最后一次退货值
  void recalculateMax(List<GoodsDetailDto> goodsList,
      Map<String, List<GoodsDetailDto>> comboDetailsMap) {
    List<GoodsDetailDto> itemList =
        goodsList.where((element) => element.comboRow == true).toList();

    for (GoodsDetailDto goods in itemList) {
      if (!goods.comboRow) return;
      List<GoodsDetailDto> cDetails =
          GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap);
      // goods.goodsDetailSaleBackHelper.qtyPreferentialShare = 0;
      // goods.goodsDetailSaleBackHelper
      //     .lastCurrencyOrderPreferentialAllotTotal = 0;
      // goods.goodsDetailSaleBackHelper.lastCurrencyDisedTaxedTotal = 0;
      // goods.goodsDetailSaleBackHelper.lastCurrencyDisedTotal = 0;
      // goods.goodsDetailSaleBackHelper.lastCurrencyPreferentialTotal = 0;
      // goods.goodsDetailSaleBackHelper.lastCurrencyTaxTotal = 0;
      // goods.goodsDetailSaleBackHelper.lastCurrencyTotal = 0;

      for (var comboDetail in cDetails) {
        BillTool.calcSaleBackBillInfo(comboDetail);
      }
    }
  }

  ///退货单套餐、剔除优惠分摊计算
  void saleBackClearPreferentialShare(List<GoodsDetailDto> details) {
    if (billType == BillType.SaleBackBill) {
      ///剔除优惠分摊计算
      List<GoodsDetailDto> itemList =
          details.where((element) => element.comboRow == true).toList();
      for (GoodsDetailDto itemDto in itemList) {
        List<GoodsDetailDto> cDetails = details
            .where((e) => itemDto.comboRowId == e.comboRowParId)
            .toList();

        ///普通套餐明细
        for (var comboDetail in cDetails) {
          BillTool.saleBackClearPreferentialShare(comboDetail, billType);
          billPriceComputeHelper.reCalculate(comboDetail,
              PtypePopValueChangeType.Qty, comboDetail.unitQty.toString());
        }
        BillTool.saleBackClearPreferentialShare(itemDto, billType);
      }
    }
  }

  void saleBackGoodsDetailPreferentialShare(List<GoodsDetailDto> details) {
    if (billType == BillType.SaleBackBill) {
      List<GoodsDetailDto> itemList =
          details.where((element) => element.comboRow == true).toList();

      for (GoodsDetailDto itemDto in itemList) {
        num currencyPreferentialShare = 0;
        num currencyTaxTotal = 0;
        List<GoodsDetailDto> cDetails = details
            .where((e) => itemDto.comboRowId == e.comboRowParId)
            .toList();

        ///普通套餐明细
        for (var comboDetail in cDetails) {
          billPriceComputeHelper.reCalculate(comboDetail,
              PtypePopValueChangeType.Qty, comboDetail.unitQty.toString());
          if (billType == BillType.SaleBackBill) {
            BillTool.saleBackGoodsDetailPreferentialShare(comboDetail);
          }
          billPriceComputeHelper.reCalculate(
              comboDetail,
              PtypePopValueChangeType.PREFERENTIAL_SHARE,
              comboDetail.currencyOrderPreferentialAllotTotal.toString());

          currencyPreferentialShare = currencyPreferentialShare +
              comboDetail.currencyOrderPreferentialAllotTotal;
          currencyTaxTotal = currencyTaxTotal + comboDetail.currencyTaxTotal;
        }
        if (itemDto.unitQty != 0) {
          billPriceComputeHelper.reCalculate(
              itemDto,
              PtypePopValueChangeType.PREFERENTIAL_SHARE,
              DecimalDisplayHelper.getTotalFixed(
                  currencyPreferentialShare.toString()));
          itemDto.currencyTaxTotal = double.parse(
              DecimalDisplayHelper.getTotalFixed(currencyTaxTotal.toString()));
        }
      }
    }
  }

  ///套餐计算
  void recalculateCombo(List<GoodsDetailDto> details) {
    //将套餐行和套餐明细行的优惠分摊都赋值0
    saleBackClearPreferentialShare(details);
    recalculateComboDetailByItem(details);
    saleBackGoodsDetailPreferentialShare(details);
  }

  ///套餐行数量、赠品改变，计算套餐明细数据并反算套餐行数据
  void recalculateComboDetailByItem(List<GoodsDetailDto> details) {
    List<GoodsDetailDto> itemList =
        details.where((element) => element.comboRow == true).toList();

    for (GoodsDetailDto itemDto in itemList) {
      billPriceComputeHelper.reCalculate(
          itemDto, PtypePopValueChangeType.Qty, itemDto.unitQty.toString());
      itemDto.currencyOrderPreferentialAllotTotal = 0;
      num currencyTotal = 0;
      num currencyDisedTaxedTotal = 0;
      num posDisedTaxedTotal = 0;
      num currencyTaxTotal = 0;
      num currencyPtypePreferentialTotal = 0;

      List<GoodsDetailDto> cDetails =
          details.where((e) => itemDto.comboRowId == e.comboRowParId).toList();

      Map<String, num> batchTotalQtyRateMap =
          _getComboDetailBatchTotalMap(itemDto, cDetails);

      //找到套餐中的赠品
      List<GoodsDetailDto> gifts = cDetails
          .where((element) => element.gift || element.comboShareScale == 0)
          .toList();
      //移除赠品
      cDetails.removeWhere(
          (element) => element.gift || element.comboShareScale == 0);

      ///赠品套餐明细
      for (var giftDetail in gifts) {
        giftDetail.unitQty = num.parse(SystemConfigTool.doubleMultiple(
            itemDto.unitQty.toString(),
            giftDetail.comboQtyRate.toString(),
            BillDecimalType.QTY));

        giftDetail.discount = itemDto.discount;

        billPriceComputeHelper.reCalculate(giftDetail,
            PtypePopValueChangeType.DISCOUNT, giftDetail.discount.toString());
      }

      for (var comboDetail in cDetails) {
        comboDetail.calIntegral = itemDto.calIntegral;
        comboDetail.joinOrder = itemDto.joinOrder;

        comboDetail.unitQty = num.parse(SystemConfigTool.doubleMultiple(
            itemDto.unitQty.toString(),
            comboDetail.comboQtyRate.toString(),
            BillDecimalType.QTY));

        num comboQtyRate = comboDetail.comboQtyRate;

        if (comboDetail.batchenabled &&
            batchTotalQtyRateMap.containsKey(comboDetail.comboRowId) &&
            comboDetail.comboQtyRate !=
                batchTotalQtyRateMap[comboDetail.comboRowId]!) {
          comboQtyRate = batchTotalQtyRateMap[comboDetail.comboRowId]!;
        }

        if (billType != BillType.SaleBackBill) {
          //套餐行价格 * 分摊比例 / 总数量Rate，同一套餐明细，不同批次需要除总数量Rate
          comboDetail.currencyPrice = num.parse(SystemConfigTool.doubleDivision(
              SystemConfigTool.doubleMultiple(
                  itemDto.currencyPrice.toString(),
                  (comboDetail.comboShareScale / 100).toString(),
                  BillDecimalType.PRICE),
              comboQtyRate.toString(),
              BillDecimalType.PRICE));
        }

        billPriceComputeHelper.reCalculate(
            comboDetail,
            PtypePopValueChangeType.PRICE,
            comboDetail.currencyPrice.toString());

        comboDetail.discount = itemDto.discount;

        billPriceComputeHelper.reCalculate(comboDetail,
            PtypePopValueChangeType.DISCOUNT, comboDetail.discount.toString());

        if (comboDetail != cDetails.last) {
          currencyTotal = num.parse(MathUtil.add(currencyTotal.toString(),
                  comboDetail.currencyTotal.toString())
              .toString());
          currencyDisedTaxedTotal = num.parse(MathUtil.add(
                  currencyDisedTaxedTotal.toString(),
                  comboDetail.currencyDisedTaxedTotal.toString())
              .toString());
          posDisedTaxedTotal = num.parse(MathUtil.add(
                  posDisedTaxedTotal.toString(),
                  comboDetail.discountTotal.toString())
              .toString());
        } else {
          billPriceComputeHelper.reCalculate(
              comboDetail,
              PtypePopValueChangeType.TOTAL,
              DecimalDisplayHelper.getTotalFixed(
                  (itemDto.currencyTotal - currencyTotal).toString()));
          billPriceComputeHelper.reCalculate(
              comboDetail,
              PtypePopValueChangeType.DISCOUNT_TOTAL,
              DecimalDisplayHelper.getTotalFixed(
                  (itemDto.discountTotal - posDisedTaxedTotal).toString()));
          billPriceComputeHelper.reCalculate(
              comboDetail,
              PtypePopValueChangeType.DISCOUNT_TAXED_TOTAL,
              DecimalDisplayHelper.getTotalFixed(
                  (itemDto.currencyDisedTaxedTotal - currencyDisedTaxedTotal)
                      .toString()));
        }
        currencyTaxTotal = num.parse(MathUtil.add(currencyTaxTotal.toString(),
                comboDetail.currencyTaxTotal.toString())
            .toString());
      }

      itemDto.currencyTaxTotal = currencyTaxTotal;
    }
  }

  ///套餐计算优惠分摊
  ///因为操作特殊性，可以对明细优惠分摊做总和，然后对套餐行做加减
  ///套餐明细比例只在请求下来或者编辑明细才使用这个方法计算：
  ///recalculateComboDetailByItem
  void recalculateComboPreferentialShare(List<GoodsDetailDto> details) {
    ///选出所有套餐行
    List<GoodsDetailDto> itemList =
        details.where((element) => element.comboRow == true).toList();
    for (GoodsDetailDto itemDto in itemList) {
      num currencyPreferentialShare = 0;
      num currencyTaxTotal = 0;

      num promotionBill = 0;
      num manualBill = 0;
      num couponBill = 0;
      num scoreBill = 0;
      num eraseZero = 0;

      List<GoodsDetailDto> cDetails =
          details.where((e) => itemDto.comboRowId == e.comboRowParId).toList();

      ///普通套餐明细
      for (var comboDetail in cDetails) {
        billPriceComputeHelper.reCalculate(
            comboDetail,
            PtypePopValueChangeType.PREFERENTIAL_SHARE,
            comboDetail.currencyOrderPreferentialAllotTotal.toString());

        currencyPreferentialShare = currencyPreferentialShare +
            comboDetail.currencyOrderPreferentialAllotTotal;
        currencyTaxTotal = currencyTaxTotal + comboDetail.currencyTaxTotal;

        promotionBill = promotionBill +
            (comboDetail.preferentialHelp[PreferentialDtoType.promotionBill]
                    ?.total ??
                0);
        manualBill = manualBill +
            (comboDetail
                    .preferentialHelp[PreferentialDtoType.manualBill]?.total ??
                0);
        couponBill = couponBill +
            (comboDetail
                    .preferentialHelp[PreferentialDtoType.couponBill]?.total ??
                0);
        scoreBill = scoreBill +
            (comboDetail
                    .preferentialHelp[PreferentialDtoType.scoreBill]?.total ??
                0);
        eraseZero += (comboDetail
                .preferentialHelp[PreferentialDtoType.eraseZeroBill]?.total ??
            0);
      }
      billPriceComputeHelper.reCalculate(
          itemDto,
          PtypePopValueChangeType.PREFERENTIAL_SHARE,
          DecimalDisplayHelper.getTotalFixed(
              currencyPreferentialShare.toString()));

      billPriceComputeHelper.calGiftTotalShare(
          cDetails,
          itemDto.currencyDisedTaxedTotal.toString(),
          itemDto.givePreferentialTotal.toString(),
          isRemoveComboDetail: false);

      itemDto.currencyTaxTotal = double.parse(
          DecimalDisplayHelper.getTotalFixed(currencyTaxTotal.toString()));

      itemDto.preferentialHelp[PreferentialDtoType.manualBill] =
          PreferentialDto()..total = manualBill;
      itemDto.preferentialHelp[PreferentialDtoType.promotionBill] =
          PreferentialDto()..total = promotionBill;
      itemDto.preferentialHelp[PreferentialDtoType.scoreBill] =
          PreferentialDto()..total = scoreBill;
      itemDto.preferentialHelp[PreferentialDtoType.couponBill] =
          PreferentialDto()..total = couponBill;
      itemDto.preferentialHelp[PreferentialDtoType.eraseZeroBill] =
          PreferentialDto()..total = eraseZero;
      itemDto.preferentialHelp[Preferential.giftStore.name] = PreferentialDto()
        ..total = itemDto.givePreferentialTotal;

      for (var comboDetail in cDetails) {
        comboDetail.lastDiscount = itemDto.lastDiscount;
      }
    }
  }

  ///多批次套餐明细，统计明细分摊比例，用于计算同个套餐明细不同批次价格（套餐行价格 * 分摊比例 / 总数量Rate）
  ///todo 这里同一个套餐明细行目前不可能存在两个批次，所以这个方法没用
  Map<String, num> _getComboDetailBatchTotalMap(
      GoodsDetailDto itemDto, List<GoodsDetailDto> cDetails) {
    Map<String, num> detailTotalQtyRateMap = {};
    for (GoodsDetailDto dto in cDetails) {
      if (dto.batchenabled == true) {
        if (!detailTotalQtyRateMap.containsKey(dto.comboDetailId)) {
          detailTotalQtyRateMap[dto.comboDetailId ?? ""] = 0;
        }
        detailTotalQtyRateMap[dto.comboDetailId ?? ""] = MathUtil.addDec(
                detailTotalQtyRateMap[dto.comboDetailId] ?? 0, dto.comboQtyRate)
            .toDouble();
      }
    }
    return detailTotalQtyRateMap;
  }
}
