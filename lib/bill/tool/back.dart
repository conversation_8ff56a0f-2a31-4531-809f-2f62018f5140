import 'package:decimal/decimal.dart';
import 'package:haloui/utils/math_util.dart';

import '../../common/tool/system_config_tool.dart';
import '../../enum/bill_decimal_type.dart';
import '../entity/goods_detail_dto.dart';
import 'promotion/price.dart';

class BackBillUtil {
  ///退货单赠金优惠分摊计算
  static void onQtyChange(GoodsDetailDto goods,
      {List<GoodsDetailDto> comboDetails = const []}) {
    //仅处理退货单
    //退货数量为0时不处理
    // if (goods.unitQty == 0 || goods.originalMaxQty == 0) {
    //   return;
    // }
    //计算总金额、折后金额、折后单价
    GoodsQtyUtil.onQtyChange(goods, goods.unitQty);

    ///单个商品明细行全部退完(refundQty为基础单位，换算为辅助单位计算)
    if (MathUtil.addDec(goods.unitQty, goods.getRefundUnitQty()).toDouble() ==
        goods.originalMaxQty) {
      //一次全部退完，直接使用原单数据即可
      if (goods.refundQty == 0 && goods.saleBackHelper.originalGoods != null) {
        resetToOriginal(goods);
        if (goods.comboRow) {
          num taxTotal = 0;
          for (var comboDetail in comboDetails) {
            resetToOriginal(comboDetail);
            taxTotal = MathUtil.addDec(taxTotal, comboDetail.currencyTaxTotal)
                .toDouble();
          }
          goods.currencyTaxTotal = taxTotal;
        }
        return;
      } else {
        ///单条商品行退完
        //剩余小计
        goods.currencyDisedTaxedTotal =
            goods.saleBackHelper.remainCurrencyDisedTaxedTotal();
        //剩余赠金优惠
        goods.givePreferentialTotal =
            goods.saleBackHelper.remainGivePreferentialTotal();
        goods.currencyGivePreferentialTotal =
            goods.saleBackHelper.remainGivePreferentialTotal();
        //剩余单品优惠
        goods.currencyPtypePreferentialTotal =
            goods.saleBackHelper.remainPtypePreferentialTotal();
      }
    } else {
      ///退货比例 = 退货数量/原单数量
      num refundRate =
          MathUtil.divideDec(goods.unitQty, goods.originalMaxQty).toDouble();

      //按比例算出小计、赠金优惠、单品优惠
      goods.currencyDisedTaxedTotal = SystemConfigTool.doubleMultipleToDecimal(
          refundRate,
          goods.saleBackHelper.originalCurrencyDisedTaxedTotal,
          BillDecimalType.TOTAL);

      goods.givePreferentialTotal = SystemConfigTool.doubleMultipleToDecimal(
          refundRate,
          goods.saleBackHelper.originalGivePreferentialTotal,
          BillDecimalType.TOTAL);
      goods.currencyGivePreferentialTotal = goods.givePreferentialTotal;

      goods.currencyPtypePreferentialTotal =
          SystemConfigTool.doubleMultipleToDecimal(
              refundRate,
              goods.saleBackHelper.originalPtypePreferentialTotal,
              BillDecimalType.TOTAL);
    }
    //最终单价
    goods.currencyDisedTaxedPrice = SystemConfigTool.doubleDivisionToDecimal(
        goods.currencyDisedTaxedTotal, goods.unitQty, BillDecimalType.PRICE);
    //最终优惠 = 总金额 - 最终金额
    goods.currencyPreferentialTotal =
        SystemConfigTool.doubleSubtractionToDecimal(goods.currencyTotal,
            goods.currencyDisedTaxedTotal, BillDecimalType.TOTAL);
    //最终折扣
    goods.lastDiscount = SystemConfigTool.doubleDivisionToDecimal(
        goods.currencyDisedTaxedTotal,
        goods.currencyTotal,
        BillDecimalType.DISCOUNT);
    //含赠金的最终金额 = 最终金额 + 赠金
    goods.posCurrencyDisedTaxedTotal = SystemConfigTool.doubleAddToDecimal(
        goods.currencyDisedTaxedTotal,
        goods.givePreferentialTotal,
        BillDecimalType.TOTAL);
    //含赠金的最终单价
    goods.posCurrencyDisedTaxedPrice = SystemConfigTool.doubleDivisionToDecimal(
        goods.posCurrencyDisedTaxedTotal, goods.unitQty, BillDecimalType.PRICE);
    //促销后金额 = 折后金额 - 单品优惠
    goods.promotedTotal = SystemConfigTool.doubleSubtractionToDecimal(
        goods.discountTotal,
        goods.currencyPtypePreferentialTotal,
        BillDecimalType.TOTAL);
    //促销后单价
    goods.promotedPrice = SystemConfigTool.doubleDivisionToDecimal(
        goods.promotedTotal, goods.unitQty, BillDecimalType.PRICE);
    //分摊前优惠 = 金额 - 促销后金额
    goods.preferentialDiscount = SystemConfigTool.doubleSubtractionToDecimal(
        goods.currencyTotal, goods.promotedTotal, BillDecimalType.TOTAL);
    //优惠分摊 = 促销后金额 - 含赠金的最终金额
    goods.currencyOrderPreferentialAllotTotal =
        SystemConfigTool.doubleSubtractionToDecimal(goods.promotedTotal,
            goods.posCurrencyDisedTaxedTotal, BillDecimalType.TOTAL);
    TaxUtil.calculateDisedPriceAndTotal(goods);
    //处理套餐明细
    if (goods.comboRow) {
      handleComboDetails(goods, comboDetails);
    }
  }

  ///对于之前没有退过货，然后一次全部退完的商品，重置为原单数据
  static void resetToOriginal(GoodsDetailDto goods) {
    GoodsDetailDto originalGoods = goods.saleBackHelper.originalGoods!;
    if (!goods.saleBackHelper.isCouponGift) {
      goods.unitQty = originalGoods.unitQty;
    }
    //单价、金额、折扣
    goods.currencyPrice = originalGoods.currencyPrice;
    goods.currencyTotal = originalGoods.currencyTotal;
    goods.discount = originalGoods.discount;
    //最终金额、最终单价、最终优惠、最终折扣、赠金、优惠分摊
    goods.currencyDisedTaxedTotal = originalGoods.currencyDisedTaxedTotal;
    goods.currencyDisedTaxedPrice = originalGoods.currencyDisedTaxedPrice;
    goods.currencyPreferentialTotal = originalGoods.currencyPreferentialTotal;
    goods.lastDiscount = originalGoods.lastDiscount;
    goods.givePreferentialTotal = originalGoods.givePreferentialTotal;
    goods.currencyGivePreferentialTotal =
        originalGoods.currencyGivePreferentialTotal;
    goods.currencyOrderPreferentialAllotTotal =
        originalGoods.currencyOrderPreferentialAllotTotal;
    //最终金额(含赠金)、最终单价（含赠金）
    goods.posCurrencyDisedTaxedTotal = SystemConfigTool.doubleAddToDecimal(
        goods.currencyDisedTaxedTotal,
        goods.givePreferentialTotal.abs(),
        BillDecimalType.TOTAL);
    goods.posCurrencyDisedTaxedPrice = SystemConfigTool.doubleDivisionToDecimal(
        goods.posCurrencyDisedTaxedTotal, goods.unitQty, BillDecimalType.PRICE);
    //不含税金额、不含税单价、税额
    goods.currencyDisedTotal = originalGoods.currencyDisedTotal;
    goods.currencyDisedPrice = originalGoods.currencyDisedPrice;
    goods.currencyTaxTotal = originalGoods.currencyTaxTotal;
    //计算分摊前优惠 = 最终优惠 - 优惠分摊 - 赠金
    goods.preferentialDiscount =
        (Decimal.parse(goods.currencyPreferentialTotal.toString()) -
                Decimal.parse(
                    goods.currencyOrderPreferentialAllotTotal.toString()) -
                Decimal.parse(goods.givePreferentialTotal.abs().toString()))
            .toDouble();
    //促销后金额 = 总金额 - 优惠分摊前优惠金额
    goods.promotedTotal = SystemConfigTool.doubleSubtractionToDecimal(
        goods.currencyTotal, goods.preferentialDiscount, BillDecimalType.TOTAL);
    goods.promotedPrice = SystemConfigTool.doubleDivisionToDecimal(
        goods.promotedTotal, goods.unitQty, BillDecimalType.PRICE);
    //单品优惠
    goods.currencyPtypePreferentialTotal =
        originalGoods.currencyPtypePreferentialTotal;
    //折后金额 = 促销后金额 + 单品优惠
    goods.discountTotal = SystemConfigTool.doubleAddToDecimal(
        goods.promotedTotal,
        goods.currencyPtypePreferentialTotal,
        BillDecimalType.TOTAL);
    goods.discountPrice = SystemConfigTool.doubleDivisionToDecimal(
        goods.discountTotal, goods.unitQty, BillDecimalType.PRICE);
  }

  static void handleComboDetails(
      GoodsDetailDto combo, List<GoodsDetailDto> comboDetails) {
    comboDetails = comboDetails.toList()
      ..sort((a, b) => a.comboShareScale.compareTo(b.comboShareScale));
    num taxTotal = 0;
    //明细行金额之和
    Decimal totalSum = Decimal.zero;
    //折后金额之和
    Decimal discountTotalSum = Decimal.zero;
    //促销后金额之和
    Decimal promotedTotalSum = Decimal.zero;
    //明细行含赠金金额之和
    Decimal giftMoneyTotal = Decimal.zero;
    //含赠金的最终金额之和
    Decimal posCurrencyDisedTaxedTotalSum = Decimal.zero;
    for (int i = 0; i < comboDetails.length; i++) {
      GoodsDetailDto comboDetail = comboDetails[i];
      num unitQty = SystemConfigTool.doubleMultipleToDecimal(
          combo.unitQty, comboDetail.comboQtyRate, BillDecimalType.QTY);
      comboDetail.unitQty = unitQty;
      //非套餐中最后一个商品，直接按 套餐金额x比例 换算
      if (i < comboDetails.length - 1) {
        num scale =
            MathUtil.divideDec(comboDetail.comboShareScale, 100).toDouble();
        //明细行金额和单价
        GoodsTotalUtil.onCurrencyTotalChange(
            comboDetail,
            SystemConfigTool.doubleMultipleToDecimal(
                combo.currencyTotal, scale, BillDecimalType.TOTAL),
            calculateDiscount: false);
        totalSum += Decimal.parse(comboDetail.currencyTotal.toString());
        //明细行折后金额、折扣和折后单价
        GoodsTotalUtil.onDiscountTotalChange(
            comboDetail,
            SystemConfigTool.doubleMultipleToDecimal(
                combo.discountTotal, scale, BillDecimalType.TOTAL));
        discountTotalSum += Decimal.parse(comboDetail.discountTotal.toString());
        //促销后金额、促销后单价
        GoodsTotalUtil.onPromotedTotalChange(
            comboDetail,
            SystemConfigTool.doubleMultipleToDecimal(
                combo.promotedTotal, scale, BillDecimalType.TOTAL));
        promotedTotalSum += Decimal.parse(comboDetail.promotedTotal.toString());
        //赠金
        comboDetail.givePreferentialTotal =
            SystemConfigTool.doubleMultipleToDecimal(
                combo.givePreferentialTotal, scale, BillDecimalType.TOTAL);
        giftMoneyTotal +=
            Decimal.parse(comboDetail.givePreferentialTotal.toString());
        //最终金额、小计
        GoodsTotalUtil.onFinalTotalChange(
            comboDetail,
            SystemConfigTool.doubleMultipleToDecimal(
                combo.posCurrencyDisedTaxedTotal,
                scale,
                BillDecimalType.TOTAL));
        posCurrencyDisedTaxedTotalSum +=
            Decimal.parse(comboDetail.posCurrencyDisedTaxedTotal.toString());
      } else {
        //最后一行，由总金额-之前的金额累计
        //金额
        GoodsTotalUtil.onCurrencyTotalChange(
            comboDetail,
            SystemConfigTool.doubleSubtractionToDecimal(combo.currencyTotal,
                totalSum.toDouble(), BillDecimalType.TOTAL),
            calculateDiscount: false);
        //折后金额
        GoodsTotalUtil.onDiscountTotalChange(
            comboDetail,
            SystemConfigTool.doubleSubtractionToDecimal(combo.discountTotal,
                discountTotalSum.toDouble(), BillDecimalType.TOTAL));
        //促销后金额
        GoodsTotalUtil.onPromotedTotalChange(
            comboDetail,
            SystemConfigTool.doubleSubtractionToDecimal(combo.promotedTotal,
                promotedTotalSum.toDouble(), BillDecimalType.TOTAL));
        //赠金
        comboDetail.givePreferentialTotal =
            SystemConfigTool.doubleSubtractionToDecimal(
                combo.givePreferentialTotal,
                giftMoneyTotal.toDouble(),
                BillDecimalType.TOTAL);
        comboDetail.currencyGivePreferentialTotal =
            comboDetail.givePreferentialTotal;
        //最终金额(含赠金)
        GoodsTotalUtil.onFinalTotalChange(
            comboDetail,
            SystemConfigTool.doubleSubtractionToDecimal(
                combo.posCurrencyDisedTaxedTotal,
                posCurrencyDisedTaxedTotalSum.toDouble(),
                BillDecimalType.TOTAL));
      }
      taxTotal =
          MathUtil.addDec(taxTotal, comboDetail.currencyTaxTotal).toDouble();
    }
    combo.currencyTaxTotal = taxTotal;
  }
}
