import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:halo_pos/common/tool/system_config_tool.dart';
import 'package:halo_pos/enum/bill_decimal_type.dart';

import 'package:halo_pos/common/tool/system_config_tool.dart';
import 'package:halo_pos/enum/bill_decimal_type.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/tool/bill_price_compute_helper.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_ptype_pop_value_change_type.dart';
import '../../../plugin/scale_plugin.dart';
import '../../../settting/entity/setting_dto.dart';
import '../../plugin/third_party_scale_plugin.dart';

/// 创建时间：2023/9/11
/// 作者：xiaotiaochong
/// 描述：

class ScaleTool {
  static String weight = "scale_weight";
  static String connection = "scale_connection";
  static String state = "scale_state";
  static String scaleTag = "scale_tag";

  static int timerCount = 0;
  static Timer? timer;

  static void calculateUnityQty(GoodsDetailDto goodsDetailDto) {
    SettingDto settingDto = SpTool.getSetting();
    if (goodsDetailDto.weight == null ||
        goodsDetailDto.weight == 0 ||
        !settingDto.openFreshWight) {
      return;
    }
    double getWeight = 0;
    if (SpTool.getSetting().innerScale) {
      if (ScalePlugin.instance.weight.toDouble() <= 0) {
        return;
      } else {
        getWeight = ScalePlugin.instance.weight.toDouble();
      }
    } else {
      if (ThirdPartyScalePlugin.instance.weight.toDouble() <= 0) {
        return;
      } else {
        getWeight = ThirdPartyScalePlugin.instance.weight.toDouble();
      }
    }
    //单位：0是g；1是kg
    int weightUnity = goodsDetailDto.weightUnit ?? 0;
    //按照 斤/500g 计算
    num weight = goodsDetailDto.weight ?? 0;
    //如果单位是千克，换算成g
    if (weightUnity == 1) {
      weight = weight * 1000;
    }
    weight =
        MathUtil.multiplyDec(weight, goodsDetailDto.unitRate ?? 1).toDouble();
    String unitQty = SystemConfigTool.doubleDivisionToDecimal(
            getWeight, weight, BillDecimalType.QTY)
        .toString();
    BillPriceComputeHelper()
        .priceCompute
        .onValueChange(goodsDetailDto, PtypePopValueChangeType.Qty, unitQty);
  }

  //电子秤
  static void addScale(BuildContext context, ScaleController scaleController) {
    if (SpTool.getSetting().innerScale) {
      ScalePlugin.instance.init();
    } else {
      if (SpTool.getSetting().thirdPartyScale != null) {
        ThirdPartyScalePlugin.instance.connectDevice(
            context,
            SpTool.getSetting().thirdPartyScale ??
                const SimpleUsbDevice(0, 0, "", ""));
        timer ??= Timer.periodic(const Duration(seconds: 1), (Timer value) {
          timerCount += 1;
          if (timerCount >= 2) {
            scaleController.changeWeight(0, 0, false);
            scaleController.onConnection(false);
          }
        });
      }
    }
  }

  static void removeScale() {
    if (SpTool.getSetting().innerScale) {
      ScalePlugin.instance.dispose();
    } else {
      if (SpTool.getSetting().thirdPartyScale != null) {
        ThirdPartyScalePlugin.instance.disconnectAllDevice();
      }
      timer?.cancel();
    }
  }

  static void onConnection(ScaleController scaleController) {
    if (SpTool.getSetting().innerScale) {
      ScalePlugin.instance.onConnectionChange = (value) {
        scaleController.onConnection(ScalePlugin.instance.isConnected);
      };
    }
  }

  static void onWeight(ScaleController scaleController) {
    if (SpTool.getSetting().innerScale) {
      ScalePlugin.instance.onWeightChange =
          ({required int weight, required int tare, required bool isStable}) {
        double weight = ScalePlugin.instance.weight.toDouble() / 1000;
        double tare = ScalePlugin.instance.tare.toDouble() / 1000;
        bool isStable = ScalePlugin.instance.isStable;
        scaleController.changeWeight(weight, tare, isStable);
      };
    } else {
      ThirdPartyScalePlugin.instance.onReceiveStableWeight = (value) {
        var weight = value.toDouble() / 1000;
        scaleController.changeWeight(weight, 0, true);
        scaleController.onConnection(true);
        timerCount = 0;
      };
    }
  }

  static onState(ScaleController scaleController) {
    if (SpTool.getSetting().innerScale) {
      ScalePlugin.instance.onStatusChange = (
          {required bool isLightWeight,
          required bool overload,
          required bool clearZeroErr,
          required bool calibrationErr}) {
        scaleController.changeState();
      };
    }
  }
}

class ScaleController extends GetxController {
  ///是否连接电子秤服务
  var isConnected = false.obs;

  ///净重 g，不包含皮重
  var weight = 0.000.obs;

  ///皮重 g
  var tare = 0.000.obs;

  ///是否稳定
  var isStable = false.obs;

  ///秤是否过轻(小于20E)
  var isLightWeight = false.obs;

  ///秤是否过载
  var overload = false.obs;

  ///秤是否清零错误
  var clearZeroErr = false.obs;

  ///秤是否标定错误
  var calibrationErr = false.obs;

  void changeWeight(double weight, double tare, bool isStable) {
    ///单位转换为kg
    this.weight = RxDouble(weight);
    this.tare = RxDouble(tare);
    this.isStable = RxBool(isStable);
    update([ScaleTool.weight]);
    update([ScaleTool.state]);
  }

  void changeState() {
    isLightWeight = RxBool(ScalePlugin.instance.isLightWeight);
    overload = RxBool(ScalePlugin.instance.overload);
    clearZeroErr = RxBool(ScalePlugin.instance.clearZeroErr);
    calibrationErr = RxBool(ScalePlugin.instance.calibrationErr);
    update([ScaleTool.state]);
  }

  void onConnection(bool isConnected) {
    this.isConnected = RxBool(isConnected);
    update([ScaleTool.connection]);
  }
}
