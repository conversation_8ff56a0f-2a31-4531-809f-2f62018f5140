import 'dart:math';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/system_config_dto.dart';

/// Copyright (C), 2019-2020, wsgjp.com
/// FileName: decimal_display_helper.dart
/// Author: lidingwen
/// Date: 2020/5/14 7:24 PM
/// Description： 有小数位控制的金额 字段显示 辅助类

class DecimalDisplayHelper {
  SystemConfigDto configDto = SpTool.getSystemConfig();

  static DecimalDisplayHelper get instance => _instance;
  static final DecimalDisplayHelper _instance =
      DecimalDisplayHelper._internal();

  DecimalDisplayHelper._internal();

  static String getPriceFixed(String value) {
    return removeTrailingZero(MathUtil.parseToDecimal(value)
        .toStringAsFixed(_instance.configDto.sysDigitalPrice));
  }

  // fix 当sku商品选择时,数量为0,显示的价格为 单价*折扣 而没使用配置的小数位数进行取舍
  static String getDisplayPriceFixed(String value) {
    return removeTrailingZero(MathUtil.parseToDecimal(value)
        .toStringAsFixed(_instance.configDto.sysDigitalPrice));
  }

  // fix 整单折扣调阅或首次赋值时,未按系统配置-折扣小数位数来进行显示
  static String getDisplayBillDiscountFixed(String value) {
    return removeTrailingZero(MathUtil.parseToDecimal(value)
        .toStringAsFixed(_instance.configDto.sysDigitalDiscount));
  }

  static String getDiscountFixed(String value) {
    return removeTrailingZero(
        formatDecimal(value, _instance.configDto.sysDigitalDiscount));
  }

  static String getQtyFixed(String value) {
    return removeTrailingZero(
        formatDecimal(value, _instance.configDto.sysDigitalQty));
  }

  static String getTotalFixed(String value) {
    if (StringUtil.isEmpty(value)) {
      return "0";
    }
    return removeTrailingZero(
        formatDecimal(value, _instance.configDto.sysDigitalTotal));
  }

  static String getCustomFixed(String value, int fractionDigits) {
    return removeTrailingZero(formatDecimal(value, fractionDigits));
  }

  static String removeTrailingZero(String string) {
    if (!string.contains('.')) {
      return string;
    }
    string = string.replaceAll(RegExp(r"0+?$"), ""); //去掉多余的0
    string = string.replaceAll(RegExp(r"[.]$"), ""); //如最后一位是.则去掉
    return string ?? "";
  }

  static String getNormalFixed(String value) {
    return removeTrailingZero(MathUtil.parseToDecimal(value).toString());
  }

  static String formatDecimal(String numString, int position) {
    num value = 0;

    ///处理特殊计算postion小于的情况
    if (position < 0) {
      position = 0;
    }
    if (numString.isNotEmpty) {
      value = num.parse(numString);
    }
    if (SpTool.getSetting().enableRounding) {
      switch (SpTool.getSetting().roundingType) {
        case 0:
          {
            //四舍五入
            return value.toStringAsFixed(position);
          }
        case 1:
          {
            //只舍不入
            num tempInt = (value * pow(10, position)).ceil();
            return (tempInt / pow(10, position).toDouble())
                .toStringAsFixed(position);
          }
        case 2:
          {
            //只入不舍
            num tempInt = (value * pow(10, position)).floor();
            return (tempInt / pow(10, position).toDouble())
                .toStringAsFixed(position);
          }
        default:
          return value.toStringAsFixed(position);
      }
    } else {
      //未开启抹零，四舍五入
      return value.toStringAsFixed(position);
    }
  }
}
