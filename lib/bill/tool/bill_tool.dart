import 'dart:math';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:halo_pos/bill/tool/promotion/card.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../common/login/login_center.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/tool/system_config_tool.dart';
import '../../db/bill_db_manager.dart';
import '../../entity/system/column_config.dart';
import '../../enum/bill_decimal_type.dart';
import '../../enum/bill_post_state.dart';
import '../../enum/bill_ptype_pop_value_change_type.dart';
import '../../enum/bill_type.dart';
import '../../login/entity/store/store_etype.dart';
import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../entity/audit_enable_response.dart';
import '../entity/back_goods_bill_list_bean.dart';
import '../entity/bill_sale_bill_detail_dto.dart';
import '../entity/bill_save_exception_detail_dto.dart';
import '../entity/bill_save_reslut_dto.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/goods_detail_dto.dart';
import '../entity/order_bill_item_entity.dart';
import '../entity/pay_result_dto.dart';
import '../entity/preferential_dto.dart';
import '../entity/ptype/ptype_prop_dto.dart';
import '../model/bill_model.dart';
import '../model/ptype_model.dart';
import '../settlement/entity/score_configuration.dart';
import '../tool/decimal_display_helper.dart';
import '../tool/price_compute/price_compute_with_no_tax.dart';
import 'back.dart';
import 'bill_combo_price_compute_helper.dart';
import 'bill_goods_util.dart';
import 'bill_price_compute_helper.dart';
import 'goods_tool.dart';
import 'goods_scope_util.dart';
import 'promotion/promotion.dart';

///
///@ClassName: bill_tool
///@Description: 类作用描述
///@Author: tanglan
///@Date: 8/13/21 5:34 PM
class BillTool {
  ///验证单据金额是否过大
  static bool checkBillTotalIsTooLarge(BuildContext context, String total) {
    String money = total;
    int pointIndex = money.indexOf('.');
    if (pointIndex != -1) {
      money = money.substring(0, pointIndex);
    }
    if (money.length >= 10) {
      HaloToast.show(context, msg: "单据金额必须小于1000000000");
      return false;
    }
    return true;
  }

  ///19位 数字
  static String getVchcode() {
    String vchcode = DateTime.now().millisecondsSinceEpoch.toString();
    while (vchcode.length < 19) {
      vchcode += Random().nextInt(10).toString();
    }
    debugPrint("$vchcode    ${vchcode.length}");
    return vchcode;
  }

  //region 新增商品

  ///商品合并
  ///[mergeHalfPriceGoods] 是否合并第二件半价商品，现在执行打折和促销之前，应该先将第二件半价商品合并，
  ///避免出现之前触发第二件半价，而再次执行的时候不触发，导致出现两个相同的商品行出现，而在这之后再合并的话，可能会导致金额和优惠有精度问题
  static GoodsDetailDto? addGoodsDetails(
    GoodsBillDto goodsBillDto,
    List<GoodsDetailDto> details, //原有的商品列表
    List<GoodsDetailDto> addDetails, //新增的商品或套餐
    BillType billType, {
    bool mergeHalfPriceGoods = false,
  }) {
    //新增的套餐
    Map<String, GoodsDetailDto> comboMap = {};
    //新增的套餐明细
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    //新增的普通商品
    List<GoodsDetailDto> normalGoodsList = [];
    // details.removeWhere((element) => element.unitQty == 0);
    GoodsTool.filterGoodsListAndGetCombo(
      addDetails,
      comboMap: comboMap,
      comboDetailsMap: comboDetailsMap,
      normalGoodsList: normalGoodsList,
      filter: (goods) {
        BillGoodsUtil.initGoodsInfo(goods, goodsBillDto);
        return true;
      },
    );
    GoodsDetailDto? result;
    if (normalGoodsList.isNotEmpty) {
      // for (var goods in normalGoodsList) {
      // //如果商品数量为0，则修改数量为1
      // if (goods.unitQty <= 0) {
      //   goods.unitQty = 1;
      // }
      // GoodsQtyUtil.onQtyChange(goods, goods.unitQty);
      // }
      result = BillGoodsUtil.addOrMergeGoods(
        details,
        normalGoodsList,
        mergeHalfPriceGoods: mergeHalfPriceGoods,
      );
    }
    if (comboMap.isNotEmpty) {
      final resultCombo = BillGoodsUtil.addOrMergeCombo(
        details,
        comboMap,
        comboDetailsMap,
        mergeHalfPriceGoods: mergeHalfPriceGoods,
      );
      //套餐
      result ??= resultCombo;
    }
    return result;
  }

  // ///合并商品
  // static void mergeAndAddDetails(
  //     List<GoodsDetailDto> existGoodsList, GoodsDetailDto goods) {
  //   //如果商品数量为0，则修改数量为1
  //   if (goods.unitQty <= 0) {
  //     goods.unitQty = 1;
  //   }
  //   GoodsQtyUtil.onQtyChange(goods, goods.unitQty);
  //   BillGoodsUtil.addOrMergeGoods(existGoodsList, goods);
  // }

  //endregion

  //region 单据信息填充
  static setGoodsBillDate(GoodsBillDto goodsBill) {
    DateTime dateTime = DateTime.now().toUtc();
    goodsBill.date = DateUtil.formatDate(
      dateTime,
      format: "yyyy-MM-ddTHH:mm:ss.SSS+00:00",
    );
  }

  static GoodsBillDto setGoodsBill(
    GoodsBillDto goodsBill, {
    BillType vchtype = BillType.SaleBill,
    BillBusinessType businessType = BillBusinessType.SaleNormal,
  }) {
    if (goodsBill.vchcode == "" || goodsBill.vchcode == null) {
      goodsBill.vchcode = getVchcode();
      goodsBill.vchtype = BillTypeData[vchtype] ?? "SaleBill";
      for (var element in goodsBill.outDetail) {
        element.vchcode = goodsBill.vchcode;
        element.vchtype = goodsBill.vchtype;
      }
      for (var element in goodsBill.inDetail) {
        element.vchcode = goodsBill.vchcode;
        element.vchtype = goodsBill.vchtype;
      }
      goodsBill.ktypeId = SpTool.getStoreInfo()!.ktypeId;
      goodsBill.kfullname = SpTool.getStoreInfo()!.ktypeName;
      goodsBill.btypeId = SpTool.getStoreInfo()!.btypeId;
      goodsBill.bfullname = SpTool.getStoreInfo()!.btypeName;
      goodsBill.createEtypeId = LoginCenter.getLoginUser().employeeId;
      goodsBill.createEfullname = LoginCenter.getLoginUser().etypeName;
      goodsBill.createUserCode = LoginCenter.getLoginUser().userCode;
      goodsBill.otypeId = SpTool.getStoreInfo()!.otypeId;
      goodsBill.ofullname = SpTool.getStoreInfo()!.otypeFullname;
      goodsBill.businessType = BillBusinessTypeString[businessType];

      ///以下是根据之前 获取单据后添加的默认值
      // goodsBill.date 会在提交单据的时候添加上
      goodsBill.profileId = LoginCenter.getLoginUser().profileId!;
      goodsBill.employeeId = LoginCenter.getLoginUser().employeeId!;
      goodsBill.pageIndex = 0;
      goodsBill.pageSize = 0;
      goodsBill.balanceType = 0;
      goodsBill.dtypeId = "0";
      goodsBill.shopId = "0";
      goodsBill.currencyAtypeTotal = 0;
      goodsBill.summary = "";
      goodsBill.postState = BillPostStateString[BillPostState.UNCONFIRMED];
      goodsBill.currencyId = "0";
      goodsBill.exchangeRate = 1;
      goodsBill.currencyOrderFeeAllotTotal = 0;
      goodsBill.confirm = false;
      goodsBill.orderCreate = false;
      goodsBill.validaterOrder = 0;
      goodsBill.billAuditInfo.bill =
          AuditEnableResponse()
            ..auditEnable = false
            ..exsitChildBills = {}
            ..overState = "UNOVER";
      goodsBill.balanceReverse = false;
      goodsBill.customType = 0;
      goodsBill.currencyAdvanceTotal = 0;
      goodsBill.printCount = 0;
      goodsBill.billDeliverType = "NO_DELIVER";
      goodsBill.billMode = 2;
      goodsBill.currencyFeeTotal = 0;
      goodsBill.currencyFreightCollectTotal = 0;
      goodsBill.currencyOrderWipeZeroTotal = 0;
      goodsBill.currencyOtherincomeTotal = 0;
      goodsBill.currencyPreferentialTotal = 0;
      goodsBill.orderOtherincomeTotal = 0;
      goodsBill.currencyBuyerFreightFee = 0;
      goodsBill.currencyDepositTotal = 0;
      goodsBill.needAmountFlag = false;
      goodsBill.onlyPost = false;
      goodsBill.postBillFlag = false;
      goodsBill.submitWarehousing = false;
      goodsBill.whetherPost = true;
    }
    return goodsBill;
  }

  //endregion

  //region 套餐
  static showComboRowDetail(
    GoodsDetailDto goodsDetailDto,
    List<GoodsDetailDto> dataSource,
  ) {
    goodsDetailDto.visible = !goodsDetailDto.visible;
    for (GoodsDetailDto element in dataSource) {
      if (element.comboRowParId == goodsDetailDto.comboRowId) {
        element.visible = goodsDetailDto.visible;
      }
    }
  }

  //是否是套餐详情
  static bool comboDetailRow(GoodsDetailDto goodsDetailDto) {
    if (goodsDetailDto.comboRow == false &&
        goodsDetailDto.comboDetailId != "0" &&
        goodsDetailDto.comboRowParId != "0") {
      return true;
    } else {
      return false;
    }
  }

  ///通过套餐行找到套餐明细
  static List<GoodsDetailDto> findComboRowDetail(
    GoodsDetailDto goodsDetailDto,
    List<GoodsDetailDto> dataSource,
  ) {
    List<GoodsDetailDto> comboDetails = [];
    if (goodsDetailDto.comboRow) {
      comboDetails =
          dataSource.where((element) {
            return element.comboRowParId == goodsDetailDto.comboRowId;
          }).toList();
    }
    return comboDetails;
  }

  ///通过明细找到套餐所有数据
  static List<GoodsDetailDto> findComboByDetails(
    GoodsDetailDto goodsDetailDto,
    List<GoodsDetailDto> dataSource,
  ) {
    List<GoodsDetailDto> currencyCombo =
        dataSource.where((element) {
          return (element.comboId == goodsDetailDto.comboId &&
                  element.comboRowParId == goodsDetailDto.comboRowParId &&
                  comboDetailRow(element)) ||
              (element.comboRowId == goodsDetailDto.comboRowParId &&
                  element.comboRow);
        }).toList();
    return currencyCombo;
  }

  //endregion

  static bool compareGoods(
    GoodsDetailDto oldDetail,
    GoodsDetailDto newDetail, {
    bool verifyMoney = true,
    bool comboCompare = false,
    bool compareBatchNo = true,
  }) {
    if (oldDetail.ptypeId != newDetail.ptypeId) {
      return false;
    }

    ///不合并套餐，默认不合并
    if (!comboCompare &&
        (newDetail.comboRow ||
            BillTool.comboDetailRow(newDetail) ||
            BillTool.comboDetailRow(oldDetail))) {
      return false;
    }

    ///优惠券商品
    if (oldDetail.preferentialHelp[PreferentialDtoType.couponGoods]?.helpId !=
        newDetail.preferentialHelp[PreferentialDtoType.couponGoods]?.helpId) {
      return false;
    }
    //增加促销类型判断商品是否是同一商品
    if (oldDetail.promotionType != newDetail.promotionType) {
      return false;
    }
    if (oldDetail.promotionId != newDetail.promotionId) {
      return false;
    }
    if (oldDetail.promotionParentPtypeId != newDetail.promotionParentPtypeId ||
        oldDetail.promotionParentSkuid != newDetail.promotionParentSkuid ||
        oldDetail.promotionParentUnitId != newDetail.promotionParentUnitId) {
      return false;
    }
    if (!oldDetail.comboRow && oldDetail.skuId != newDetail.skuId) {
      return false;
    }
    if (!StringUtil.equal(oldDetail.pFullName!, newDetail.pFullName ?? "")) {
      return false;
    }
    if (!StringUtil.equal(oldDetail.unitName, newDetail.unitName)) {
      return false;
    }
    if (!StringUtil.equal(oldDetail.batchNo, newDetail.batchNo) &&
        compareBatchNo) {
      return false;
    }
    if (!StringUtil.equal(
          oldDetail.produceDate ?? "",
          newDetail.produceDate ?? "",
        ) &&
        compareBatchNo) {
      return false;
    }
    if ((oldDetail.costMode == 1 || newDetail.costMode == 1) &&
        !StringUtil.equal(
          oldDetail.batchPrice.toString(),
          newDetail.batchPrice.toString(),
        ) &&
        compareBatchNo) {
      return false;
    }
    if (!StringUtil.equal(oldDetail.standard, newDetail.standard)) {
      return false;
    }
    if (!StringUtil.equal(oldDetail.ptypetype, newDetail.ptypetype)) {
      return false;
    }
    if (!StringUtil.isEmpty(oldDetail.comboId) &&
        !(oldDetail.comboId == "0") &&
        !StringUtil.equal(oldDetail.comboId, newDetail.comboId)) {
      return false;
    }
    if (verifyMoney) {
      ///套餐金额判断,只用判断比例
      if (BillTool.comboDetailRow(oldDetail) &&
          BillTool.comboDetailRow(newDetail)) {
        if (oldDetail.comboShareScale != newDetail.comboShareScale) {
          return false;
        }
      } else {
        if ((oldDetail.currencyPrice != newDetail.currencyPrice ||
            oldDetail.discount != newDetail.discount ||
            oldDetail.taxRate != newDetail.taxRate)) {
          return false;
        }
        if (oldDetail.discountPrice != newDetail.discountPrice) {
          return false;
        }
      }
    }
    return true;
  }

  static void deleteGoods(
    GoodsDetailDto goodsDetailDto,
    List<GoodsDetailDto> dataSource,
  ) {
    if (goodsDetailDto.comboRow) {
      dataSource.removeWhere((element) {
        return element.comboRowParId == goodsDetailDto.comboRowId;
      });
      dataSource.remove(goodsDetailDto);
    } else {
      dataSource.remove(goodsDetailDto);
    }
  }

  static void setGoodsGift(GoodsBillDto goodsBillDto, BillType billType) {
    if (billType == BillType.SaleBill) {
      for (var data in goodsBillDto.outDetail) {
        if (data.gift && !BillTool.comboDetailRow(data)) {
          // data.temPrice = data.currencyPrice;
          BillPriceComputeHelper().priceCompute.onValueChange(
            data,
            PtypePopValueChangeType.Gift,
            "",
          );
        }
      }
    } else {
      for (var data in goodsBillDto.inDetail) {
        if (data.gift && !BillTool.comboDetailRow(data)) {
          // data.temPrice = data.currencyPrice;
          BillPriceComputeHelper().priceCompute.onValueChange(
            data,
            PtypePopValueChangeType.Gift,
            "",
          );
        }
      }
    }
  }

  //是否是序列号商品,pos都当做严格序列号
  static bool snDetailRow(GoodsDetailDto goodsDetailDto) {
    return goodsDetailDto.snenabled == 1 || //非严格
        goodsDetailDto.snenabled == 2; //严格
  }

  //列对齐
  static Alignment getTextAlignment(ColumnConfig item) {
    ColumnType columnType = item.type!;
    if (columnType == ColumnType.barCode ||
        columnType == ColumnType.pName ||
        columnType == ColumnType.orderNumber ||
        columnType == ColumnType.userCode) {
      return Alignment.centerLeft;
    }
    if (columnType == ColumnType.number || columnType == ColumnType.image) {
      return Alignment.center;
    }
    return Alignment.centerRight;
  }

  static List<GoodsDetailDto> getGoodsDetails(
    GoodsBillDto goodsBill,
    BillType billType,
  ) {
    return billType == BillType.SaleBill || billType == BillType.SaleChangeBill
        ? goodsBill.outDetail
        : goodsBill.inDetail;
  }

  static void setGoodsDetails(
    GoodsBillDto goodsBill,
    BillType billType,
    List<GoodsDetailDto> dataSource,
  ) {
    if (billType == BillType.SaleBill || billType == BillType.SaleChangeBill) {
      goodsBill.outDetail = dataSource;
    } else {
      goodsBill.inDetail = dataSource;
    }
  }

  ///优惠分摊总额 = 其他优惠分摊相加
  static void setGoodsBillCurrencyPreferentialTotal(GoodsBillDto goodsBillDto) {
    goodsBillDto.currencyOrderPreferentialAllotTotal = 0;
    for (PreferentialDto preferentialDto
        in goodsBillDto.preferentialHelp.values) {
      goodsBillDto.currencyOrderPreferentialAllotTotal =
          goodsBillDto.currencyOrderPreferentialAllotTotal +
          preferentialDto.total;
    }
  }

  ///赠金分摊(储值)
  ///赠金分摊到明细
  ///规则：根据明细行的小计的占比对单据赠金进行分摊，误差精度放在最后一条明细行进行磨平处理
  static void setStoreTotalGiftTotal(
    GoodsBillDto goodsBillDto,
    num giftTotal,
    BillType billType, {
    Function? onSuccess,
    num backSumGiveTotal = 0,
  }) {
    if (0 == giftTotal + backSumGiveTotal) {
      return;
    }
    List<GoodsDetailDto> details = BillTool.getGoodsDetails(
      goodsBillDto,
      billType,
    );

    if (giftTotal > 0) {
      PreferentialDto preferentialDto = PreferentialDto();
      preferentialDto.total = giftTotal;
      preferentialDto.type = PreferentialType.GiftTotal.value;
      goodsBillDto.preferentialHelp[Preferential.giftStore.name] =
          preferentialDto;
    }
    BillPriceComputeHelper().giftTotalPreferentialShareCalculate(
      goodsBillDto,
      details,
      billType,
      backSumGiveTotal: backSumGiveTotal,
    );

    BillComboPriceComputeHelper(
      billType,
    ).recalculateComboPreferentialShare(details);

    setGoodsBillCurrencyPreferentialTotal(goodsBillDto);
    goodsBillDto.currencyGivePreferentialTotal = giftTotal;
    reCalcStatistic(goodsBillDto, billType: billType);
    if (onSuccess != null) onSuccess();
  }

  //region 退货单计算

  ///获取折后金额和折后单价
  static void resetSaleBackBillData(GoodsDetailDto goods) {
    goods.discountTotal =
        (MathUtil.addDec(
                  goods.currencyPtypePreferentialTotal, //单品优惠
                  goods.currencyOrderPreferentialAllotTotal,
                ) + //优惠分摊
                MathUtil.addDec(
                  goods.currencyGivePreferentialTotal.abs(), //赠金优惠
                  goods.currencyDisedTaxedTotal,
                )) //最终金额
            .toDouble();
    //计算折后单价
    PriceComputeWithNoTax.calculateDiscountPrice(goods);
  }

  ///退货单明细优惠分摊计算
  static void saleBackClearPreferentialShare(
    GoodsDetailDto goodsDetailDto,
    BillType billType,
  ) {
    if (billType == BillType.SaleBackBill && goodsDetailDto.unitQty != 0) {
      BillPriceComputeHelper().priceCompute.onValueChange(
        goodsDetailDto,
        PtypePopValueChangeType.PREFERENTIAL_SHARE,
        "0",
      );
    }
  }

  ///退货单明细优惠分摊计算
  static void saleBackGoodsDetailPreferentialShare(
    GoodsDetailDto goodsDetailDto,
  ) {
    ///退货数量为0或者需分摊金额为0时不处理
    if (goodsDetailDto.unitQty == 0 ||
        goodsDetailDto.currencyOrderPreferentialAllotTotal == 0 ||
        goodsDetailDto.originalMaxQty == 0) {
      return;
    }

    ///单个商品明细行全部退完(refundQty为基础单位，换算成辅助单位计算)
    if (goodsDetailDto.unitQty + goodsDetailDto.getRefundUnitQty() ==
        goodsDetailDto.originalMaxQty) {
      ///分摊优惠金额 = 明细总分摊优惠(销售出库单的明细赠金优惠)-已退分摊优惠
      goodsDetailDto.currencyOrderPreferentialAllotTotal =
          goodsDetailDto
              .saleBackHelper
              .originalCurrencyOrderPreferentialAllotTotal -
          goodsDetailDto
              .saleBackHelper
              .refundCurrencyOrderPreferentialAllotTotal;
    } else {
      ///退货比例 = 退货数量/原单数量
      num refundRate = goodsDetailDto.unitQty / goodsDetailDto.originalMaxQty;

      ///退货单分摊金额=退货比例*明细总分摊(销售出库单的明细分摊金额)
      goodsDetailDto.currencyOrderPreferentialAllotTotal =
          SystemConfigTool.doubleDivisionToDecimal(
            refundRate,
            goodsDetailDto
                .saleBackHelper
                .originalCurrencyOrderPreferentialAllotTotal,
            BillDecimalType.TOTAL,
          );
    }

    BillPriceComputeHelper().priceCompute.onValueChange(
      goodsDetailDto,
      PtypePopValueChangeType.PREFERENTIAL_SHARE,
      goodsDetailDto.currencyOrderPreferentialAllotTotal.toString(),
    );
  }

  ///退货单赠金优惠分摊计算
  static void saleBackGiveTotalShare(GoodsDetailDto goodsDetailDto) {
    ///仅处理退货单
    ///退货数量为0或者需分摊金额为0时不处理
    if (goodsDetailDto.unitQty == 0 ||
        goodsDetailDto.givePreferentialTotal == 0 ||
        goodsDetailDto.originalMaxQty == 0) {
      return;
    }

    ///单个商品明细行全部退完
    if (MathUtil.addDec(
          goodsDetailDto.unitQty,
          goodsDetailDto.getRefundUnitQty(),
        ).toDouble() ==
        goodsDetailDto.originalMaxQty) {
      ///单条商品行退完
      ///赠金优惠金额 = 明细总赠金优惠(销售出库单的明细赠金优惠)-已退赠金优惠
      goodsDetailDto.givePreferentialTotal =
          goodsDetailDto.saleBackHelper.remainGivePreferentialTotal();
      goodsDetailDto.currencyGivePreferentialTotal =
          goodsDetailDto.givePreferentialTotal;
    } else {
      ///退货比例 = 退货数量/原单数量
      num refundRate = goodsDetailDto.unitQty / goodsDetailDto.originalMaxQty;

      ///退货单赠金优惠数据 =退货比例*明细总赠金优惠(销售出库单的明细赠金优惠)
      goodsDetailDto
          .givePreferentialTotal = SystemConfigTool.doubleMultipleToDecimal(
        refundRate,
        goodsDetailDto.saleBackHelper.originalGivePreferentialTotal,
        BillDecimalType.TOTAL,
      );
      goodsDetailDto.currencyGivePreferentialTotal =
          goodsDetailDto.givePreferentialTotal;
    }

    BillPriceComputeHelper().priceCompute.onValueChange(
      goodsDetailDto,
      PtypePopValueChangeType.giftTotal,
      goodsDetailDto.givePreferentialTotal.toString(),
    );
  }

  ///数据调整
  static void setSaleBackGoodsBill(
    GoodsBillDto bill,
    BillSaleBillDetailDto billDetailDto,
    OrderBillItem saleBill,
  ) {
    bill.sourceVchcode = saleBill.vchcode;

    // 换货单需要设置换入仓库(ktypeId2)为门店仓库
    if (bill.vchtype == BillTypeData[BillType.SaleChangeBill]) {
      bill.ktypeId2 = SpTool.getStoreInfo()!.ktypeId;
      bill.kfullname2 = SpTool.getStoreInfo()!.ktypeName;
    }

    //因为单据组给的数据不规范，所以要单独对套餐进行处理

    //套餐明细行
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    //非套餐明细行
    List<GoodsDetailDto> inDetailsWiNoComboDetails = [];

    //原单的单据金额
    bill.originalTotal = num.parse(
      billDetailDto.billDetail?.currencyBillTotal ?? "0",
    );

    //原单的单品优惠汇总 todo

    //原单的赠金优惠
    bill.originalCurrencyGivePreferentialTotal =
        billDetailDto.billDetail?.currencyGivePreferentialTotal ?? 0;
    bill.originCurrencyAdvanceTotal =
        billDetailDto.billDetail?.currencyAdvanceTotal ?? 0;
    //找到原单中的明细行辅助，且类型为优惠券的
    List<PreferentialGoodsDetailsBean> giftCouponPreferentialList =
        billDetailDto.preferentialGoodsDetails
            ?.where(
              (element) =>
                  element.preferentialType == PreferentialType.Coupon.value,
            )
            .toList() ??
        [];
    for (var element in billDetailDto.billDetail!.outDetail) {
      GoodsDetailDto goods = GoodsDetailDto.fromMap(element.toJson());
      //目前查询退货单，没有单独给单据和明细行设置优惠辅助表，但是退换货时，
      //要退的商品中如果是提货券的赠品，则不允许退
      if (element.gift && giftCouponPreferentialList.isNotEmpty) {
        final PreferentialGoodsDetailsBean? couponPreferential =
            giftCouponPreferentialList.firstWhereOrNull(
              (e) => e.detailId == element.detailId,
            );
        if (couponPreferential != null) {
          goods.saleBackHelper.isCouponGift = true;
          goods.unitQty = 0;
        }
      }
      goods.saleBackHelper.originalGoods = element;
      //填入原单数据
      goods.vchtype = BillTypeData[BillType.SaleBackBill];
      goods.sourceDetailId = goods.detailId;
      goods.sourceVchcode = saleBill.vchcode;
      goods.sourceVchtype = saleBill.vchtype; //原单退，一定是零售出库单
      goods.sourceBusinessType = BillBusinessTypeIntData[saleBill.businessType];
      goods.sourceBillNumber = saleBill.billNumber;
      goods.sourceBfullname = saleBill.bfullname;
      goods.sourceBtypeId = saleBill.btypeId;
      goods.sourceBillDate = saleBill.billDate;
      goods.sourceDfullname = saleBill.dfullname;
      goods.sourceDtypeId = saleBill.dtypeId;
      goods.sourceEfullname = saleBill.efullname;
      goods.sourceEtypeId = saleBill.etypeId;
      goods.sourceKfullname = saleBill.kfullname;
      goods.sourceKtypeId = saleBill.ktypeId;
      goods.sourceOfullname = saleBill.ofullname;
      goods.sourceOtypeId = saleBill.otypeId;
      goods.sourceDetailPeriod = saleBill.period;
      goods.hasSource = true;
      goods.detailId = null;
      //对于退货入库的商品，把序列号清除掉，后面让用户去选
      goods.serialNoList.clear();
      //计算分摊前优惠
      goods.preferentialDiscount =
          (Decimal.parse(goods.currencyPreferentialTotal.toString()) -
                  Decimal.parse(
                    goods.currencyOrderPreferentialAllotTotal.toString(),
                  ) -
                  Decimal.parse(
                    goods.currencyGivePreferentialTotal.abs().toString(),
                  ))
              .toDouble();

      //更新数据
      goods.vchcode = bill.vchcode;
      //计算折后金额和折后单价
      BillTool.resetSaleBackBillData(goods);

      if (BillTool.comboDetailRow(goods)) {
        comboDetailsMap.putIfAbsent(goods.comboRowParId, () => []).add(goods);
      } else {
        inDetailsWiNoComboDetails.add(goods);
      }
    }
    //将原单商品添加到退货商品列表中，用套餐明细行的已退货数量计算套餐行的已退货数量
    for (var goods in inDetailsWiNoComboDetails) {
      bill.inDetail.add(goods);
      if (goods.comboRow) {
        List<GoodsDetailDto> details = GoodsTool.getComboDetailsFromMap(
          goods,
          comboDetailsMap,
        );
        //根据套餐明细行判断套餐是否是提货券赠品
        if (details.any((e) => e.saleBackHelper.isCouponGift)) {
          goods.saleBackHelper.isCouponGift = true;
          goods.unitQty = 0;
        }
        goods.refundQty =
            MathUtil.divideDec(
              details.first.refundQty,
              details.first.comboQtyRate,
            ).toDouble();
        bill.inDetail.addAll(details);
      }
    }

    for (var goods in bill.inDetail) {
      GoodsDetailDto originGoods = GoodsDetailDto.fromMap(goods.toJson());
      bill.originalDetail.add(originGoods);

      // //计算最终优惠，含赠金的最终优惠
      // BillPriceComputeHelper().priceCompute.onValueChange(
      //     goods,
      //     PtypePopValueChangeType.giftTotal,
      //     DecimalDisplayHelper.getTotalFixed(
      //         originGoods.givePreferentialTotal.abs().toString()));
      //计算各种已退金额汇总
      BillTool.calculateSaleBackBillGoods(goods, billDetailDto);
    }
    for (var goods in inDetailsWiNoComboDetails) {
      BackBillUtil.onQtyChange(
        goods,
        comboDetails: GoodsTool.getComboDetailsFromMap(goods, comboDetailsMap),
      );
    }
    //todo 这里在上面 BillTool.calculateSaleBackBillGoods(goods, billDetailDto); 已经算过一次，为什么还要算一次？
    // BillComboPriceComputeHelper(BillType.SaleBackBill)
    //     .recalculateMax(inDetailsWiNoComboDetails, comboDetailsMap);
    // BillComboPriceComputeHelper(BillType.SaleBackBill)
    //     .recalculateCombo(bill.inDetail);
    // BillComboPriceComputeHelper(BillType.SaleBackBill)
    //     .recalculateComboPreferentialShare(bill.inDetail);
  }

  ///退货单商品最大值
  static void calculateSaleBackBillGoods(
    GoodsDetailDto currencyGoods,
    BillSaleBillDetailDto billDetailDto,
  ) {
    GoodsDetailDto origGoods = GoodsDetailDto.fromMap(currencyGoods.toJson());

    //region  记录原单据信息，用于后续退货计算
    ///对销售出库单的原始明细金额记录
    currencyGoods.originalMaxQty = currencyGoods.unitQty;

    ///原单赠金优惠
    currencyGoods.saleBackHelper.originalGivePreferentialTotal =
        currencyGoods.givePreferentialTotal.abs();

    //原单单品优惠
    currencyGoods.saleBackHelper.originalPtypePreferentialTotal =
        currencyGoods.currencyPtypePreferentialTotal;

    ///原单优惠分摊
    currencyGoods.saleBackHelper.originalCurrencyOrderPreferentialAllotTotal =
        currencyGoods.currencyOrderPreferentialAllotTotal;

    ///原单最终优惠
    currencyGoods.saleBackHelper.originalCurrencyPreferentialTotal =
        currencyGoods.currencyPreferentialTotal;

    ///原单折后金额
    currencyGoods.saleBackHelper.originalCurrencyTaxTotal =
        currencyGoods.currencyTaxTotal;

    ///原单单据金额
    currencyGoods.saleBackHelper.originalCurrencyTotal =
        currencyGoods.currencyTotal;

    ///原单折后含税金额
    currencyGoods.saleBackHelper.originalCurrencyDisedTaxedTotal =
        currencyGoods.currencyDisedTaxedTotal;

    ///原单折后不含税金额
    currencyGoods.saleBackHelper.originalCurrencyDisedTotal =
        currencyGoods.currencyDisedTotal;

    ///原单单据金额
    currencyGoods.saleBackHelper.originalCurrencyTotal =
        currencyGoods.currencyTotal;
    //endregion

    currencyGoods.maxQty =
        currencyGoods.saleBackHelper.isCouponGift
            ? 0
            : SystemConfigTool.doubleSubtractionToDecimal(
              origGoods.unitQty,
              origGoods.getRefundUnitQty(),
              BillDecimalType.QTY,
            );

    currencyGoods.unitQty = currencyGoods.maxQty!;
    // currencyGoods.unitQty = SystemConfigTool.doubleSubtractionToDecimal(
    //     origGoods.unitQty, origGoods.refundQty, BillDecimalType.QTY);

    ///之前未已退过商品
    if (origGoods.refundQty == 0) {
      return;
    }

    for (BackGoodsBillListBean listBean in billDetailDto.backGoodsBillList!) {
      for (GoodsDetailDto inDetailGoods in listBean.goodsbill!.inDetail) {
        GoodsDetailDto origInDetailGoods = GoodsDetailDto.fromMap(
          inDetailGoods.toJson(),
        );

        ///处理多次退货中的相同明细
        if (currencyGoods.sourceDetailId != origInDetailGoods.sourceDetailId) {
          continue;
        }

        //region 汇总单据明细已退的数据
        //汇总已退赠金
        currencyGoods.saleBackHelper.refundGivePreferentialTotal =
            MathUtil.addDec(
              currencyGoods.saleBackHelper.refundGivePreferentialTotal,
              inDetailGoods.givePreferentialTotal.abs(),
            ).toDouble();

        //汇总已退单品优惠
        currencyGoods.saleBackHelper.refundPtypePreferentialTotal =
            MathUtil.addDec(
              currencyGoods.saleBackHelper.refundPtypePreferentialTotal,
              inDetailGoods.currencyPtypePreferentialTotal,
            ).toDouble();

        //汇总已退优惠分摊
        currencyGoods.saleBackHelper.refundCurrencyOrderPreferentialAllotTotal =
            MathUtil.addDec(
              currencyGoods
                  .saleBackHelper
                  .refundCurrencyOrderPreferentialAllotTotal,
              inDetailGoods.currencyOrderPreferentialAllotTotal,
            ).toDouble();

        //汇总已退最终优惠
        currencyGoods.saleBackHelper.refundCurrencyPreferentialTotal =
            MathUtil.addDec(
              currencyGoods.saleBackHelper.refundCurrencyPreferentialTotal,
              inDetailGoods.currencyPreferentialTotal,
            ).toDouble();

        //汇总已退折后税额
        currencyGoods.saleBackHelper.refundCurrencyTaxTotal =
            MathUtil.addDec(
              currencyGoods.saleBackHelper.refundCurrencyTaxTotal,
              inDetailGoods.currencyTaxTotal,
            ).toDouble();

        //汇总已退单据金额
        currencyGoods.saleBackHelper.refundCurrencyTotal =
            MathUtil.addDec(
              currencyGoods.saleBackHelper.refundCurrencyTotal,
              inDetailGoods.currencyTotal,
            ).toDouble();

        //汇总已退折后单据金额
        currencyGoods.saleBackHelper.refundCurrencyDisedTaxedTotal =
            MathUtil.addDec(
              currencyGoods.saleBackHelper.refundCurrencyDisedTaxedTotal,
              inDetailGoods.currencyDisedTaxedTotal,
            ).toDouble();

        currencyGoods.saleBackHelper.refundCurrencyDisedTotal =
            MathUtil.addDec(
              currencyGoods.saleBackHelper.refundCurrencyDisedTotal,
              inDetailGoods.currencyDisedTotal,
            ).toDouble();

        //endregion
      }
    }

    // calcSaleBackBillInfo(currencyGoods);
    //
    // if (currencyGoods.unitQty == 0) {
    //   BillPriceComputeHelper().priceCompute.onValueChange(
    //       currencyGoods,
    //       PtypePopValueChangeType.DISCOUNT_TAXED_PRICE,
    //       currencyGoods.currencyDisedTaxedPrice.toString());
    //   BillPriceComputeHelper().priceCompute.onValueChange(currencyGoods,
    //       PtypePopValueChangeType.Qty, currencyGoods.unitQty.toString());
    // }
  }

  ///计算销售出库单可退明细的信息
  static calcSaleBackBillInfo(GoodsDetailDto currencyGoods) {
    currencyGoods.currencyTotal =
        currencyGoods.saleBackHelper.remainCurrencyTotal();

    currencyGoods.currencyTaxTotal =
        currencyGoods.saleBackHelper.remainCurrencyTaxTotal();
    currencyGoods.currencyPtypePreferentialTotal =
        currencyGoods.saleBackHelper.remainPtypePreferentialTotal();
    currencyGoods.currencyPreferentialTotal =
        currencyGoods.saleBackHelper.remainCurrencyPreferentialTotal();
    currencyGoods.currencyDisedTaxedTotal =
        currencyGoods.saleBackHelper.remainCurrencyDisedTaxedTotal();
    currencyGoods.currencyDisedTotal =
        currencyGoods.saleBackHelper.remainCurrencyDisedTotal();
    currencyGoods.givePreferentialTotal =
        currencyGoods.saleBackHelper.remainGivePreferentialTotal();
    currencyGoods.currencyGivePreferentialTotal =
        currencyGoods.givePreferentialTotal;
    currencyGoods.posCurrencyDisedTaxedTotal =
        MathUtil.addDec(
          currencyGoods.currencyDisedTotal,
          currencyGoods.givePreferentialTotal.abs(),
        ).toDouble();
    currencyGoods.currencyOrderPreferentialAllotTotal =
        currencyGoods.saleBackHelper
            .remainCurrencyOrderPreferentialAllotTotal();
  }

  //endregion

  //region 商品取价
  ///获取商品价格
  ///包含零售价、门店价、和会员价
  ///[calculateDiscount] 是否取价后重新计算折扣，这里算过之后折扣会变回1
  static Future<void> getPtypePrice(
    BuildContext context,
    List<GoodsDetailDto> goods, {
    bool calculateDiscount = true,
  }) {
    return PtypeModel.getPtypePrice(context, goods).then((priceList) {
      for (var price in priceList) {
        //是否开启sku定价管理
        bool skuPriceEnabled = price.skuId != "0" || price.skuPrice == 1;
        for (var goods in goods) {
          if (price.ptypeId == goods.ptypeId && price.unitId == goods.unitId) {
            //未开启sku定价管理无需判断skuId
            if (!skuPriceEnabled || price.skuId == goods.skuId) {
              //当价格本中的门店价不为0时，优先取门店价，否则取零售价
              if (price.otypePrice != null && price.otypePrice! > 0) {
                goods.currencyPrice = price.otypePrice!;
              } else {
                goods.currencyPrice = price.retailPrice ?? 0;
              }
              if (goods.unitQty <= 0) {
                goods.unitQty = 1;
              }
              goods.saleOtypeVipPrice = price.vipPrice?.toString();
              //重新根据价格计算折扣
              if (calculateDiscount) {
                BillPriceComputeHelper().priceCompute.onValueChange(
                  goods,
                  PtypePopValueChangeType.PRICE,
                  goods.currencyPrice.toString(),
                );
              }
            }
          }
        }
      }
    });
  }

  //endregion

  //region 商品编辑

  ///pos ui上的编辑条件
  static bool checkPtypeDetailEnable(GoodsDetailDto goodsDetailDto) {
    if (goodsDetailDto.saleBackHelper.isCouponGift || //退货时，提货券赠品不允许修改
        BillTool.comboDetailRow(goodsDetailDto) ||
        (goodsDetailDto.promotionGift &&
            goodsDetailDto.promotionType !=
                PromotionType.combinationBuy.index) ||
        goodsDetailDto.preferentialHelp.containsKey(
          Preferential.giftCoupon.name,
        )) {
      return false;
    } else {
      return true;
    }
  }

  ///是否展示商品明细行选择促销赠品按钮
  ///只有指定赠品的促销才会显示
  static bool showGiftEditButton(
    GoodsDetailDto goodsDetailDto,
    BillType billType,
  ) {
    if (billType == BillType.SaleBill &&
        goodsDetailDto.gift &&
        goodsDetailDto.promotionGift &&
        goodsDetailDto.promotionGiftScope ==
            PromotionGiftScope.chooseGoods.value) {
      return true;
    } else {
      return false;
    }
  }

  //endregion

  //region 结算
  static void setGoodsBillDefaultValue(
    GoodsBillDto goodsBillDto,
    StoreEtype? etype,
  ) {
    goodsBillDto.freightAtypeName = "";
    goodsBillDto.freightBillNo = "";
    goodsBillDto.freightBtypeId = "0";
    goodsBillDto.freightBtypeName = "";
    goodsBillDto.freightaTypeTotal = "0";
    goodsBillDto.shareType = 0;
    goodsBillDto.dfullname = "";
    goodsBillDto.billType = "goodsBill";
    goodsBillDto.postState =
        BillPostStateString[BillPostState.PROCESS_COMPLETED];
    goodsBillDto.confirm = false;
    goodsBillDto.etypeId = etype?.etypeId ?? "0";
    goodsBillDto.efullname = etype?.etypeName ?? "";
  }

  ///结算信息payment
  static Map getAtypeMessage(GoodsBillDto goodsBill, BillType billType) {
    String afullnameCation = "";
    String atypeTotalCation = "";
    if (billType == BillType.SaleBill) {
      afullnameCation = "收款金额：";
      atypeTotalCation = "收款信息：";
    } else if (billType == BillType.SaleBackBill) {
      afullnameCation = "付款金额：";
      atypeTotalCation = "付款信息：";
      goodsBill.balanceReverse = true;
    } else if (billType == BillType.SaleChangeBill) {
      if (num.parse(goodsBill.currencyBillTotal) >= 0) {
        afullnameCation = "收款金额：";
        atypeTotalCation = "收款信息：";
      } else {
        afullnameCation = "付款金额：";
        atypeTotalCation = "付款信息：";
        goodsBill.balanceReverse = true;
      }
    }
    return {
      "afullnameCation": afullnameCation,
      atypeTotalCation: "atypeTotalCation",
    };
  }

  ///构建备注
  static String buildMemo(
    GoodsBillDto bill,
    VipWithLevelAssertsRightsCardDTO? vipInfo,
  ) {
    List<String> memoList = [];
    if (bill.tips.isNotEmpty) {
      memoList.add(bill.tips);
    }
    if (bill.vchtype == BillTypeData[BillType.SaleBackBill] &&
        StringUtil.isZeroOrEmpty(bill.sourceVchcode)) {
      memoList.add("按商品退货");
    }
    if (vipInfo != null && vipInfo.vip != null) {
      if (bill.preferentialHelp.containsKey(Preferential.giftCoupon.name)) {
        memoList.add(discountZero);
      }
      final scorePreferential =
          bill.preferentialHelp[Preferential.scoreDiscount.name];
      if (scorePreferential != null &&
          scorePreferential.total > 0 &&
          scorePreferential.value > 0) {
        memoList.add(
          "会员:${vipInfo.vip!.name}(${vipInfo.vip!.phone})使用${scorePreferential.value}积分抵扣金额${scorePreferential.total}元",
        );
      }
    }
    return memoList.join(";");
  }

  static String createSaveExceptionMessage(
    List<BillSaveExceptionDetailDTO> details,
  ) {
    String names = "";
    for (var item in details) {
      if (!StringUtil.isEmpty(names)) {
        names += "\n";
      }
      names +=
          "${item.pfullname ?? ""} ${item.propValues ?? ""} ${item.message ?? ""}";
    }
    return names;
  }

  static String getPayTotal(GoodsBillDto goodsBillDto) {
    String backTotal = "0";
    for (var element in goodsBillDto.payment) {
      if (element.paywayType == 2) {
        backTotal = element.currencyAtypeTotal!;
      }
    }
    return backTotal;
  }

  ///计算赠送积分的金额
  ///积分累计策略规则： 促销的积分累计与积分策略配置交集，即促销和积分策略配置其中任何一个条件不赠送积分，则该商品不赠送
  static Decimal getGiveScoreMoney({
    required GoodsBillDto goodsBill,
    required BillType billType,
    ScoreConfiguration? scoreConfiguration,
  }) {
    Decimal giveScoreMoney = Decimal.zero;

    if (billType == BillType.SaleChangeBill || billType == BillType.SaleBill) {
      giveScoreMoney = calSaleGiveScoreMoney(goodsBill, scoreConfiguration);
    }
    if (giveScoreMoney < Decimal.zero) return Decimal.zero;
    return giveScoreMoney;
  }

  ///销售出库单累计赠送积分金额计算逻辑
  static Decimal calSaleGiveScoreMoney(
    GoodsBillDto goodsBill,
    ScoreConfiguration? scoreConfiguration,
  ) {
    Decimal giveScoreMoney = Decimal.zero;
    List<GoodsDetailDto> comboList = [];
    Map<String, List<GoodsDetailDto>> comboDetails = {};
    for (var goodsDetail in goodsBill.outDetail) {
      //套餐明细行不参与计算（套餐行计算即可）
      if (BillTool.comboDetailRow(goodsDetail)) {
        comboDetails
            .putIfAbsent(goodsDetail.comboRowParId, () => [])
            .add(goodsDetail);
        continue;
      }
      if (goodsDetail.comboRow) comboList.add(goodsDetail);
      //验证使用促销的商品是否需要积分累计
      bool calIntegral = PromotionUtil.checkPromotionGoodsScoreVerify(
        goodsDetail,
      );

      //参加了促销且不累计积分，则无需继续验证积分策略逻辑
      if (!calIntegral) {
        continue;
      }

      ///全部商品都参与积分赠送
      if (null == scoreConfiguration ||
          scoreConfiguration.scoreAccrualType == 1) {
        giveScoreMoney += Decimal.parse(
          goodsDetail.posCurrencyDisedTaxedTotal.toString(),
        );
        goodsDetail.giveVipScore = true;
        continue;
      }
      ///指定商品
      else if (scoreConfiguration.scoreAccrualType == 2) {
        ///当前商品在指定赠品中
        bool isExist =
            (scoreConfiguration.ssVipScoreConfigurationPtype
                    ?.where(
                      (element) =>
                          element.skuId == goodsDetail.skuId &&
                          element.unitId == goodsDetail.unitId &&
                          element.ptypeId == goodsDetail.ptypeId,
                    )
                    .length ??
                0) >
            0;
        if (isExist) {
          goodsDetail.giveVipScore = true;
          giveScoreMoney += Decimal.parse(
            goodsDetail.posCurrencyDisedTaxedTotal.toString(),
          );
        }
        continue;
      }
      ///排除商品
      else if (scoreConfiguration.scoreAccrualType == 3) {
        ///当前商品不在排除商品中
        bool isNotExist =
            (scoreConfiguration.ssVipScoreConfigurationPtype
                    ?.where(
                      (element) =>
                          element.skuId == goodsDetail.skuId &&
                          element.unitId == goodsDetail.unitId &&
                          element.ptypeId == goodsDetail.ptypeId,
                    )
                    .length ??
                0) ==
            0;

        if (isNotExist) {
          goodsDetail.giveVipScore = true;
          giveScoreMoney += Decimal.parse(
            goodsDetail.posCurrencyDisedTaxedTotal.toString(),
          );
        }
        continue;
      }
      ///商品分类
      else if (scoreConfiguration.scoreAccrualType == 4) {
        ///当前商品在指定分类中
        bool isExist = _isGoodsInScoreConfigurationByCategory(
          goodsDetail,
          scoreConfiguration,
        );
        if (isExist) {
          goodsDetail.giveVipScore = true;
          giveScoreMoney += Decimal.parse(
            goodsDetail.posCurrencyDisedTaxedTotal.toString(),
          );
        }
        continue;
      }
      ///商品标签
      else if (scoreConfiguration.scoreAccrualType == 5) {
        ///当前商品包含指定标签
        bool isExist = _isGoodsInScoreConfigurationByLabel(
          goodsDetail,
          scoreConfiguration,
        );
        if (isExist) {
          goodsDetail.giveVipScore = true;
          giveScoreMoney += Decimal.parse(
            goodsDetail.posCurrencyDisedTaxedTotal.toString(),
          );
        }
        continue;
      }
    }
    if (comboList.isNotEmpty) {
      for (var combo in comboList) {
        if (combo.giveVipScore) {
          List<GoodsDetailDto> comboDetailList =
              comboDetails[combo.comboRowId] ?? [];
          for (var o in comboDetailList) {
            o.giveVipScore = true;
          }
        }
      }
    }
    return giveScoreMoney;
  }

  //endregion

  //region 单据汇总

  ///根据明细行汇总单据金额
  ///计算总金额、总数、总优惠
  static void reCalcStatistic(
    GoodsBillDto goodsBillDto, {
    required BillType billType,
  }) {
    if (billType == BillType.SaleBill || billType == BillType.SaleBackBill) {
      reCalcStatisticSaleOrSaleBack(goodsBillDto, billType: billType);
    } else {
      reCalcStatisticChangeBill(goodsBillDto);
    }
  }

  ///根据明细行汇总单据金额
  ///开单或退货单
  static BackSumAllTotal reCalcStatisticSaleOrSaleBack(
    GoodsBillDto goodsBillDto, {
    BillType billType = BillType.SaleBill,
  }) {
    List<GoodsDetailDto> details = BillTool.getGoodsDetails(
      goodsBillDto,
      billType,
    );
    BackSumAllTotal backSumAllTotal = calculateBackSumAllTotal(details);
    //优惠分摊
    goodsBillDto.currencyOrderPreferentialAllotTotal =
        backSumAllTotal.currencyOrderPreferentialAllotTotal.toDouble();
    //赠金优惠
    goodsBillDto.currencyGivePreferentialTotal =
        backSumAllTotal.sumGivePreferentialTotal.toDouble();
    //优惠分摊前优惠
    goodsBillDto.discountPreferential =
        backSumAllTotal.sumPreferential.toString();
    //最终金额
    goodsBillDto.currencyBillTotal =
        backSumAllTotal.sumDisedTaxedTotal.toString();
    //最终金额含赠金
    goodsBillDto.posCurrencyBillTotal =
        backSumAllTotal.sumPosDisedTaxedTotal.toString();
    //单品优惠汇总
    goodsBillDto.currencyPtypePreferentialTotal =
        backSumAllTotal.currencyPtypePreferentialTotal.toDouble();
    return backSumAllTotal;
  }

  ///根据明细行汇总单据金额
  ///换货单
  static Map reCalcStatisticChangeBill(GoodsBillDto goodsBillDto) {
    Map<String, BackSumAllTotal> backSumAll = {};
    List<GoodsDetailDto> outDetails = goodsBillDto.outDetail;
    List<GoodsDetailDto> inDetails = goodsBillDto.inDetail;
    BackSumAllTotal outSumAllTotal = calculateBackSumAllTotal(outDetails);
    BackSumAllTotal inSumAllTotal = calculateBackSumAllTotal(inDetails);
    backSumAll["inDetail"] = inSumAllTotal;
    backSumAll["outDetail"] = outSumAllTotal;
    //优惠分摊
    goodsBillDto.currencyOrderPreferentialAllotTotal =
        SystemConfigTool.doubleSubtractionToDecimal(
          outSumAllTotal.currencyOrderPreferentialAllotTotal.toDouble(),
          inSumAllTotal.currencyOrderPreferentialAllotTotal.toDouble(),
          BillDecimalType.TOTAL,
        );

    //分摊前优惠
    goodsBillDto.discountPreferential =
        SystemConfigTool.doubleSubtractionToDecimal(
          outSumAllTotal.sumPreferential.toDouble(),
          inSumAllTotal.sumPreferential.toDouble(),
          BillDecimalType.TOTAL,
        ).toString();

    //最终金额
    goodsBillDto.currencyBillTotal =
        SystemConfigTool.doubleSubtractionToDecimal(
          outSumAllTotal.sumDisedTaxedTotal.toDouble(),
          inSumAllTotal.sumDisedTaxedTotal.toDouble(),
          BillDecimalType.TOTAL,
        ).toString();

    //赠金优惠
    goodsBillDto.currencyGivePreferentialTotal =
        SystemConfigTool.doubleSubtractionToDecimal(
          outSumAllTotal.sumGivePreferentialTotal.toDouble(),
          inSumAllTotal.sumGivePreferentialTotal.toDouble(),
          BillDecimalType.TOTAL,
        );

    //单品优惠
    goodsBillDto.currencyPtypePreferentialTotal =
        SystemConfigTool.doubleSubtractionToDecimal(
          outSumAllTotal.currencyPtypePreferentialTotal.toDouble(),
          inSumAllTotal.currencyPtypePreferentialTotal.toDouble(),
          BillDecimalType.TOTAL,
        );

    goodsBillDto.currencyBillTotal.toString();
    goodsBillDto.sumInDetailTotal = inSumAllTotal.sumDisedTaxedTotal.toDouble();
    goodsBillDto.sumInDetailGiveTotal =
        inSumAllTotal.sumGivePreferentialTotal.toDouble();
    goodsBillDto.sumPosInDetailTotal =
        inSumAllTotal.sumPosDisedTaxedTotal.toDouble();

    goodsBillDto.posCurrencyBillTotal =
        MathUtil.add(
          goodsBillDto.currencyBillTotal,
          goodsBillDto.currencyGivePreferentialTotal.toString(),
        ).toString();
    return backSumAll;
  }

  // static BackSumAllTotal calculateBackSumAllTotal1(
  //     List<GoodsDetailDto> details) {
  //   BackSumAllTotal backSumAllTotal = BackSumAllTotal();
  //   for (int i = 0; i < details.length; i++) {
  //     ///退货数量为0时，不计算
  //     if (details[i].unitQty == 0) {
  //       continue;
  //     }
  //
  //     ///套餐明细行不计算
  //     if (!BillTool.comboDetailRow(details[i])) {
  //       backSumAllTotal.sumQty = backSumAllTotal.sumQty +
  //           MathUtil.parseToDecimal(details[i].unitQty.toString());
  //     }
  //     if (details[i].comboRow) {
  //       continue;
  //     }
  //     backSumAllTotal.currencyOrderPreferentialAllotTotal +=
  //         MathUtil.parseToDecimal(
  //             details[i].currencyOrderPreferentialAllotTotal.toString());
  //     backSumAllTotal.sumTotal +=
  //         MathUtil.parseToDecimal(details[i].currencyTotal.toString());
  //     backSumAllTotal.sumDisedTaxedTotal += MathUtil.parseToDecimal(
  //         details[i].currencyDisedTaxedTotal.toString());
  //     backSumAllTotal.sumPreferential +=
  //         MathUtil.parseToDecimal(details[i].preferentialDiscount.toString());
  //     backSumAllTotal.sumFinalPreferential += MathUtil.parseToDecimal(
  //         details[i].currencyPreferentialTotal.toString());
  //     backSumAllTotal.sumGivePreferentialTotal +=
  //         MathUtil.parseToDecimal(details[i].givePreferentialTotal.toString());
  //   }
  //   return backSumAllTotal;
  // }

  ///根据明细行汇总单据金额
  static BackSumAllTotal calculateBackSumAllTotal(
    List<GoodsDetailDto> details,
  ) {
    BackSumAllTotal backSumAllTotal = BackSumAllTotal();
    for (int i = 0; i < details.length; i++) {
      final goods = details[i];
      //退货数量为0时，不计算
      if (goods.unitQty == 0) {
        continue;
      }

      //套餐明细行不计算
      if (!BillTool.comboDetailRow(goods)) {
        backSumAllTotal.sumQty =
            backSumAllTotal.sumQty +
            MathUtil.parseToDecimal(goods.unitQty.toString());
      }
      if (goods.comboRow) {
        continue;
      }
      //优惠分摊
      backSumAllTotal
          .currencyOrderPreferentialAllotTotal += MathUtil.parseToDecimal(
        goods.currencyOrderPreferentialAllotTotal.toString(),
      );
      //总金额（零售金额）
      backSumAllTotal.sumTotal += MathUtil.parseToDecimal(
        goods.currencyTotal.toString(),
      );

      //最终金额
      backSumAllTotal.sumDisedTaxedTotal += MathUtil.parseToDecimal(
        goods.currencyDisedTaxedTotal.toString(),
      );

      //最终金额（含赠金）
      backSumAllTotal.sumPosDisedTaxedTotal += MathUtil.parseToDecimal(
        goods.posCurrencyDisedTaxedTotal.toString(),
      );

      //优惠分摊前的优惠金额
      backSumAllTotal.sumPreferential += MathUtil.parseToDecimal(
        goods.preferentialDiscount.toString(),
      );

      //最终优惠
      backSumAllTotal.sumFinalPreferential += MathUtil.parseToDecimal(
        goods.currencyPreferentialTotal.toString(),
      );

      //单品优惠金额
      backSumAllTotal.currencyPtypePreferentialTotal += MathUtil.parseToDecimal(
        goods.currencyPtypePreferentialTotal.toString(),
      );

      //赠金优惠金额
      backSumAllTotal.sumGivePreferentialTotal += MathUtil.parseToDecimal(
        goods.givePreferentialTotal.abs().toString(),
      );
    }
    return backSumAllTotal;
  }

  //endregion 单据汇总

  static String getPropName(GoodsDetailDto goodsDetailDto) {
    ///商品属性
    String propName = "";
    if (goodsDetailDto.prop != null) {
      for (PtypePropDto propDto in (goodsDetailDto.prop ?? [])) {
        if (propDto == goodsDetailDto.prop?.first) {
          propName = propDto.propvalueName ?? "";
        } else {
          propName += ":${propDto.propvalueName}";
        }
      }
    }
    return propName;
  }

  ///根据单位换算
  static String getStockQty(num? stock, num? unitRate, num? pcategory) {
    if (pcategory == 1) {
      return "0";
    }

    ///商品属性
    return DecimalDisplayHelper.getQtyFixed(
      ((stock ?? 0) / (unitRate ?? 1)).toString(),
    );
  }

  ///单据提交成功后回填服务端返回信息
  static void backFillBillAfterSubmit(
    PayResult? data,
    GoodsBillDto goodsBill,
    GoodsBillDto currentGoodsBill,
  ) {
    BillSaveResultDto? result = data?.resultDTO;
    if (null == result) {
      return;
    }
    goodsBill.number = result.billNumber;
    goodsBill.vchcode = result.vchcode;
    goodsBill.date = result.billDate;
    goodsBill.invoiceSign = result.invoiceSign;
    currentGoodsBill.number = result.billNumber;
    currentGoodsBill.vchcode = result.vchcode;
    currentGoodsBill.date = result.billDate;
    currentGoodsBill.invoiceSign = result.invoiceSign;
  }

  static Future<void> syncOfflineBillSubmit(
    BuildContext context, {
    Function? afterSyncSuccess,
  }) async {
    String profileId = LoginCenter.getLoginUser().profileId!;
    String? otypeId = SpTool.getStoreInfo()?.otypeId;

    // 只查询当前门店的离线单据
    List<GoodsBillDto> goods = await BillDBManager.selectSaleBill(
      profileId: profileId,
      otypeId: otypeId,
    );

    List<Map> request = [];
    debugPrint("length  bill  ${goods.length}");
    for (GoodsBillDto element in goods) {
      element.changeDate = false;
      var data = element.toJson();
      data["sourceTag"] = "POS";
      request.add({
        "cashierId": SpTool.getCashierInfo().id,
        "goodsBill": data,
        "printTimes": 0,
      });
    }
    if (context.mounted && request.isNotEmpty) {
      BillModel.saveGoodsBillList(context, request).then((value) {
        // 删除时也只删除当前门店的单据
        BillDBManager.deleteSaleBillList(
          profileId: profileId,
          vchcodeList: value,
          otypeId: otypeId,
        ).then((value) {
          if (null != afterSyncSuccess) {
            afterSyncSuccess();
          }
          HaloToast.showMsg(context, msg: "同步单据完成");
        });
      });
    } else {
      HaloToast.showMsg(context, msg: "无离线单据需要同步");
    }
  }

  ///判断商品是否在积分策略的商品分类范围内
  static bool _isGoodsInScoreConfigurationByCategory(
    GoodsDetailDto goods,
    ScoreConfiguration scoreConfiguration,
  ) {
    if (scoreConfiguration.ssVipScoreConfigurationPtype?.isEmpty != false) {
      return false;
    }

    // 获取配置的分类ID列表
    // 此方法只在scoreAccrualType=4时调用，ptypeClassId字段存储的是分类ID
    List<String> categoryIds =
        scoreConfiguration.ssVipScoreConfigurationPtype!
            .map(
              (element) => element.ptypeClassId ?? "",
            ) // 使用ptypeClassId作为分类ID
            .where((id) => id.isNotEmpty)
            .toList();

    if (categoryIds.isEmpty) {
      return false;
    }

    // 使用工具类判断商品是否在分类范围内
    return GoodsScopeUtil.isGoodsInCategoryList(goods, categoryIds);
  }

  ///判断商品是否在积分策略的商品标签范围内
  static bool _isGoodsInScoreConfigurationByLabel(
    GoodsDetailDto goods,
    ScoreConfiguration scoreConfiguration,
  ) {
    if (scoreConfiguration.ssVipScoreConfigurationPtype?.isEmpty != false) {
      return false;
    }

    // 获取配置的标签ID列表
    // 此方法只在scoreAccrualType=5时调用，ptypeId字段存储的是标签ID
    List<String> labelIds =
        scoreConfiguration.ssVipScoreConfigurationPtype!
            .map((element) => element.ptypeId ?? "") // 使用ptypeId作为标签ID
            .where((id) => id.isNotEmpty)
            .toList();

    if (labelIds.isEmpty) {
      return false;
    }

    // 使用工具类判断商品是否包含标签
    return GoodsScopeUtil.isGoodsHasAnyLabel(goods, labelIds);
  }
}

class BackSumAllTotal {
  ///商品数量
  Decimal sumQty = Decimal.zero;

  ///总金额
  Decimal sumTotal = Decimal.zero;

  ///优惠分摊前的优惠金额
  Decimal sumPreferential = Decimal.zero;

  ///最终优惠
  Decimal sumFinalPreferential = Decimal.zero;

  ///最终金额
  Decimal sumDisedTaxedTotal = Decimal.zero;

  ///最终金额（含赠金）
  Decimal sumPosDisedTaxedTotal = Decimal.zero;

  ///优惠分摊
  Decimal currencyOrderPreferentialAllotTotal = Decimal.zero;

  ///赠金优惠金额
  Decimal sumGivePreferentialTotal = Decimal.zero;

  ///单品优惠金额（促销优惠金额）汇总
  Decimal currencyPtypePreferentialTotal = Decimal.zero;
}
