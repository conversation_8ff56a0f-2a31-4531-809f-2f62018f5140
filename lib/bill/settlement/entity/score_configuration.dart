class ScoreConfiguration {
  ScoreConfiguration({
    this.id,
    this.profileId,
    this.createTime,
    this.updateTime,
    this.validityType,
    this.validitySetType,
    this.validitySetTime,
    this.validityDatailTime,
    this.validityDatailType,
    this.protectionPeriodType,
    this.protectionPeriodTime,
    this.discountingThresholdType,
    this.discountingThresholdNum,
    this.discountingLimitType,
    this.discountingLimitRmb,
    this.discountingLimitPercent,
    this.discountingProportion,
    this.discountingValue,
    this.presentedType,
    this.presentedDay,
    this.presentedMonth,
    this.presentedDirect,
    this.scoreAccrualType,
    this.ptypeFullName,
    this.ssVipScoreConfigurationPtype,
    this.ssVipScoreConfigurationFixedRule,
    this.discountingModel,
  });

  ScoreConfiguration.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    validityType = json['validityType'];
    validitySetType = json['validitySetType'];
    validitySetTime = json['validitySetTime'];
    validityDatailTime = json['validityDatailTime'];
    validityDatailType = json['validityDatailType'];
    protectionPeriodType = json['protectionPeriodType'];
    protectionPeriodTime = json['protectionPeriodTime'];
    discountingThresholdType = json['discountingThresholdType'];
    discountingThresholdNum = json['discountingThresholdNum'];
    discountingLimitType = json['discountingLimitType'];
    discountingLimitRmb = json['discountingLimitRmb'];
    discountingLimitPercent = json['discountingLimitPercent'];
    discountingProportion = json['discountingProportion'];
    discountingValue = json['discountingValue'];
    presentedType = json['presentedType'];
    presentedDay = json['presentedDay'];
    presentedMonth = json['presentedMonth'];
    presentedDirect = json['presentedDirect'];
    scoreAccrualType = json['scoreAccrualType'];
    ptypeFullName = json['ptypeFullName'];
    discountingModel = json['discountingModel'];

    if (json['ssVipScoreConfigurationPtype'] != null) {
      ssVipScoreConfigurationPtype = [];
      json['ssVipScoreConfigurationPtype'].forEach((v) {
        ssVipScoreConfigurationPtype?.add(
          SsVipScoreConfigurationPtype.fromJson(v),
        );
      });
    }
    if (json['ruleList'] != null) {
      ssVipScoreConfigurationFixedRule = [];
      json['ruleList'].forEach((v) {
        ssVipScoreConfigurationFixedRule?.add(
          SsVipScoreConfigurationFixedRule.fromJson(v),
        );
      });
    }
  }

  ///积分策略id
  String? id;

  ///账套id
  String? profileId;

  ///创建时间
  String? createTime;

  ///修改时间
  String? updateTime;

  ///积分有效期类型：
  /// 1、永久有效；
  /// 2、获得积分后固定时间到期；
  /// 3、每笔积分的有效期为xx天
  int? validityType;

  ///固定到期时间
  String? validitySetTime;

  ///每笔积分的有效期
  int? validityDatailTime;

  ///每笔积分的有效期类型:
  /// 1、年
  /// 2、月
  /// 3、天
  int? validityDatailType;

  /// 积分保护期类型：
  /// 1、无保护期；
  /// 2、获得积分后x天不能使用
  int? protectionPeriodType;

  ///积分保护期天数
  int? protectionPeriodTime;

  /// 积分抵现门槛类型：
  /// 1、无门槛；
  /// 2、最低消费金额x元
  int? discountingThresholdType;

  ///抵现门槛最低消费金额
  num? discountingThresholdNum;

  /// 积分可抵现上限类型：
  /// 1、无限制；
  /// 2、每笔交易最多抵扣x元；
  /// 3、每笔交易最多抵扣x%
  int? discountingLimitType;

  ///抵现（元）
  num? discountingLimitRmb;

  ///抵现（百分之）
  num? discountingLimitPercent;

  ///抵现比例（[discountingProportion]积分=[discountingValue]元）
  int? discountingProportion;

  ///抵现比例（[discountingProportion]积分=[discountingValue]元）
  num? discountingValue;

  /// 生日积分赠送类型：
  /// 1、不开启；
  /// 2、生日当天倍送x倍；
  /// 3、生日当月倍送x倍；
  /// 4、生日当天直接赠送x积分
  int? presentedType;

  ///生日当天赠送倍数
  num? presentedDay;

  ///生日当月赠送倍数
  num? presentedMonth;

  ///生日当天直接赠送积分
  int? presentedDirect;

  num? validitySetType;

  ///1全部,2指定,3排除 积分累计商品
  num? scoreAccrualType;

  String? ptypeFullName;

  ///累计商品信息
  List<SsVipScoreConfigurationPtype>? ssVipScoreConfigurationPtype;

  ///会员积分固定抵现规则
  List<SsVipScoreConfigurationFixedRule>? ssVipScoreConfigurationFixedRule;

  ///积分抵现方式
  int? discountingModel;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['validityType'] = validityType;
    map['validitySetType'] = validitySetType;
    map['validitySetTime'] = validitySetTime;
    map['validityDatailTime'] = validityDatailTime;
    map['validityDatailType'] = validityDatailType;
    map['protectionPeriodType'] = protectionPeriodType;
    map['protectionPeriodTime'] = protectionPeriodTime;
    map['discountingThresholdType'] = discountingThresholdType;
    map['discountingThresholdNum'] = discountingThresholdNum;
    map['discountingLimitType'] = discountingLimitType;
    map['discountingLimitRmb'] = discountingLimitRmb;
    map['discountingLimitPercent'] = discountingLimitPercent;
    map['discountingProportion'] = discountingProportion;
    map['discountingValue'] = discountingValue;
    map['presentedType'] = presentedType;
    map['presentedDay'] = presentedDay;
    map['presentedMonth'] = presentedMonth;
    map['presentedDirect'] = presentedDirect;
    map['scoreAccrualType'] = scoreAccrualType;
    map['ptypeFullName'] = ptypeFullName;
    map['discountingModel'] = discountingModel;
    if (ssVipScoreConfigurationPtype != null) {
      map['ssVipScoreConfigurationPtype'] =
          ssVipScoreConfigurationPtype?.map((v) => v.toJson()).toList();
    }
    if (ssVipScoreConfigurationFixedRule != null) {
      map['ruleList'] =
          ssVipScoreConfigurationFixedRule?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class SsVipScoreConfigurationPtype {
  SsVipScoreConfigurationPtype({
    this.id,
    this.profileId,
    this.createTime,
    this.updateTime,
    this.ptypeClassId,
    this.ptypeId,
    this.skuId,
    this.unitId,
    this.scoreConfigurationId,
    this.fullName,
    this.scoreAccrualType,
  });

  SsVipScoreConfigurationPtype.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    ptypeClassId = json['ptypeClassId'];
    ptypeId = json['ptypeId'];
    skuId = json['skuId'];
    unitId = json['unitId'];
    scoreConfigurationId = json['scoreConfigurationId'];
    fullName = json['fullName'];
    scoreAccrualType = json['scoreAccrualType'];
  }
  String? id;
  String? profileId;
  dynamic createTime;
  dynamic updateTime;
  String? ptypeClassId;
  String? ptypeId;
  String? skuId;
  String? unitId;
  String? scoreConfigurationId;
  String? fullName;

  ///参与积分累计商品范围（0=商品 4=商品分类 5=商品标签）
  int? scoreAccrualType;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['createTime'] = createTime;
    map['updateTime'] = updateTime;
    map['ptypeClassId'] = ptypeClassId;
    map['ptypeId'] = ptypeId;
    map['skuId'] = skuId;
    map['unitId'] = unitId;
    map['scoreConfigurationId'] = scoreConfigurationId;
    map['fullName'] = fullName;
    map['scoreAccrualType'] = scoreAccrualType;
    return map;
  }
}

class SsVipScoreConfigurationFixedRule {
  String id = "";
  String profileId = "";
  String scoreConfigurationId = "";
  int discountingProportion = 0;
  double discountingValue = 0;
  int rowIndex = 0;

  SsVipScoreConfigurationFixedRule({
    required this.id,
    required this.profileId,
    required this.scoreConfigurationId,
    required this.discountingProportion,
    required this.discountingValue,
    required this.rowIndex,
  });

  SsVipScoreConfigurationFixedRule.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    profileId = json['profileId'];
    scoreConfigurationId = json['scoreConfigurationId'];
    discountingProportion = json['discountingProportion'];
    discountingValue = json['discountingValue'];
    rowIndex = json['rowIndex'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['profileId'] = profileId;
    data['scoreConfigurationId'] = scoreConfigurationId;
    data['discountingProportion'] = discountingProportion;
    data['discountingValue'] = discountingValue;
    data['rowIndex'] = rowIndex;
    return data;
  }
}
