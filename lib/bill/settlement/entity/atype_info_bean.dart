import 'package:flutter/material.dart';
import '../../../iconfont/icon_font.dart';
import '../../../login/entity/store/store_payment.dart';

import '../../../common/keyboard_hidden.dart';

/// 创建时间：10/8/22
/// 作者：xiaotiaochong
/// 描述：

///多渠道支付bean
///用来记录
///1.此页面包含的支付方式id
///2.支付方式是否被选中
///3.该支付方式的金额
class AtypeInfoBean {
  // ///支付方式id
  // final String atypeId;
  //
  // ///支付方式名称
  // final String name;
  //
  // ///显示名称
  // final String viewName;

  ///支付方式
  StorePayway storePayway;

  ///支付方式金额
  String total;

  ///支付方式金额包含赠金
  String posTotal = "0";

  ///支付方式最大可退金额
  String maxTotal;

  ///是否被启用
  bool enable = false;

  ///是否可编辑
  bool modify = false;

  // ///iconsName
  // IconNames iconNames;

  // ///是否是储值，特殊支付方式
  // bool isStoreTotal;

  final KeyboardHiddenFocusNode focusNode = KeyboardHiddenFocusNode();

  ///记录三方交易开发者流水号
  String? payOutNo;

  ///记录三方交易流水号
  String? payOrderNo;

  AtypeInfoBean(this.storePayway, this.total,
      {this.modify = false,
      this.enable = false,
      this.maxTotal = "0",
      this.posTotal = "0"});

  IconNames get iconNames {
    IconNames names;
    switch (storePayway.paywayType) {
      case 0:
        names = IconNames.xianjin;
        break;
      case 1:
        names = IconNames.yinhangka;
        break;
      case 2:
        names = IconNames.juhe;
        break;
      case 3:
        names = IconNames.chuzhi;
        break;
      default:
        names = IconNames.xianjin;
        break;
    }
    return names;
  }

  AtypeInfoBean copyWith({
    StorePayway? storePayway,
    String? total,
    String? posTotal,
    String? maxTotal,
    bool? enable,
    bool? modify,
    String? payOutNo,String? payOrderNo
  }) {
    return AtypeInfoBean(
      storePayway ?? this.storePayway,
      total ?? this.total,
      posTotal: posTotal ?? this.posTotal,
      maxTotal: maxTotal ?? this.maxTotal,
      enable: enable ?? this.enable,
      modify: modify ?? this.modify,
    )..payOutNo = payOutNo ?? this.payOutNo
      ..payOrderNo = payOrderNo??this.payOrderNo;
  }
}
