import '../../../login/entity/store/store_payment.dart';
import '../../entity/goods_bill.dto.dart';

/// 创建时间：2023/10/10
/// 作者：xiaotiaochong
/// 描述：

class PaymentGoodsBillDto {
  List<StorePayway> storePaywayList;
  GoodsBillDto goodsBillDto;

  PaymentGoodsBillDto(this.storePaywayList, this.goodsBillDto);

  static PaymentGoodsBillDto fromJson(dynamic json) {
    List? storePaywayListMap = json["storePaywayList"];
    Map<String, dynamic> goodsBillMap = json["goodsBill"] ?? {};
    List<StorePayway> storePaywayList =
        storePaywayListMap?.map((e) => StorePayway.fromMap(e)).toList() ?? [];
    GoodsBillDto goodsBillDto = GoodsBillDto.fromMap(goodsBillMap);
    PaymentGoodsBillDto paymentGoodsBillDto =
        PaymentGoodsBillDto(storePaywayList, goodsBillDto);
    return paymentGoodsBillDto;
  }
}
