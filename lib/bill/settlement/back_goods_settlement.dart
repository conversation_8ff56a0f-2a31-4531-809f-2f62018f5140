import 'package:flutter/material.dart';

import '../../enum/bill_type.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../entity/goods_bill.dto.dart';
import 'sale_bill_settlement.dart';
import 'settlement_mixin.dart';

///按商品退货结算界面
class BackGoodsSettlementPage extends SaleBillSettlementPage {
  const BackGoodsSettlementPage(
      {Key? key,
      required GoodsBillDto goodsBillDto,
      required Function successCallback})
      : super(
            key: key,
            goodsBillDto: goodsBillDto,
            successCallback: successCallback);

  @override
  BaseStatefulPageState<BackGoodsSettlementPage> createState() =>
      _BackGoodsSettlementPageState();
}

class _BackGoodsSettlementPageState
    extends SaleBillSettlementState<BackGoodsSettlementPage> {
  @override
  BillType get billType => BillType.SaleBackBill;

  ///是否展示组合支付
  @override
  bool get showCombinationPay => true;

  ///初始化支付方式
  @override
  void initPaywayList() {
    paywayList = filterPayWayList(isOffline: true);
  }
}
