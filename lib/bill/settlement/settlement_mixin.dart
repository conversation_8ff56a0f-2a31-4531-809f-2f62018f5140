import 'dart:math';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/common/standard.dart';
import 'package:halo_pos/common/tool/system_config_tool.dart';
import 'package:halo_pos/enum/bill_decimal_type.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../common/tool/sp_tool.dart';
import '../../enum/bill_type.dart';
import '../../login/entity/store/store_etype.dart';
import '../../login/entity/store/store_payment.dart';
import '../../offline/offline_tool.dart';
import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../tool/sms_verification_tool.dart';
import '../tool/store_tool.dart';
import 'entity/atype_info_bean.dart';
import 'widget/payment_widget.dart';

@pragma('vm:entry-point')
List<StorePayway> filterPayWayList(
    {bool enableStore = true, bool isOffline = false}) {
  final paywayList = SpTool.getStoreInfo()!.paywayList!;
  if (!enableStore || isOffline) {
    paywayList.removeWhere((element) {
      if (!enableStore && element.paywayType == 3) {
        return true;
      }
      //离线登录，不支持储值和扫码支付
      if (isOffline) {
        if (element.paywayType == 2 || element.paywayType == 3) {
          return true;
        }
      }
      return false;
    });
  }
  return paywayList;
}

mixin SettlementMixin<T extends StatefulWidget> on State<T> {
  ///是否展示营业员选择
  bool showChooseEtype = true;

  ///会员信息资产等
  VipWithLevelAssertsRightsCardDTO? vipInfo;

  ///是否需要验证会员消费密码
  bool isValidVipPwd = false;

  ///经手人
  StoreEtype? etype;

  ///应收金额
  num moneyReceived = 0;

  ///所有付款金额相加
  num finalGetMoney = 0;

  ///找零
  num pettyCash = 0;

  ///是否打印小票
  bool print = false;

  ///是否开启组合支付
  bool combinationPay = false;

  ///是否是退款
  bool isRefund = false;

  ///是否可以使用储值(会员)，充值不允许使用储值支付
  bool enableStore = true;

  ///是否显示组合支付
  bool showCombinationPay = true;

  ///支付方式数据源
  List<StorePayway> paywayList = [];

  ///支付方式
  List<AtypeInfoBean> paymentList = [];

  ///初始化支付方式时选中单个支付方式的优先级
  ///从小到大排序
  Map<int, int> get initPaymentPriority => {
        3: 0, //储值
        2: 1, //聚合支付
        4: 2, //其他支付方式
        0: 3, //现金
        1: 4, //银行卡
      };

  Future<void> init() async {
    initPaywayList();
    initPayment();
    initEtype();
    initPrint();
    refreshMoney(false);
  }

  ///初始化经手人
  void initEtype() {
    //默认营业员
    etype = StoreEtype()
      ..etypeId = SpTool.getEtypeId()
      ..etypeName = SpTool.getEtypeName();
  }

  void initPrint() {
    this.print = SpTool.getPrint();
  }

  ///初始化支付方式
  void initPaywayList() {
    paywayList = filterPayWayList(
        enableStore: enableStore, isOffline: OffLineTool().isOfflineLogin);
  }

  ///初始化支付方式
  void initPayment() {
    for (int i = 0; i < paywayList.length; i++) {
      StorePayway payWay = paywayList[i];
      //屏蔽储值支付
      if (payWay.paywayType == 3 && !enableStore) {
        continue;
      }
      final payment = AtypeInfoBean(payWay, "0", enable: false, modify: false);
      //只有现金支付方式可以编辑（非组合支付情况下）
      payment.modify = payment.storePayway.paywayType == 0;
      //一般最大值都是应收金额
      num maxValue;
      //对于非现金支付方式，需要限制其录入的最大值
      if (payWay.paywayType != 0) {
        maxValue = moneyReceived;
        //对于储值，根据储值余额取最小值
        if (payWay.paywayType == 3) {
          maxValue = min(maxValue, vipInfo?.asserts?.totalMoney ?? 0);
        }
      } else {
        maxValue = double.maxFinite;
      }
      payment.maxTotal = maxValue.toString();
      paymentList.add(payment);
    }
    //进入结算页面默认是选中单个支付方式，并且填入对应应收金额
    //这里给支付方式排序，然后通过此变量判断是否已经有启用的支付方式
    paymentList
        .sortedBy<num>(
            (e) => initPaymentPriority[e.storePayway.paywayType] ?? 999)
        .firstOrNull
        ?.let((payment) => initChoseSinglePayment(payment: payment));
  }

  ///初始化时，选中单个支付方式
  void initChoseSinglePayment({required AtypeInfoBean payment, num? total}) {
    total ??= moneyReceived;
    num? maxTotal = num.tryParse(payment.maxTotal);
    if (maxTotal != null && maxTotal >= 0) {
      total = min(total, maxTotal);
    }
    payment.total = total.toString();
    payment.posTotal = payment.total;
    //对于储值支付，根据根据本金赠金比例，分别给[total]和[posTotal]赋值
    if (payment.storePayway.paywayType == 3) {
      Decimal proportion = getStoreProportion();
      payment.total = SystemConfigTool.doubleMultiple(
          total.toString(), proportion.toString(), BillDecimalType.TOTAL);
    }
    payment.enable = true;
  }

  ///获取储值本金比率
  Decimal getStoreProportion() {
    if (vipInfo?.asserts == null) return Decimal.zero;
    return MathUtil.divideDec(
        vipInfo!.asserts!.chargeTotal ?? 0, vipInfo!.asserts!.totalMoney ?? 0);
  }

  ///刷新收到的款项
  void refreshMoney([bool refreshView = true]) {
    finalGetMoney = paymentList.where((element) => element.enable).fold(
        0,
        (previousValue, element) =>
            MathUtil.add(previousValue.toString(), element.posTotal)
                .toDouble());
    pettyCash = MathUtil.subtractDec(finalGetMoney, moneyReceived).toDouble();
    //现在退款要求应付金额和实付金额必须一致，所以找零金额为0
    // if (isRefund) {
    //   pettyCash = -pettyCash;
    // }
    if (refreshView) {
      setState(() {});
    }
  }

  Widget buildSettlementWidget() {
    return PaymentWidget(
      showChooseEtype: showChooseEtype,
      print: print,
      isRefund: isRefund,
      etype: etype,
      combinationPay: combinationPay,
      showCombinationPay: showCombinationPay,
      moneyReceived: moneyReceived.toString(),
      pettyCash: pettyCash.toString(),
      finalGetMoney: finalGetMoney.toString(),
      paymentList: paymentList,
      storeTotal: (vipInfo?.asserts?.totalMoney ?? 0).toString(),
      doSubmitAction: onSubmitClick,
      doTipsAction: onTipsClick,
      onEditChange: onPaymentChange,
    );
  }

  ///点击确定按钮
  void onSubmitClick() {
    if (!checkNext()) {
      return;
    }
    submit();
  }

  ///通过验证，开始下一步
  Future<void> submit() async {}

  ///点击了提示按钮
  void onTipsClick() {}

  ///支付方式改变（paymentWidget回调）
  void onPaymentChange(PaymentEditChange editChange) {
    paymentList = editChange.paymentList;
    combinationPay = editChange.combiPay;
    print = editChange.print;
    etype = editChange.etype;
    refreshMoney();
  }

  ///验证支付方式
  bool checkNext() {
    if (moneyReceived > finalGetMoney) {
      HaloToast.show(context, msg: "还未收齐款项，请检查付款金额");
      return false;
    }
    if (!checkVipPwd()) {
      return false;
    }
    return true;
  }

  bool checkVipPwd() {
    if (vipInfo == null) return true;
    //非退款的情况下，如果使用了储值支付，则需要验证密码
    if (!isRefund &&
        paymentList.any((payment) =>
            payment.enable && payment.storePayway.paywayType == 3)) {
      if (!checkVipPwdSet()) {
        return false;
      }
      if (!validVipPwd()) {
        return false;
      }
    }
    return true;
  }

  ///校验会员是否设置密码
  bool checkVipPwdSet() {
    if ((SpTool.getStoreInfo()?.openStoreValuePassword ?? false) &&
        vipInfo != null &&
        vipInfo?.vip?.password?.isEmpty == true &&
        SpTool.getStoreInfo()?.passwordType == 0) {
      StoreTool.showStorePageSet(context, vipInfo?.vip?.id ?? "", (value) {
        vipInfo?.vip?.password = value;
        isValidVipPwd = true;
        onSubmitClick();
      });
      return false;
    }
    return true;
  }

  ///验证会员支付密码
  bool validVipPwd() {
    if ((SpTool.getStoreInfo()?.openStoreValuePassword ?? false) &&
        !isValidVipPwd &&
        vipInfo != null) {
      //判断是固定密码还是短信验证
      if (SpTool.getStoreInfo()?.passwordType == 0) {
        StoreTool.showStorePageValid(
            context, vipInfo?.vip?.id ?? "", vipInfo?.vip?.phone, (value) {
          if (value == true) {
            isValidVipPwd = true;
            onSubmitClick();
          }
        });
      } else {
        SMSTool.showSMSDialog(context, vipInfo?.vip?.phone ?? "", (value) {
          if (value == true) {
            isValidVipPwd = true;
            onSubmitClick();
          }
        }, title: "短信验证");
      }
      return false;
    }
    return true;
  }

  ///获取启用的支付方式，并且现金支付方式需要减去找零金额
  ///[removeZero] 是否要移除金额为0的支付方式(对于开单不需要)
  List<AtypeInfoBean> getEnablePaymentSubtractPettyCash(
      {bool removeZero = false}) {
    return paymentList
        .where((element) =>
            element.enable &&
            (!removeZero || StringUtil.isNotZeroOrEmpty(element.total)))
        .map((e) {
      final copy = e.copyWith();
      //现金,根据找零金额计算实际金额
      if (copy.storePayway.paywayType == 0) {
        copy.total =
            MathUtil.subtraction(copy.total, pettyCash.toString()).toString();
        copy.posTotal = copy.total;
      }
      return copy;
    }).toList(growable: false);
  }
}
