import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/iconfont/icon_font.dart';
import 'package:haloui/widget/halo_radio.dart';
import 'dart:math' as math;
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

import '../../../bill/widget/ptype/base_goods_dialog.dart';
import '../../../common/standard.dart';
import '../../../iconfont/icon_font.dart';
import '../../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../entity/goods_bill.dto.dart';
import '../../entity/preferential_dto.dart';
import '../../tool/promotion/preferential.dart';
import '../../tool/promotion/score.dart';
import '../entity/score_configuration.dart';

///使用积分抵扣弹窗
class UseScoreDialog extends StatefulWidget {
  ///是否使用全部抵现
  final bool useAllScore;

  ///当前抵现金额
  final int userScore;

  ///积分策略
  final ScoreConfiguration scoreConfiguration;

  ///单据信息
  final GoodsBillDto goodsBillDto;

  ///会员信息
  final VipWithLevelAssertsRightsCardDTO vipInfo;

  ///确认回调
  final Function(int userScore, bool useAllScore) scoreChange;

  const UseScoreDialog({
    Key? key,
    required this.goodsBillDto,
    required this.vipInfo,
    required this.useAllScore,
    required this.userScore,
    required this.scoreConfiguration,
    required this.scoreChange,
  }) : super(key: key);

  @override
  State<UseScoreDialog> createState() => _UseScoreDialogState();
}

/// 积分抵现场景
enum ScoreDiscountScenario {
  /// 不足最低抵现积分
  insufficientScore,

  /// 按比例抵现-全部使用
  proportionalAll,

  /// 按比例抵现-部分使用
  proportionalPartial,

  /// 固定阶梯抵现-正常选择
  fixedTierNormal,

  /// 固定阶梯抵现-不满足抵现要求
  fixedTierInsufficientAmount,

  /// 固定阶梯抵现-超出订单金额
  fixedTierExceedAmount,
}

class _UseScoreDialogState extends State<UseScoreDialog> {
  /// 当前使用的积分
  int userScore = 0;

  /// 是否使用全部积分
  bool isAllScore = false;

  /// 最大可用积分
  int maxScore = 0;

  /// 订单总金额（计算优惠前）
  Decimal totalBeforeScoreDiscount = Decimal.zero;

  /// 固定阶梯规则列表
  List<SsVipScoreConfigurationFixedRule> rules = [];

  /// 当前抵现场景
  ScoreDiscountScenario currentScenario =
      ScoreDiscountScenario.proportionalPartial;

  /// 最低抵现积分
  late num minThresholdScore;

  /// 最低抵现金额
  late Decimal minThresholdAmount;

  TextEditingController scoreController = TextEditingController();
  final FocusNode _scoreFocusNode = FocusNode();
  final ScrollController _gridViewController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 初始化时使用传入的状态
    isAllScore = widget.useAllScore;
    userScore = widget.userScore;
    scoreController.text =
        widget.userScore > 0 ? widget.userScore.toString() : "";
    _initializeScoreDiscount();

    // 如果是固定阶梯模式，确保应用了交易限制
    if (widget.scoreConfiguration.discountingModel != 0) {
      Future.delayed(Duration.zero, () {
        jumpToIndex();
      });
    }

    // 在按比例抵现模式下，并且没有使用全部抵现时，自动聚焦到输入框
    if (widget.scoreConfiguration.discountingModel == 0 && !isAllScore) {
      Future.delayed(Duration.zero, () {
        _scoreFocusNode.requestFocus();
      });
    }
  }

  /// 初始化积分抵现数据
  void _initializeScoreDiscount() {
    // 计算订单总金额（优惠前）
    totalBeforeScoreDiscount =
        BillPreferentialTool.getBillTotalBeforeBillPreferential(
            widget.goodsBillDto, Preferential.scoreDiscount);

    // 初始化最低抵现要求
    _initializeThresholds();

    // 根据抵现模式初始化数据
    if (widget.scoreConfiguration.discountingModel == 0) {
      _initializeProportionalDiscount();
    } else {
      _initializeFixedTierDiscount();
    }

    // 确定当前抵现场景
    _determineCurrentScenario();
  }

  /// 初始化最低抵现要求
  void _initializeThresholds() {
    final availableScore = widget.vipInfo.asserts?.availableScore ?? 0;

    if (widget.scoreConfiguration.discountingModel == 0) {
      // 按比例抵现模式
      // 1. 计算最小有效抵现金额对应的积分
      // 根据 0.01元 反推所需最小积分数
      minThresholdScore = (Decimal.parse('0.01') *
              Decimal.fromInt(
                  widget.scoreConfiguration.discountingProportion!) /
              Decimal.parse(
                  widget.scoreConfiguration.discountingValue!.toString()))
          .toDouble()
          .ceil();

      // 2. 如果配置中的最低抵现积分大于按0.01计算的积分，则使用配置中的值
      if ((widget.scoreConfiguration.discountingThresholdNum ?? 0) >
          minThresholdScore) {
        minThresholdScore = widget.scoreConfiguration.discountingThresholdNum!;
      }

      // 3. 计算最低抵现积分对应的金额
      num minDiscountMoney = ScoreDiscountUtil.getMoneyByScore(
          minThresholdScore.toInt(), widget.scoreConfiguration);
      minThresholdAmount = Decimal.parse(minDiscountMoney.toString());
    } else {
      // 固定阶梯模式
      // 1. 获取最小的固定阶梯规则
      final minRule = widget.scoreConfiguration.ssVipScoreConfigurationFixedRule
          ?.reduce((a, b) =>
              (a.discountingValue ?? 0) < (b.discountingValue ?? 0) ? a : b);

      if (minRule != null) {
        minThresholdScore = minRule.discountingProportion ?? 0;
        minThresholdAmount =
            Decimal.parse((minRule.discountingValue ?? 0).toString());
      } else {
        minThresholdScore = 0;
        minThresholdAmount = Decimal.zero;
      }
    }
  }

  /// 初始化按比例抵现数据
  void _initializeProportionalDiscount() {
    // 如果是首次进入（没有之前的积分记录），则不计算最大积分
    if (widget.userScore == 0) {
      userScore = 0;
      scoreController.text = "";
    } else {
      // 如果有之前的记录，恢复之前的状态，但需要验证是否仍然有效
      _getMaxScore();
      // 确保之前使用的积分不超过当前计算出的最大可用积分
      userScore = math.min(widget.userScore, maxScore);
      scoreController.text = userScore.toString();
    }
  }

  /// 初始化固定阶梯抵现数据
  void _initializeFixedTierDiscount() {
    // 如果是首次进入（没有之前的积分记录），则不选择任何方案
    if (widget.userScore == 0) {
      userScore = 0;
    } else {
      // 如果有之前的记录，恢复之前选择的方案
      userScore = widget.userScore;
    }
    _initFixedRules();
  }

  /// 确定当前抵现场景
  void _determineCurrentScenario() {
    final availableScore = widget.vipInfo.asserts?.availableScore ?? 0;

    // 检查是否满足最低抵现积分要求
    if (availableScore < minThresholdScore) {
      currentScenario = ScoreDiscountScenario.insufficientScore;
      return;
    }

    // 检查是否可以进行有效抵现
    bool canDiscount = false;
    if (widget.scoreConfiguration.discountingModel == 0) {
      // 按比例抵现模式：检查最小抵现金额是否大于等于0.01
      num minDiscountMoney = ScoreDiscountUtil.getMoneyByScore(
          minThresholdScore.toInt(), widget.scoreConfiguration);
      canDiscount = minDiscountMoney >= 0.01;
    } else {
      // 固定阶梯模式：检查是否有可用的抵现规则
      canDiscount = rules.isNotEmpty;
    }

    if (!canDiscount) {
      currentScenario = ScoreDiscountScenario.fixedTierInsufficientAmount;
      return;
    }

    // 设置正常抵现场景
    if (widget.scoreConfiguration.discountingModel == 0) {
      currentScenario = isAllScore
          ? ScoreDiscountScenario.proportionalAll
          : ScoreDiscountScenario.proportionalPartial;
    } else {
      currentScenario = ScoreDiscountScenario.fixedTierNormal;
    }
  }

  /// 获取最大可用积分
  void _getMaxScore() {
    // 获取基于用户可用积分的最大可用积分
    maxScore = ScoreDiscountUtil.getMaxUsableScore(true,
        totalBeforeScoreDiscount, widget.scoreConfiguration, widget.vipInfo);

    // 确保maxScore不为0，即使是极小金额也至少可以使用1积分
    if (maxScore == 0 &&
        totalBeforeScoreDiscount > Decimal.zero &&
        widget.vipInfo.asserts?.availableScore != null &&
        widget.vipInfo.asserts!.availableScore! > 0) {
      maxScore = 1;
    }

    // 获取最大可抵扣金额（考虑所有限制因素）
    Decimal maxAllowedAmount = ScoreDiscountUtil.getScoreConfigurationMinMoney(
        totalBeforeScoreDiscount, widget.scoreConfiguration);

    // 特殊处理边界金额（接近整数但略大于整数的金额，如20.01元）
    // 这种情况下允许多使用一点积分来完全抵扣
    // 但只有在maxAllowedAmount为0或不存在限制的情况下才执行
    if (widget.scoreConfiguration.discountingModel == 0 &&
        (widget.vipInfo.asserts?.availableScore ?? 0) > maxScore &&
        (maxAllowedAmount == Decimal.zero ||
            widget.scoreConfiguration.discountingLimitType ==
                ScoreDiscountLimitType.none.value)) {
      // 计算完全抵扣所需的理论积分值（不取整）
      double exactScoreNeeded = maxAllowedAmount.toDouble() /
          widget.scoreConfiguration.discountingValue! *
          widget.scoreConfiguration.discountingProportion!;

      // 如果存在小数部分，且用户有足够积分，允许多使用一点积分
      double fractionalPart = exactScoreNeeded - exactScoreNeeded.floor();
      if (fractionalPart > 0 && fractionalPart < 0.5) {
        // 计算原始的最大积分（考虑了交易抵扣限制）
        int originalMaxScore = exactScoreNeeded.floor();

        // 检查用户的可用积分是否足够多使用1积分，且加1后不超过交易限制
        if ((widget.vipInfo.asserts?.availableScore ?? 0) >=
            originalMaxScore + 1) {
          // 确保不超过ScoreDiscountUtil.getMaxUsableScore()返回的最大值
          // 这保证了即使添加额外的1积分也不会突破交易抵扣限制
          maxScore = math.min(
              maxScore + 1,
              ScoreDiscountUtil.getScoreByMoney(
                      maxAllowedAmount, widget.scoreConfiguration) +
                  1);
        }
      }
    }
  }

  ///计算订单完全抵扣所需的积分（向上取整）
  int _getRequiredScoreForFullDiscount() {
    if (widget.scoreConfiguration.discountingModel != 0) {
      return maxScore; // 对于固定阶梯模式，返回最大可用积分
    }

    // 获取考虑交易抵扣限制的最大金额
    Decimal maxAllowedAmount = ScoreDiscountUtil.getScoreConfigurationMinMoney(
        totalBeforeScoreDiscount, widget.scoreConfiguration);

    // 计算完全抵扣所需的积分（向上取整，允许损失部分积分价值）
    double scoreNeeded = maxAllowedAmount.toDouble() /
        widget.scoreConfiguration.discountingValue! *
        widget.scoreConfiguration.discountingProportion!;

    // 确保即使是极小金额也至少需要1积分
    int result = scoreNeeded.ceil();
    return result > 0 ? result : 1; // 保证至少返回1积分
  }

  /// 初始化固定阶梯规则列表
  void _initFixedRules() {
    // 获取考虑所有限制因素（包括discountingLimitType）的限制金额
    Decimal maxAllowedAmount = ScoreDiscountUtil.getScoreConfigurationMinMoney(
        totalBeforeScoreDiscount, widget.scoreConfiguration);

    // 获取可用的固定阶梯规则列表
    rules = ScoreDiscountUtil.getFixedRatioList(
        widget.scoreConfiguration,
        widget.vipInfo.asserts?.availableScore ?? 0,
        maxAllowedAmount.toDouble());
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Center(
        child: Container(
          width:
              widget.scoreConfiguration.discountingModel == 0 ? 800.w : 920.w,
          constraints: BoxConstraints(maxHeight: 0.8.sh),
          decoration: BoxDecoration(
            color: const Color(0xFFEDEDED),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              SizedBox(height: 20.w),
              _buildScoreInfo(),
              SizedBox(height: 20.w),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildContent(),
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 17.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '积分抵现',
            style: TextStyle(
              fontSize: 26.sp,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF333333),
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: IconFont(IconNames.close, size: 30.w),
          ),
        ],
      ),
    );
  }

  Widget _buildScoreInfo() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 27.w),
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '剩余积分:',
                style: TextStyle(
                  fontSize: 22.sp,
                  color: ColorUtil.stringColor("#666666"),
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                '${widget.vipInfo.asserts?.availableScore ?? 0}',
                style: TextStyle(
                  fontSize: 22.sp,
                  fontWeight: FontWeight.w500,
                  color: ColorUtil.stringColor("#333333"),
                ),
              ),
            ],
          ),
          if (widget.scoreConfiguration.discountingModel == 0) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Text(
                  '抵现规则:',
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: ColorUtil.stringColor("#666666"),
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  '${widget.scoreConfiguration.discountingProportion}积分=¥${widget.scoreConfiguration.discountingValue}',
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: ColorUtil.stringColor("#666666"),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildContent() {
    switch (currentScenario) {
      case ScoreDiscountScenario.insufficientScore:
        return _buildInsufficientScoreContent();
      case ScoreDiscountScenario.proportionalAll:
      case ScoreDiscountScenario.proportionalPartial:
        return _buildProportionalDiscountContent();
      case ScoreDiscountScenario.fixedTierNormal:
        return _buildFixedTierDiscountContent();
      case ScoreDiscountScenario.fixedTierInsufficientAmount:
        return _buildFixedTierInsufficientAmountContent();
      case ScoreDiscountScenario.fixedTierExceedAmount:
        return _buildFixedTierDiscountContent();
    }
  }

  Widget _buildInsufficientScoreContent() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: HaloContainer(
        direction: Axis.vertical,
        mainAxisSize: MainAxisSize.max,
        children: [
          SizedBox(height: 77.h),
          Container(
            padding: EdgeInsets.symmetric(vertical: 40.h),
            child: Column(
              children: [
                IconFont(
                  IconNames.jifenbumanzu,
                  size: 80.w,
                ),
                SizedBox(height: 27.h),
                Text(
                  '当前无满足条件的可用抵现规则',
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: ColorUtil.stringColor("#333333"),
                  ),
                ),
                SizedBox(height: 10.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFixedTierInsufficientAmountContent() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        children: [
          SizedBox(height: 77.h),
          Container(
            padding: EdgeInsets.symmetric(vertical: 40.h),
            child: Column(
              children: [
                IconFont(
                  IconNames.jifenbumanzu,
                  size: 80.w,
                ),
                SizedBox(height: 27.h),
                Text(
                  '订单金额不满足最低抵现要求',
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: ColorUtil.stringColor("#333333"),
                  ),
                ),
                SizedBox(height: 10.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProportionalDiscountContent() {
    return Column(
      children: [
        SizedBox(height: 18.h),
        _buildScoreInput(),
        SizedBox(height: 9.h),
        _buildDiscountAmount(),
        _buildNumberKeyboard(),
      ],
    );
  }

  Widget _buildFixedTierDiscountContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        _buildFixedTierList(),
      ],
    );
  }

  Widget _buildFooter() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12.r),
          bottomRight: Radius.circular(12.r),
        ),
      ),
      child: Column(
        children: [
          SizedBox(height: 20.h),
          Container(
            height: 100.h,
            padding: EdgeInsets.all(15.h),
            child: HaloButton(
              text: '确定',
              backgroundColor: ColorUtil.stringColor("#4679FC"),
              width: double.infinity,
              height: 70.h,
              fontSize: 26.sp,
              borderRadius: 8.r,
              onPressed: _handleConfirm,
            ),
          ),
        ],
      ),
    );
  }

  void _handleConfirm() {
    if (!checkDiscountMoney()) {
      return;
    }

    if (widget.scoreConfiguration.discountingModel == 0) {
      // 按比例抵现模式
      if (userScore > (widget.vipInfo.asserts?.availableScore ?? 0)) {
        HaloToast.show(context, msg: "使用积分不能超过可用积分");
        return;
      }

      if (userScore > 0 && userScore < minThresholdScore) {
        HaloToast.show(context, msg: "使用积分不能低于最低抵现要求");
        return;
      }

      // 检查抵现金额是否超过所有限制
      Decimal maxAllowedAmount =
          ScoreDiscountUtil.getScoreConfigurationMinMoney(
              totalBeforeScoreDiscount, widget.scoreConfiguration);
      num discountMoney = ScoreDiscountUtil.getMoneyByScore(
              userScore, widget.scoreConfiguration)
          .toDouble();

      // if (Decimal.parse(discountMoney.toString()) > maxAllowedAmount) {
      //   HaloToast.show(context, msg: "抵现金额超过限制");
      //   // 重新计算符合限制的积分
      //   int newScore = ScoreDiscountUtil.getScoreByMoney(
      //       maxAllowedAmount, widget.scoreConfiguration);
      //   setState(() {
      //     userScore = newScore;
      //     scoreController.text = newScore.toString();
      //   });
      //   return;
      // }
    } else {
      // 固定阶梯模式
      if (totalBeforeScoreDiscount < minThresholdAmount) {
        HaloToast.show(context, msg: "订单金额不满足最低抵现要求");
        return;
      }

      // 允许userScore为0，表示不使用积分抵扣
      // 注释掉以下代码
      // if (userScore == 0) {
      //   HaloToast.show(context, msg: "请选择抵现规则");
      //   return;
      // }

      // 检查所选规则是否满足所有抵扣限制
      Decimal maxAllowedAmount =
          ScoreDiscountUtil.getScoreConfigurationMinMoney(
              totalBeforeScoreDiscount, widget.scoreConfiguration);
      num discountMoney = ScoreDiscountUtil.getFixedRatioMoneyForScore(
              userScore, widget.scoreConfiguration)
          .toDouble();

      if (Decimal.parse(discountMoney.toString()) > maxAllowedAmount) {
        HaloToast.show(context, msg: "所选规则超过抵扣限制");
        return;
      }
    }

    // 计算抵现金额
    num discountMoney = widget.scoreConfiguration.discountingModel == 0
        ? ScoreDiscountUtil.getMoneyByScore(
                userScore, widget.scoreConfiguration)
            .toDouble()
        : ScoreDiscountUtil.getFixedRatioMoneyForScore(
                userScore, widget.scoreConfiguration)
            .toDouble();

    widget.scoreChange(userScore, isAllScore);
    Navigator.pop(context);
  }

  bool checkDiscountMoney() {
    if (widget.scoreConfiguration.discountingModel == 0) {
      num discountMoney = ScoreDiscountUtil.getMoneyByScore(
              userScore, widget.scoreConfiguration)
          .toDouble();

      // 验证最低抵现要求
      if (userScore > 0 && userScore < minThresholdScore) {
        HaloToast.show(context,
            msg: "积分不能低于最低抵现要求${minThresholdScore.toInt()}");
        setState(() {
          scoreController.text = "";
          userScore = 0;
        });
        return false;
      }

      // 不再限制抵现金额不能超过订单金额，因为我们允许部分积分价值损失
    }
    return true;
  }

  ///设置全部积分
  setAllScore(bool value) {
    _getMaxScore();

    if (!value) {
      // 如果取消全部抵现，直接清零
      userScore = 0;
      scoreController.text = "";
      getScoreDiscountMoney();
      setState(() {
        isAllScore = false;
      });
      return;
    }

    // 直接使用全部可用积分
    userScore = maxScore;

    scoreController.text = userScore.toString();
    getScoreDiscountMoney();
    setState(() {
      isAllScore = true;
    });
  }

  ///计算积分抵用金额
  String getScoreDiscountMoney() {
    // 计算理论抵现金额
    num theoreticalMoney = widget.scoreConfiguration.discountingModel == 0
        ? ScoreDiscountUtil.getMoneyByScore(
                userScore, widget.scoreConfiguration)
            .toDouble()
        : ScoreDiscountUtil.getFixedRatioMoneyForScore(
                userScore, widget.scoreConfiguration)
            .toDouble();

    // 如果理论抵现金额超过订单金额，则显示订单金额
    if (Decimal.parse(theoreticalMoney.toString()) > totalBeforeScoreDiscount) {
      return totalBeforeScoreDiscount.toStringAsFixed(2);
    }

    return theoreticalMoney.toStringAsFixed(2);
  }

  ///计算剩余积分
  String getLeftScore() {
    int leftScore = 0;
    leftScore = widget.vipInfo.asserts!.availableScore! - userScore;
    return "剩余积分：$leftScore";
  }

  void _onClose() {
    Navigator.pop(context);
  }

  void jumpToIndex() {
    totalBeforeScoreDiscount =
        BillPreferentialTool.getBillTotalBeforeBillPreferential(
            widget.goodsBillDto, Preferential.scoreDiscount);

    // 获取考虑所有限制因素（包括discountingLimitType）的限制金额
    Decimal maxAllowedAmount = ScoreDiscountUtil.getScoreConfigurationMinMoney(
        totalBeforeScoreDiscount, widget.scoreConfiguration);

    ///获取积分抵扣比例列表
    rules = ScoreDiscountUtil.getFixedRatioList(
        widget.scoreConfiguration,
        widget.vipInfo.asserts?.availableScore ?? 0,
        maxAllowedAmount.toDouble());

    ///选中index
    int index = rules
        .indexWhere((element) => element.discountingProportion == userScore);

    /// 页面加载完成后自动滚动到指定位置
    if (rules.isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 300), () {
        scrollToOffset(
          index ~/ 2 * 50.w,
        );
      });
    }
  }

  void scrollToOffset(double offset) {
    _gridViewController.animateTo(
      offset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.ease,
    );
  }

  void _onScoreChanged(String text) {
    // 如果文本为空，设置为0
    int inputScore = text.isEmpty ? 0 : int.tryParse(text) ?? 0;

    // 获取最新的maxScore
    _getMaxScore();

    // 检查是否超过最低有效积分数量
    if (inputScore > 0 && inputScore < minThresholdScore) {
      HaloToast.show(context, msg: "积分不能低于最低抵现要求${minThresholdScore.toInt()}");
      inputScore = 0;
      // 不直接清空文本框，避免用户输入体验不佳
    }

    // 确保不超过最大可用积分
    if (inputScore > maxScore && maxScore > 0) {
      inputScore = maxScore;
      scoreController.text = maxScore.toString();

      // 更新光标位置到末尾
      scoreController.selection = TextSelection.fromPosition(
        TextPosition(offset: scoreController.text.length),
      );
    }

    setState(() {
      userScore = inputScore;
      getScoreDiscountMoney();
    });
  }

  void _updateAllScore(bool value) {
    if (value != isAllScore) {
      setState(() {
        isAllScore = value;
        setAllScore(value);
      });
    }
  }

  @override
  void dispose() {
    scoreController.dispose();
    _gridViewController.dispose();
    _scoreFocusNode.dispose();
    super.dispose();
  }

  void _useAllScore() {
    setAllScore(true);
  }

  void _selectRule(SsVipScoreConfigurationFixedRule rule) {
    // 如果用户点击的是已选中的规则，取消选择
    if (userScore == rule.discountingProportion) {
      setState(() {
        userScore = 0;
      });
      return;
    }

    // 验证积分是否足够
    if ((rule.discountingProportion ?? 0) >
        (widget.vipInfo.asserts?.availableScore ?? 0)) {
      HaloToast.show(context, msg: "可用积分不足");
      return;
    }

    // 获取考虑所有限制因素（包括discountingLimitType）的限制金额
    Decimal maxAllowedAmount = ScoreDiscountUtil.getScoreConfigurationMinMoney(
        totalBeforeScoreDiscount, widget.scoreConfiguration);

    // 验证规则金额是否超过限制
    if (rule.discountingValue != null) {
      Decimal ruleValue = Decimal.parse(rule.discountingValue.toString());

      // 检查规则金额是否超过可用金额（考虑了交易限制）
      if (ruleValue > maxAllowedAmount) {
        HaloToast.show(context, msg: "抵现金额不能超过限制");
        return;
      }

      // 检查规则金额是否超过订单金额
      if (ruleValue > totalBeforeScoreDiscount) {
        HaloToast.show(context, msg: "抵现金额不能超过订单金额");
        return;
      }
    }

    setState(() {
      userScore = rule.discountingProportion ?? 0;
    });
  }

  void _onKeyPress(String key) {
    // 如果当前是全部抵现状态，先取消
    if (isAllScore) {
      setState(() {
        isAllScore = false;
        scoreController.text = "";
      });
    }

    String currentText = scoreController.text;

    if (key == '←') {
      if (currentText.isNotEmpty) {
        currentText = currentText.substring(0, currentText.length - 1);
      }
    } else {
      // 处理首位0的情况
      if (currentText == "0") {
        currentText = key;
      } else {
        currentText += key;
      }
    }

    // 检查积分合法性
    _getMaxScore(); // 确保maxScore是最新的
    int inputScore = currentText.isEmpty ? 0 : int.tryParse(currentText) ?? 0;

    // 检查是否超过最低有效积分数量
    if (inputScore > 0 && inputScore < minThresholdScore) {
      HaloToast.show(context, msg: "积分不能低于最低抵现要求${minThresholdScore.toInt()}");
      inputScore = 0;
      currentText = "";
    }

    // 正常订单允许超额使用积分，但不超过最大可用积分
    if (inputScore > maxScore && maxScore > 0) {
      inputScore = maxScore;
      currentText = maxScore.toString();
    }

    setState(() {
      scoreController.text = currentText;
      userScore = inputScore;
      // 更新光标位置到末尾
      scoreController.selection = TextSelection.fromPosition(
        TextPosition(offset: currentText.length),
      );
      // 确保输入框有焦点，光标显示
      _scoreFocusNode.requestFocus();
    });
  }

  Widget _buildScoreInput() {
    // 检测当前平台
    final bool isDesktopOrWeb =
        kIsWeb || (Platform.isWindows || Platform.isLinux || Platform.isMacOS);

    return Container(
      height: 80.h,
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: isAllScore ? const Color(0xFF4679FC) : const Color(0xFFEEEEEE),
        ),
      ),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 22.w),
            child: Text(
              "使用积分",
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF333333),
              ),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: Center(
              child: TextField(
                controller: scoreController,
                focusNode: _scoreFocusNode,
                textAlign: TextAlign.start,
                enabled: !isAllScore,
                // 在桌面或Web平台使用number键盘，在移动平台使用none
                keyboardType:
                    isDesktopOrWeb ? TextInputType.number : TextInputType.none,
                // 在移动平台上设置为只读，但仍然可以通过程序修改文本
                readOnly: !isDesktopOrWeb,
                onChanged: _onScoreChanged,
                style: TextStyle(
                  fontSize: 24.sp,
                  color: const Color(0xFF333333),
                  height: 1.2,
                ),
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  isDense: true,
                  isCollapsed: true,
                ),
                showCursor: true,
                cursorColor: const Color(0xFF4679FC),
              ),
            ),
          ),
          GestureDetector(
            onTap: _useAllScore,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 21.w),
              alignment: Alignment.center,
              child: Text(
                "全部抵现",
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF4679FC),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDiscountAmount() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 27.w),
      height: 40.h,
      child: Align(
        alignment: const Alignment(-1.0, -0.8),
        child: Row(
          children: [
            Text(
              "抵现: ",
              style: TextStyle(
                fontSize: 22.sp,
                color: const Color(0xFF666666),
                height: 1.2,
              ),
            ),
            Text(
              '¥${getScoreDiscountMoney()}',
              style: TextStyle(
                fontSize: 22.sp,
                color: const Color(0xFFFF0012),
                height: 1.2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNumberKeyboard() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        children: [
          _buildKeyboardRow(['1', '2', '3']),
          SizedBox(height: 10.h),
          _buildKeyboardRow(['4', '5', '6']),
          SizedBox(height: 10.h),
          _buildKeyboardRow(['7', '8', '9']),
          SizedBox(height: 10.h),
          _buildKeyboardRow(['←', '0', '']),
        ],
      ),
    );
  }

  Widget _buildKeyboardRow(List<String> keys) {
    return Row(
      children: keys.map((key) => _buildKeyboardButton(key)).toList(),
    );
  }

  Widget _buildKeyboardButton(String text) {
    if (text.isEmpty) {
      return Expanded(child: Container());
    }

    return Expanded(
      child: GestureDetector(
        onTap: () => _onKeyPress(text),
        child: Container(
          height: 80.h,
          margin: EdgeInsets.symmetric(horizontal: 5.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(color: const Color(0xFFEEEEEE)),
          ),
          alignment: Alignment.center,
          child: text == '←'
              ? Icon(
                  Icons.backspace_outlined,
                  size: 41.w,
                  color: const Color(0xFF666666),
                )
              : Text(
                  text,
                  style: TextStyle(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildFixedTierList() {
    // 如果规则列表为空，调用jumpToIndex重新获取所有适用规则
    if (rules.isEmpty) {
      jumpToIndex();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      alignment: Alignment.topLeft,
      child: Wrap(
        spacing: 20.w,
        runSpacing: 20.h,
        alignment: WrapAlignment.start,
        children: (widget.scoreConfiguration.ssVipScoreConfigurationFixedRule
                    ?.length ==
                1)
            ? [
                Align(
                  alignment: Alignment.centerLeft,
                  child: _buildFixedTierItem(widget
                      .scoreConfiguration.ssVipScoreConfigurationFixedRule![0]),
                )
              ]
            : widget.scoreConfiguration.ssVipScoreConfigurationFixedRule
                    ?.map((rule) => _buildFixedTierItem(rule))
                    .toList() ??
                [],
      ),
    );
  }

  Widget _buildFixedTierItem(SsVipScoreConfigurationFixedRule rule) {
    final isSelected = userScore == rule.discountingProportion;
    return GestureDetector(
      onTap: () => _selectRule(rule),
      child: Container(
        height: 80.h,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF4679FC) : const Color(0xFFEEEEEE),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: HaloContainer(
          mainAxisAlignment: MainAxisAlignment.start,
          constraints: BoxConstraints(
            minWidth: 380.w,
          ),
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 28.w,
              height: 28.w,
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFF4679FC) : Colors.white,
                borderRadius: BorderRadius.circular(14.r),
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF4679FC)
                      : const Color(0xFFBFBFBF),
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: 14.w,
                      color: Colors.white,
                    )
                  : null,
            ),
            SizedBox(width: 8.w),
            Flexible(
              child: Text(
                '${rule.discountingProportion}积分抵现${rule.discountingValue}元',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: const Color(0xFF333333),
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
