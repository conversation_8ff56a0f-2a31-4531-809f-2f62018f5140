import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../bill/entity/ss_card_dto.dart';
import '../../../common/standard.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../entity/select_wrapper_bean.dart';
import '../../../iconfont/icon_font.dart';
import '../../../vip/model/vip_model.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:halo_utils/utils/math_util.dart';

///优惠券选择弹窗
class CardSelectDialog extends StatefulWidget {
  ///会员id
  final String vipId;

  ///已选择的卡卷列表
  final List<SsCardDto> selectedList;

  const CardSelectDialog._({required this.vipId, required this.selectedList});

  factory CardSelectDialog(
      {required String vipId, required List<SsCardDto> selectedList}) {
    return CardSelectDialog._(
        vipId: vipId, selectedList: [...selectedList]);
  }

  @override
  State<CardSelectDialog> createState() => _CardSelectDialogState();
}

class _CardSelectDialogState extends State<CardSelectDialog> {
  final EdgeInsets _padding = EdgeInsets.symmetric(horizontal: 22.w);

  List<SsCardDto> selectedList = [];

  ///会员绑定的所有卡卷
  List<SsCardDto> _allCardList = [];

  ///列表
  List<SelectWrapperBean<SsCardDto>> data = [];

  ///搜索关键词
  String _keyword = "";

  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      FocusScope.of(context).requestFocus(_focusNode);
    });
    _getAllCardList().then((allCardList) {
      _allCardList = allCardList;
      selectedList.addAll(widget.selectedList);
      _initList();
    });
  }

  ///根据会员id获取全部的优惠券
  Future<List<SsCardDto>> _getAllCardList() async {
    return await VipModel.getCardByVipId(context,
            vipId: widget.vipId,
            otypeId: SpTool.getStoreInfo()!.otypeId!,
            cardType: [2, 3, 4]) ??
        [];
  }

  ///根据关键词初始化展示的列表
  void _initList() {
    List<SelectWrapperBean<SsCardDto>> list = [];
    for (SsCardDto card in _allCardList) {
      if (_keyword.isEmpty != false ||
          card.cardNo?.contains(_keyword) == true) {
        bool selected = selectedList
                .firstWhere((element) => element.id == card.id,
                    orElse: () => SsCardDto())
                .id !=
            null;
        list.add(SelectWrapperBean(data: card, selected: selected));
      }
    }
    data = list;
  }

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      child: SizedBox(
        width: 1094.w,
        height: 870.h,
        child: Container(
            color: Colors.white,
            child: Material(
              child: Column(
                children: [
                  _buildTitle(context),
                  Divider(height: 1.h, color: const Color(0xFFDBDBDB)),
                  _buildSearch(context),
                  Expanded(child: _buildGridView()),
                  _buildBottomButtons(),
                ],
              ),
            )),
      ),
    );
  }

  ///构建列表
  Widget _buildGridView() {
    return Container(
      color: const Color(0xFFF6F6F6),
      padding: _padding.copyWith(top: 26.h),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 30.w,
          crossAxisSpacing: 30.w,
          childAspectRatio: 498 / 180,
        ),
        itemBuilder: (context, index) =>
            data[index].let<Widget>((item) => _buildItem(item)),
        itemCount: data.length,
      ),
    );
  }

  ///构建列表item
  Widget _buildItem(SelectWrapperBean<SsCardDto> item) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        bool selected = !item.selected;
        if (selected) {
          if (selectedList
              .every((element) => element.cardType != item.data.cardType)) {
            setState(() {
              item.selected = true;
              selectedList.add(item.data);
            });
          }
        } else {
          setState(() {
            item.selected = false;
            selectedList.remove(selectedList
                .firstWhere((element) => item.data.id == element.id));
          });
        }
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
              color:
                  item.selected ? const Color(0xFFF91E1E) : Colors.transparent,
              width: 2.w),
          borderRadius: BorderRadius.circular(10.w),
        ),
        child: Stack(
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: double.infinity,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: _getCardTypeBackgroundColor(item.data.cardType!),
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10.w),
                          bottomLeft: Radius.circular(10.w),
                        )),
                    child: Text(
                      _getCardType(item.data.cardType!),
                      style: TextStyle(
                        color: _getCardTypeTextColor(item.data.cardType!),
                        fontWeight: FontWeight.bold,
                        fontSize: 32.sp,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          topRight: Radius.circular(10.w),
                          bottomRight: Radius.circular(10.w)),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.data.fullname ?? "",
                          style: TextStyle(
                            color: const Color(0xFF333333),
                            fontSize: 32.sp,
                            fontWeight: FontWeight.bold,
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 1,
                        ),
                        Text(
                          _getCardLimit(item.data),
                          style: TextStyle(
                            color: const Color(0xFFF90000),
                            fontSize: 24.sp,
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 1,
                        ),
                        Text(
                          "卡券编号：${item.data.cardNo ?? ""}",
                          style: TextStyle(
                            color: const Color(0xFF999999),
                            fontSize: 22.sp,
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 1,
                        ),
                        Text(
                          "有效期：${_formatDate(item.data.createTime!)}-${_formatDate(item.data.expiryDay!)}",
                          style: TextStyle(
                            color: const Color(0xFF999999),
                            fontSize: 22.sp,
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Visibility(
              visible: item.selected,
              child: Positioned(
                top: 0,
                right: 0,
                width: 56.w,
                height: 48.h,
                child: ClipRRect(
                  borderRadius:
                      BorderRadius.only(topRight: Radius.circular(5.w)),
                  child: CustomPaint(painter: _RedTriangle()),
                ),
              ),
            ),
            Visibility(
              visible: item.selected,
              child: Positioned(
                top: 8.h,
                right: 5.w,
                child: IconFont(IconNames.duigou, size: 15.w),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///标题行
  Widget _buildTitle(BuildContext context) {
    return Container(
      color: Colors.white,
      height: 80.h,
      padding: _padding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "选择优惠券",
            style: TextStyle(
              color: const Color(0xFF333333),
              fontSize: 28.sp,
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => Navigator.pop(context),
            child: Padding(
              padding: EdgeInsets.all(10.w),
              child: IconFont(IconNames.close, size: 20.w),
            ),
          ),
        ],
      ),
    );
  }

  ///搜索框
  Widget _buildSearch(BuildContext context) {
    return Container(
      color: const Color(0xFFE4E4E4),
      child: Container(
        height: 80.h,
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 26.w),
        child: Row(
          children: [
            IconFont(IconNames.saomiao, size: 28.w),
            SizedBox(width: 20.w),
            Expanded(
              child: TextField(
                focusNode: _focusNode,
                decoration: const InputDecoration(
                  hintText: "扫描或输入优惠券码",
                  contentPadding: EdgeInsets.zero,
                  border: OutlineInputBorder(borderSide: BorderSide.none),
                ),
                onChanged: (text) => setState(() {
                  _keyword = text;
                  _initList();
                }),
                style:
                    TextStyle(color: const Color(0xFF333333), fontSize: 26.sp),
                maxLines: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///底部确定和取消按钮
  Widget _buildBottomButtons() {
    return Container(
      color: Colors.white,
      height: 100.h,
      padding: EdgeInsets.only(right: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          _buildButton(context, "取消", onTap: () => Navigator.pop(context)),
          SizedBox(width: 30.w),
          _buildButton(
            context,
            "确定",
            textColor: Colors.white,
            background: const Color(0xFF4679FC),
            borderColor: null,
            onTap: () =>
                Navigator.pop(context, [selectedList, _allCardList.length]),
          ),
        ],
      ),
    );
  }

  ///底部按钮
  Widget _buildButton(
    BuildContext context,
    String content, {
    Color textColor = const Color(0xFF333333),
    Color background = Colors.white,
    Color? borderColor = const Color(0xFF999999),
    VoidCallback? onTap,
  }) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: 66.h,
          width: 196.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: background,
            borderRadius: BorderRadius.circular(4.w),
            border: borderColor?.let(
                (borderColor) => Border.all(color: borderColor, width: 2.w)),
          ),
          child: Text(content,
              style: TextStyle(color: textColor, fontSize: 26.sp)),
        ));
  }

  ///获取优惠券使用说明
  String _getCardLimit(SsCardDto card) {
    if (card.memberEquityValues.isNotEmpty != true) {
      return "";
    }
    MemberEquityValuesBean value = card.memberEquityValues[0];
    switch (card.cardType) {
      case 2:
      case 3:
        DetailListBean? detail = (value.detailList.isNotEmpty)
            ? value.detailList[0]
            : null;
        if (detail == null) return "";
        if (card.cardType == 2) {
          return "满${detail.valueCondition}元减${detail.valueDetail ?? 0}元";
        } else {
          return "满${detail.valueCondition}元打${MathUtil.multiplication((detail.valueDetail ?? 0).toString(), "10").toString()}折";
        }
      case 4:
        return "可提货${value.detailList.first.valueDetail ?? 0}件";
      default:
        return "";
    }
  }

  ///优惠券权益类型 2：代金券 3：折扣券 4：礼品券
  String _getCardType(int cardType) {
    switch (cardType) {
      case 2:
        return "代金券";
      case 3:
        return "折扣券";
      case 4:
        return "礼品券";
      default:
        return "";
    }
  }

  ///获取卡券类型文字颜色
  ///优惠券权益类型 2：代金券 3：折扣券 4：礼品券
  Color _getCardTypeTextColor(int cardType) {
    switch (cardType) {
      case 2:
        return const Color(0xFFF90000);
      case 3:
        return const Color(0xFFFF7B00);
      case 4:
        return const Color(0xFF21B93F);
      default:
        return const Color(0xFFF90000);
    }
  }

  ///获取卡券类型背景颜色
  ///优惠券权益类型 2：代金券 3：折扣券 4：礼品券
  Color _getCardTypeBackgroundColor(int cardType) {
    switch (cardType) {
      case 2:
        return const Color(0xFFFDF2F0);
      case 3:
        return const Color(0xFFFFF8EE);
      case 4:
        return const Color(0xFFF0FDF2);
      default:
        return const Color(0xFFF90000);
    }
  }

  ///日期格式转换
  String _formatDate(String date) {
    return DateUtil.formatDateStr(date, format: "yyyy.MM.dd");
  }
}

///优惠券选中的红色三角形
class _RedTriangle extends CustomPainter {
  final Paint _paint = Paint()
    ..style = PaintingStyle.fill
    ..color = const Color(0xFFFF3F3F)
    ..isAntiAlias = true;

  final Path _path = Path();

  @override
  void paint(Canvas canvas, Size size) {
    _path.moveTo(0, 0);
    _path.lineTo(size.width, 0);
    _path.lineTo(size.width, size.height);
    _path.lineTo(0, 0);
    canvas.drawPath(_path, _paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
