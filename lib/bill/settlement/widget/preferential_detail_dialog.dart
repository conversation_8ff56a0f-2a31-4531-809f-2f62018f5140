import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/bill/tool/promotion/promotion.dart';
import 'package:halo_pos/common/num_extension.dart';
import 'package:haloui/utils/math_util.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/preferential_dto.dart';
import '../../../bill/tool/bill_tool.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/color_util.dart';

import '../../tool/goods_tool.dart';
import '../../tool/promotion/preferential.dart';

///单据优惠明细弹窗
class PreferentialDetailDialog extends StatelessWidget {
  final List<GoodsDetailDto> dataSource;

  final bool isBack;

  const PreferentialDetailDialog(
      {Key? key, required this.dataSource, this.isBack = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      constrainedAxis: Axis.vertical,
      child: SizedBox(
        child: Dialog(
          insetPadding: EdgeInsets.zero,
          child:
              isBack ? _BackDialog(dataSource) : _ShowDetailsDialog(dataSource),
        ),
      ),
    );
  }
}

class _ShowDetailsDialog extends StatefulWidget {
  final List<GoodsDetailDto> dataSource;

  const _ShowDetailsDialog(this.dataSource);

  @override
  State<StatefulWidget> createState() {
    return _ShowDetailsDialogState();
  }
}

class _ShowDetailsDialogState extends State<_ShowDetailsDialog> {
  Map<GoodsDetailDto, Map<String, PreferentialDto>> preferentialMap = {};

  @override
  void initState() {
    super.initState();
    Map<String, GoodsDetailDto> comboMap = {};
    Map<String, List<GoodsDetailDto>> comboDetailsMap = {};
    GoodsTool.filterGoodsListAndGetCombo(widget.dataSource,
        comboMap: comboMap, comboDetailsMap: comboDetailsMap, filter: (goods) {
      if (!GoodsTool.isComboDetail(goods)) {
        preferentialMap[goods] =
            GoodsTool.isComboRow(goods) ? {} : goods.preferentialHelp;
      }
      return true;
    });
    //处理套餐
    for (var entry in comboDetailsMap.entries) {
      final comboDetails = entry.value;
      final comboPreferential = preferentialMap[comboMap[entry.key]];
      if (comboPreferential != null) {
        for (var comboDetail in comboDetails) {
          for (var preferential in Preferential.values) {
            PreferentialUtil.collectPreferential(
                comboPreferential, comboDetail, preferential);
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      width: 1400.w,
      height: 900.h,
      child: Column(
        children: [
          _buildTitle(),
          _buildListTitle(),
          _buildList(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      height: 86.h,
      margin: EdgeInsets.only(left: 28.w),
      child: Row(
        children: [
          Expanded(
            child: HaloPosLabel(
              "优惠明细",
              textStyle:
                  TextStyle(fontSize: 28.sp, color: const Color(0xFF333333)),
            ),
          ),
          GestureDetector(
            child: Container(
                width: 80.w,
                height: 86.h,
                alignment: Alignment.center,
                child: IconFont(
                  IconNames.close,
                  size: 24.w,
                  color: ColorUtil.color2String(Colors.black),
                )),
            onTap: () => NavigateUtil.pop(context),
          ),
        ],
      ),
    );
  }

  Widget _buildListTitle() {
    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(horizontal: 28.w),
      color: ColorUtil.stringColor("#F5F5F5"),
      child: Row(
        children: [
          Expanded(flex: 10, child: HaloPosLabel("商品名称")),
          Expanded(flex: 3, child: HaloPosLabel("数量")),
          Expanded(flex: 3, child: HaloPosLabel("单位")),
          Expanded(flex: 8, child: HaloPosLabel("折扣优惠")),
          Expanded(flex: 8, child: HaloPosLabel("促销优惠")),
          Expanded(flex: 8, child: HaloPosLabel("优惠分摊")),
        ],
      ),
    );
  }

  Widget _buildList() {
    List<Widget> children = [];
    for (var goods in widget.dataSource) {
      if (!BillTool.comboDetailRow(goods)) {
        children.add(_buildListItem(goods));
      }
    }
    return Expanded(
        child: ListView(
      children: children,
    ));
  }

  Widget _buildListItem(GoodsDetailDto goodsDetailDto) {
    return Column(
      children: [
        Container(
          height: 172.h,
          padding: EdgeInsets.symmetric(horizontal: 28.w),
          child: Row(
            children: [
              Expanded(flex: 10, child: _buildName(goodsDetailDto)),
              Expanded(
                  flex: 3,
                  child: HaloPosLabel(goodsDetailDto.unitQty.toString())),
              Expanded(flex: 3, child: HaloPosLabel(goodsDetailDto.unitName)),
              Expanded(
                  flex: 8, child: _buildDiscountPreferential(goodsDetailDto)),
              Expanded(
                  flex: 8, child: _buildPromotionPreferential(goodsDetailDto)),
              Expanded(flex: 8, child: _buildPreferentialOrder(goodsDetailDto)),
            ],
          ),
        ),
        Container(
          height: 1.h,
          color: ColorUtil.stringColor("#E8E8E8"),
        )
      ],
    );
  }

  Widget _buildName(GoodsDetailDto goodsDetailDto) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        HaloPosLabel(
          goodsDetailDto.pFullName!,
          maxLines: 1,
          textStyle: TextStyle(
              fontSize: 24.sp, color: ColorUtil.stringColor("#494848")),
        ),
        Padding(padding: EdgeInsets.symmetric(vertical: 6.h)),
        HaloPosLabel(goodsDetailDto.fullbarcode,
            maxLines: 1,
            textStyle: TextStyle(
                fontSize: 22.sp, color: ColorUtil.stringColor("#666666"))),
      ],
    );
  }

  ///折扣优惠
  Widget _buildDiscountPreferential(GoodsDetailDto goodsDetailDto) {
    List<Widget> children = [];
    Map<String, PreferentialDto> preferentialHelp =
        preferentialMap[goodsDetailDto] ?? {};
    for (var entry in preferentialHelp.entries) {
      final key = entry.key;
      final value = entry.value;
      if (value.total == 0) continue;
      String? title;
      if (key == Preferential.manualPrice.name) {
        title = "手工改价:";
      } else if (key == Preferential.vipPrice.name) {
        title = "会员价优惠:";
      } else if (key == Preferential.discountRightsCard.name) {
        title = "权益卡-折扣:";
      } else if (key == Preferential.discountCoupon.name) {
        title = "优惠券-折扣券:";
      } else if (key == Preferential.giftCoupon.name) {
        title = "优惠券-礼品券:";
      }
      if (title != null) {
        children.add(_buildPreferentialItem(title, value.total));
      }
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  ///促销优惠
  Widget _buildPromotionPreferential(GoodsDetailDto goodsDetailDto) {
    List<Widget> children = [];
    Map<String, PreferentialDto> preferentialHelp =
        preferentialMap[goodsDetailDto] ?? {};
    for (var entry in preferentialHelp.entries) {
      final key = entry.key;
      final value = entry.value;
      if (value.total == 0) continue;
      String? title;
      if (key == Preferential.goodsPromotion.name) {
        final promotionType = PromotionType.values.firstWhereOrNull(
            (type) => type.value == goodsDetailDto.promotionType);
        title = "${promotionType?.name1 ?? "促销"}:";
      } else if (key == Preferential.billPromotion.name) {
        title = "订单满减:";
      }
      if (title != null) {
        children.add(_buildPreferentialItem(title, value.total));
      }
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }

  ///优惠分摊
  Widget _buildPreferentialOrder(GoodsDetailDto goodsDetailDto) {
    List<Widget> children = [];
    Map<String, PreferentialDto> preferentialHelp =
        preferentialMap[goodsDetailDto] ?? {};
    for (var entry in preferentialHelp.entries) {
      final key = entry.key;
      final value = entry.value;
      if (value.total == 0) continue;
      String? title;
      if (key == Preferential.billPreferential.name) {
        title = "总额优惠:";
      } else if (key == Preferential.moneyCoupon.name) {
        title = "优惠券-代金券:";
      } else if (key == Preferential.scoreDiscount.name) {
        title = "积分抵扣:";
      } else if (key == Preferential.eraseZero.name) {
        title = "抹零优惠:";
      }
      if (title != null) {
        children.add(_buildPreferentialItem(title, value.total));
      }
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: children,
    );
  }
}

class _BackDialog extends StatefulWidget {
  final List<GoodsDetailDto> dataSource;

  const _BackDialog(this.dataSource);

  @override
  State<StatefulWidget> createState() {
    return _BackState();
  }
}

class _BackState extends State<_BackDialog> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      width: 1400.w,
      height: 900.h,
      child: Column(
        children: [
          _buildTitle(),
          _buildListTitle(),
          _buildList(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      height: 86.h,
      margin: EdgeInsets.only(left: 28.w),
      child: Row(
        children: [
          Expanded(
            child: HaloPosLabel(
              "优惠明细",
              textStyle:
                  TextStyle(fontSize: 28.sp, color: const Color(0xFF333333)),
            ),
          ),
          GestureDetector(
            child: Container(
                width: 80.w,
                height: 86.h,
                alignment: Alignment.center,
                child: IconFont(
                  IconNames.close,
                  size: 24.w,
                  color: ColorUtil.color2String(Colors.black),
                )),
            onTap: () => NavigateUtil.pop(context),
          ),
        ],
      ),
    );
  }

  Widget _buildListTitle() {
    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(horizontal: 28.w),
      color: ColorUtil.stringColor("#F5F5F5"),
      child: Row(
        children: [
          Expanded(flex: 10, child: HaloPosLabel("商品名称")),
          Expanded(flex: 3, child: HaloPosLabel("数量")),
          Expanded(flex: 3, child: HaloPosLabel("单位")),
          Expanded(flex: 8, child: HaloPosLabel("折扣优惠")),
          Expanded(flex: 8, child: HaloPosLabel("促销优惠")),
          Expanded(flex: 8, child: HaloPosLabel("优惠分摊")),
        ],
      ),
    );
  }

  Widget _buildList() {
    List<Widget> children = [];
    for (var goods in widget.dataSource) {
      if (!BillTool.comboDetailRow(goods)) {
        children.add(_buildListItem(goods));
      }
    }
    return Expanded(
        child: ListView(
      children: children,
    ));
  }

  Widget _buildListItem(GoodsDetailDto goodsDetailDto) {
    return Column(
      children: [
        Container(
          height: 172.h,
          padding: EdgeInsets.symmetric(horizontal: 28.w),
          child: Row(
            children: [
              Expanded(flex: 10, child: _buildName(goodsDetailDto)),
              Expanded(
                  flex: 3,
                  child: HaloPosLabel(goodsDetailDto.unitQty.toString())),
              Expanded(flex: 3, child: HaloPosLabel(goodsDetailDto.unitName)),
              Expanded(
                  flex: 8, child: _buildDiscountPreferential(goodsDetailDto)),
              Expanded(
                  flex: 8, child: _buildPromotionPreferential(goodsDetailDto)),
              Expanded(flex: 8, child: _buildPreferentialOrder(goodsDetailDto)),
            ],
          ),
        ),
        Container(
          height: 1.h,
          color: ColorUtil.stringColor("#E8E8E8"),
        )
      ],
    );
  }

  Widget _buildName(GoodsDetailDto goodsDetailDto) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        HaloPosLabel(
          goodsDetailDto.pFullName!,
          maxLines: 1,
          textStyle: TextStyle(
              fontSize: 24.sp, color: ColorUtil.stringColor("#494848")),
        ),
        Padding(padding: EdgeInsets.symmetric(vertical: 6.h)),
        HaloPosLabel(goodsDetailDto.fullbarcode,
            maxLines: 1,
            textStyle: TextStyle(
                fontSize: 22.sp, color: ColorUtil.stringColor("#666666"))),
      ],
    );
  }

  ///折扣优惠
  Widget _buildDiscountPreferential(GoodsDetailDto goodsDetailDto) {
    num discountPreferential = MathUtil.subtractDec(
            goodsDetailDto.currencyTotal, goodsDetailDto.discountTotal)
        .toDouble()
        .getIntWhenInteger;
    if (discountPreferential == 0) return Container();
    return _buildPreferentialItem("", discountPreferential);
  }

  ///促销优惠
  Widget _buildPromotionPreferential(GoodsDetailDto goodsDetailDto) {
    if (goodsDetailDto.currencyPtypePreferentialTotal == 0) return Container();
    return _buildPreferentialItem(
        "", goodsDetailDto.currencyPtypePreferentialTotal);
  }

  ///优惠分摊
  Widget _buildPreferentialOrder(GoodsDetailDto goodsDetailDto) {
    if (goodsDetailDto.currencyOrderPreferentialAllotTotal == 0) {
      return Container();
    }
    return _buildPreferentialItem(
        "", goodsDetailDto.currencyOrderPreferentialAllotTotal);
  }
}

Widget _buildPreferentialItem(String title, num total) {
  return Container(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          if (title.isNotEmpty)
            HaloPosLabel(
              title,
              maxLines: 1,
              textStyle: TextStyle(
                  fontSize: 20.sp, color: ColorUtil.stringColor("#555555")),
            ),
          Expanded(
            child: HaloPosLabel(
              "${total >= 0 ? '-' : '+'}¥${DecimalDisplayHelper.getTotalFixed(total.abs().toString())}",
              maxLines: 1,
              textStyle: TextStyle(
                  fontSize: 20.sp, color: ColorUtil.stringColor("#EA4818")),
            ),
          )
        ],
      ));
}
