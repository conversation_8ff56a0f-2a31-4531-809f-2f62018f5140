import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/haloui.dart';

/// 创建时间：2023/11/13
/// 作者：xiaotiaochong
/// 描述：

class AddTipsPage extends StatefulWidget {
  final String defaultText;
  final Function(String text) submitCallback;

  const AddTipsPage(
      {Key? key, required this.defaultText, required this.submitCallback})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _AddTipsPageState();
  }
}

class _AddTipsPageState extends State<AddTipsPage> {
  double width = 600.w;
  String tips = "";
  TextEditingController controller = TextEditingController();
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    tips = widget.defaultText;
    controller.text = tips;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      FocusScope.of(context).requestFocus(focusNode);
    });
  }

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
        constrainedAxis: Axis.vertical,
        child: Material(
            color: Colors.transparent,
            child: Container(
                alignment: Alignment.center,
                child: Container(
                  height: 240.h,
                  width: width,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: ColorUtil.stringColor("#DDDDDD"), width: 1.w),
                    borderRadius: BorderRadius.circular(8.w),
                    color: Colors.white,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        height: 80.h,
                        margin: EdgeInsets.symmetric(
                            vertical: 20.w, horizontal: 20.h),
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: ColorUtil.stringColor("#DDDDDD"),
                              width: 1.w),
                          borderRadius: BorderRadius.circular(8.w),
                          color: Colors.white,
                        ),
                        child: TextField(
                          maxLength: 250,
                          focusNode: focusNode,
                          controller: controller,
                          decoration: InputDecoration(
                            hintText: "请输入备注内容",
                            hintStyle:
                                TextStyle(color: Colors.grey, fontSize: 20.sp),
                            counterText: "",

                            ///textField 下半部分被遮挡
                            focusedBorder: const OutlineInputBorder(
                                borderSide: BorderSide(
                                    width: 0, color: Colors.transparent)),
                            disabledBorder: const OutlineInputBorder(
                                borderSide: BorderSide(
                                    width: 0, color: Colors.transparent)),
                            enabledBorder: const OutlineInputBorder(
                                borderSide: BorderSide(
                                    width: 0, color: Colors.transparent)),
                            border: const OutlineInputBorder(
                                borderSide: BorderSide(
                                    width: 0, color: Colors.transparent)),
                            contentPadding: EdgeInsets.zero,
                          ),
                          onChanged: (String text) {
                            _onChange(text);
                          },
                          onSubmitted: (String text) {
                            _doSubmit();
                          },
                        ),
                      ),
                      _buildBottomBtn(),
                    ],
                  ),
                ))));
  }

  _buildBottomBtn() {
    return SizedBox(
        width: width,
        height: 80.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildBottomButton("确定", onTap: () {
              _doSubmit();
            }),
            SizedBox(
              width: 50.w,
            ),
            _buildBottomButton("取消",
                textColor: Colors.grey,
                buttonType: HaloButtonType.outlinedButton,
                borderColor: Colors.grey, onTap: () {
              Navigator.pop(context);
            }),
          ],
        ));
  }

  _buildBottomButton(String text,
      {double margin = 20,
      Color? color,
      Color? textColor,
      Color? borderColor,
      HaloButtonType? buttonType,
      Function? onTap}) {
    return Container(
      margin: EdgeInsets.only(right: margin.w),
      child: HaloButton(
        buttonType: buttonType ?? HaloButtonType.elevatedButton,
        borderColor: borderColor ?? Colors.transparent,
        outLineWidth: 2.w,
        text: text,
        textColor: textColor ?? Colors.white,
        width: 120.w,
        height: 40.h,
        fontSize: 24.sp,
        backgroundColor: color,
        onPressed: () {
          if (onTap != null) {
            onTap();
          }
        },
      ),
    );
  }

  _onChange(String text) {
    tips = text;
  }

  _doSubmit() {
    widget.submitCallback(tips);
    Navigator.pop(context);
  }
}
