import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../widgets/base/halo_base_stateless_widget.dart';
import '../../widget/exception_item_widget.dart';

/// Copyright (C), 2019-2020, wsgjp.com
/// FileName: exception_widget
/// Author: tanglan
/// Date: 2020/10/19 14:26
/// Description:异常信息弹出界面

class ExceptionWidget<T> extends HaloBaseStatelessWidget {
  final List<T> exceptionList;
  final ExceptionItemWidget Function(
      BuildContext context, int index, T itemData) itemBuilder;

  const ExceptionWidget(this.exceptionList, this.itemBuilder);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        height: 600.h,
        width: ScreenUtil().screenWidth,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: buildWidget(context),
          ),
        ));
  }

  List<ExceptionItemWidget> buildWidget(BuildContext context) {
    List<ExceptionItemWidget> list = [];
    for (int i = 0; i < exceptionList.length; i++) {
      if (null != itemBuilder) {
        list.add(itemBuilder(context, i, exceptionList![i]!));
      }
    }
    return list;
  }
}
