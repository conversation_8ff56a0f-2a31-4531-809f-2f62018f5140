import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;

import '../../../common/keyboard_hidden.dart';
import '../../../bill/widget/ptype/ptype_detail_button.dart';
import '../../../common/tool/decimal_scale_input_formatter.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';

import '../../../login/entity/store/store_info.dart';
import '../../../widgets/price_keyboard.dart' hide Key;

///总额优惠（原整单优惠）界面
///整单折扣界面

class InputBoardPage extends StatefulWidget {
  final String defaultText;
  final num maxValue;
  final String title;
  final int scale;
  final String? tips;

  const InputBoardPage({
    Key? key,
    required this.defaultText,
    required this.maxValue,
    required this.scale,
    required this.title,
    this.tips,
  }) : super(key: key);

  @override
  InputBoardPageState createState() => InputBoardPageState();

  static Future showAlertDialog(BuildContext context,
      {required String defaultText,
      required num maxValue,
      required String title,
      required int scale,
      String? tips}) async {
    return HaloDialog(context,
        dismissOnTouchOutside: true,
        dismissOnBackKeyPress: true,
        child: InputBoardPage(
          title: title,
          scale: scale,
          defaultText: defaultText,
          maxValue: maxValue,
          tips: tips,
        )).show();
  }
}

class InputBoardPageState extends State<InputBoardPage> {
  final TextEditingController _price = TextEditingController(text: "0");

  @override
  void initState() {
    super.initState();
    _price.text = widget.defaultText;
    _price.selection = TextSelection(baseOffset: 0, extentOffset: _price.text.length);
    _price.addListener(onValueChange);
  }

  @override
  void dispose() {
    _price.removeListener(onValueChange);
    super.dispose();
  }

  void onValueChange() {
    if ((num.tryParse(_price.text) ?? 0) > widget.maxValue) {
      HaloToast.show(context, msg: "已达最大值");
      String max = widget.maxValue.toString();
      _price.value = TextEditingValue(
          text: max, selection: TextSelection.collapsed(offset: max.length));
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          Navigator.pop(context);
        },
        child: Scaffold(
            backgroundColor: Colors.transparent,
            body: Center(
                child: HaloContainer(
              width: 700.w,
              direction: Axis.vertical,
              borderRadius: BorderRadius.circular(6.w),
              color: Colors.white,
              children: [
                _buildTitle(context),
                _buildPrice(context),
                _buildKeyboard(context),
                if (widget.tips != null && widget.tips!.isNotEmpty)
                  _buildTips(context),
                _buildButton(context)
              ],
            ))));
  }

  ///标题栏
  Widget _buildTitle(BuildContext context) {
    return HaloContainer(
      padding: EdgeInsets.symmetric(vertical: 20.h),
      children: [
        Expanded(
            child: Center(
          child: Text(
            widget.title,
            style: TextStyle(
                fontWeight: FontWeight.bold,
                color: const Color(0xff333333),
                fontSize: 24.sp),
          ),
        )),
        GestureDetector(
          child: Container(
              margin: EdgeInsets.only(right: 24.w),
              alignment: Alignment.centerRight,
              child: IconFont(IconNames.close, size: 30.w)),
          onTap: () => Navigator.pop(context),
        ),
      ],
    );
  }

  ///价格
  Widget _buildPrice(BuildContext context) {
    return HaloContainer(
      margin: EdgeInsets.symmetric(horizontal: 40.w),
      borderRadius: BorderRadius.circular(6.w),
      border: Border.all(color: const Color(0xFFCFCFCF), width: 2.w),
      padding: EdgeInsets.only(left: 32.w, top: 20.h, bottom: 20.h),
      children: [
        Text(widget.title,
            style: TextStyle(
                color: const Color(0xff333333),
                fontSize: 26.sp,
                fontWeight: FontWeight.bold)),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
          child: KeyboardHiddenTextField(
            cleanTextWhenSearch: false,
            controller: _price,
            style: TextStyle(
                color: const Color(0xff333333),
                fontWeight: FontWeight.bold,
                fontSize: 28.sp),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            onTapBefore: () => false,
            onSubmitted: (_) => _submit(),
            inputFormatters: [
              DecimalScaleInputFormatter(scale: widget.scale),
            ],
          ),
        ),
      ],
    );
  }

  ///小键盘
  Widget _buildKeyboard(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(vertical: 10.h, horizontal: 40.w),
        child: PriceKeyBoard(
          controller: _price,
            scale:widget.scale
        ));
  }

  _buildButton(BuildContext context) {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.end,
      padding: EdgeInsets.symmetric(
        vertical: 20.h,
        horizontal: 35.w,
      ),
      children: [
        Expanded(
            child: HaloButton(
          text: "确定",
          height: 70.h,
          outLineWidth: 1.h,
          onPressed: () {
            _submit();
          },
        )),
        SizedBox(
          width: 40.w,
        ),
        Expanded(
            child: HaloButton(
          text: "取消",
          buttonType: HaloButtonType.outlinedButton,
          height: 70.h,
          outLineWidth: 1.h,
          onPressed: () {
            Navigator.pop(context);
          },
        )),
      ],
    );
    //
    //     return HaloContainer(
    //   padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 40.h, top: 4.h),
    //   children: [
    //     Expanded(
    //         child: _buildButton("确定", HaloButtonType.elevatedButton,
    //             callback: submit)),
    //     SizedBox(
    //       width: 40.w,
    //     ),
    //     Expanded(
    //         child: _buildButton("取消", HaloButtonType.outlinedButton,
    //             borderColor: const Color(0xFFB7B7B7), callback: cancel))
    //   ],
    // );
    //
    // return PtypeDetailButton(
    //   margin: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 40.h, top: 4.h),
    //   submit: _submit,
    //   cancel: () {
    //     Navigator.pop(context);
    //   },
    // );
  }

  void _submit() {
    StoreInfo storeInfo = SpTool.getStoreInfo()!;
    if (storeInfo.minDiscount != 0 && widget.title == "整单折扣") {
      if (num.parse(_price.text) < storeInfo.minDiscount!) {
        HaloToast.showError(context, msg: "低于设置的手工最低折扣，保存失败,如需调整，请到PC端门店资料设置");
        return;
      }
    }
    Navigator.pop(context, _price.text);
  }

  ///提示
  _buildTips(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Text(widget.tips ?? "",
          style: TextStyle(
            color: Colors.red,
            fontSize: 24.sp,
          )),
    );
  }
}
