import 'dart:math';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/math_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_radio.dart';

import '../../../bill/settlement/entity/atype_info_bean.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../bill/widget/ptype/ptype_textfield.dart';
import '../../../bill/widget/ptype/settlement_keyboard.dart';
import '../../../common/keyboard_hidden.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../login/entity/store/store_etype.dart';
import '../../../login/entity/store/store_payment.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../../widgets/selector/etype_selector.dart';

///结算控件
class PaymentWidget extends StatefulWidget {
  ///经手人
  final StoreEtype? etype;

  ///显示组合支付
  final bool showCombinationPay;

  ///是否展示选择经手人
  final bool showChooseEtype;

  ///实收
  final String moneyReceived;

  ///找零
  final String pettyCash;

  ///所有付款金额相加
  final String finalGetMoney;

  ///储值总金额
  final String storeTotal;

  ///支付方式
  final List<AtypeInfoBean> paymentList;

  final Function doSubmitAction;
  final Function? doTipsAction;
  final Function(PaymentEditChange) onEditChange;

  ///是否打印小票
  final bool print;

  ///是否开启组合支付
  final bool combinationPay;

  ///是否是退款
  final bool isRefund;

  const PaymentWidget({
    Key? key,
    this.isRefund = false,
    this.showCombinationPay = true,
    this.showChooseEtype = false,
    this.doTipsAction,
    required this.paymentList,
    required this.doSubmitAction,
    required this.onEditChange,
    this.moneyReceived = "0",
    this.pettyCash = "0",
    this.finalGetMoney = "0",
    this.print = false,
    this.etype,
    this.combinationPay = false,
    this.storeTotal = "0",
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _PaymentWidgetState();
  }
}

class _PaymentWidgetState extends State<PaymentWidget> {
  List<StoreEtype> etypeList = SpTool.getStoreInfo()!.etypeList!;

  String saleBackMsg = "退货/换货退款储值不可修改";

  //此处控制保存非组合支付的上一次选择
  //editing 是因封装控件内部不能去更新defaultText，当前焦点需要从键盘去获取值而不是传入值。
  //singlePaymentTotal 保存组合支付前的值。例：选择现金修改，并有找零。
  bool editing = false;
  AtypeInfoBean? singlePayment;
  String? singlePaymentTotal;

  List<AtypeInfoBean> get _paymentList => widget.paymentList; //支付方式

  // GlobalKey wrapKey = GlobalKey();

  //region method
  @override
  void initState() {
    super.initState();
    //默认将第一个启用的支付方式获取焦点
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) => requestFocus());
  }

  @override
  void didUpdateWidget(oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (editing) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        requestFocus();
        editing = false;
      });
    }
  }

  ///获取所以支付方式中第一个的焦点
  void requestFocus() {
    if (_paymentList.isNotEmpty) {
      AtypeInfoBean? atype = _paymentList.firstWhereOrNull(
        (element) => element.enable,
      );
      if (null != atype) {
        FocusScope.of(context).requestFocus(atype.focusNode);
      }
    }
  }

  ///获取支付方式的最大金额
  ///[max] 最大值，默认为-1，当大于0，则取两者最小值
  num _getMaxValue(AtypeInfoBean infoBean, {num max = -1}) {
    num maxValue;
    //非退款的情况下，现金支付方式不限制金额（可以找零）
    if (infoBean.storePayway.paywayType == 0 && !widget.isRefund) {
      maxValue = SpTool.getSystemConfig().sysGlobalDecimalMax;
    } else {
      //限制多账户输入金额不能超过应收
      Decimal sumTotal = Decimal.zero;
      for (AtypeInfoBean bean in _paymentList) {
        if (bean.enable &&
            bean.storePayway.paywayId != infoBean.storePayway.paywayId) {
          //退款，则此支付方式的金额为总金额-其他付款方式的金额
          //非退款，由于现金支付可以填入任意金额，所以此支付方式的金额为总金额-其他付款方式的金额（不含现金支付）
          if (!widget.isRefund && bean.storePayway.paywayType == 0) {
            continue;
          }
          sumTotal += Decimal.tryParse(bean.total) ?? Decimal.zero;
        }
      }
      //总金额-其他付款方式的金额
      maxValue =
          MathUtil.subtraction(
            widget.moneyReceived,
            sumTotal.toString(),
          ).toDouble();
      //如果支付方式本身有限制金额，则取两者最小值
      final maxTotal = num.parse(infoBean.maxTotal);
      if (maxTotal > 0) {
        maxValue = min(maxTotal, maxValue);
      }
    }
    if (max >= 0) {
      return min(max, maxValue);
    }
    return maxValue;
  }

  //endregion

  //region view

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF0F0F0),
      child: Column(
        children: [
          if (widget.showChooseEtype) _buildPopupEtype(),
          Expanded(
            child: Container(
              clipBehavior: Clip.hardEdge,
              margin: EdgeInsets.only(top: 14.w, left: 14.w, right: 14.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.w),
              ),
              child: Column(
                children: [
                  _buildContentRightTitle(),
                  _buildButtons(),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.all(40.w),
                      child: _buildPayView(),
                    ),
                  ),
                ],
              ),
            ),
          ),
          _buildKeyBoard(),
        ],
      ),
    );
  }

  _buildPopupEtype() {
    return Container(
      clipBehavior: Clip.hardEdge,
      height: 80.h,
      margin: EdgeInsets.only(top: 14.w, left: 14.w, right: 14.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: Row(
        children: [
          Padding(padding: EdgeInsets.only(left: 40.w)),
          Text(
            "业务员",
            style: TextStyle(fontSize: 26.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(width: 80.w),
          PopupMenuButton(
            offset: Offset(0, 60.w),
            constraints: BoxConstraints(minWidth: 300.w, maxWidth: 800.w),
            itemBuilder:
                (context) => [
                  ...List.generate(etypeList.length, (index) {
                    return PopupMenuItem(
                      value: etypeList[index],
                      child: HaloPosLabel(
                        etypeList[index].etypeName!,
                        textStyle: TextStyle(fontSize: 24.sp),
                      ),
                    );
                  }),
                ],
            onSelected: (selectEtype) {
              setState(() {
                widget.onEditChange(
                  PaymentEditChange(
                    paymentList: _paymentList,
                    print: widget.print,
                    combiPay: widget.combinationPay,
                    etype: selectEtype as StoreEtype,
                  ),
                );
              });
            },
            child: Container(
              padding: EdgeInsets.all(10.w),
              width: 300.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.all(Radius.circular(4.0)),
                border: Border.all(width: 1, color: const Color(0xFFE0E0E0)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: HaloPosLabel(
                        TextUtil.isEmpty(widget.etype?.etypeName)
                            ? "选择业务员"
                            : widget.etype!.etypeName!,
                        textStyle: TextStyle(
                          fontSize: 26.sp,
                          color:
                              TextUtil.isEmpty(widget.etype?.etypeName)
                                  ? const Color(0xFFBDBDBD)
                                  : Colors.black,
                        ),
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.keyboard_arrow_down,
                    color: Color(0xFFBDBDBD),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///标题以及组合支付、打印按钮
  Widget _buildContentRightTitle() {
    return Container(
      height: 98.h,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 40.w),
      child: Row(
        children: [
          Expanded(
            child: HaloLabel(
              "支付方式",
              textStyle: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // _buildEtype(),
          Visibility(visible: widget.combinationPay, child: _buildLeftMoney()),
          SizedBox(width: 10.w),
          Visibility(
            visible: widget.showCombinationPay,
            child: _buildCombinedPay(),
          ),
          Visibility(visible: false, child: _buildPrint()),
        ],
      ),
    );
  }

  ///剩余所需支付金额k
  _buildLeftMoney() {
    Decimal leftMoneyDecimal = MathUtil.subtraction(
      widget.moneyReceived,
      widget.finalGetMoney.toString(),
    );
    if (leftMoneyDecimal.compareTo(Decimal.zero) < 0) {
      leftMoneyDecimal = Decimal.zero;
    }
    String leftMoney = leftMoneyDecimal.toString();
    return Expanded(
      child: Container(
        padding: EdgeInsets.only(right: 10.w),
        alignment: Alignment.centerRight,
        child: HaloLabel(
          "待支付金额：$leftMoney元",
          textStyle: TextStyle(
            color: Colors.red,
            fontSize: 28.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  ///构建支付方式选择列表
  Widget _buildButtons() {
    return Container(
      height: 90.h,
      alignment: Alignment.bottomLeft,
      margin: EdgeInsets.symmetric(horizontal: 40.w),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: _paymentList.length,
        separatorBuilder: (context, index) => SizedBox(width: 20.w),
        shrinkWrap: true,
        itemBuilder: (context, index) {
          final atype = _paymentList[index];
          return _buildButton(
            atype.storePayway.paywayName!,
            atype.enable,
            iconNames: atype.iconNames,
            onPressed: () {
              if (atype.storePayway.paywayType == 3 && widget.isRefund) {
                HaloToast.showMsg(context, msg: saleBackMsg);
                return;
              }
              return _onButtonTap(atype);
            },
          );
        },
      ),
    );
  }

  ///构建选中的支付方式列表，填写金额
  Widget _buildPayView() {
    List<Widget> widgets = _buildContentPay();
    return GridView.builder(
      clipBehavior: Clip.none,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2, // 每行的item数量
        mainAxisSpacing: 30.w, // 主轴方向的间距
        crossAxisSpacing: 40.w, // 交叉轴方向的间距
        childAspectRatio: 6, // item的宽高比例
      ),
      itemCount: widgets.length, // 指定每行的格子数量
      itemBuilder: (context, index) {
        return widgets[index];
      },
    );
    // return Container(
    //     key: wrapKey,
    //     margin: EdgeInsets.symmetric(horizontal: 40.w, vertical: 40.h),
    //     child: SingleChildScrollView(
    //       child: Wrap(
    //         alignment: WrapAlignment.spaceBetween,
    //         crossAxisAlignment: WrapCrossAlignment.center,
    //         runSpacing: 26.h,
    //         children: _buildContentPay(),
    //       ),
    //     ));
  }

  ///构建键盘
  Widget _buildKeyBoard() {
    return SettlementKeyboard(submitCallback: _doSubmit, tipsCallback: _doTips);
  }

  ///组合支付开关
  Widget _buildCombinedPay() {
    return _buildRadio(
      text: "组合支付",
      value: widget.combinationPay,
      selectedColor: "#4679FC",
      onChanged: (newV) {
        _doCombinationPay(newV);
      },
    );
  }

  ///构建单选反选框
  Widget _buildRadio({
    required String text,
    required bool value,
    String selectedColor = "#F5B429",
    required Function(bool newV) onChanged,
  }) {
    return Container(
      width: 170.w,
      height: 60.h,
      alignment: Alignment.centerRight,
      child: HaloRadio(
        defaultImage: IconFont(IconNames.square, size: 28.w),
        selectedImage: IconFont(
          IconNames.xuanzhong,
          size: 28.w,
          color: selectedColor,
        ),
        imageSize: 28,
        value: value,
        groupValue: true,
        textStyle: TextStyle(fontSize: 26.sp),
        text: text,
        onChanged: (dynamic newV) {
          onChanged(newV);
        },
      ),
    );
  }

  ///构建打印开关
  Widget _buildPrint() {
    return _buildRadio(
      text: "打印小票",
      value: widget.print,
      onChanged: (newV) {
        _doPrint(newV);
      },
    );
  }

  ///构建支付方式选择按钮
  Widget _buildButton(
    String title,
    bool isChose, {
    IconNames iconNames = IconNames.yinhangka,
    Function? onPressed,
  }) {
    return GestureDetector(
      child: Container(
        constraints: BoxConstraints(minWidth: 200.w),
        padding: EdgeInsets.all(20.h),
        height: 90.h,
        decoration: BoxDecoration(
          color: isChose ? const Color(0xFFE9EFFF) : const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(10.w),
          border: Border.all(
            color: isChose ? const Color(0xFF4679FC) : const Color(0xFFFFFFFF),
            width: 2.w,
          ),
        ),
        child: Row(
          children: [
            Container(
              alignment: Alignment.center,
              height: 45.w,
              width: 45.w,
              decoration: BoxDecoration(
                color:
                    isChose
                        ? const Color(0xFF4679FC)
                        : const Color(0xFF686B76), // 圆形背景颜色
                shape: BoxShape.circle, // 形状设置为圆形
              ),
              child: Padding(
                padding: EdgeInsets.only(left: 0.w),
                child: IconFont(iconNames, size: 22.w, color: "#FFFFFF"),
              ),
            ),
            SizedBox(width: 20.w),
            HaloPosLabel(
              title,
              textStyle: TextStyle(fontSize: 26.sp),
              showFullText: false,
            ),
          ],
        ),
      ),
      onTap: () {
        if (onPressed != null) {
          onPressed();
        }
      },
    );
  }

  ///构建选中的各种支付渠道的金额输入框
  List<Widget> _buildContentPay() {
    List<Widget> widgets = [];
    //储值支付方式
    AtypeInfoBean? storePayment;
    widgets.addAll(
      _paymentList
          .where((element) {
            if (!element.enable) {
              return false;
            }
            if (element.storePayway.paywayType == 3) {
              //储值
              storePayment ??= element;
              return false;
            }
            return true;
          })
          .map((e) {
            return _buildEditRow(
              rightText:
                  e.storePayway.paywayType! == 0 && !widget.isRefund
                      ? "找 ¥ ${widget.pettyCash}"
                      : "",
              //找零ui
              title: e.storePayway.paywayName!,
              focusNode: e.focusNode,
              afterPoint: SpTool.getSystemConfig().sysDigitalTotal,
              textString: DecimalDisplayHelper.getTotalFixed(
                e.total.toString(),
              ),
              maxValue: _getMaxValue(e),
              enable: e.modify,
              showTips: widget.isRefund && e.storePayway.paywayType == 2,
              onChange: (text) {
                if (e.posTotal != text) {
                  _onEditChange(e, text);
                }
              },
            );
          })
          .toList(),
    );
    if (storePayment != null) {
      bool enable = false;
      //对于非退款，则允许修改
      if (!widget.isRefund) {
        enable = storePayment!.modify;
      }
      widgets.add(
        _buildEditSingleRow(
          rightText: "",
          //找零ui
          title: storePayment!.storePayway.paywayName!,
          focusNode: storePayment!.focusNode,
          afterPoint: SpTool.getSystemConfig().sysDigitalTotal,
          textString: DecimalDisplayHelper.getTotalFixed(
            storePayment!.posTotal.toString(),
          ),
          maxValue: _getMaxValue(
            storePayment!,
            max: !widget.isRefund ? num.parse(widget.storeTotal) : -1,
          ),
          enable: enable,
          showTips:
              widget.isRefund && storePayment!.storePayway.paywayType == 2,
          onChange: (text) {
            if (text != storePayment!.posTotal) {
              _onEditChange(storePayment!, text);
            }
          },
        ),
      );
    }
    //warp 需要一个垫底view做拉伸
    widgets.add(
      Container(
        width: double.infinity,
        height: 0.1.h,
        color: Colors.transparent,
      ),
    );
    return widgets;
  }

  Widget _buildEditRow({
    String title = "",
    String textString = "",
    required Function(String value) onChange,
    required KeyboardHiddenFocusNode focusNode,
    int afterPoint = 2,
    required num maxValue,
    bool chose = true,
    bool enable = false,
    String rightText = "",
    bool showYuan = true,
    bool showTips = false,
  }) {
    final width =
        ((MediaQuery.of(context).size.width - 14.w * 3) * 7 / 11 - 110.w) / 2;
    final height = 100.h;
    return Stack(
      alignment: Alignment.topRight,
      clipBehavior: Clip.none,
      fit: StackFit.expand,
      children: [
        Container(
          clipBehavior: Clip.none,
          height: height,
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.all(Radius.circular(4.0)),
            border: Border.all(color: const Color(0xFFCFCFCF), width: 2.w),
          ),
          padding: EdgeInsets.symmetric(horizontal: 26.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(right: 8.w),
                width: 140.w,
                child: HaloPosLabel(
                  title,
                  textStyle: TextStyle(fontSize: 28.sp),
                ),
              ),
              Visibility(
                visible: showYuan,
                child: HaloPosLabel("¥", textStyle: TextStyle(fontSize: 28.sp)),
              ),
              Expanded(
                flex: 1,
                child: Container(
                  padding: EdgeInsets.only(top: 2.h),
                  child: PtypeTextField(
                    enable: enable,
                    textAlign: TextAlign.start,
                    afterPoint: afterPoint,
                    focusNode: focusNode,
                    maxValue: maxValue,
                    defaultText: textString,
                    onChange: onChange,
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: -20.w,
          child: Visibility(
            visible: !TextUtil.isEmpty(rightText),
            child: HaloContainer(
              borderRadius: BorderRadius.all(Radius.circular(5.w)),
              padding: EdgeInsets.all(5.w),
              color: Colors.red,
              constraints: BoxConstraints(
                maxWidth: width,
                minWidth: 50.w,
                minHeight: 40.w,
              ),
              children: [
                Flexible(
                  flex: 1,
                  child: HaloPosLabel(
                    rightText,
                    textStyle: TextStyle(color: Colors.white, fontSize: 20.sp),
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: -28.h,
          left: 0,
          child: Visibility(
            visible: showTips,
            child: HaloLabel(
              "聚合支付退款时，退款金额将自动退回到原支付账户中",
              textStyle: TextStyle(fontSize: 20.sp, color: Colors.red),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEditSingleRow({
    String title = "",
    String textString = "",
    required Function(String value) onChange,
    required KeyboardHiddenFocusNode focusNode,
    int afterPoint = 2,
    required num maxValue,
    bool chose = true,
    bool enable = false,
    required String rightText,
    bool showYuan = true,
    bool showTips = false,
    // Color rightTextColor = const Color(0xFFEA4818),
  }) {
    final width =
        ((MediaQuery.of(context).size.width - 14.w * 3) * 7 / 11 - 110.w) / 2;
    final height = 100.h;

    String total = DecimalDisplayHelper.getTotalFixed(
      (num.parse(widget.storeTotal) - num.parse(textString)).toString(),
    );
    if (widget.isRefund) {
      total = DecimalDisplayHelper.getTotalFixed(
        (num.parse(widget.storeTotal) + num.parse(textString)).toString(),
      );
    }
    return Stack(
      alignment: Alignment.topRight,
      clipBehavior: Clip.none,
      fit: StackFit.expand,
      children: [
        Container(
          width: width,
          height: height,
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.all(Radius.circular(4.0)),
            border: Border.all(color: const Color(0xFFCFCFCF), width: 2.w),
          ),
          padding: EdgeInsets.symmetric(horizontal: 26.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: EdgeInsets.only(right: 8.w),
                width: 140.w,
                child: HaloPosLabel(
                  title,
                  textStyle: TextStyle(fontSize: 28.sp),
                ),
              ),
              Visibility(
                visible: showYuan,
                child: HaloPosLabel("¥", textStyle: TextStyle(fontSize: 28.sp)),
              ),
              Expanded(
                flex: 1,
                child: Container(
                  padding: EdgeInsets.only(top: 2.h),
                  child: PtypeTextField(
                    enable: enable,
                    textAlign: TextAlign.start,
                    afterPoint: afterPoint,
                    focusNode: focusNode,
                    maxValue: maxValue,
                    defaultText: textString,
                    onChange: onChange,
                  ),
                ),
              ),
              Padding(padding: EdgeInsets.only(left: 10.w)),
            ],
          ),
        ),
        Positioned(
          top: -20.w,
          child: HaloContainer(
            borderRadius: BorderRadius.all(Radius.circular(5.w)),
            padding: EdgeInsets.all(5.w),
            color: Colors.green,
            constraints: BoxConstraints(
              maxWidth: width,
              minWidth: 50.w,
              minHeight: 40.w,
            ),
            children: [
              Flexible(
                flex: 1,
                child: HaloLabel(
                  "余额: ¥ $total",
                  textStyle: TextStyle(color: Colors.white, fontSize: 20.sp),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: -28.h,
          left: 0,
          child: Visibility(
            visible: showTips,
            child: HaloLabel(
              "聚合支付退款时，退款金额将自动退回到原支付账户中",
              textStyle: TextStyle(fontSize: 20.sp, color: Colors.red),
            ),
          ),
        ),
      ],
    );
  }

  //endregion

  //region action

  void _onEditChange(AtypeInfoBean infoBean, String text) {
    infoBean.total = text;
    infoBean.posTotal = text;
    widget.onEditChange(
      PaymentEditChange(
        paymentList: _paymentList,
        print: widget.print,
        combiPay: widget.combinationPay,
        etype: widget.etype,
      ),
    );
  }

  void _doPrint(newV) {
    setState(() {
      widget.onEditChange(
        PaymentEditChange(
          paymentList: _paymentList,
          print: widget.print,
          combiPay: widget.combinationPay,
          etype: widget.etype,
        ),
      );
      SpTool.savePrint(widget.print);
    });
  }

  void _doCombinationPay(newV) {
    bool newValue = !widget.combinationPay;
    widget.onEditChange(
      PaymentEditChange(
        paymentList: _paymentList,
        print: widget.print,
        combiPay: newValue,
        etype: widget.etype,
      ),
    );
    //先让外部调用setState刷新，否则这里读取到的值还是旧的
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        if (newValue) {
          singlePayment = _paymentList.firstWhereOrNull(
            (element) => element.enable,
          );
          singlePaymentTotal = singlePayment?.total.toString() ?? "0";
          singlePayment?.modify = true;
        } else {
          editing = true;
          if (singlePayment != null) {
            singlePayment!.enable = false;
            singlePayment!.total = singlePaymentTotal!;
            _onButtonTap(singlePayment!, editing: false);
          }
        }
      });
    });
  }

  void _onButtonTap(AtypeInfoBean atypeInfo, {bool editing = true}) {
    if (widget.combinationPay) {
      for (var element in _paymentList) {
        element.modify = true;
        if (!element.enable) {
          element.total = "0";
          element.posTotal = "0";
        }
      }
    }
    FocusNode? focusNode;
    atypeInfo.enable = !atypeInfo.enable;
    if (atypeInfo.enable) {
      focusNode = atypeInfo.focusNode;
    }
    focusNode ??= FocusNode();

    if (!widget.combinationPay) {
      //单方式支付
      atypeInfo.total = widget.moneyReceived;
      atypeInfo.posTotal = widget.moneyReceived;
      for (var element in _paymentList) {
        if (element.storePayway.paywayType == 0) {
          element.modify = true;
        } else {
          element.modify = false;
        }
        if (!StringUtil.equal(
              atypeInfo.storePayway.atypeId!,
              element.storePayway.atypeId!,
            ) ||
            element.storePayway.paywayName !=
                atypeInfo.storePayway.paywayName ||
            element.storePayway.paywayType !=
                atypeInfo.storePayway.paywayType) {
          element.enable = false;
        }
      }
      if (atypeInfo.storePayway.paywayType == 3) {
        if (num.tryParse(widget.moneyReceived)! >
            num.tryParse(widget.storeTotal)!) {
          atypeInfo.posTotal = widget.storeTotal;
        }
      }
    } else {
      //多账户支付
      atypeInfo.total = "0";
      atypeInfo.posTotal = "0";
      atypeInfo.modify = true;
      //重复现金、淘淘谷、储值

      if (atypeInfo.storePayway.paywayType != 1 && atypeInfo.enable) {
        AtypeInfoBean moneyBean = _paymentList.firstWhere(
          (element) =>
              element.storePayway.paywayType ==
                  atypeInfo.storePayway.paywayType &&
              atypeInfo != element &&
              element.enable,
          orElse: () => AtypeInfoBean(StorePayway(), "0"),
        );
        if (moneyBean.storePayway.id != null) {
          atypeInfo.total = moneyBean.total;
          atypeInfo.posTotal = moneyBean.posTotal;
          moneyBean.total = "0";
          moneyBean.posTotal = "0";
          moneyBean.enable = false;
          String msg = "";
          switch (moneyBean.storePayway.paywayType) {
            case 0:
              msg = "现金";
              break;
            case 2:
              msg = "淘淘谷";
              break;
            case 3:
              msg = "储值";
              break;
          }
          HaloToast.show(context, msg: "只能选一个$msg账户来完成组合支付");
        }
      }
    }
    if (atypeInfo.storePayway.id != null &&
        atypeInfo.enable &&
        atypeInfo.modify &&
        editing) {
      focusNode.requestFocus();
    } else {
      FocusScope.of(context).requestFocus(FocusNode());
    }
    widget.onEditChange(
      PaymentEditChange(
        paymentList: _paymentList,
        print: widget.print,
        combiPay: widget.combinationPay,
        etype: widget.etype,
      ),
    );
  }

  _doSubmit() {
    if (_paymentList.isEmpty) {
      HaloToast.show(context, msg: "请选择支付方式");
      return;
    }
    widget.doSubmitAction();
  }

  _doTips() {
    if (widget.doTipsAction != null) {
      widget.doTipsAction!();
    }
  }

  //endregion
}

class PaymentEditChange {
  List<AtypeInfoBean> paymentList;
  bool print; //打印小票
  bool combiPay; //组合方式
  StoreEtype? etype; //业务员
  PaymentEditChange({
    required this.paymentList,
    required this.print,
    required this.combiPay,
    required this.etype,
  });
}
