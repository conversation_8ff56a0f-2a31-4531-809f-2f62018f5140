import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../common/tool/sp_tool.dart';
import '../../enum/bill_type.dart';
import '../../login/entity/store/store_etype.dart';
import '../../login/entity/store/store_payment.dart';
import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../entity/back_goods_bill_list_bean.dart';
import '../entity/bill_sale_bill_detail_dto.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/payment_dto.dart';
import '../entity/preferential_dto.dart';
import '../tool/bill_tool.dart';
import 'entity/atype_info_bean.dart';
import 'sale_bill_settlement.dart';

///结算弹窗
class BackBillSettlementPage extends SaleBillSettlementPage {
  ///单据类型
  final BillType billType;

  ///原销售出库单所有产生的退货单和换货单
  final List<BackGoodsBillListBean?>? backGoodsBillList;

  ///退货单要退资产
  final VipAssertsBillDtoBean? vipAssertsBillDtoBean;

  const BackBillSettlementPage(
      {Key? key,
      required GoodsBillDto goodsBillDto,
      this.billType = BillType.SaleBackBill,
      VipWithLevelAssertsRightsCardDTO? vipInfo,
      this.vipAssertsBillDtoBean,
      this.backGoodsBillList,
      required Function successCallback})
      : super(
            key: key,
            goodsBillDto: goodsBillDto,
            vipInfo: vipInfo,
            successCallback: successCallback);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _BackBillSettlementState();
}

class _BackBillSettlementState
    extends SaleBillSettlementState<BackBillSettlementPage> {
  ///单据类型
  @override
  BillType get billType => widget.billType;


  @override
  void initEtype() {
    if (isRefund) {
      etype = StoreEtype()
        ..etypeId =  goodsBillDto.etypeId ?? SpTool.getEtypeId()
        ..etypeName = goodsBillDto.efullname??  SpTool.getEtypeName();
    }
  }

  @override
  void initPaywayList() {
    super.initPaywayList();
    if (isRefund) {
      initRefundPaywayList();
    }
  }

  @override
  void initPayment() {
    if (isRefund) {
      initRefundPayment();
    } else {
      super.initPayment();
    }
  }

  ///退款/换货退款 获取可用的支付方式
  void initRefundPaywayList() {
    List<StorePayway> sourcePaymentList = [];

    ///匹配原单
    for (var payment in goodsBillDto.sourcePayment) {
      StorePayway? storePayway = paywayList
          .firstWhereOrNull((element) => element.paywayId == payment.paywayId);
      if (storePayway != null) {
        if (Decimal.parse(getPaymentTypeTotal(storePayway.paywayId!)) >
            Decimal.zero) {
          sourcePaymentList.add(storePayway);
        }
      }
    }

    //大于1种的支付 属于原单组合支付
    if (goodsBillDto.sourcePayment.length > 1) {
      //原单组合支付
      combinationPay = true;
      //增加现金
      StorePayway? storePayway =
          paywayList.firstWhereOrNull((element) => element.paywayType == 0);
      //找不到现金 也就是原单就不包含现金需要新增现金
      if (storePayway != null &&
          !sourcePaymentList.any((element) => element.paywayType == 0)) {
        sourcePaymentList.add(storePayway);
      }
    } else {
      //单支付
      combinationPay = false;
      //先找储值
      StorePayway? storePayway = sourcePaymentList
          .firstWhereOrNull((element) => element.paywayType == 3);
      //没有储值 也没有现金的情况下 在原单的支付方式下 增加现金
      if (storePayway == null &&
          !sourcePaymentList.any((element) => element.paywayType == 0)) {
        //不存在储值 需要增加现金的支付方式
        StorePayway? storePayway =
            paywayList.firstWhereOrNull((element) => element.paywayType == 0);
        if (storePayway != null) {
          sourcePaymentList.add(storePayway);
        }
      }
    }
    paywayList = sourcePaymentList;
  }

  ///获取退换货原单支付方式金额
  String getPaymentTypeTotal(String paywayId) {
    ///先找到原单的
    Decimal sumTotal = Decimal.zero;
    PaymentDto? sourcePaymentDto = widget.goodsBillDto.sourcePayment
        .firstWhereOrNull((element) => element.paywayId == paywayId);
    if (sourcePaymentDto == null) {
      return "0";
    }
    if (widget.backGoodsBillList != null) {
      for (var element in widget.backGoodsBillList!) {
        //换货单的收款 不计算
        if (element!.goodsbill!.vchtype ==
                BillTypeData[BillType.SaleChangeBill] &&
            num.parse(element.goodsbill!.currencyBillTotal) < 0) {
          continue;
        } else {
          PaymentDto? paymentDto = element.goodsbill!.payment
              .firstWhereOrNull((element) => element.paywayId == paywayId);
          if (paymentDto != null) {
            sumTotal = MathUtil.add(
                sumTotal.toString(), paymentDto.currencyAtypeTotal);

            //储值支付需增加已退赠金
            if (paymentDto.paywayType == 3) {
              sumTotal = MathUtil.add(sumTotal.toString(),
                  element.goodsbill?.currencyGivePreferentialTotal.toString());
            }
          }
        }
      }
    }
    String? posCurrencyAtypeTotal = sourcePaymentDto.currencyAtypeTotal;
    if (sourcePaymentDto.paywayType == 3) {
      posCurrencyAtypeTotal = MathUtil.add(posCurrencyAtypeTotal,
              goodsBillDto.originalCurrencyGivePreferentialTotal.toString())
          .toString();
    }
    return MathUtil.subtraction(posCurrencyAtypeTotal, sumTotal.toString())
        .toString();
  }

  ///初始化退款的支付方式
  void initRefundPayment() {
    PaymentDto? sourceStorePayment;
    //找到退换货单中的储值支付方式，并且找到当前支付方式中对应的储值支付方式
    if (widget.vipAssertsBillDtoBean != null) {
      sourceStorePayment = goodsBillDto.sourcePayment
          .firstWhereOrNull((element) => element.paywayType == 3);
    }
    AtypeInfoBean? storePayment;
    paymentList = [];
    for (int i = 0; i < paywayList.length; i++) {
      StorePayway payWay = paywayList[i];
      //屏蔽储值支付
      if (payWay.paywayType == 3 && !enableStore) {
        continue;
      }
      final payment = AtypeInfoBean(payWay, "0", enable: false, modify: false);

      String maxTotal = getPaymentTypeTotal(payWay.paywayId!);

      //非现金支付方式
      if (payWay.paywayType != 0) {
        payment.maxTotal = maxTotal;
      } else {
        //现金的方式 不需要设置原支付方式的最大值，现金的最大值就是所退款的最大值
        payment.maxTotal = moneyReceived.toString();
      }
      paymentList.add(payment);
      //找到匹配原单的储值支付方式
      if (sourceStorePayment != null &&
          storePayment == null &&
          payWay.paywayType == 3 &&
          payWay.paywayId == sourceStorePayment.paywayId) {
        storePayment = payment;
      }
    }
    ///处理客户问题，退货商品金额为0时候 且只有一个现金支付方式，默认没选中导致结算方式为空的bug
    if(paymentList.length == 1){
      paymentList.first.enable = true;
    }
    //剩余退款金额
    Decimal leftTotal = Decimal.parse(moneyReceived.toString());
    //处理退款储值,储值金额不可变
    if (storePayment != null) {
      //typed 1 本金 2赠金
      Decimal vipChargeTotal = Decimal.zero;
      Decimal sumGiveTotal = Decimal.zero;
      for (var element in widget.vipAssertsBillDtoBean!.assertsBillDetailDtoList) {
        if (element.typed == "1") {
          vipChargeTotal += Decimal.tryParse(element.qty!) ?? Decimal.zero;
        }
        if (element.typed == "2") {
          sumGiveTotal +=
              (Decimal.tryParse(element.qty!) ?? Decimal.zero).abs();
        }
      }
      if (vipChargeTotal != Decimal.zero || sumGiveTotal != Decimal.zero) {
        storePayment.enable = true;
      }
      storePayment.total = vipChargeTotal.toString();
      storePayment.posTotal =
          MathUtil.add(storePayment.total, sumGiveTotal.toString()).toString();
      //剩余金额减去储值金额
      leftTotal = MathUtil.subtraction(
          leftTotal.toString(), storePayment.posTotal.toString());
    }


    //依次递减，给其他支付方式赋值
    //扫码
    leftTotal = setRefundPaymentTotal(paywayType: 2, leftTotal: leftTotal);
    //银行卡
    leftTotal = setRefundPaymentTotal(paywayType: 1, leftTotal: leftTotal);
    //第三方
    leftTotal = setRefundPaymentTotal(paywayType: 4, leftTotal: leftTotal);
    //现金
    leftTotal = setRefundPaymentTotal(paywayType: 0, leftTotal: leftTotal);
  }

  ///设置退款支付方式的金额
  Decimal setRefundPaymentTotal(
      {required int paywayType, required Decimal leftTotal}) {
    if (leftTotal > Decimal.zero) {
      final list = paymentList
          .where((element) => element.storePayway.paywayType == paywayType)
          .toList();
      if (list.isNotEmpty) {
        //现金和扫码支付只有一个
        if (paywayType == 0 || paywayType == 2) {
          leftTotal = getLeftTotal(payment: list.first, left: leftTotal);
        } else {
          for (var payment in list) {
            leftTotal = getLeftTotal(payment: payment, left: leftTotal);
          }
        }
      }
    }
    return leftTotal;
  }

  ///计算剩余支付金额
  Decimal getLeftTotal(
      {required AtypeInfoBean payment, required Decimal left}) {
    Decimal leftTotal = left;
    Decimal total = Decimal.parse(payment.maxTotal);
    if (total >= leftTotal) {
      total = leftTotal;
      leftTotal = Decimal.zero;
    } else {
      leftTotal = MathUtil.subtraction(leftTotal.toString(), payment.maxTotal);
    }
    payment.total = total.toString();
    payment.posTotal = payment.total;
    payment.enable = true;
    //储值不修改 /非组合支付不能修改
    payment.modify = combinationPay && payment.storePayway.paywayType != 3;
    return leftTotal;
  }

  @override
  String calculateStoreMoney(GoodsBillDto goodsBill, AtypeInfoBean store) {
    if (!isRefund) {
      return super.calculateStoreMoney(goodsBill, store);
    }
    //无会员或者会员无储值资产信息
    if (null == vipInfo ||
        null == vipInfo?.asserts ||
        null == vipInfo?.asserts?.total) {
      return "0";
    }
    //未选择储值支付或者支付方式未被启用
    if (null == store.storePayway.id || !store.enable) {
      /// 若换入金额大于0，需支付的发支付方式无储值，将退入赠金分摊到出库明细上
      if (goodsBill.sumInDetailGiveTotal > 0) {
        BillTool.setStoreTotalGiftTotal(goodsBill, 0, billType,
            backSumGiveTotal: goodsBill.sumInDetailGiveTotal);
      }
      return "0";
    }

    //记录优惠辅助表
    PreferentialDto preferentialDto = PreferentialDto();
    preferentialDto.total = goodsBill.currencyGivePreferentialTotal;
    goodsBill.currencyAdvanceTotal = num.parse(store.total);
    preferentialDto.type = PreferentialType.GiftTotal.value;
    goodsBill.preferentialHelp[Preferential.giftStore.name] = preferentialDto;
    //退货单无需重新分摊赠金
    if (billType == BillType.SaleChangeBill) {
      BillTool.setStoreTotalGiftTotal(goodsBill, goodsBill.currencyGivePreferentialTotal, billType,
          backSumGiveTotal: goodsBill.sumInDetailGiveTotal);
    }
    return store.total;
  }

  @override
  Future<bool> buildScanInfo(PaymentDto scanPayment, String newOutNo) async {
    if (!isRefund) {
      return super.buildScanInfo(scanPayment, newOutNo);
    }
    String? outNo = goodsBillDto.sourcePayment
        .firstWhereOrNull((element) => element.outNo?.isNotEmpty == true)
        ?.outNo;
    if (outNo == null || outNo.isEmpty) {
      HaloToast.show(context, msg: "没有可退的聚合支付！");
      return true;
    }
    String? orderNo = goodsBillDto.sourcePayment
        .firstWhereOrNull((element) => element.payOrderNo!.isNotEmpty)
        ?.payOrderNo;
    isPaying = true;
    Map params = {
      "tradeAmount": num.parse(scanPayment.currencyAtypeTotal!).abs().toString(),
      "outNo": outNo,
      "orderNo": orderNo,
      "paywayId": scanPayment.paywayId,
      "refundOutNo": newOutNo,
    };
    scanPayment.outNo = newOutNo;
    payInfo = params;
    return true;
  }
}
