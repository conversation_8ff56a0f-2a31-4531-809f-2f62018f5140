// import 'dart:convert';
// import 'dart:math';
//
// import 'package:decimal/decimal.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:halo_pos/bill/settlement/settlement_mixin.dart';
// import 'package:halo_pos/common/tool/payment.dart';
// import 'package:halo_pos/common/tool/payment_orders_mixin.dart';
// import 'package:halo_utils/utils/string_util.dart';
// import 'package:haloui/utils/math_util.dart';
// import 'package:haloui/widget/halo_toast.dart';
//
// import '../../cashierbox/entity/cash_box_payment_request_dto.dart';
// import '../../common/login/login_center.dart';
// import '../../common/tool/sp_tool.dart';
// import '../../common/tool/system_config_tool.dart';
// import '../../db/bill_db_manager.dart';
// import '../../enum/bill_decimal_type.dart';
// import '../../enum/bill_pay_state.dart';
// import '../../enum/bill_post_state.dart';
// import '../../enum/bill_type.dart';
// import '../../login/entity/store/store_etype.dart';
// import '../../login/entity/store/store_payment.dart';
// import '../../offline/offline_tool.dart';
// import '../../plugin/scanner_plugin.dart';
// import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
// import '../../widgets/base/base_stateful_page.dart';
// import '../entity/back_goods_bill_list_bean.dart';
// import '../entity/bill_sale_bill_detail_dto.dart';
// import '../entity/goods_bill.dto.dart';
// import '../entity/goods_detail_dto.dart';
// import '../entity/payment_dto.dart';
// import '../entity/preferential_dto.dart';
// import '../tool/bill_tool.dart';
// import '../tool/decimal_display_helper.dart';
// import '../tool/promotion/preferential_tool.dart';
// import '../tool/sms_verification_tool.dart';
// import '../tool/store_tool.dart';
// import 'entity/atype_info_bean.dart';
// import 'entity/score_configuration.dart';
// import 'widget/add_tips_page.dart';
// import 'widget/payment_widget.dart';
//
// ///结算弹窗
// @Deprecated("已废弃，使用新的结算界面")
// class DialogSettlementPage extends BaseStatefulPage {
//   ///单据类型
//   final BillType billType;
//
//   ///单据信息
//   final GoodsBillDto goodsBillDto;
//
//   ///原销售出库单所有产生的退货单和换货单
//   final List<BackGoodsBillListBean?>? backGoodsBillList;
//
//   ///成功回调
//   final Function successCallback;
//
//   ///积分策略
//   final ScoreConfiguration? scoreConfiguration;
//
//   ///会员信息
//   final VipWithLevelAssertsRightsCardDTO? vipInfo;
//
//   ///todo 拆分退货
//   ///退货单要退资产
//   final VipAssertsBillDtoBean? vipAssertsBillDtoBean;
//
//   ///当前使用积分数量
//   final int? scoreUsed;
//
//   ///会员使用优惠券时，优惠券明细id
//   final List<String>? cardDetailIds;
//
//   const DialogSettlementPage(
//       {Key? key,
//       required this.goodsBillDto,
//       this.billType = BillType.SaleBill,
//       this.vipInfo,
//       this.scoreUsed = 0,
//       this.vipAssertsBillDtoBean,
//       this.cardDetailIds,
//       this.scoreConfiguration,
//       this.backGoodsBillList,
//       required this.successCallback})
//       : super(key: key);
//
//   @override
//   BaseStatefulPageState<BaseStatefulPage> createState() =>
//       _DialogSettlementPageState();
// }
//
// class _DialogSettlementPageState
//     extends BaseStatefulPageState<DialogSettlementPage>
//     with PaymentOrdersMixin {
//   ///是否需要验证会员消费密码
//   bool validVipPwd = false;
//
//   ///组合方式
//   bool combinationPay = false;
//
//   ///是否正在支付
//   bool isPaying = false;
//
//   ///选择职员的数据
//   StoreEtype? _etype;
//
//   ///所有付款金额相加
//   String finalGetMoney = "0";
//
//   ///实收
//   String moneyReceived = "0";
//
//   ///赠送积分的金额
//   Decimal giveScoreMoney = Decimal.zero;
//
//   ///支付方式数据源
//   List<StorePayway> paywayList = [];
//
//   ///单据类型
//   @override
//   BillType get billType => widget.billType;
//
//   ///当前使用积分数量
//   int scoreUsed = 0;
//
//   ///会员使用优惠券时，优惠券明细id
//   List<String> cardDetailIds = [];
//
//   bool isReturnOrChange = false;
//
//   //region method
//   @override
//   Future<void> onInitState() async {
//     _initData();
//     _updateValue();
//     _initPayment();
//     _updateSinglePay();
//     _calculateFinalGetMoneyAndPettyCash();
//     super.initState();
//   }
//
//   ///初始化上个页面的数据
//   _initData() {
//     ///默认营业员
//     _etype = StoreEtype()
//       ..etypeId = SpTool.getEtypeId()
//       ..etypeName = SpTool.getEtypeName();
//     vipAssertsBillDtoBean = widget.vipAssertsBillDtoBean;
//     paywayList = filterPayWayList(
//         enableStore: widget.vipInfo != null,
//         isOffline: OffLineTool().isOfflineLogin);
//     goodsBillDto = widget.goodsBillDto;
//     vipInfo = widget.vipInfo;
//     scoreUsed = widget.scoreUsed ?? 0;
//     cardDetailIds = widget.cardDetailIds ?? [];
//     isReturnOrChange = (billType == BillType.SaleBackBill) ||
//         (billType == BillType.SaleChangeBill) &&
//             num.parse(goodsBillDto.currencyBillTotal) <= 0;
//   }
//
//   _updateValue() {
//     ///实收金额取单据金额绝对值
//     moneyReceived =
//         num.parse(goodsBillDto.posCurrencyBillTotal).abs().toString();
//
//     ///根据明细计算总金额 优惠 总qty ？？
//     BillTool.reCalcStatistic(goodsBillDto, billType: billType);
//   }
//
//   ///初始化多账号支付方式=>在这里处理进来的默认支付方式
//   void _initPayment() {
//     ///退款/换货退款 获取可用的支付方式
//     if (isReturnOrChange) {
//       _getAvailablePaymentList();
//     }
//     paymentList = [];
//     for (int i = 0; i < paywayList.length; i++) {
//       StorePayway storePayway = paywayList[i];
//       //非会员时，屏蔽储值支付
//       if (storePayway.paywayType == 3 && vipInfo == null) {
//         continue;
//       }
//       AtypeInfoBean infoBean =
//           AtypeInfoBean(storePayway, "0", enable: false, modify: false);
//       paymentList.add(infoBean);
//     }
//
//     if (isReturnOrChange) {
//       _initBackOrChangePayment();
//     } else {
//       ///银行卡
//       _initPaymentAtypeInfoBean(paywayType: 1);
//
//       ///现金
//       _initPaymentAtypeInfoBean(paywayType: 0);
//
//       ///其他第三方
//       _initPaymentAtypeInfoBean(paywayType: 4);
//
//       ///聚合支付
//       _initPaymentAtypeInfoBean(paywayType: 2);
//
//       ///储值
//       _initStorePayment();
//     }
//   }
//
//   void _initBackOrChangePayment() {
//     paymentList = [];
//     for (int i = 0; i < paywayList.length; i++) {
//       StorePayway storePayway = paywayList[i];
//       AtypeInfoBean infoBean =
//           AtypeInfoBean(storePayway, "0", enable: false, modify: false);
//       String maxTotal = getPaymentTypeTotal(storePayway.paywayId!);
//
//       //非现金支付方式
//       if (storePayway.paywayType != 0) {
//         infoBean.maxTotal = maxTotal;
//       } else {
//         //现金的方式 不需要设置原支付方式的最大值，现金的最大值就是所退款的最大值
//         infoBean.maxTotal = moneyReceived;
//       }
//
//       ///加载非储值账户
//       if (storePayway.paywayType != 3) {
//         paymentList.add(infoBean);
//       }
//
//       ///有会员时加载储值账户
//       else if (storePayway.paywayType == 3 && vipInfo != null) {
//         paymentList.add(infoBean);
//       }
//     }
//
//     ///优先储值并不允许修改
//     _initStorePayment();
//
//     ///其他支付方式按优先级分配
//     _buildPaymentTotal();
//   }
//
//   _buildPaymentTotal() {
//     Decimal leftTotal = Decimal.parse(moneyReceived);
//
//     ///先找到固定的储值支行剩余的需要支付的金额
//     AtypeInfoBean store = paymentList.firstWhere(
//         (element) => element.storePayway.paywayType == 3,
//         orElse: () => AtypeInfoBean(StorePayway(), "0"));
//
//     if (store.storePayway.id != null) {
//       leftTotal = MathUtil.subtraction(leftTotal.toString(), store.posTotal);
//     }
//     /** 优先级如下 */
//
//     ///是否有扫码支付
//     AtypeInfoBean storePay = paymentList.firstWhere(
//         (element) => element.storePayway.paywayType == 2,
//         orElse: () => AtypeInfoBean(StorePayway(), "0"));
//     if (leftTotal > Decimal.zero && storePay.storePayway.id != null) {
//       leftTotal = getLeftTotal(paywayType: storePay, left: leftTotal);
//     }
//
//     ///银行卡
//     List<AtypeInfoBean> storeCard = paymentList
//         .where((element) => element.storePayway.paywayType == 1)
//         .toList();
//     for (var element in storeCard) {
//       if (leftTotal > Decimal.zero && element.storePayway.id != null) {
//         leftTotal = getLeftTotal(paywayType: element, left: leftTotal);
//       }
//     }
//
//     ///是否第三方
//     List<AtypeInfoBean> storeOther = paymentList
//         .where((element) => element.storePayway.paywayType == 4)
//         .toList();
//     for (var element in storeOther) {
//       if (leftTotal > Decimal.zero && element.storePayway.id != null) {
//         leftTotal = getLeftTotal(paywayType: element, left: leftTotal);
//       }
//     }
//
//     ///是否现金
//     AtypeInfoBean storeCash = paymentList.firstWhere(
//         (element) => element.storePayway.paywayType == 0,
//         orElse: () => AtypeInfoBean(StorePayway(), "0"));
//     if (leftTotal > Decimal.zero && storeCash.storePayway.id != null) {
//       leftTotal = getLeftTotal(paywayType: storeCash, left: leftTotal);
//     }
//
//     if (paymentList.length == 1) {
//       AtypeInfoBean? storePayway = paymentList
//           .firstWhereOrNull((element) => element.storePayway.paywayType == 0);
//       if (storePayway != null) {
//         storePayway.enable = true;
//       }
//     }
//   }
//
//   ///退款/换货退款 获取可用的支付方式
//   _getAvailablePaymentList() {
//     List<StorePayway> sourcePaymentList = [];
//
//     ///匹配原单
//     for (var payment in goodsBillDto.sourcePayment) {
//       StorePayway? storePayway = paywayList
//           .firstWhereOrNull((element) => element.paywayId == payment.paywayId);
//       if (storePayway != null) {
//         if (Decimal.parse(getPaymentTypeTotal(storePayway.paywayId!)) >
//             Decimal.zero) {
//           sourcePaymentList.add(storePayway);
//         }
//       }
//     }
//
//     ///大于1种的支付 属于原单组合支付
//     if (goodsBillDto.sourcePayment.length > 1) {
//       ///原单组合支付
//       combinationPay = true;
//
//       ///增加现金
//       StorePayway? storePayway =
//           paywayList.firstWhereOrNull((element) => element.paywayType == 0);
//
//       ///找不到现金 也就是原单就不包含现金需要新增现金
//       if (storePayway != null &&
//           !sourcePaymentList.any((element) => element.paywayType == 0)) {
//         sourcePaymentList.add(storePayway);
//       }
//     } else {
//       ///单支付
//       combinationPay = false;
//
//       ///先找储值
//       StorePayway? storePayway = sourcePaymentList
//           .firstWhereOrNull((element) => element.paywayType == 3);
//
//       ///没有储值 也没有现金的情况下 在原单的支付方式下 增加现金
//       if (storePayway == null &&
//           !sourcePaymentList.any((element) => element.paywayType == 0)) {
//         //不存在储值 需要增加现金的支付方式
//         StorePayway? storePayway =
//             paywayList.firstWhereOrNull((element) => element.paywayType == 0);
//         if (storePayway != null) {
//           sourcePaymentList.add(storePayway);
//         }
//       }
//     }
//     paywayList = sourcePaymentList;
//   }
//
//   ///计算剩余支付金额
//   Decimal getLeftTotal(
//       {required AtypeInfoBean paywayType, required Decimal left}) {
//     Decimal leftTotal = left;
//     if (Decimal.parse(paywayType.maxTotal) >= leftTotal) {
//       _initBackPaymentAtypeInfoBean(
//           payway: paywayType, total: leftTotal.toString());
//       leftTotal = Decimal.zero;
//     } else {
//       _initBackPaymentAtypeInfoBean(
//           payway: paywayType, total: paywayType.maxTotal);
//       leftTotal =
//           MathUtil.subtraction(leftTotal.toString(), paywayType.maxTotal);
//     }
//     return leftTotal;
//   }
//
//   ///初始化支付方式
//   void _initPaymentAtypeInfoBean({required int paywayType, String? total}) {
//     AtypeInfoBean atypeInfoBean = paymentList.firstWhere(
//         (element) => element.storePayway.paywayType == paywayType,
//         orElse: () => AtypeInfoBean(StorePayway(), "0"));
//     if (atypeInfoBean.storePayway.id != null) {
//       atypeInfoBean.total = total ?? moneyReceived;
//       atypeInfoBean.posTotal = atypeInfoBean.total;
//       atypeInfoBean.enable = true;
//       if (!isReturnOrChange) {
//         ///现金才可修改
//         atypeInfoBean.modify = paywayType == 0;
//         _resetOtherPayment(atypeInfoBean);
//       } else {
//         ///储值不修改 /非组合支付不能修改
//         atypeInfoBean.modify = combinationPay ? paywayType != 3 : false;
//       }
//     }
//   }
//
//   ///初始化退款类支付方式
//   void _initBackPaymentAtypeInfoBean(
//       {required AtypeInfoBean payway, String? total}) {
//     AtypeInfoBean atypeInfoBean = paymentList.firstWhere(
//         (element) =>
//             element.storePayway.paywayId == payway.storePayway.paywayId,
//         orElse: () => AtypeInfoBean(StorePayway(), "0"));
//     if (atypeInfoBean.storePayway.id != null) {
//       atypeInfoBean.total = total ?? moneyReceived;
//       atypeInfoBean.posTotal = atypeInfoBean.total;
//       atypeInfoBean.enable = true;
//
//       ///储值不修改 /非组合支付不能修改
//       atypeInfoBean.modify =
//           combinationPay ? payway.storePayway.paywayType != 3 : false;
//     }
//   }
//
//   ///重置其他支付方式
//   void _resetOtherPayment(AtypeInfoBean atypeInfoBean) {
//     for (var element in paymentList) {
//       if (element != atypeInfoBean) {
//         element.enable = false;
//         element.total = "0";
//         element.posTotal = "0";
//       }
//     }
//   }
//
//   ///c初始化储值
//   void _initStorePayment() {
//     List<AtypeInfoBean> storeList = paymentList
//         .where((element) => element.storePayway.paywayType == 3)
//         .toList();
//
//     ///不存在储值支付
//     if (storeList.isEmpty) {
//       return;
//     }
//
//     AtypeInfoBean store = storeList.first;
//     store.enable = false;
//     //储值余额和应付款金额取最小值
//     String vipChargeTotalString = num.parse(moneyReceived) >
//             vipInfo!.asserts!.total!
//         ? DecimalDisplayHelper.getTotalFixed(vipInfo!.asserts!.total.toString())
//         : moneyReceived.toString();
//     //赠金
//     double sumGiveTotal = 0;
//
//     ///退货单储值不允许修改
//     if (isReturnOrChange) {
//       if (vipAssertsBillDtoBean != null) {
//         //找到退换货单中的储值支付方式，并且找到当前支付方式中对应的储值支付方式
//         PaymentDto storePayment = goodsBillDto.sourcePayment.firstWhere(
//             (element) => element.paywayType == 3,
//             orElse: () => PaymentDto());
//         store = storeList.firstWhere(
//             (element) => element.storePayway.paywayId == storePayment.paywayId,
//             orElse: () => AtypeInfoBean(StorePayway(), "0"));
//
//         if (store.storePayway.id != null) {
//           //typed 1 本金 2赠金
//           double vipChargeTotal = 0;
//
//           for (var element in vipAssertsBillDtoBean!.assertsBillDetailDtoList) {
//             if (element.typed == "1") {
//               vipChargeTotal += double.parse(element.qty!);
//             }
//             if (element.typed == "2") {
//               sumGiveTotal += double.parse(element.qty!).abs();
//             }
//           }
//           vipChargeTotalString =
//               DecimalDisplayHelper.getTotalFixed(vipChargeTotal.toString());
//           if (StringUtil.isNotZeroOrEmpty(vipChargeTotalString) ||
//               sumGiveTotal != 0) {
//             store.enable = true;
//           }
//         }
//       }
//     } else if (billType == BillType.SaleBill ||
//         (billType == BillType.SaleChangeBill) &&
//             num.parse(goodsBillDto.currencyBillTotal) >= 0) {
//       if ((vipInfo!.asserts?.totalMoney ?? 0) > 0) {
//         store.enable = true;
//       }
//     }
//     if (store.enable) {
//       _resetOtherPayment(store);
//     }
//     store.total = vipChargeTotalString;
//     store.posTotal =
//         MathUtil.add(store.total, sumGiveTotal.toString()).toString();
//   }
//
//   ///单一支付时要实时更新
//   void _updateSinglePay() {
//     if (!combinationPay) {
//       AtypeInfoBean atypeInfoBean = paymentList.firstWhere(
//           (element) => element.enable,
//           orElse: () => AtypeInfoBean(StorePayway(), "0"));
//       if ((billType == BillType.SaleBackBill ||
//               (billType == BillType.SaleChangeBill &&
//                   num.parse(goodsBillDto.currencyBillTotal) < 0)) &&
//           atypeInfoBean.storePayway.paywayType == 3) {
//         ///退货储值不让修改
//         atypeInfoBean.total = atypeInfoBean.total;
//         atypeInfoBean.posTotal = atypeInfoBean.posTotal;
//       } else {
//         if (atypeInfoBean.storePayway.paywayType == 3) {
//           String vipChargeTotalString =
//               num.parse(moneyReceived) > vipInfo!.asserts!.total!
//                   ? DecimalDisplayHelper.getTotalFixed(
//                       vipInfo!.asserts!.total.toString())
//                   : moneyReceived.toString();
//
//           ///计算含金量 =本金/储值
//           ///储值=本金+赠金
//           String proportion = DecimalDisplayHelper.getTotalFixed(
//               (vipInfo!.asserts!.chargeTotal! / vipInfo!.asserts!.total!)
//                   .toString());
//           atypeInfoBean.total =
//               MathUtil.multiplication(vipChargeTotalString, proportion)
//                   .toString();
//           atypeInfoBean.posTotal = vipChargeTotalString;
//         } else {
//           atypeInfoBean.total = moneyReceived;
//           atypeInfoBean.posTotal = moneyReceived;
//         }
//       }
//     }
//   }
//
//   ///获取储值本金比率
//   Decimal getStoreProportion() {
//     if (vipInfo?.asserts == null) return Decimal.zero;
//     return MathUtil.divideDec(
//         vipInfo!.asserts!.chargeTotal ?? 0, vipInfo!.asserts!.totalMoney ?? 0);
//   }
//
//   //region view
//   @override
//   Widget build(BuildContext context) {
//     super.build(context);
//     return WillPopScope(
//       child: Scaffold(
//           backgroundColor: Colors.transparent,
//           body: GestureDetector(
//             child: Container(
//               padding: EdgeInsets.only(
//                   top: 70.w + MediaQuery.of(context).padding.top),
//               alignment: Alignment.center,
//               color: Colors.transparent,
//               child: Column(
//                 children: [
//                   Expanded(child: _buildContent()),
//                 ],
//               ),
//             ),
//             onTap: () {
//               paymentCancelCallBack();
//             },
//           )),
//       onWillPop: () async {
//         paymentCancelCallBack();
//         return true;
//       },
//     );
//   }
//
//   _buildContent() {
//     return Container(
//         alignment: Alignment.center,
//         color: Colors.transparent,
//         child: Row(
//           children: [
//             Expanded(
//               flex: 1,
//               child: _buildContentLeft(),
//             ),
//             Expanded(
//               flex: 2,
//               child: GestureDetector(
//                 child: _buildContentRight(),
//                 onTap: () {
//                   ///空点击事件，屏蔽穿透点击
//                 },
//               ),
//             ),
//           ],
//         ));
//   }
//
//   ///构建左边
//   _buildContentLeft() {
//     return Container();
//   }
//
//   ///构建右边支付方式
//   _buildContentRight() {
//     String storeTotal = "0";
//     if (vipInfo != null) {
//       storeTotal = DecimalDisplayHelper.getTotalFixed(
//           vipInfo?.asserts?.total == null
//               ? "0"
//               : (vipInfo?.asserts?.total.toString() ?? "0"));
//     }
//     bool isRefund = billType == BillType.SaleBackBill ||
//         (billType == BillType.SaleChangeBill &&
//             num.parse(goodsBillDto.currencyBillTotal) < 0);
//     return PaymentWidget(
//       showChooseEtype: true,
//       print: printReceipt,
//       isRefund: isRefund,
//       etype: _etype,
//       combinationPay: combinationPay,
//       showCombinationPay: !isRefund,
//       moneyReceived: moneyReceived,
//       pettyCash: goodsBillDto.pettyCash,
//       finalGetMoney: finalGetMoney,
//       paymentList: paymentList,
//       storeTotal: storeTotal,
//       doSubmitAction: () {
//         _doSubmit();
//       },
//       doTipsAction: () {
//         _doTips();
//       },
//       onEditChange: (PaymentEditChange editChange) {
//         _onPaymentViewEditChange(editChange);
//       },
//     );
//   }
//
//   @override
//   Widget buildLeftBody(BuildContext context) {
//     return Container();
//   }
//
//   @override
//   String getActionBarTitle() {
//     return "收银机";
//   }
//
//   //region action
//   @override
//   paymentSuccessCallBack() {
//     Navigator.pop(context);
//     widget.successCallback();
//     super.paymentSuccessCallBack();
//   }
//
//   @override
//   paymentCancelCallBack() {
//     // goodsBillDto.vchcode = null;
//     // goodsBillDto.number = null;
//     Navigator.pop(context);
//     super.paymentCancelCallBack();
//   }
//
//   _onPaymentViewEditChange(PaymentEditChange editChange) {
//     paymentList = editChange.paymentList;
//     combinationPay = editChange.combiPay;
//     printReceipt = editChange.print;
//     _etype = editChange.etype;
//     //缓存经手人
//     SpTool.saveEtypeInfo(_etype?.etypeId ?? "", _etype?.etypeName ?? "");
//     _calculateFinalGetMoneyAndPettyCash();
//   }
//
//   _calculateFinalGetMoneyAndPettyCash() {
//     finalGetMoney = "0";
//     for (var element in paymentList) {
//       if (element.enable) {
//         finalGetMoney =
//             MathUtil.add(finalGetMoney, element.posTotal).toString();
//       }
//     }
//     setState(() {
//       if (isReturnOrChange) {
//         goodsBillDto.pettyCash =
//             MathUtil.subtraction(moneyReceived, finalGetMoney).toString();
//       } else {
//         goodsBillDto.pettyCash =
//             MathUtil.subtraction(finalGetMoney, moneyReceived).toString();
//       }
//     });
//   }
//
//   bool _checkNext() {
//     if (isReturnOrChange) {
//       //退款 必须相等
//       if (!MathUtil.equal(moneyReceived, finalGetMoney)) {
//         HaloToast.show(context, msg: "应退金额和输入金额不符，请检查退款金额");
//         return false;
//       }
//     } else {
//       if (MathUtil.compare(moneyReceived, finalGetMoney)) {
//         HaloToast.show(context, msg: "还未收齐款项，请检查付款金额");
//         return false;
//       }
//     }
//     return BillTool.checkBillTotalIsTooLarge(context, moneyReceived);
//   }
//
//   bool _validVipPwd() {
//     if ((SpTool.getStoreInfo()?.openStoreValuePassword ?? false) &&
//         !validVipPwd &&
//         vipInfo != null) {
//       //判断是固定密码还是短信验证
//       if (SpTool.getStoreInfo()?.passwordType == 0) {
//         StoreTool.showStorePageValid(
//             context, vipInfo?.vip?.id ?? "", vipInfo?.vip?.phone, (value) {
//           if (value == true) {
//             validVipPwd = true;
//             _doSubmit();
//           }
//         });
//       } else {
//         SMSTool.showSMSDialog(context, vipInfo?.vip?.phone ?? "", (value) {
//           if (value == true) {
//             validVipPwd = true;
//             _doSubmit();
//           }
//         }, title: "短信验证");
//       }
//       return false;
//     }
//     return true;
//   }
//
//   bool _checkVipPwdSet() {
//     if ((SpTool.getStoreInfo()?.openStoreValuePassword ?? false) &&
//         vipInfo != null &&
//         vipInfo?.vip?.password?.isEmpty == true &&
//         SpTool.getStoreInfo()?.passwordType == 0) {
//       StoreTool.showStorePageSet(context, vipInfo?.vip?.id ?? "", (value) {
//         vipInfo?.vip?.password = value;
//         validVipPwd = true;
//         _doSubmit();
//       });
//       return false;
//     }
//     return true;
//   }
//
//   String getVipString() {
//     if ((billType == BillType.SaleChangeBill &&
//             num.parse(goodsBillDto.currencyBillTotal) >= 0) ||
//         billType == BillType.SaleBill) {
//       return "消费";
//     } else {
//       return "退款";
//     }
//   }
//
//   _doTips() {
//     showDialog(
//         context: context,
//         builder: (c) {
//           return AddTipsPage(
//             defaultText: goodsBillDto.tips,
//             submitCallback: (String text) {
//               goodsBillDto.tips = text;
//             },
//           );
//         });
//   }
//
//   _doSubmit() async {
//     if (!_checkNext()) {
//       return;
//     }
//
//     ///储值支付开启
//     AtypeInfoBean? store = paymentList.firstWhereOrNull(
//         (element) => element.storePayway.paywayType == 3 && element.enable);
//
//     ///未选择储值的支付方式
//     /// 支持出库单或金额为正的换货单 TODO 不应该在此处验证单据，应该在结算界面控制可选的支付方式
//     if (null != store &&
//         store.storePayway.id != null &&
//         store.enable &&
//         (billType == BillType.SaleBill ||
//             (billType == BillType.SaleChangeBill &&
//                 num.parse(goodsBillDto.currencyBillTotal) > 0))) {
//       if (!_checkVipPwdSet()) {
//         return;
//       }
//       if (!_validVipPwd()) {
//         return;
//       }
//     }
//     BillTool.setGoodsBillDefaultValue(goodsBillDto, _etype);
//
//     GoodsBillDto goodsBill = GoodsBillDto.fromMap(goodsBillDto.toJson());
//
//     ///促销验证使用
//     // PromotionTool.deleteNoUsePromotion(
//     //     goodsBill.outDetail, goodsBill.giftCouponListOld);
//
//     goodsBill.vipCardId = vipInfo?.vip?.id;
//
//     ///pos 单据实际金额，包含储值赠金
//     String payMoney = goodsBill.currencyBillTotal.toString();
//
//     ///赠送积分的金额
//     giveScoreMoney = BillTool.getGiveScoreMoney(
//         goodsBill: goodsBill,
//         billType: widget.billType,
//         scoreConfiguration: widget.scoreConfiguration);
//
//     ///备注
//     goodsBill.memo = BillTool.buildMemo(goodsBill, vipInfo);
//
//     ///含有储值支付
//     ///
//     String storedValue = _calculateStoreMoney(goodsBill, store);
//
//     ///未含储值支付,将储值支付的
//     if (null == store ||
//         store.storePayway.id == null ||
//         store.enable == false) {
//       goodsBill.currencyGivePreferentialTotal = 0;
//
//       ///未含储值支付时，将储值支付的赠金优惠清除
//       goodsBill.preferentialHelp.remove(Preferential.giftStore.name);
//       for (GoodsDetailDto item in goodsBill.inDetail) {
//         item.currencyGivePreferentialTotal = 0;
//         item.givePreferentialTotal = 0;
//       }
//     }
//
//     ///将支付方式提交上去
//     _setPayment(goodsBill, storedValue);
//
//     BillTool.setGoodsBillDate(goodsBill);
//     if (OffLineTool().isOfflineLogin) {
//       _offLineSubmitBill(goodsBill);
//     } else {
//       _onLineSubmitBill(goodsBill, storedValue, payMoney);
//     }
//   }
//
//   _offLineSubmitBill(GoodsBillDto goodsBill) {
//     for (var element in goodsBill.outDetail) {
//       if (StringUtil.isNotZeroOrEmpty(element.promotionId)) {
//         HaloToast.showMsg(context, msg: "离线开单失败，离线开单不能使用促销");
//         return;
//       }
//     }
//     if ((goodsBill.preferentialHelp[Preferential.billPromotion.name]?.total ??
//                 0) !=
//             0 ||
//         (goodsBill.preferentialHelp[Preferential.goodsPromotion.name]?.total ??
//                 0) !=
//             0) {
//       HaloToast.showMsg(context, msg: "离线开单失败，离线开单不能使用促销");
//       return;
//     }
//     if (vipInfo != null) {
//       HaloToast.showMsg(context, msg: "离线开单失败，离线开单不能有会员信息");
//       return;
//     }
//
//     ///构建钱箱数据
//     CashBoxPaymentRequestDto? cashBoxPaymentRequestDto =
//         PaymentUtil.setCashPaymentFail(
//             context, goodsBillDto.pettyCash, paymentList, getPaymentType());
//     if (cashBoxPaymentRequestDto != null) {
//       goodsBill.cashBoxPayment = cashBoxPaymentRequestDto;
//     }
//     goodsBill.postState = BillPostStateString[BillPostState.PROCESS_COMPLETED];
//     goodsBill.payState = BillPayStateString[BillPayState.Paied];
//     goodsBill.storeManagerId = SpTool.getStoreInfo()?.ownerId ?? "";
//     String? profileId = LoginCenter.getLoginUser().profileId;
//     if (profileId != null) {
//       paymentSuccessCallBack();
//       BillDBManager.insertSaleBill(
//           profileId: profileId,
//           vchcode: goodsBill.vchcode!,
//           json: jsonEncode(goodsBill.toJson()));
//       BillDBManager.selectSaleBill(profileId: profileId)
//           .then((List<GoodsBillDto> bills) {
//         debugPrint("length  bill  ${bills.length}");
//         for (var element in bills) {
//           debugPrint("vchcode   ${element.vchcode}");
//         }
//       });
//     } else {
//       HaloToast.showMsg(context, msg: "离线开单失败，离线用户错误");
//     }
//   }
//
//   ///有网络提交
//   _onLineSubmitBill(
//       GoodsBillDto goodsBill, String storedValue, String payMoney) {
//     goodsBill.payState = BillPayStateString[BillPayState.UnPay];
//
//     ///校验单据
//     PaymentUtil.validaGoodsBill(context, goodsBill, (value) {
//       ///开始支付z
//       payOrderBill(context, goodsBill,
//           scoreUsed: scoreUsed,
//           giveScoreMoney: giveScoreMoney,
//           cardDetailIds: cardDetailIds);
//     });
//   }
//
//   ///设置账户信息
//   _setPayment(GoodsBillDto goodsBill, String storedValue) {
//     Map atype = BillTool.getAtypeMessage(goodsBill, billType);
//     goodsBill.payment = paymentList.where((element) => element.enable).map((e) {
//       PaymentDto payment = PaymentDto()
//         ..afullnameCation = atype["afullnameCation"]
//         ..atypeTotalCation = atype["atypeTotalCation"]
//         ..paywayFullname = e.storePayway.paywayName
//         ..atypeId = e.storePayway.atypeId
//         ..atypeFullName = e.storePayway.atypeFullname
//         ..paywayId = e.storePayway.paywayId
//         ..paywayType = e.storePayway.paywayType
//         ..currencyAtypeTotal = e.total;
//       if (payment.paywayType == 0) {
//         payment.currencyAtypeTotal =
//             MathUtil.subtraction(e.total, goodsBillDto.pettyCash).toString();
//       }
//
//       ///储值金额
//       if (e.storePayway.paywayType == 3) {
//         storedValue = num.parse(storedValue).toString();
//         if (num.parse(goodsBillDto.currencyBillTotal) > 0) {
//           payment.currencyAtypeTotal = storedValue;
//           payment.posCurrencyAtypeTotal = MathUtil.add(storedValue,
//                   goodsBill.currencyGivePreferentialTotal.toString())
//               .toString();
//         } else {
//           payment.currencyAtypeTotal = "-$storedValue";
//           payment.posCurrencyAtypeTotal =
//               "-${MathUtil.add(storedValue, goodsBill.currencyGivePreferentialTotal.abs().toString()).toString()}";
//         }
//
//         payment.atypeId = SpTool.getStoreInfo()!.storedMoneyAtypeId;
//         payment.atypeFullName = SpTool.getStoreInfo()!.storedMoneyAtypeName;
//         payment.accountDetailType = "ADVANCE_ACCOUNTS";
//         goodsBill.currencyAdvanceTotal =
//             num.parse(payment.currencyAtypeTotal ?? "0");
//       } else {
//         if (billType == BillType.SaleChangeBill) {
//           String total =
//               num.parse(payment.currencyAtypeTotal ?? "0").abs().toString();
//           if (num.parse(goodsBillDto.currencyBillTotal) > 0) {
//             payment.currencyAtypeTotal = total;
//           } else {
//             payment.currencyAtypeTotal = "-$total";
//           }
//
//           ///0去掉负号
//           if (num.parse(e.total) == 0) {
//             payment.currencyAtypeTotal = "0";
//           }
//         }
//       }
//       return payment;
//     }).toList();
//   }
//
//   ///Todo  返回计算好的本金
//   String _calculateStoreMoney(GoodsBillDto goodsBill, AtypeInfoBean? store) {
//     ///无会员或者会员无储值资产信息
//     if (null == vipInfo ||
//         null == vipInfo?.asserts ||
//         null == vipInfo?.asserts?.total) {
//       return "0";
//     }
//
//     ///未选择储值支付或者支付方式未被启用
//     if (null == store || null == store.storePayway.id || !store.enable) {
//       /// 若换入金额大于0，需支付的发支付方式无储值，将退入赠金分摊到出库明细上
//       if (goodsBill.sumInDetailGiveTotal > 0) {
//         BillTool.setStoreTotalGiftTotal(goodsBill, 0, billType,
//             backSumGiveTotal: goodsBill.sumInDetailGiveTotal);
//       }
//       return "0";
//     }
//
//     //收款
//     if (!isReturnOrChange) {
//       //计算含金量 =本金/储值
//       //储值=本金+赠金
//       Decimal proportion = getStoreProportion();
//       //计算应扣本金 = 储值支付金额*含金量
//       num chargeTotal = SystemConfigTool.doubleMultipleToDecimal(
//           num.parse(store.posTotal),
//           proportion.toDouble(),
//           BillDecimalType.TOTAL);
//       //避免误差
//       final num vipChargeTotal = vipInfo?.asserts?.chargeTotal ?? 0;
//       chargeTotal = min(chargeTotal, vipChargeTotal);
//       //计算应扣赠金=储值支付金额-本金
//       num giftTotal =
//           MathUtil.subtraction(store.posTotal, chargeTotal.toString())
//               .toDouble();
//       //避免误差
//       final num vipGiveTotal = vipInfo?.asserts?.giftTotal ?? 0;
//       if (giftTotal > vipGiveTotal) {
//         giftTotal = vipGiveTotal;
//         chargeTotal = MathUtil.subtraction(store.posTotal, giftTotal.toString())
//             .toDouble();
//       }
//       goodsBillDto.currencyGivePreferentialTotal = giftTotal;
//       BillTool.setStoreTotalGiftTotal(goodsBill, giftTotal.toDouble(), billType,
//           backSumGiveTotal: goodsBill.sumInDetailGiveTotal);
//       return chargeTotal.toString();
//     }
//     //退款
//     else {
//       //记录优惠辅助表
//       PreferentialDto preferentialDto = PreferentialDto();
//       preferentialDto.total = goodsBill.currencyGivePreferentialTotal;
//       preferentialDto.type =
//           PreferentialTool.preferentialType(PreferentialType.GiftTotal);
//       goodsBill.preferentialHelp[Preferential.giftStore.name] = preferentialDto;
//       //退货单无需重新分摊赠金
//       if (billType == BillType.SaleChangeBill) {
//         BillTool.setStoreTotalGiftTotal(
//             goodsBill, goodsBill.currencyGivePreferentialTotal, billType,
//             backSumGiveTotal: goodsBill.sumInDetailGiveTotal);
//       }
//       return store.total;
//     }
//   }
//
//   String getPaymentTypeTotal(String paywayId) {
//     ///先找到原单的
//     Decimal sumTotal = Decimal.zero;
//     PaymentDto? sourcePaymentDto = widget.goodsBillDto.sourcePayment
//         .firstWhereOrNull((element) => element.paywayId == paywayId);
//     if (sourcePaymentDto == null) {
//       return "0";
//     }
//     if (widget.backGoodsBillList != null) {
//       for (var element in widget.backGoodsBillList!) {
//         //换货单的收款 不计算
//         if (element!.goodsbill!.vchtype ==
//                 BillTypeData[BillType.SaleChangeBill] &&
//             num.parse(element.goodsbill!.currencyBillTotal) < 0) {
//           continue;
//         } else {
//           PaymentDto? paymentDto = element.goodsbill!.payment
//               .firstWhereOrNull((element) => element.paywayId == paywayId);
//           if (paymentDto != null) {
//             sumTotal = MathUtil.add(
//                 sumTotal.toString(), paymentDto.currencyAtypeTotal);
//
//             ///储值支付需增加已退赠金
//             if (paymentDto.paywayType == 3) {
//               sumTotal = MathUtil.add(sumTotal.toString(),
//                   element.goodsbill?.currencyGivePreferentialTotal.toString());
//             }
//           }
//         }
//       }
//     }
//     String? posCurrencyAtypeTotal = sourcePaymentDto.currencyAtypeTotal;
//     if (sourcePaymentDto.paywayType == 3) {
//       posCurrencyAtypeTotal = MathUtil.add(posCurrencyAtypeTotal,
//               goodsBillDto.originalCurrencyGivePreferentialTotal.toString())
//           .toString();
//     }
//     return MathUtil.subtraction(posCurrencyAtypeTotal, sumTotal.toString())
//         .toString();
//   }
//
//   @override
//   void dispose() {
//     ScannerPlugin.callback = null;
//     Get.reset();
//     super.dispose();
//   }
// }
