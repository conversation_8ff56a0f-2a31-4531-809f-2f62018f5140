import 'dart:convert';
import 'dart:math';

import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:halo_pos/common/tool/payment.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../cashierbox/entity/cash_box_payment_request_dto.dart';
import '../../common/login/login_center.dart';
import '../../common/string_res.dart';
import '../../common/tool/dialog_util.dart';
import '../../common/tool/performance_capture_util.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/tool/system_config_tool.dart';
import '../../db/bill_db_manager.dart';
import '../../enum/bill_decimal_type.dart';
import '../../enum/bill_pay_state.dart';
import '../../enum/bill_post_state.dart';
import '../../enum/bill_type.dart';
import '../../enum/payment_enum.dart';
import '../../login/entity/store/store_payment.dart';
import '../../offline/offline_tool.dart';
import '../../plugin/notify_voice_plugin.dart';
import '../../plugin/scanner_plugin.dart';
import '../../print/tool/print_tool.dart';
import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/scanner_dialog.dart';
import '../entity/bill_save_exception_dto.dart';
import '../entity/bill_save_reslut_dto.dart';
import '../entity/bill_save_result_type.dart';
import '../entity/bill_vip_assert_change.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/goods_detail_dto.dart';
import '../entity/pay_result_dto.dart';
import '../entity/payment_dto.dart';
import '../entity/preferential_dto.dart';
import '../model/bill_model.dart';
import '../model/ptype_model.dart';
import '../tool/bill_goods_util.dart';
import '../tool/bill_tool.dart';
import '../widget/exception_item_widget.dart';
import 'entity/atype_info_bean.dart';
import 'settlement_mixin.dart';
import 'widget/add_tips_page.dart';
import 'widget/exception_widget.dart';
import 'widget/payment_widget.dart';

///结算弹窗
class SaleBillSettlementPage extends BaseStatefulPage {
  ///单据信息
  final GoodsBillDto goodsBillDto;

  ///成功回调
  final Function successCallback;

  ///会员信息
  final VipWithLevelAssertsRightsCardDTO? vipInfo;

  const SaleBillSettlementPage({
    Key? key,
    required this.goodsBillDto,
    this.vipInfo,
    required this.successCallback,
  }) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      SaleBillSettlementState();
}

///出库单结算
class SaleBillSettlementState<T extends SaleBillSettlementPage>
    extends BaseStatefulPageState<T>
    with SettlementMixin {
  ///单据类型
  BillType get billType => BillType.SaleBill;

  ///是否正在支付
  bool isPaying = false;

  ///当前页面goodsBill
  GoodsBillDto get goodsBillDto => widget.goodsBillDto;

  ///聚合支付所需参数
  Map payInfo = {};

  @override
  set pettyCash(num pettyCash) {
    goodsBillDto.pettyCash = pettyCash.toString();
  }

  @override
  num get pettyCash => num.tryParse(goodsBillDto.pettyCash) ?? 0;

  //region method
  @override
  Future<void> onInitState() async {
    return init();
    // _initData();
    // _initPayment();
    // _updateSinglePay();
    // _calculateFinalGetMoneyAndPettyCash();
  }

  @override
  Future<void> init() async {
    vipInfo = widget.vipInfo;
    enableStore = vipInfo != null;
    isRefund =
        (billType == BillType.SaleBackBill) ||
        (billType == BillType.SaleChangeBill) &&
            num.parse(goodsBillDto.currencyBillTotal) <= 0;
    showCombinationPay = !isRefund;
    //实收金额取单据金额绝对值
    moneyReceived = num.parse(goodsBillDto.posCurrencyBillTotal).abs();
    //根据明细计算总金额 优惠 总qty ？？
    BillTool.reCalcStatistic(goodsBillDto, billType: billType);
    return super.init();
  }

  //region view
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        cancelBackToSalePage();
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: GestureDetector(
          onTap: cancelBackToSalePage,
          child: Container(
            padding: EdgeInsets.only(
              top: 70.w + MediaQuery.of(context).padding.top,
            ),
            alignment: Alignment.center,
            color: Colors.transparent,
            child: Column(children: [Expanded(child: _buildContent())]),
          ),
        ),
      ),
    );
  }

  _buildContent() {
    return Container(
      alignment: Alignment.center,
      color: Colors.transparent,
      child: Row(
        children: [
          Expanded(flex: 1, child: _buildContentLeft()),
          Expanded(
            flex: 2,
            child: GestureDetector(
              child: buildSettlementWidget(),
              onTap: () {
                //空点击事件，屏蔽穿透点击
              },
            ),
          ),
        ],
      ),
    );
  }

  ///构建左边
  Widget _buildContentLeft() {
    return Container();
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container();
  }

  @override
  String getActionBarTitle() => "收银机";

  //region action
  void backToSalePage() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
    widget.successCallback();
  }

  void cancelBackToSalePage() {
    // goodsBillDto.vchcode = null;
    // goodsBillDto.number = null;
    Navigator.pop(context);
  }

  @override
  void onPaymentChange(PaymentEditChange editChange) {
    super.onPaymentChange(editChange);
    //缓存经手人
    SpTool.saveEtypeInfo(etype?.etypeId ?? "", etype?.etypeName ?? "");
  }

  @override
  bool checkNext() {
    if (isRefund && moneyReceived != finalGetMoney) {
      HaloToast.show(context, msg: "应退金额和输入金额不符，请检查退款金额");
      return false;
    }
    if (!super.checkNext()) {
      return false;
    }
    return BillTool.checkBillTotalIsTooLarge(context, moneyReceived.toString());
  }

  @override
  void onTipsClick() {
    showDialog(
      context: context,
      builder: (c) {
        return AddTipsPage(
          defaultText: goodsBillDto.tips,
          submitCallback: (String text) => goodsBillDto.tips = text,
        );
      },
    );
  }

  @override
  Future<void> submit() async {
    //填写单据信息
    BillTool.setGoodsBillDefaultValue(goodsBillDto, etype);
    GoodsBillDto goodsBill = GoodsBillDto.fromMap(goodsBillDto.toJson());
    goodsBill.vipCardId = vipInfo?.vip?.id;

    //pos 单据实际金额，包含储值赠金
    String payMoney = goodsBill.currencyBillTotal.toString();

    //备注
    goodsBill.memo = BillTool.buildMemo(goodsBill, vipInfo);

    //找到储值支付方式
    final storePayment = paymentList.firstWhereOrNull(
      (payment) => payment.enable && payment.storePayway.paywayType == 3,
    );

    num storedValue = 0;
    //未含储值支付,将单据中的赠金移除
    if (null == storePayment) {
      goodsBill.currencyGivePreferentialTotal = 0;
      goodsBill.currencyAdvanceTotal = 0;
      //未含储值支付时，将储值支付的赠金优惠清除
      goodsBill.preferentialHelp.remove(Preferential.giftStore.name);
      //这里换货单中，出入库都会有赠金优惠，来配平，所以不能设置为0
      // for (GoodsDetailDto item in goodsBill.inDetail) {
      //   item.currencyGivePreferentialTotal = 0;
      //   item.givePreferentialTotal = 0;
      // }
    } else {
      storedValue =
          num.tryParse(calculateStoreMoney(goodsBill, storePayment)) ?? 0;
    }
    //填写单据支付方式
    _setPayment(goodsBill, storedValue);
    //填写单据日期
    BillTool.setGoodsBillDate(goodsBill);
    if (OffLineTool().isOfflineLogin) {
      offLineSubmitBill(goodsBill);
    } else {
      await onLineSubmitBill(goodsBill, payMoney);
    }
  }

  ///离线开单
  void offLineSubmitBill(GoodsBillDto goodsBill) {
    for (var element in goodsBill.outDetail) {
      if (StringUtil.isNotZeroOrEmpty(element.promotionId)) {
        HaloToast.showMsg(context, msg: "离线开单失败，离线开单不能使用促销");
        return;
      }
    }
    if ((goodsBill.preferentialHelp[Preferential.billPromotion.name]?.total ??
                0) !=
            0 ||
        (goodsBill.preferentialHelp[Preferential.goodsPromotion.name]?.total ??
                0) !=
            0) {
      HaloToast.showMsg(context, msg: "离线开单失败，离线开单不能使用促销");
      return;
    }
    if (vipInfo != null) {
      HaloToast.showMsg(context, msg: "离线开单失败，离线开单不能有会员信息");
      return;
    }
    if (setCashPaymentFail(goodsBill)) {
      return;
    }
    goodsBill.vipAsserts = null;
    goodsBill.cashierId = SpTool.getCashierInfo().id;
    goodsBill.postState = BillPostStateString[BillPostState.PROCESS_COMPLETED];
    goodsBill.payState = BillPayStateString[BillPayState.Paied];
    goodsBill.storeManagerId = SpTool.getStoreInfo()?.ownerId ?? "";
    //设置单据标记
    _setBillMark(goodsBill);
    goodsBill.memo += " 离线单据号:${goodsBillDto.vchcode}";
    String? profileId = LoginCenter.getLoginUser().profileId;
    if (profileId != null) {
      paySuccess(goodsBill, printBill: false);
      BillDBManager.insertSaleBill(
        profileId: profileId,
        vchcode: goodsBill.vchcode!,
        json: jsonEncode(goodsBill.toJson()),
        otypeId: SpTool.getStoreInfo()?.otypeId,
      );
      BillDBManager.selectSaleBill(
        profileId: profileId,
        otypeId: SpTool.getStoreInfo()?.otypeId,
      ).then((List<GoodsBillDto> bills) {
        debugPrint("length  bill  ${bills.length}");
        for (var element in bills) {
          debugPrint("vchcode   ${element.vchcode}");
        }
      });
    } else {
      HaloToast.showMsg(context, msg: "离线开单失败，离线用户错误");
    }
  }

  ///有网络提交
  Future<void> onLineSubmitBill(GoodsBillDto goodsBill, String payMoney) async {
    goodsBill.payState = BillPayStateString[BillPayState.UnPay];

    //校验单据
    await PaymentUtil.validaGoodsBill(context, goodsBill, (value) async {
      PaymentDto? scanPayment = goodsBill.payment.firstWhereOrNull(
        (element) => element.paywayType == 2,
      );
      //执行聚合支付
      if (scanPayment != null && scanPayment.currencyAtypeTotal != "0") {
        //校验，校验通过之后执行聚合支付
        if (await checkScanPay(goodsBill, scanPayment)) {
          payAndSaveGoods(goodsBill, payMoney);
        }
      }
      //执行非聚合支付
      else {
        goodsBill.confirm = true;
        saveGoodsBill(goodsBill, payMoney);
      }
    });
  }

  ///设置账户信息
  void _setPayment(GoodsBillDto goodsBill, num storedValue) {
    Map atype = BillTool.getAtypeMessage(goodsBill, billType);
    goodsBill.payment =
        getEnablePaymentSubtractPettyCash().map((e) {
          PaymentDto payment =
              PaymentDto()
                ..afullnameCation = atype["afullnameCation"]
                ..atypeTotalCation = atype["atypeTotalCation"]
                ..paywayFullname = e.storePayway.paywayName
                ..atypeId = e.storePayway.atypeId
                ..atypeFullName = e.storePayway.atypeFullname
                ..paywayId = e.storePayway.paywayId
                ..paywayType = e.storePayway.paywayType
                ..currencyAtypeTotal = e.total
                ..posCurrencyAtypeTotal = e.total;
          //这里[getEnablePaymentSubtractPettyCash]方法已经将减去找零
          // //现金,根据找零金额计算实际金额
          // if (payment.paywayType == 0) {
          //   //现在退货没有找零金额(为0)，所以可以直接用现金支付方式的金额-找零
          //   payment.currencyAtypeTotal =
          //       MathUtil.subtraction(e.total, pettyCash.toString()).toString();
          //   payment.posCurrencyAtypeTotal = payment.currencyAtypeTotal!;
          // }
          //储值，区分本金和赠金
          if (payment.paywayType == 3) {
            payment.atypeId = SpTool.getStoreInfo()!.storedMoneyAtypeId;
            payment.atypeFullName = SpTool.getStoreInfo()!.storedMoneyAtypeName;
            payment.accountDetailType = "ADVANCE_ACCOUNTS";
            //本金
            payment.currencyAtypeTotal = storedValue.toString();
            //本金+赠金
            payment.posCurrencyAtypeTotal =
                MathUtil.addDec(
                  storedValue,
                  goodsBill.currencyGivePreferentialTotal,
                ).toString();
          }
          //对于出库单和退货单，金额都为正数，对于换货单，有正有负(当退款时为负数)
          if (num.parse(goodsBillDto.currencyBillTotal) < 0) {
            payment.currencyAtypeTotal =
                (-(num.tryParse(payment.currencyAtypeTotal ?? "") ?? 0))
                    .toString();
            payment.posCurrencyAtypeTotal =
                (-num.parse(payment.posCurrencyAtypeTotal)).toString();
          }
          return payment;
        }).toList();
  }

  ///返回计算好的本金
  String calculateStoreMoney(GoodsBillDto goodsBill, AtypeInfoBean store) {
    //无会员或者会员无储值资产信息
    if (null == vipInfo ||
        null == vipInfo?.asserts ||
        null == vipInfo?.asserts?.total) {
      return "0";
    }
    //未选择储值支付或者支付方式未被启用
    if (null == store || null == store.storePayway.id || !store.enable) {
      // 若换入金额大于0，需支付的发支付方式无储值，将退入赠金分摊到出库明细上
      if (goodsBill.sumInDetailGiveTotal > 0) {
        BillTool.setStoreTotalGiftTotal(
          goodsBill,
          0,
          billType,
          backSumGiveTotal: goodsBill.sumInDetailGiveTotal,
        );
      }
      return "0";
    }
    //退入赠金
    //计算含金量 =本金/储值
    //储值=本金+赠金
    Decimal proportion = getStoreProportion();

    //计算应扣本金 = 储值支付金额*含金量
    num chargeTotal = SystemConfigTool.doubleMultipleToDecimal(
      num.parse(store.posTotal),
      proportion.toDouble(),
      BillDecimalType.TOTAL,
    );
    //避免误差
    final num vipChargeTotal = vipInfo?.asserts?.chargeTotal ?? 0;
    chargeTotal = min(chargeTotal, vipChargeTotal);
    //计算应扣赠金=储值支付金额-本金
    num giftTotal =
        MathUtil.subtraction(store.posTotal, chargeTotal.toString()).toDouble();
    //避免误差
    final num vipGiveTotal = vipInfo?.asserts?.giftTotal ?? 0;
    if (giftTotal > vipGiveTotal) {
      giftTotal = vipGiveTotal;
      chargeTotal =
          MathUtil.subtraction(store.posTotal, giftTotal.toString()).toDouble();
    }
    goodsBill.currencyGivePreferentialTotal = giftTotal.toDouble();
    goodsBill.currencyAdvanceTotal = chargeTotal;
    BillTool.setStoreTotalGiftTotal(
      goodsBill,
      giftTotal.toDouble(),
      billType,
      backSumGiveTotal: goodsBill.sumInDetailGiveTotal,
    );
    return chargeTotal.toString();
  }

  ///检查聚合支付
  Future<bool> checkScanPay(
    GoodsBillDto goodsBillDto,
    PaymentDto? scanPayment,
  ) async {
    if (scanPayment == null || scanPayment.currencyAtypeTotal == "0") {
      return true;
    } else {
      if (isPaying) {
        return false;
      }
      String scanOutNo =
          goodsBillDto.vchcode! +
          DateTime.now().millisecondsSinceEpoch.toString();
      return await buildScanInfo(scanPayment, scanOutNo);
    }
  }

  ///扫码，构建扫码支付信息
  Future<bool> buildScanInfo(PaymentDto scanPayment, String newOutNo) async {
    String? scanResult = await showScannerDialog(context);
    if (scanResult == null || scanResult.isEmpty || isPaying) {
      return false;
    }
    isPaying = true;
    //去掉\n
    var reducedString = scanResult;
    if (scanResult.contains("\n")) {
      var indexOfSlashN = scanResult.indexOf("\n");
      reducedString = scanResult.substring(0, indexOfSlashN);
    }
    Map params = {
      "paywayId": scanPayment.paywayId,
      "tradeAmount": scanPayment.currencyAtypeTotal,
      "authCode": reducedString,
      "outNo": newOutNo,
    };
    scanPayment.outNo = newOutNo;
    payInfo = params;
    return true;
  }

  ///支付成功
  void paySuccess(GoodsBillDto goodsBill, {bool printBill = true}) async {
    if (printBill && this.print) {
      try {
        await PrintTool.printBill(context, goodsBill);
      } catch (e) {
        HaloToast.showError(context, msg: "打印机打印异常");
      }
    }
    //打开钱箱
    _checkCash();
    //返回开单页面
    backToSalePage();
    //批量更改库存
    PtypeModel.changePtypeStockQtyByGoodsBill(
      outDetail: goodsBill.outDetail,
      inDetail: goodsBill.inDetail,
    );
  }

  ///处理储值资产变动
  void setStoreAssertChange(GoodsBillDto bill) {
    bill.vipAsserts ??= [];
    if (vipInfo == null) return;
    bill.vipAsserts!.removeWhere(
      (element) => element.typed == 1 || element.typed == 2,
    );
    num store = bill.currencyAdvanceTotal.abs();
    num giveStore = bill.currencyGivePreferentialTotal.abs();
    int changeType = 7;
    //退款为正，开单为负
    if (!isRefund) {
      store = -store;
      giveStore = -giveStore;
      changeType = 6;
    }
    if (store != 0) {
      bill.vipAsserts!.add(
        BillVipAssertChange(
          qty: store,
          typed: 1,
          memo: "储值本金",
          changeType: changeType,
        ),
      );
    }
    if (giveStore != 0) {
      bill.vipAsserts!.add(
        BillVipAssertChange(
          qty: giveStore,
          typed: 2,
          memo: "储值赠金",
          changeType: changeType,
        ),
      );
    }
  }

  ///提交单据（在线）
  void submitBill({
    required GoodsBillDto goodsBill,
    required String payMoney,
    required BillPayState payState,
    required PaySaveBillEnum saveBillEnum,
    bool confirm = false,
    Map? payInfo,
    required void Function(PayResult? payResult) onResult,
  }) {
    //处理储值资产变动
    setStoreAssertChange(goodsBill);
    goodsBill.cashierId = SpTool.getCashierInfo().id;
    goodsBill.sourceOperation = !isRefund ? 3 : 10; //操作来源为零售
    //非聚合支付需要设为核算完成 已支付状态
    goodsBill.postState = BillPostStateString[BillPostState.PROCESS_COMPLETED];
    goodsBill.payState = BillPayStateString[payState];
    BillTool.reCalcStatistic(goodsBill, billType: billType);
    //设置单据标记
    _setBillMark(goodsBill);
    //构建钱箱数据
    if (setCashPaymentFail(goodsBill)) return;
    goodsBill.confirm = confirm;
    BillModel.saveGoodsBill(
      context,
      goodsBill,
      payInfo: payInfo,
      paySaveBillEnum: saveBillEnum,
    ).then(onResult);
  }

  ///非聚合支付
  void saveGoodsBill(
    GoodsBillDto goodsBill,
    String payMoney, [
    bool confirm = false,
  ]) {
    submitBill(
      goodsBill: goodsBill,
      payMoney: payMoney,
      payState: BillPayState.Paied,
      saveBillEnum: PaySaveBillEnum.COMMON,
      confirm: confirm,
      onResult: (payResult) async {
        BillSaveResultDto value = payResult!.resultDTO!;
        if (value.resultType == BillSaveResultType.SUCCESS) {
          //单据保存后回填服务端返回信息
          BillTool.backFillBillAfterSubmit(payResult, goodsBill, goodsBillDto);
          if (goodsBill.vchtype == BillTypeData[BillType.SaleBill]) {
            updateInvoiceAndScoreInfo(payResult.resultDTO, goodsBill);
            SpTool.saveLastVchCode(goodsBill.vchcode!);
          }
          paySuccess(goodsBill);
        } else if (value.resultType == BillSaveResultType.ERROR) {
          DialogUtil.showAlertDialog(
            context,
            childContent: Container(
              margin: const EdgeInsets.only(top: 10),
              child: ExceptionWidget(value.exceptionInfo!, (
                context,
                index,
                BillSaveExceptionDto itemData,
              ) {
                return ExceptionItemWidget(
                  itemData.message ??
                      StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
                  BillTool.createSaveExceptionMessage(itemData.detailList!),
                  errorList: const [],
                );
              }),
            ),
            title: value.message ?? "",
          );
        } else if (value.resultType == BillSaveResultType.INIOVER) {
          HaloToast.showError(context, msg: value.exceptionInfo![0].message);
        } else if (value.resultType == BillSaveResultType.CONFIRM) {
          DialogUtil.showConfirmDialog(
            context,
            actionLabels: ["取消", "继续"],
            confirmCallback: () => saveGoodsBill(goodsBill, payMoney, true),
            childContent: Container(
              margin: const EdgeInsets.only(top: 10),
              child: ExceptionWidget(value.exceptionInfo!, (
                context,
                index,
                BillSaveExceptionDto itemData,
              ) {
                return ExceptionItemWidget(
                  itemData.message ??
                      StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
                  BillTool.createSaveExceptionMessage(itemData.detailList!),
                  errorList: const [],
                );
              }),
            ),
            title: value.message!,
          );
        } else {
          HaloToast.showError(context, msg: value.exceptionInfo![0].message);
        }
      },
    );
  }

  ///聚合支付
  void payAndSaveGoods(GoodsBillDto goodsBill, String payMoney) {
    submitBill(
      goodsBill: goodsBill,
      payInfo: payInfo,
      //聚合支付信息
      payMoney: payMoney,
      payState: BillPayState.UnPay,
      //聚合支付设置成未支付状态
      saveBillEnum:
          isRefund ? PaySaveBillEnum.SCANREFUND : PaySaveBillEnum.SCANPAY,
      confirm: true,
      onResult: (payResult) async {
        isPaying = false;
        //从支付结果中拿到单据编号
        BillTool.backFillBillAfterSubmit(payResult, goodsBill, goodsBillDto);
        if (payResult != null) {
          if (goodsBill.vchtype == BillTypeData[BillType.SaleBill]) {
            updateInvoiceAndScoreInfo(payResult.resultDTO, goodsBill);
            SpTool.saveLastVchCode(goodsBill.vchcode!);
          }
        }
        PaymentUtil.processingPayResult(
          context,
          payResult,
          result: (PayResultType resultType) {
            if (resultType == PayResultType.SUCCEEDED) {
              paySuccess(goodsBill);
            }
          },
          onActionListener: (int index) {
            doErrorAction(index, goodsBill);
          },
        );
      },
    );
  }

  ///错误弹窗选择事件
  doErrorAction(int index, GoodsBillDto goodsBill) {
    if (index == 0 || index == 2) {
      goodsBill.whetherPost = true;
      if (index == 0) {
        goodsBill.memo = "手工确认已收到钱款";
        BillModel.updateBillMemo(
          context,
          goodsBill.memo,
          goodsBill.vchcode!,
          goodsBill.etypeId!,
          goodsBill.dtypeId!,
          goodsBill.summary ?? "",
        ).then((value) async {
          ///更新备注成功
        });
      }

      BillModel.updateBillPayState(context, goodsBill).then((value) async {
        SpTool.saveLastVchCode(goodsBill.vchcode!);
        SpTool.saveEtypeInfo(
          goodsBill.etypeId ?? "",
          goodsBill.efullname ?? "",
        );
        paySuccess(goodsBill);
        if (context.mounted) HaloToast.show(context, msg: "支付成功");
        NotifyVoicePlugin.success();
      });
    } else {
      BillModel.logicalDeleteBill(context, goodsBill.vchcode!).then((value) {
        ///删除成功
      });
    }
  }

  ///发票信息
  void updateInvoiceAndScoreInfo(
    BillSaveResultDto? saveResultDto,
    GoodsBillDto goodsBill,
  ) {
    if (null == saveResultDto) {
      return;
    }
    if (null != goodsBill.vipBillInfo) {
      goodsBill.vipBillInfo!.saleScore = saveResultDto.saleScore;
      goodsBill.vipBillInfo!.scoreTotal =
          (vipInfo?.asserts?.score ?? 0) +
                  (saveResultDto.saleScore ?? 0) -
                  (goodsBillDto
                          .preferentialHelp[Preferential.scoreDiscount]
                          ?.value ??
                      0)
              as int?;
    }
  }

  ///检查是否有现金支付,有的话打开钱箱
  void _checkCash() async {
    var cashPayment = paymentList.firstWhereOrNull(
      (element) => element.enable && element.storePayway.paywayType == 0,
    );
    if (cashPayment != null) {
      //如果选择了现金支付，则打开钱箱
      PrintTool.openCashBox(context);
    }
  }

  ///如果是现金，就构建钱箱记录数据
  bool setCashPaymentFail(GoodsBillDto goodsBill) {
    AtypeInfoBean? cashPayment = paymentList.firstWhere(
      (element) => element.enable && element.storePayway.paywayType == 0,
      orElse: () => AtypeInfoBean(StorePayway(), "0"),
    );
    if (cashPayment.storePayway.id != null) {
      //如果选择了现金支付，则打开钱箱
      String cashierId = SpTool.getCashierInfo().id!;
      String otypeId = SpTool.getStoreInfo()!.otypeId!;
      String etypeId = LoginCenter.getLoginUser().employeeId!;
      double num =
          MathUtil.subtraction(
            cashPayment.total,
            goodsBillDto.pettyCash,
          ).toDouble();
      //2为销售存入，3为退货取出
      int paymentType = isRefund ? 3 : 2;
      if (StringUtil.isNotEmpty(cashierId) &&
          StringUtil.isNotEmpty(otypeId) &&
          StringUtil.isNotEmpty(etypeId)) {
        goodsBill.cashBoxPayment = CashBoxPaymentRequestDto(
          num,
          paymentType,
          cashierId,
          etypeId,
          otypeId,
        );
      } else {
        HaloToast.showError(context, msg: "钱箱参数不全");
        return true;
      }
    }
    return false;
  }

  ///设置单据标记
  void _setBillMark(GoodsBillDto goodsBill) {
    // 检查单据是否包含礼品券，如果包含则设置礼品券标记
    if (BillGoodsUtil.billContainsGiftCoupon(goodsBill)) {
      goodsBill.sourceType = BillMarkEnum.GIFT_COUPON;
    } else {
      goodsBill.sourceType = BillMarkEnum.NORMAL_BILL;
    }
  }

  @override
  void dispose() {
    ScannerPlugin.callback = null;
    Get.reset();
    super.dispose();
  }
}
