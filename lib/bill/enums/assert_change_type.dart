/// 资产变更类型枚举
enum AssertChangeType {
  /// 新增会员
  NEW_VIP(0, "新增会员"),

  /// 生日赠送
  BIRTHDAY_GIFT(1, "生日赠送"),

  /// 充值赠送
  RECHARGE_GIFT(2, "充值赠送"),

  /// 开卡赠送
  CARD_OPENING_GIFT(3, "开卡赠送"),

  /// 升级赠送
  UPGRADE_GIFT(4, "升级赠送"),

  /// 消费获得
  CONSUME_GET(5, "消费获得"),

  /// 消费使用
  CONSUME_USE(6, "消费使用"),

  /// 退款退回
  REFUND_RETURN(7, "退款退回"),

  /// 退款扣除
  REFUND_DEDUCTION(8, "退款扣除"),

  /// 会员充值
  VIP_RECHARGE(9, "会员充值"),

  /// 储值退款
  STORED_VALUE_REFUND(10, "储值退款"),

  /// 充值作废扣除
  RECHARGE_INVALIDATE(11, "充值作废扣除"),

  /// 积分兑换
  SCORE_EXCHANGE(12, "积分兑换"),

  /// 手工调整
  MANUAL_ADJUSTMENT(13, "手工调整"),

  /// 手工发放
  MANUAL_DISTRIBUTION(14, "手工发放"),

  /// 手工解绑
  MANUAL_UNBINDING(15, "手工解绑"),

  /// 手动变更等级
  MANUALLY_CHANGE_LEVEL(16, "手动变更等级"),

  /// 积分过期
  SCORE_EXPIRE(17, "积分过期"),

  /// 卡券到期
  CARD_EXPIRE(18, "卡券到期"),

  /// 累计消费笔数赠送
  ACCUMULATED_GIFT(19, "累计消费笔数赠送"),

  /// 改单回退消费
  CHANGE_ORDER_RETURN_CONSUME(20, "改单回退消费"),

  /// 改单回退获得
  CHANGE_ORDER_RETURN_GET(21, "改单回退获得"),

  /// 改单获得
  CHANGE_ORDER_USE(22, "改单获得"),

  /// 改单消费
  CHANGE_ORDER_CONSUME(23, "改单消费"),

  /// 删单回退消费
  DELETE_ORDER_RETURN_CONSUME(24, "删单回退消费"),

  /// 删单回退获得
  DELETE_ORDER_RETURN_GET(25, "删单回退获得");

  /// 构造函数
  const AssertChangeType(this.code, this.description);

  /// 枚举值
  final int code;

  /// 描述文本
  final String description;

  /// 根据code获取枚举
  static AssertChangeType? fromCode(int? code) {
    if (code == null) return null;
    return AssertChangeType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => AssertChangeType.NEW_VIP,
    );
  }

  /// 根据字符串名称获取枚举
  static AssertChangeType? fromString(String? name) {
    if (name == null) return null;

    try {
      return AssertChangeType.values.firstWhere((type) => type.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 获取描述文本
  String getDescription() => description;

  /// 获取code值
  int getCode() => code;
}
