import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart' show SizeExtension;
import 'package:haloui/widget/halo_button.dart';

import '../../../common/style/app_colors.dart';
import '../../../iconfont/icon_font.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../common/tool/sp_tool.dart';
import '../../widgets/custom_table.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/goods_detail_dto.dart';
import '../tendermanager/entity/tender_manage_config.dart';
import '../tendermanager/widget/column_config_drawer.dart';

mixin ColumnConfigMixin<T extends BaseStatefulPage>
    on BaseStatefulPageState<T> {
  Type get configType;

  @override
  buildEndDrawer() {
    return ColumnConfigDrawer(type: configType);
  }
}

///确认发货和确认收货弹窗基类，包含标题，商品列表，底部按钮
mixin TransferAndAllocateConfirmMixin<T extends StatefulWidget> on State<T> {
  ///严格序列号
  final isStrict =
      SpTool.getSystemConfig().sysIndustryEnabledStrictSerialNumber;

  final Map<int, double> columnConfig = {};
  final Map<int, String> columnTitle = {};

  ///列配置
  TransferColumnConfig get config;

  ///标题
  String get title;

  ///调拨单
  GoodsBillDto? get goodsBill;

  ///点击了列配置按钮
  void onOpenColumnConfig();

  ///点击了确定按钮
  void confirm();

  ///构建表格单元格
  Widget buildCell(GoodsDetailDto item, int columnType);

  @override
  void initState() {
    initColumnConfig();
    super.initState();
  }

  ///初始化列配置
  void initColumnConfig() {
    for (var entry in config.columnConfig.entries) {
      int columnType = int.parse(entry.key);
      columnConfig[columnType] = entry.value ? 1 : 0;
      columnTitle[columnType] = ColumnType.values[columnType].title;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        color: Colors.white,
        width: 1800.w,
        height: 800.h,
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 30.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildTop(context),
            SizedBox(height: 20.h),
            Expanded(child: buildTable(context)),
            buildBottomButton(context),
          ],
        ),
      ),
    );
  }

  Widget buildTop(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title,
            style: TextStyle(fontSize: 30.sp, fontWeight: FontWeight.bold)),
        buildColumnConfigButton(context),
      ],
    );
  }

  Widget buildColumnConfigButton(BuildContext context) {
    return GestureDetector(
      onTap: onOpenColumnConfig,
      behavior: HitTestBehavior.opaque,
      child: IconFont(IconNames.shezhi, size: 30.w),
    );
  }

  Widget buildTable(BuildContext context) {
    BorderSide borderSide = const BorderSide(color: AppColors.lineColor);
    return CustomColumnTable<GoodsDetailDto>(
      columnConfig: columnConfig,
      columnTitle: columnTitle,
      cellBuilder: buildCell,
      columnTitleBuilder: (title, columnType) => Container(
        padding: EdgeInsets.symmetric(vertical: 20.h),
        alignment: Alignment.center,
        child: Text(title, overflow: TextOverflow.ellipsis),
      ),
      data: goodsBill?.outDetail ?? [],
      border: TableBorder(
          top: borderSide, bottom: borderSide, horizontalInside: borderSide),
      scrollable: true,
    );
  }

  Widget buildBottomButton(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        HaloButton(
          backgroundColor: Colors.blue,
          height: 60.h,
          width: 120.w,
          textColor: Colors.white,
          text: "确认",
          fontSize: 22.sp,
          onPressed: confirm,
        ),
        SizedBox(width: 20.w),
        HaloButton(
          onPressed: () => Navigator.pop(context),
          backgroundColor: Colors.white,
          height: 60.h,
          width: 120.w,
          buttonType: HaloButtonType.outlinedButton,
          textColor: Colors.black54,
          text: "取消",
          borderColor: Colors.black54,
          outLineWidth: 1,
          fontSize: 22.sp,
        ),
      ],
    );
  }
}
