class TransferOrderDTO {
  /// 单据id
  final String vchcode;

  /// 单据编号
  final String billNumber;

  /// 录单日期
  final String billDate;

  /// 订单状态
  final int postState;

  final String? ktypeId;

  final String? ktypeId2;

  /// 订单完成状态（0未完成，1完成,2终止）
  final int orderoverState;

  /// 关联的调拨单Id
  final String? relationVchcode;

  /// 关联的调拨单状态
  final int? relationState;

  TransferOrderDTO.fromMap(Map<String, dynamic> json)
      : vchcode = json["vchcode"],
        billNumber = json["billNumber"],
        billDate = json["billDate"],
        postState = json["postState"],
        ktypeId = json["ktypeId"],
        ktypeId2 = json["ktypeId2"],
        orderoverState = json["orderoverState"],
        relationVchcode = json["relationVchcode"],
        relationState = json["relationState"];
}
