import 'package:flutter/foundation.dart' as foundation show Key;
import 'package:flutter/material.dart' hide Key;
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;

import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/widget/ptype/goods_bind_batch_page.dart';
import '../../../common/standard.dart';
import '../../common/keyboard_hidden.dart';
import '../../common/tool/decimal_scale_input_formatter.dart';
import '../../widgets/price_keyboard.dart';
import '../entity/ptype/ptype_serial_no_dto.dart';
import '../tool/goods_tool.dart';
import '../widget/ptype/base_goods_dialog.dart';
import '../widget/ptype/goods_bind_sn_page.dart';

///调拨单用商品编辑弹窗，可以编辑数量、批次号，序列号
class GoodsEditDialog extends StatefulWidget {
  ///允许编辑的最大数量,当小于等于0时，对商品数量不做限制
  final num maxCount;

  ///商品
  final GoodsDetailDto goodsDetail;

  ///商品列表，用于校验序列号是否重复
  final List<GoodsDetailDto> goodsList;

  const GoodsEditDialog({
    foundation.Key? key,
    required this.goodsDetail,
    required this.goodsList,
    this.maxCount = 0,
  }) : super(key: key);

  @override
  State<GoodsEditDialog> createState() => _GoodsEditDialogState();
}

class _GoodsEditDialogState extends BaseGoodsDialogState<GoodsEditDialog> {
  @override
  double get height => 652.h;

  @override
  String get title => "商品信息";

  @override
  double get width => 720.w;

  @override
  EdgeInsets get dialogPadding =>
      EdgeInsets.only(left: 38.w, right: 42.w, bottom: 30.h, top: 10.h);

  late GoodsDetailDto goodsDetail;

  late TextEditingController controller;

  @override
  void initState() {
    super.initState();
    //拷贝，直到点击确定才算提交
    goodsDetail = GoodsDetailDto.fromMap(widget.goodsDetail.toJson());
    controller = controller =
        TextEditingController(text: goodsDetail.unitQty.toString());
    controller.addListener(onQtyChange);
  }

  @override
  void dispose() {
    super.dispose();
    controller.removeListener(onQtyChange);
  }

  void onQtyChange() {
    num count = num.tryParse(controller.text) ?? 0;
    int snCount = goodsDetail.serialNoList.length;

    if (count < snCount) {
      HaloToast.show(context, msg: "商品数量不能小于序列号数量");
      String snCountStr = snCount.toString();
      controller.value = TextEditingValue(
          text: snCountStr,
          selection: TextSelection.collapsed(offset: snCountStr.length));
      goodsDetail.unitQty = snCount;
      return;
    } else {
      if (count > widget.maxCount && widget.maxCount > 0) {
        String max = widget.maxCount.toString();
        controller.value = TextEditingValue(
            text: max, selection: TextSelection.collapsed(offset: max.length));
        goodsDetail.unitQty = widget.maxCount;
        return;
      }
    }
    goodsDetail.unitQty = count;
  }

  @override
  Widget buildContent(BuildContext context) {
    return Padding(
      padding: dialogPadding,
      child: Column(
        children: [
          _buildCount(context),
          SizedBox(height: 15.h),
          PriceKeyBoard(
            controller: controller,
            keyList: const [
              [Key.one, Key.two, Key.three],
              [Key.four, Key.five, Key.six],
              [Key.seven, Key.eight, Key.nine],
              [Key.back, Key.zero, Key.clear]
            ],
            max: widget.maxCount,
              scale:SpTool.getSystemConfig().sysDigitalQty,
            numberChangeCallback: (str) => onQtyChange(),
          ),
          Expanded(child: Container()),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child: Visibility(
                  visible: goodsDetail.batchenabled,
                  child: _buildButton(context, "批次号", onTap: _showBatchDialog),
                ),
              ),
              SizedBox(width: 10.w),
              Expanded(
                child: Visibility(
                  visible: goodsDetail.snenabled != 0,
                  child: _buildButton(context, "序列号", onTap: _showSnDialog),
                ),
              ),
              SizedBox(width: 10.w),
              _buildButton(context, "确定",
                  background: const Color(0xFF4679FC),
                  textColor: Colors.white,
                  borderColor: null,
                  onTap: () => _commit()),
            ],
          ),
        ],
      ),
    );
  }

  ///构建金额栏
  HaloContainer _buildCount(BuildContext context) {
    return HaloContainer(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      height: 80.h,
      mainAxisSize: MainAxisSize.max,
      borderRadius: BorderRadius.circular(6.w),
      border: Border.all(color: const Color(0xFFCFCFCF), width: 2.w),
      children: [
        Text("数量",
            style: TextStyle(
                color: const Color(0xff333333),
                fontSize: 32.sp,
                fontWeight: FontWeight.bold)),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 6.h),
            child: KeyboardHiddenTextField(
              controller: controller,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: const Color(0xff333333),
                  fontWeight: FontWeight.bold,
                  backgroundColor: const Color(0xFFC3D4FF),
                  fontSize: 28.sp),
              onTapBefore: () => false,
              cleanTextWhenSearch: false,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                DecimalScaleInputFormatter(scale: 2),
              ],
            ),
          ),
        ),
      ],
    );
  }

  ///底部按钮
  Widget _buildButton(
    BuildContext context,
    String content, {
    Color textColor = const Color(0xFF333333),
    Color background = Colors.white,
    Color? borderColor = const Color(0xFF999999),
    VoidCallback? onTap,
  }) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: 66.h,
          width: 196.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: background,
            borderRadius: BorderRadius.circular(4.w),
            border: borderColor?.let(
                (borderColor) => Border.all(color: borderColor, width: 2.w)),
          ),
          child: Text(content,
              style: TextStyle(color: textColor, fontSize: 26.sp)),
        ));
  }

  void _showBatchDialog() {
    showDialog(
        context: context,
        builder: (context) => GoodsBindBatchPage(goods: goodsDetail));
  }

  void _showSnDialog() {
    if (goodsDetail.batchenabled) {
      if (!GoodsTool.isGoodsBindWithBatch(goodsDetail)) {
        HaloToast.show(context, msg: "请先选择批次号");
        return;
      }
    }
    showDialog(
        context: context,
        builder: (context) => GoodsBindSnPage(
              goods: goodsDetail,
              limitMaxCount: true,
              existSN: widget.goodsList
                  .expand<PtypeSerialNoDto>((element) =>
                      (element.snenabled != 0) ? element.serialNoList : [])
                  .map<String>((e) => e.snno!)
                  .toList(),
            ));
  }

  void _commit() {
    copyQtyBatchAndSn(goodsDetail, widget.goodsDetail);
    Navigator.pop(context, widget.goodsDetail);
  }
}

void copyQtyBatchAndSn(GoodsDetailDto from, GoodsDetailDto to) {
  //修改商品数量，批次号，序列号然后返回
  to.unitQty = from.unitQty;

  //将该商品关联上批次号
  to.batchId = from.batchId;
  //批次成本
  to.batchPrice = from.batchPrice;
  //解决个别计价商品，选择批次后核算不通过的问题
  to.costId = from.costId;
  to.batchNo = from.batchNo;
  to.produceDate = from.produceDate;
  to.expireDate = from.expireDate;
  to.protectDays = from.protectDays;

  //序列号
  to.serialNoList = from.serialNoList;
}
