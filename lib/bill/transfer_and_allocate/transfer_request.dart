import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import '../../../common/num_extension.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../bill/entity/check_detail_bill_request.dart';
import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/order_bill_dto.dart';
import '../../../bill/entity/select_detail_create_bill_request.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/model/ptype_model.dart';
import '../../../bill/tender/tender_bill.dart';
import '../../../bill/tendermanager/entity/tender_manage_config.dart';
import '../../../bill/transfer_and_allocate/entity/transfer_order_list_item.dart';
import '../../../bill/transfer_and_allocate/transfer_and_allocate.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_post_state.dart';
import '../../../iconfont/icon_font.dart';
import '../../../print/tool/print_tool.dart';
import '../../../settting/widget/column_config_page.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/custom_table.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../common/string_res.dart';
import '../../common/tool/dialog_util.dart';
import '../../common/widget/datetime_filter.dart';
import '../../entity/system/column_config.dart' as column_config;
import '../../enum/bill_type.dart';
import '../../widgets/filter_mixin.dart';
import '../entity/bill_load_request.dart';
import '../entity/bill_save_exception_detail_dto.dart';
import '../entity/bill_save_exception_dto.dart';
import '../entity/bill_save_reslut_dto.dart';
import '../entity/bill_save_result_type.dart';
import '../entity/create_bill_response.dart';
import '../settlement/widget/exception_widget.dart';
import '../tendermanager/widget/column_config_drawer.dart';
import '../tool/goods_tool.dart';
import '../widget/exception_item_widget.dart';
import 'goods_edit_dialog.dart';

///空白占位图
Widget _buildEmptyPlaceholder() {
  return HaloEmptyContainer(
    gravity: EmptyGravity.CENTER,
    image: Image.asset('assets/images/nodata.png'),
    title: "暂无数据",
    titleStyle: TextStyle(
      decoration: TextDecoration.none,
      fontSize: ScreenUtil().setSp(30),
      color: Colors.grey,
    ),
  );
}

///要货管理
///调拨订单管理 vchtype9005
class TransferRequestManagePage extends BaseStatefulPage {
  const TransferRequestManagePage({Key? key})
    : super(key: key, rightFlex: 3, showEndDrawer: false);

  @override
  BaseStatefulPageState<TransferRequestManagePage> createState() =>
      _TransferRequestManagePageState();
}

class _TransferRequestManagePageState
    extends BaseStatefulPageState<TransferRequestManagePage>
    with DateTimeFilterMixin, FilterMixin {
  ///要货角色
  static const List<String> _filterSourceRole = ["要货方", "发货方"];

  ///要货处理
  static const List<String> _filterSourceDeliveryState = ["全部", "待出库", "已出库"];

  ///申请状态
  static const List<String> _filterSourceState = ["全部", "未提交", "已提交"];

  ///分页大小
  static const int _pageSize = 20;

  ///当前分页
  int _pageIndex = 1;

  ///是否有更多数据
  bool _hasMore = true;

  ///要货角色筛选条件popupWindow弹窗的key
  final GlobalKey _roleKey = GlobalKey();

  ///申请状态筛选条件popupWindow弹窗的key
  final GlobalKey _stateKey = GlobalKey();

  ///发货状态筛选条件popupWindow弹窗的key
  final GlobalKey _deliveryKey = GlobalKey();

  ///权限
  final _permission = SpTool.getPermission();

  ///当前角色筛选条件
  String _currentRole = _filterSourceRole.first;

  ///当前发货状态筛选条件
  String _currentDeliveryState = _filterSourceDeliveryState.first;

  ///当前申请状态筛选条件
  String _currentState = _filterSourceState.first;

  ///列表当前选中的item的单据id
  final ValueNotifier<String?> _currentVchcode = ValueNotifier(null);

  ///接口的请求参数，在变更任意筛选条件时，并不会同步到。而是用户点击查询按钮才同步并请求数据
  final Map<String, dynamic> requestParams = {};

  final List<TransferOrderDTO> _data = [];

  @override
  final configType = Type.send;

  @override
  Future<void>? onInitState() async {
    await super.onInitState();
    onSearch();
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    Widget content;
    if (_data.isEmpty) {
      content = _buildEmptyPlaceholder();
    } else {
      content = EasyRefresh(
        header: MaterialHeader(),
        footer: MaterialFooter(),
        onRefresh: () async => onSearch(),
        onLoad: _hasMore ? _request : null,
        child: ListView.builder(
          itemBuilder: _buildItem,
          itemCount: _data.length,
        ),
      );
    }
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              Expanded(child: content),
              Visibility(
                visible: _permission.shopsaletransferOrdercreate ?? false,
                child: GestureDetector(
                  onTap: () {
                    //跳转至新增订单页面
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const TenderBillPages(),
                      ),
                    ).then((value) => onSearch());
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    color: const Color(0xFFF1A51C),
                    height: 106.h,
                    width: double.infinity,
                    alignment: Alignment.center,
                    child: Text(
                      "+新增申请",
                      maxLines: 1,
                      style: TextStyle(fontSize: 30.sp, color: Colors.white),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const VerticalDivider(width: 1, color: AppColors.lineColor),
      ],
    );
  }

  Widget _buildItem(BuildContext context, int index) {
    TransferOrderDTO itemData = _data[index];
    final storeKtypeId = SpTool.getStoreInfo()!.ktypeId;
    String state;
    Color stateColor;
    if (itemData.ktypeId2 == storeKtypeId) {
      //当前店铺为收货方，要货订单,状态为未提交和已提交
      state =
          itemData.postState == BillPostState.UNCONFIRMED.state ? "未提交" : "已提交";
      stateColor =
          itemData.postState == BillPostState.UNCONFIRMED.state
              ? Colors.red
              : Colors.green;
    } else {
      //发货角色，状态为待出库和已出库
      state =
          (itemData.relationState ?? 0) <= BillPostState.UNCONFIRMED.state
              ? "待出库"
              : "已出库";
      stateColor =
          (itemData.relationState ?? 0) <= BillPostState.UNCONFIRMED.state
              ? Colors.red
              : Colors.green;
    }
    return GestureDetector(
      onTap: () => setState(() => _currentVchcode.value = itemData.vchcode),
      behavior: HitTestBehavior.opaque,
      child: HaloContainer(
        height: 100.w,
        color:
            _currentVchcode.value == itemData.vchcode
                ? const Color(0xFFF4F7FF)
                : Colors.white,
        padding: EdgeInsets.only(
          left: 32.w,
          right: 15.w,
          top: 15.h,
          bottom: 10.h,
        ),
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            flex: 2,
            child: HaloContainer(
              direction: Axis.vertical,
              crossAxisAlignment: CrossAxisAlignment.start,
              padding: EdgeInsets.only(left: ScreenUtil().setWidth(11)),
              children: [
                HaloPosLabel(
                  itemData.billNumber,
                  textStyle: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.none,
                    fontSize: 26.sp,
                  ),
                ),
                const SizedBox(height: 5),
                HaloPosLabel(
                  formatDateStringToLocal(itemData.billDate),
                  textStyle: TextStyle(
                    color: Colors.grey,
                    decoration: TextDecoration.none,
                    fontSize: 20.sp,
                  ),
                ),
              ],
            ),
          ),
          HaloPosLabel(
            state,
            textStyle: TextStyle(
              color: stateColor,
              decoration: TextDecoration.none,
              fontSize: 24.sp,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget buildRightBody(BuildContext context) {
    return TransferRequestOrderDetail(_currentVchcode, () => onSearch());
  }

  @override
  String getActionBarTitle() => "要货管理";

  @override
  Widget buildOtherFilter(BuildContext context) {
    return Row(
      children: [
        buildPopupFilterWidget(
          context,
          key: _roleKey,
          title: "要货角色:",
          value: _currentRole,
          source: _filterSourceRole,
          onItemClick:
              (value) => setState(() {
                _currentRole = value;
                if (value == _filterSourceRole.first) {
                  //要货方，隐藏发货状态筛选条件，所以发货状态为全部
                  _currentDeliveryState = _filterSourceDeliveryState.first;
                } else {
                  //发货方，隐藏确认状态筛选条件
                  _currentState = _filterSourceState.first;
                }
              }),
        ),
        if (_currentRole == _filterSourceRole.last)
          buildPopupFilterWidget(
            context,
            key: _deliveryKey,
            title: "要货处理:",
            value: _currentDeliveryState,
            source: _filterSourceDeliveryState,
            onItemClick:
                (value) => setState(() => _currentDeliveryState = value),
          ),
        if (_currentRole == _filterSourceRole.first)
          buildPopupFilterWidget(
            context,
            key: _stateKey,
            title: "申请状态:",
            value: _currentState,
            source: _filterSourceState,
            onItemClick: (value) => setState(() => _currentState = value),
          ),
      ],
    );
  }

  @override
  void onSearch() {
    _buildRequestParams();
    _pageIndex = 1;
    _request();
  }

  void _buildRequestParams() {
    requestParams["startTime"] = formatDateStringToUtc(
      textStartTimeController.text,
    );
    requestParams["endTime"] = formatDateStringToUtc(
      textEndTimeController.text,
    );
    requestParams["billNumber"] = filterValue;
    if (_currentRole == _filterSourceRole.first) {
      //要货方
      requestParams["ktypeId"] = null;
      requestParams["ktypeId2"] = SpTool.getStoreInfo()?.ktypeId;
    } else {
      //发货方
      requestParams["ktypeId"] = SpTool.getStoreInfo()?.ktypeId;
      requestParams["ktypeId2"] = null;
    }
    if (_currentDeliveryState == _filterSourceDeliveryState.first) {
      requestParams["deliveryState"] = null;
    } else {
      //未发货 0  已发货 1
      requestParams["deliveryState"] =
          _filterSourceDeliveryState.indexOf(_currentDeliveryState) - 1;
    }
    if (_currentState == _filterSourceState.first) {
      requestParams["postStateList"] = null;
    }
    //单据已确认=500|600 ，未确认=草稿=0
    else if (_currentState == _filterSourceState.last) {
      requestParams["postStateList"] = [500, 600];
    } else {
      requestParams["postStateList"] = [0];
    }
  }

  Future<void> _request() async {
    List<TransferOrderDTO> orderList = await BillModel.getTransferOrderList(
      context,
      pageIndex: _pageIndex,
      pageSize: _pageSize,
      startTime: requestParams["startTime"],
      endTime: requestParams["endTime"],
      billNumber: requestParams["billNumber"],
      ktypeId: requestParams["ktypeId"],
      ktypeId2: requestParams["ktypeId2"],
      postStateList: requestParams["postStateList"],
      deliveryState: requestParams["deliveryState"],
    );
    if (mounted) {
      setState(() {
        if (_pageIndex == 1) {
          _data.clear();
          if (orderList.isNotEmpty) {
            _currentVchcode.value = orderList.first.vchcode;
          } else {
            _currentVchcode.value = null;
          }
        }
        _hasMore = orderList.length >= _pageSize;
        _data.addAll(orderList);
        _pageIndex++;
      });
    }
  }
}

///调拨订单详情
class TransferRequestOrderDetail extends StatefulWidget {
  final ValueNotifier<String?> vchcode;
  final VoidCallback refreshCallback;

  const TransferRequestOrderDetail(
    this.vchcode,
    this.refreshCallback, {
    Key? key,
  }) : super(key: key);

  @override
  State<TransferRequestOrderDetail> createState() =>
      _TransferRequestOrderDetailState();
}

class _TransferRequestOrderDetailState
    extends State<TransferRequestOrderDetail> {
  ///权限
  final _permission = SpTool.getPermission();

  OrderBillDTO? _data;

  // 添加列配置列表
  List<column_config.TenderColumnConfig> columnConfigList = [];

  @override
  void initState() {
    super.initState();
    widget.vchcode.addListener(_request);
    // 获取列配置
    columnConfigList = SpTool.getTenderManageColumnConfig();
  }

  @override
  void dispose() {
    super.dispose();
    widget.vchcode.removeListener(_request);
  }

  @override
  void didUpdateWidget(covariant TransferRequestOrderDetail oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.vchcode != widget.vchcode) {
      oldWidget.vchcode.removeListener(_request);
      widget.vchcode.addListener(_request);
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child;
    if (_data == null || widget.vchcode.value == null) {
      child = Center(child: _buildEmptyPlaceholder());
    } else {
      child = _buildContent(context);
    }
    return Container(color: Colors.white, child: child);
  }

  Widget _buildContent(BuildContext context) {
    return HaloContainer(
      padding: const EdgeInsets.all(0),
      color: Colors.white,
      direction: Axis.vertical,
      children: [
        HaloContainer(
          color: Colors.white,
          padding: EdgeInsets.only(
            right: 90.w,
            bottom: 15.w,
            left: 20.w,
            top: 10.w,
          ),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            HaloPosLabel(
              "商品信息",
              textStyle: const TextStyle(fontWeight: FontWeight.w600),
            ),
            HaloPosLabel(getCountWithBill()),
          ],
        ),
        Expanded(child: _buildTable(context)),
        const Divider(height: 1, color: AppColors.lineColor),
        _buildOrderInfoWidget(context),
        const Divider(height: 1, color: AppColors.lineColor),
        _buildButtons(context),
      ],
    );
  }

  ///单据信息
  Widget _buildOrderInfoWidget(BuildContext context) {
    final style = TextStyle(fontSize: 24.sp, overflow: TextOverflow.ellipsis);
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                child: Text("调出仓库：${_data?.kfullname ?? ""}", style: style),
              ),
              Expanded(
                child: Text("调入仓库：${_data?.kfullname2 ?? ""}", style: style),
              ),
              Expanded(
                child: Text("经手人：${_data?.efullname ?? ""}", style: style),
              ),
            ],
          ),
          SizedBox(height: 10.h),
          Row(
            children: [
              Expanded(
                child: Text(
                  "单据日期：${formatDateStringToLocal(_data?.date)}",
                  style: style,
                ),
              ),
              Expanded(child: Text("备注：${_data?.memo ?? ""}", style: style)),
              Expanded(child: Container()),
            ],
          ),
        ],
      ),
    );
  }

  ///构建商品列表
  Widget _buildTable(BuildContext context) {
    final rowHeight = 70.w;

    // 获取启用的列类型
    final enabledColumnTypes =
        columnConfigList
            .where(
              (config) =>
                  (config.isShow || config.isRequired) &&
                  config.type != column_config.TenderColumnType.all,
            )
            .map((config) => config.type)
            .where((type) => type != null) // 过滤掉 null 值
            .cast<column_config.TenderColumnType>() // 转换类型
            .toSet();

    // 确保设置列总是显示
    enabledColumnTypes.add(column_config.TenderColumnType.setting);

    // 创建列宽度和标题配置
    Map<int, double> columnWidths = {};
    Map<int, String> columnTitles = {};

    // 配置列宽和标题
    int columnIndex = 0;
    for (var type in column_config.TenderColumnType.values) {
      // 跳过'all'类型和不显示的列
      if (type == column_config.TenderColumnType.all ||
          !enabledColumnTypes.contains(type)) {
        continue;
      }

      // 根据列类型设置宽度
      double width = 1.0;
      if (type == column_config.TenderColumnType.pName) {
        width = 2.0;
      } else if (type == column_config.TenderColumnType.userCode) {
        width = 2.0;
      } else if (type == column_config.TenderColumnType.image) {
        width = 1.0;
      } else if (type == column_config.TenderColumnType.setting) {
        width = 0.5;
      }

      columnWidths[columnIndex] = width;

      // 设置列标题
      String title =
          columnConfigList
              .firstWhere(
                (config) => config.type == type,
                orElse:
                    () => column_config.TenderColumnConfig(title: "Unknown"),
              )
              .title;
      columnTitles[columnIndex] = title;

      columnIndex++;
    }

    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: CustomColumnTable<GoodsDetailDto>(
          scrollable: true,
          columnConfig: columnWidths,
          columnTitle: columnTitles,
          data: _data?.detail ?? [],
          border: const TableBorder(
            horizontalInside: BorderSide(color: AppColors.lineColor),
          ),
          columnTitleBuilder: (title, columnIndex) {
            // 获取当前列的类型
            var currentType = _getColumnTypeByIndex(
              columnIndex,
              enabledColumnTypes,
            );

            // 如果是设置列，显示设置图标
            if (currentType == column_config.TenderColumnType.setting) {
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => _showColumnConfig(context),
                child: Container(
                  height: rowHeight,
                  color: AppColorHelper(context).getScaffoldBackgroundColor(),
                  alignment: Alignment.center,
                  child: IconFont(IconNames.shezhi, size: 24.w),
                ),
              );
            }

            // 其他列显示标题
            return Container(
              height: rowHeight,
              color: AppColorHelper(context).getScaffoldBackgroundColor(),
              alignment: Alignment.center,
              child: HaloPosLabel(
                title,
                maxLines: 1,
                textStyle: TextStyle(
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  decoration: TextDecoration.none,
                  fontSize: 20.sp,
                ),
              ),
            );
          },
          cellBuilder: (item, columnIndex) {
            // 获取当前列的类型
            var currentType = _getColumnTypeByIndex(
              columnIndex,
              enabledColumnTypes,
            );

            // 特殊列处理
            if (currentType == column_config.TenderColumnType.setting) {
              return Container(height: rowHeight, color: Colors.white);
            }

            // 处理图片列
            if (currentType == column_config.TenderColumnType.image) {
              return Container(
                height: 70.w,
                alignment: Alignment.center,
                padding: EdgeInsets.zero,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(5.w),
                  child: _buildClickableImage(
                    context,
                    item.picUrl ?? "",
                    size: 60,
                  ),
                ),
              );
            }

            // 处理常规文本列
            String text = _getColumnText(item, currentType);

            return Container(
              color: Colors.white,
              height: rowHeight,
              alignment: Alignment.center,
              child: HaloPosLabel(
                text,
                textStyle: TextStyle(
                  color: const Color(0xFF494848),
                  fontSize: 24.sp,
                  height: 1,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // 根据列索引获取列类型
  column_config.TenderColumnType _getColumnTypeByIndex(
    int index,
    Set<column_config.TenderColumnType> enabledTypes,
  ) {
    int currentIndex = 0;
    for (var type in column_config.TenderColumnType.values) {
      if (type == column_config.TenderColumnType.all ||
          !enabledTypes.contains(type)) {
        continue;
      }
      if (currentIndex == index) {
        return type;
      }
      currentIndex++;
    }
    return column_config.TenderColumnType.all; // 默认返回
  }

  // 获取列单元格文本
  String _getColumnText(
    GoodsDetailDto item,
    column_config.TenderColumnType type,
  ) {
    switch (type) {
      case column_config.TenderColumnType.pName:
        return item.pFullName ?? "";
      case column_config.TenderColumnType.userCode:
        return item.pUserCode ?? "";
      case column_config.TenderColumnType.barCode:
        return item.fullbarcode ?? "";
      case column_config.TenderColumnType.attributeFormat:
        return item.propFormat ?? "";
      case column_config.TenderColumnType.attributeCombo:
        return item.skuName ?? "";
      case column_config.TenderColumnType.serialNumber:
        return item.serialNoList.map((e) => e.snno).join(",");
      case column_config.TenderColumnType.produceDate:
        return item.produceDate != null
            ? formatDateStringToLocal(
              item.produceDate,
              format: DateFormats.y_mo_d,
            )
            : "";
      case column_config.TenderColumnType.qualityDays:
        return item.protectDays?.toString() ?? "";
      case column_config.TenderColumnType.expireDate:
        return item.expireDate != null
            ? formatDateStringToLocal(
              item.expireDate,
              format: DateFormats.y_mo_d,
            )
            : "";
      case column_config.TenderColumnType.batchNumber:
        return item.batchNo ?? "";
      case column_config.TenderColumnType.spec:
        return item.standard ?? "";
      case column_config.TenderColumnType.model:
        return item.ptypetype ?? "";
      case column_config.TenderColumnType.number:
        return item.unitQty.toString();
      case column_config.TenderColumnType.unit:
        return item.unitName;
      default:
        return "";
    }
  }

  ///构建图片
  Widget _buildItemImage(String picUrl, {int size = 100}) {
    final defaultImage = Container(
      width: size.w,
      height: size.w,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: Icon(
        Icons.image_not_supported,
        color: Colors.grey[400],
        size: 24.w,
      ),
    );

    return SizedBox(
      width: size.w,
      height: size.w,
      child: Image.network(
        picUrl,
        fit: BoxFit.cover,
        width: size.w,
        height: size.w,
        loadingBuilder: (
          BuildContext context,
          Widget child,
          ImageChunkEvent? loadingProgress,
        ) {
          if (loadingProgress == null) {
            return child;
          }
          return defaultImage;
        },
        errorBuilder:
            (BuildContext context, Object error, StackTrace? stackTrace) =>
                defaultImage,
      ),
    );
  }

  ///构建可点击查看大图的图片控件
  Widget _buildClickableImage(
    BuildContext context,
    String picUrl, {
    int size = 100,
  }) {
    Widget image = _buildItemImage(picUrl, size: size);
    if (StringUtil.isNotEmpty(picUrl)) {
      image = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap:
            () => DialogUtil.showAlertDialog(
              context,
              actionCount: 0,
              childContent: _buildItemImage(picUrl, size: 500),
            ),
        child: image,
      );
    }
    return image;
  }

  ///底部操作按钮
  ///当是收货角色，并且订单状态为草稿时，展示删除按钮、编辑按钮和提交按钮
  ///当时发货角色，且未发货时，展示发货按钮
  Widget _buildButtons(BuildContext context) {
    if (_data == null) {
      return Container();
    }
    //是否是要货角色
    bool isReceiver = _data!.ktypeId2 == SpTool.getStoreInfo()?.ktypeId;
    //删除、编辑、提交（确认订单）和发货按钮
    Widget? delete, edit, confirm, send;
    if (isReceiver && _data!.postState == BillPostState.UNCONFIRMED.name) {
      if (_permission.shopsaletransferOrderdelete == true) {
        delete = _buildButton(content: "删除", onTap: _deleteOrder);
      }
      // 添加编辑按钮，根据权限状态设置可用性
      bool hasEditPermission = _permission.shopsaletransferOrderEdit == true;
      edit = _buildButton(
        content: "编辑",
        textColor:
            hasEditPermission
                ? const Color(0xFF4679FC)
                : AppColors.describeFontColor,
        backgroundColor: Colors.white,
        borderColor:
            hasEditPermission
                ? const Color(0xFF4679FC)
                : AppColors.unEnableBorderColor,
        onTap: () => _handleEditOrder(hasEditPermission),
      );
      if (_permission.shopsaletransferOrderconfirm == true) {
        confirm = _buildButton(
          content: "提交",
          textColor: Colors.white,
          backgroundColor: const Color(0xFF4679FC),
          buttonType: HaloButtonType.elevatedButton,
          borderColor: null,
          outLineWidth: null,
          onTap: _submit,
        );
      }
    }
    if (!isReceiver &&
        _permission.shopsaletransferOrdersend == true &&
        //调拨订单已提交确认
        _data!.postState != BillPostState.UNCONFIRMED.name &&
        //调拨订单未完成
        _data!.intOrderOverState == 0) {
      //调拨订单未生成调拨单或者生成的调拨单状态为草稿
      if (_data!.goodBillList?.isNotEmpty != true ||
          _data!.goodBillList?[0].postState ==
              BillPostState.UNCONFIRMED.state) {
        send = _buildButton(
          content: "发货",
          textColor: Colors.white,
          backgroundColor: const Color(0xFF4679FC),
          buttonType: HaloButtonType.elevatedButton,
          borderColor: null,
          outLineWidth: null,
          onTap: _send,
        );
      }
    }
    return Container(
      height: 106.h,
      padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                if (delete != null) delete,
                // 编辑按钮始终显示，根据权限状态设置可用性
                if (delete != null) SizedBox(width: 20.w),
                if (edit != null) edit,
              ],
            ),
          ),
          Row(
            children: [
              if (confirm != null) confirm,
              if (send != null) send,
              SizedBox(width: 20.w),
              if (_permission.shopsaletransferOrderprint == true)
                _buildButton(content: "打印", onTap: _print),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildButton({
    required String content,
    bool visible = true,
    Color textColor = AppColors.titleBoldTextColor,
    Color backgroundColor = Colors.white,
    HaloButtonType? buttonType = HaloButtonType.outlinedButton,
    Color? borderColor = const Color(0xFF272C35),
    double? outLineWidth = 1,
    VoidCallback? onTap,
  }) {
    return HaloButton(
      visible: visible,
      width: 200.w,
      height: 66.h,
      borderRadius: 6.w,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      buttonType: buttonType ?? HaloButtonType.elevatedButton,
      outLineWidth: outLineWidth,
      textColor: textColor,
      fontSize: 30.sp,
      text: content,
      onPressed: onTap,
    );
  }

  Future<void> _request() async {
    if (!mounted) {
      return;
    }
    if (widget.vchcode.value == null) {
      setState(() => _data = null);
      return;
    }
    OrderBillDTO? data = await BillModel.getOrderBill(
      context,
      BillLoadRequest()
        ..vchcode = widget.vchcode.value
        ..vchtype = BillType.TransferOrder.name
        ..show = true,
    );
    if (data != null) {
      setState(() => _data = data);
    }
  }

  ///删除订单
  void _deleteOrder() {
    if (_data?.vchcode == null && !mounted) return;
    BillModel.deleteOrderBill(context, _data!.vchcode!).then((result) {
      if (!mounted) {
        return;
      }
      if (result == null) {
        HaloToast.show(context, msg: "删除失败");
      } else {
        if (result.success != true) {
          String message =
              result.errorDetail?.map((e) => e.message ?? "").join() ?? "";
          HaloToast.show(context, msg: "删除失败。$message");
        } else {
          setState(() {
            widget.vchcode.value = null;
            _data = null;
          });
          widget.refreshCallback();
        }
      }
      //刷新数据
      _request();
    });
  }

  ///确认订单 PROCESS_COMPLETED
  void _submit() {
    if (!mounted || _data?.vchcode == null) {
      return;
    }
    _data!.postState = BillPostState.PROCESS_COMPLETED.name;
    BillModel.submitOrderBill(context, _data!).then((result) {
      if (!mounted) {
        return;
      }
      if (result?.resultType != BillSaveResultType.SUCCESS) {
        HaloToast.show(context, msg: "确认失败。${result?.message ?? ""}");
      } else {
        widget.refreshCallback();
      }
      //刷新数据
      _request();
    });
  }

  ///发货
  ///分为两种情况：
  /// 1.调拨订单已确认，但是未生成调拨单。那么，需要先生成调拨单，再获取这张调拨单。
  /// 2.已生成调拨单，则直接获取这张调拨单。
  ///获取调拨单之后，发货操作，其实和直接调拨差不多，只不过商品已经被选好。
  ///发货之后，调拨单状态将变为500(已发货，待入库)。
  void _send() async {
    if (!mounted || _data?.vchcode == null) {
      return;
    }
    if (!(_permission.analysiscloudouterInventorytransOnwayview ?? false)) {
      HaloToast.showError(context, msg: "开启【调拨在途】功能后可正常使用");
      return;
    }

    CheckDetailBillRequest request =
        CheckDetailBillRequest()
          ..createVchtype = "GoodsTrans"
          ..customType = 1
          ..detailIdList = _data!.detail?.map((e) => e.detailId ?? "").toList()
          ..newDetailList =
              _data!.detail
                  ?.map(
                    (e) => SelectOrderBillDTO(
                      detailId: e.detailId,
                      selectQty: e.unitQty,
                      selectSubQty: e.subQty,
                    ),
                  )
                  .toList();
    ResponseModel checkResponse = await BillModel.checkBillByDetail(
      context,
      request,
    );
    if (!mounted) return;

    ///关联的调拨单单号
    String? vchcode;
    //未生成调拨单，先调用接口生成调拨单，正好和检查接口参数一样
    if (checkResponse.code == 200) {
      CreteBillResponse? createBillResponse =
          await BillModel.selectDetailCreateBill(
            context,
            SelectDetailCreateBillRequest(
              createVchtype: request.createVchtype,
              customType: request.customType,
              detailIdList: request.detailIdList,
              newDetailList: request.newDetailList,
            ),
          );
      if (!mounted) return;
      if (createBillResponse?.success != true) {
        HaloToast.show(
          context,
          msg: "生成调拨单失败。${createBillResponse?.message ?? ""}",
        );
        return;
      }
      if (createBillResponse?.vchcodeList?.isNotEmpty == true) {
        vchcode = createBillResponse!.vchcodeList![0];
      }
    }

    if (vchcode == null) {
      //刷新调拨订单，获取关联的调拨单号
      await _request();
      if (_data?.goodBillList?.isNotEmpty == true) {
        vchcode = _data!.goodBillList![0].vchcode;
      }
    }
    if (!mounted) return;
    //跳转到调拨单发货弹窗
    if (vchcode != null) {
      showDialog(
        context: context,
        builder:
            (context1) => _TransferDialog(
              vchcode: vchcode!,
              openColumnConfigCallback: () {
                //打开endDrawer
                Scaffold.of(context).openEndDrawer();
              },
            ),
      ).then((success) {
        if (success == true) {
          widget.refreshCallback();
          //刷新
          _request();
        }
      });
    }
  }

  ///处理编辑订单点击事件
  void _handleEditOrder(bool hasPermission) {
    if (!hasPermission) {
      HaloToast.show(context, msg: "没有要货申请编辑权限");
      return;
    }
    _editOrder();
  }

  ///编辑订单
  void _editOrder() {
    if (_data == null) {
      return;
    }
    // 跳转到要货申请页面，传递当前订单数据进行编辑
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TenderBillPages(editOrderData: _data),
      ),
    ).then((value) {
      // 编辑完成后刷新页面
      widget.refreshCallback();
      _request();
    });
  }

  ///打印
  void _print() {
    if (_data != null) {
      PrintTool.printBill(context, _data!, printCount: 1);
    }
  }

  String getCountWithBill() {
    if (_data?.detail == null) {
      return "总数量:0";
    }
    List<GoodsDetailDto> detail = _data!.detail!;
    num count = 0;
    for (var element in detail) {
      if (!GoodsTool.isComboDetail(element)) {
        count = MathUtil.addDec(count, element.unitQty).toDouble();
      }
    }
    return "总数量:${count.getIntWhenInteger}";
  }

  // 显示列配置对话框
  void _showColumnConfig(BuildContext context) {
    // 使用对话框显示列配置
    DialogUtil.showAlertDialog(
      context,
      child: ColumnConfigPages(
        columnPagesType: ColumnPagesType.ColumnPagesTenderManage,
        changed: (config) {
          setState(() {
            columnConfigList = SpTool.getTenderManageColumnConfig();
          });
        },
      ),
    );
  }
}

class _TransferDialog extends StatefulWidget {
  final String vchcode;
  final VoidCallback openColumnConfigCallback;

  const _TransferDialog({
    Key? key,
    required this.vchcode,
    required this.openColumnConfigCallback,
  }) : super(key: key);

  @override
  State<_TransferDialog> createState() => _TransferDialogState();
}

class _TransferDialogState extends State<_TransferDialog>
    with TransferAndAllocateConfirmMixin {
  @override
  final TransferColumnConfig config = SpTool.getTransferConfig();

  GoodsBillDto? _goodsBillDto;

  @override
  GoodsBillDto? get goodsBill => _goodsBillDto;

  @override
  String get title => "确认发货";

  ///记录下每个商品的最大个数，因为编辑商品不允许超过最大个数
  late Map<GoodsDetailDto, num> maxCountMap;

  @override
  void initState() {
    _getGoodsBill();
    super.initState();
  }

  @override
  Widget buildCell(GoodsDetailDto item, int columnType) {
    TextStyle style = const TextStyle(overflow: TextOverflow.ellipsis);
    Widget? child;
    if (columnType == ColumnType.userCode.index) {
      child = Text(item.pUserCode ?? "", style: style);
    } else if (columnType == ColumnType.barcode.index) {
      child = Text(item.fullbarcode, style: style);
    } else if (columnType == ColumnType.ptypeName.index) {
      child = Text(
        "${item.pFullName ?? ""}${item.skuName ?? ""}",
        style: style,
      );
    } else if (columnType == ColumnType.qty.index) {
      child = Text(item.unitQty.toString(), style: style);
    } else if (columnType == ColumnType.unit.index) {
      child = Text(item.unitName, style: style);
    } else if (columnType == ColumnType.batch.index) {
      child = Text(item.batchNo, style: style);
    } else if (columnType == ColumnType.produceDate.index) {
      child = Text(
        formatDateStringToLocal(item.produceDate, format: DateFormats.y_mo_d),
        style: style,
      );
    } else if (columnType == ColumnType.protectDays.index) {
      child = Text(item.protectDays?.toString() ?? "", style: style);
    } else if (columnType == ColumnType.sn.index) {
      child = Text(
        item.serialNoList.map((e) => e.snno).join(","),
        style: style,
      );
    }
    return GestureDetector(
      onTap: () => _onItemClick(context, item),
      behavior: HitTestBehavior.opaque,
      child: Container(height: 80.h, alignment: Alignment.center, child: child),
    );
  }

  ///点击了一行
  void _onItemClick(BuildContext context, GoodsDetailDto item) {
    //商品弹窗，可以编辑数量，序列号，批次号
    showDialog(
      context: context,
      builder:
          (context) => GoodsEditDialog(
            goodsDetail: item,
            goodsList: goodsBill!.outDetail,
            maxCount: maxCountMap[item] ?? 0,
          ),
    ).then((value) {
      if (value == null) return;
      setState(() {});
    });
  }

  @override
  void confirm() async {
    if (_goodsBillDto == null) {
      return;
    }
    //单据状态为已确认发货（500），注意table里面的是outDetail，将选中的批次号，序列号同步到inDetail（pc就是这样操作的）
    GoodsBillDto billCopy = GoodsBillDto.fromMap(_goodsBillDto!.toJson());
    for (int i = billCopy.outDetail.length - 1; i >= 0; i--) {
      GoodsDetailDto outGoods = billCopy.outDetail[i];
      GoodsDetailDto inGoods = billCopy.inDetail[i];
      //这里本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill
      outGoods.vchtype = BillType.GoodsTrans.name;
      inGoods.vchtype = BillType.GoodsTrans.name;
      if (outGoods.unitQty <= 0) {
        billCopy.outDetail.removeAt(i);
        billCopy.inDetail.removeAt(i);
        continue;
      }
      //将outDetail的商品数量，批次，序列号同步到inDetail
      copyQtyBatchAndSn(outGoods, inGoods);
    }
    if (billCopy.outDetail.isEmpty) {
      HaloToast.show(context, msg: "商品不能为空");
      return;
    }
    billCopy.postState = BillPostState.PROCESS_COMPLETED.name;
    //这里本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill
    billCopy.vchtype = BillType.GoodsTrans.name;
    billCopy.onlyPost = false;
    _saveGoodsBill(billCopy);
  }

  Future<void> _saveGoodsBill(GoodsBillDto goodsBillDto) async {
    BillSaveResultDto resultDto = await BillModel.jxcSaveGoodsBill(
      context,
      goodsBillDto,
    );
    if (!mounted) return;
    if (resultDto.resultType == BillSaveResultType.SUCCESS) {
      Navigator.pop(context, true);
      //减少本地库存
      PtypeModel.changePtypeStockQtyByGoodsBill(
        outDetail: goodsBillDto.outDetail,
      );
    } else if (resultDto.resultType == BillSaveResultType.ERROR) {
      DialogUtil.showAlertDialog(
        context,
        childContent: Container(
          margin: const EdgeInsets.only(top: 10),
          child: ExceptionWidget(resultDto.exceptionInfo!, (
            context,
            index,
            BillSaveExceptionDto itemData,
          ) {
            return ExceptionItemWidget(
              itemData.message ??
                  StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
              _createSaveExceptionMessage(itemData.detailList!),
              errorList: const [],
            );
          }),
        ),
        title: resultDto.message ?? "",
      );
    } else if (resultDto.resultType == BillSaveResultType.INIOVER) {
      HaloToast.showError(context, msg: resultDto.exceptionInfo![0].message);
    } else if (resultDto.resultType == BillSaveResultType.CONFIRM) {
      DialogUtil.showConfirmDialog(
        context,
        actionLabels: ["取消", "继续"],
        confirmCallback: () {
          goodsBillDto.confirm = true;
          _saveGoodsBill(goodsBillDto);
        },
        childContent: Container(
          margin: const EdgeInsets.only(top: 10),
          child: ExceptionWidget(resultDto.exceptionInfo!, (
            context,
            index,
            BillSaveExceptionDto itemData,
          ) {
            return ExceptionItemWidget(
              itemData.message ??
                  StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
              _createSaveExceptionMessage(itemData.detailList!),
              errorList: const [],
            );
          }),
        ),
        title: resultDto.message!,
      );
    } else {
      HaloToast.showError(context, msg: resultDto.exceptionInfo![0].message);
    }
  }

  ///打开列配置drawer
  @override
  void onOpenColumnConfig() {
    Navigator.pop(context);
    widget.openColumnConfigCallback();
  }

  ///获取调拨单
  Future<void> _getGoodsBill() async {
    GoodsBillDto? goodsBillDto = await BillModel.getGoodsBill(
      context,
      widget.vchcode,
      BillTypeData[BillType.GoodsTrans],
      BillBusinessTypeString[BillBusinessType.GoodsTrans]!,
    );
    if (!mounted) {
      return;
    }
    if (StringUtil.isNotEmpty(goodsBillDto?.vchcode)) {
      setState(() {
        _goodsBillDto = goodsBillDto;
        maxCountMap = {};
        for (var element in _goodsBillDto!.outDetail) {
          maxCountMap[element] = element.unitQty;
        }
      });
    } else {
      HaloToast.show(context, msg: "获取调拨单失败，请重试");
      Navigator.pop(context);
    }
  }
}

String _createSaveExceptionMessage(List<BillSaveExceptionDetailDTO> details) {
  String names = "";
  for (var item in details) {
    if (!StringUtil.isEmpty(names)) {
      names += "\n";
    }
    names +=
        "${item.pfullname ?? ""} ${item.propValues ?? ""} ${item.message ?? ""}";
  }
  return names;
}
