import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import '../../../bill/model/ptype_model.dart';
import '../../../bill/entity/bill_save_result_type.dart';
import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/order_bill_dto.dart';
import '../../../bill/entity/otype_with_ktype.dart';
import '../../../bill/model/base_info_model.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/tool/bill_tool.dart';
import '../../../bill/tool/decimal_display_helper.dart';
import '../../../bill/tool/scan_tool.dart';
import '../../../bill/widget/ptype/goods_and_combo_select_list_page.dart';
import '../../../common/keyboard_hidden.dart';
import '../../../common/login/login_center.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../common/tool/date_util.dart';
import '../../../entity/system/permission_dto.dart';
import '../../../entity/system/column_config.dart';
import '../../../enum/bill_post_state.dart';
import '../../../enum/bill_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../login/model/login_user_model.dart';
import '../../../settting/widget/column_config_page.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/custom_table.dart';
import '../../../widgets/halo_pos_label.dart';
import '../../../widgets/selector/etype_selector.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;
import 'package:haloui/utils/math_util.dart';

import '../../common/string_res.dart';
import '../../common/tool/performance_capture_util.dart';
import '../../common/widget/ptype_note_richtext.dart';
import '../../login/entity/store/store_etype.dart';
import '../../widgets/selector/scan_select.dart';
import '../entity/bill_load_request.dart';
import '../entity/bill_save_exception_detail_dto.dart';
import '../entity/bill_save_exception_dto.dart';
import '../settlement/widget/add_tips_page.dart';
import '../settlement/widget/exception_widget.dart';
import '../widget/exception_item_widget.dart';

enum TenderBillType {
  TenderBillApply, //要货申请单
  StockBillApply, //库存调拨
}

///要货申请单
class TenderBillPages extends BaseStatefulPage {
  final TenderBillType billType;
  final OrderBillDTO? editOrderData; // 编辑模式时传入的订单数据

  const TenderBillPages({
    Key? key,
    this.billType = TenderBillType.TenderBillApply,
    this.editOrderData,
  }) : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _TenderBillPagesState();
}

class _TenderBillPagesState extends BaseStatefulPageState<TenderBillPages> {
  String searchString = "";
  final KeyboardHiddenFocusNode _searchFocusNode = KeyboardHiddenFocusNode();

  // 使用TenderColumnConfig代替固定的titleList
  late List<TenderColumnConfig> columnConfigList;

  ///商品详情数组
  final List<GoodsDetailDto> goodsDetailList = [];

  ///仓库数组
  List<OtypeWithKtypeDTO> ktypeList = [];

  ///总数量
  Decimal sumAllQty = Decimal.zero;
  GoodsBillDto? billDto; //订单模版
  ///仓库选择框key
  final GlobalKey _storeGlobalKey = GlobalKey();

  ///当前选中的门店（仓库）
  OtypeWithKtypeDTO? _currentStore;

  ///门店setState
  StateSetter? _storeStateSetter;

  ///当前经手人
  StoreEtype? _currentEtype;

  ///经手人setState
  StateSetter? _etypeStateSetter;

  ///权限
  late PermissionDto permissionDto;

  ///拉取调拨订单
  OrderBillDTO? orderBillDTO;

  ///是否向总部要货
  bool requestToHead = true;

  ///备注
  String memo = "";

  /*appbar*/
  @override
  buildAppBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(80.h),
      child: AppBar(
        backgroundColor: AppColorHelper(context).getAppBarColor(),
        titleSpacing: 0.0,
        toolbarHeight: 80.h,
        automaticallyImplyLeading: false,
        title: SizedBox(
          height: 80.h,
          child: Stack(
            children: [
              HaloContainer(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  HaloContainer(
                    children: [
                      _buildBack(),
                      HaloPosLabel(
                        widget.billType == TenderBillType.StockBillApply
                            ? "库存调拨"
                            : widget.editOrderData != null
                            ? "编辑要货申请"
                            : "要货申请",
                        textStyle: TextStyle(
                          color:
                              AppColorHelper(context).getTitleBoldTextColor(),
                          fontSize: widget.titleTextSize.sp,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  _buildEtype(),
                ],
              ),
            ],
          ),
        ),
        centerTitle: false,
      ),
    );
  }

  ///选择业务员Widget
  Widget _buildEtype() {
    return GestureDetector(
      child: Container(
        width: 400.w,
        height: 60.h,
        padding: EdgeInsets.only(right: 10.w),
        child: Row(
          children: [
            Expanded(
              child: Container(
                alignment: Alignment.centerRight,
                padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 10.w),
                child: HaloPosLabel(
                  "业务员:",
                  textStyle: TextStyle(
                    fontSize: 26.sp,
                    color: Colors.black,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            ),
            StatefulBuilder(
              builder: (context, setState) {
                _etypeStateSetter = setState;
                return Container(
                  alignment: Alignment.centerRight,
                  padding: EdgeInsets.symmetric(
                    vertical: 10.w,
                    horizontal: 10.w,
                  ),
                  child: HaloPosLabel(
                    StringUtil.isEmpty(_currentEtype?.etypeName)
                        ? "无"
                        : _currentEtype!.etypeName!,
                    textStyle: TextStyle(
                      color: Colors.black,
                      fontSize: 26.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              },
            ),
            const Icon(Icons.arrow_drop_down),
          ],
        ),
      ),
      onTap: () {
        HaloDialog(
          context,
          child: EtypeSelector(
            onItemClick: (buildContext, selectEtype) async {
              _etypeStateSetter?.call(() => _currentEtype = selectEtype);
              NavigateUtil.pop(context);
            },
          ),
        ).show();
      },
    );
  }

  /*返回*/
  _buildBack() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => NavigateUtil.pop(context),
      child: Container(
        width: 80.w,
        height: double.infinity,
        alignment: Alignment.center,
        child: IconFont(
          IconNames.ngp_left_back,
          size: 32.w,
          color: ColorUtil.color2String(
            AppColorHelper(context).getTitleBoldTextColor(),
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();

    // 初始化列配置
    if (widget.billType == TenderBillType.StockBillApply) {
      // 库存调拨使用专用配置（包含库存列）
      columnConfigList = SpTool.getStockBillTenderManageColumnConfig();
    } else {
      // 要货申请使用要货申请的列配置
      columnConfigList = SpTool.getTenderRequestColumnConfig();
    }

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      FocusScope.of(context).requestFocus(_searchFocusNode); // 自动聚焦
    });

    permissionDto = SpTool.getPermission();

    BaseInfoModel.selectOtypeWithKtype(context).then((result) {
      ktypeList =
          result
              .where(
                //排除当前和当前门店相同的仓库
                (element) => element.ktypeId != SpTool.getStoreInfo()?.ktypeId,
              )
              .toList();

      // 如果是编辑模式，初始化数据
      if (widget.editOrderData != null) {
        _initEditData();
      }
    });
    //开单自动带出经手人
    if (SpTool.getSystemConfig().recordsheetBillEnabledDefaultEtype == true) {
      LoginUserModel loginUser = LoginCenter.getLoginUser();
      _currentEtype =
          StoreEtype()
            ..etypeName = loginUser.user!
            ..etypeId = loginUser.employeeId!;
    }
  }

  /// 初始化编辑数据
  void _initEditData() {
    if (widget.editOrderData == null) return;

    final editData = widget.editOrderData!;

    // 设置订单数据
    orderBillDTO = editData;

    // 设置门店信息
    if (editData.ktypeId != null && editData.ktypeId2 != null) {
      // 查找对应的门店信息
      try {
        final targetStore = ktypeList.firstWhere(
          (store) => store.ktypeId == editData.ktypeId,
        );
        _currentStore = targetStore;
        _storeStateSetter?.call(() {});
      } catch (e) {
        // 如果找不到对应的门店，忽略错误
      }
    }

    // 设置经手人信息
    if (editData.etypeId != null && editData.efullname != null) {
      _currentEtype =
          StoreEtype()
            ..etypeId = editData.etypeId
            ..etypeName = editData.efullname;
      _etypeStateSetter?.call(() {});
    }

    // 设置商品列表
    if (editData.detail != null && editData.detail!.isNotEmpty) {
      goodsDetailList.clear();
      goodsDetailList.addAll(editData.detail!);
      _reCalcStatistic();
    }

    // 设置备注
    memo = editData.memo ?? "";

    // 刷新界面
    setState(() {});
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return HaloContainer(
      color: Colors.white,
      direction: Axis.vertical,
      width: double.infinity,
      children: [
        Container(
          color: Colors.white,
          height: 70.w,
          width: double.infinity,
          child: _buildTable(true),
        ),
        Expanded(child: SingleChildScrollView(child: _buildTable(false))),
        _buildBottom(),
      ],
    );
  }

  @override
  buildTopBody(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(100.w),
      child: HaloContainer(
        color: ColorUtil.stringColor("#FFFFFF"),
        height: 100.w,
        padding: EdgeInsets.symmetric(vertical: 16.w, horizontal: 20.w),
        children: [
          buildSearch(),
          SizedBox(width: 10.w),
          //添加商品
          buildAddGoodsProp(),
          _buildSelectorKtype(),
          Expanded(child: Container()),
          buildMemoBtn(),
          buildClearBtn(),
        ],
      ),
    );
  }

  // 构建表格，isHeader参数区分是表头还是内容
  Widget _buildTable(bool isHeader) {
    // 获取启用的列类型
    final enabledColumnTypes =
        columnConfigList
            .where(
              (config) =>
                  (config.isShow || config.isRequired) &&
                  config.type != TenderColumnType.all,
            )
            .map((config) => config.type)
            .toList();

    // 确保设置列总是显示
    if (!enabledColumnTypes.contains(TenderColumnType.setting)) {
      enabledColumnTypes.add(TenderColumnType.setting);
    }

    // 创建列宽度和标题配置
    Map<int, double> columnWidths = {};
    Map<int, String> columnTitles = {};

    // 配置列宽和标题
    for (int i = 0; i < enabledColumnTypes.length; i++) {
      var type = enabledColumnTypes[i];

      // 根据列类型设置宽度
      double width = 1.0;
      if (type == TenderColumnType.pName || type == TenderColumnType.number) {
        width = 2.2;
      } else if (type == TenderColumnType.image) {
        width = 0.8;
      } else if (type == TenderColumnType.userCode ||
          type == TenderColumnType.barCode) {
        width = 1.5;
      } else if (type == TenderColumnType.setting) {
        width = 0.5;
      }

      columnWidths[i] = width;

      // 设置列标题
      String title =
          columnConfigList
              .firstWhere(
                (config) => config.type == type,
                orElse: () => TenderColumnConfig(title: "Unknown"),
              )
              .title;
      columnTitles[i] = title;
    }

    if (isHeader) {
      // 表头
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: CustomColumnTable<String>(
          columnConfig: columnWidths,
          columnTitle: columnTitles,
          data: [""], // 表头只需一行数据
          scrollable: false,
          border: const TableBorder(
            bottom: BorderSide(color: AppColors.lineColor),
          ),
          columnTitleBuilder: (title, columnIndex) {
            var type = enabledColumnTypes[columnIndex];

            // 如果是设置列，显示设置图标
            if (type == TenderColumnType.setting) {
              return Container(
                color: ColorUtil.stringColor("#F5F5F5"),
                height: 70.w,
                alignment: Alignment.center,
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    // 打开列配置弹窗
                    DialogUtil.showAlertDialog(
                      context,
                      child: ColumnConfigPages(
                        columnPagesType:
                            widget.billType == TenderBillType.StockBillApply
                                ? ColumnPagesType
                                    .ColumnPagesStockBillTenderManage
                                : ColumnPagesType.ColumnPagesTenderRequest,
                        changed: (newConfig) {
                          setState(() {
                            columnConfigList =
                                newConfig as List<TenderColumnConfig>;
                          });
                        },
                      ),
                    );
                  },
                  child: IconFont(IconNames.shezhi),
                ),
              );
            }

            // 对其他列，确定对齐方式
            AlignmentGeometry alignment;
            switch (type) {
              case TenderColumnType.unit:
              case TenderColumnType.image:
              case TenderColumnType.number:
                alignment = Alignment.center;
                break;
              default:
                alignment = Alignment.centerLeft;
            }

            return Container(
              color: ColorUtil.stringColor("#F5F5F5"),
              height: 70.w,
              alignment: alignment,
              child: HaloPosLabel(
                title,
                maxLines: 1,
                textStyle: TextStyle(
                  color: AppColorHelper(context).getTitleBoldTextColor(),
                  decoration: TextDecoration.none,
                  fontSize: ScreenUtil().setSp(20),
                ),
              ),
            );
          },
          cellBuilder: (item, columnIndex) {
            // 表头的cellBuilder不会被调用
            return Container();
          },
        ),
      );
    } else {
      // 表格内容
      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: CustomColumnTable<GoodsDetailDto>(
          columnConfig: columnWidths,
          columnTitle: null,
          data: goodsDetailList,
          scrollable: false,
          border: const TableBorder(
            horizontalInside: BorderSide(color: AppColors.lineColor),
            bottom: BorderSide(color: AppColors.lineColor),
          ),
          cellBuilder: (item, columnIndex) {
            var type = enabledColumnTypes[columnIndex];

            // 处理不同类型的列
            switch (type) {
              case TenderColumnType.userCode: // 商品编码
                return _buildTabItem(item.pUserCode);
              case TenderColumnType.image: // 图片
                return _buildTabPic(item.picUrl ?? "");
              case TenderColumnType.pName: // 商品名称
                return _buildTabItem(item.pFullName ?? "", detailDto: item);
              case TenderColumnType.unit: // 单位
                return _buildTabItem(
                  item.unitName,
                  alignment: Alignment.center,
                );
              case TenderColumnType.number: // 数量
                return _buildCountStepper(item);
              case TenderColumnType.barCode: // 条码
                return _buildTabItem(item.fullbarcode.toString());
              case TenderColumnType.model: // 型号
                return _buildTabItem(item.ptypetype ?? "");
              case TenderColumnType.spec: // 规格
                return _buildTabItem(item.standard ?? "");
              case TenderColumnType.stock: // 库存
                return _buildTabItem(item.stockQty?.toString() ?? "");
              case TenderColumnType.attributeFormat: // 属性格式
                return _buildTabItem(item.propFormat ?? "");
              case TenderColumnType.attributeCombo: // 属性组合
                return _buildTabItem(item.propValues ?? "");
              case TenderColumnType.serialNumber: // 序列号
                return _buildTabItem(
                  item.serialNoList.map((e) => e.snno).join(","),
                );
              case TenderColumnType.produceDate: // 生产日期
                return _buildTabItem(
                  item.produceDate != null
                      ? formatDateStringToLocal(
                        item.produceDate,
                        format: "yyyy-MM-dd",
                      )
                      : "",
                );
              case TenderColumnType.qualityDays: // 保质期
                return _buildTabItem(item.protectDays?.toString() ?? "");
              case TenderColumnType.expireDate: // 到期日期
                return _buildTabItem(
                  item.expireDate != null
                      ? formatDateStringToLocal(
                        item.expireDate,
                        format: "yyyy-MM-dd",
                      )
                      : "",
                );
              case TenderColumnType.batchNumber: // 批次号
                return _buildTabItem(item.batchNo ?? "");
              case TenderColumnType.setting: // 设置列（空白占位）
                return Container(height: 70.w, color: Colors.white);
              default:
                return Container(height: 70.w, color: Colors.white);
            }
          },
        ),
      );
    }
  }

  //item tab
  Widget _buildTabItem(
    String? text, {
    EdgeInsets padding = const EdgeInsets.only(left: 0.0),
    AlignmentGeometry? alignment,
    GoodsDetailDto? detailDto,
  }) {
    return HaloContainer(
      height: 70.w,
      direction: Axis.vertical,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: detailDto != null,
          child: Expanded(
            child: Container(
              padding: padding,
              alignment: alignment ?? Alignment.centerLeft,
              child: PtypeNoteRichText(
                goodsDetailDto: detailDto ?? GoodsDetailDto(),
                showProp: false,
                textStyle: TextStyle(
                  color: ColorUtil.stringColor("#494848"),
                  fontSize: 24.sp,
                ),
              ),
            ),
          ),
        ),
        Visibility(
          visible: detailDto == null,
          child: Expanded(
            child: Container(
              padding: padding,
              alignment: alignment ?? Alignment.centerLeft,
              child: HaloPosLabel(
                text ?? "",
                textStyle: TextStyle(
                  color: ColorUtil.stringColor("#494848"),
                  fontSize: 24.sp,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  //加减器
  Widget _buildCountStepper(GoodsDetailDto detailDto) {
    return Column(
      children: [
        Container(
          height: 68.6.w,
          alignment: Alignment.center,
          child: HaloCountStepper(
            decimalCount: SpTool.getSystemConfig().sysDigitalQty,
            notifier: HaloCountStepperNotifier(
              max: SpTool.getSystemConfig().sysGlobalDecimalMax,
              defaultValue: num.parse(
                DecimalDisplayHelper.getQtyFixed(
                  detailDto.unitQty?.toString() ?? "0",
                ),
              ),
            ),
            enable: true,
            inputBackgroundColor: Colors.white,
            textColor: Colors.black,
            inputFontWeight: FontWeight.w600,
            inputMinWidth: 90.w,
            inputFontSize: 22.sp,
            borderRadius: BorderRadius.circular(6.w),
            border: Border.all(color: AppColors.txtBorderColor, width: 1),
            textColorDiy: true,
            addIcon: Container(
              height: 40.w,
              width: 40.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6.w),
                border: Border.all(color: AppColors.txtBorderColor, width: 1),
              ),
              child: Text(
                "+",
                style: TextStyle(
                  color: const Color(0xFF606060),
                  fontSize: 24.sp,
                ),
              ),
            ),
            subIcon: Container(
              height: 40.w,
              width: 40.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6.w),
                border: Border.all(color: AppColors.txtBorderColor, width: 1),
              ),
              child: Text(
                "-",
                style: TextStyle(
                  color: const Color(0xFF606060),
                  fontSize: 30.sp,
                ),
              ),
            ),
            onChanged: (value) {
              detailDto.unitQty = num.parse(value.toString());
              if (detailDto.unitQty! <= 0) {
                goodsDetailList.remove(detailDto);
              }
              _reCalcStatistic();
            },
            onCompleted: (value) {
              detailDto.unitQty = num.parse(value.toString());
              if (detailDto.unitQty! <= 0) {
                goodsDetailList.remove(detailDto);
              }
              _reCalcStatistic();
            },
          ),
        ),
      ],
    );
  }

  //搜索框
  Widget buildSearch() {
    return ScanSelectWidget(
      searchFocusNode: _searchFocusNode,
      hint: "扫描或输入商品条码",
      onSubmitted: (text) {
        _doAddGoods(text);
      },
    );
  }

  ///清空
  Widget buildClearBtn() {
    return _buildTopButton(
      "清空",
      IconNames.shanchu,
      onTap: () {
        goodsDetailList.clear();
        _reCalcStatistic();
      },
    );
  }

  ///顶部备注按钮
  Widget buildMemoBtn() {
    return _buildTopButton("备注", IconNames.xiaoshoudingdan, onTap: memoBill);
  }

  memoBill() {
    showDialog(
      context: context,
      builder: (c) {
        return AddTipsPage(
          defaultText: memo,
          submitCallback: (String text) {
            memo = text;
          },
        );
      },
    );
  }

  ///添加商品按钮
  Widget buildAddGoodsProp() {
    return _buildAddGoodsButton("选择商品", () {
      showDialog(
            context: context,
            builder: (context) => const GoodsAndComboSelectListPage(),
          )
          .then(
            (goods) => ScanTool.handleScanResult(
              context,
              goods,
              goodsDetailList ?? [],
              BillType.SaleBill,
              handleBatch: widget.billType == TenderBillType.StockBillApply,
            ),
          )
          .then((List<GoodsDetailDto>? list) {
            if (list != null) {
              if (list.first.comboRow) {
                HaloToast.showError(context, msg: "不支持套餐类型");
              } else {
                _setGoodsDetail(list);
              }
            }
          });
    });
  }

  //选择仓库
  Widget _buildSelectorKtype() {
    String title;
    if (widget.billType == TenderBillType.TenderBillApply) {
      //向总部要货，不展示仓库选择
      if (requestToHead) {
        return Container();
      }
      title = "申请要货";
    } else {
      title = "发起调拨";
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: () => _showStorePopWindow(context),
          behavior: HitTestBehavior.opaque,
          child: HaloContainer(
            key: _storeGlobalKey,
            mainAxisSize: MainAxisSize.max,
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            margin: EdgeInsets.only(right: 20.w),
            height: 62.w,
            width: 361.w,
            borderRadius: BorderRadius.all(Radius.circular(6.w)),
            border: Border.all(color: AppColors.borderColor),
            children: [
              Text(
                "向门店:",
                style: TextStyle(fontSize: 24.sp),
              ),
              Expanded(
                child: StatefulBuilder(
                  builder: (context, setState) {
                    _storeStateSetter = setState;
                    return Text(
                      _currentStore?.ofullname ?? "",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: AppColorHelper(context).getTitleBoldTextColor(),
                        fontWeight: FontWeight.w500,
                        fontSize: 24.sp,
                      ),
                    );
                  },
                ),
              ),
              SizedBox(width: 10.w),
              IconFont(IconNames.guolvxiala, size: 12.w),
            ],
          ),
        ),
        Text(title, style: TextStyle(fontSize: 24.sp)),
      ],
    );
  }

  //仓库选择
  _showStorePopWindow(BuildContext context) {
    HaloPopWindow().show(
      _storeGlobalKey,
      gravity: PopWindowGravity.bottom,
      backgroundColor: Colors.transparent,
      child: Container(
        margin: EdgeInsets.zero,
        width: 361.w,
        constraints: BoxConstraints(maxHeight: 300.w),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: AppColors.dividerColor),
          borderRadius: BorderRadius.all(Radius.circular(8.w)),
        ),
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: ktypeList.length,
          itemBuilder: (buildContext, index) {
            return _ItemWidget(
              name: ktypeList[index].ofullname ?? "",
              index: index,
              onSelectItem: (selectIndex) {
                _storeStateSetter?.call(
                  () => _currentStore = ktypeList[selectIndex],
                );
                HaloPopWindow().disMiss();
              },
            );
          },
        ),
      ),
    );
  }

  ///构建无码商品和添加商品按钮
  Widget _buildAddGoodsButton(String title, VoidCallback onTap) {
    return GestureDetector(
      child: Container(
        width: 140.w,
        height: 70.w,
        margin: EdgeInsets.only(right: 10.w),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: AppColors.buttonBorderColor),
          borderRadius: BorderRadius.all(Radius.circular(5)),
        ),
        padding: EdgeInsets.all(12.w),
        child: Text(
          title,
          style: TextStyle(
            height: 1,
            color: ColorUtil.stringColor("#67686A"),
            fontSize: 22.sp,
          ),
        ),
      ),
      onTap: onTap,
    );
  }

  //清空
  Widget _buildTopButton(
    String title,
    IconNames name, {
    required Function onTap,
  }) {
    return Container(
      margin: EdgeInsets.only(right: 12.w),
      child: HaloButton(
        icon: IconFont(name, color: "#3478FF", size: 22.w),
        foregroundColor: Color(0xff3478ff),
        backgroundColor: Color(0xFFE8F1FF),
        fontSize: 26.sp,
        text: title,
        onPressed: () {
          if (onTap != null) {
            onTap();
          }
        },
      ),
    );
  }

  //底部
  Widget _buildBottom() {
    List<Widget> children = [];

    return Column(
      children: [
        const Divider(height: 1, color: AppColors.lineColor),
        HaloContainer(
          padding: EdgeInsets.only(right: 33.w),
          margin: EdgeInsets.only(bottom: 26.w, top: 20.w),
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            HaloPosLabel(
              "总数量:",
              textStyle: TextStyle(
                fontSize: 30.sp,
                color: ColorUtil.stringColor("#666666"),
                height: 1,
              ),
            ),
            SizedBox(width: 16.w),
            HaloPosLabel(
              sumAllQty.toString(),
              textStyle: TextStyle(
                fontSize: 34.sp,
                color: const Color(0xFF4679FC),
                height: 1,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (widget.billType == TenderBillType.TenderBillApply &&
                permissionDto.shopsaletransferOrdercreate == true) ...{
              SizedBox(width: 35.w),
              HaloButton(
                width: 220.w,
                height: 80.w,
                backgroundColor: Colors.white,
                textColor: AppColors.titleBoldTextColor,
                buttonType: HaloButtonType.outlinedButton,
                borderColor: const Color(0xFF272C35),
                outLineWidth: 1,
                text: "保存",
                onPressed: () => _tenderBillSubmit(confirm: false),
              ),
            },
            if (widget.billType != TenderBillType.TenderBillApply ||
                permissionDto.shopsaletransferOrderconfirm == true) ...{
              SizedBox(width: 35.w),
              HaloButton(
                width: 225.w,
                fontSize: 28.sp,
                height: 80.w,
                buttonType: HaloButtonType.elevatedButton,
                text:
                    widget.billType == TenderBillType.TenderBillApply
                        ? "保存并提交"
                        : "提交",
                onPressed: () => _tenderBillSubmit(),
              ),
            },
          ],
        ),
      ],
    );
  }

  /*商品搜索 新增逻辑*/
  //搜索添加商品
  _doAddGoods(String text) async {
    searchString = text;

    ScanTool.scan(
      context: context,
      list: goodsDetailList,
      billType: BillType.SaleBill,
      isCheckBatch: widget.billType == TenderBillType.StockBillApply,
      scanCode: StringUtil.trim(searchString),
      billDate: DateUtil.formatDate(DateTime.now(), format: DateFormats.full),
    ).then((dynamic value) {
      searchString = "";
      if (null != value) {
        if (value.first.comboRow) {
          HaloToast.showError(context, msg: "不支持套餐类型");
        } else {
          _setGoodsDetail(value);
        }
      } else {
        setState(() {});
      }
      FocusScope.of(context).requestFocus(_searchFocusNode);
    });
  }

  //商品新增
  _setGoodsDetail(List<GoodsDetailDto> details) {
    GoodsBillDto goodsBillDto = GoodsBillDto();
    goodsBillDto.vchcode = "0";
    goodsBillDto.vchtype = "TransferOrder";
    BillTool.addGoodsDetails(
      goodsBillDto,
      goodsDetailList,
      details,
      BillType.SaleBill,
    );
    //计算总数量
    _reCalcStatistic();
  }

  ///算总价 总数量
  _reCalcStatistic() {
    sumAllQty = Decimal.parse("0");
    goodsDetailList.forEach((element) {
      sumAllQty =
          sumAllQty + MathUtil.parseToDecimal(element.unitQty.toString());
    });
    setState(() {});
  }

  //提交调拨
  _tenderBillSubmit({bool confirm = true}) async {
    if (goodsDetailList.any(
      (element) => !checkSysMax(element.unitQty.toString()),
    )) {
      HaloToast.showError(context, msg: "数量超出系统最大");
      return;
    }
    if (!checkBill(widget.billType)) {
      return;
    }

    if (widget.billType == TenderBillType.TenderBillApply) {
      List<GoodsDetailDto> detailMap =
          goodsDetailList.map((element) {
            element.vchcode = "0";
            element.vchtype = "TransferOrder";
            element.comboShareScale = 0;
            element.memo = "";

            ///明细备注去掉
            return GoodsDetailDto.fromMap(element.toJson());
          }).toList();
      //编辑模式下使用现有订单，新增模式下拉取新订单
      if (orderBillDTO == null) {
        // 如果是编辑模式，应该已经在初始化时设置了orderBillDTO
        if (widget.editOrderData != null) {
          HaloToast.show(context, msg: "编辑数据异常，请重新进入编辑");
          return;
        }
        // 新增模式：拉取一张新的订单
        OrderBillDTO? orderBill = await _getOrderBill(context);
        if (!mounted) return;
        if (orderBill != null) {
          orderBillDTO = orderBill;
        } else {
          HaloToast.show(context, msg: "获取调拨订单失败");
          return;
        }
      }
      if (!mounted) return;
      PerformanceCaptureUtil.start(PerformanceTimeName.tenderOrderBill);
      final storeInfo = SpTool.getStoreInfo()!;
      orderBillDTO!.ktypeId = _currentStore?.ktypeId;
      orderBillDTO!.kfullname = _currentStore?.kfullname;
      orderBillDTO!.ktypeId2 = storeInfo.ktypeId;
      orderBillDTO!.kfullname2 = storeInfo.ktypeName;
      orderBillDTO!.detail = detailMap;
      orderBillDTO!.etypeId = _currentEtype?.etypeId;
      orderBillDTO!.efullname = _currentEtype?.etypeName;
      orderBillDTO!.employeeId = _currentEtype?.etypeId;
      orderBillDTO!.otypeId = storeInfo.otypeId;
      orderBillDTO!.source = "POS";
      orderBillDTO!.customType = 0;
      orderBillDTO!.billType = "goodsBill";
      orderBillDTO!.postState =
          confirm
              ? BillPostState.PROCESS_COMPLETED.name
              : BillPostState.UNCONFIRMED.name;
      orderBillDTO!.memo = memo;
      BillModel.submitOrderBill(context, orderBillDTO!).then((value) {
        PerformanceCaptureUtil.end(PerformanceTimeName.tenderOrderBill);
        if (value == null) {
          return;
        }
        //请求
        if (value.resultType == BillSaveResultType.SUCCESS) {
          if (!mounted) return;
          HaloToast.showSuccess(context, msg: "已${confirm ? "提交" : "保存"}要货申请");

          // 编辑模式下保存成功后返回上一页
          if (widget.editOrderData != null) {
            Navigator.of(context).pop(true);
          } else {
            // 新增模式下清空数据继续添加
            goodsDetailList.clear();
            _reCalcStatistic();
            memo = "";
            orderBillDTO = null;
          }
        } else {
          if (!mounted) return;
          HaloToast.showError(context, msg: value.exceptionInfo![0].message);
        }
      });
    } else {
      //库存调拨 先拉取调拨单，填入信息后再提交
      PerformanceCaptureUtil.start(PerformanceTimeName.tenderBill);
      await getGoodsBill();
      _submitJxcGoodsBill();
      PerformanceCaptureUtil.end(PerformanceTimeName.tenderBill);
    }
  }

  ///拉取一张调拨单
  Future<void> getGoodsBill() async {
    List<GoodsDetailDto> detailMap =
        goodsDetailList.map((element) {
          GoodsDetailDto newObj = GoodsDetailDto.fromMap(element.toJson());
          newObj.vchcode = "0";
          newObj.vchtype = null;
          newObj.comboShareScale = 0;
          newObj.ktypeId = _currentStore?.ktypeId;
          newObj.kfullname = _currentStore?.kfullname;
          newObj.memo = "";
          newObj.requestUnitQty = newObj.unitQty;
          return newObj;
        }).toList();
    List<GoodsDetailDto> outDetailMap =
        goodsDetailList.map((outElement) {
          GoodsDetailDto newObj = GoodsDetailDto.fromMap(outElement.toJson());
          newObj.vchcode = "0";
          newObj.vchtype = null;
          newObj.comboShareScale = 0;
          newObj.ktypeId = SpTool.getStoreInfo()!.ktypeId;
          newObj.kfullname = SpTool.getStoreInfo()!.ktypeName;
          newObj.memo = "";
          newObj.requestUnitQty = newObj.unitQty;
          return newObj;
        }).toList();
    GoodsBillDto? billDto = await BillModel.getGoodsBill(
      context,
      "",
      BillTypeData[BillType.GoodsTrans],
      "GoodsTrans",
      customType: 1,
    );
    if (billDto != null) {
      //调拨出库
      billDto.customType = 1;
      billDto.postState = BillPostStateString[BillPostState.PROCESS_COMPLETED];
      billDto.billType = "goodsBill";
      billDto.inDetail = detailMap;
      billDto.outDetail = outDetailMap;
      billDto.ktypeId = SpTool.getStoreInfo()!.ktypeId;
      billDto.kfullname = SpTool.getStoreInfo()!.ktypeName;
      billDto.ktypeId2 = _currentStore?.ktypeId;
      billDto.kfullname2 = _currentStore?.kfullname;
      billDto.etypeId = _currentEtype?.etypeId;
      billDto.efullname = _currentEtype?.etypeName;
      billDto.createEfullname = LoginCenter.getLoginUser()?.user!;
      billDto.createEtypeId = LoginCenter.getLoginUser()?.employeeId!;
      billDto.createUserCode = LoginCenter.getLoginUser().userCode;
      billDto.otypeId = SpTool.getStoreInfo()!.otypeId;
      billDto.createType = "INPUT";
      billDto.memo = memo;
      //不让sale的单据提交接口自动过账，因为是库存调拨
      // billDto.whetherPost = false;

      //这里本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill
      billDto.vchtype = BillType.GoodsTrans.name;
      billDto.onlyPost = false;
      billDto.orderSaleMode = "";
    }
    this.billDto = billDto;
  }

  void _submitJxcGoodsBill() {
    if (billDto != null) {
      BillModel.jxcSaveGoodsBill(context, billDto!).then((value) {
        if (value.resultType == BillSaveResultType.SUCCESS) {
          setState(() {
            HaloToast.showSuccess(context, msg: "调拨成功");
            goodsDetailList.clear();
            _reCalcStatistic();
            PtypeModel.changePtypeStockQtyByGoodsBill(
              outDetail: billDto!.outDetail,
            );
            memo = "";
          });
        } else if (value.resultType == BillSaveResultType.ERROR) {
          DialogUtil.showAlertDialog(
            context,
            childContent: Container(
              margin: const EdgeInsets.only(top: 10),
              child: ExceptionWidget(value.exceptionInfo!, (
                context,
                index,
                BillSaveExceptionDto itemData,
              ) {
                return ExceptionItemWidget(
                  itemData.message ??
                      StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
                  _createSaveExceptionMessage(itemData.detailList!),
                  errorList: const [],
                );
              }),
            ),
            title: value.message ?? "",
          );
        } else if (value.resultType == BillSaveResultType.INIOVER) {
          HaloToast.showError(context, msg: value.exceptionInfo![0].message);
        } else if (value.resultType == BillSaveResultType.CONFIRM) {
          DialogUtil.showConfirmDialog(
            context,
            actionLabels: ["取消", "继续"],
            confirmCallback: () {
              billDto!.confirm = true;
              _submitJxcGoodsBill();
            },
            childContent: Container(
              margin: const EdgeInsets.only(top: 10),
              child: ExceptionWidget(value.exceptionInfo!, (
                context,
                index,
                BillSaveExceptionDto itemData,
              ) {
                return ExceptionItemWidget(
                  itemData.message ??
                      StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
                  _createSaveExceptionMessage(itemData.detailList!),
                  errorList: const [],
                );
              }),
            ),
            title: value.message!,
          );
        } else {
          HaloToast.showError(context, msg: value.exceptionInfo![0].message);
        }
      });
    }
  }

  static String _createSaveExceptionMessage(
    List<BillSaveExceptionDetailDTO> details,
  ) {
    String names = "";
    for (var item in details) {
      if (!StringUtil.isEmpty(names)) {
        names += "\n";
      }
      names +=
          "${item.pfullname ?? ""} ${item.propValues ?? ""} ${item.message ?? ""}";
    }
    return names;
  }

  bool checkSysMax(String value) {
    List<String> valueString = value.split(".");
    int length = 0;
    valueString.forEach((element) {
      length += element.length;
    });
    if (length >= 12) {
      return false;
    }
    return true;
  }

  bool checkBill(TenderBillType billType) {
    if (billType == TenderBillType.TenderBillApply) {
      if (permissionDto.recordsheetGoodsTransOrderBillcreate != null &&
          !permissionDto.recordsheetGoodsTransOrderBillcreate!) {
        HaloToast.showError(context, msg: "没有创建要货订单的权限！");
        return false;
      }
      if (goodsDetailList.length <= 0) {
        HaloToast.showError(context, msg: "请先添加要货商品");
        return false;
      }
    } else {
      if (permissionDto.recordsheetTransGoodsBillcreate != null &&
          !permissionDto.recordsheetTransGoodsBillcreate!) {
        HaloToast.showError(context, msg: "没有库存调拨的权限！");
        return false;
      }
      if (permissionDto.recordsheetTransGoodsBillsaveBill != null &&
          !permissionDto.recordsheetTransGoodsBillsaveBill!) {
        HaloToast.showError(context, msg: "没有库存保存过账的权限！");
        return false;
      }
      if (goodsDetailList.length <= 0) {
        HaloToast.showError(context, msg: "请先添加调拨商品");
        return false;
      }

      if (StringUtil.isEmpty(_currentStore?.ktypeId)) {
        HaloToast.showError(context, msg: "请先选择入库门店");
        return false;
      }
    }
    return true;
  }

  @override
  String getActionBarTitle() {
    return "";
  }

  @override
  Future<void> onInitState() {
    if (widget.billType == TenderBillType.TenderBillApply) {
      // 编辑模式下跳过选择要货类型的弹窗，直接使用原单的门店信息
      if (widget.editOrderData != null) {
        // 编辑模式：根据原单的门店信息设置要货类型
        final editData = widget.editOrderData!;
        // 如果原单没有指定调出仓库或调出仓库为空，则认为是总部要货
        // 否则认为是门店要货
        if (editData.ktypeId == null ||
            editData.ktypeId!.isEmpty ||
            editData.ktypeId == "0") {
          requestToHead = true; // 总部要货
        } else {
          requestToHead = false; // 门店要货
        }
      } else {
        // 新增模式：显示选择要货类型的弹窗
        Future.delayed(const Duration(microseconds: 500)).then((value) {
          if (!mounted) return;
          showDialog(
            context: context,
            builder: (context) => const RequestToHeadDialog(),
          ).then((value) {
            if (value != null) {
              setState(() => requestToHead = value);
            }
          });
        });
      }
    }
    return Future.value(null);
  }

  ///拉取空白的调拨订单
  Future<OrderBillDTO?> _getOrderBill(BuildContext context) {
    BillLoadRequest request = BillLoadRequest(
      businessType: BillBusinessType.GoodsTrans.name,
      sourceVchtype: BillType.TransferOrder.name,
      targetVchtype: BillType.TransferOrder.name,
      vchtype: BillType.TransferOrder.name,
    );
    return BillModel.getOrderBill(context, request);
  }

  ///构建图片
  Widget _buildItemImage(String picUrl, {int size = 100}) {
    final defaultImage = Container(
      width: size.w,
      height: size.w,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: Icon(
        Icons.image_not_supported,
        color: Colors.grey[400],
        size: 24.w,
      ),
    );

    return SizedBox(
      width: size.w,
      height: size.w,
      child: Image.network(
        picUrl,
        fit: BoxFit.cover,
        width: size.w,
        height: size.w,
        loadingBuilder: (
          BuildContext context,
          Widget child,
          ImageChunkEvent? loadingProgress,
        ) {
          if (loadingProgress == null) {
            return child;
          }
          return defaultImage;
        },
        errorBuilder:
            (BuildContext context, Object error, StackTrace? stackTrace) =>
                defaultImage,
      ),
    );
  }

  //表格中的图片
  Widget _buildTabPic(String picUrl) {
    return Column(
      children: [
        Container(
          height: 70.w,
          alignment: Alignment.center,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5.w),
            child: _buildClickableImage(context, picUrl, size: 60),
          ),
        ),
        Divider(height: 1, color: AppColors.lineColor),
      ],
    );
  }

  ///构建可点击查看大图的图片控件
  Widget _buildClickableImage(
    BuildContext context,
    String picUrl, {
    int size = 100,
  }) {
    Widget image = _buildItemImage(picUrl, size: size);
    if (StringUtil.isNotEmpty(picUrl)) {
      image = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap:
            () => DialogUtil.showAlertDialog(
              context,
              actionCount: 0,
              childContent: _buildItemImage(picUrl, size: 500),
            ),
        child: image,
      );
    }
    return image;
  }
}

class _ItemWidget extends StatelessWidget {
  final Function(int) onSelectItem;
  final String name;
  final int index;

  const _ItemWidget({
    required this.name,
    required this.index,
    required this.onSelectItem,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        onSelectItem(index);
      },
      child: Container(
        height: 60.h,
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(color: AppColors.dividerColor)),
        ),
        child: Text(
          name,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 24.sp,
            color: AppColorHelper(context).getTitleBoldTextColor(),
          ),
        ),
      ),
    );
  }
}

///确认是向总部要货还是向门店要货
class RequestToHeadDialog extends StatelessWidget {
  const RequestToHeadDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      constrainedAxis: Axis.vertical,
      child: SizedBox(
        width: 463.w,
        child: Dialog(
          insetPadding: EdgeInsets.zero,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildItem(
                title: "向总部要货",
                onTap: () => Navigator.pop(context, true),
              ),
              const Divider(height: 1, color: AppColors.lineColor),
              _buildItem(
                title: "向门店要货",
                onTap: () => Navigator.pop(context, false),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItem({required String title, required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        color: Colors.white,
        width: double.infinity,
        height: 80.h,
        alignment: Alignment.centerLeft,
        child: Text(
          title,
          style: TextStyle(fontSize: 32.sp),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}
