class OperationBillResponse {
  List<ErrorDetailBean?>? errorDetail;
  String? partialDataFields;
  late String result;
  late bool success;

  static OperationBillResponse? fromMap(Map<String, dynamic> map) {
    OperationBillResponse operationBillResponseBean = OperationBillResponse();
    List errorDetail = map['errorDetail'] ?? [];
    operationBillResponseBean.errorDetail = []
      ..addAll(errorDetail.map((o) => ErrorDetailBean.fromMap(o)));
    operationBillResponseBean.partialDataFields = map['partialDataFields'];
    operationBillResponseBean.result = map['result'] ?? "";
    operationBillResponseBean.success = map['success'] ?? false;
    return operationBillResponseBean;
  }

  Map toJson() => {
        "errorDetail": errorDetail,
        "partialDataFields": partialDataFields,
        "result": result,
        "success": success,
      };
}

class ErrorDetailBean {
  late String bizErrorCode;
  List<DetailListBean?>? detailList;
  String? message;
  List<RelationBillListBean?>? relationBillList;

  static ErrorDetailBean? fromMap(Map<String, dynamic> map) {
    if (map == null) return null;
    ErrorDetailBean errorDetailBean = ErrorDetailBean();
    errorDetailBean.bizErrorCode = map['bizErrorCode'] ?? "";

    List detailList = map['detailList'] ?? [];
    errorDetailBean.detailList = []
      ..addAll(detailList.map((o) => DetailListBean.fromMap(o)));
    errorDetailBean.message = map['message'];

    List relationBillList = map['relationBillList'] ?? [];

    errorDetailBean.relationBillList = []
      ..addAll(relationBillList.map((o) => RelationBillListBean.fromMap(o)));
    return errorDetailBean;
  }

  Map toJson() => {
        "bizErrorCode": bizErrorCode,
        "detailList": detailList,
        "message": message,
        "relationBillList": relationBillList,
      };
}

class RelationBillListBean {
  String? createType;
  String? createTypeName;
  String? date;
  String? deleErrorMsg;
  String? fields;
  String? modErrorMsg;
  String? number;
  int? postState;
  int? processType;
  bool? showDetail;
  int? sourceId;
  int? status;
  String? vchcode;
  int? vchtype;
  String? vchtypeEnum;
  String? vchtypeName;

  static RelationBillListBean? fromMap(Map<String, dynamic> map) {
    if (map == null) return null;
    RelationBillListBean relationBillListBean = RelationBillListBean();
    relationBillListBean.createType = map['createType'];
    relationBillListBean.createTypeName = map['createTypeName'];
    relationBillListBean.date = map['date'];
    relationBillListBean.deleErrorMsg = map['deleErrorMsg'];
    relationBillListBean.fields = map['fields'];
    relationBillListBean.modErrorMsg = map['modErrorMsg'];
    relationBillListBean.number = map['number'];
    relationBillListBean.postState = map['postState'];
    relationBillListBean.processType = map['processType'];
    relationBillListBean.showDetail = map['showDetail'];
    relationBillListBean.sourceId = map['sourceId'];
    relationBillListBean.status = map['status'];
    relationBillListBean.vchcode = map['vchcode'];
    relationBillListBean.vchtype = map['vchtype'];
    relationBillListBean.vchtypeEnum = map['vchtypeEnum'];
    relationBillListBean.vchtypeName = map['vchtypeName'];
    return relationBillListBean;
  }

  Map toJson() => {
        "createType": createType,
        "createTypeName": createTypeName,
        "date": date,
        "deleErrorMsg": deleErrorMsg,
        "fields": fields,
        "modErrorMsg": modErrorMsg,
        "number": number,
        "postState": postState,
        "processType": processType,
        "showDetail": showDetail,
        "sourceId": sourceId,
        "status": status,
        "vchcode": vchcode,
        "vchtype": vchtype,
        "vchtypeEnum": vchtypeEnum,
        "vchtypeName": vchtypeName,
      };
}

class DetailListBean {
  int? availableSendStockQty;
  int? backQty;
  String? batchNo;
  bool? batchenabled;
  String? costPrice;
  String? currencyPrice;
  int? detailQty;
  String? expireDate;
  String? fullbarcode;
  String? kfullname;
  String? ktypeId;
  int? line;
  String? message;
  int? minPrice;
  String? pfullname;
  String? priceShowFieldEnum;
  String? produceDate;
  String? prop1Name;
  String? prop2Name;
  String? propValues;
  bool? propenabled;
  int? protectDays;
  String? ptypeId;
  int? ptypeQty;
  String? ptypeUserCode;
  String? ptypetype;
  String? qty;
  int? recPrice;
  int? serialQty;
  int? snenabled;
  String? standard;
  String? stockQty;
  int? unCompletedQty;

  static DetailListBean? fromMap(Map<String, dynamic> map) {
    if (map == null) return null;
    DetailListBean detailListBean = DetailListBean();
    detailListBean.availableSendStockQty = map['availableSendStockQty'];
    detailListBean.backQty = map['backQty'];
    detailListBean.batchNo = map['batchNo'];
    detailListBean.batchenabled = map['batchenabled'];
    detailListBean.costPrice = map['costPrice'];
    detailListBean.currencyPrice = map['currencyPrice'];
    detailListBean.detailQty = map['detailQty'];
    detailListBean.expireDate = map['expireDate'];
    detailListBean.fullbarcode = map['fullbarcode'];
    detailListBean.kfullname = map['kfullname'];
    detailListBean.ktypeId = map['ktypeId'];
    detailListBean.line = map['line'];
    detailListBean.message = map['message'];
    detailListBean.minPrice = map['minPrice'];
    detailListBean.pfullname = map['pfullname'];
    detailListBean.priceShowFieldEnum = map['priceShowFieldEnum'];
    detailListBean.produceDate = map['produceDate'];
    detailListBean.prop1Name = map['prop1Name'];
    detailListBean.prop2Name = map['prop2Name'];
    detailListBean.propValues = map['propValues'];
    detailListBean.propenabled = map['propenabled'];
    detailListBean.protectDays = map['protectDays'];
    detailListBean.ptypeId = map['ptypeId'];
    detailListBean.ptypeQty = map['ptypeQty'];
    detailListBean.ptypeUserCode = map['ptypeUserCode'];
    detailListBean.ptypetype = map['ptypetype'];
    detailListBean.qty = map['qty'];
    detailListBean.recPrice = map['recPrice'];
    detailListBean.serialQty = map['serialQty'];
    detailListBean.snenabled = map['snenabled'];
    detailListBean.standard = map['standard'];
    detailListBean.stockQty = map['stockQty'];
    detailListBean.unCompletedQty = map['unCompletedQty'];
    return detailListBean;
  }

  Map toJson() => {
        "availableSendStockQty": availableSendStockQty,
        "backQty": backQty,
        "batchNo": batchNo,
        "batchenabled": batchenabled,
        "costPrice": costPrice,
        "currencyPrice": currencyPrice,
        "detailQty": detailQty,
        "expireDate": expireDate,
        "fullbarcode": fullbarcode,
        "kfullname": kfullname,
        "ktypeId": ktypeId,
        "line": line,
        "message": message,
        "minPrice": minPrice,
        "pfullname": pfullname,
        "priceShowFieldEnum": priceShowFieldEnum,
        "produceDate": produceDate,
        "prop1Name": prop1Name,
        "prop2Name": prop2Name,
        "propValues": propValues,
        "propenabled": propenabled,
        "protectDays": protectDays,
        "ptypeId": ptypeId,
        "ptypeQty": ptypeQty,
        "ptypeUserCode": ptypeUserCode,
        "ptypetype": ptypetype,
        "qty": qty,
        "recPrice": recPrice,
        "serialQty": serialQty,
        "snenabled": snenabled,
        "standard": standard,
        "stockQty": stockQty,
        "unCompletedQty": unCompletedQty,
      };
}
