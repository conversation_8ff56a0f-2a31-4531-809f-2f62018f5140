import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../../bill/entity/bill_list_requset_dto.dart';
import '../../../bill/entity/order_bill_item_entity.dart';
import '../../../bill/model/bill_model.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/style/app_pos_size.dart';
import '../../../common/tool/date_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_type.dart';
import '../../../widgets/base/base_list.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/halo_pos_label.dart';

enum OrderBillType {
  OrderSelling, //取单
  OrderSaled, //销售单据查询,
  offlineBill, //离线单据,
}

class OrderListPage extends BaseListPage {
  final Function(int, dynamic item) onChanged;
  final OrderBillType billType;
  final bool crossCash;
  final List<OrderBillItem>? billList;

  const OrderListPage(
      {Key? key,
      required this.onChanged,
      this.crossCash = false,
      this.billType = OrderBillType.OrderSelling,
      this.billList})
      : super(
            key: key,
            isShowfilter: billType == OrderBillType.OrderSelling,
            bgColor: Colors.white,
            isPullDownRefresh: billType != OrderBillType.offlineBill,
            isPullUpRefresh: billType != OrderBillType.offlineBill);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() => OrderListPageState();
}

class OrderListPageState extends BaseListPageState<OrderListPage> {
  int selectIndex = 0;

  @override
  Future<void> onInitState() {
    if (null != widget.billList && widget.billList!.isNotEmpty) {
      dataSource = widget.billList!;
    }
    return super.onInitState();
  }

  @override
  Widget buildItemView(int index, item) {
    return GestureDetector(
      child: HaloContainer(
        color: selectIndex == index
            ? AppColors.accentColor.withOpacity(0.12)
            : Colors.white,
        crossAxisAlignment: CrossAxisAlignment.start,
        direction: Axis.vertical,
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 12.w),
        border: Border(
            bottom: BorderSide(color: AppColors.dividerColor, width: 1.w)),
        children: [
          buildNumberAndMarkAndTotal(item),
          buildBillTypeAndDate(item),
        ],
      ),
      onTap: () {
        setState(() {
          selectIndex = index;
        });
        widget.onChanged(selectIndex, dataSource[selectIndex]);
      },
    );
  }

  ///构建单据列表的编号，标签，金额
  Widget buildNumberAndMarkAndTotal(OrderBillItem itemData) {
    return HaloContainer(
      children: [
        HaloPosLabel(
          itemData.billNumber!,
          textStyle: TextStyle(
              color: AppColors.normalFontColor,
              fontWeight: FontWeight.w500,
              decoration: TextDecoration.none,
              fontSize: AppPosSize.secondaryTitleFontSize.sp),
        ),
        Visibility(
            visible: itemData.orderBillMarkList.any((element) =>
                element?.baseOrderMarkEnum != BillMarkEnum.NORMAL_BILL),
            child: HaloContainer(
                margin: EdgeInsets.only(left: 8.w),
                children: getMarkTitle(itemData.orderBillMarkList))),
        Expanded(
            child: HaloPosLabel(
          "￥${itemData.billTotal}",
          textAlign: TextAlign.right,
          textStyle: TextStyle(
              color: AppColors.normalFontColor,
              fontWeight: FontWeight.w500,
              decoration: TextDecoration.none,
              fontSize: AppPosSize.secondaryTitleFontSize.sp),
        ))
      ],
    );
  }

  Widget buildBillTypeAndDate(OrderBillItem itemData) {
    return HaloContainer(
      margin: EdgeInsets.only(top: 2.h, bottom: 2.h),
      children: [
        HaloPosLabel(
          billCodeName[itemData.vchtype]!,
          textStyle: TextStyle(
              color: AppColors.describeFontColor,
              decoration: TextDecoration.none,
              fontSize: AppPosSize.contentFontSize.sp),
        ),
        Expanded(
          child: HaloPosLabel(
            formatDateStringToLocal(itemData.billDate),
            textAlign: TextAlign.right,
            textStyle: TextStyle(
                color: AppColors.describeFontColor,
                decoration: TextDecoration.none,
                fontSize: AppPosSize.contentFontSize.sp),
          ),
        )
      ],
    );
  }

  getMarkTitle(List<OrderBillMark?> orderBillMark) {
    //生成标签文本
    List<Widget> list = [];
    for (OrderBillMark? element in orderBillMark) {
      if (null != element) {
        list.add(_buildMarkTitle(element));
      }
    }
    return list;
  }

  _buildMarkTitle(OrderBillMark mark) {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.only(left: 6.w, right: 6.w, top: 4.h, bottom: 6.h),
      decoration: BoxDecoration(
          color: StringUtil.isNotEmpty(mark.backgroundColor)
              ? ColorUtil.stringColor(mark.backgroundColor!).withOpacity(0.2)
              : AppColors.billMarkBgColor,
          borderRadius: BorderRadius.all(Radius.circular(4.sp)),border: Border.all(color: StringUtil.isNotEmpty(mark.borderColor) ? ColorUtil.stringColor(mark.borderColor!) : AppColors.billMarkBgColor,width: 1.w)),
      child: Text(
        mark.markShowName ?? "",
        style: TextStyle(
            color: StringUtil.isNotEmpty(mark.fontColor)
                ? ColorUtil.stringColor(mark.fontColor!)
                : AppColors.billMarkFontColor,
            fontSize: AppPosSize.describeFontSize.sp),
      ),
    );
  }

  updateWidgetDataSource(List<OrderBillItem> list) {
    setState(() {
      dataSource = list;
    });
  }

  @override
  onItemClick(int index, item) {}

  @override
  filterValue() {
    super.filterValue();

    ///这是清空选中状态和右侧选中信息
    selectIndex = 0;
    dataSource.clear();
    // widget.onChanged(this.seletIndex, dataSource[this.seletIndex]);
  }

  void removeItem(int index) {
    selectIndex = 0;
    dataSource.removeAt(index);
    widget.onChanged(
        selectIndex, dataSource.isEmpty ? null : dataSource[selectIndex]);
  }

  ///筛选条件回调方法
  void filterRequest(String startTime, String endTime, String billNumber,
      String phone, String payOutNo, String goodsId, String unitId) {
    filterParams["starTime"] = startTime;
    filterParams["endTime"] = endTime;
    filterParams["billNumber"] = billNumber;
    filterParams["phone"] = phone;
    filterParams["payOutNo"] = payOutNo;
    filterParams["goodsId"] = goodsId;
    filterParams["unitId"] = unitId;
    selectIndex = 0;
    pageIndex = 1;
    onLoadData();
    dataSource.clear();
    // widget.onChanged(widget.seletIndex, null);
  }

  @override
  Future<List> onRequestData() {
    if (widget.billType == OrderBillType.OrderSaled) {
      return BillModel.orderBillAllList(context, getRequestParams())
          .then((value) {
        if (selectIndex == 0) {
          widget.onChanged(
              selectIndex,
              dataSource.isNotEmpty
                  ? dataSource[selectIndex]
                  : value.isNotEmpty
                      ? value[selectIndex]
                      : null);
        }
        return Future.value(value);
      }).whenComplete(() => null);
    } else if (widget.billType == OrderBillType.offlineBill) {
      return Future.value(dataSource);
    } else {
      return BillModel.orderBillAllList(context, getRequestParams())
          .then((value) {
        if (selectIndex == 0) {
          widget.onChanged(
              selectIndex,
              dataSource.isNotEmpty
                  ? dataSource[selectIndex]
                  : value.isNotEmpty
                      ? value[selectIndex]
                      : null);
        }
        return Future.value(value);
      }).whenComplete(() {});
    }
  }

  Map<String, dynamic> getRequestParams() {
    BillCoreListRequestDto queryParams = BillCoreListRequestDto()
      ..startTime = formatDateStringToUtc(filterParams["starTime"])
      ..endTime = formatDateStringToUtc(filterParams["endTime"]);
    Map<String, dynamic> data = {};
    data["pageIndex"] = pageIndex;
    data["pageSize"] = pageSize;
    queryParams.billNumber = filterParams["billNumber"];
    queryParams.phone = filterParams["phone"];
    queryParams.payOutNo = filterParams["payOutNo"];
    queryParams.postState =
        widget.billType == OrderBillType.OrderSelling ? 0 : 8;
    if (widget.billType == OrderBillType.OrderSelling) {
      queryParams.postStateList = [0];
      queryParams.cashierId =
          widget.crossCash ? null : SpTool.getCashierInfo().id;
      queryParams.otypeId = SpTool.getStoreInfo()!.otypeId;
    }
    queryParams.saleOrbuy = 2;
    queryParams.paymentType = 0;
    queryParams.invoiceType = 0;
    queryParams.goodsId = filterParams["goodsId"];
    queryParams.unitId = filterParams["unitId"];
    queryParams.orderBillModel = OrderBillModelData[OrderBillModel.STORE_SALE]!;
    queryParams.vchtypes = [BillCodeData[BillType.SaleBill]];
    if (widget.billType != OrderBillType.OrderSelling) {
      queryParams.vchtypes = [
        BillCodeData[BillType.SaleBill],
        BillCodeData[BillType.SaleBackBill],
        BillCodeData[BillType.SaleChangeBill]
      ];
      if (!SpTool.getPermission().recordsheetSaleBillview!) {
        queryParams.vchtypes.remove(BillCodeData[BillType.SaleBill]);
      }
      if (!SpTool.getPermission().recordsheetSaleBackBillview!) {
        queryParams.vchtypes.remove(BillCodeData[BillType.SaleBackBill]);
      }
      if (!SpTool.getPermission().recordsheetSaleBackBillview! &&
          !SpTool.getPermission().recordsheetSaleBillview!) {
        queryParams.vchtypes = [];
      }
    }
    queryParams.businessTypeList = [
      BillBusinessTypeData[BillBusinessType.SaleNormal],
      202
    ];
    queryParams.otypeId = SpTool.getStoreInfo()!.otypeId;
    queryParams.businessType =
        BillBusinessTypeString[BillBusinessType.SaleNormal]!;
    //收银员
    data["queryParams"] = queryParams.toJson();
    return data;
  }

  @override
  String getActionBarTitle() {
    return "";
  }
}
