import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/bill/saleorder/offline_sale_list.dart';
import 'package:halo_pos/bill/tool/bill_tool.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import '../../../bill/entity/delete_bill_request.dart';
import '../../../common/style/app_color_helper.dart';
import '../../../common/style/app_colors.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../bill/tool/scan_tool.dart';
import '../../../bill/entity/order_bill_item_entity.dart';
import '../../../enum/bill_type.dart';
import '../../../iconfont/icon_font.dart';
import '../../../bill/model/bill_model.dart';
import '../../../widgets/base/base_stateful_page.dart';
import '../../../widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/color_util.dart';
import 'package:haloui/widget/datepicker/pduration.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';

import '../../common/keyboard_hidden.dart';
import '../../common/login/login_center.dart';
import '../../common/tool/DateTimePickUtil.dart';
import '../../common/tool/sp_tool.dart';
import '../../common/widget/datetime_filter.dart';
import '../../db/bill_db_manager.dart';
import '../../widgets/selector/scan_select.dart';
import '../entity/goods_bill.dto.dart';
import '../entity/ptype/ptype_and_sku_dto.dart';
import '../widget/ptype/goods_and_combo_select_list_page.dart';
import 'widget/order_detail_list.page.dart';
import 'widget/order_list_page.dart';

class SaleOrderList extends BaseStatefulPage {
  final OrderBillType billType;
  final bool crossCash;

  const SaleOrderList({
    Key? key,
    this.billType = OrderBillType.OrderSaled,
    this.crossCash = false,
  }) : super(key: key, rightFlex: 2.8);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      _SaleOrderListState();
}

class _SaleOrderListState extends BaseStatefulPageState<SaleOrderList>
    with DateTimeFilterMixin {
  int index = 0;
  final GlobalKey<OrderListPageState> _globalKey = GlobalKey();
  final GlobalKey<OrderDetailListState> _detailGlobalKey = GlobalKey();

  ///离线单据数
  int _offlineBillCount = 0;

  ///选中第几个
  OrderBillItem? itemData;

  ///选中数据
  DeleteBillRequest deleteBillRequest = DeleteBillRequest();
  GlobalKey filterGlobalKey = GlobalKey(); //筛选
  TextEditingController textBillNumberController = TextEditingController();
  TextEditingController textBillPhoneController = TextEditingController();
  TextEditingController textPTypeNameController = TextEditingController();
  TextEditingController textBillPayOutNoController = TextEditingController();
  TextEditingController textOfflineCountController = TextEditingController();
  final KeyboardHiddenFocusNode _searchFocusNode = KeyboardHiddenFocusNode();
  String skuId = "";
  String unitId = "";

  @override
  Future<void> onInitState() async {
    await setOfflineBillText();
  }

  setOfflineBillText() async {
    String profileId = LoginCenter.getLoginUser().profileId!;
    String? otypeId = SpTool.getStoreInfo()?.otypeId;

    debugPrint('当前门店ID: $otypeId');

    // 按当前门店统计离线单据数量
    _offlineBillCount = await BillDBManager.selectSaleBillCount(
      profileId: profileId,
      otypeId: otypeId,
    );

    // 同时查询所有数据作为对比
    int totalCount = await BillDBManager.selectSaleBillCount(
      profileId: profileId,
      // otypeId: null, // 不传门店ID，查询所有
    );

    debugPrint('当前门店离线单据数: $_offlineBillCount, 总数: $totalCount');

    textOfflineCountController.text = "查看离线单据($_offlineBillCount)";
  }

  @override
  String getActionBarTitle() {
    return widget.billType == OrderBillType.OrderSelling ? "取单" : "销售单据查询";
  }

  @override
  Widget buildLeftBody(BuildContext context) {
    return Container(
      color: Colors.white,
      child: OrderListPage(
        key: _globalKey,
        billType: widget.billType,
        crossCash: widget.crossCash,
        onChanged: (int value, item) {
          setState(() {
            itemData = item;
            index = value;
          });
          _detailGlobalKey.currentState?.updateWightDate(
            itemData?.vchcode,
            itemData != null ? BillTypeCoreToString[itemData?.vchtype] : null,
            itemData?.billTotal?.toString() ?? "0",
          );
        },
      ),
    );
  }

  @override
  Widget buildRightBody(BuildContext context) {
    //右侧详情
    return HaloContainer(
      border: const Border(
        left: BorderSide(width: 1, color: AppColors.dividerColor),
      ),
      direction: Axis.vertical,
      mainAxisSize: MainAxisSize.max,
      children: [
        OrderDetailList(
          _detailGlobalKey, //用于调用方法
          (index >= 0 ? itemData?.vchcode : null),
          BillTypeCoreToString[itemData?.vchtype],
          widget.billType,
          afterDeleteCallback: () {
            ///删除成功
            _globalKey.currentState?.removeItem(index);
          },
        ),
      ],
    );
  }

  @override
  Widget buildTopBody(BuildContext context) {
    return Visibility(
      visible: widget.billType == OrderBillType.OrderSaled,
      child: HaloContainer(
        height: 100.h,
        color: Colors.white,
        padding: EdgeInsets.only(right: 16.w),
        border: const Border(
          bottom: BorderSide(color: AppColors.borderColor, width: 1),
        ),
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          buildDateTimeQuery(context, showFastSelect: true),

          ///单据编号
          Container(
            width: 300.w,
            height: 56.h,
            margin: EdgeInsets.only(left: 16.w),
            padding: EdgeInsets.only(
              left: 16.w,
              right: 8.w,
              top: 10.h,
              bottom: 16.h,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.w)),
              border: Border.all(color: AppColors.borderColor, width: 1.w),
            ),
            child: HaloTextField(
              cursorHeight: 30.h,
              cursorColor: AppColors.accentColor,
              contentPadding: 0,
              controller: textBillNumberController,
              hintText: "输入单据编号进行查询",
              hintStyle: TextStyle(
                color: AppColors.hintColor,
                fontSize: AppPosSize.totalFontSize.sp,
              ),
              textColor: AppColors.normalFontColor,
              fontSize: AppPosSize.totalFontSize.sp,
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              onFilterWindows();
            },
            child: HaloContainer(
              key: filterGlobalKey,
              margin: EdgeInsets.only(left: 14.w),
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              color: Colors.white,
              height: 56.h,
              border: Border.all(color: AppColors.buttonBorderColor),
              borderRadius: const BorderRadius.all(Radius.circular(6)),
              children: [
                HaloPosLabel(
                  "更多筛选",
                  textStyle: TextStyle(
                    fontSize: AppPosSize.secondaryTitleFontSize.sp,
                    color: AppColors.normalFontColor,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 12.w),
          HaloButton(
            borderRadius: 6.sp,
            height: 56.h,
            width: 100.w,
            text: "查询",
            fontSize: AppPosSize.secondaryTitleFontSize.sp,
            backgroundColor: AppColors.accentColor,
            onPressed: () {
              FocusScopeNode currentFocus = FocusScope.of(context);
              if (!currentFocus.hasPrimaryFocus &&
                  currentFocus.focusedChild != null) {
                FocusManager.instance.primaryFocus?.unfocus();
              }
              _filterRequest();
            },
          ),
          SizedBox(width: 12.w),
          HaloButton(
            height: 56.h,
            width: 100.w,
            buttonType: HaloButtonType.outlinedButton,
            borderColor: AppColors.btnBorderColor,
            outLineWidth: 2.w,
            borderRadius: 6.sp,
            textColor: AppColors.normalFontColor,
            fontSize: AppPosSize.secondaryTitleFontSize.sp,
            text: "重置",
            onPressed: () {
              _resetFilter();
            },
          ),

          Expanded(child: Container()),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              if (_offlineBillCount == 0) {
                HaloToast.show(context, msg: "无离线单据数据");
                return;
              }
              NavigateUtil.navigateTo(context, OfflineSaleList()).then((
                value,
              ) async {
                await setOfflineBillText();
              });
            },
            child: Container(
              height: 56.h,
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
                top: 12.h,
                bottom: 16.h,
              ),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.accentColor),
                borderRadius: BorderRadius.all(Radius.circular(6.w)),
              ),
              child: HaloTextField(
                enabled: false,
                contentPadding: 0,
                fontSize: AppPosSize.secondaryTitleFontSize.sp,
                textColor: AppColors.accentColor,
                controller: textOfflineCountController,
              ),
            ),
          ),
        ],
      ),
    );
  }

  onFilterWindows() {
    HaloPopWindow().show(
      filterGlobalKey,
      gravity: PopWindowGravity.leftBottom,
      backgroundColor: Colors.transparent,
      child: HaloContainer(
        direction: Axis.vertical,
        width: 500.w,
        height: 500.h,
        mainAxisSize: MainAxisSize.min,
        color: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
        margin: EdgeInsets.only(top: 20.h),
        borderRadius: BorderRadius.all(Radius.circular(6.w)),
        borderShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.16),
            offset: Offset(0, 3.w),
            blurRadius: 16.w,
            spreadRadius: 0,
          ),
        ],
        children: [
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            margin: EdgeInsets.only(top: 16.h),
            children: [
              Expanded(
                child: HaloContainer(
                  direction: Axis.vertical,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    HaloPosLabel(
                      "商品名称",
                      textStyle: TextStyle(
                        fontSize: AppPosSize.secondaryTitleFontSize.sp,
                        color: AppColors.normalFontColor,
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        HaloPopWindow().disMiss();
                        showDialog(
                          context: context,
                          builder:
                              (context) => const GoodsAndComboSelectListPage(),
                        ).then((value) {
                          if (value is PtypeListModel) {
                            textPTypeNameController.text = value.fullname;

                            if (!value.comboRow) {
                              skuId = value.sku?.id ?? "";
                              unitId = value.unit?.id ?? "";
                            } else {
                              ///套餐行不取单位，套餐行取套餐id
                              skuId = value.comboId ?? "";
                            }
                            onFilterWindows();
                          }
                        });
                      },
                      child: HaloContainer(
                        mainAxisSize: MainAxisSize.max,
                        margin: EdgeInsets.only(top: 8.h),
                        padding: EdgeInsets.symmetric(
                          vertical: 8.h,
                          horizontal: 12.w,
                        ),
                        border: Border.all(
                          color: AppColors.borderColor,
                          width: 1.w,
                        ),
                        borderRadius: const BorderRadius.all(
                          Radius.circular(4),
                        ),
                        children: [
                          Expanded(
                            child: HaloTextField(
                              controller: textPTypeNameController,
                              contentPadding: 0,
                              enabled: false,
                              textColor: AppColors.normalFontColor,
                              fontSize: AppPosSize.secondaryTitleFontSize.sp,
                              hintStyle: const TextStyle(
                                color: AppColors.hintColor,
                              ),
                              maxLines: 1,
                              hintText: "请选择商品查询",
                            ),
                          ),
                          IconFont(IconNames.gengduo_2, size: 5.w),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          _filterItem("手机号", textBillPhoneController),
          _filterItem("支付流水号", textBillPayOutNoController),
          Expanded(child: Container()),
          HaloContainer(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.max,
            children: [
              HaloButton(
                height: 56.h,
                width: 130.w,
                borderRadius: 6.sp,
                fontSize: AppPosSize.secondaryTitleFontSize.sp,
                backgroundColor: AppColors.accentColor,
                text: "确定",
                onPressed: () {
                  _filterRequest();
                  HaloPopWindow().disMiss();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  _resetFilter() {
    textPTypeNameController.text = "";
    textBillPhoneController.text = "";
    textBillPayOutNoController.text = "";
    textBillNumberController.text = "";
    skuId = "";
    unitId = "";
    fastSetDate(FastTimeType.last30Day);
    setState(() {});
  }

  Widget _filterItem(
    String title,
    TextEditingController textEditingController,
  ) {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      margin: EdgeInsets.only(top: 16.h),
      children: [
        Expanded(
          child: HaloContainer(
            direction: Axis.vertical,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              HaloPosLabel(
                title,
                textStyle: TextStyle(
                  fontSize: AppPosSize.secondaryTitleFontSize.sp,
                  color: AppColors.normalFontColor,
                ),
              ),
              HaloContainer(
                mainAxisSize: MainAxisSize.max,
                margin: EdgeInsets.only(top: 8.h),
                padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
                border: Border.all(color: AppColors.borderColor, width: 1.w),
                borderRadius: const BorderRadius.all(Radius.circular(4)),
                children: [
                  Expanded(
                    child: HaloTextField(
                      controller: textEditingController,
                      contentPadding: 0,
                      textColor: AppColors.normalFontColor,
                      fontSize: AppPosSize.secondaryTitleFontSize.sp,
                      hintStyle: const TextStyle(color: AppColors.hintColor),
                      maxLines: 1,
                      hintText: "请输入$title查询",
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  _filterRequest() {
    _globalKey.currentState?.filterRequest(
      textStartTimeController.text,
      textEndTimeController.text,
      textBillNumberController.text,
      textBillPhoneController.text,
      textBillPayOutNoController.text,
      skuId,
      unitId,
    );
  }
}
