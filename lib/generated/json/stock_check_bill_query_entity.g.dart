
import '../../stockcheck/entity/stock_check_bill_query_entity.dart';
import 'base/json_convert_content.dart';

StockCheckBillQueryEntity $StockCheckBillQueryEntityFromJson(
    Map<String, dynamic> json) {
  final StockCheckBillQueryEntity stockCheckBillQueryEntity = StockCheckBillQueryEntity();
  final List<String>? ptypeIds = (json['ptypeIds'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (ptypeIds != null) {
    stockCheckBillQueryEntity.ptypeIds = ptypeIds;
  }
  final List<String>? skuIds = (json['skuIds'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (skuIds != null) {
    stockCheckBillQueryEntity.skuIds = skuIds;
  }
  final String? ktypeId = jsonConvert.convert<String>(json['ktypeId']);
  if (ktypeId != null) {
    stockCheckBillQueryEntity.ktypeId = ktypeId;
  }
  final bool? checkBatchStock = jsonConvert.convert<bool>(
      json['checkBatchStock']);
  if (checkBatchStock != null) {
    stockCheckBillQueryEntity.checkBatchStock = checkBatchStock;
  }
  final bool? scanSnAndBarcode = jsonConvert.convert<bool>(
      json['scanSnAndBarcode']);
  if (scanSnAndBarcode != null) {
    stockCheckBillQueryEntity.scanSnAndBarcode = scanSnAndBarcode;
  }
  final bool? scanPtype = jsonConvert.convert<bool>(json['scanPtype']);
  if (scanPtype != null) {
    stockCheckBillQueryEntity.scanPtype = scanPtype;
  }
  final List<
      StockCheckBillQueryCheckPtypes>? checkPtypes = (json['checkPtypes'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<StockCheckBillQueryCheckPtypes>(
          e) as StockCheckBillQueryCheckPtypes).toList();
  if (checkPtypes != null) {
    stockCheckBillQueryEntity.checkPtypes = checkPtypes;
  }
  final String? snNo = jsonConvert.convert<String>(json['snNo']);
  if (snNo != null) {
    stockCheckBillQueryEntity.snNo = snNo;
  }
  return stockCheckBillQueryEntity;
}

Map<String, dynamic> $StockCheckBillQueryEntityToJson(
    StockCheckBillQueryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['ptypeIds'] = entity.ptypeIds;
  data['skuIds'] = entity.skuIds;
  data['ktypeId'] = entity.ktypeId;
  data['checkBatchStock'] = entity.checkBatchStock;
  data['scanSnAndBarcode'] = entity.scanSnAndBarcode;
  data['scanPtype'] = entity.scanPtype;
  data['checkPtypes'] = entity.checkPtypes?.map((v) => v.toJson()).toList();
  data['snNo'] = entity.snNo;
  return data;
}

extension StockCheckBillQueryEntityExtension on StockCheckBillQueryEntity {
  StockCheckBillQueryEntity copyWith({
    List<String>? ptypeIds,
    List<String>? skuIds,
    String? ktypeId,
    bool? checkBatchStock,
    bool? scanSnAndBarcode,
    bool? scanPtype,
    List<StockCheckBillQueryCheckPtypes>? checkPtypes,
    String? snNo,
  }) {
    return StockCheckBillQueryEntity()
      ..ptypeIds = ptypeIds ?? this.ptypeIds
      ..skuIds = skuIds ?? this.skuIds
      ..ktypeId = ktypeId ?? this.ktypeId
      ..checkBatchStock = checkBatchStock ?? this.checkBatchStock
      ..scanSnAndBarcode = scanSnAndBarcode ?? this.scanSnAndBarcode
      ..scanPtype = scanPtype ?? this.scanPtype
      ..checkPtypes = checkPtypes ?? this.checkPtypes
      ..snNo = snNo ?? this.snNo;
  }
}

StockCheckBillQueryCheckPtypes $StockCheckBillQueryCheckPtypesFromJson(
    Map<String, dynamic> json) {
  final StockCheckBillQueryCheckPtypes stockCheckBillQueryCheckPtypes = StockCheckBillQueryCheckPtypes();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    stockCheckBillQueryCheckPtypes.id = id;
  }
  final String? skuId = jsonConvert.convert<String>(json['skuId']);
  if (skuId != null) {
    stockCheckBillQueryCheckPtypes.skuId = skuId;
  }
  final String? propvalueName1 = jsonConvert.convert<String>(
      json['propvalueName1']);
  if (propvalueName1 != null) {
    stockCheckBillQueryCheckPtypes.propvalueName1 = propvalueName1;
  }
  final String? propvalueName2 = jsonConvert.convert<String>(
      json['propvalueName2']);
  if (propvalueName2 != null) {
    stockCheckBillQueryCheckPtypes.propvalueName2 = propvalueName2;
  }
  final String? propvalueName3 = jsonConvert.convert<String>(
      json['propvalueName3']);
  if (propvalueName3 != null) {
    stockCheckBillQueryCheckPtypes.propvalueName3 = propvalueName3;
  }
  final String? propvalueName4 = jsonConvert.convert<String>(
      json['propvalueName4']);
  if (propvalueName4 != null) {
    stockCheckBillQueryCheckPtypes.propvalueName4 = propvalueName4;
  }
  final String? propvalueName5 = jsonConvert.convert<String>(
      json['propvalueName5']);
  if (propvalueName5 != null) {
    stockCheckBillQueryCheckPtypes.propvalueName5 = propvalueName5;
  }
  final String? propvalueName6 = jsonConvert.convert<String>(
      json['propvalueName6']);
  if (propvalueName6 != null) {
    stockCheckBillQueryCheckPtypes.propvalueName6 = propvalueName6;
  }
  final bool? propenabled = jsonConvert.convert<bool>(json['propenabled']);
  if (propenabled != null) {
    stockCheckBillQueryCheckPtypes.propenabled = propenabled;
  }
  return stockCheckBillQueryCheckPtypes;
}

Map<String, dynamic> $StockCheckBillQueryCheckPtypesToJson(
    StockCheckBillQueryCheckPtypes entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['skuId'] = entity.skuId;
  data['propvalueName1'] = entity.propvalueName1;
  data['propvalueName2'] = entity.propvalueName2;
  data['propvalueName3'] = entity.propvalueName3;
  data['propvalueName4'] = entity.propvalueName4;
  data['propvalueName5'] = entity.propvalueName5;
  data['propvalueName6'] = entity.propvalueName6;
  data['propenabled'] = entity.propenabled;
  return data;
}

extension StockCheckBillQueryCheckPtypesExtension on StockCheckBillQueryCheckPtypes {
  StockCheckBillQueryCheckPtypes copyWith({
    String? id,
    String? skuId,
    String? propvalueName1,
    String? propvalueName2,
    String? propvalueName3,
    String? propvalueName4,
    String? propvalueName5,
    String? propvalueName6,
    bool? propenabled,
  }) {
    return StockCheckBillQueryCheckPtypes()
      ..id = id ?? this.id
      ..skuId = skuId ?? this.skuId
      ..propvalueName1 = propvalueName1 ?? this.propvalueName1
      ..propvalueName2 = propvalueName2 ?? this.propvalueName2
      ..propvalueName3 = propvalueName3 ?? this.propvalueName3
      ..propvalueName4 = propvalueName4 ?? this.propvalueName4
      ..propvalueName5 = propvalueName5 ?? this.propvalueName5
      ..propvalueName6 = propvalueName6 ?? this.propvalueName6
      ..propenabled = propenabled ?? this.propenabled;
  }
}