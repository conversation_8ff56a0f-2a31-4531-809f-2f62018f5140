

import '../../stockcheck/entity/stock_check_create_entity.dart';
import 'base/json_convert_content.dart';

StockCheckCreateEntity $StockCheckCreateEntityFromJson(
    Map<String, dynamic> json) {
  final StockCheckCreateEntity stockCheckCreateEntity = StockCheckCreateEntity();
  final dynamic message = json['message'];
  if (message != null) {
    stockCheckCreateEntity.message = message;
  }
  final String? checkId = jsonConvert.convert<String>(json['checkId']);
  if (checkId != null) {
    stockCheckCreateEntity.checkId = checkId;
  }
  final String? detailId = jsonConvert.convert<String>(json['detailId']);
  if (detailId != null) {
    stockCheckCreateEntity.detailId = detailId;
  }
  final dynamic repeatDetails = json['repeatDetails'];
  if (repeatDetails != null) {
    stockCheckCreateEntity.repeatDetails = repeatDetails;
  }
  return stockCheckCreateEntity;
}

Map<String, dynamic> $StockCheckCreateEntityToJson(
    StockCheckCreateEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['message'] = entity.message;
  data['checkId'] = entity.checkId;
  data['detailId'] = entity.detailId;
  data['repeatDetails'] = entity.repeatDetails;
  return data;
}

extension StockCheckCreateEntityExtension on StockCheckCreateEntity {
  StockCheckCreateEntity copyWith({
    dynamic message,
    String? checkId,
    String? detailId,
    dynamic repeatDetails,
  }) {
    return StockCheckCreateEntity()
      ..message = message ?? this.message
      ..checkId = checkId ?? this.checkId
      ..detailId = detailId ?? this.detailId
      ..repeatDetails = repeatDetails ?? this.repeatDetails;
  }
}