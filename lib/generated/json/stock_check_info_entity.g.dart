
import 'package:flutter/cupertino.dart';

import '../../stockcheck/entity/stock_check_info_entity.dart';
import 'base/json_convert_content.dart';


StockCheckInfoEntity $StockCheckInfoEntityFromJson(Map<String, dynamic> json) {
  final StockCheckInfoEntity stockCheckInfoEntity = StockCheckInfoEntity();
  final TextEditingController? textEditingController = jsonConvert.convert<
      TextEditingController>(json['textEditingController']);
  if (textEditingController != null) {
    stockCheckInfoEntity.textEditingController = textEditingController;
  }
  final int? index = jsonConvert.convert<int>(json['index']);
  if (index != null) {
    stockCheckInfoEntity.index = index;
  }
  final String? ptypeId = jsonConvert.convert<String>(json['ptypeId']);
  if (ptypeId != null) {
    stockCheckInfoEntity.ptypeId = ptypeId;
  }
  final dynamic picUrl = json['picUrl'];
  if (picUrl != null) {
    stockCheckInfoEntity.picUrl = picUrl;
  }
  final String? usercode = jsonConvert.convert<String>(json['usercode']);
  if (usercode != null) {
    stockCheckInfoEntity.usercode = usercode;
  }
  final dynamic pFullname = json['pFullname'];
  if (pFullname != null) {
    stockCheckInfoEntity.pFullname = pFullname;
  }
  final dynamic shortname = json['shortname'];
  if (shortname != null) {
    stockCheckInfoEntity.shortname = shortname;
  }
  final dynamic brandName = json['brandName'];
  if (brandName != null) {
    stockCheckInfoEntity.brandName = brandName;
  }
  final String? standard = jsonConvert.convert<String>(json['standard']);
  if (standard != null) {
    stockCheckInfoEntity.standard = standard;
  }
  final String? ptypeType = jsonConvert.convert<String>(json['ptypeType']);
  if (ptypeType != null) {
    stockCheckInfoEntity.ptypeType = ptypeType;
  }
  final String? unitName = jsonConvert.convert<String>(json['unitName']);
  if (unitName != null) {
    stockCheckInfoEntity.unitName = unitName;
  }
  final String? subUnitName = jsonConvert.convert<String>(json['subUnitName']);
  if (subUnitName != null) {
    stockCheckInfoEntity.subUnitName = subUnitName;
  }
  final dynamic ptypeMemo = json['ptypeMemo'];
  if (ptypeMemo != null) {
    stockCheckInfoEntity.ptypeMemo = ptypeMemo;
  }
  final dynamic fullbarcode = json['fullbarcode'];
  if (fullbarcode != null) {
    stockCheckInfoEntity.fullbarcode = fullbarcode;
  }
  final String? skuId = jsonConvert.convert<String>(json['skuId']);
  if (skuId != null) {
    stockCheckInfoEntity.skuId = skuId;
  }
  final dynamic xcode = json['xcode'];
  if (xcode != null) {
    stockCheckInfoEntity.xcode = xcode;
  }
  final dynamic taxRate = json['taxRate'];
  if (taxRate != null) {
    stockCheckInfoEntity.taxRate = taxRate;
  }
  final int? skuPrice = jsonConvert.convert<int>(json['skuPrice']);
  if (skuPrice != null) {
    stockCheckInfoEntity.skuPrice = skuPrice;
  }
  final dynamic pcategory = json['pcategory'];
  if (pcategory != null) {
    stockCheckInfoEntity.pcategory = pcategory;
  }
  final int? industryCategory = jsonConvert.convert<int>(
      json['industryCategory']);
  if (industryCategory != null) {
    stockCheckInfoEntity.industryCategory = industryCategory;
  }
  final dynamic parName1 = json['parName1'];
  if (parName1 != null) {
    stockCheckInfoEntity.parName1 = parName1;
  }
  final dynamic parName2 = json['parName2'];
  if (parName2 != null) {
    stockCheckInfoEntity.parName2 = parName2;
  }
  final dynamic parName3 = json['parName3'];
  if (parName3 != null) {
    stockCheckInfoEntity.parName3 = parName3;
  }
  final dynamic parName4 = json['parName4'];
  if (parName4 != null) {
    stockCheckInfoEntity.parName4 = parName4;
  }
  final dynamic parName5 = json['parName5'];
  if (parName5 != null) {
    stockCheckInfoEntity.parName5 = parName5;
  }
  final dynamic ptypeWeight = json['ptypeWeight'];
  if (ptypeWeight != null) {
    stockCheckInfoEntity.ptypeWeight = ptypeWeight;
  }
  final dynamic unitRelation = json['unitRelation'];
  if (unitRelation != null) {
    stockCheckInfoEntity.unitRelation = unitRelation;
  }
  final dynamic picUrls = json['picUrls'];
  if (picUrls != null) {
    stockCheckInfoEntity.picUrls = picUrls;
  }
  final int? skuName = jsonConvert.convert<int>(json['skuName']);
  if (skuName != null) {
    stockCheckInfoEntity.skuName = skuName;
  }
  final String? propNames = jsonConvert.convert<String>(json['propNames']);
  if (propNames != null) {
    stockCheckInfoEntity.propNames = propNames;
  }
  final String? propValues = jsonConvert.convert<String>(json['propValues']);
  if (propValues != null) {
    stockCheckInfoEntity.propValues = propValues;
  }
  final dynamic propValueName1 = json['propValueName1'];
  if (propValueName1 != null) {
    stockCheckInfoEntity.propValueName1 = propValueName1;
  }
  final dynamic propValueName2 = json['propValueName2'];
  if (propValueName2 != null) {
    stockCheckInfoEntity.propValueName2 = propValueName2;
  }
  final String? batchno = jsonConvert.convert<String>(json['batchno']);
  if (batchno != null) {
    stockCheckInfoEntity.batchno = batchno;
  }
  final String? produceDate = jsonConvert.convert<String>(json['produceDate']);
  if (produceDate != null) {
    stockCheckInfoEntity.produceDate = produceDate;
  }
  final String? expireDate = jsonConvert.convert<String>(json['expireDate']);
  if (expireDate != null) {
    stockCheckInfoEntity.expireDate = expireDate;
  }
  final dynamic defProduceDate = json['defProduceDate'];
  if (defProduceDate != null) {
    stockCheckInfoEntity.defProduceDate = defProduceDate;
  }
  final dynamic defExpireDate = json['defExpireDate'];
  if (defExpireDate != null) {
    stockCheckInfoEntity.defExpireDate = defExpireDate;
  }
  final dynamic customHead01 = json['customHead01'];
  if (customHead01 != null) {
    stockCheckInfoEntity.customHead01 = customHead01;
  }
  final dynamic customHead02 = json['customHead02'];
  if (customHead02 != null) {
    stockCheckInfoEntity.customHead02 = customHead02;
  }
  final dynamic customHead03 = json['customHead03'];
  if (customHead03 != null) {
    stockCheckInfoEntity.customHead03 = customHead03;
  }
  final dynamic customHead04 = json['customHead04'];
  if (customHead04 != null) {
    stockCheckInfoEntity.customHead04 = customHead04;
  }
  final dynamic customHead05 = json['customHead05'];
  if (customHead05 != null) {
    stockCheckInfoEntity.customHead05 = customHead05;
  }
  final dynamic customHead06 = json['customHead06'];
  if (customHead06 != null) {
    stockCheckInfoEntity.customHead06 = customHead06;
  }
  final dynamic customHead07 = json['customHead07'];
  if (customHead07 != null) {
    stockCheckInfoEntity.customHead07 = customHead07;
  }
  final dynamic customHead08 = json['customHead08'];
  if (customHead08 != null) {
    stockCheckInfoEntity.customHead08 = customHead08;
  }
  final dynamic customHead09 = json['customHead09'];
  if (customHead09 != null) {
    stockCheckInfoEntity.customHead09 = customHead09;
  }
  final dynamic customHead10 = json['customHead10'];
  if (customHead10 != null) {
    stockCheckInfoEntity.customHead10 = customHead10;
  }
  final dynamic customHead11 = json['customHead11'];
  if (customHead11 != null) {
    stockCheckInfoEntity.customHead11 = customHead11;
  }
  final dynamic customHead12 = json['customHead12'];
  if (customHead12 != null) {
    stockCheckInfoEntity.customHead12 = customHead12;
  }
  final dynamic customHead13 = json['customHead13'];
  if (customHead13 != null) {
    stockCheckInfoEntity.customHead13 = customHead13;
  }
  final dynamic customHead14 = json['customHead14'];
  if (customHead14 != null) {
    stockCheckInfoEntity.customHead14 = customHead14;
  }
  final dynamic subUnit = json['subUnit'];
  if (subUnit != null) {
    stockCheckInfoEntity.subUnit = subUnit;
  }
  final dynamic subQty = json['subQty'];
  if (subQty != null) {
    stockCheckInfoEntity.subQty = subQty;
  }
  final dynamic subInQty = json['subInQty'];
  if (subInQty != null) {
    stockCheckInfoEntity.subInQty = subInQty;
  }
  final dynamic subOutQty = json['subOutQty'];
  if (subOutQty != null) {
    stockCheckInfoEntity.subOutQty = subOutQty;
  }
  final dynamic subInOutQty = json['subInOutQty'];
  if (subInOutQty != null) {
    stockCheckInfoEntity.subInOutQty = subInOutQty;
  }
  final dynamic subInStockQty = json['subInStockQty'];
  if (subInStockQty != null) {
    stockCheckInfoEntity.subInStockQty = subInStockQty;
  }
  final dynamic subInOutPercent = json['subInOutPercent'];
  if (subInOutPercent != null) {
    stockCheckInfoEntity.subInOutPercent = subInOutPercent;
  }
  final dynamic subDiffQty = json['subDiffQty'];
  if (subDiffQty != null) {
    stockCheckInfoEntity.subDiffQty = subDiffQty;
  }
  final bool? hasPerm = jsonConvert.convert<bool>(json['hasPerm']);
  if (hasPerm != null) {
    stockCheckInfoEntity.hasPerm = hasPerm;
  }
  final dynamic baseUnitName = json['baseUnitName'];
  if (baseUnitName != null) {
    stockCheckInfoEntity.baseUnitName = baseUnitName;
  }
  final String? unitxqty = jsonConvert.convert<String>(json['unitxqty']);
  if (unitxqty != null) {
    stockCheckInfoEntity.unitxqty = unitxqty;
  }
  final dynamic ptypeUnits = json['ptypeUnits'];
  if (ptypeUnits != null) {
    stockCheckInfoEntity.ptypeUnits = ptypeUnits;
  }
  final List<StockCheckInfoMySnList>? mySnList = (json['mySnList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<StockCheckInfoMySnList>(e) as StockCheckInfoMySnList)
      .toList();
  if (mySnList != null) {
    stockCheckInfoEntity.mySnList = mySnList;
  }
  final List<
      StockCheckInfoMySnList>? checkSnList = (json['checkSnList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<StockCheckInfoMySnList>(e) as StockCheckInfoMySnList)
      .toList();
  if (checkSnList != null) {
    stockCheckInfoEntity.checkSnList = checkSnList;
  }
  final String? snnoStrButton = jsonConvert.convert<String>(
      json['snnoStrButton']);
  if (snnoStrButton != null) {
    stockCheckInfoEntity.snnoStrButton = snnoStrButton;
  }
  final int? unitCode = jsonConvert.convert<int>(json['unitCode']);
  if (unitCode != null) {
    stockCheckInfoEntity.unitCode = unitCode;
  }
  final dynamic unitRate = json['unitRate'];
  if (unitRate != null) {
    stockCheckInfoEntity.unitRate = unitRate;
  }
  final String? unitId = jsonConvert.convert<String>(json['unitId']);
  if (unitId != null) {
    stockCheckInfoEntity.unitId = unitId;
  }
  final String? detailId = jsonConvert.convert<String>(json['detailId']);
  if (detailId != null) {
    stockCheckInfoEntity.detailId = detailId;
  }
  final String? stockCheckId = jsonConvert.convert<String>(
      json['stockCheckId']);
  if (stockCheckId != null) {
    stockCheckInfoEntity.stockCheckId = stockCheckId;
  }
  final int? batchPrice = jsonConvert.convert<int>(json['batchPrice']);
  if (batchPrice != null) {
    stockCheckInfoEntity.batchPrice = batchPrice;
  }
  final double? batchQty = jsonConvert.convert<double>(json['batchQty']);
  if (batchQty != null) {
    stockCheckInfoEntity.batchQty = batchQty;
  }
  final double? batchLifeQty = jsonConvert.convert<double>(
      json['batchLifeQty']);
  if (batchLifeQty != null) {
    stockCheckInfoEntity.batchLifeQty = batchLifeQty;
  }
  final int? stockPrice = jsonConvert.convert<int>(json['stockPrice']);
  if (stockPrice != null) {
    stockCheckInfoEntity.stockPrice = stockPrice;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    stockCheckInfoEntity.id = id;
  }
  final dynamic profileId = json['profileId'];
  if (profileId != null) {
    stockCheckInfoEntity.profileId = profileId;
  }
  final String? fullname = jsonConvert.convert<String>(json['fullname']);
  if (fullname != null) {
    stockCheckInfoEntity.fullname = fullname;
  }
  final dynamic shortName = json['shortName'];
  if (shortName != null) {
    stockCheckInfoEntity.shortName = shortName;
  }
  final dynamic namePy = json['namePy'];
  if (namePy != null) {
    stockCheckInfoEntity.namePy = namePy;
  }
  final dynamic classed = json['classed'];
  if (classed != null) {
    stockCheckInfoEntity.classed = classed;
  }
  final dynamic stoped = json['stoped'];
  if (stoped != null) {
    stockCheckInfoEntity.stoped = stoped;
  }
  final dynamic deleted = json['deleted'];
  if (deleted != null) {
    stockCheckInfoEntity.deleted = deleted;
  }
  final dynamic rowindex = json['rowindex'];
  if (rowindex != null) {
    stockCheckInfoEntity.rowindex = rowindex;
  }
  final String? barcode = jsonConvert.convert<String>(json['barcode']);
  if (barcode != null) {
    stockCheckInfoEntity.barcode = barcode;
  }
  final String? ptypeArea = jsonConvert.convert<String>(json['ptypeArea']);
  if (ptypeArea != null) {
    stockCheckInfoEntity.ptypeArea = ptypeArea;
  }
  final dynamic memo = json['memo'];
  if (memo != null) {
    stockCheckInfoEntity.memo = memo;
  }
  final dynamic createType = json['createType'];
  if (createType != null) {
    stockCheckInfoEntity.createType = createType;
  }
  final int? costMode = jsonConvert.convert<int>(json['costMode']);
  if (costMode != null) {
    stockCheckInfoEntity.costMode = costMode;
  }
  final dynamic taxNumber = json['taxNumber'];
  if (taxNumber != null) {
    stockCheckInfoEntity.taxNumber = taxNumber;
  }
  final int? costPrice = jsonConvert.convert<int>(json['costPrice']);
  if (costPrice != null) {
    stockCheckInfoEntity.costPrice = costPrice;
  }
  final dynamic supplyInfo = json['supplyInfo'];
  if (supplyInfo != null) {
    stockCheckInfoEntity.supplyInfo = supplyInfo;
  }
  final dynamic brandId = json['brandId'];
  if (brandId != null) {
    stockCheckInfoEntity.brandId = brandId;
  }
  final dynamic ktypeLimit = json['ktypeLimit'];
  if (ktypeLimit != null) {
    stockCheckInfoEntity.ktypeLimit = ktypeLimit;
  }
  final int? snenabled = jsonConvert.convert<int>(json['snenabled']);
  if (snenabled != null) {
    stockCheckInfoEntity.snenabled = snenabled;
  }
  final int? snEnabled = jsonConvert.convert<int>(json['snEnabled']);
  if (snEnabled != null) {
    stockCheckInfoEntity.snEnabled = snEnabled;
  }
  final bool? propenabled = jsonConvert.convert<bool>(json['propenabled']);
  if (propenabled != null) {
    stockCheckInfoEntity.propenabled = propenabled;
  }
  final bool? batchenabled = jsonConvert.convert<bool>(json['batchenabled']);
  if (batchenabled != null) {
    stockCheckInfoEntity.batchenabled = batchenabled;
  }
  final int? protectDays = jsonConvert.convert<int>(json['protectDays']);
  if (protectDays != null) {
    stockCheckInfoEntity.protectDays = protectDays;
  }
  final int? protectDaysUnit = jsonConvert.convert<int>(
      json['protectDaysUnit']);
  if (protectDaysUnit != null) {
    stockCheckInfoEntity.protectDaysUnit = protectDaysUnit;
  }
  final dynamic protectWarndays = json['protectWarndays'];
  if (protectWarndays != null) {
    stockCheckInfoEntity.protectWarndays = protectWarndays;
  }
  final dynamic weight = json['weight'];
  if (weight != null) {
    stockCheckInfoEntity.weight = weight;
  }
  final dynamic weightUnit = json['weightUnit'];
  if (weightUnit != null) {
    stockCheckInfoEntity.weightUnit = weightUnit;
  }
  final dynamic lithiumBattery = json['lithiumBattery'];
  if (lithiumBattery != null) {
    stockCheckInfoEntity.lithiumBattery = lithiumBattery;
  }
  final dynamic solid = json['solid'];
  if (solid != null) {
    stockCheckInfoEntity.solid = solid;
  }
  final dynamic difficultyLevel = json['difficultyLevel'];
  if (difficultyLevel != null) {
    stockCheckInfoEntity.difficultyLevel = difficultyLevel;
  }
  final dynamic weighted = json['weighted'];
  if (weighted != null) {
    stockCheckInfoEntity.weighted = weighted;
  }
  final dynamic retailDefaultUnit = json['retailDefaultUnit'];
  if (retailDefaultUnit != null) {
    stockCheckInfoEntity.retailDefaultUnit = retailDefaultUnit;
  }
  final dynamic saleDefaultUnit = json['saleDefaultUnit'];
  if (saleDefaultUnit != null) {
    stockCheckInfoEntity.saleDefaultUnit = saleDefaultUnit;
  }
  final dynamic purchaseDefaultUnit = json['purchaseDefaultUnit'];
  if (purchaseDefaultUnit != null) {
    stockCheckInfoEntity.purchaseDefaultUnit = purchaseDefaultUnit;
  }
  final dynamic stockDefaultUnit = json['stockDefaultUnit'];
  if (stockDefaultUnit != null) {
    stockCheckInfoEntity.stockDefaultUnit = stockDefaultUnit;
  }
  final dynamic ptypeLength = json['ptypeLength'];
  if (ptypeLength != null) {
    stockCheckInfoEntity.ptypeLength = ptypeLength;
  }
  final dynamic ptypeWidth = json['ptypeWidth'];
  if (ptypeWidth != null) {
    stockCheckInfoEntity.ptypeWidth = ptypeWidth;
  }
  final dynamic ptypeHeight = json['ptypeHeight'];
  if (ptypeHeight != null) {
    stockCheckInfoEntity.ptypeHeight = ptypeHeight;
  }
  final dynamic lengthUnit = json['lengthUnit'];
  if (lengthUnit != null) {
    stockCheckInfoEntity.lengthUnit = lengthUnit;
  }
  final dynamic createTime = json['createTime'];
  if (createTime != null) {
    stockCheckInfoEntity.createTime = createTime;
  }
  final dynamic updateTime = json['updateTime'];
  if (updateTime != null) {
    stockCheckInfoEntity.updateTime = updateTime;
  }
  final String? batchlifeId = jsonConvert.convert<String>(json['batchlifeId']);
  if (batchlifeId != null) {
    stockCheckInfoEntity.batchlifeId = batchlifeId;
  }
  final double? stockQty = jsonConvert.convert<double>(json['stockQty']);
  if (stockQty != null) {
    stockCheckInfoEntity.stockQty = stockQty;
  }
  final int? stockSubQty = jsonConvert.convert<int>(json['stockSubQty']);
  if (stockSubQty != null) {
    stockCheckInfoEntity.stockSubQty = stockSubQty;
  }
  final double? checkQty = jsonConvert.convert<double>(json['checkQty']);
  if (checkQty != null) {
    stockCheckInfoEntity.checkQty = checkQty;
  }
  final dynamic checkSubQty = json['checkSubQty'];
  if (checkSubQty != null) {
    stockCheckInfoEntity.checkSubQty = checkSubQty;
  }
  final dynamic adjustQty = json['adjustQty'];
  if (adjustQty != null) {
    stockCheckInfoEntity.adjustQty = adjustQty;
  }
  final dynamic adjustSubQty = json['adjustSubQty'];
  if (adjustSubQty != null) {
    stockCheckInfoEntity.adjustSubQty = adjustSubQty;
  }
  final String? checkTypeStr = jsonConvert.convert<String>(
      json['checkTypeStr']);
  if (checkTypeStr != null) {
    stockCheckInfoEntity.checkTypeStr = checkTypeStr;
  }
  final String? checkTime = jsonConvert.convert<String>(json['checkTime']);
  if (checkTime != null) {
    stockCheckInfoEntity.checkTime = checkTime;
  }
  final bool? checked = jsonConvert.convert<bool>(json['checked']);
  if (checked != null) {
    stockCheckInfoEntity.checked = checked;
  }
  final bool? subChecked = jsonConvert.convert<bool>(json['subChecked']);
  if (subChecked != null) {
    stockCheckInfoEntity.subChecked = subChecked;
  }
  final String? propvalueName1 = jsonConvert.convert<String>(
      json['propvalueName1']);
  if (propvalueName1 != null) {
    stockCheckInfoEntity.propvalueName1 = propvalueName1;
  }
  final String? propvalueName2 = jsonConvert.convert<String>(
      json['propvalueName2']);
  if (propvalueName2 != null) {
    stockCheckInfoEntity.propvalueName2 = propvalueName2;
  }
  final String? propvalueName3 = jsonConvert.convert<String>(
      json['propvalueName3']);
  if (propvalueName3 != null) {
    stockCheckInfoEntity.propvalueName3 = propvalueName3;
  }
  final String? propvalueName4 = jsonConvert.convert<String>(
      json['propvalueName4']);
  if (propvalueName4 != null) {
    stockCheckInfoEntity.propvalueName4 = propvalueName4;
  }
  final String? propvalueName5 = jsonConvert.convert<String>(
      json['propvalueName5']);
  if (propvalueName5 != null) {
    stockCheckInfoEntity.propvalueName5 = propvalueName5;
  }
  final String? propvalueName6 = jsonConvert.convert<String>(
      json['propvalueName6']);
  if (propvalueName6 != null) {
    stockCheckInfoEntity.propvalueName6 = propvalueName6;
  }
  final String? propvalueAll = jsonConvert.convert<String>(
      json['propvalueAll']);
  if (propvalueAll != null) {
    stockCheckInfoEntity.propvalueAll = propvalueAll;
  }
  final String? proDateStr = jsonConvert.convert<String>(json['proDateStr']);
  if (proDateStr != null) {
    stockCheckInfoEntity.proDateStr = proDateStr;
  }
  final String? expDateStr = jsonConvert.convert<String>(json['expDateStr']);
  if (expDateStr != null) {
    stockCheckInfoEntity.expDateStr = expDateStr;
  }
  final bool? selectchecked = jsonConvert.convert<bool>(json['selectchecked']);
  if (selectchecked != null) {
    stockCheckInfoEntity.selectchecked = selectchecked;
  }
  final dynamic snno = json['snno'];
  if (snno != null) {
    stockCheckInfoEntity.snno = snno;
  }
  final String? costId = jsonConvert.convert<String>(json['costId']);
  if (costId != null) {
    stockCheckInfoEntity.costId = costId;
  }
  final dynamic positionStrText = json['positionStrText'];
  if (positionStrText != null) {
    stockCheckInfoEntity.positionStrText = positionStrText;
  }
  final dynamic positionList = json['positionList'];
  if (positionList != null) {
    stockCheckInfoEntity.positionList = positionList;
  }
  final dynamic inoutId = json['inoutId'];
  if (inoutId != null) {
    stockCheckInfoEntity.inoutId = inoutId;
  }
  return stockCheckInfoEntity;
}

Map<String, dynamic> $StockCheckInfoEntityToJson(StockCheckInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  // data['textEditingController'] = entity.textEditingController.toJson();
  data['index'] = entity.index;
  data['ptypeId'] = entity.ptypeId;
  data['picUrl'] = entity.picUrl;
  data['usercode'] = entity.usercode;
  data['pFullname'] = entity.pFullname;
  data['shortname'] = entity.shortname;
  data['brandName'] = entity.brandName;
  data['standard'] = entity.standard;
  data['ptypeType'] = entity.ptypeType;
  data['unitName'] = entity.unitName;
  data['subUnitName'] = entity.subUnitName;
  data['ptypeMemo'] = entity.ptypeMemo;
  data['fullbarcode'] = entity.fullbarcode;
  data['skuId'] = entity.skuId;
  data['xcode'] = entity.xcode;
  data['taxRate'] = entity.taxRate;
  data['skuPrice'] = entity.skuPrice;
  data['pcategory'] = entity.pcategory;
  data['industryCategory'] = entity.industryCategory;
  data['parName1'] = entity.parName1;
  data['parName2'] = entity.parName2;
  data['parName3'] = entity.parName3;
  data['parName4'] = entity.parName4;
  data['parName5'] = entity.parName5;
  data['ptypeWeight'] = entity.ptypeWeight;
  data['unitRelation'] = entity.unitRelation;
  data['picUrls'] = entity.picUrls;
  data['skuName'] = entity.skuName;
  data['propNames'] = entity.propNames;
  data['propValues'] = entity.propValues;
  data['propValueName1'] = entity.propValueName1;
  data['propValueName2'] = entity.propValueName2;
  data['batchno'] = entity.batchno;
  data['produceDate'] = entity.produceDate;
  data['expireDate'] = entity.expireDate;
  data['defProduceDate'] = entity.defProduceDate;
  data['defExpireDate'] = entity.defExpireDate;
  data['customHead01'] = entity.customHead01;
  data['customHead02'] = entity.customHead02;
  data['customHead03'] = entity.customHead03;
  data['customHead04'] = entity.customHead04;
  data['customHead05'] = entity.customHead05;
  data['customHead06'] = entity.customHead06;
  data['customHead07'] = entity.customHead07;
  data['customHead08'] = entity.customHead08;
  data['customHead09'] = entity.customHead09;
  data['customHead10'] = entity.customHead10;
  data['customHead11'] = entity.customHead11;
  data['customHead12'] = entity.customHead12;
  data['customHead13'] = entity.customHead13;
  data['customHead14'] = entity.customHead14;
  data['subUnit'] = entity.subUnit;
  data['subQty'] = entity.subQty;
  data['subInQty'] = entity.subInQty;
  data['subOutQty'] = entity.subOutQty;
  data['subInOutQty'] = entity.subInOutQty;
  data['subInStockQty'] = entity.subInStockQty;
  data['subInOutPercent'] = entity.subInOutPercent;
  data['subDiffQty'] = entity.subDiffQty;
  data['hasPerm'] = entity.hasPerm;
  data['baseUnitName'] = entity.baseUnitName;
  data['unitxqty'] = entity.unitxqty;
  data['ptypeUnits'] = entity.ptypeUnits;
  data['mySnList'] = entity.mySnList?.map((v) => v.toJson()).toList();
  data['checkSnList'] = entity.checkSnList?.map((v) => v.toJson()).toList();
  data['snnoStrButton'] = entity.snnoStrButton;
  data['unitCode'] = entity.unitCode;
  data['unitRate'] = entity.unitRate;
  data['unitId'] = entity.unitId;
  data['detailId'] = entity.detailId;
  data['stockCheckId'] = entity.stockCheckId;
  data['batchPrice'] = entity.batchPrice;
  data['batchQty'] = entity.batchQty;
  data['batchLifeQty'] = entity.batchLifeQty;
  data['stockPrice'] = entity.stockPrice;
  data['id'] = entity.id;
  data['profileId'] = entity.profileId;
  data['fullname'] = entity.fullname;
  data['shortName'] = entity.shortName;
  data['namePy'] = entity.namePy;
  data['classed'] = entity.classed;
  data['stoped'] = entity.stoped;
  data['deleted'] = entity.deleted;
  data['rowindex'] = entity.rowindex;
  data['barcode'] = entity.barcode;
  data['ptypeArea'] = entity.ptypeArea;
  data['memo'] = entity.memo;
  data['createType'] = entity.createType;
  data['costMode'] = entity.costMode;
  data['taxNumber'] = entity.taxNumber;
  data['costPrice'] = entity.costPrice;
  data['supplyInfo'] = entity.supplyInfo;
  data['brandId'] = entity.brandId;
  data['ktypeLimit'] = entity.ktypeLimit;
  data['snenabled'] = entity.snenabled;
  data['snEnabled'] = entity.snEnabled;
  data['propenabled'] = entity.propenabled;
  data['batchenabled'] = entity.batchenabled;
  data['protectDays'] = entity.protectDays;
  data['protectDaysUnit'] = entity.protectDaysUnit;
  data['protectWarndays'] = entity.protectWarndays;
  data['weight'] = entity.weight;
  data['weightUnit'] = entity.weightUnit;
  data['lithiumBattery'] = entity.lithiumBattery;
  data['solid'] = entity.solid;
  data['difficultyLevel'] = entity.difficultyLevel;
  data['weighted'] = entity.weighted;
  data['retailDefaultUnit'] = entity.retailDefaultUnit;
  data['saleDefaultUnit'] = entity.saleDefaultUnit;
  data['purchaseDefaultUnit'] = entity.purchaseDefaultUnit;
  data['stockDefaultUnit'] = entity.stockDefaultUnit;
  data['ptypeLength'] = entity.ptypeLength;
  data['ptypeWidth'] = entity.ptypeWidth;
  data['ptypeHeight'] = entity.ptypeHeight;
  data['lengthUnit'] = entity.lengthUnit;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['batchlifeId'] = entity.batchlifeId;
  data['stockQty'] = entity.stockQty;
  data['stockSubQty'] = entity.stockSubQty;
  data['checkQty'] = entity.checkQty;
  data['checkSubQty'] = entity.checkSubQty;
  data['adjustQty'] = entity.adjustQty;
  data['adjustSubQty'] = entity.adjustSubQty;
  data['checkTypeStr'] = entity.checkTypeStr;
  data['checkTime'] = entity.checkTime;
  data['checked'] = entity.checked;
  data['subChecked'] = entity.subChecked;
  data['propvalueName1'] = entity.propvalueName1;
  data['propvalueName2'] = entity.propvalueName2;
  data['propvalueName3'] = entity.propvalueName3;
  data['propvalueName4'] = entity.propvalueName4;
  data['propvalueName5'] = entity.propvalueName5;
  data['propvalueName6'] = entity.propvalueName6;
  data['propvalueAll'] = entity.propvalueAll;
  data['proDateStr'] = entity.proDateStr;
  data['expDateStr'] = entity.expDateStr;
  data['selectchecked'] = entity.selectchecked;
  data['snno'] = entity.snno;
  data['costId'] = entity.costId;
  data['positionStrText'] = entity.positionStrText;
  data['positionList'] = entity.positionList;
  data['inoutId'] = entity.inoutId;
  return data;
}

extension StockCheckInfoEntityExtension on StockCheckInfoEntity {
  StockCheckInfoEntity copyWith({
    TextEditingController? textEditingController,
    int? index,
    String? ptypeId,
    dynamic picUrl,
    String? usercode,
    dynamic pFullname,
    dynamic shortname,
    dynamic brandName,
    String? standard,
    String? ptypeType,
    String? unitName,
    String? subUnitName,
    dynamic ptypeMemo,
    dynamic fullbarcode,
    String? skuId,
    dynamic xcode,
    dynamic taxRate,
    int? skuPrice,
    dynamic pcategory,
    int? industryCategory,
    dynamic parName1,
    dynamic parName2,
    dynamic parName3,
    dynamic parName4,
    dynamic parName5,
    dynamic ptypeWeight,
    dynamic unitRelation,
    dynamic picUrls,
    int? skuName,
    String? propNames,
    String? propValues,
    dynamic propValueName1,
    dynamic propValueName2,
    String? batchno,
    String? produceDate,
    String? expireDate,
    dynamic defProduceDate,
    dynamic defExpireDate,
    dynamic customHead01,
    dynamic customHead02,
    dynamic customHead03,
    dynamic customHead04,
    dynamic customHead05,
    dynamic customHead06,
    dynamic customHead07,
    dynamic customHead08,
    dynamic customHead09,
    dynamic customHead10,
    dynamic customHead11,
    dynamic customHead12,
    dynamic customHead13,
    dynamic customHead14,
    dynamic subUnit,
    dynamic subQty,
    dynamic subInQty,
    dynamic subOutQty,
    dynamic subInOutQty,
    dynamic subInStockQty,
    dynamic subInOutPercent,
    dynamic subDiffQty,
    bool? hasPerm,
    dynamic baseUnitName,
    String? unitxqty,
    dynamic ptypeUnits,
    List<StockCheckInfoMySnList>? mySnList,
    List<StockCheckInfoMySnList>? checkSnList,
    String? snnoStrButton,
    int? unitCode,
    dynamic unitRate,
    String? unitId,
    String? detailId,
    String? stockCheckId,
    int? batchPrice,
    double? batchQty,
    double? batchLifeQty,
    int? stockPrice,
    String? id,
    dynamic profileId,
    String? fullname,
    dynamic shortName,
    dynamic namePy,
    dynamic classed,
    dynamic stoped,
    dynamic deleted,
    dynamic rowindex,
    String? barcode,
    String? ptypeArea,
    dynamic memo,
    dynamic createType,
    int? costMode,
    dynamic taxNumber,
    int? costPrice,
    dynamic supplyInfo,
    dynamic brandId,
    dynamic ktypeLimit,
    int? snenabled,
    int? snEnabled,
    bool? propenabled,
    bool? batchenabled,
    int? protectDays,
    int? protectDaysUnit,
    dynamic protectWarndays,
    dynamic weight,
    dynamic weightUnit,
    dynamic lithiumBattery,
    dynamic solid,
    dynamic difficultyLevel,
    dynamic weighted,
    dynamic retailDefaultUnit,
    dynamic saleDefaultUnit,
    dynamic purchaseDefaultUnit,
    dynamic stockDefaultUnit,
    dynamic ptypeLength,
    dynamic ptypeWidth,
    dynamic ptypeHeight,
    dynamic lengthUnit,
    dynamic createTime,
    dynamic updateTime,
    String? batchlifeId,
    double? stockQty,
    int? stockSubQty,
    double? checkQty,
    dynamic checkSubQty,
    dynamic adjustQty,
    dynamic adjustSubQty,
    String? checkTypeStr,
    String? checkTime,
    bool? checked,
    bool? subChecked,
    String? propvalueName1,
    String? propvalueName2,
    String? propvalueName3,
    String? propvalueName4,
    String? propvalueName5,
    String? propvalueName6,
    String? propvalueAll,
    String? proDateStr,
    String? expDateStr,
    bool? selectchecked,
    dynamic snno,
    String? costId,
    dynamic positionStrText,
    dynamic positionList,
    dynamic inoutId,
  }) {
    return StockCheckInfoEntity()
      ..textEditingController = textEditingController ??
          this.textEditingController
      ..index = index ?? this.index
      ..ptypeId = ptypeId ?? this.ptypeId
      ..picUrl = picUrl ?? this.picUrl
      ..usercode = usercode ?? this.usercode
      ..pFullname = pFullname ?? this.pFullname
      ..shortname = shortname ?? this.shortname
      ..brandName = brandName ?? this.brandName
      ..standard = standard ?? this.standard
      ..ptypeType = ptypeType ?? this.ptypeType
      ..unitName = unitName ?? this.unitName
      ..subUnitName = subUnitName ?? this.subUnitName
      ..ptypeMemo = ptypeMemo ?? this.ptypeMemo
      ..fullbarcode = fullbarcode ?? this.fullbarcode
      ..skuId = skuId ?? this.skuId
      ..xcode = xcode ?? this.xcode
      ..taxRate = taxRate ?? this.taxRate
      ..skuPrice = skuPrice ?? this.skuPrice
      ..pcategory = pcategory ?? this.pcategory
      ..industryCategory = industryCategory ?? this.industryCategory
      ..parName1 = parName1 ?? this.parName1
      ..parName2 = parName2 ?? this.parName2
      ..parName3 = parName3 ?? this.parName3
      ..parName4 = parName4 ?? this.parName4
      ..parName5 = parName5 ?? this.parName5
      ..ptypeWeight = ptypeWeight ?? this.ptypeWeight
      ..unitRelation = unitRelation ?? this.unitRelation
      ..picUrls = picUrls ?? this.picUrls
      ..skuName = skuName ?? this.skuName
      ..propNames = propNames ?? this.propNames
      ..propValues = propValues ?? this.propValues
      ..propValueName1 = propValueName1 ?? this.propValueName1
      ..propValueName2 = propValueName2 ?? this.propValueName2
      ..batchno = batchno ?? this.batchno
      ..produceDate = produceDate ?? this.produceDate
      ..expireDate = expireDate ?? this.expireDate
      ..defProduceDate = defProduceDate ?? this.defProduceDate
      ..defExpireDate = defExpireDate ?? this.defExpireDate
      ..customHead01 = customHead01 ?? this.customHead01
      ..customHead02 = customHead02 ?? this.customHead02
      ..customHead03 = customHead03 ?? this.customHead03
      ..customHead04 = customHead04 ?? this.customHead04
      ..customHead05 = customHead05 ?? this.customHead05
      ..customHead06 = customHead06 ?? this.customHead06
      ..customHead07 = customHead07 ?? this.customHead07
      ..customHead08 = customHead08 ?? this.customHead08
      ..customHead09 = customHead09 ?? this.customHead09
      ..customHead10 = customHead10 ?? this.customHead10
      ..customHead11 = customHead11 ?? this.customHead11
      ..customHead12 = customHead12 ?? this.customHead12
      ..customHead13 = customHead13 ?? this.customHead13
      ..customHead14 = customHead14 ?? this.customHead14
      ..subUnit = subUnit ?? this.subUnit
      ..subQty = subQty ?? this.subQty
      ..subInQty = subInQty ?? this.subInQty
      ..subOutQty = subOutQty ?? this.subOutQty
      ..subInOutQty = subInOutQty ?? this.subInOutQty
      ..subInStockQty = subInStockQty ?? this.subInStockQty
      ..subInOutPercent = subInOutPercent ?? this.subInOutPercent
      ..subDiffQty = subDiffQty ?? this.subDiffQty
      ..hasPerm = hasPerm ?? this.hasPerm
      ..baseUnitName = baseUnitName ?? this.baseUnitName
      ..unitxqty = unitxqty ?? this.unitxqty
      ..ptypeUnits = ptypeUnits ?? this.ptypeUnits
      ..mySnList = mySnList ?? this.mySnList
      ..checkSnList = checkSnList ?? this.checkSnList
      ..snnoStrButton = snnoStrButton ?? this.snnoStrButton
      ..unitCode = unitCode ?? this.unitCode
      ..unitRate = unitRate ?? this.unitRate
      ..unitId = unitId ?? this.unitId
      ..detailId = detailId ?? this.detailId
      ..stockCheckId = stockCheckId ?? this.stockCheckId
      ..batchPrice = batchPrice ?? this.batchPrice
      ..batchQty = batchQty ?? this.batchQty
      ..batchLifeQty = batchLifeQty ?? this.batchLifeQty
      ..stockPrice = stockPrice ?? this.stockPrice
      ..id = id ?? this.id
      ..profileId = profileId ?? this.profileId
      ..fullname = fullname ?? this.fullname
      ..shortName = shortName ?? this.shortName
      ..namePy = namePy ?? this.namePy
      ..classed = classed ?? this.classed
      ..stoped = stoped ?? this.stoped
      ..deleted = deleted ?? this.deleted
      ..rowindex = rowindex ?? this.rowindex
      ..barcode = barcode ?? this.barcode
      ..ptypeArea = ptypeArea ?? this.ptypeArea
      ..memo = memo ?? this.memo
      ..createType = createType ?? this.createType
      ..costMode = costMode ?? this.costMode
      ..taxNumber = taxNumber ?? this.taxNumber
      ..costPrice = costPrice ?? this.costPrice
      ..supplyInfo = supplyInfo ?? this.supplyInfo
      ..brandId = brandId ?? this.brandId
      ..ktypeLimit = ktypeLimit ?? this.ktypeLimit
      ..snenabled = snenabled ?? this.snenabled
      ..snEnabled = snEnabled ?? this.snEnabled
      ..propenabled = propenabled ?? this.propenabled
      ..batchenabled = batchenabled ?? this.batchenabled
      ..protectDays = protectDays ?? this.protectDays
      ..protectDaysUnit = protectDaysUnit ?? this.protectDaysUnit
      ..protectWarndays = protectWarndays ?? this.protectWarndays
      ..weight = weight ?? this.weight
      ..weightUnit = weightUnit ?? this.weightUnit
      ..lithiumBattery = lithiumBattery ?? this.lithiumBattery
      ..solid = solid ?? this.solid
      ..difficultyLevel = difficultyLevel ?? this.difficultyLevel
      ..weighted = weighted ?? this.weighted
      ..retailDefaultUnit = retailDefaultUnit ?? this.retailDefaultUnit
      ..saleDefaultUnit = saleDefaultUnit ?? this.saleDefaultUnit
      ..purchaseDefaultUnit = purchaseDefaultUnit ?? this.purchaseDefaultUnit
      ..stockDefaultUnit = stockDefaultUnit ?? this.stockDefaultUnit
      ..ptypeLength = ptypeLength ?? this.ptypeLength
      ..ptypeWidth = ptypeWidth ?? this.ptypeWidth
      ..ptypeHeight = ptypeHeight ?? this.ptypeHeight
      ..lengthUnit = lengthUnit ?? this.lengthUnit
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..batchlifeId = batchlifeId ?? this.batchlifeId
      ..stockQty = stockQty ?? this.stockQty
      ..stockSubQty = stockSubQty ?? this.stockSubQty
      ..checkQty = checkQty ?? this.checkQty
      ..checkSubQty = checkSubQty ?? this.checkSubQty
      ..adjustQty = adjustQty ?? this.adjustQty
      ..adjustSubQty = adjustSubQty ?? this.adjustSubQty
      ..checkTypeStr = checkTypeStr ?? this.checkTypeStr
      ..checkTime = checkTime ?? this.checkTime
      ..checked = checked ?? this.checked
      ..subChecked = subChecked ?? this.subChecked
      ..propvalueName1 = propvalueName1 ?? this.propvalueName1
      ..propvalueName2 = propvalueName2 ?? this.propvalueName2
      ..propvalueName3 = propvalueName3 ?? this.propvalueName3
      ..propvalueName4 = propvalueName4 ?? this.propvalueName4
      ..propvalueName5 = propvalueName5 ?? this.propvalueName5
      ..propvalueName6 = propvalueName6 ?? this.propvalueName6
      ..propvalueAll = propvalueAll ?? this.propvalueAll
      ..proDateStr = proDateStr ?? this.proDateStr
      ..expDateStr = expDateStr ?? this.expDateStr
      ..selectchecked = selectchecked ?? this.selectchecked
      ..snno = snno ?? this.snno
      ..costId = costId ?? this.costId
      ..positionStrText = positionStrText ?? this.positionStrText
      ..positionList = positionList ?? this.positionList
      ..inoutId = inoutId ?? this.inoutId;
  }
}

StockCheckInfoMySnList $StockCheckInfoMySnListFromJson(
    Map<String, dynamic> json) {
  final StockCheckInfoMySnList stockCheckInfoMySnList = StockCheckInfoMySnList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    stockCheckInfoMySnList.id = id;
  }
  final String? batchNo = jsonConvert.convert<String>(json['batchNo']);
  if (batchNo != null) {
    stockCheckInfoMySnList.batchNo = batchNo;
  }
  final String? batchId = jsonConvert.convert<String>(json['batchId']);
  if (batchId != null) {
    stockCheckInfoMySnList.batchId = batchId;
  }
  final dynamic detailId = json['detailId'];
  if (detailId != null) {
    stockCheckInfoMySnList.detailId = detailId;
  }
  final String? snno = jsonConvert.convert<String>(json['snno']);
  if (snno != null) {
    stockCheckInfoMySnList.snno = snno;
  }
  final String? sn1 = jsonConvert.convert<String>(json['sn1']);
  if (sn1 != null) {
    stockCheckInfoMySnList.sn1 = sn1;
  }
  final String? sn2 = jsonConvert.convert<String>(json['sn2']);
  if (sn2 != null) {
    stockCheckInfoMySnList.sn2 = sn2;
  }
  final String? sn3 = jsonConvert.convert<String>(json['sn3']);
  if (sn3 != null) {
    stockCheckInfoMySnList.sn3 = sn3;
  }
  final String? memo = jsonConvert.convert<String>(json['memo']);
  if (memo != null) {
    stockCheckInfoMySnList.memo = memo;
  }
  final dynamic profileId = json['profileId'];
  if (profileId != null) {
    stockCheckInfoMySnList.profileId = profileId;
  }
  final dynamic produceDate = json['produceDate'];
  if (produceDate != null) {
    stockCheckInfoMySnList.produceDate = produceDate;
  }
  final dynamic expireDate = json['expireDate'];
  if (expireDate != null) {
    stockCheckInfoMySnList.expireDate = expireDate;
  }
  final String? proDateStr = jsonConvert.convert<String>(json['proDateStr']);
  if (proDateStr != null) {
    stockCheckInfoMySnList.proDateStr = proDateStr;
  }
  final String? expDateStr = jsonConvert.convert<String>(json['expDateStr']);
  if (expDateStr != null) {
    stockCheckInfoMySnList.expDateStr = expDateStr;
  }
  final String? ptypeId = jsonConvert.convert<String>(json['ptypeId']);
  if (ptypeId != null) {
    stockCheckInfoMySnList.ptypeId = ptypeId;
  }
  final String? skuId = jsonConvert.convert<String>(json['skuId']);
  if (skuId != null) {
    stockCheckInfoMySnList.skuId = skuId;
  }
  final int? batchPrice = jsonConvert.convert<int>(json['batchPrice']);
  if (batchPrice != null) {
    stockCheckInfoMySnList.batchPrice = batchPrice;
  }
  return stockCheckInfoMySnList;
}

Map<String, dynamic> $StockCheckInfoMySnListToJson(
    StockCheckInfoMySnList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['batchNo'] = entity.batchNo;
  data['batchId'] = entity.batchId;
  data['detailId'] = entity.detailId;
  data['snno'] = entity.snno;
  data['sn1'] = entity.sn1;
  data['sn2'] = entity.sn2;
  data['sn3'] = entity.sn3;
  data['memo'] = entity.memo;
  data['profileId'] = entity.profileId;
  data['produceDate'] = entity.produceDate;
  data['expireDate'] = entity.expireDate;
  data['proDateStr'] = entity.proDateStr;
  data['expDateStr'] = entity.expDateStr;
  data['ptypeId'] = entity.ptypeId;
  data['skuId'] = entity.skuId;
  data['batchPrice'] = entity.batchPrice;
  return data;
}

extension StockCheckInfoMySnListExtension on StockCheckInfoMySnList {
  StockCheckInfoMySnList copyWith({
    String? id,
    String? batchNo,
    String? batchId,
    dynamic detailId,
    String? snno,
    String? sn1,
    String? sn2,
    String? sn3,
    String? memo,
    dynamic profileId,
    dynamic produceDate,
    dynamic expireDate,
    String? proDateStr,
    String? expDateStr,
    String? ptypeId,
    String? skuId,
    int? batchPrice,
  }) {
    return StockCheckInfoMySnList()
      ..id = id ?? this.id
      ..batchNo = batchNo ?? this.batchNo
      ..batchId = batchId ?? this.batchId
      ..detailId = detailId ?? this.detailId
      ..snno = snno ?? this.snno
      ..sn1 = sn1 ?? this.sn1
      ..sn2 = sn2 ?? this.sn2
      ..sn3 = sn3 ?? this.sn3
      ..memo = memo ?? this.memo
      ..profileId = profileId ?? this.profileId
      ..produceDate = produceDate ?? this.produceDate
      ..expireDate = expireDate ?? this.expireDate
      ..proDateStr = proDateStr ?? this.proDateStr
      ..expDateStr = expDateStr ?? this.expDateStr
      ..ptypeId = ptypeId ?? this.ptypeId
      ..skuId = skuId ?? this.skuId
      ..batchPrice = batchPrice ?? this.batchPrice;
  }
}