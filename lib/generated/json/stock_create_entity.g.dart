


import '../../stockcheck/entity/stock_check_info_entity.dart';
import '../../stockcheck/request/stock_create_entity.dart';
import 'base/json_convert_content.dart';

StockCreateEntity $StockCreateEntityFromJson(Map<String, dynamic> json) {
  final StockCreateEntity stockCreateEntity = StockCreateEntity();
  final List<StockCheckInfoEntity>? checkPtypes = (json['checkPtypes'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<StockCheckInfoEntity>(e) as StockCheckInfoEntity)
      .toList();
  if (checkPtypes != null) {
    stockCreateEntity.checkPtypes = checkPtypes;
  }
  final int? checkId = jsonConvert.convert<int>(json['checkId']);
  if (checkId != null) {
    stockCreateEntity.checkId = checkId;
  }
  final bool? checkBatchStock = jsonConvert.convert<bool>(
      json['checkBatchStock']);
  if (checkBatchStock != null) {
    stockCreateEntity.checkBatchStock = checkBatchStock;
  }
  final String? scanMode = jsonConvert.convert<String>(json['scanMode']);
  if (scanMode != null) {
    stockCreateEntity.scanMode = scanMode;
  }
  final String? scanPtypeFilter = jsonConvert.convert<String>(
      json['scanPtypeFilter']);
  if (scanPtypeFilter != null) {
    stockCreateEntity.scanPtypeFilter = scanPtypeFilter;
  }
  final int? checkMode = jsonConvert.convert<int>(json['checkMode']);
  if (checkMode != null) {
    stockCreateEntity.checkMode = checkMode;
  }
  final String? checkKtypeId = jsonConvert.convert<String>(
      json['checkKtypeId']);
  if (checkKtypeId != null) {
    stockCreateEntity.checkKtypeId = checkKtypeId;
  }
  final String? summary = jsonConvert.convert<String>(json['summary']);
  if (summary != null) {
    stockCreateEntity.summary = summary;
  }
  final String? checkTime = jsonConvert.convert<String>(json['checkTime']);
  if (checkTime != null) {
    stockCreateEntity.checkTime = checkTime;
  }
  final String? memo = jsonConvert.convert<String>(json['memo']);
  if (memo != null) {
    stockCreateEntity.memo = memo;
  }
  final String? number = jsonConvert.convert<String>(json['number']);
  if (number != null) {
    stockCreateEntity.number = number;
  }
  final bool? reCheck = jsonConvert.convert<bool>(json['reCheck']);
  if (reCheck != null) {
    stockCreateEntity.reCheck = reCheck;
  }
  final int? checkType = jsonConvert.convert<int>(json['checkType']);
  if (checkType != null) {
    stockCreateEntity.checkType = checkType;
  }
  final String? checkEtypeId = jsonConvert.convert<String>(
      json['checkEtypeId']);
  if (checkEtypeId != null) {
    stockCreateEntity.checkEtypeId = checkEtypeId;
  }
  return stockCreateEntity;
}

Map<String, dynamic> $StockCreateEntityToJson(StockCreateEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['checkPtypes'] = entity.checkPtypes?.map((v) => v.toJson()).toList();
  data['checkId'] = entity.checkId;
  data['checkBatchStock'] = entity.checkBatchStock;
  data['scanMode'] = entity.scanMode;
  data['scanPtypeFilter'] = entity.scanPtypeFilter;
  data['checkMode'] = entity.checkMode;
  data['checkKtypeId'] = entity.checkKtypeId;
  data['summary'] = entity.summary;
  data['checkTime'] = entity.checkTime;
  data['memo'] = entity.memo;
  data['number'] = entity.number;
  data['reCheck'] = entity.reCheck;
  data['checkType'] = entity.checkType;
  data['checkEtypeId'] = entity.checkEtypeId;
  return data;
}

extension StockCreateEntityExtension on StockCreateEntity {
  StockCreateEntity copyWith({
    List<StockCheckInfoEntity>? checkPtypes,
    int? checkId,
    bool? checkBatchStock,
    String? scanMode,
    String? scanPtypeFilter,
    int? checkMode,
    String? checkKtypeId,
    String? summary,
    String? checkTime,
    String? memo,
    String? number,
    bool? reCheck,
    int? checkType,
    String? checkEtypeId,
  }) {
    return StockCreateEntity()
      ..checkPtypes = checkPtypes ?? this.checkPtypes
      ..checkId = checkId ?? this.checkId
      ..checkBatchStock = checkBatchStock ?? this.checkBatchStock
      ..scanMode = scanMode ?? this.scanMode
      ..scanPtypeFilter = scanPtypeFilter ?? this.scanPtypeFilter
      ..checkMode = checkMode ?? this.checkMode
      ..checkKtypeId = checkKtypeId ?? this.checkKtypeId
      ..summary = summary ?? this.summary
      ..checkTime = checkTime ?? this.checkTime
      ..memo = memo ?? this.memo
      ..number = number ?? this.number
      ..reCheck = reCheck ?? this.reCheck
      ..checkType = checkType ?? this.checkType
      ..checkEtypeId = checkEtypeId ?? this.checkEtypeId;
  }
}

StockCreateCheckPtypes $StockCreateCheckPtypesFromJson(
    Map<String, dynamic> json) {
  final StockCreateCheckPtypes stockCreateCheckPtypes = StockCreateCheckPtypes();
  final String? ptypeId = jsonConvert.convert<String>(json['ptypeId']);
  if (ptypeId != null) {
    stockCreateCheckPtypes.ptypeId = ptypeId;
  }
  final String? picUrl = jsonConvert.convert<String>(json['picUrl']);
  if (picUrl != null) {
    stockCreateCheckPtypes.picUrl = picUrl;
  }
  final String? usercode = jsonConvert.convert<String>(json['usercode']);
  if (usercode != null) {
    stockCreateCheckPtypes.usercode = usercode;
  }
  final dynamic pFullname = json['pFullname'];
  if (pFullname != null) {
    stockCreateCheckPtypes.pFullname = pFullname;
  }
  final dynamic shortname = json['shortname'];
  if (shortname != null) {
    stockCreateCheckPtypes.shortname = shortname;
  }
  final dynamic brandName = json['brandName'];
  if (brandName != null) {
    stockCreateCheckPtypes.brandName = brandName;
  }
  final String? standard = jsonConvert.convert<String>(json['standard']);
  if (standard != null) {
    stockCreateCheckPtypes.standard = standard;
  }
  final String? ptypeType = jsonConvert.convert<String>(json['ptypeType']);
  if (ptypeType != null) {
    stockCreateCheckPtypes.ptypeType = ptypeType;
  }
  final String? unitName = jsonConvert.convert<String>(json['unitName']);
  if (unitName != null) {
    stockCreateCheckPtypes.unitName = unitName;
  }
  final String? subUnitName = jsonConvert.convert<String>(json['subUnitName']);
  if (subUnitName != null) {
    stockCreateCheckPtypes.subUnitName = subUnitName;
  }
  final dynamic ptypeMemo = json['ptypeMemo'];
  if (ptypeMemo != null) {
    stockCreateCheckPtypes.ptypeMemo = ptypeMemo;
  }
  final dynamic fullbarcode = json['fullbarcode'];
  if (fullbarcode != null) {
    stockCreateCheckPtypes.fullbarcode = fullbarcode;
  }
  final String? skuId = jsonConvert.convert<String>(json['skuId']);
  if (skuId != null) {
    stockCreateCheckPtypes.skuId = skuId;
  }
  final String? xcode = jsonConvert.convert<String>(json['xcode']);
  if (xcode != null) {
    stockCreateCheckPtypes.xcode = xcode;
  }
  final dynamic taxRate = json['taxRate'];
  if (taxRate != null) {
    stockCreateCheckPtypes.taxRate = taxRate;
  }
  final int? skuPrice = jsonConvert.convert<int>(json['skuPrice']);
  if (skuPrice != null) {
    stockCreateCheckPtypes.skuPrice = skuPrice;
  }
  final dynamic pcategory = json['pcategory'];
  if (pcategory != null) {
    stockCreateCheckPtypes.pcategory = pcategory;
  }
  final int? industryCategory = jsonConvert.convert<int>(
      json['industryCategory']);
  if (industryCategory != null) {
    stockCreateCheckPtypes.industryCategory = industryCategory;
  }
  final dynamic parName1 = json['parName1'];
  if (parName1 != null) {
    stockCreateCheckPtypes.parName1 = parName1;
  }
  final dynamic parName2 = json['parName2'];
  if (parName2 != null) {
    stockCreateCheckPtypes.parName2 = parName2;
  }
  final dynamic parName3 = json['parName3'];
  if (parName3 != null) {
    stockCreateCheckPtypes.parName3 = parName3;
  }
  final dynamic parName4 = json['parName4'];
  if (parName4 != null) {
    stockCreateCheckPtypes.parName4 = parName4;
  }
  final dynamic parName5 = json['parName5'];
  if (parName5 != null) {
    stockCreateCheckPtypes.parName5 = parName5;
  }
  final dynamic ptypeWeight = json['ptypeWeight'];
  if (ptypeWeight != null) {
    stockCreateCheckPtypes.ptypeWeight = ptypeWeight;
  }
  final dynamic unitRelation = json['unitRelation'];
  if (unitRelation != null) {
    stockCreateCheckPtypes.unitRelation = unitRelation;
  }
  final dynamic picUrls = json['picUrls'];
  if (picUrls != null) {
    stockCreateCheckPtypes.picUrls = picUrls;
  }
  final int? skuName = jsonConvert.convert<int>(json['skuName']);
  if (skuName != null) {
    stockCreateCheckPtypes.skuName = skuName;
  }
  final String? propNames = jsonConvert.convert<String>(json['propNames']);
  if (propNames != null) {
    stockCreateCheckPtypes.propNames = propNames;
  }
  final String? propValues = jsonConvert.convert<String>(json['propValues']);
  if (propValues != null) {
    stockCreateCheckPtypes.propValues = propValues;
  }
  final dynamic propValueName1 = json['propValueName1'];
  if (propValueName1 != null) {
    stockCreateCheckPtypes.propValueName1 = propValueName1;
  }
  final dynamic propValueName2 = json['propValueName2'];
  if (propValueName2 != null) {
    stockCreateCheckPtypes.propValueName2 = propValueName2;
  }
  final dynamic batchno = json['batchno'];
  if (batchno != null) {
    stockCreateCheckPtypes.batchno = batchno;
  }
  final dynamic produceDate = json['produceDate'];
  if (produceDate != null) {
    stockCreateCheckPtypes.produceDate = produceDate;
  }
  final dynamic expireDate = json['expireDate'];
  if (expireDate != null) {
    stockCreateCheckPtypes.expireDate = expireDate;
  }
  final dynamic defProduceDate = json['defProduceDate'];
  if (defProduceDate != null) {
    stockCreateCheckPtypes.defProduceDate = defProduceDate;
  }
  final dynamic defExpireDate = json['defExpireDate'];
  if (defExpireDate != null) {
    stockCreateCheckPtypes.defExpireDate = defExpireDate;
  }
  final dynamic customHead01 = json['customHead01'];
  if (customHead01 != null) {
    stockCreateCheckPtypes.customHead01 = customHead01;
  }
  final dynamic customHead02 = json['customHead02'];
  if (customHead02 != null) {
    stockCreateCheckPtypes.customHead02 = customHead02;
  }
  final dynamic customHead03 = json['customHead03'];
  if (customHead03 != null) {
    stockCreateCheckPtypes.customHead03 = customHead03;
  }
  final dynamic customHead04 = json['customHead04'];
  if (customHead04 != null) {
    stockCreateCheckPtypes.customHead04 = customHead04;
  }
  final dynamic customHead05 = json['customHead05'];
  if (customHead05 != null) {
    stockCreateCheckPtypes.customHead05 = customHead05;
  }
  final dynamic customHead06 = json['customHead06'];
  if (customHead06 != null) {
    stockCreateCheckPtypes.customHead06 = customHead06;
  }
  final dynamic customHead07 = json['customHead07'];
  if (customHead07 != null) {
    stockCreateCheckPtypes.customHead07 = customHead07;
  }
  final dynamic customHead08 = json['customHead08'];
  if (customHead08 != null) {
    stockCreateCheckPtypes.customHead08 = customHead08;
  }
  final dynamic customHead09 = json['customHead09'];
  if (customHead09 != null) {
    stockCreateCheckPtypes.customHead09 = customHead09;
  }
  final dynamic customHead10 = json['customHead10'];
  if (customHead10 != null) {
    stockCreateCheckPtypes.customHead10 = customHead10;
  }
  final dynamic customHead11 = json['customHead11'];
  if (customHead11 != null) {
    stockCreateCheckPtypes.customHead11 = customHead11;
  }
  final dynamic customHead12 = json['customHead12'];
  if (customHead12 != null) {
    stockCreateCheckPtypes.customHead12 = customHead12;
  }
  final dynamic customHead13 = json['customHead13'];
  if (customHead13 != null) {
    stockCreateCheckPtypes.customHead13 = customHead13;
  }
  final dynamic customHead14 = json['customHead14'];
  if (customHead14 != null) {
    stockCreateCheckPtypes.customHead14 = customHead14;
  }
  final dynamic subUnit = json['subUnit'];
  if (subUnit != null) {
    stockCreateCheckPtypes.subUnit = subUnit;
  }
  final dynamic subQty = json['subQty'];
  if (subQty != null) {
    stockCreateCheckPtypes.subQty = subQty;
  }
  final dynamic subInQty = json['subInQty'];
  if (subInQty != null) {
    stockCreateCheckPtypes.subInQty = subInQty;
  }
  final dynamic subOutQty = json['subOutQty'];
  if (subOutQty != null) {
    stockCreateCheckPtypes.subOutQty = subOutQty;
  }
  final dynamic subInOutQty = json['subInOutQty'];
  if (subInOutQty != null) {
    stockCreateCheckPtypes.subInOutQty = subInOutQty;
  }
  final dynamic subInStockQty = json['subInStockQty'];
  if (subInStockQty != null) {
    stockCreateCheckPtypes.subInStockQty = subInStockQty;
  }
  final dynamic subInOutPercent = json['subInOutPercent'];
  if (subInOutPercent != null) {
    stockCreateCheckPtypes.subInOutPercent = subInOutPercent;
  }
  final dynamic subDiffQty = json['subDiffQty'];
  if (subDiffQty != null) {
    stockCreateCheckPtypes.subDiffQty = subDiffQty;
  }
  final bool? hasPerm = jsonConvert.convert<bool>(json['hasPerm']);
  if (hasPerm != null) {
    stockCreateCheckPtypes.hasPerm = hasPerm;
  }
  final dynamic baseUnitName = json['baseUnitName'];
  if (baseUnitName != null) {
    stockCreateCheckPtypes.baseUnitName = baseUnitName;
  }
  final String? unitxqty = jsonConvert.convert<String>(json['unitxqty']);
  if (unitxqty != null) {
    stockCreateCheckPtypes.unitxqty = unitxqty;
  }
  final dynamic ptypeUnits = json['ptypeUnits'];
  if (ptypeUnits != null) {
    stockCreateCheckPtypes.ptypeUnits = ptypeUnits;
  }
  final List<dynamic>? mySnList = (json['mySnList'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (mySnList != null) {
    stockCreateCheckPtypes.mySnList = mySnList;
  }
  final dynamic checkSnList = json['checkSnList'];
  if (checkSnList != null) {
    stockCreateCheckPtypes.checkSnList = checkSnList;
  }
  final String? snnoStrButton = jsonConvert.convert<String>(
      json['snnoStrButton']);
  if (snnoStrButton != null) {
    stockCreateCheckPtypes.snnoStrButton = snnoStrButton;
  }
  final int? unitCode = jsonConvert.convert<int>(json['unitCode']);
  if (unitCode != null) {
    stockCreateCheckPtypes.unitCode = unitCode;
  }
  final dynamic unitRate = json['unitRate'];
  if (unitRate != null) {
    stockCreateCheckPtypes.unitRate = unitRate;
  }
  final String? unitId = jsonConvert.convert<String>(json['unitId']);
  if (unitId != null) {
    stockCreateCheckPtypes.unitId = unitId;
  }
  final dynamic detailId = json['detailId'];
  if (detailId != null) {
    stockCreateCheckPtypes.detailId = detailId;
  }
  final dynamic stockCheckId = json['stockCheckId'];
  if (stockCheckId != null) {
    stockCreateCheckPtypes.stockCheckId = stockCheckId;
  }
  final int? batchPrice = jsonConvert.convert<int>(json['batchPrice']);
  if (batchPrice != null) {
    stockCreateCheckPtypes.batchPrice = batchPrice;
  }
  final int? batchQty = jsonConvert.convert<int>(json['batchQty']);
  if (batchQty != null) {
    stockCreateCheckPtypes.batchQty = batchQty;
  }
  final dynamic batchLifeQty = json['batchLifeQty'];
  if (batchLifeQty != null) {
    stockCreateCheckPtypes.batchLifeQty = batchLifeQty;
  }
  final dynamic stockPrice = json['stockPrice'];
  if (stockPrice != null) {
    stockCreateCheckPtypes.stockPrice = stockPrice;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    stockCreateCheckPtypes.id = id;
  }
  final dynamic profileId = json['profileId'];
  if (profileId != null) {
    stockCreateCheckPtypes.profileId = profileId;
  }
  final String? fullname = jsonConvert.convert<String>(json['fullname']);
  if (fullname != null) {
    stockCreateCheckPtypes.fullname = fullname;
  }
  final dynamic shortName = json['shortName'];
  if (shortName != null) {
    stockCreateCheckPtypes.shortName = shortName;
  }
  final dynamic namePy = json['namePy'];
  if (namePy != null) {
    stockCreateCheckPtypes.namePy = namePy;
  }
  final dynamic classed = json['classed'];
  if (classed != null) {
    stockCreateCheckPtypes.classed = classed;
  }
  final dynamic stoped = json['stoped'];
  if (stoped != null) {
    stockCreateCheckPtypes.stoped = stoped;
  }
  final dynamic deleted = json['deleted'];
  if (deleted != null) {
    stockCreateCheckPtypes.deleted = deleted;
  }
  final String? rowindex = jsonConvert.convert<String>(json['rowindex']);
  if (rowindex != null) {
    stockCreateCheckPtypes.rowindex = rowindex;
  }
  final String? barcode = jsonConvert.convert<String>(json['barcode']);
  if (barcode != null) {
    stockCreateCheckPtypes.barcode = barcode;
  }
  final String? ptypeArea = jsonConvert.convert<String>(json['ptypeArea']);
  if (ptypeArea != null) {
    stockCreateCheckPtypes.ptypeArea = ptypeArea;
  }
  final dynamic memo = json['memo'];
  if (memo != null) {
    stockCreateCheckPtypes.memo = memo;
  }
  final dynamic createType = json['createType'];
  if (createType != null) {
    stockCreateCheckPtypes.createType = createType;
  }
  final int? costMode = jsonConvert.convert<int>(json['costMode']);
  if (costMode != null) {
    stockCreateCheckPtypes.costMode = costMode;
  }
  final dynamic taxNumber = json['taxNumber'];
  if (taxNumber != null) {
    stockCreateCheckPtypes.taxNumber = taxNumber;
  }
  final int? costPrice = jsonConvert.convert<int>(json['costPrice']);
  if (costPrice != null) {
    stockCreateCheckPtypes.costPrice = costPrice;
  }
  final dynamic supplyInfo = json['supplyInfo'];
  if (supplyInfo != null) {
    stockCreateCheckPtypes.supplyInfo = supplyInfo;
  }
  final dynamic brandId = json['brandId'];
  if (brandId != null) {
    stockCreateCheckPtypes.brandId = brandId;
  }
  final dynamic ktypeLimit = json['ktypeLimit'];
  if (ktypeLimit != null) {
    stockCreateCheckPtypes.ktypeLimit = ktypeLimit;
  }
  final int? snenabled = jsonConvert.convert<int>(json['snenabled']);
  if (snenabled != null) {
    stockCreateCheckPtypes.snenabled = snenabled;
  }
  final bool? propenabled = jsonConvert.convert<bool>(json['propenabled']);
  if (propenabled != null) {
    stockCreateCheckPtypes.propenabled = propenabled;
  }
  final bool? batchenabled = jsonConvert.convert<bool>(json['batchenabled']);
  if (batchenabled != null) {
    stockCreateCheckPtypes.batchenabled = batchenabled;
  }
  final int? protectDays = jsonConvert.convert<int>(json['protectDays']);
  if (protectDays != null) {
    stockCreateCheckPtypes.protectDays = protectDays;
  }
  final int? protectDaysUnit = jsonConvert.convert<int>(
      json['protectDaysUnit']);
  if (protectDaysUnit != null) {
    stockCreateCheckPtypes.protectDaysUnit = protectDaysUnit;
  }
  final dynamic protectWarndays = json['protectWarndays'];
  if (protectWarndays != null) {
    stockCreateCheckPtypes.protectWarndays = protectWarndays;
  }
  final dynamic weight = json['weight'];
  if (weight != null) {
    stockCreateCheckPtypes.weight = weight;
  }
  final dynamic weightUnit = json['weightUnit'];
  if (weightUnit != null) {
    stockCreateCheckPtypes.weightUnit = weightUnit;
  }
  final dynamic lithiumBattery = json['lithiumBattery'];
  if (lithiumBattery != null) {
    stockCreateCheckPtypes.lithiumBattery = lithiumBattery;
  }
  final dynamic solid = json['solid'];
  if (solid != null) {
    stockCreateCheckPtypes.solid = solid;
  }
  final dynamic difficultyLevel = json['difficultyLevel'];
  if (difficultyLevel != null) {
    stockCreateCheckPtypes.difficultyLevel = difficultyLevel;
  }
  final dynamic weighted = json['weighted'];
  if (weighted != null) {
    stockCreateCheckPtypes.weighted = weighted;
  }
  final dynamic retailDefaultUnit = json['retailDefaultUnit'];
  if (retailDefaultUnit != null) {
    stockCreateCheckPtypes.retailDefaultUnit = retailDefaultUnit;
  }
  final dynamic saleDefaultUnit = json['saleDefaultUnit'];
  if (saleDefaultUnit != null) {
    stockCreateCheckPtypes.saleDefaultUnit = saleDefaultUnit;
  }
  final dynamic purchaseDefaultUnit = json['purchaseDefaultUnit'];
  if (purchaseDefaultUnit != null) {
    stockCreateCheckPtypes.purchaseDefaultUnit = purchaseDefaultUnit;
  }
  final dynamic stockDefaultUnit = json['stockDefaultUnit'];
  if (stockDefaultUnit != null) {
    stockCreateCheckPtypes.stockDefaultUnit = stockDefaultUnit;
  }
  final dynamic ptypeLength = json['ptypeLength'];
  if (ptypeLength != null) {
    stockCreateCheckPtypes.ptypeLength = ptypeLength;
  }
  final dynamic ptypeWidth = json['ptypeWidth'];
  if (ptypeWidth != null) {
    stockCreateCheckPtypes.ptypeWidth = ptypeWidth;
  }
  final dynamic ptypeHeight = json['ptypeHeight'];
  if (ptypeHeight != null) {
    stockCreateCheckPtypes.ptypeHeight = ptypeHeight;
  }
  final dynamic lengthUnit = json['lengthUnit'];
  if (lengthUnit != null) {
    stockCreateCheckPtypes.lengthUnit = lengthUnit;
  }
  final dynamic createTime = json['createTime'];
  if (createTime != null) {
    stockCreateCheckPtypes.createTime = createTime;
  }
  final dynamic updateTime = json['updateTime'];
  if (updateTime != null) {
    stockCreateCheckPtypes.updateTime = updateTime;
  }
  final String? batchlifeId = jsonConvert.convert<String>(json['batchlifeId']);
  if (batchlifeId != null) {
    stockCreateCheckPtypes.batchlifeId = batchlifeId;
  }
  final int? stockQty = jsonConvert.convert<int>(json['stockQty']);
  if (stockQty != null) {
    stockCreateCheckPtypes.stockQty = stockQty;
  }
  final int? stockSubQty = jsonConvert.convert<int>(json['stockSubQty']);
  if (stockSubQty != null) {
    stockCreateCheckPtypes.stockSubQty = stockSubQty;
  }
  final int? checkQty = jsonConvert.convert<int>(json['checkQty']);
  if (checkQty != null) {
    stockCreateCheckPtypes.checkQty = checkQty;
  }
  final dynamic checkSubQty = json['checkSubQty'];
  if (checkSubQty != null) {
    stockCreateCheckPtypes.checkSubQty = checkSubQty;
  }
  final int? adjustQty = jsonConvert.convert<int>(json['adjustQty']);
  if (adjustQty != null) {
    stockCreateCheckPtypes.adjustQty = adjustQty;
  }
  final dynamic adjustSubQty = json['adjustSubQty'];
  if (adjustSubQty != null) {
    stockCreateCheckPtypes.adjustSubQty = adjustSubQty;
  }
  final String? checkTypeStr = jsonConvert.convert<String>(
      json['checkTypeStr']);
  if (checkTypeStr != null) {
    stockCreateCheckPtypes.checkTypeStr = checkTypeStr;
  }
  final String? checkTime = jsonConvert.convert<String>(json['checkTime']);
  if (checkTime != null) {
    stockCreateCheckPtypes.checkTime = checkTime;
  }
  final int? checked = jsonConvert.convert<int>(json['checked']);
  if (checked != null) {
    stockCreateCheckPtypes.checked = checked;
  }
  final bool? subChecked = jsonConvert.convert<bool>(json['subChecked']);
  if (subChecked != null) {
    stockCreateCheckPtypes.subChecked = subChecked;
  }
  final String? propvalueName1 = jsonConvert.convert<String>(
      json['propvalueName1']);
  if (propvalueName1 != null) {
    stockCreateCheckPtypes.propvalueName1 = propvalueName1;
  }
  final String? propvalueName2 = jsonConvert.convert<String>(
      json['propvalueName2']);
  if (propvalueName2 != null) {
    stockCreateCheckPtypes.propvalueName2 = propvalueName2;
  }
  final dynamic propvalueName3 = json['propvalueName3'];
  if (propvalueName3 != null) {
    stockCreateCheckPtypes.propvalueName3 = propvalueName3;
  }
  final dynamic propvalueName4 = json['propvalueName4'];
  if (propvalueName4 != null) {
    stockCreateCheckPtypes.propvalueName4 = propvalueName4;
  }
  final dynamic propvalueName5 = json['propvalueName5'];
  if (propvalueName5 != null) {
    stockCreateCheckPtypes.propvalueName5 = propvalueName5;
  }
  final dynamic propvalueName6 = json['propvalueName6'];
  if (propvalueName6 != null) {
    stockCreateCheckPtypes.propvalueName6 = propvalueName6;
  }
  final String? propvalueAll = jsonConvert.convert<String>(
      json['propvalueAll']);
  if (propvalueAll != null) {
    stockCreateCheckPtypes.propvalueAll = propvalueAll;
  }
  final String? proDateStr = jsonConvert.convert<String>(json['proDateStr']);
  if (proDateStr != null) {
    stockCreateCheckPtypes.proDateStr = proDateStr;
  }
  final String? expDateStr = jsonConvert.convert<String>(json['expDateStr']);
  if (expDateStr != null) {
    stockCreateCheckPtypes.expDateStr = expDateStr;
  }
  final bool? selectchecked = jsonConvert.convert<bool>(json['selectchecked']);
  if (selectchecked != null) {
    stockCreateCheckPtypes.selectchecked = selectchecked;
  }
  final dynamic snno = json['snno'];
  if (snno != null) {
    stockCreateCheckPtypes.snno = snno;
  }
  final String? costId = jsonConvert.convert<String>(json['costId']);
  if (costId != null) {
    stockCreateCheckPtypes.costId = costId;
  }
  final dynamic positionStrText = json['positionStrText'];
  if (positionStrText != null) {
    stockCreateCheckPtypes.positionStrText = positionStrText;
  }
  final dynamic positionList = json['positionList'];
  if (positionList != null) {
    stockCreateCheckPtypes.positionList = positionList;
  }
  final dynamic inoutId = json['inoutId'];
  if (inoutId != null) {
    stockCreateCheckPtypes.inoutId = inoutId;
  }
  return stockCreateCheckPtypes;
}

Map<String, dynamic> $StockCreateCheckPtypesToJson(
    StockCreateCheckPtypes entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['ptypeId'] = entity.ptypeId;
  data['picUrl'] = entity.picUrl;
  data['usercode'] = entity.usercode;
  data['pFullname'] = entity.pFullname;
  data['shortname'] = entity.shortname;
  data['brandName'] = entity.brandName;
  data['standard'] = entity.standard;
  data['ptypeType'] = entity.ptypeType;
  data['unitName'] = entity.unitName;
  data['subUnitName'] = entity.subUnitName;
  data['ptypeMemo'] = entity.ptypeMemo;
  data['fullbarcode'] = entity.fullbarcode;
  data['skuId'] = entity.skuId;
  data['xcode'] = entity.xcode;
  data['taxRate'] = entity.taxRate;
  data['skuPrice'] = entity.skuPrice;
  data['pcategory'] = entity.pcategory;
  data['industryCategory'] = entity.industryCategory;
  data['parName1'] = entity.parName1;
  data['parName2'] = entity.parName2;
  data['parName3'] = entity.parName3;
  data['parName4'] = entity.parName4;
  data['parName5'] = entity.parName5;
  data['ptypeWeight'] = entity.ptypeWeight;
  data['unitRelation'] = entity.unitRelation;
  data['picUrls'] = entity.picUrls;
  data['skuName'] = entity.skuName;
  data['propNames'] = entity.propNames;
  data['propValues'] = entity.propValues;
  data['propValueName1'] = entity.propValueName1;
  data['propValueName2'] = entity.propValueName2;
  data['batchno'] = entity.batchno;
  data['produceDate'] = entity.produceDate;
  data['expireDate'] = entity.expireDate;
  data['defProduceDate'] = entity.defProduceDate;
  data['defExpireDate'] = entity.defExpireDate;
  data['customHead01'] = entity.customHead01;
  data['customHead02'] = entity.customHead02;
  data['customHead03'] = entity.customHead03;
  data['customHead04'] = entity.customHead04;
  data['customHead05'] = entity.customHead05;
  data['customHead06'] = entity.customHead06;
  data['customHead07'] = entity.customHead07;
  data['customHead08'] = entity.customHead08;
  data['customHead09'] = entity.customHead09;
  data['customHead10'] = entity.customHead10;
  data['customHead11'] = entity.customHead11;
  data['customHead12'] = entity.customHead12;
  data['customHead13'] = entity.customHead13;
  data['customHead14'] = entity.customHead14;
  data['subUnit'] = entity.subUnit;
  data['subQty'] = entity.subQty;
  data['subInQty'] = entity.subInQty;
  data['subOutQty'] = entity.subOutQty;
  data['subInOutQty'] = entity.subInOutQty;
  data['subInStockQty'] = entity.subInStockQty;
  data['subInOutPercent'] = entity.subInOutPercent;
  data['subDiffQty'] = entity.subDiffQty;
  data['hasPerm'] = entity.hasPerm;
  data['baseUnitName'] = entity.baseUnitName;
  data['unitxqty'] = entity.unitxqty;
  data['ptypeUnits'] = entity.ptypeUnits;
  data['mySnList'] = entity.mySnList;
  data['checkSnList'] = entity.checkSnList;
  data['snnoStrButton'] = entity.snnoStrButton;
  data['unitCode'] = entity.unitCode;
  data['unitRate'] = entity.unitRate;
  data['unitId'] = entity.unitId;
  data['detailId'] = entity.detailId;
  data['stockCheckId'] = entity.stockCheckId;
  data['batchPrice'] = entity.batchPrice;
  data['batchQty'] = entity.batchQty;
  data['batchLifeQty'] = entity.batchLifeQty;
  data['stockPrice'] = entity.stockPrice;
  data['id'] = entity.id;
  data['profileId'] = entity.profileId;
  data['fullname'] = entity.fullname;
  data['shortName'] = entity.shortName;
  data['namePy'] = entity.namePy;
  data['classed'] = entity.classed;
  data['stoped'] = entity.stoped;
  data['deleted'] = entity.deleted;
  data['rowindex'] = entity.rowindex;
  data['barcode'] = entity.barcode;
  data['ptypeArea'] = entity.ptypeArea;
  data['memo'] = entity.memo;
  data['createType'] = entity.createType;
  data['costMode'] = entity.costMode;
  data['taxNumber'] = entity.taxNumber;
  data['costPrice'] = entity.costPrice;
  data['supplyInfo'] = entity.supplyInfo;
  data['brandId'] = entity.brandId;
  data['ktypeLimit'] = entity.ktypeLimit;
  data['snenabled'] = entity.snenabled;
  data['propenabled'] = entity.propenabled;
  data['batchenabled'] = entity.batchenabled;
  data['protectDays'] = entity.protectDays;
  data['protectDaysUnit'] = entity.protectDaysUnit;
  data['protectWarndays'] = entity.protectWarndays;
  data['weight'] = entity.weight;
  data['weightUnit'] = entity.weightUnit;
  data['lithiumBattery'] = entity.lithiumBattery;
  data['solid'] = entity.solid;
  data['difficultyLevel'] = entity.difficultyLevel;
  data['weighted'] = entity.weighted;
  data['retailDefaultUnit'] = entity.retailDefaultUnit;
  data['saleDefaultUnit'] = entity.saleDefaultUnit;
  data['purchaseDefaultUnit'] = entity.purchaseDefaultUnit;
  data['stockDefaultUnit'] = entity.stockDefaultUnit;
  data['ptypeLength'] = entity.ptypeLength;
  data['ptypeWidth'] = entity.ptypeWidth;
  data['ptypeHeight'] = entity.ptypeHeight;
  data['lengthUnit'] = entity.lengthUnit;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['batchlifeId'] = entity.batchlifeId;
  data['stockQty'] = entity.stockQty;
  data['stockSubQty'] = entity.stockSubQty;
  data['checkQty'] = entity.checkQty;
  data['checkSubQty'] = entity.checkSubQty;
  data['adjustQty'] = entity.adjustQty;
  data['adjustSubQty'] = entity.adjustSubQty;
  data['checkTypeStr'] = entity.checkTypeStr;
  data['checkTime'] = entity.checkTime;
  data['checked'] = entity.checked;
  data['subChecked'] = entity.subChecked;
  data['propvalueName1'] = entity.propvalueName1;
  data['propvalueName2'] = entity.propvalueName2;
  data['propvalueName3'] = entity.propvalueName3;
  data['propvalueName4'] = entity.propvalueName4;
  data['propvalueName5'] = entity.propvalueName5;
  data['propvalueName6'] = entity.propvalueName6;
  data['propvalueAll'] = entity.propvalueAll;
  data['proDateStr'] = entity.proDateStr;
  data['expDateStr'] = entity.expDateStr;
  data['selectchecked'] = entity.selectchecked;
  data['snno'] = entity.snno;
  data['costId'] = entity.costId;
  data['positionStrText'] = entity.positionStrText;
  data['positionList'] = entity.positionList;
  data['inoutId'] = entity.inoutId;
  return data;
}

extension StockCreateCheckPtypesExtension on StockCreateCheckPtypes {
  StockCreateCheckPtypes copyWith({
    String? ptypeId,
    String? picUrl,
    String? usercode,
    dynamic pFullname,
    dynamic shortname,
    dynamic brandName,
    String? standard,
    String? ptypeType,
    String? unitName,
    String? subUnitName,
    dynamic ptypeMemo,
    dynamic fullbarcode,
    String? skuId,
    String? xcode,
    dynamic taxRate,
    int? skuPrice,
    dynamic pcategory,
    int? industryCategory,
    dynamic parName1,
    dynamic parName2,
    dynamic parName3,
    dynamic parName4,
    dynamic parName5,
    dynamic ptypeWeight,
    dynamic unitRelation,
    dynamic picUrls,
    int? skuName,
    String? propNames,
    String? propValues,
    dynamic propValueName1,
    dynamic propValueName2,
    dynamic batchno,
    dynamic produceDate,
    dynamic expireDate,
    dynamic defProduceDate,
    dynamic defExpireDate,
    dynamic customHead01,
    dynamic customHead02,
    dynamic customHead03,
    dynamic customHead04,
    dynamic customHead05,
    dynamic customHead06,
    dynamic customHead07,
    dynamic customHead08,
    dynamic customHead09,
    dynamic customHead10,
    dynamic customHead11,
    dynamic customHead12,
    dynamic customHead13,
    dynamic customHead14,
    dynamic subUnit,
    dynamic subQty,
    dynamic subInQty,
    dynamic subOutQty,
    dynamic subInOutQty,
    dynamic subInStockQty,
    dynamic subInOutPercent,
    dynamic subDiffQty,
    bool? hasPerm,
    dynamic baseUnitName,
    String? unitxqty,
    dynamic ptypeUnits,
    List<dynamic>? mySnList,
    dynamic checkSnList,
    String? snnoStrButton,
    int? unitCode,
    dynamic unitRate,
    String? unitId,
    dynamic detailId,
    dynamic stockCheckId,
    int? batchPrice,
    int? batchQty,
    dynamic batchLifeQty,
    dynamic stockPrice,
    String? id,
    dynamic profileId,
    String? fullname,
    dynamic shortName,
    dynamic namePy,
    dynamic classed,
    dynamic stoped,
    dynamic deleted,
    String? rowindex,
    String? barcode,
    String? ptypeArea,
    dynamic memo,
    dynamic createType,
    int? costMode,
    dynamic taxNumber,
    int? costPrice,
    dynamic supplyInfo,
    dynamic brandId,
    dynamic ktypeLimit,
    int? snenabled,
    bool? propenabled,
    bool? batchenabled,
    int? protectDays,
    int? protectDaysUnit,
    dynamic protectWarndays,
    dynamic weight,
    dynamic weightUnit,
    dynamic lithiumBattery,
    dynamic solid,
    dynamic difficultyLevel,
    dynamic weighted,
    dynamic retailDefaultUnit,
    dynamic saleDefaultUnit,
    dynamic purchaseDefaultUnit,
    dynamic stockDefaultUnit,
    dynamic ptypeLength,
    dynamic ptypeWidth,
    dynamic ptypeHeight,
    dynamic lengthUnit,
    dynamic createTime,
    dynamic updateTime,
    String? batchlifeId,
    int? stockQty,
    int? stockSubQty,
    int? checkQty,
    dynamic checkSubQty,
    int? adjustQty,
    dynamic adjustSubQty,
    String? checkTypeStr,
    String? checkTime,
    int? checked,
    bool? subChecked,
    String? propvalueName1,
    String? propvalueName2,
    dynamic propvalueName3,
    dynamic propvalueName4,
    dynamic propvalueName5,
    dynamic propvalueName6,
    String? propvalueAll,
    String? proDateStr,
    String? expDateStr,
    bool? selectchecked,
    dynamic snno,
    String? costId,
    dynamic positionStrText,
    dynamic positionList,
    dynamic inoutId,
  }) {
    return StockCreateCheckPtypes()
      ..ptypeId = ptypeId ?? this.ptypeId
      ..picUrl = picUrl ?? this.picUrl
      ..usercode = usercode ?? this.usercode
      ..pFullname = pFullname ?? this.pFullname
      ..shortname = shortname ?? this.shortname
      ..brandName = brandName ?? this.brandName
      ..standard = standard ?? this.standard
      ..ptypeType = ptypeType ?? this.ptypeType
      ..unitName = unitName ?? this.unitName
      ..subUnitName = subUnitName ?? this.subUnitName
      ..ptypeMemo = ptypeMemo ?? this.ptypeMemo
      ..fullbarcode = fullbarcode ?? this.fullbarcode
      ..skuId = skuId ?? this.skuId
      ..xcode = xcode ?? this.xcode
      ..taxRate = taxRate ?? this.taxRate
      ..skuPrice = skuPrice ?? this.skuPrice
      ..pcategory = pcategory ?? this.pcategory
      ..industryCategory = industryCategory ?? this.industryCategory
      ..parName1 = parName1 ?? this.parName1
      ..parName2 = parName2 ?? this.parName2
      ..parName3 = parName3 ?? this.parName3
      ..parName4 = parName4 ?? this.parName4
      ..parName5 = parName5 ?? this.parName5
      ..ptypeWeight = ptypeWeight ?? this.ptypeWeight
      ..unitRelation = unitRelation ?? this.unitRelation
      ..picUrls = picUrls ?? this.picUrls
      ..skuName = skuName ?? this.skuName
      ..propNames = propNames ?? this.propNames
      ..propValues = propValues ?? this.propValues
      ..propValueName1 = propValueName1 ?? this.propValueName1
      ..propValueName2 = propValueName2 ?? this.propValueName2
      ..batchno = batchno ?? this.batchno
      ..produceDate = produceDate ?? this.produceDate
      ..expireDate = expireDate ?? this.expireDate
      ..defProduceDate = defProduceDate ?? this.defProduceDate
      ..defExpireDate = defExpireDate ?? this.defExpireDate
      ..customHead01 = customHead01 ?? this.customHead01
      ..customHead02 = customHead02 ?? this.customHead02
      ..customHead03 = customHead03 ?? this.customHead03
      ..customHead04 = customHead04 ?? this.customHead04
      ..customHead05 = customHead05 ?? this.customHead05
      ..customHead06 = customHead06 ?? this.customHead06
      ..customHead07 = customHead07 ?? this.customHead07
      ..customHead08 = customHead08 ?? this.customHead08
      ..customHead09 = customHead09 ?? this.customHead09
      ..customHead10 = customHead10 ?? this.customHead10
      ..customHead11 = customHead11 ?? this.customHead11
      ..customHead12 = customHead12 ?? this.customHead12
      ..customHead13 = customHead13 ?? this.customHead13
      ..customHead14 = customHead14 ?? this.customHead14
      ..subUnit = subUnit ?? this.subUnit
      ..subQty = subQty ?? this.subQty
      ..subInQty = subInQty ?? this.subInQty
      ..subOutQty = subOutQty ?? this.subOutQty
      ..subInOutQty = subInOutQty ?? this.subInOutQty
      ..subInStockQty = subInStockQty ?? this.subInStockQty
      ..subInOutPercent = subInOutPercent ?? this.subInOutPercent
      ..subDiffQty = subDiffQty ?? this.subDiffQty
      ..hasPerm = hasPerm ?? this.hasPerm
      ..baseUnitName = baseUnitName ?? this.baseUnitName
      ..unitxqty = unitxqty ?? this.unitxqty
      ..ptypeUnits = ptypeUnits ?? this.ptypeUnits
      ..mySnList = mySnList ?? this.mySnList
      ..checkSnList = checkSnList ?? this.checkSnList
      ..snnoStrButton = snnoStrButton ?? this.snnoStrButton
      ..unitCode = unitCode ?? this.unitCode
      ..unitRate = unitRate ?? this.unitRate
      ..unitId = unitId ?? this.unitId
      ..detailId = detailId ?? this.detailId
      ..stockCheckId = stockCheckId ?? this.stockCheckId
      ..batchPrice = batchPrice ?? this.batchPrice
      ..batchQty = batchQty ?? this.batchQty
      ..batchLifeQty = batchLifeQty ?? this.batchLifeQty
      ..stockPrice = stockPrice ?? this.stockPrice
      ..id = id ?? this.id
      ..profileId = profileId ?? this.profileId
      ..fullname = fullname ?? this.fullname
      ..shortName = shortName ?? this.shortName
      ..namePy = namePy ?? this.namePy
      ..classed = classed ?? this.classed
      ..stoped = stoped ?? this.stoped
      ..deleted = deleted ?? this.deleted
      ..rowindex = rowindex ?? this.rowindex
      ..barcode = barcode ?? this.barcode
      ..ptypeArea = ptypeArea ?? this.ptypeArea
      ..memo = memo ?? this.memo
      ..createType = createType ?? this.createType
      ..costMode = costMode ?? this.costMode
      ..taxNumber = taxNumber ?? this.taxNumber
      ..costPrice = costPrice ?? this.costPrice
      ..supplyInfo = supplyInfo ?? this.supplyInfo
      ..brandId = brandId ?? this.brandId
      ..ktypeLimit = ktypeLimit ?? this.ktypeLimit
      ..snenabled = snenabled ?? this.snenabled
      ..propenabled = propenabled ?? this.propenabled
      ..batchenabled = batchenabled ?? this.batchenabled
      ..protectDays = protectDays ?? this.protectDays
      ..protectDaysUnit = protectDaysUnit ?? this.protectDaysUnit
      ..protectWarndays = protectWarndays ?? this.protectWarndays
      ..weight = weight ?? this.weight
      ..weightUnit = weightUnit ?? this.weightUnit
      ..lithiumBattery = lithiumBattery ?? this.lithiumBattery
      ..solid = solid ?? this.solid
      ..difficultyLevel = difficultyLevel ?? this.difficultyLevel
      ..weighted = weighted ?? this.weighted
      ..retailDefaultUnit = retailDefaultUnit ?? this.retailDefaultUnit
      ..saleDefaultUnit = saleDefaultUnit ?? this.saleDefaultUnit
      ..purchaseDefaultUnit = purchaseDefaultUnit ?? this.purchaseDefaultUnit
      ..stockDefaultUnit = stockDefaultUnit ?? this.stockDefaultUnit
      ..ptypeLength = ptypeLength ?? this.ptypeLength
      ..ptypeWidth = ptypeWidth ?? this.ptypeWidth
      ..ptypeHeight = ptypeHeight ?? this.ptypeHeight
      ..lengthUnit = lengthUnit ?? this.lengthUnit
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..batchlifeId = batchlifeId ?? this.batchlifeId
      ..stockQty = stockQty ?? this.stockQty
      ..stockSubQty = stockSubQty ?? this.stockSubQty
      ..checkQty = checkQty ?? this.checkQty
      ..checkSubQty = checkSubQty ?? this.checkSubQty
      ..adjustQty = adjustQty ?? this.adjustQty
      ..adjustSubQty = adjustSubQty ?? this.adjustSubQty
      ..checkTypeStr = checkTypeStr ?? this.checkTypeStr
      ..checkTime = checkTime ?? this.checkTime
      ..checked = checked ?? this.checked
      ..subChecked = subChecked ?? this.subChecked
      ..propvalueName1 = propvalueName1 ?? this.propvalueName1
      ..propvalueName2 = propvalueName2 ?? this.propvalueName2
      ..propvalueName3 = propvalueName3 ?? this.propvalueName3
      ..propvalueName4 = propvalueName4 ?? this.propvalueName4
      ..propvalueName5 = propvalueName5 ?? this.propvalueName5
      ..propvalueName6 = propvalueName6 ?? this.propvalueName6
      ..propvalueAll = propvalueAll ?? this.propvalueAll
      ..proDateStr = proDateStr ?? this.proDateStr
      ..expDateStr = expDateStr ?? this.expDateStr
      ..selectchecked = selectchecked ?? this.selectchecked
      ..snno = snno ?? this.snno
      ..costId = costId ?? this.costId
      ..positionStrText = positionStrText ?? this.positionStrText
      ..positionList = positionList ?? this.positionList
      ..inoutId = inoutId ?? this.inoutId;
  }
}