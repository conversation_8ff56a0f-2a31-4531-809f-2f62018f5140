
import '../../stockcheck/request/stock_check_infos_request_entity.dart';
import 'base/json_convert_content.dart';

StockCheckInfosRequestEntity $StockCheckInfosRequestEntityFromJson(
    Map<String, dynamic> json) {
  final StockCheckInfosRequestEntity stockCheckInfosRequestEntity = StockCheckInfosRequestEntity();
  final int? pageSize = jsonConvert.convert<int>(json['pageSize']);
  if (pageSize != null) {
    stockCheckInfosRequestEntity.pageSize = pageSize;
  }
  final int? pageIndex = jsonConvert.convert<int>(json['pageIndex']);
  if (pageIndex != null) {
    stockCheckInfosRequestEntity.pageIndex = pageIndex;
  }
  final StockCheckInfosRequestQueryParams? queryParams = jsonConvert.convert<
      StockCheckInfosRequestQueryParams>(json['queryParams']);
  if (queryParams != null) {
    stockCheckInfosRequestEntity.queryParams = queryParams;
  }
  final dynamic sorts = json['sorts'];
  if (sorts != null) {
    stockCheckInfosRequestEntity.sorts = sorts;
  }
  return stockCheckInfosRequestEntity;
}

Map<String, dynamic> $StockCheckInfosRequestEntityToJson(
    StockCheckInfosRequestEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['pageSize'] = entity.pageSize;
  data['pageIndex'] = entity.pageIndex;
  data['queryParams'] = entity.queryParams.toJson();
  data['sorts'] = entity.sorts;
  return data;
}

extension StockCheckInfosRequestEntityExtension on StockCheckInfosRequestEntity {
  StockCheckInfosRequestEntity copyWith({
    int? pageSize,
    int? pageIndex,
    StockCheckInfosRequestQueryParams? queryParams,
    dynamic sorts,
  }) {
    return StockCheckInfosRequestEntity()
      ..pageSize = pageSize ?? this.pageSize
      ..pageIndex = pageIndex ?? this.pageIndex
      ..queryParams = queryParams ?? this.queryParams
      ..sorts = sorts ?? this.sorts;
  }
}

StockCheckInfosRequestQueryParams $StockCheckInfosRequestQueryParamsFromJson(
    Map<String, dynamic> json) {
  final StockCheckInfosRequestQueryParams stockCheckInfosRequestQueryParams = StockCheckInfosRequestQueryParams();
  final String? checkId = jsonConvert.convert<String>(json['checkId']);
  if (checkId != null) {
    stockCheckInfosRequestQueryParams.checkId = checkId;
  }
  final String? ktypeId = jsonConvert.convert<String>(json['ktypeId']);
  if (ktypeId != null) {
    stockCheckInfosRequestQueryParams.ktypeId = ktypeId;
  }
  final int? checkType = jsonConvert.convert<int>(json['checkType']);
  if (checkType != null) {
    stockCheckInfosRequestQueryParams.checkType = checkType;
  }
  final bool? checkScanPtype = jsonConvert.convert<bool>(
      json['checkScanPtype']);
  if (checkScanPtype != null) {
    stockCheckInfosRequestQueryParams.checkScanPtype = checkScanPtype;
  }
  final int? viewMode = jsonConvert.convert<int>(json['viewMode']);
  if (viewMode != null) {
    stockCheckInfosRequestQueryParams.viewMode = viewMode;
  }
  final List<dynamic>? gridFilter = (json['gridFilter'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (gridFilter != null) {
    stockCheckInfosRequestQueryParams.gridFilter = gridFilter;
  }
  final bool? checkAllStock = jsonConvert.convert<bool>(json['checkAllStock']);
  if (checkAllStock != null) {
    stockCheckInfosRequestQueryParams.checkAllStock = checkAllStock;
  }
  final String? filterValue = jsonConvert.convert<String>(json['filterValue']);
  if (filterValue != null) {
    stockCheckInfosRequestQueryParams.filterValue = filterValue;
  }
  return stockCheckInfosRequestQueryParams;
}

Map<String, dynamic> $StockCheckInfosRequestQueryParamsToJson(
    StockCheckInfosRequestQueryParams entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['checkId'] = entity.checkId;
  data['ktypeId'] = entity.ktypeId;
  data['checkType'] = entity.checkType;
  data['checkScanPtype'] = entity.checkScanPtype;
  data['viewMode'] = entity.viewMode;
  data['gridFilter'] = entity.gridFilter;
  data['checkAllStock'] = entity.checkAllStock;
  data['filterValue'] = entity.filterValue;
  return data;
}

extension StockCheckInfosRequestQueryParamsExtension on StockCheckInfosRequestQueryParams {
  StockCheckInfosRequestQueryParams copyWith({
    String? checkId,
    String? ktypeId,
    int? checkType,
    bool? checkScanPtype,
    int? viewMode,
    List<dynamic>? gridFilter,
    bool? checkAllStock,
    String? filterValue,
  }) {
    return StockCheckInfosRequestQueryParams()
      ..checkId = checkId ?? this.checkId
      ..ktypeId = ktypeId ?? this.ktypeId
      ..checkType = checkType ?? this.checkType
      ..checkScanPtype = checkScanPtype ?? this.checkScanPtype
      ..viewMode = viewMode ?? this.viewMode
      ..gridFilter = gridFilter ?? this.gridFilter
      ..checkAllStock = checkAllStock ?? this.checkAllStock
      ..filterValue = filterValue ?? this.filterValue;
  }
}