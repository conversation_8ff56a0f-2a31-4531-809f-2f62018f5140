
import '../../stockcheck/entity/stock_check_result_entity.dart';
import 'base/json_convert_content.dart';

StockCheckResultEntity $StockCheckResultEntityFromJson(
    Map<String, dynamic> json) {
  final StockCheckResultEntity stockCheckResultEntity = StockCheckResultEntity();
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    stockCheckResultEntity.message = message;
  }
  final dynamic checkId = json['checkId'];
  if (checkId != null) {
    stockCheckResultEntity.checkId = checkId;
  }
  final dynamic detailId = json['detailId'];
  if (detailId != null) {
    stockCheckResultEntity.detailId = detailId;
  }
  final List<
      StockCheckResultRepeatDetails>? repeatDetails = (json['repeatDetails'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<StockCheckResultRepeatDetails>(
          e) as StockCheckResultRepeatDetails).toList();
  if (repeatDetails != null) {
    stockCheckResultEntity.repeatDetails = repeatDetails;
  }
  return stockCheckResultEntity;
}

Map<String, dynamic> $StockCheckResultEntityToJson(
    StockCheckResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['message'] = entity.message;
  data['checkId'] = entity.checkId;
  data['detailId'] = entity.detailId;
  data['repeatDetails'] = entity.repeatDetails?.map((v) => v.toJson()).toList();
  return data;
}

extension StockCheckResultEntityExtension on StockCheckResultEntity {
  StockCheckResultEntity copyWith({
    String? message,
    dynamic checkId,
    dynamic detailId,
    List<StockCheckResultRepeatDetails>? repeatDetails,
  }) {
    return StockCheckResultEntity()
      ..message = message ?? this.message
      ..checkId = checkId ?? this.checkId
      ..detailId = detailId ?? this.detailId
      ..repeatDetails = repeatDetails ?? this.repeatDetails;
  }
}

StockCheckResultRepeatDetails $StockCheckResultRepeatDetailsFromJson(
    Map<String, dynamic> json) {
  final StockCheckResultRepeatDetails stockCheckResultRepeatDetails = StockCheckResultRepeatDetails();
  final dynamic ptypeId = json['ptypeId'];
  if (ptypeId != null) {
    stockCheckResultRepeatDetails.ptypeId = ptypeId;
  }
  final String? picUrl = jsonConvert.convert<String>(json['picUrl']);
  if (picUrl != null) {
    stockCheckResultRepeatDetails.picUrl = picUrl;
  }
  final String? usercode = jsonConvert.convert<String>(json['usercode']);
  if (usercode != null) {
    stockCheckResultRepeatDetails.usercode = usercode;
  }
  final dynamic pFullname = json['pFullname'];
  if (pFullname != null) {
    stockCheckResultRepeatDetails.pFullname = pFullname;
  }
  final dynamic shortname = json['shortname'];
  if (shortname != null) {
    stockCheckResultRepeatDetails.shortname = shortname;
  }
  final dynamic brandName = json['brandName'];
  if (brandName != null) {
    stockCheckResultRepeatDetails.brandName = brandName;
  }
  final String? standard = jsonConvert.convert<String>(json['standard']);
  if (standard != null) {
    stockCheckResultRepeatDetails.standard = standard;
  }
  final String? ptypeType = jsonConvert.convert<String>(json['ptypeType']);
  if (ptypeType != null) {
    stockCheckResultRepeatDetails.ptypeType = ptypeType;
  }
  final String? unitName = jsonConvert.convert<String>(json['unitName']);
  if (unitName != null) {
    stockCheckResultRepeatDetails.unitName = unitName;
  }
  final dynamic subUnitName = json['subUnitName'];
  if (subUnitName != null) {
    stockCheckResultRepeatDetails.subUnitName = subUnitName;
  }
  final dynamic ptypeMemo = json['ptypeMemo'];
  if (ptypeMemo != null) {
    stockCheckResultRepeatDetails.ptypeMemo = ptypeMemo;
  }
  final dynamic fullbarcode = json['fullbarcode'];
  if (fullbarcode != null) {
    stockCheckResultRepeatDetails.fullbarcode = fullbarcode;
  }
  final String? skuId = jsonConvert.convert<String>(json['skuId']);
  if (skuId != null) {
    stockCheckResultRepeatDetails.skuId = skuId;
  }
  final String? xcode = jsonConvert.convert<String>(json['xcode']);
  if (xcode != null) {
    stockCheckResultRepeatDetails.xcode = xcode;
  }
  final dynamic taxRate = json['taxRate'];
  if (taxRate != null) {
    stockCheckResultRepeatDetails.taxRate = taxRate;
  }
  final int? skuPrice = jsonConvert.convert<int>(json['skuPrice']);
  if (skuPrice != null) {
    stockCheckResultRepeatDetails.skuPrice = skuPrice;
  }
  final dynamic pcategory = json['pcategory'];
  if (pcategory != null) {
    stockCheckResultRepeatDetails.pcategory = pcategory;
  }
  final dynamic industryCategory = json['industryCategory'];
  if (industryCategory != null) {
    stockCheckResultRepeatDetails.industryCategory = industryCategory;
  }
  final dynamic parName1 = json['parName1'];
  if (parName1 != null) {
    stockCheckResultRepeatDetails.parName1 = parName1;
  }
  final dynamic parName2 = json['parName2'];
  if (parName2 != null) {
    stockCheckResultRepeatDetails.parName2 = parName2;
  }
  final dynamic parName3 = json['parName3'];
  if (parName3 != null) {
    stockCheckResultRepeatDetails.parName3 = parName3;
  }
  final dynamic parName4 = json['parName4'];
  if (parName4 != null) {
    stockCheckResultRepeatDetails.parName4 = parName4;
  }
  final dynamic parName5 = json['parName5'];
  if (parName5 != null) {
    stockCheckResultRepeatDetails.parName5 = parName5;
  }
  final dynamic ptypeWeight = json['ptypeWeight'];
  if (ptypeWeight != null) {
    stockCheckResultRepeatDetails.ptypeWeight = ptypeWeight;
  }
  final dynamic unitRelation = json['unitRelation'];
  if (unitRelation != null) {
    stockCheckResultRepeatDetails.unitRelation = unitRelation;
  }
  final dynamic picUrls = json['picUrls'];
  if (picUrls != null) {
    stockCheckResultRepeatDetails.picUrls = picUrls;
  }
  final int? skuName = jsonConvert.convert<int>(json['skuName']);
  if (skuName != null) {
    stockCheckResultRepeatDetails.skuName = skuName;
  }
  final String? propNames = jsonConvert.convert<String>(json['propNames']);
  if (propNames != null) {
    stockCheckResultRepeatDetails.propNames = propNames;
  }
  final String? propValues = jsonConvert.convert<String>(json['propValues']);
  if (propValues != null) {
    stockCheckResultRepeatDetails.propValues = propValues;
  }
  final dynamic propValueName1 = json['propValueName1'];
  if (propValueName1 != null) {
    stockCheckResultRepeatDetails.propValueName1 = propValueName1;
  }
  final dynamic propValueName2 = json['propValueName2'];
  if (propValueName2 != null) {
    stockCheckResultRepeatDetails.propValueName2 = propValueName2;
  }
  final String? batchno = jsonConvert.convert<String>(json['batchno']);
  if (batchno != null) {
    stockCheckResultRepeatDetails.batchno = batchno;
  }
  final dynamic produceDate = json['produceDate'];
  if (produceDate != null) {
    stockCheckResultRepeatDetails.produceDate = produceDate;
  }
  final dynamic expireDate = json['expireDate'];
  if (expireDate != null) {
    stockCheckResultRepeatDetails.expireDate = expireDate;
  }
  final dynamic defProduceDate = json['defProduceDate'];
  if (defProduceDate != null) {
    stockCheckResultRepeatDetails.defProduceDate = defProduceDate;
  }
  final dynamic defExpireDate = json['defExpireDate'];
  if (defExpireDate != null) {
    stockCheckResultRepeatDetails.defExpireDate = defExpireDate;
  }
  final dynamic customHead01 = json['customHead01'];
  if (customHead01 != null) {
    stockCheckResultRepeatDetails.customHead01 = customHead01;
  }
  final dynamic customHead02 = json['customHead02'];
  if (customHead02 != null) {
    stockCheckResultRepeatDetails.customHead02 = customHead02;
  }
  final dynamic customHead03 = json['customHead03'];
  if (customHead03 != null) {
    stockCheckResultRepeatDetails.customHead03 = customHead03;
  }
  final dynamic customHead04 = json['customHead04'];
  if (customHead04 != null) {
    stockCheckResultRepeatDetails.customHead04 = customHead04;
  }
  final dynamic customHead05 = json['customHead05'];
  if (customHead05 != null) {
    stockCheckResultRepeatDetails.customHead05 = customHead05;
  }
  final dynamic customHead06 = json['customHead06'];
  if (customHead06 != null) {
    stockCheckResultRepeatDetails.customHead06 = customHead06;
  }
  final dynamic customHead07 = json['customHead07'];
  if (customHead07 != null) {
    stockCheckResultRepeatDetails.customHead07 = customHead07;
  }
  final dynamic customHead08 = json['customHead08'];
  if (customHead08 != null) {
    stockCheckResultRepeatDetails.customHead08 = customHead08;
  }
  final dynamic customHead09 = json['customHead09'];
  if (customHead09 != null) {
    stockCheckResultRepeatDetails.customHead09 = customHead09;
  }
  final dynamic customHead10 = json['customHead10'];
  if (customHead10 != null) {
    stockCheckResultRepeatDetails.customHead10 = customHead10;
  }
  final dynamic customHead11 = json['customHead11'];
  if (customHead11 != null) {
    stockCheckResultRepeatDetails.customHead11 = customHead11;
  }
  final dynamic customHead12 = json['customHead12'];
  if (customHead12 != null) {
    stockCheckResultRepeatDetails.customHead12 = customHead12;
  }
  final dynamic customHead13 = json['customHead13'];
  if (customHead13 != null) {
    stockCheckResultRepeatDetails.customHead13 = customHead13;
  }
  final dynamic customHead14 = json['customHead14'];
  if (customHead14 != null) {
    stockCheckResultRepeatDetails.customHead14 = customHead14;
  }
  final dynamic subUnit = json['subUnit'];
  if (subUnit != null) {
    stockCheckResultRepeatDetails.subUnit = subUnit;
  }
  final dynamic subQty = json['subQty'];
  if (subQty != null) {
    stockCheckResultRepeatDetails.subQty = subQty;
  }
  final dynamic subInQty = json['subInQty'];
  if (subInQty != null) {
    stockCheckResultRepeatDetails.subInQty = subInQty;
  }
  final dynamic subOutQty = json['subOutQty'];
  if (subOutQty != null) {
    stockCheckResultRepeatDetails.subOutQty = subOutQty;
  }
  final dynamic subInOutQty = json['subInOutQty'];
  if (subInOutQty != null) {
    stockCheckResultRepeatDetails.subInOutQty = subInOutQty;
  }
  final dynamic subInStockQty = json['subInStockQty'];
  if (subInStockQty != null) {
    stockCheckResultRepeatDetails.subInStockQty = subInStockQty;
  }
  final dynamic subInOutPercent = json['subInOutPercent'];
  if (subInOutPercent != null) {
    stockCheckResultRepeatDetails.subInOutPercent = subInOutPercent;
  }
  final dynamic subDiffQty = json['subDiffQty'];
  if (subDiffQty != null) {
    stockCheckResultRepeatDetails.subDiffQty = subDiffQty;
  }
  final bool? hasPerm = jsonConvert.convert<bool>(json['hasPerm']);
  if (hasPerm != null) {
    stockCheckResultRepeatDetails.hasPerm = hasPerm;
  }
  final dynamic baseUnitName = json['baseUnitName'];
  if (baseUnitName != null) {
    stockCheckResultRepeatDetails.baseUnitName = baseUnitName;
  }
  final String? unitxqty = jsonConvert.convert<String>(json['unitxqty']);
  if (unitxqty != null) {
    stockCheckResultRepeatDetails.unitxqty = unitxqty;
  }
  final dynamic ptypeUnits = json['ptypeUnits'];
  if (ptypeUnits != null) {
    stockCheckResultRepeatDetails.ptypeUnits = ptypeUnits;
  }
  final List<dynamic>? mySnList = (json['mySnList'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (mySnList != null) {
    stockCheckResultRepeatDetails.mySnList = mySnList;
  }
  final List<dynamic>? checkSnList = (json['checkSnList'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (checkSnList != null) {
    stockCheckResultRepeatDetails.checkSnList = checkSnList;
  }
  final String? snnoStrButton = jsonConvert.convert<String>(
      json['snnoStrButton']);
  if (snnoStrButton != null) {
    stockCheckResultRepeatDetails.snnoStrButton = snnoStrButton;
  }
  final int? unitCode = jsonConvert.convert<int>(json['unitCode']);
  if (unitCode != null) {
    stockCheckResultRepeatDetails.unitCode = unitCode;
  }
  final dynamic unitRate = json['unitRate'];
  if (unitRate != null) {
    stockCheckResultRepeatDetails.unitRate = unitRate;
  }
  final String? unitId = jsonConvert.convert<String>(json['unitId']);
  if (unitId != null) {
    stockCheckResultRepeatDetails.unitId = unitId;
  }
  final String? detailId = jsonConvert.convert<String>(json['detailId']);
  if (detailId != null) {
    stockCheckResultRepeatDetails.detailId = detailId;
  }
  final String? stockCheckId = jsonConvert.convert<String>(
      json['stockCheckId']);
  if (stockCheckId != null) {
    stockCheckResultRepeatDetails.stockCheckId = stockCheckId;
  }
  final int? batchPrice = jsonConvert.convert<int>(json['batchPrice']);
  if (batchPrice != null) {
    stockCheckResultRepeatDetails.batchPrice = batchPrice;
  }
  final int? batchQty = jsonConvert.convert<int>(json['batchQty']);
  if (batchQty != null) {
    stockCheckResultRepeatDetails.batchQty = batchQty;
  }
  final int? batchLifeQty = jsonConvert.convert<int>(json['batchLifeQty']);
  if (batchLifeQty != null) {
    stockCheckResultRepeatDetails.batchLifeQty = batchLifeQty;
  }
  final dynamic stockPrice = json['stockPrice'];
  if (stockPrice != null) {
    stockCheckResultRepeatDetails.stockPrice = stockPrice;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    stockCheckResultRepeatDetails.id = id;
  }
  final dynamic profileId = json['profileId'];
  if (profileId != null) {
    stockCheckResultRepeatDetails.profileId = profileId;
  }
  final String? fullname = jsonConvert.convert<String>(json['fullname']);
  if (fullname != null) {
    stockCheckResultRepeatDetails.fullname = fullname;
  }
  final dynamic shortName = json['shortName'];
  if (shortName != null) {
    stockCheckResultRepeatDetails.shortName = shortName;
  }
  final dynamic namePy = json['namePy'];
  if (namePy != null) {
    stockCheckResultRepeatDetails.namePy = namePy;
  }
  final dynamic classed = json['classed'];
  if (classed != null) {
    stockCheckResultRepeatDetails.classed = classed;
  }
  final dynamic stoped = json['stoped'];
  if (stoped != null) {
    stockCheckResultRepeatDetails.stoped = stoped;
  }
  final dynamic deleted = json['deleted'];
  if (deleted != null) {
    stockCheckResultRepeatDetails.deleted = deleted;
  }
  final dynamic rowindex = json['rowindex'];
  if (rowindex != null) {
    stockCheckResultRepeatDetails.rowindex = rowindex;
  }
  final String? barcode = jsonConvert.convert<String>(json['barcode']);
  if (barcode != null) {
    stockCheckResultRepeatDetails.barcode = barcode;
  }
  final String? ptypeArea = jsonConvert.convert<String>(json['ptypeArea']);
  if (ptypeArea != null) {
    stockCheckResultRepeatDetails.ptypeArea = ptypeArea;
  }
  final dynamic memo = json['memo'];
  if (memo != null) {
    stockCheckResultRepeatDetails.memo = memo;
  }
  final dynamic createType = json['createType'];
  if (createType != null) {
    stockCheckResultRepeatDetails.createType = createType;
  }
  final int? costMode = jsonConvert.convert<int>(json['costMode']);
  if (costMode != null) {
    stockCheckResultRepeatDetails.costMode = costMode;
  }
  final dynamic taxNumber = json['taxNumber'];
  if (taxNumber != null) {
    stockCheckResultRepeatDetails.taxNumber = taxNumber;
  }
  final dynamic costPrice = json['costPrice'];
  if (costPrice != null) {
    stockCheckResultRepeatDetails.costPrice = costPrice;
  }
  final dynamic supplyInfo = json['supplyInfo'];
  if (supplyInfo != null) {
    stockCheckResultRepeatDetails.supplyInfo = supplyInfo;
  }
  final dynamic brandId = json['brandId'];
  if (brandId != null) {
    stockCheckResultRepeatDetails.brandId = brandId;
  }
  final dynamic ktypeLimit = json['ktypeLimit'];
  if (ktypeLimit != null) {
    stockCheckResultRepeatDetails.ktypeLimit = ktypeLimit;
  }
  final int? snenabled = jsonConvert.convert<int>(json['snenabled']);
  if (snenabled != null) {
    stockCheckResultRepeatDetails.snenabled = snenabled;
  }
  final bool? propenabled = jsonConvert.convert<bool>(json['propenabled']);
  if (propenabled != null) {
    stockCheckResultRepeatDetails.propenabled = propenabled;
  }
  final bool? batchenabled = jsonConvert.convert<bool>(json['batchenabled']);
  if (batchenabled != null) {
    stockCheckResultRepeatDetails.batchenabled = batchenabled;
  }
  final int? protectDays = jsonConvert.convert<int>(json['protectDays']);
  if (protectDays != null) {
    stockCheckResultRepeatDetails.protectDays = protectDays;
  }
  final int? protectDaysUnit = jsonConvert.convert<int>(
      json['protectDaysUnit']);
  if (protectDaysUnit != null) {
    stockCheckResultRepeatDetails.protectDaysUnit = protectDaysUnit;
  }
  final dynamic protectWarndays = json['protectWarndays'];
  if (protectWarndays != null) {
    stockCheckResultRepeatDetails.protectWarndays = protectWarndays;
  }
  final dynamic weight = json['weight'];
  if (weight != null) {
    stockCheckResultRepeatDetails.weight = weight;
  }
  final dynamic weightUnit = json['weightUnit'];
  if (weightUnit != null) {
    stockCheckResultRepeatDetails.weightUnit = weightUnit;
  }
  final dynamic lithiumBattery = json['lithiumBattery'];
  if (lithiumBattery != null) {
    stockCheckResultRepeatDetails.lithiumBattery = lithiumBattery;
  }
  final dynamic solid = json['solid'];
  if (solid != null) {
    stockCheckResultRepeatDetails.solid = solid;
  }
  final dynamic difficultyLevel = json['difficultyLevel'];
  if (difficultyLevel != null) {
    stockCheckResultRepeatDetails.difficultyLevel = difficultyLevel;
  }
  final dynamic weighted = json['weighted'];
  if (weighted != null) {
    stockCheckResultRepeatDetails.weighted = weighted;
  }
  final dynamic retailDefaultUnit = json['retailDefaultUnit'];
  if (retailDefaultUnit != null) {
    stockCheckResultRepeatDetails.retailDefaultUnit = retailDefaultUnit;
  }
  final dynamic saleDefaultUnit = json['saleDefaultUnit'];
  if (saleDefaultUnit != null) {
    stockCheckResultRepeatDetails.saleDefaultUnit = saleDefaultUnit;
  }
  final dynamic purchaseDefaultUnit = json['purchaseDefaultUnit'];
  if (purchaseDefaultUnit != null) {
    stockCheckResultRepeatDetails.purchaseDefaultUnit = purchaseDefaultUnit;
  }
  final dynamic stockDefaultUnit = json['stockDefaultUnit'];
  if (stockDefaultUnit != null) {
    stockCheckResultRepeatDetails.stockDefaultUnit = stockDefaultUnit;
  }
  final dynamic ptypeLength = json['ptypeLength'];
  if (ptypeLength != null) {
    stockCheckResultRepeatDetails.ptypeLength = ptypeLength;
  }
  final dynamic ptypeWidth = json['ptypeWidth'];
  if (ptypeWidth != null) {
    stockCheckResultRepeatDetails.ptypeWidth = ptypeWidth;
  }
  final dynamic ptypeHeight = json['ptypeHeight'];
  if (ptypeHeight != null) {
    stockCheckResultRepeatDetails.ptypeHeight = ptypeHeight;
  }
  final dynamic lengthUnit = json['lengthUnit'];
  if (lengthUnit != null) {
    stockCheckResultRepeatDetails.lengthUnit = lengthUnit;
  }
  final dynamic createTime = json['createTime'];
  if (createTime != null) {
    stockCheckResultRepeatDetails.createTime = createTime;
  }
  final dynamic updateTime = json['updateTime'];
  if (updateTime != null) {
    stockCheckResultRepeatDetails.updateTime = updateTime;
  }
  final String? batchlifeId = jsonConvert.convert<String>(json['batchlifeId']);
  if (batchlifeId != null) {
    stockCheckResultRepeatDetails.batchlifeId = batchlifeId;
  }
  final int? stockQty = jsonConvert.convert<int>(json['stockQty']);
  if (stockQty != null) {
    stockCheckResultRepeatDetails.stockQty = stockQty;
  }
  final int? stockSubQty = jsonConvert.convert<int>(json['stockSubQty']);
  if (stockSubQty != null) {
    stockCheckResultRepeatDetails.stockSubQty = stockSubQty;
  }
  final dynamic checkQty = json['checkQty'];
  if (checkQty != null) {
    stockCheckResultRepeatDetails.checkQty = checkQty;
  }
  final dynamic checkSubQty = json['checkSubQty'];
  if (checkSubQty != null) {
    stockCheckResultRepeatDetails.checkSubQty = checkSubQty;
  }
  final dynamic adjustQty = json['adjustQty'];
  if (adjustQty != null) {
    stockCheckResultRepeatDetails.adjustQty = adjustQty;
  }
  final dynamic adjustSubQty = json['adjustSubQty'];
  if (adjustSubQty != null) {
    stockCheckResultRepeatDetails.adjustSubQty = adjustSubQty;
  }
  final String? checkTypeStr = jsonConvert.convert<String>(
      json['checkTypeStr']);
  if (checkTypeStr != null) {
    stockCheckResultRepeatDetails.checkTypeStr = checkTypeStr;
  }
  final String? checkTime = jsonConvert.convert<String>(json['checkTime']);
  if (checkTime != null) {
    stockCheckResultRepeatDetails.checkTime = checkTime;
  }
  final bool? checked = jsonConvert.convert<bool>(json['checked']);
  if (checked != null) {
    stockCheckResultRepeatDetails.checked = checked;
  }
  final bool? subChecked = jsonConvert.convert<bool>(json['subChecked']);
  if (subChecked != null) {
    stockCheckResultRepeatDetails.subChecked = subChecked;
  }
  final String? propvalueName1 = jsonConvert.convert<String>(
      json['propvalueName1']);
  if (propvalueName1 != null) {
    stockCheckResultRepeatDetails.propvalueName1 = propvalueName1;
  }
  final String? propvalueName2 = jsonConvert.convert<String>(
      json['propvalueName2']);
  if (propvalueName2 != null) {
    stockCheckResultRepeatDetails.propvalueName2 = propvalueName2;
  }
  final dynamic propvalueName3 = json['propvalueName3'];
  if (propvalueName3 != null) {
    stockCheckResultRepeatDetails.propvalueName3 = propvalueName3;
  }
  final dynamic propvalueName4 = json['propvalueName4'];
  if (propvalueName4 != null) {
    stockCheckResultRepeatDetails.propvalueName4 = propvalueName4;
  }
  final dynamic propvalueName5 = json['propvalueName5'];
  if (propvalueName5 != null) {
    stockCheckResultRepeatDetails.propvalueName5 = propvalueName5;
  }
  final dynamic propvalueName6 = json['propvalueName6'];
  if (propvalueName6 != null) {
    stockCheckResultRepeatDetails.propvalueName6 = propvalueName6;
  }
  final String? propvalueAll = jsonConvert.convert<String>(
      json['propvalueAll']);
  if (propvalueAll != null) {
    stockCheckResultRepeatDetails.propvalueAll = propvalueAll;
  }
  final String? proDateStr = jsonConvert.convert<String>(json['proDateStr']);
  if (proDateStr != null) {
    stockCheckResultRepeatDetails.proDateStr = proDateStr;
  }
  final String? expDateStr = jsonConvert.convert<String>(json['expDateStr']);
  if (expDateStr != null) {
    stockCheckResultRepeatDetails.expDateStr = expDateStr;
  }
  final bool? selectchecked = jsonConvert.convert<bool>(json['selectchecked']);
  if (selectchecked != null) {
    stockCheckResultRepeatDetails.selectchecked = selectchecked;
  }
  final dynamic snno = json['snno'];
  if (snno != null) {
    stockCheckResultRepeatDetails.snno = snno;
  }
  final String? costId = jsonConvert.convert<String>(json['costId']);
  if (costId != null) {
    stockCheckResultRepeatDetails.costId = costId;
  }
  final dynamic positionStrText = json['positionStrText'];
  if (positionStrText != null) {
    stockCheckResultRepeatDetails.positionStrText = positionStrText;
  }
  final dynamic positionList = json['positionList'];
  if (positionList != null) {
    stockCheckResultRepeatDetails.positionList = positionList;
  }
  final dynamic inoutId = json['inoutId'];
  if (inoutId != null) {
    stockCheckResultRepeatDetails.inoutId = inoutId;
  }
  return stockCheckResultRepeatDetails;
}

Map<String, dynamic> $StockCheckResultRepeatDetailsToJson(
    StockCheckResultRepeatDetails entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['ptypeId'] = entity.ptypeId;
  data['picUrl'] = entity.picUrl;
  data['usercode'] = entity.usercode;
  data['pFullname'] = entity.pFullname;
  data['shortname'] = entity.shortname;
  data['brandName'] = entity.brandName;
  data['standard'] = entity.standard;
  data['ptypeType'] = entity.ptypeType;
  data['unitName'] = entity.unitName;
  data['subUnitName'] = entity.subUnitName;
  data['ptypeMemo'] = entity.ptypeMemo;
  data['fullbarcode'] = entity.fullbarcode;
  data['skuId'] = entity.skuId;
  data['xcode'] = entity.xcode;
  data['taxRate'] = entity.taxRate;
  data['skuPrice'] = entity.skuPrice;
  data['pcategory'] = entity.pcategory;
  data['industryCategory'] = entity.industryCategory;
  data['parName1'] = entity.parName1;
  data['parName2'] = entity.parName2;
  data['parName3'] = entity.parName3;
  data['parName4'] = entity.parName4;
  data['parName5'] = entity.parName5;
  data['ptypeWeight'] = entity.ptypeWeight;
  data['unitRelation'] = entity.unitRelation;
  data['picUrls'] = entity.picUrls;
  data['skuName'] = entity.skuName;
  data['propNames'] = entity.propNames;
  data['propValues'] = entity.propValues;
  data['propValueName1'] = entity.propValueName1;
  data['propValueName2'] = entity.propValueName2;
  data['batchno'] = entity.batchno;
  data['produceDate'] = entity.produceDate;
  data['expireDate'] = entity.expireDate;
  data['defProduceDate'] = entity.defProduceDate;
  data['defExpireDate'] = entity.defExpireDate;
  data['customHead01'] = entity.customHead01;
  data['customHead02'] = entity.customHead02;
  data['customHead03'] = entity.customHead03;
  data['customHead04'] = entity.customHead04;
  data['customHead05'] = entity.customHead05;
  data['customHead06'] = entity.customHead06;
  data['customHead07'] = entity.customHead07;
  data['customHead08'] = entity.customHead08;
  data['customHead09'] = entity.customHead09;
  data['customHead10'] = entity.customHead10;
  data['customHead11'] = entity.customHead11;
  data['customHead12'] = entity.customHead12;
  data['customHead13'] = entity.customHead13;
  data['customHead14'] = entity.customHead14;
  data['subUnit'] = entity.subUnit;
  data['subQty'] = entity.subQty;
  data['subInQty'] = entity.subInQty;
  data['subOutQty'] = entity.subOutQty;
  data['subInOutQty'] = entity.subInOutQty;
  data['subInStockQty'] = entity.subInStockQty;
  data['subInOutPercent'] = entity.subInOutPercent;
  data['subDiffQty'] = entity.subDiffQty;
  data['hasPerm'] = entity.hasPerm;
  data['baseUnitName'] = entity.baseUnitName;
  data['unitxqty'] = entity.unitxqty;
  data['ptypeUnits'] = entity.ptypeUnits;
  data['mySnList'] = entity.mySnList;
  data['checkSnList'] = entity.checkSnList;
  data['snnoStrButton'] = entity.snnoStrButton;
  data['unitCode'] = entity.unitCode;
  data['unitRate'] = entity.unitRate;
  data['unitId'] = entity.unitId;
  data['detailId'] = entity.detailId;
  data['stockCheckId'] = entity.stockCheckId;
  data['batchPrice'] = entity.batchPrice;
  data['batchQty'] = entity.batchQty;
  data['batchLifeQty'] = entity.batchLifeQty;
  data['stockPrice'] = entity.stockPrice;
  data['id'] = entity.id;
  data['profileId'] = entity.profileId;
  data['fullname'] = entity.fullname;
  data['shortName'] = entity.shortName;
  data['namePy'] = entity.namePy;
  data['classed'] = entity.classed;
  data['stoped'] = entity.stoped;
  data['deleted'] = entity.deleted;
  data['rowindex'] = entity.rowindex;
  data['barcode'] = entity.barcode;
  data['ptypeArea'] = entity.ptypeArea;
  data['memo'] = entity.memo;
  data['createType'] = entity.createType;
  data['costMode'] = entity.costMode;
  data['taxNumber'] = entity.taxNumber;
  data['costPrice'] = entity.costPrice;
  data['supplyInfo'] = entity.supplyInfo;
  data['brandId'] = entity.brandId;
  data['ktypeLimit'] = entity.ktypeLimit;
  data['snenabled'] = entity.snenabled;
  data['propenabled'] = entity.propenabled;
  data['batchenabled'] = entity.batchenabled;
  data['protectDays'] = entity.protectDays;
  data['protectDaysUnit'] = entity.protectDaysUnit;
  data['protectWarndays'] = entity.protectWarndays;
  data['weight'] = entity.weight;
  data['weightUnit'] = entity.weightUnit;
  data['lithiumBattery'] = entity.lithiumBattery;
  data['solid'] = entity.solid;
  data['difficultyLevel'] = entity.difficultyLevel;
  data['weighted'] = entity.weighted;
  data['retailDefaultUnit'] = entity.retailDefaultUnit;
  data['saleDefaultUnit'] = entity.saleDefaultUnit;
  data['purchaseDefaultUnit'] = entity.purchaseDefaultUnit;
  data['stockDefaultUnit'] = entity.stockDefaultUnit;
  data['ptypeLength'] = entity.ptypeLength;
  data['ptypeWidth'] = entity.ptypeWidth;
  data['ptypeHeight'] = entity.ptypeHeight;
  data['lengthUnit'] = entity.lengthUnit;
  data['createTime'] = entity.createTime;
  data['updateTime'] = entity.updateTime;
  data['batchlifeId'] = entity.batchlifeId;
  data['stockQty'] = entity.stockQty;
  data['stockSubQty'] = entity.stockSubQty;
  data['checkQty'] = entity.checkQty;
  data['checkSubQty'] = entity.checkSubQty;
  data['adjustQty'] = entity.adjustQty;
  data['adjustSubQty'] = entity.adjustSubQty;
  data['checkTypeStr'] = entity.checkTypeStr;
  data['checkTime'] = entity.checkTime;
  data['checked'] = entity.checked;
  data['subChecked'] = entity.subChecked;
  data['propvalueName1'] = entity.propvalueName1;
  data['propvalueName2'] = entity.propvalueName2;
  data['propvalueName3'] = entity.propvalueName3;
  data['propvalueName4'] = entity.propvalueName4;
  data['propvalueName5'] = entity.propvalueName5;
  data['propvalueName6'] = entity.propvalueName6;
  data['propvalueAll'] = entity.propvalueAll;
  data['proDateStr'] = entity.proDateStr;
  data['expDateStr'] = entity.expDateStr;
  data['selectchecked'] = entity.selectchecked;
  data['snno'] = entity.snno;
  data['costId'] = entity.costId;
  data['positionStrText'] = entity.positionStrText;
  data['positionList'] = entity.positionList;
  data['inoutId'] = entity.inoutId;
  return data;
}

extension StockCheckResultRepeatDetailsExtension on StockCheckResultRepeatDetails {
  StockCheckResultRepeatDetails copyWith({
    dynamic ptypeId,
    String? picUrl,
    String? usercode,
    dynamic pFullname,
    dynamic shortname,
    dynamic brandName,
    String? standard,
    String? ptypeType,
    String? unitName,
    dynamic subUnitName,
    dynamic ptypeMemo,
    dynamic fullbarcode,
    String? skuId,
    String? xcode,
    dynamic taxRate,
    int? skuPrice,
    dynamic pcategory,
    dynamic industryCategory,
    dynamic parName1,
    dynamic parName2,
    dynamic parName3,
    dynamic parName4,
    dynamic parName5,
    dynamic ptypeWeight,
    dynamic unitRelation,
    dynamic picUrls,
    int? skuName,
    String? propNames,
    String? propValues,
    dynamic propValueName1,
    dynamic propValueName2,
    String? batchno,
    dynamic produceDate,
    dynamic expireDate,
    dynamic defProduceDate,
    dynamic defExpireDate,
    dynamic customHead01,
    dynamic customHead02,
    dynamic customHead03,
    dynamic customHead04,
    dynamic customHead05,
    dynamic customHead06,
    dynamic customHead07,
    dynamic customHead08,
    dynamic customHead09,
    dynamic customHead10,
    dynamic customHead11,
    dynamic customHead12,
    dynamic customHead13,
    dynamic customHead14,
    dynamic subUnit,
    dynamic subQty,
    dynamic subInQty,
    dynamic subOutQty,
    dynamic subInOutQty,
    dynamic subInStockQty,
    dynamic subInOutPercent,
    dynamic subDiffQty,
    bool? hasPerm,
    dynamic baseUnitName,
    String? unitxqty,
    dynamic ptypeUnits,
    List<dynamic>? mySnList,
    List<dynamic>? checkSnList,
    String? snnoStrButton,
    int? unitCode,
    dynamic unitRate,
    String? unitId,
    String? detailId,
    String? stockCheckId,
    int? batchPrice,
    int? batchQty,
    int? batchLifeQty,
    dynamic stockPrice,
    String? id,
    dynamic profileId,
    String? fullname,
    dynamic shortName,
    dynamic namePy,
    dynamic classed,
    dynamic stoped,
    dynamic deleted,
    dynamic rowindex,
    String? barcode,
    String? ptypeArea,
    dynamic memo,
    dynamic createType,
    int? costMode,
    dynamic taxNumber,
    dynamic costPrice,
    dynamic supplyInfo,
    dynamic brandId,
    dynamic ktypeLimit,
    int? snenabled,
    bool? propenabled,
    bool? batchenabled,
    int? protectDays,
    int? protectDaysUnit,
    dynamic protectWarndays,
    dynamic weight,
    dynamic weightUnit,
    dynamic lithiumBattery,
    dynamic solid,
    dynamic difficultyLevel,
    dynamic weighted,
    dynamic retailDefaultUnit,
    dynamic saleDefaultUnit,
    dynamic purchaseDefaultUnit,
    dynamic stockDefaultUnit,
    dynamic ptypeLength,
    dynamic ptypeWidth,
    dynamic ptypeHeight,
    dynamic lengthUnit,
    dynamic createTime,
    dynamic updateTime,
    String? batchlifeId,
    int? stockQty,
    int? stockSubQty,
    dynamic checkQty,
    dynamic checkSubQty,
    dynamic adjustQty,
    dynamic adjustSubQty,
    String? checkTypeStr,
    String? checkTime,
    bool? checked,
    bool? subChecked,
    String? propvalueName1,
    String? propvalueName2,
    dynamic propvalueName3,
    dynamic propvalueName4,
    dynamic propvalueName5,
    dynamic propvalueName6,
    String? propvalueAll,
    String? proDateStr,
    String? expDateStr,
    bool? selectchecked,
    dynamic snno,
    String? costId,
    dynamic positionStrText,
    dynamic positionList,
    dynamic inoutId,
  }) {
    return StockCheckResultRepeatDetails()
      ..ptypeId = ptypeId ?? this.ptypeId
      ..picUrl = picUrl ?? this.picUrl
      ..usercode = usercode ?? this.usercode
      ..pFullname = pFullname ?? this.pFullname
      ..shortname = shortname ?? this.shortname
      ..brandName = brandName ?? this.brandName
      ..standard = standard ?? this.standard
      ..ptypeType = ptypeType ?? this.ptypeType
      ..unitName = unitName ?? this.unitName
      ..subUnitName = subUnitName ?? this.subUnitName
      ..ptypeMemo = ptypeMemo ?? this.ptypeMemo
      ..fullbarcode = fullbarcode ?? this.fullbarcode
      ..skuId = skuId ?? this.skuId
      ..xcode = xcode ?? this.xcode
      ..taxRate = taxRate ?? this.taxRate
      ..skuPrice = skuPrice ?? this.skuPrice
      ..pcategory = pcategory ?? this.pcategory
      ..industryCategory = industryCategory ?? this.industryCategory
      ..parName1 = parName1 ?? this.parName1
      ..parName2 = parName2 ?? this.parName2
      ..parName3 = parName3 ?? this.parName3
      ..parName4 = parName4 ?? this.parName4
      ..parName5 = parName5 ?? this.parName5
      ..ptypeWeight = ptypeWeight ?? this.ptypeWeight
      ..unitRelation = unitRelation ?? this.unitRelation
      ..picUrls = picUrls ?? this.picUrls
      ..skuName = skuName ?? this.skuName
      ..propNames = propNames ?? this.propNames
      ..propValues = propValues ?? this.propValues
      ..propValueName1 = propValueName1 ?? this.propValueName1
      ..propValueName2 = propValueName2 ?? this.propValueName2
      ..batchno = batchno ?? this.batchno
      ..produceDate = produceDate ?? this.produceDate
      ..expireDate = expireDate ?? this.expireDate
      ..defProduceDate = defProduceDate ?? this.defProduceDate
      ..defExpireDate = defExpireDate ?? this.defExpireDate
      ..customHead01 = customHead01 ?? this.customHead01
      ..customHead02 = customHead02 ?? this.customHead02
      ..customHead03 = customHead03 ?? this.customHead03
      ..customHead04 = customHead04 ?? this.customHead04
      ..customHead05 = customHead05 ?? this.customHead05
      ..customHead06 = customHead06 ?? this.customHead06
      ..customHead07 = customHead07 ?? this.customHead07
      ..customHead08 = customHead08 ?? this.customHead08
      ..customHead09 = customHead09 ?? this.customHead09
      ..customHead10 = customHead10 ?? this.customHead10
      ..customHead11 = customHead11 ?? this.customHead11
      ..customHead12 = customHead12 ?? this.customHead12
      ..customHead13 = customHead13 ?? this.customHead13
      ..customHead14 = customHead14 ?? this.customHead14
      ..subUnit = subUnit ?? this.subUnit
      ..subQty = subQty ?? this.subQty
      ..subInQty = subInQty ?? this.subInQty
      ..subOutQty = subOutQty ?? this.subOutQty
      ..subInOutQty = subInOutQty ?? this.subInOutQty
      ..subInStockQty = subInStockQty ?? this.subInStockQty
      ..subInOutPercent = subInOutPercent ?? this.subInOutPercent
      ..subDiffQty = subDiffQty ?? this.subDiffQty
      ..hasPerm = hasPerm ?? this.hasPerm
      ..baseUnitName = baseUnitName ?? this.baseUnitName
      ..unitxqty = unitxqty ?? this.unitxqty
      ..ptypeUnits = ptypeUnits ?? this.ptypeUnits
      ..mySnList = mySnList ?? this.mySnList
      ..checkSnList = checkSnList ?? this.checkSnList
      ..snnoStrButton = snnoStrButton ?? this.snnoStrButton
      ..unitCode = unitCode ?? this.unitCode
      ..unitRate = unitRate ?? this.unitRate
      ..unitId = unitId ?? this.unitId
      ..detailId = detailId ?? this.detailId
      ..stockCheckId = stockCheckId ?? this.stockCheckId
      ..batchPrice = batchPrice ?? this.batchPrice
      ..batchQty = batchQty ?? this.batchQty
      ..batchLifeQty = batchLifeQty ?? this.batchLifeQty
      ..stockPrice = stockPrice ?? this.stockPrice
      ..id = id ?? this.id
      ..profileId = profileId ?? this.profileId
      ..fullname = fullname ?? this.fullname
      ..shortName = shortName ?? this.shortName
      ..namePy = namePy ?? this.namePy
      ..classed = classed ?? this.classed
      ..stoped = stoped ?? this.stoped
      ..deleted = deleted ?? this.deleted
      ..rowindex = rowindex ?? this.rowindex
      ..barcode = barcode ?? this.barcode
      ..ptypeArea = ptypeArea ?? this.ptypeArea
      ..memo = memo ?? this.memo
      ..createType = createType ?? this.createType
      ..costMode = costMode ?? this.costMode
      ..taxNumber = taxNumber ?? this.taxNumber
      ..costPrice = costPrice ?? this.costPrice
      ..supplyInfo = supplyInfo ?? this.supplyInfo
      ..brandId = brandId ?? this.brandId
      ..ktypeLimit = ktypeLimit ?? this.ktypeLimit
      ..snenabled = snenabled ?? this.snenabled
      ..propenabled = propenabled ?? this.propenabled
      ..batchenabled = batchenabled ?? this.batchenabled
      ..protectDays = protectDays ?? this.protectDays
      ..protectDaysUnit = protectDaysUnit ?? this.protectDaysUnit
      ..protectWarndays = protectWarndays ?? this.protectWarndays
      ..weight = weight ?? this.weight
      ..weightUnit = weightUnit ?? this.weightUnit
      ..lithiumBattery = lithiumBattery ?? this.lithiumBattery
      ..solid = solid ?? this.solid
      ..difficultyLevel = difficultyLevel ?? this.difficultyLevel
      ..weighted = weighted ?? this.weighted
      ..retailDefaultUnit = retailDefaultUnit ?? this.retailDefaultUnit
      ..saleDefaultUnit = saleDefaultUnit ?? this.saleDefaultUnit
      ..purchaseDefaultUnit = purchaseDefaultUnit ?? this.purchaseDefaultUnit
      ..stockDefaultUnit = stockDefaultUnit ?? this.stockDefaultUnit
      ..ptypeLength = ptypeLength ?? this.ptypeLength
      ..ptypeWidth = ptypeWidth ?? this.ptypeWidth
      ..ptypeHeight = ptypeHeight ?? this.ptypeHeight
      ..lengthUnit = lengthUnit ?? this.lengthUnit
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..batchlifeId = batchlifeId ?? this.batchlifeId
      ..stockQty = stockQty ?? this.stockQty
      ..stockSubQty = stockSubQty ?? this.stockSubQty
      ..checkQty = checkQty ?? this.checkQty
      ..checkSubQty = checkSubQty ?? this.checkSubQty
      ..adjustQty = adjustQty ?? this.adjustQty
      ..adjustSubQty = adjustSubQty ?? this.adjustSubQty
      ..checkTypeStr = checkTypeStr ?? this.checkTypeStr
      ..checkTime = checkTime ?? this.checkTime
      ..checked = checked ?? this.checked
      ..subChecked = subChecked ?? this.subChecked
      ..propvalueName1 = propvalueName1 ?? this.propvalueName1
      ..propvalueName2 = propvalueName2 ?? this.propvalueName2
      ..propvalueName3 = propvalueName3 ?? this.propvalueName3
      ..propvalueName4 = propvalueName4 ?? this.propvalueName4
      ..propvalueName5 = propvalueName5 ?? this.propvalueName5
      ..propvalueName6 = propvalueName6 ?? this.propvalueName6
      ..propvalueAll = propvalueAll ?? this.propvalueAll
      ..proDateStr = proDateStr ?? this.proDateStr
      ..expDateStr = expDateStr ?? this.expDateStr
      ..selectchecked = selectchecked ?? this.selectchecked
      ..snno = snno ?? this.snno
      ..costId = costId ?? this.costId
      ..positionStrText = positionStrText ?? this.positionStrText
      ..positionList = positionList ?? this.positionList
      ..inoutId = inoutId ?? this.inoutId;
  }
}