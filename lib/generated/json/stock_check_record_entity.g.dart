
import '../../stockcheck/entity/stock_check_record_entity.dart';
import 'base/json_convert_content.dart';

StockCheckRecordEntity $StockCheckRecordEntityFromJson(
    Map<String, dynamic> json) {
  final StockCheckRecordEntity stockCheckRecordEntity = StockCheckRecordEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    stockCheckRecordEntity.id = id;
  }
  final String? ktypeUsercode = jsonConvert.convert<String>(
      json['ktypeUsercode']);
  if (ktypeUsercode != null) {
    stockCheckRecordEntity.ktypeUsercode = ktypeUsercode;
  }
  final String? ktypeId = jsonConvert.convert<String>(json['ktypeId']);
  if (ktypeId != null) {
    stockCheckRecordEntity.ktypeId = ktypeId;
  }
  final String? ktypeName = jsonConvert.convert<String>(json['ktypeName']);
  if (ktypeName != null) {
    stockCheckRecordEntity.ktypeName = ktypeName;
  }
  final String? otypeId = jsonConvert.convert<String>(json['otypeId']);
  if (otypeId != null) {
    stockCheckRecordEntity.otypeId = otypeId;
  }
  final dynamic otypeName = json['otypeName'];
  if (otypeName != null) {
    stockCheckRecordEntity.otypeName = otypeName;
  }
  final String? etypeName = jsonConvert.convert<String>(json['etypeName']);
  if (etypeName != null) {
    stockCheckRecordEntity.etypeName = etypeName;
  }
  final String? number = jsonConvert.convert<String>(json['number']);
  if (number != null) {
    stockCheckRecordEntity.number = number;
  }
  final String? checkTime = jsonConvert.convert<String>(json['checkTime']);
  if (checkTime != null) {
    stockCheckRecordEntity.checkTime = checkTime;
  }
  final int? checkState = jsonConvert.convert<int>(json['checkState']);
  if (checkState != null) {
    stockCheckRecordEntity.checkState = checkState;
  }
  final String? checkStateName = jsonConvert.convert<String>(
      json['checkStateName']);
  if (checkStateName != null) {
    stockCheckRecordEntity.checkStateName = checkStateName;
  }
  final String? memo = jsonConvert.convert<String>(json['memo']);
  if (memo != null) {
    stockCheckRecordEntity.memo = memo;
  }
  final String? summary = jsonConvert.convert<String>(json['summary']);
  if (summary != null) {
    stockCheckRecordEntity.summary = summary;
  }
  final int? checkType = jsonConvert.convert<int>(json['checkType']);
  if (checkType != null) {
    stockCheckRecordEntity.checkType = checkType;
  }
  return stockCheckRecordEntity;
}

Map<String, dynamic> $StockCheckRecordEntityToJson(
    StockCheckRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['ktypeUsercode'] = entity.ktypeUsercode;
  data['ktypeId'] = entity.ktypeId;
  data['ktypeName'] = entity.ktypeName;
  data['otypeId'] = entity.otypeId;
  data['otypeName'] = entity.otypeName;
  data['etypeName'] = entity.etypeName;
  data['number'] = entity.number;
  data['checkTime'] = entity.checkTime;
  data['checkState'] = entity.checkState;
  data['checkStateName'] = entity.checkStateName;
  data['memo'] = entity.memo;
  data['summary'] = entity.summary;
  data['checkType'] = entity.checkType;
  return data;
}

extension StockCheckRecordEntityExtension on StockCheckRecordEntity {
  StockCheckRecordEntity copyWith({
    String? id,
    String? ktypeUsercode,
    String? ktypeId,
    String? ktypeName,
    String? otypeId,
    dynamic otypeName,
    String? etypeName,
    String? number,
    String? checkTime,
    int? checkState,
    String? checkStateName,
    String? memo,
    String? summary,
    int? checkType,
  }) {
    return StockCheckRecordEntity()
      ..id = id ?? this.id
      ..ktypeUsercode = ktypeUsercode ?? this.ktypeUsercode
      ..ktypeId = ktypeId ?? this.ktypeId
      ..ktypeName = ktypeName ?? this.ktypeName
      ..otypeId = otypeId ?? this.otypeId
      ..otypeName = otypeName ?? this.otypeName
      ..etypeName = etypeName ?? this.etypeName
      ..number = number ?? this.number
      ..checkTime = checkTime ?? this.checkTime
      ..checkState = checkState ?? this.checkState
      ..checkStateName = checkStateName ?? this.checkStateName
      ..memo = memo ?? this.memo
      ..summary = summary ?? this.summary
      ..checkType = checkType ?? this.checkType;
  }
}