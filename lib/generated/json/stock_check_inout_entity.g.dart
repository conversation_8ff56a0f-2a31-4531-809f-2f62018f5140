
import '../../stockcheck/entity/stock_check_inout_entity.dart';
import 'base/json_convert_content.dart';

StockCheckInoutEntity $StockCheckInoutEntityFromJson(
    Map<String, dynamic> json) {
  final StockCheckInoutEntity stockCheckInoutEntity = StockCheckInoutEntity();
  final StockCheckInoutQueryParams? queryParams = jsonConvert.convert<
      StockCheckInoutQueryParams>(json['queryParams']);
  if (queryParams != null) {
    stockCheckInoutEntity.queryParams = queryParams;
  }
  return stockCheckInoutEntity;
}

Map<String, dynamic> $StockCheckInoutEntityToJson(
    StockCheckInoutEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['queryParams'] = entity.queryParams?.toJson();
  return data;
}

extension StockCheckInoutEntityExtension on StockCheckInoutEntity {
  StockCheckInoutEntity copyWith({
    StockCheckInoutQueryParams? queryParams,
  }) {
    return StockCheckInoutEntity()
      ..queryParams = queryParams ?? this.queryParams;
  }
}

StockCheckInoutQueryParams $StockCheckInoutQueryParamsFromJson(
    Map<String, dynamic> json) {
  final StockCheckInoutQueryParams stockCheckInoutQueryParams = StockCheckInoutQueryParams();
  final String? source = jsonConvert.convert<String>(json['source']);
  if (source != null) {
    stockCheckInoutQueryParams.source = source;
  }
  final String? stockCheckId = jsonConvert.convert<String>(
      json['stockCheckId']);
  if (stockCheckId != null) {
    stockCheckInoutQueryParams.stockCheckId = stockCheckId;
  }
  return stockCheckInoutQueryParams;
}

Map<String, dynamic> $StockCheckInoutQueryParamsToJson(
    StockCheckInoutQueryParams entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['source'] = entity.source;
  data['stockCheckId'] = entity.stockCheckId;
  return data;
}

extension StockCheckInoutQueryParamsExtension on StockCheckInoutQueryParams {
  StockCheckInoutQueryParams copyWith({
    String? source,
    String? stockCheckId,
  }) {
    return StockCheckInoutQueryParams()
      ..source = source ?? this.source
      ..stockCheckId = stockCheckId ?? this.stockCheckId;
  }
}