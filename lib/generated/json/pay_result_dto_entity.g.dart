import '../../bill/entity/bill_save_reslut_dto.dart';
import '../../bill/entity/pay_result_dto_entity.dart';
import 'base/json_convert_content.dart';

PayResultDtoEntity $PayResultDtoEntityFromJson(Map<String, dynamic> json) {
  final PayResultDtoEntity payResultDtoEntity = PayResultDtoEntity();
  final String? ordNo = jsonConvert.convert<String>(json['ordNo']);
  if (ordNo != null) {
    payResultDtoEntity.ordNo = ordNo;
  }
  final int? ordMctId = jsonConvert.convert<int>(json['ordMctId']);
  if (ordMctId != null) {
    payResultDtoEntity.ordMctId = ordMctId;
  }
  final int? ordShopId = jsonConvert.convert<int>(json['ordShopId']);
  if (ordShopId != null) {
    payResultDtoEntity.ordShopId = ordShopId;
  }
  final dynamic ordCurrency = json['ordCurrency'];
  if (ordCurrency != null) {
    payResultDtoEntity.ordCurrency = ordCurrency;
  }
  final String? currencySign =
      jsonConvert.convert<String>(json['currencySign']);
  if (currencySign != null) {
    payResultDtoEntity.currencySign = currencySign;
  }
  final String? pmtTag = jsonConvert.convert<String>(json['pmtTag']);
  if (pmtTag != null) {
    payResultDtoEntity.pmtTag = pmtTag;
  }
  final String? pmtName = jsonConvert.convert<String>(json['pmtName']);
  if (pmtName != null) {
    payResultDtoEntity.pmtName = pmtName;
  }
  final String? tradeNo = jsonConvert.convert<String>(json['tradeNo']);
  if (tradeNo != null) {
    payResultDtoEntity.tradeNo = tradeNo;
  }
  final int? tradeAmount = jsonConvert.convert<int>(json['tradeAmount']);
  if (tradeAmount != null) {
    payResultDtoEntity.tradeAmount = tradeAmount;
  }
  final dynamic tradeQrcode = json['tradeQrcode'];
  if (tradeQrcode != null) {
    payResultDtoEntity.tradeQrcode = tradeQrcode;
  }
  final dynamic tradeAccount = json['tradeAccount'];
  if (tradeAccount != null) {
    payResultDtoEntity.tradeAccount = tradeAccount;
  }
  // final PayResultDtoTradeResult? tradeResult = jsonConvert.convert<
  //     PayResultDtoTradeResult>(json['tradeResult']);
  // if (tradeResult != null) {
  //   payResultDtoEntity.tradeResult = tradeResult;
  // }
  final dynamic tradePayTime = json['tradePayTime'];
  if (tradePayTime != null) {
    payResultDtoEntity.tradePayTime = tradePayTime;
  }
  final int? tradeDiscoutAmount =
      jsonConvert.convert<int>(json['tradeDiscoutAmount']);
  if (tradeDiscoutAmount != null) {
    payResultDtoEntity.tradeDiscoutAmount = tradeDiscoutAmount;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    payResultDtoEntity.status = status;
  }
  final String? outNo = jsonConvert.convert<String>(json['outNo']);
  if (outNo != null) {
    payResultDtoEntity.outNo = outNo;
  }
  final int? ordType = jsonConvert.convert<int>(json['ordType']);
  if (ordType != null) {
    payResultDtoEntity.ordType = ordType;
  }
  final String? ordName = jsonConvert.convert<String>(json['ordName']);
  if (ordName != null) {
    payResultDtoEntity.ordName = ordName;
  }
  final String? addTime = jsonConvert.convert<String>(json['addTime']);
  if (addTime != null) {
    payResultDtoEntity.addTime = addTime;
  }
  final dynamic tradeTime = json['tradeTime'];
  if (tradeTime != null) {
    payResultDtoEntity.tradeTime = tradeTime;
  }
  final dynamic remark = json['remark'];
  if (remark != null) {
    payResultDtoEntity.remark = remark;
  }
  final int? originalAmount = jsonConvert.convert<int>(json['originalAmount']);
  if (originalAmount != null) {
    payResultDtoEntity.originalAmount = originalAmount;
  }
  final int? discountAmount = jsonConvert.convert<int>(json['discountAmount']);
  if (discountAmount != null) {
    payResultDtoEntity.discountAmount = discountAmount;
  }
  final int? ignoreAmount = jsonConvert.convert<int>(json['ignoreAmount']);
  if (ignoreAmount != null) {
    payResultDtoEntity.ignoreAmount = ignoreAmount;
  }
  final dynamic originalOrdNo = json['originalOrdNo'];
  if (originalOrdNo != null) {
    payResultDtoEntity.originalOrdNo = originalOrdNo;
  }
  final dynamic tag = json['tag'];
  if (tag != null) {
    payResultDtoEntity.tag = tag;
  }
  final int? scrId = jsonConvert.convert<int>(json['scrId']);
  if (scrId != null) {
    payResultDtoEntity.scrId = scrId;
  }
  final int? shopNo = jsonConvert.convert<int>(json['shopNo']);
  if (shopNo != null) {
    payResultDtoEntity.shopNo = shopNo;
  }
  return payResultDtoEntity;
}

Map<String, dynamic> $PayResultDtoEntityToJson(PayResultDtoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['ordNo'] = entity.ordNo;
  data['ordMctId'] = entity.ordMctId;
  data['ordShopId'] = entity.ordShopId;
  data['ordCurrency'] = entity.ordCurrency;
  data['currencySign'] = entity.currencySign;
  data['pmtTag'] = entity.pmtTag;
  data['pmtName'] = entity.pmtName;
  data['tradeNo'] = entity.tradeNo;
  data['tradeAmount'] = entity.tradeAmount;
  data['tradeQrcode'] = entity.tradeQrcode;
  data['tradeAccount'] = entity.tradeAccount;
  data['tradeResult'] = entity.tradeResult?.toJson();
  data['tradePayTime'] = entity.tradePayTime;
  data['tradeDiscoutAmount'] = entity.tradeDiscoutAmount;
  data['status'] = entity.status;
  data['outNo'] = entity.outNo;
  data['ordType'] = entity.ordType;
  data['ordName'] = entity.ordName;
  data['addTime'] = entity.addTime;
  data['tradeTime'] = entity.tradeTime;
  data['remark'] = entity.remark;
  data['originalAmount'] = entity.originalAmount;
  data['discountAmount'] = entity.discountAmount;
  data['ignoreAmount'] = entity.ignoreAmount;
  data['originalOrdNo'] = entity.originalOrdNo;
  data['tag'] = entity.tag;
  data['scrId'] = entity.scrId;
  data['shopNo'] = entity.shopNo;
  data["billSaveResultDto"] = entity.billSaveResultDto?.toJson();
  return data;
}

extension PayResultDtoEntityExtension on PayResultDtoEntity {
  PayResultDtoEntity copyWith({
    String? ordNo,
    int? ordMctId,
    int? ordShopId,
    dynamic ordCurrency,
    String? currencySign,
    String? pmtTag,
    String? pmtName,
    String? tradeNo,
    int? tradeAmount,
    dynamic tradeQrcode,
    dynamic tradeAccount,
    PayResultDtoTradeResult? tradeResult,
    dynamic tradePayTime,
    int? tradeDiscoutAmount,
    String? status,
    String? outNo,
    int? ordType,
    String? ordName,
    String? addTime,
    dynamic tradeTime,
    dynamic remark,
    int? originalAmount,
    int? discountAmount,
    int? ignoreAmount,
    dynamic originalOrdNo,
    dynamic tag,
    int? scrId,
    int? shopNo,
    BillSaveResultDto? billSaveResultDto
  }) {
    return PayResultDtoEntity()
      ..ordNo = ordNo ?? this.ordNo
      ..ordMctId = ordMctId ?? this.ordMctId
      ..ordShopId = ordShopId ?? this.ordShopId
      ..ordCurrency = ordCurrency ?? this.ordCurrency
      ..currencySign = currencySign ?? this.currencySign
      ..pmtTag = pmtTag ?? this.pmtTag
      ..pmtName = pmtName ?? this.pmtName
      ..tradeNo = tradeNo ?? this.tradeNo
      ..tradeAmount = tradeAmount ?? this.tradeAmount
      ..tradeQrcode = tradeQrcode ?? this.tradeQrcode
      ..tradeAccount = tradeAccount ?? this.tradeAccount
      ..tradeResult = tradeResult ?? this.tradeResult
      ..tradePayTime = tradePayTime ?? this.tradePayTime
      ..tradeDiscoutAmount = tradeDiscoutAmount ?? this.tradeDiscoutAmount
      ..status = status ?? this.status
      ..outNo = outNo ?? this.outNo
      ..ordType = ordType ?? this.ordType
      ..ordName = ordName ?? this.ordName
      ..addTime = addTime ?? this.addTime
      ..tradeTime = tradeTime ?? this.tradeTime
      ..remark = remark ?? this.remark
      ..originalAmount = originalAmount ?? this.originalAmount
      ..discountAmount = discountAmount ?? this.discountAmount
      ..ignoreAmount = ignoreAmount ?? this.ignoreAmount
      ..originalOrdNo = originalOrdNo ?? this.originalOrdNo
      ..tag = tag ?? this.tag
      ..scrId = scrId ?? this.scrId
      ..shopNo = shopNo ?? this.shopNo
      ..billSaveResultDto = billSaveResultDto ?? this.billSaveResultDto;
  }
}

PayResultDtoTradeResult $PayResultDtoTradeResultFromJson(
    Map<String, dynamic> json) {
  final PayResultDtoTradeResult payResultDtoTradeResult =
      PayResultDtoTradeResult();
  final String? respCode = jsonConvert.convert<String>(json['resp_code']);
  if (respCode != null) {
    payResultDtoTradeResult.respCode = respCode;
  }
  final String? royalty = jsonConvert.convert<String>(json['royalty']);
  if (royalty != null) {
    payResultDtoTradeResult.royalty = royalty;
  }
  final String? channelFlag = jsonConvert.convert<String>(json['channel_flag']);
  if (channelFlag != null) {
    payResultDtoTradeResult.channelFlag = channelFlag;
  }
  final String? sign = jsonConvert.convert<String>(json['sign']);
  if (sign != null) {
    payResultDtoTradeResult.sign = sign;
  }
  final String? resultCode = jsonConvert.convert<String>(json['result_code']);
  if (resultCode != null) {
    payResultDtoTradeResult.resultCode = resultCode;
  }
  final String? thirdOrderId =
      jsonConvert.convert<String>(json['third_order_id']);
  if (thirdOrderId != null) {
    payResultDtoTradeResult.thirdOrderId = thirdOrderId;
  }
  final String? nonceStr = jsonConvert.convert<String>(json['nonce_str']);
  if (nonceStr != null) {
    payResultDtoTradeResult.nonceStr = nonceStr;
  }
  final String? payWay = jsonConvert.convert<String>(json['pay_way']);
  if (payWay != null) {
    payResultDtoTradeResult.payWay = payWay;
  }
  final String? leshuaOrderId =
      jsonConvert.convert<String>(json['leshua_order_id']);
  if (leshuaOrderId != null) {
    payResultDtoTradeResult.leshuaOrderId = leshuaOrderId;
  }
  final String? merchantId = jsonConvert.convert<String>(json['merchant_id']);
  if (merchantId != null) {
    payResultDtoTradeResult.merchantId = merchantId;
  }
  final String? tradeType = jsonConvert.convert<String>(json['trade_type']);
  if (tradeType != null) {
    payResultDtoTradeResult.tradeType = tradeType;
  }
  final String? costTime = jsonConvert.convert<String>(json['cost_time']);
  if (costTime != null) {
    payResultDtoTradeResult.costTime = costTime;
  }
  final String? signType = jsonConvert.convert<String>(json['sign_type']);
  if (signType != null) {
    payResultDtoTradeResult.signType = signType;
  }
  final String? amount = jsonConvert.convert<String>(json['amount']);
  if (amount != null) {
    payResultDtoTradeResult.amount = amount;
  }
  final String? errorMsg = jsonConvert.convert<String>(json['error_msg']);
  if (errorMsg != null) {
    payResultDtoTradeResult.errorMsg = errorMsg;
  }
  final String? subMerchantId =
      jsonConvert.convert<String>(json['sub_merchant_id']);
  if (subMerchantId != null) {
    payResultDtoTradeResult.subMerchantId = subMerchantId;
  }
  final String? refundAmount =
      jsonConvert.convert<String>(json['refund_amount']);
  if (refundAmount != null) {
    payResultDtoTradeResult.refundAmount = refundAmount;
  }
  final String? errorCode = jsonConvert.convert<String>(json['error_code']);
  if (errorCode != null) {
    payResultDtoTradeResult.errorCode = errorCode;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    payResultDtoTradeResult.status = status;
  }
  return payResultDtoTradeResult;
}

Map<String, dynamic> $PayResultDtoTradeResultToJson(
    PayResultDtoTradeResult entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['resp_code'] = entity.respCode;
  data['royalty'] = entity.royalty;
  data['channel_flag'] = entity.channelFlag;
  data['sign'] = entity.sign;
  data['result_code'] = entity.resultCode;
  data['third_order_id'] = entity.thirdOrderId;
  data['nonce_str'] = entity.nonceStr;
  data['pay_way'] = entity.payWay;
  data['leshua_order_id'] = entity.leshuaOrderId;
  data['merchant_id'] = entity.merchantId;
  data['trade_type'] = entity.tradeType;
  data['cost_time'] = entity.costTime;
  data['sign_type'] = entity.signType;
  data['amount'] = entity.amount;
  data['error_msg'] = entity.errorMsg;
  data['sub_merchant_id'] = entity.subMerchantId;
  data['refund_amount'] = entity.refundAmount;
  data['error_code'] = entity.errorCode;
  data['status'] = entity.status;
  return data;
}

extension PayResultDtoTradeResultExtension on PayResultDtoTradeResult {
  PayResultDtoTradeResult copyWith({
    String? respCode,
    String? royalty,
    String? channelFlag,
    String? sign,
    String? resultCode,
    String? thirdOrderId,
    String? nonceStr,
    String? payWay,
    String? leshuaOrderId,
    String? merchantId,
    String? tradeType,
    String? costTime,
    String? signType,
    String? amount,
    String? errorMsg,
    String? subMerchantId,
    String? refundAmount,
    String? errorCode,
    String? status,
  }) {
    return PayResultDtoTradeResult()
      ..respCode = respCode ?? this.respCode
      ..royalty = royalty ?? this.royalty
      ..channelFlag = channelFlag ?? this.channelFlag
      ..sign = sign ?? this.sign
      ..resultCode = resultCode ?? this.resultCode
      ..thirdOrderId = thirdOrderId ?? this.thirdOrderId
      ..nonceStr = nonceStr ?? this.nonceStr
      ..payWay = payWay ?? this.payWay
      ..leshuaOrderId = leshuaOrderId ?? this.leshuaOrderId
      ..merchantId = merchantId ?? this.merchantId
      ..tradeType = tradeType ?? this.tradeType
      ..costTime = costTime ?? this.costTime
      ..signType = signType ?? this.signType
      ..amount = amount ?? this.amount
      ..errorMsg = errorMsg ?? this.errorMsg
      ..subMerchantId = subMerchantId ?? this.subMerchantId
      ..refundAmount = refundAmount ?? this.refundAmount
      ..errorCode = errorCode ?? this.errorCode
      ..status = status ?? this.status;
  }
}
