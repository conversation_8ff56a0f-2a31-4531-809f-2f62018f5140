
import '../../bill/bill/channel/model/bill_channel_filter_entity.dart';
import 'base/json_convert_content.dart';

BillChannelFilterEntity $BillChannelFilterEntityFromJson(
    Map<String, dynamic> json) {
  final BillChannelFilterEntity billChannelFilterEntity = BillChannelFilterEntity();
  final List<
      BillChannelFilterEshopType>? eshopType = (json['eshopType'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<BillChannelFilterEshopType>(
          e) as BillChannelFilterEshopType).toList();
  if (eshopType != null) {
    billChannelFilterEntity.eshopType = eshopType;
  }
  final List<BillChannelFilterEshops>? eshops = (json['eshops'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<BillChannelFilterEshops>(
          e) as BillChannelFilterEshops).toList();
  if (eshops != null) {
    billChannelFilterEntity.eshops = eshops;
  }
  final List<
      BillChannelFilterSelfDeliveryMode>? selfDeliveryMode = (json['selfDeliveryMode'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<BillChannelFilterSelfDeliveryMode>(
          e) as BillChannelFilterSelfDeliveryMode).toList();
  if (selfDeliveryMode != null) {
    billChannelFilterEntity.selfDeliveryMode = selfDeliveryMode;
  }
  return billChannelFilterEntity;
}

Map<String, dynamic> $BillChannelFilterEntityToJson(
    BillChannelFilterEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['eshopType'] = entity.eshopType?.map((v) => v.toJson()).toList();
  data['eshops'] = entity.eshops?.map((v) => v.toJson()).toList();
  data['selfDeliveryMode'] =
      entity.selfDeliveryMode?.map((v) => v.toJson()).toList();
  return data;
}

extension BillChannelFilterEntityExtension on BillChannelFilterEntity {
  BillChannelFilterEntity copyWith({
    List<BillChannelFilterEshopType>? eshopType,
    List<BillChannelFilterEshops>? eshops,
    List<BillChannelFilterSelfDeliveryMode>? selfDeliveryMode,
  }) {
    return BillChannelFilterEntity()
      ..eshopType = eshopType ?? this.eshopType
      ..eshops = eshops ?? this.eshops
      ..selfDeliveryMode = selfDeliveryMode ?? this.selfDeliveryMode;
  }
}

BillChannelFilterEshopType $BillChannelFilterEshopTypeFromJson(
    Map<String, dynamic> json) {
  final BillChannelFilterEshopType billChannelFilterEshopType = BillChannelFilterEshopType();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    billChannelFilterEshopType.code = code;
  }
  final dynamic key = json['key'];
  if (key != null) {
    billChannelFilterEshopType.key = key;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    billChannelFilterEshopType.description = description;
  }
  return billChannelFilterEshopType;
}

Map<String, dynamic> $BillChannelFilterEshopTypeToJson(
    BillChannelFilterEshopType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['key'] = entity.key;
  data['description'] = entity.description;
  return data;
}

extension BillChannelFilterEshopTypeExtension on BillChannelFilterEshopType {
  BillChannelFilterEshopType copyWith({
    int? code,
    dynamic key,
    String? description,
  }) {
    return BillChannelFilterEshopType()
      ..code = code ?? this.code
      ..key = key ?? this.key
      ..description = description ?? this.description;
  }
}

BillChannelFilterEshops $BillChannelFilterEshopsFromJson(
    Map<String, dynamic> json) {
  final BillChannelFilterEshops billChannelFilterEshops = BillChannelFilterEshops();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    billChannelFilterEshops.id = id;
  }
  final dynamic profileId = json['profileId'];
  if (profileId != null) {
    billChannelFilterEshops.profileId = profileId;
  }
  final String? fullname = jsonConvert.convert<String>(json['fullname']);
  if (fullname != null) {
    billChannelFilterEshops.fullname = fullname;
  }
  final int? eshopType = jsonConvert.convert<int>(json['eshopType']);
  if (eshopType != null) {
    billChannelFilterEshops.eshopType = eshopType;
  }
  final String? platformName = jsonConvert.convert<String>(
      json['platformName']);
  if (platformName != null) {
    billChannelFilterEshops.platformName = platformName;
  }
  final bool? enable = jsonConvert.convert<bool>(json['enable']);
  if (enable != null) {
    billChannelFilterEshops.enable = enable;
  }
  final int? ocategory = jsonConvert.convert<int>(json['ocategory']);
  if (ocategory != null) {
    billChannelFilterEshops.ocategory = ocategory;
  }
  final int? storeType = jsonConvert.convert<int>(json['storeType']);
  if (storeType != null) {
    billChannelFilterEshops.storeType = storeType;
  }
  return billChannelFilterEshops;
}

Map<String, dynamic> $BillChannelFilterEshopsToJson(
    BillChannelFilterEshops entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['profileId'] = entity.profileId;
  data['fullname'] = entity.fullname;
  data['eshopType'] = entity.eshopType;
  data['platformName'] = entity.platformName;
  data['enable'] = entity.enable;
  data['ocategory'] = entity.ocategory;
  data['storeType'] = entity.storeType;
  return data;
}

extension BillChannelFilterEshopsExtension on BillChannelFilterEshops {
  BillChannelFilterEshops copyWith({
    String? id,
    dynamic profileId,
    String? fullname,
    int? eshopType,
    String? platformName,
    bool? enable,
    int? ocategory,
    int? storeType,
  }) {
    return BillChannelFilterEshops()
      ..id = id ?? this.id
      ..profileId = profileId ?? this.profileId
      ..fullname = fullname ?? this.fullname
      ..eshopType = eshopType ?? this.eshopType
      ..platformName = platformName ?? this.platformName
      ..enable = enable ?? this.enable
      ..ocategory = ocategory ?? this.ocategory
      ..storeType = storeType ?? this.storeType;
  }
}

BillChannelFilterSelfDeliveryMode $BillChannelFilterSelfDeliveryModeFromJson(
    Map<String, dynamic> json) {
  final BillChannelFilterSelfDeliveryMode billChannelFilterSelfDeliveryMode = BillChannelFilterSelfDeliveryMode();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    billChannelFilterSelfDeliveryMode.code = code;
  }
  final String? key = jsonConvert.convert<String>(json['key']);
  if (key != null) {
    billChannelFilterSelfDeliveryMode.key = key;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    billChannelFilterSelfDeliveryMode.description = description;
  }
  return billChannelFilterSelfDeliveryMode;
}

Map<String, dynamic> $BillChannelFilterSelfDeliveryModeToJson(
    BillChannelFilterSelfDeliveryMode entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['key'] = entity.key;
  data['description'] = entity.description;
  return data;
}

extension BillChannelFilterSelfDeliveryModeExtension on BillChannelFilterSelfDeliveryMode {
  BillChannelFilterSelfDeliveryMode copyWith({
    int? code,
    String? key,
    String? description,
  }) {
    return BillChannelFilterSelfDeliveryMode()
      ..code = code ?? this.code
      ..key = key ?? this.key
      ..description = description ?? this.description;
  }
}