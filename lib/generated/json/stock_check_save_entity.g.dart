
import '../../stockcheck/entity/stock_check_save_entity.dart';
import 'base/json_convert_content.dart';

StockCheckSaveEntity $StockCheckSaveEntityFromJson(Map<String, dynamic> json) {
  final StockCheckSaveEntity stockCheckSaveEntity = StockCheckSaveEntity();
  final String? stockCheckId = jsonConvert.convert<String>(
      json['stockCheckId']);
  if (stockCheckId != null) {
    stockCheckSaveEntity.stockCheckId = stockCheckId;
  }
  final String? memo = jsonConvert.convert<String>(json['memo']);
  if (memo != null) {
    stockCheckSaveEntity.memo = memo;
  }
  final String? checkMemo = jsonConvert.convert<String>(json['checkMemo']);
  if (checkMemo != null) {
    stockCheckSaveEntity.checkMemo = checkMemo;
  }
  final String? etypeId = jsonConvert.convert<String>(json['etypeId']);
  if (etypeId != null) {
    stockCheckSaveEntity.etypeId = etypeId;
  }
  return stockCheckSaveEntity;
}

Map<String, dynamic> $StockCheckSaveEntityToJson(StockCheckSaveEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['stockCheckId'] = entity.stockCheckId;
  data['memo'] = entity.memo;
  data['checkMemo'] = entity.checkMemo;
  data['etypeId'] = entity.etypeId;
  return data;
}

extension StockCheckSaveEntityExtension on StockCheckSaveEntity {
  StockCheckSaveEntity copyWith({
    String? stockCheckId,
    String? memo,
    String? checkMemo,
    String? etypeId,
  }) {
    return StockCheckSaveEntity()
      ..stockCheckId = stockCheckId ?? this.stockCheckId
      ..memo = memo ?? this.memo
      ..checkMemo = checkMemo ?? this.checkMemo
      ..etypeId = etypeId ?? this.etypeId;
  }
}