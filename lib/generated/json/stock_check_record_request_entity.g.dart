

import '../../stockcheck/request/stock_check_record_request_entity.dart';
import 'base/json_convert_content.dart';

StockCheckRecordRequestEntity $StockCheckRecordRequestEntityFromJson(
    Map<String, dynamic> json) {
  final StockCheckRecordRequestEntity stockCheckRecordRequestEntity = StockCheckRecordRequestEntity();
  final bool? refresh = jsonConvert.convert<bool>(json['refresh']);
  if (refresh != null) {
    stockCheckRecordRequestEntity.refresh = refresh;
  }
  final StockCheckRecordRequestQueryParams? queryParams = jsonConvert.convert<
      StockCheckRecordRequestQueryParams>(json['queryParams']);
  if (queryParams != null) {
    stockCheckRecordRequestEntity.queryParams = queryParams;
  }
  final int? pageSize = jsonConvert.convert<int>(json['pageSize']);
  if (pageSize != null) {
    stockCheckRecordRequestEntity.pageSize = pageSize;
  }
  final int? pageIndex = jsonConvert.convert<int>(json['pageIndex']);
  if (pageIndex != null) {
    stockCheckRecordRequestEntity.pageIndex = pageIndex;
  }
  final int? first = jsonConvert.convert<int>(json['first']);
  if (first != null) {
    stockCheckRecordRequestEntity.first = first;
  }
  final int? count = jsonConvert.convert<int>(json['count']);
  if (count != null) {
    stockCheckRecordRequestEntity.count = count;
  }
  return stockCheckRecordRequestEntity;
}

Map<String, dynamic> $StockCheckRecordRequestEntityToJson(
    StockCheckRecordRequestEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['refresh'] = entity.refresh;
  data['queryParams'] = entity.queryParams.toJson();
  data['pageSize'] = entity.pageSize;
  data['pageIndex'] = entity.pageIndex;
  data['first'] = entity.first;
  data['count'] = entity.count;
  return data;
}

extension StockCheckRecordRequestEntityExtension on StockCheckRecordRequestEntity {
  StockCheckRecordRequestEntity copyWith({
    bool? refresh,
    StockCheckRecordRequestQueryParams? queryParams,
    int? pageSize,
    int? pageIndex,
    int? first,
    int? count,
  }) {
    return StockCheckRecordRequestEntity()
      ..refresh = refresh ?? this.refresh
      ..queryParams = queryParams ?? this.queryParams
      ..pageSize = pageSize ?? this.pageSize
      ..pageIndex = pageIndex ?? this.pageIndex
      ..first = first ?? this.first
      ..count = count ?? this.count;
  }
}

StockCheckRecordRequestQueryParams $StockCheckRecordRequestQueryParamsFromJson(
    Map<String, dynamic> json) {
  final StockCheckRecordRequestQueryParams stockCheckRecordRequestQueryParams = StockCheckRecordRequestQueryParams();
  final dynamic filter = json['filter'];
  if (filter != null) {
    stockCheckRecordRequestQueryParams.filter = filter;
  }
  final dynamic selectType = json['selectType'];
  if (selectType != null) {
    stockCheckRecordRequestQueryParams.selectType = selectType;
  }
  final String? isPC = jsonConvert.convert<String>(json['isPC']);
  if (isPC != null) {
    stockCheckRecordRequestQueryParams.isPC = isPC;
  }
  final dynamic batchno = json['batchno'];
  if (batchno != null) {
    stockCheckRecordRequestQueryParams.batchno = batchno;
  }
  final String? startDate = jsonConvert.convert<String>(json['startDate']);
  if (startDate != null) {
    stockCheckRecordRequestQueryParams.startDate = startDate;
  }
  final String? endDate = jsonConvert.convert<String>(json['endDate']);
  if (endDate != null) {
    stockCheckRecordRequestQueryParams.endDate = endDate;
  }
  final String? filterValue = jsonConvert.convert<String>(json['filterValue']);
  if (filterValue != null) {
    stockCheckRecordRequestQueryParams.filterValue = filterValue;
  }
  final dynamic ktypeId = json['ktypeId'];
  if (ktypeId != null) {
    stockCheckRecordRequestQueryParams.ktypeId = ktypeId;
  }
  final String? ktypeName = jsonConvert.convert<String>(json['ktypeName']);
  if (ktypeName != null) {
    stockCheckRecordRequestQueryParams.ktypeName = ktypeName;
  }
  final dynamic etypeId = json['etypeId'];
  if (etypeId != null) {
    stockCheckRecordRequestQueryParams.etypeId = etypeId;
  }
  final int? filterFinish = jsonConvert.convert<int>(json['filterFinish']);
  if (filterFinish != null) {
    stockCheckRecordRequestQueryParams.filterFinish = filterFinish;
  }
  return stockCheckRecordRequestQueryParams;
}

Map<String, dynamic> $StockCheckRecordRequestQueryParamsToJson(
    StockCheckRecordRequestQueryParams entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['filter'] = entity.filter;
  data['selectType'] = entity.selectType;
  data['isPC'] = entity.isPC;
  data['batchno'] = entity.batchno;
  data['startDate'] = entity.startDate;
  data['endDate'] = entity.endDate;
  data['filterValue'] = entity.filterValue;
  data['ktypeId'] = entity.ktypeId;
  data['ktypeName'] = entity.ktypeName;
  data['etypeId'] = entity.etypeId;
  data['filterFinish'] = entity.filterFinish;
  return data;
}

extension StockCheckRecordRequestQueryParamsExtension on StockCheckRecordRequestQueryParams {
  StockCheckRecordRequestQueryParams copyWith({
    dynamic filter,
    dynamic selectType,
    String? isPC,
    dynamic batchno,
    String? startDate,
    String? endDate,
    String? filterValue,
    dynamic ktypeId,
    String? ktypeName,
    dynamic etypeId,
    int? filterFinish,
  }) {
    return StockCheckRecordRequestQueryParams()
      ..filter = filter ?? this.filter
      ..selectType = selectType ?? this.selectType
      ..isPC = isPC ?? this.isPC
      ..batchno = batchno ?? this.batchno
      ..startDate = startDate ?? this.startDate
      ..endDate = endDate ?? this.endDate
      ..filterValue = filterValue ?? this.filterValue
      ..ktypeId = ktypeId ?? this.ktypeId
      ..ktypeName = ktypeName ?? this.ktypeName
      ..etypeId = etypeId ?? this.etypeId
      ..filterFinish = filterFinish ?? this.filterFinish;
  }
}