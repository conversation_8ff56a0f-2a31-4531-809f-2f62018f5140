// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import '../../../bill/bill/channel/model/bill_channel_count_entity.dart';
import '../../../bill/bill/channel/model/bill_channel_filter_entity.dart';
import '../../../bill/entity/pay_result_dto_entity.dart';
import '../../../stockcheck/entity/stock_check_bill_query_entity.dart';
import '../../../stockcheck/entity/stock_check_create_entity.dart';
import '../../../stockcheck/entity/stock_check_info_entity.dart';
import '../../../stockcheck/entity/stock_check_inout_entity.dart';
import '../../../stockcheck/entity/stock_check_record_entity.dart';
import '../../../stockcheck/entity/stock_check_result_entity.dart';
import '../../../stockcheck/entity/stock_check_save_entity.dart';
import '../../../stockcheck/request/stock_check_infos_request_entity.dart';
import '../../../stockcheck/request/stock_check_record_request_entity.dart';
import '../../../stockcheck/request/stock_create_entity.dart';
import '../../../bill/entity/pay_result_dto_entity.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(
    Object error, StackTrace stackTrace);

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value
          .map((dynamic e) => _asT<T>(e, enumConvert: enumConvert))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>)
          .map((dynamic e) => _asT<T>(e, enumConvert: enumConvert)!)
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        return convertFuncMap[type]!(Map<String, dynamic>.from(value)) as T;
      } else {
        throw UnimplementedError(
            '$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<BillChannelCountEntity>[] is M) {
      return data
          .map<BillChannelCountEntity>(
              (Map<String, dynamic> e) => BillChannelCountEntity.fromJson(e))
          .toList() as M;
    }
    if (<BillChannelFilterEntity>[] is M) {
      return data
          .map<BillChannelFilterEntity>(
              (Map<String, dynamic> e) => BillChannelFilterEntity.fromJson(e))
          .toList() as M;
    }
    if (<BillChannelFilterEshopType>[] is M) {
      return data
          .map<BillChannelFilterEshopType>((Map<String, dynamic> e) =>
              BillChannelFilterEshopType.fromJson(e))
          .toList() as M;
    }
    if (<BillChannelFilterEshops>[] is M) {
      return data
          .map<BillChannelFilterEshops>(
              (Map<String, dynamic> e) => BillChannelFilterEshops.fromJson(e))
          .toList() as M;
    }
    if (<BillChannelFilterSelfDeliveryMode>[] is M) {
      return data
          .map<BillChannelFilterSelfDeliveryMode>((Map<String, dynamic> e) =>
              BillChannelFilterSelfDeliveryMode.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckBillQueryEntity>[] is M) {
      return data
          .map<StockCheckBillQueryEntity>(
              (Map<String, dynamic> e) => StockCheckBillQueryEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckBillQueryCheckPtypes>[] is M) {
      return data
          .map<StockCheckBillQueryCheckPtypes>((Map<String, dynamic> e) =>
              StockCheckBillQueryCheckPtypes.fromJson(e))
          .toList() as M;
    }
    if (<PayResultDtoEntity>[] is M) {
      return data
          .map<PayResultDtoEntity>(
              (Map<String, dynamic> e) => PayResultDtoEntity.fromJson(e))
          .toList() as M;
    }
    if (<PayResultDtoTradeResult>[] is M) {
      return data
          .map<PayResultDtoTradeResult>(
              (Map<String, dynamic> e) => PayResultDtoTradeResult.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckCreateEntity>[] is M) {
      return data
          .map<StockCheckCreateEntity>(
              (Map<String, dynamic> e) => StockCheckCreateEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckInfoEntity>[] is M) {
      return data
          .map<StockCheckInfoEntity>(
              (Map<String, dynamic> e) => StockCheckInfoEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckInfoMySnList>[] is M) {
      return data
          .map<StockCheckInfoMySnList>(
              (Map<String, dynamic> e) => StockCheckInfoMySnList.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckInoutEntity>[] is M) {
      return data
          .map<StockCheckInoutEntity>(
              (Map<String, dynamic> e) => StockCheckInoutEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckInoutQueryParams>[] is M) {
      return data
          .map<StockCheckInoutQueryParams>((Map<String, dynamic> e) =>
              StockCheckInoutQueryParams.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckRecordEntity>[] is M) {
      return data
          .map<StockCheckRecordEntity>(
              (Map<String, dynamic> e) => StockCheckRecordEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckResultEntity>[] is M) {
      return data
          .map<StockCheckResultEntity>(
              (Map<String, dynamic> e) => StockCheckResultEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckResultRepeatDetails>[] is M) {
      return data
          .map<StockCheckResultRepeatDetails>((Map<String, dynamic> e) =>
              StockCheckResultRepeatDetails.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckSaveEntity>[] is M) {
      return data
          .map<StockCheckSaveEntity>(
              (Map<String, dynamic> e) => StockCheckSaveEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckInfosRequestEntity>[] is M) {
      return data
          .map<StockCheckInfosRequestEntity>((Map<String, dynamic> e) =>
              StockCheckInfosRequestEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckInfosRequestQueryParams>[] is M) {
      return data
          .map<StockCheckInfosRequestQueryParams>((Map<String, dynamic> e) =>
              StockCheckInfosRequestQueryParams.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckRecordRequestEntity>[] is M) {
      return data
          .map<StockCheckRecordRequestEntity>((Map<String, dynamic> e) =>
              StockCheckRecordRequestEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCheckRecordRequestQueryParams>[] is M) {
      return data
          .map<StockCheckRecordRequestQueryParams>((Map<String, dynamic> e) =>
              StockCheckRecordRequestQueryParams.fromJson(e))
          .toList() as M;
    }
    if (<StockCreateEntity>[] is M) {
      return data
          .map<StockCreateEntity>(
              (Map<String, dynamic> e) => StockCreateEntity.fromJson(e))
          .toList() as M;
    }
    if (<StockCreateCheckPtypes>[] is M) {
      return data
          .map<StockCreateCheckPtypes>(
              (Map<String, dynamic> e) => StockCreateCheckPtypes.fromJson(e))
          .toList() as M;
    }

    debugPrint("${M.toString()} not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(
          json.map((e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (BillChannelCountEntity).toString(): BillChannelCountEntity.fromJson,
    (BillChannelFilterEntity).toString(): BillChannelFilterEntity.fromJson,
    (BillChannelFilterEshopType).toString():
        BillChannelFilterEshopType.fromJson,
    (BillChannelFilterEshops).toString(): BillChannelFilterEshops.fromJson,
    (BillChannelFilterSelfDeliveryMode).toString():
        BillChannelFilterSelfDeliveryMode.fromJson,
    (StockCheckBillQueryEntity).toString(): StockCheckBillQueryEntity.fromJson,
    (StockCheckBillQueryCheckPtypes).toString():
        StockCheckBillQueryCheckPtypes.fromJson,
    (StockCheckCreateEntity).toString(): StockCheckCreateEntity.fromJson,
    (StockCheckInfoEntity).toString(): StockCheckInfoEntity.fromJson,
    (StockCheckInfoMySnList).toString(): StockCheckInfoMySnList.fromJson,
    (StockCheckInoutEntity).toString(): StockCheckInoutEntity.fromJson,
    (StockCheckInoutQueryParams).toString():
        StockCheckInoutQueryParams.fromJson,
    (StockCheckRecordEntity).toString(): StockCheckRecordEntity.fromJson,
    (StockCheckResultEntity).toString(): StockCheckResultEntity.fromJson,
    (StockCheckResultRepeatDetails).toString():
        StockCheckResultRepeatDetails.fromJson,
    (StockCheckSaveEntity).toString(): StockCheckSaveEntity.fromJson,
    (StockCheckInfosRequestEntity).toString():
        StockCheckInfosRequestEntity.fromJson,
    (StockCheckInfosRequestQueryParams).toString():
        StockCheckInfosRequestQueryParams.fromJson,
    (StockCheckRecordRequestEntity).toString():
        StockCheckRecordRequestEntity.fromJson,
    (StockCheckRecordRequestQueryParams).toString():
        StockCheckRecordRequestQueryParams.fromJson,
    (StockCreateEntity).toString(): StockCreateEntity.fromJson,
    (StockCreateCheckPtypes).toString(): StockCreateCheckPtypes.fromJson,
    (PayResultDtoEntity).toString(): PayResultDtoEntity.fromJson,
    (PayResultDtoTradeResult).toString(): PayResultDtoTradeResult.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}
