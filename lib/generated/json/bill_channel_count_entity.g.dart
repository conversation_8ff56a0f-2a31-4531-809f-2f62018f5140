import '../../../generated/json/base/json_convert_content.dart';
import '../../../bill/bill/channel/model/bill_channel_count_entity.dart';

BillChannelCountEntity $BillChannelCountEntityFromJson(
    Map<String, dynamic> json) {
  final BillChannelCountEntity billChannelCountEntity = BillChannelCountEntity();
  final int? doneCount = jsonConvert.convert<int>(json['doneCount']);
  if (doneCount != null) {
    billChannelCountEntity.doneCount = doneCount;
  }
  final int? waiteCount = jsonConvert.convert<int>(json['waiteCount']);
  if (waiteCount != null) {
    billChannelCountEntity.waiteCount = waiteCount;
  }
  return billChannelCountEntity;
}

Map<String, dynamic> $BillChannelCountEntityToJson(
    BillChannelCountEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['doneCount'] = entity.doneCount;
  data['waiteCount'] = entity.waiteCount;
  return data;
}

extension BillChannelCountEntityExtension on BillChannelCountEntity {
  BillChannelCountEntity copyWith({
    int? doneCount,
    int? waiteCount,
  }) {
    return BillChannelCountEntity()
      ..doneCount = doneCount ?? this.doneCount
      ..waiteCount = waiteCount ?? this.waiteCount;
  }
}