import 'package:flutter/material.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_utils/utils/date_util.dart' as DateUtil;
import 'package:haloui/haloui.dart';

import '../common/tool/dialog_util.dart';
import '../common/tool/sp_tool.dart';
import '../login/entity/store/store_etype.dart';
import '../login/entity/store/store_info.dart';
import '../widgets/base/base_stateful_page.dart';
import 'entity/stock_check_bill_query_entity.dart';
import 'entity/stock_check_info_entity.dart';
import 'entity/stock_check_record_entity.dart';
import 'entity/stock_check_save_entity.dart';
import 'model/stock_check_model.dart';
import 'request/stock_create_entity.dart';
import 'widget/filter_box_widget.dart';
import 'widget/stock_ptype_select.dart';
import 'widget/stock_table_check_widget.dart';

class StockCheckPage extends BaseStatefulPage {
  final StockCheckRecordEntity? recordEntity;
  const StockCheckPage({Key? key,this.recordEntity}) : super(key: key);

  @override
  BaseStatefulPageState<StockCheckPage> createState() => _StockCheckPageState();
}

class _StockCheckPageState extends BaseStatefulPageState<StockCheckPage> {
  String checkId = "";
  String etypeId = "";

  StoreInfo storeInfo = SpTool.getStoreInfo()!;
  final GlobalKey<StockCheckTableWidgetState> _tableGlobalKey = GlobalKey();
  CheckTableType checkTableType = CheckTableType.select;
  ///全部商品数据
  // ValueNotifierList<StockCheckInfoEntity> dataSourceSelectNotifier = ValueNotifierList([]);
  TextEditingController textTimeController = TextEditingController();
  TextEditingController textBillNumberController = TextEditingController();
  TextEditingController textETypeController = TextEditingController();
  TextEditingController textScanController = TextEditingController();

  //记录进入的当前时间
  DateTime currentTime = DateTime.now();

  List<StoreEtype> etypeList = SpTool.getStoreInfo()!.etypeList!;

  int isSelectIndex = 0;

  final ScrollController scrollController = ScrollController();

  bool isEdit = true;

  @override
  Widget buildLeftBody(BuildContext context) {
    // TODO: implement buildLeftBody
    return HaloContainer(
      direction: Axis.vertical,
      children: [
        _buildTopFilter(),
        Divider(
          height: 1.w,
        ),
        _buildTable(),
        // HaloContainer(
        //   padding: EdgeInsets.only(
        //       left: ScreenUtil().setWidth(24),
        //       bottom: ScreenUtil().setWidth(20)),
        //   mainAxisSize: MainAxisSize.max,
        //   mainAxisAlignment: MainAxisAlignment.start,
        //   children: [
        //     _buildSelectBtn("总商品数123", 0, isSelectIndex == 0, (i) {
        //       setState(() {
        //         isSelectIndex = i;
        //       });
        //     }),
        //     _buildSelectBtn("已盘点123", 1, isSelectIndex == 1, (i) {
        //       setState(() {
        //         isSelectIndex = i;
        //       });
        //     }),
        //     _buildSelectBtn("未盘点123", 2, isSelectIndex == 2, (i) {
        //       setState(() {
        //         isSelectIndex = i;
        //       });
        //     }),
        //   ],
        // ),
        Padding(
          padding: const EdgeInsets.only(bottom: 30),
          child: Visibility(visible: isEdit, child: _buildBottom()),
        )
      ],
    );
  }

  _buildTopFilter() {
    return HaloContainer(
        direction: Axis.vertical,
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        margin: EdgeInsets.only(left: 30.w),
        children: [
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              FilterBox(
                filterTitle: "盘点编号",
                enabled: false,
                filterBoxType: FilterBoxType.text,
                textEditingController: textBillNumberController,
                width: 350,
              ),
              const SizedBox(
                width: 100,
              ),
              FilterBox<String>(
                filterTitle: "盘点人",
                filterBoxType: FilterBoxType.drop,
                textEditingController: textETypeController,
                width: 300,
                value: etypeId,
                enabled: isEdit,
                dropItems: getETypeFilterBoxItem(),
              ),
              const SizedBox(
                width: 100,
              ),
              FilterBox(
                filterTitle: "盘点时间",
                enabled: false,
                filterBoxType: FilterBoxType.text,
                textEditingController: textTimeController,
                width: 300,
              ),
            ],
          ),
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _buildAddStockBtn(),
              const SizedBox(
                width: 50,
              ),
              _buildALLStockBtn(),
              const SizedBox(
                width: 100,
              ),
              FilterBox(
                filterTitle: "扫码盘点",
                filterBoxType: FilterBoxType.scan,
                textEditingController: textScanController,
                width: 500,
                enabled: isEdit,
                callback: (scanStr) {
                  getPtypesByScan(scanStr);
                },
              ),
            ],
          )
        ]);
  }

  // Widget _buildScanQuery() {
  //   return HaloContainer(
  //     mainAxisSize: MainAxisSize.max,
  //     color: widget.enabled?Colors.white: Colors.black12,
  //     margin: margin,
  //     padding: padding,
  //     width: widget.width,
  //     height: height,
  //     border: Border.all(color: borderColor, width: 1),
  //     children: [
  //       Padding(
  //         padding: const EdgeInsets.only(bottom: 3,right: 16),
  //         child: HaloPosLabel(
  //           "${widget.filterTitle}: ",
  //           textAlign: TextAlign.center,
  //           textStyle: TextStyle(
  //               fontSize: 24.sp,
  //               color: AppColorHelper(context).getTitleBoldTextColor()),
  //         ),
  //       ),
  //       Expanded(
  //         child: Keyboard.KeyboardHiddenTextField(
  //           hint: "支持扫码序列号/条码",
  //           focusNode: searchFocusNode,
  //           isShowClean: true,
  //           controller: widget.textEditingController,
  //           cleanTextWhenSearch: true,
  //           style: TextStyle(
  //               textBaseline: TextBaseline.alphabetic,
  //               color: AppColorHelper(context).getTitleBoldTextColor(),
  //               fontSize: 28.sp),
  //           onSubmitted: (text) {
  //             onSubmittedButton();
  //           },
  //         ),
  //       ),
  //     ],
  //   );
  // }

  _buildTable() {
    return Expanded(
        child: StockCheckTableWidget(
          isEdit: isEdit,
          key: _tableGlobalKey,
      checkId: checkId,
      checkTableType: checkTableType,
     scrollC: scrollController,checkCallback: (stockCheckInfoList,insert,isBatch){
            createOrInsertOrUpdate(stockCheckInfoList, insert,isBatch);
        },
          deleteCallback: (String detailId,bool isDeleteCheckId){
            deleteDetail(detailId, isDeleteCheckId);
          },
    ));
  }

  Widget _buildAddStockBtn() {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      margin: const EdgeInsets.only(left: 10),
      children: [
        HaloButton(
          enabled: (checkTableType == CheckTableType.select && isEdit),
          buttonType: HaloButtonType.outlinedButton,
          borderRadius: 1.sp,
          outLineWidth: 0.5,
          text: "商品选择",
          fontSize: 25.sp,
          height: 52.h,
          width: 160.w,
          borderColor: ColorUtil.stringColor("333333"),
          textColor: ColorUtil.stringColor("333333"),
          onPressed: () {
            DialogUtil.showAlertDialog(context, child: StockPtypeSelect(onChanged: ( List<StockCheckInfoEntity> item){
              selectAddPtype(item);
            },));
          },
        ),
      ],
    );
  }

  Widget _buildALLStockBtn() {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      margin: const EdgeInsets.only(left: 10),
      children: [
        HaloButton(
          buttonType: HaloButtonType.outlinedButton,
          borderRadius: 1.sp,
          outLineWidth: 0.5,
          text: "全仓盘点",
          fontSize: 25.sp,
          enabled: isEdit,
          height: 52.h,
          width: 160.w,
          borderColor: ColorUtil.stringColor("333333"),
          textColor: ColorUtil.stringColor("333333"),
          onPressed: () {
            setState(() {
              checkId = '';
              checkTableType = CheckTableType.all;
            });
          },
        ),
      ],
    );
  }

  Widget _buildSelectBtn(
      String title, int i, bool isSelect, void Function(int) callBack) {
    return HaloButton(
      buttonType: HaloButtonType.outlinedButton,
      borderRadius: 1.sp,
      outLineWidth: 0.5,
      text: title,
      fontSize: 25.sp,
      height: 52.h,
      borderColor: isSelect ? Colors.blue : Colors.black,
      textColor: isSelect ? Colors.blue : Colors.black,
      onPressed: () {
        callBack(i);
      },
    );
  }

  Widget _buildBottom() {
    return HaloContainer(
      color: Colors.white,
      children: [
        HaloButton(
          buttonType: HaloButtonType.elevatedButton,
          borderRadius: 1.sp,
          outLineWidth: 0.5,
          text: "完成盘点",
          fontSize: 25.sp,
          height: 52.h,
          onPressed: () {
            saveInout();
          },
        ),
        const SizedBox(
          width: 100,
        ),
        HaloButton(
          buttonType: HaloButtonType.outlinedButton,
          borderRadius: 1.sp,
          outLineWidth: 0.5,
          text: "保存盘点记录",
          fontSize: 25.sp,
          height: 52.h,
          borderColor: Colors.black,
          textColor: Colors.black,
          onPressed: () {
            SaveCheckRecord();
          },
        )
      ],
    );
  }

  List<FilterBoxItem> getETypeFilterBoxItem() {
    List<FilterBoxItem> items = [];
    items.insert(0, FilterBoxItem(text: "全部", value: ""));
    items.addAll(etypeList.map((e) {
      return FilterBoxItem(text: e.etypeName!, value: e.etypeId!);
    }).toList());
    return items;
  }

  getPtypesByScan(String scan){
    StockCheckModel.searchPtypeByScan(context,scan).then((entity) {

      if(entity.isEmpty) {
        HaloToast.show(context, msg: "找不到对应商品");
        return;
      }

      StockCheckBillQueryEntity stockCheckBillQueryEntity = StockCheckBillQueryEntity();
      for (StockCheckInfoEntity entity in entity) {
        stockCheckBillQueryEntity.ptypeIds!.add(entity.id!);
        stockCheckBillQueryEntity.skuIds!.add(entity.skuId??"0");
        stockCheckBillQueryEntity.ktypeId = storeInfo.ktypeId;
        stockCheckBillQueryEntity.checkBatchStock = true;
        stockCheckBillQueryEntity.scanSnAndBarcode = true;
        StockCheckBillQueryCheckPtypes  checkPtypes = StockCheckBillQueryCheckPtypes();
        checkPtypes.id = entity.ptypeId!.isEmpty?entity.id:entity.ptypeId;
        checkPtypes.skuId = entity.skuId??"0";
        checkPtypes.propenabled = entity.propenabled;
        checkPtypes.propvalueName1 = entity.propvalueName1;
        checkPtypes.propvalueName2 = entity.propvalueName2;
        checkPtypes.propvalueName3 = entity.propvalueName3;
        checkPtypes.propvalueName4 = entity.propvalueName4;
        checkPtypes.propvalueName5 = entity.propvalueName5;
        checkPtypes.propvalueName6 = entity.propvalueName6;
        stockCheckBillQueryEntity.checkPtypes!.add(checkPtypes);
        if(entity.snno!=null){
          stockCheckBillQueryEntity.snNo = entity.snno;
        }
      }

      StockCheckModel.getPtypes(context, stockCheckBillQueryEntity).then((value) {
        if(value.isEmpty){
          HaloToast.show(context,msg: "找不到对应商品");
        }else{
          createOrInsertOrUpdate(value,true,true,isScanPtype:true,scan: scan,isSnno: stockCheckBillQueryEntity.snNo!=null);
        }
      });

    });
  }

  selectAddPtype(List<StockCheckInfoEntity> entity){
    createOrInsertOrUpdate(entity,checkTableType!=CheckTableType.all,false);
    // scrollController.jumpTo(scrollController.position.maxScrollExtent);
  }


  createOrInsertOrUpdate(List<StockCheckInfoEntity> entity,insertCheck,isBatch,{bool isScanPtype = false,String scan ='',bool isSnno =false}){
    if(checkId.isEmpty){
      //盘点Id是空的要处理创建
      StockCreateEntity createEntity = StockCreateEntity();
      createEntity.checkBatchStock = true;
      createEntity.checkEtypeId = textETypeController.text.isNotEmpty? textETypeController.text:"";
      createEntity.checkId = 0;
      createEntity.checkKtypeId = storeInfo.ktypeId;
      createEntity.checkMode = 0;
      createEntity.checkPtypes = entity;
      createEntity.checkTime = DateUtil.DateUtil.getDateStrByDateTime(currentTime);
      createEntity.reCheck = false;
      createEntity.number = textBillNumberController.text;
      createEntity.summary = '盘点开单';
      if(isScanPtype){
        createEntity.scanMode = "barcode";
        createEntity.scanPtypeFilter = scan;
      }

      if(isSnno){
        createEntity.scanMode = "snno";
        createEntity.scanPtypeFilter = scan;
      }

      StockCheckModel.createCheck(context, createEntity).then((value) {
        //刷新
        checkId = value.checkId!;
        reloadTab(needLoad: true,isScanPtype: isScanPtype);
      });
    }else{
      if(insertCheck){
        //非全仓盘点-插入新数据
        //盘点Id是空的要处理创建
        StockCreateEntity insertEntity = StockCreateEntity();
        insertEntity.checkBatchStock = true;
        insertEntity.checkId = int.parse(checkId);
        insertEntity.checkMode = 0;
        insertEntity.checkPtypes = entity;
        if(isScanPtype){
          insertEntity.scanMode = "barcode";
          insertEntity.scanPtypeFilter = scan;
        }
        if(isSnno){
          insertEntity.scanMode = "snno";
          insertEntity.scanPtypeFilter = scan;
        }

        StockCheckModel.insertCheckDetail(context, insertEntity).then((value) {
          if(value.message?.isNotEmpty??false){
            HaloToast.show(context,msg: value.message!);
          }
          if(entity.length == 1){
            entity.first.detailId =value.detailId;
          }
          reloadTab(needLoad: isBatch,isScanPtype:isScanPtype);
        });
      }else{
        //更新的情况只会存在一个元素
        StockCheckInfoEntity stockCheckInfoEntity = entity.first;
        stockCheckInfoEntity.stockCheckId = checkId;
        stockCheckInfoEntity.snenabled = stockCheckInfoEntity.snEnabled;
        StockCheckModel.updateCheckDetail(context, stockCheckInfoEntity).then((value) {
          //不刷新直接更新
          reloadTab();
        });
      }
    }
  }
  reloadTab({ bool needLoad = false,bool isScanPtype = false}){
    //刷新
    if(checkTableType == CheckTableType.select || needLoad){
      _tableGlobalKey.currentState?.reloadData(checkId,isScanPtype:isScanPtype);
    }else{
      _tableGlobalKey.currentState?.refreshItem();
    }
  }

  ///删除
  deleteDetail(String detailId,isDeleteCheckId){
    if(isDeleteCheckId){
      StockCheckModel.deleteDetailByCheckId(context, checkId).then((value){
        checkId = '';
        reloadTab(needLoad: true);
      });
    }else{
      StockCheckModel.deleteDetail(context, detailId).then((value){
        reloadTab(needLoad: true);
      });
    }
  }

  ///保存盘点记录
  SaveCheckRecord() {
    if(checkId.isEmpty){
      HaloToast.show(context,msg: "请输入盘点数量");
      return;
    }
    StockCheckSaveEntity saveEntity = StockCheckSaveEntity();
    saveEntity.stockCheckId = checkId;
    saveEntity.etypeId = textETypeController.text;
    saveEntity.memo = '新增盘点，保存盘点';
    StockCheckModel.saveCheck(context, saveEntity).then((value){
      Navigator.pop(context);
    });
  }
  //完成盘点
  saveInout(){
    if(checkId.isEmpty){
      HaloToast.show(context,msg: "请输入盘点数量");
      return;
    }
    StockCheckSaveEntity saveEntity = StockCheckSaveEntity();
    saveEntity.stockCheckId = checkId;
    saveEntity.etypeId = textETypeController.text;
    StockCheckModel.saveInout(context, saveEntity).then((value){
      Navigator.pop(context);
    });
  }

  @override
  Future<void>? onInitState() {
    if(widget.recordEntity!=null){
      //非新增盘点
      //获取历史盘点信息
      StockCheckModel.getCheckBillInfo(context, widget.recordEntity!.id!).then((value) {
        StockCreateEntity createEntity = value;
        checkId = createEntity.checkId.toString();
        textBillNumberController.text = createEntity.number!;
        textTimeController.text = formatDateStringToLocal(value.checkTime);
        if(createEntity.checkType == 1){
          checkTableType = CheckTableType.select;
        }else{
          checkTableType = CheckTableType.all;
        }
        isEdit = widget.recordEntity?.checkState! == 0;
        etypeId = value.checkEtypeId!;
        setState(() {

        });
        reloadTab(needLoad: true);
      });
    }else{
      textTimeController.text =
      DateUtil.DateUtil.getDateStrByDateTime(currentTime)!;
      StockCheckModel.getBillNumber(context).then((value) {
        textBillNumberController.text = value;
      });
      etypeId = etypeList.first.etypeId!;
    }

    textETypeController.addListener(() {
      StockCheckModel.updateCheckModel(context, etypeId:textETypeController.text,checkId:checkId);
    });
  }


  @override
  String getActionBarTitle() {
    return "库存盘点";
  }

  @override
  void dispose() {
    textETypeController.removeListener(() { });
    super.dispose();
  }

}
