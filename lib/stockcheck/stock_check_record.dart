import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:halo_utils/utils/date_util.dart' as DateUtil;
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';

import '../common/change_notifier.dart';
import '../common/style/app_colors.dart';
import '../common/tool/sp_tool.dart';
import '../entity/system/permission_dto.dart';
import '../login/entity/store/store_etype.dart';
import '../widgets/base/base_stateful_page.dart';
import 'entity/stock_check_record_entity.dart';
import 'stock_check.dart';
import 'widget/filter_box_widget.dart';
import 'widget/stock_table_record_widget.dart';

///库存盘点
class StockCheckRecord extends BaseStatefulPage {
  const StockCheckRecord({Key? key}) : super(key: key);

  @override
  BaseStatefulPageState createState() => _StockCheckRecordState();
}

class _StockCheckRecordState extends BaseStatefulPageState<StockCheckRecord> {
  ///权限
  final PermissionDto _permissionDto = SpTool.getPermission();
  ///当前单据状态筛选条件
  late FilterStockController _filter;
  TextEditingController textStartTimeController = TextEditingController();
  TextEditingController textEndTimeController = TextEditingController();
  TextEditingController textBillNumberController = TextEditingController();
  TextEditingController textETypeController = TextEditingController();
  TextEditingController textFilterFinishController = TextEditingController();

  //记录进入的当前时间 避免每次获取当前时间 导致限制最大最小时间不准确

  String endDateString = DateUtil.DateUtil.getDateStrByDateTime(
    DateTime.now(),
    format: DateFormat.YEAR_MONTH_DAY,
  )!;

  String starDateString = DateUtil.DateUtil.getDateStrByDateTime(
    DateTime.now().add(const Duration(days: -30)),
    format: DateFormat.YEAR_MONTH_DAY,
  )!;

  late DateTime beforeTime;
  late DateTime afterTime;

  List<StoreEtype> etypeList = SpTool.getStoreInfo()!.etypeList!;
  final ScrollController scrollController = ScrollController();


  @override
  Widget buildLeftBody(BuildContext context) {
    // TODO: implement buildLeftBody
    return HaloContainer(
      direction: Axis.vertical,
      children: [
        _buildTopFilter(),
        Divider(height: 1.w,),
        _buildAddStockBtn(),
        _buildTable(),
      ],
    );
  }

  Widget _buildTopFilter(){
    return
      HaloContainer(
        direction:Axis.vertical,
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        margin: EdgeInsets.only(left: 30.w),
        children: [
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              FilterBox(
                filterTitle: "盘点时间",
                filterBoxType: FilterBoxType.date,
                textStartTimeController: textStartTimeController,
                textEndTimeController: textEndTimeController,
                beforeTime: beforeTime,
                afterTime: afterTime,
                width: 500,
              ),
              const SizedBox(width: 100,),
              FilterBox(
                filterTitle: "盘点人",
                filterBoxType: FilterBoxType.drop,
                textEditingController: textETypeController,
                width: 400,
                dropItems: getETypeFilterBoxItem(),
              ),
            ],
          ),
          HaloContainer(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              FilterBox(
                filterTitle: "盘点编号",
                filterBoxType: FilterBoxType.text,
                textEditingController: textBillNumberController,
                width: 500,
              ),
              const SizedBox(width: 100,),
              FilterBox(
                filterTitle: "完成状态",
                filterBoxType: FilterBoxType.drop,
                textEditingController: textFilterFinishController,
                width: 400,
                dropItems: [
                  FilterBoxItem(text: "全部", value: "0"),
                  FilterBoxItem(text: "未完成", value: "1"),
                  FilterBoxItem(text: "已完成", value: "2")
                ],
              ),
              const SizedBox(width: 100,),
              HaloButton(
                borderRadius: 5.sp,
                text: "查询",
                fontSize: 25.sp,
                height: 52.h,
                width: 100.w,
                backgroundColor: AppColors.accentColor,
                onPressed: () {
                  FocusScopeNode currentFocus = FocusScope.of(context);
                  if (!currentFocus.hasPrimaryFocus &&
                      currentFocus.focusedChild != null) {
                    FocusManager.instance.primaryFocus?.unfocus();
                  }
                  _filter._notify();
                },
              ),
            ],
          )
        ],
      );
  }

  Widget _buildAddStockBtn(){
   return HaloContainer(
     mainAxisSize: MainAxisSize.max,
     mainAxisAlignment: MainAxisAlignment.start,
     padding: EdgeInsets.only(left: 30,top: 15),
     children: [
       HaloButton(
         buttonType: HaloButtonType.outlinedButton,
         borderRadius: 1.sp,
         outLineWidth: 0.5,
         text: "新增盘点",
          fontSize: 25.sp,
          height: 52.h,
          width: 160.w,
         borderColor: ColorUtil.stringColor("333333"),
          textColor: ColorUtil.stringColor("333333"),
          onPressed: () {
           if(_permissionDto.analysiscloudStockCheckRecordcreate??false){
             Navigator.push(
                 context,
                 MaterialPageRoute(
                     builder: (context) =>
                     const StockCheckPage())).then((value) {
               _filter._notify();

             });
           }else{
             HaloToast.showError(context,msg: "没有新增盘点的权限");
           }

          },
        ),
     ],
   );
  }

  List<FilterBoxItem> getETypeFilterBoxItem(){
    List<FilterBoxItem> items = [];
    items.insert(0, FilterBoxItem(text: "全部", value: ""));
    items.addAll(etypeList.map((e) {
      return FilterBoxItem(text: e.etypeName!, value: e.etypeId!);
    }).toList());
    return items;
  }

  Widget _buildTable() {
    return  Expanded(
      child: StockTableWidget(
        filterController: _filter,scrollC: scrollController,checkCallback: (StockCheckRecordEntity data){
          if(data.checkState == 0 && !(_permissionDto.analysiscloudStockCheckRecordmodify??false)){
            HaloToast.showError(context,msg: "你没有修改盘点记录的权限");
            return;
          }
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) =>
                 StockCheckPage(recordEntity: data,))).then((value) {
          _filter._notify();
        });
      },
      ),
    );
  }



  @override
  String getActionBarTitle() {
    return "库存盘点记录";
  }

  @override
  Future<void>? onInitState() {

    endDateString = '$endDateString 23:59:59';
    afterTime = DateUtil.DateUtil.getDateTime(endDateString)!;
    starDateString = '$starDateString 00:00:00';
     beforeTime= DateUtil.DateUtil.getDateTime(starDateString)!;

    textStartTimeController.text =
    DateUtil.DateUtil.getDateStrByDateTime(beforeTime)!;
    textEndTimeController.text =
    DateUtil.DateUtil.getDateStrByDateTime(afterTime)!;

    //初始化订阅实体
    _filter = FilterStockController._(
        startDate: textStartTimeController.text,
        endDate: textEndTimeController.text,
        filterValue: textBillNumberController.text,
        filterFinish: textFilterFinishController.text,
        etypeId: textETypeController.text);

    //监听条件变化 _filterController.postState.interceptNotify = true;
    textStartTimeController.addListener(
            () {
              _filter.startDate.interceptNotify = true;
          _filter.startDate.value = textStartTimeController.text;
              _filter.startDate.interceptNotify = false;
            });
    textEndTimeController.addListener(
            () {
              _filter.endDate.interceptNotify = true;
              _filter.endDate.value = textEndTimeController.text;
              _filter.endDate.interceptNotify = false;

            });
    textBillNumberController.addListener(
            () {
              _filter.filterValue.interceptNotify = true;
              _filter.filterValue.value = textBillNumberController.text;
              _filter.filterValue.interceptNotify = false;
            });
    textETypeController.addListener(
            () {
              _filter.etypeId.interceptNotify = true;
              _filter.etypeId.value = textETypeController.text;
              _filter.etypeId.interceptNotify = false;
            });
    textFilterFinishController.addListener(
            () {
              _filter.filterFinish.interceptNotify = true;
              _filter.filterFinish.value = textFilterFinishController.text;
              _filter.filterFinish.interceptNotify = false;
            });

  }

}

///筛选条件的订阅通知
class FilterStockController extends ChangeNotifier {
  ///开始时间
  final InterceptValueNotifier<String> startDate;

  ///结束时间
  final InterceptValueNotifier<String> endDate;

  ///单据编号
  final InterceptValueNotifier<String> filterValue;

  ///完成状态
  final InterceptValueNotifier<String> filterFinish;

  ///盘点人
  final InterceptValueNotifier<String> etypeId;

  FilterStockController._(
      {required String startDate,
        required String endDate,
        required String filterValue,
        required String filterFinish,
        required String etypeId})
      : startDate = InterceptValueNotifier(startDate),
        endDate = InterceptValueNotifier(endDate),
        filterValue = InterceptValueNotifier(filterValue),
        filterFinish = InterceptValueNotifier(filterFinish),
        etypeId = InterceptValueNotifier(etypeId);

  ///强制通知listener，用于右侧单据操作后，刷新左侧列表
  void _notify() {
    notifyListeners();
  }

  @override
  void addListener(VoidCallback listener) {
    super.addListener(listener);
    startDate.addListener(listener);
    endDate.addListener(listener);
    filterValue.addListener(listener);
    filterFinish.addListener(listener);
    etypeId.addListener(listener);
  }

  @override
  void removeListener(VoidCallback listener) {
    super.removeListener(listener);
    startDate.removeListener(listener);
    endDate.removeListener(listener);
    filterValue.removeListener(listener);
    filterFinish.removeListener(listener);
    etypeId.removeListener(listener);
  }

  @override
  void dispose() {
    super.dispose();
    startDate.dispose();
    endDate.dispose();
    filterValue.dispose();
    filterFinish.dispose();
    etypeId.dispose();
  }
}
