
class StockCheckOverEntity{
  //批次号是否重复
   bool?  batchnoRepeat = false;
  //返回的提示信息
   String? checkMessage = "";

   StockCheckOverEntity({this.batchnoRepeat,this.checkMessage});

   StockCheckOverEntity.fromMap(Map<String, dynamic> map){
     batchnoRepeat = map["batchnoRepeat"];
     checkMessage = map["checkMessage"];
   }

   Map toJson() => {
     "batchnoRepeat": batchnoRepeat,
     "checkMessage": checkMessage,
   };
}