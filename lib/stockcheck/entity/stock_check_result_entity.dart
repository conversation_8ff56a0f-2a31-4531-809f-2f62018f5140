
import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_result_entity.g.dart';

@JsonSerializable()
class StockCheckResultEntity {
	String? message = '';
	dynamic checkId;
	dynamic detailId;
	List<StockCheckResultRepeatDetails>? repeatDetails = [];

	StockCheckResultEntity();

	factory StockCheckResultEntity.fromJson(Map<String, dynamic> json) => $StockCheckResultEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckResultEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StockCheckResultRepeatDetails {
	dynamic ptypeId;
	String? picUrl = '';
	String? usercode = '';
	dynamic pFullname;
	dynamic shortname;
	dynamic brandName;
	String? standard = '';
	String? ptypeType = '';
	String? unitName = '';
	dynamic subUnitName;
	dynamic ptypeMemo;
	dynamic fullbarcode;
	String? skuId = '';
	String? xcode = '';
	dynamic taxRate;
	int? skuPrice = 0;
	dynamic pcategory;
	dynamic industryCategory;
	dynamic parName1;
	dynamic parName2;
	dynamic parName3;
	dynamic parName4;
	dynamic parName5;
	dynamic ptypeWeight;
	dynamic unitRelation;
	dynamic picUrls;
	int? skuName = 0;
	String? propNames = '';
	String? propValues = '';
	dynamic propValueName1;
	dynamic propValueName2;
	String? batchno = '';
	dynamic produceDate;
	dynamic expireDate;
	dynamic defProduceDate;
	dynamic defExpireDate;
	dynamic customHead01;
	dynamic customHead02;
	dynamic customHead03;
	dynamic customHead04;
	dynamic customHead05;
	dynamic customHead06;
	dynamic customHead07;
	dynamic customHead08;
	dynamic customHead09;
	dynamic customHead10;
	dynamic customHead11;
	dynamic customHead12;
	dynamic customHead13;
	dynamic customHead14;
	dynamic subUnit;
	dynamic subQty;
	dynamic subInQty;
	dynamic subOutQty;
	dynamic subInOutQty;
	dynamic subInStockQty;
	dynamic subInOutPercent;
	dynamic subDiffQty;
	bool? hasPerm = false;
	dynamic baseUnitName;
	String? unitxqty = '';
	dynamic ptypeUnits;
	List<dynamic>? mySnList = [];
	List<dynamic>? checkSnList = [];
	String? snnoStrButton = '';
	int? unitCode = 0;
	dynamic unitRate;
	String? unitId = '';
	String? detailId = '';
	String? stockCheckId = '';
	int? batchPrice = 0;
	int? batchQty = 0;
	int? batchLifeQty = 0;
	dynamic stockPrice;
	String? id = '';
	dynamic profileId;
	String? fullname = '';
	dynamic shortName;
	dynamic namePy;
	dynamic classed;
	dynamic stoped;
	dynamic deleted;
	dynamic rowindex;
	String? barcode = '';
	String? ptypeArea = '';
	dynamic memo;
	dynamic createType;
	int? costMode = 0;
	dynamic taxNumber;
	dynamic costPrice;
	dynamic supplyInfo;
	dynamic brandId;
	dynamic ktypeLimit;
	int? snenabled = 0;
	bool? propenabled = false;
	bool? batchenabled = false;
	int? protectDays = 0;
	int? protectDaysUnit = 0;
	dynamic protectWarndays;
	dynamic weight;
	dynamic weightUnit;
	dynamic lithiumBattery;
	dynamic solid;
	dynamic difficultyLevel;
	dynamic weighted;
	dynamic retailDefaultUnit;
	dynamic saleDefaultUnit;
	dynamic purchaseDefaultUnit;
	dynamic stockDefaultUnit;
	dynamic ptypeLength;
	dynamic ptypeWidth;
	dynamic ptypeHeight;
	dynamic lengthUnit;
	dynamic createTime;
	dynamic updateTime;
	String? batchlifeId = '';
	int? stockQty = 0;
	int? stockSubQty = 0;
	dynamic checkQty;
	dynamic checkSubQty;
	dynamic adjustQty;
	dynamic adjustSubQty;
	String? checkTypeStr = '';
	String? checkTime = '';
	bool? checked = false;
	bool? subChecked = false;
	String? propvalueName1 = '';
	String? propvalueName2 = '';
	dynamic propvalueName3;
	dynamic propvalueName4;
	dynamic propvalueName5;
	dynamic propvalueName6;
	String? propvalueAll = '';
	String? proDateStr = '';
	String? expDateStr = '';
	bool? selectchecked = false;
	dynamic snno;
	String? costId = '';
	dynamic positionStrText;
	dynamic positionList;
	dynamic inoutId;

	StockCheckResultRepeatDetails();

	factory StockCheckResultRepeatDetails.fromJson(Map<String, dynamic> json) => $StockCheckResultRepeatDetailsFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckResultRepeatDetailsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}