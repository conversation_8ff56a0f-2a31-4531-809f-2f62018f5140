
import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_bill_query_entity.g.dart';

@JsonSerializable()
class StockCheckBillQueryEntity {
	List<String>? ptypeIds = [];
	List<String>? skuIds = [];
	String? ktypeId = '';
	bool? checkBatchStock = false;
	bool? scanSnAndBarcode = false;
	bool? scanPtype = false;
	List<StockCheckBillQueryCheckPtypes>? checkPtypes = [];
	String? snNo = '';

	StockCheckBillQueryEntity();

	factory StockCheckBillQueryEntity.fromJson(Map<String, dynamic> json) => $StockCheckBillQueryEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckBillQueryEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StockCheckBillQueryCheckPtypes {
	String? id = '';
	String? skuId = '';
	String? propvalueName1 = '';
	String? propvalueName2 = '';
	String? propvalueName3 = '';
	String? propvalueName4 = '';
	String? propvalueName5 = '';
	String? propvalueName6 = '';
	bool? propenabled = false;

	StockCheckBillQueryCheckPtypes();

	factory StockCheckBillQueryCheckPtypes.fromJson(Map<String, dynamic> json) => $StockCheckBillQueryCheckPtypesFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckBillQueryCheckPtypesToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}