
import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_create_entity.g.dart';
@JsonSerializable()
class StockCheckCreateEntity {
	dynamic message;
	String? checkId = '';
	String? detailId = '';
	dynamic repeatDetails;

	StockCheckCreateEntity();

	factory StockCheckCreateEntity.fromJson(Map<String, dynamic> json) => $StockCheckCreateEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckCreateEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}