
import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_save_entity.g.dart';

@JsonSerializable()
class StockCheckSaveEntity {
	String? stockCheckId = '';
	String? memo = '';
	String? checkMemo = '';
	String? etypeId = '';

	StockCheckSaveEntity();

	factory StockCheckSaveEntity.fromJson(Map<String, dynamic> json) => $StockCheckSaveEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckSaveEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}