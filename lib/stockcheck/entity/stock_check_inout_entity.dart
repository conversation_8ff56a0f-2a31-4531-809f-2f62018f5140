import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_inout_entity.g.dart';

@JsonSerializable()
class StockCheckInoutEntity {
	StockCheckInoutQueryParams? queryParams;

	StockCheckInoutEntity();

	factory StockCheckInoutEntity.fromJson(Map<String, dynamic> json) => $StockCheckInoutEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckInoutEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StockCheckInoutQueryParams {
	String? source = '';
	String? stockCheckId = '';

	StockCheckInoutQueryParams();

	factory StockCheckInoutQueryParams.fromJson(Map<String, dynamic> json) => $StockCheckInoutQueryParamsFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckInoutQueryParamsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}