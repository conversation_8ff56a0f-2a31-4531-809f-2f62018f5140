
import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_record_entity.g.dart';

@JsonSerializable()
class StockCheckRecordEntity {
	String? id = '';
	String? ktypeUsercode = '';
	String? ktypeId = '';
	String? ktypeName = '';
	String? otypeId = '';
	dynamic otypeName;
	String? etypeName = '';
	String? number = '';
	String? checkTime = '';
	int? checkState = 0;
	String? checkStateName = '';
	String? memo = '';
	String? summary = '';
	int? checkType = 0;

	StockCheckRecordEntity();

	factory StockCheckRecordEntity.fromJson(Map<String, dynamic> json) => $StockCheckRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}