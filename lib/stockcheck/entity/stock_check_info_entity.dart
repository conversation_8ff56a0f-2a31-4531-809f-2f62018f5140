import 'package:flutter/cupertino.dart';
import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_info_entity.g.dart';

@JsonSerializable()
class StockCheckInfoEntity {
	//前端字段
	TextEditingController textEditingController = TextEditingController();
	int index = 0;

	String? ptypeId = '';
	dynamic picUrl;
	String? usercode = '';
	dynamic pFullname;
	dynamic shortname;
	dynamic brandName;
	String? standard = '';
	String? ptypeType = '';
	String? unitName = '';
	String? subUnitName = '';
	dynamic ptypeMemo;
	dynamic fullbarcode;
	String? skuId = '';
	dynamic xcode;
	dynamic taxRate;
	int? skuPrice = 0;
	dynamic pcategory;
	int? industryCategory = 0;
	dynamic parName1;
	dynamic parName2;
	dynamic parName3;
	dynamic parName4;
	dynamic parName5;
	dynamic ptypeWeight;
	dynamic unitRelation;
	dynamic picUrls;
	int? skuName = 0;
	String? propNames = '';
	String? propValues = '';
	dynamic propValueName1;
	dynamic propValueName2;
	String? batchno = '';
	String? produceDate = '';
	String? expireDate = '';
	dynamic defProduceDate;
	dynamic defExpireDate;
	dynamic customHead01;
	dynamic customHead02;
	dynamic customHead03;
	dynamic customHead04;
	dynamic customHead05;
	dynamic customHead06;
	dynamic customHead07;
	dynamic customHead08;
	dynamic customHead09;
	dynamic customHead10;
	dynamic customHead11;
	dynamic customHead12;
	dynamic customHead13;
	dynamic customHead14;
	dynamic subUnit;
	dynamic subQty;
	dynamic subInQty;
	dynamic subOutQty;
	dynamic subInOutQty;
	dynamic subInStockQty;
	dynamic subInOutPercent;
	dynamic subDiffQty;
	bool? hasPerm = false;
	dynamic baseUnitName;
	String? unitxqty = '';
	dynamic ptypeUnits;
	List<StockCheckInfoMySnList>? mySnList = [];
	List<StockCheckInfoMySnList>? checkSnList = [];
	String? snnoStrButton = '';
	int? unitCode = 0;
	dynamic unitRate;
	String? unitId = '';
	String? detailId = '';
	String? stockCheckId = '';
	int? batchPrice = 0;
	double? batchQty;
	double? batchLifeQty;
	int? stockPrice = 0;
	String? id = '';
	dynamic profileId;
	String? fullname = '';
	dynamic shortName;
	dynamic namePy;
	dynamic classed;
	dynamic stoped;
	dynamic deleted;
	dynamic rowindex;
	String? barcode = '';
	String? ptypeArea = '';
	dynamic memo;
	dynamic createType;
	int? costMode = 0;
	dynamic taxNumber;
	int? costPrice = 0;
	dynamic supplyInfo;
	dynamic brandId;
	dynamic ktypeLimit;
	int? snenabled = 0;
	int? snEnabled = 0;
	bool? propenabled = false;
	bool? batchenabled = false;
	int? protectDays = 0;
	int? protectDaysUnit = 0;
	dynamic protectWarndays;
	dynamic weight;
	dynamic weightUnit;
	dynamic lithiumBattery;
	dynamic solid;
	dynamic difficultyLevel;
	dynamic weighted;
	dynamic retailDefaultUnit;
	dynamic saleDefaultUnit;
	dynamic purchaseDefaultUnit;
	dynamic stockDefaultUnit;
	dynamic ptypeLength;
	dynamic ptypeWidth;
	dynamic ptypeHeight;
	dynamic lengthUnit;
	dynamic createTime;
	dynamic updateTime;
	String? batchlifeId = '';
	double? stockQty;
	int? stockSubQty = 0;
	double? checkQty;
	dynamic checkSubQty;
	dynamic adjustQty;
	dynamic adjustSubQty;
	String? checkTypeStr = '';
	String? checkTime = '';
	bool? checked = false;
	bool? subChecked = false;
	String? propvalueName1 = '';
	String? propvalueName2 = '';
	String? propvalueName3 = '';
	String? propvalueName4 = '';
	String? propvalueName5 = '';
	String? propvalueName6 = '';
	String? propvalueAll = '';
	String? proDateStr = '';
	String? expDateStr = '';
	bool? selectchecked = false;
	dynamic snno;
	String? costId = '';
	dynamic positionStrText;
	dynamic positionList;
	dynamic inoutId;

	StockCheckInfoEntity();

	factory StockCheckInfoEntity.fromJson(Map<String, dynamic> json) => $StockCheckInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StockCheckInfoMySnList {
	String? id = '';
	String? batchNo = '';
	String? batchId = '';
	dynamic detailId;
	String? snno = '';
	String? sn1 = '';
	String? sn2 = '';
	String? sn3 = '';
	String? memo = '';
	dynamic profileId;
	dynamic produceDate;
	dynamic expireDate;
	String? proDateStr = '';
	String? expDateStr = '';
	String? ptypeId = '';
	String? skuId = '';
	int? batchPrice = 0;

	StockCheckInfoMySnList();

	factory StockCheckInfoMySnList.fromJson(Map<String, dynamic> json) => $StockCheckInfoMySnListFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckInfoMySnListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}