
import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_infos_request_entity.g.dart';

@JsonSerializable()
class StockCheckInfosRequestEntity {
	int? pageSize = 0;
	int? pageIndex = 0;
	StockCheckInfosRequestQueryParams queryParams = StockCheckInfosRequestQueryParams();
	dynamic sorts;

	StockCheckInfosRequestEntity();

	factory StockCheckInfosRequestEntity.fromJson(Map<String, dynamic> json) => $StockCheckInfosRequestEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckInfosRequestEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StockCheckInfosRequestQueryParams {
	String? checkId = '';
	String ktypeId = '';
	int? checkType = 0;
	bool? checkScanPtype = false;
	int? viewMode = 0;
	List<dynamic>? gridFilter = [];
	bool? checkAllStock = false;
	String filterValue = '';



	StockCheckInfosRequestQueryParams();

	factory StockCheckInfosRequestQueryParams.fromJson(Map<String, dynamic> json) => $StockCheckInfosRequestQueryParamsFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckInfosRequestQueryParamsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}