
import 'dart:convert';

import '../../generated/json/base/json_field.dart';
import '../../generated/json/stock_check_record_request_entity.g.dart';

@JsonSerializable()
class StockCheckRecordRequestEntity {
	bool? refresh = false;
	StockCheckRecordRequestQueryParams queryParams = StockCheckRecordRequestQueryParams();
	int? pageSize = 0;
	int? pageIndex = 0;
	int? first = 0;
	int? count = 0;

	StockCheckRecordRequestEntity();

	factory StockCheckRecordRequestEntity.fromJson(Map<String, dynamic> json) => $StockCheckRecordRequestEntityFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckRecordRequestEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StockCheckRecordRequestQueryParams {
	dynamic filter;
	dynamic selectType;
	String? isPC = '';
	dynamic batchno;
	String? startDate = '';
	String? endDate = '';
	String? filterValue = '';
	dynamic ktypeId;
	String? ktypeName = '';
	dynamic etypeId;
	int? filterFinish = 0;

	StockCheckRecordRequestQueryParams();

	factory StockCheckRecordRequestQueryParams.fromJson(Map<String, dynamic> json) => $StockCheckRecordRequestQueryParamsFromJson(json);

	Map<String, dynamic> toJson() => $StockCheckRecordRequestQueryParamsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}