import 'package:flutter/cupertino.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:haloui/haloui.dart';

import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../common/tool/sp_tool.dart';
import '../entity/stock_check_bill_query_entity.dart';
import '../entity/stock_check_create_entity.dart';
import '../entity/stock_check_info_entity.dart';
import '../entity/stock_check_inout_entity.dart';
import '../entity/stock_check_inout_entity.dart';
import '../entity/stock_check_over_entity.dart';
import '../entity/stock_check_record_entity.dart';
import '../entity/stock_check_result_entity.dart';
import '../entity/stock_check_save_entity.dart';
import '../request/stock_check_infos_request_entity.dart';
import '../request/stock_check_record_request_entity.dart';
import '../request/stock_create_entity.dart';

class StockCheckModel{
  ///盘点记录
  static Future<List<StockCheckRecordEntity>> getListStockCheckRecord(
      BuildContext context,
      StockCheckRecordRequestEntity stockCheckRecordRequestEntity,
      ) async {
    List<StockCheckRecordEntity> list = [];
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_STOCK_CHECKRECORD,
        data: stockCheckRecordRequestEntity.toJson());
    var map = response.data['list'];
    map.forEach((item) => (list.add(StockCheckRecordEntity.fromJson(item))));
    return list;
  }

  ///获取盘点信息
  static Future<StockCreateEntity> getCheckBillInfo(
      BuildContext context,
      String checkId,
      ) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_STOCK_GETCHECKINFO,
        data: checkId);

    return  StockCreateEntity.fromJson(response.data);
  }

  ///删除盘点记录
 static Future<int>deleteCheck(BuildContext context,StockCheckRecordEntity entity) async {
   ResponseModel response = await HttpUtil.request(context,
       method: RequestMethod.POST_GET_STOCK_DELETECHECK,
       data: entity.toJson());
   if(response.code == 200){
     return Future(() => 1);
   }else{
     return Future(() => 0);
   }
 }

 ///全仓盘点获取商品列表
  static Future<List<StockCheckInfoEntity>> getListStockCheckInfos(
      BuildContext context,
      StockCheckInfosRequestEntity infosRequestEntity,
      ) async {
    List<StockCheckInfoEntity> list = [];
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_STOCK_CHECKRE_INFOS,
        data: infosRequestEntity.toJson());
    var map = response.data['list'];
    map.forEach((item) {
      StockCheckInfoEntity stockCheckInfosEntity = StockCheckInfoEntity.fromJson(item);
      stockCheckInfosEntity.textEditingController.text = stockCheckInfosEntity.checked! ?stockCheckInfosEntity.checkQty.toString():'';
      list.add(stockCheckInfosEntity);
    });
    return list;
  }

  ///盘点获取商品列表
  static Future<List<StockCheckInfoEntity>> getListCheckPtypeSelector(
      BuildContext context,
      StockCheckInfosRequestEntity infosRequestEntity,
      ) async {
    List<StockCheckInfoEntity> list = [];
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_STOCK_CHECKRE_PTYPESELECTOR,
        data: infosRequestEntity.toJson());
    var map = response.data['list'];
    map.forEach((item) {
      StockCheckInfoEntity stockCheckInfosEntity = StockCheckInfoEntity.fromJson(item);
      list.add(stockCheckInfosEntity);
    });
    return list;
  }

  ///获取单据编号
  static Future<String> getBillNumber(
      BuildContext context,
      ) async {
    ResponseModel response = await HttpUtil.request(context,
        method: RequestMethod.POST_GET_STOCK_CHECKRE_BILLNUMBER,);
    return response.data;
  }

  ///获取盘点id
  static Future<StockCheckCreateEntity> createCheck(
      BuildContext context,
      StockCreateEntity stockCreateEntity,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: stockCreateEntity.toJson(),
      method: RequestMethod.POST_GET_STOCK_CHECKRE_CREATE,);
    return StockCheckCreateEntity.fromJson(response.data);
  }

  ///插入盘点商品
  static Future<StockCheckResultEntity> insertCheckDetail(
      BuildContext context,
      StockCreateEntity stockCreateEntity,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: stockCreateEntity.toJson(),
      method: RequestMethod.POST_GET_STOCK_CHECKRE_INSERT,);
    return StockCheckResultEntity.fromJson(response.data);
  }

  ///插入盘点商品
  static Future<void> updateCheckDetail(
      BuildContext context,
      StockCheckInfoEntity infoEntity,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: infoEntity.toJson(),
      method: RequestMethod.POST_GET_STOCK_CHECKRE_UPDATECHECK,);
    if(response.code!=200){
      HaloToast.showError(context,msg: response.message);
    }
    return Future(() => null);
  }

  ///删除盘点商品
  static Future<void> deleteDetail(
      BuildContext context,
      String detailId,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: detailId,
      method: RequestMethod.POST_GET_STOCK_CHECKRE_DELETEDETAIL,);
    if(response.code!=200){
      HaloToast.showError(context,msg: response.message);
    }
    return Future(() => null);
  }

  ///删除盘点
  static Future<void> deleteDetailByCheckId(
      BuildContext context,
      String checkId,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: checkId,
      method: RequestMethod.POST_GET_STOCK_CHECKRE_DELETECHECKID,);
    if(response.code!=200){
      HaloToast.showError(context,msg: response.message);
    }
    return Future(() => null);
  }

  ///根据商品获取盘点
  static Future<List<StockCheckInfoEntity>> getPtypes(
      BuildContext context,
      StockCheckBillQueryEntity stockCheckBillQueryEntity,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: stockCheckBillQueryEntity.toJson(),
      method: RequestMethod.POST_GET_STOCK_CHECKRE_GETPTYPES,);

    List<StockCheckInfoEntity> list = [];
    response.data.forEach((item) {
      StockCheckInfoEntity stockCheckInfosEntity = StockCheckInfoEntity.fromJson(item);
      list.add(stockCheckInfosEntity);
    });
    return list;
  }

  //扫码获取盘点信息
  static Future<List<StockCheckInfoEntity>> searchPtypeByScan(
      BuildContext context,
      String scan,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data:{
      "filterValue":scan,
      "ktypeId":SpTool.getStoreInfo()?.ktypeId!
    },
      method: RequestMethod.POST_GET_STOCK_CHECKRE_BYSCAN,);

    List<StockCheckInfoEntity> list = [];
    response.data.forEach((item) {
      StockCheckInfoEntity stockCheckInfosEntity = StockCheckInfoEntity.fromJson(item);
      list.add(stockCheckInfosEntity);
    });
    return list;
  }

  //保存盘点记录
  static Future<void> updateCheckModel(
      BuildContext context,
  {required String checkId,required String etypeId}
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: {
      "checkEtypeId":etypeId,"checkId":checkId
    },
      method: RequestMethod.POST_GET_STOCK_CHECKRE_UPDATACHECKMODEL,);
    if(response.code == 200){
      HaloToast.showSuccess(context,msg: "盘点人修改成功");
      return Future(() => null);
    }else{
      HaloToast.showError(context,msg: response.message);
    }
  }


  //保存盘点记录
  static Future<void> saveCheck(
      BuildContext context,
      StockCheckSaveEntity saveEntity,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: saveEntity.toJson(),
      method: RequestMethod.POST_GET_STOCK_CHECKRE_SAVECHECK,);
    if(response.data!=null){
      HaloToast.showSuccess(context,msg: response.data["message"]);
      return Future(() => null);
    }else{
      HaloToast.showError(context,msg: response.message);
    }
  }

  //完成盘点前验证
  static Future<StockCheckOverEntity> saveOverCheck(
      BuildContext context,
      StockCheckInoutEntity stockCheckInoutEntity,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: stockCheckInoutEntity.toJson(),
      method: RequestMethod.POST_GET_STOCK_CHECKRE_SAVEOVERCHECK,);
    if(response.code == 200){
      return StockCheckOverEntity.fromMap(response.data);
    }
    return StockCheckOverEntity();
  }

  //完成盘点
  static Future<void> saveInout(
      BuildContext context,
      StockCheckSaveEntity saveEntity,
      ) async {
    ResponseModel response = await HttpUtil.request(context,data: saveEntity.toJson(),
      method: RequestMethod.POST_GET_STOCK_CHECKRE_SAVEINOUT,);
    if(response.data!=null){
      HaloToast.showSuccess(context,msg: response.data["message"]);
      return Future(() => null);
    }else{
      HaloToast.showError(context,msg: response.message);
    }
  }
}