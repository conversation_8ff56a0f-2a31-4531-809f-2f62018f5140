import 'package:flutter/cupertino.dart';

/// 泛型数组监听
class ValueNotifierList<T> extends ValueNotifier<List<T>> {

  ValueNotifierList(List<T> initValue) : super(initValue);

  void add(T item) {
    value.add(item);
    _copyValue();
  }

  void addAll(List<T> item) {
    value.addAll(item);
    _copyValue();
  }
  void insertAll(index,List<T> item){
    value.insertAll(index, item);
    _copyValue();
  }

  /// 删除
  void remove(int index) {
    if (value.length < index) {
      return;
    }
    value.removeAt(index);
    _copyValue();
  }

  /// 删除最后
  void removeLast() {
    if (value.isEmpty) {
      return;
    }
    value.removeLast();
    _copyValue();
  }

  void removeAll() {
    value.clear();
    _copyValue();
  }

  void removeAllNoNotifier() {
    value.clear();
  }

  /// 利用深copy 重新赋值,触发监听
  void _copyValue() {
    value = [...value];
  }

}