class StockCheckHelper{

  // static void setPropValueString(GoodsDetailDto model) {
  //   if (model == null || StringUtil.isNotZeroOrEmpty(model.propValues)) {
  //     return;
  //   }
  //   String propValues = "";
  //   if (model.prop != null && model.prop!.length > 0) {
  //     model.prop!.forEach((element) {
  //       if (!StringUtil.isEmpty(element.propvalueName)) {
  //         propValues += element.propvalueName! + ":";
  //       }
  //     });
  //   }
  //   model.propValues = StringUtil.trim(propValues);
  // }

}