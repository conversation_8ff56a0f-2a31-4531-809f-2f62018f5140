import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../widgets/base/base_list.dart';

typedef RefreshCallback = Future<void> Function();
typedef LoadMoreCallback = Future<void> Function();
typedef BuildTableColumnWidth = HashMap<int, TableColumnWidth> Function();
typedef OnRequestData = Future<List<TableRow>?> Function();

const Color _defaultTextColor = Colors.black26;
const Color colorTab = Colors.black26;

abstract class TableList extends StatefulWidget {
  final RefreshCallback? onRefresh; //刷新回调函数
  final LoadMoreCallback? onLoadMore; //加载更多
  final ScrollController scrollController; //加载更多需要指定
  bool isNeedRefresh;
  bool isNeedLoadMore;

  TableList(
      {Key? key,
      required this.scrollController,
      this.onRefresh,
      this.onLoadMore,
      this.isNeedRefresh = true,
      this.isNeedLoadMore = true})
      : super(key: key);
}

abstract class TableListState<T extends TableList> extends State<T> {
  int pageIndex = 1;
  final int pageSize = 20;
  bool isLoadMore = false;
  bool isHasMoreData = true;
  List<TableRow> dataSource = []; //内容

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    widget.scrollController.addListener(() {
      if (isLoadMore || !isHasMoreData) {
        return;
      }
      if (widget.scrollController.position.pixels ==
          widget.scrollController.position.maxScrollExtent) {
        _doLoadMore();
      }
    });
    onLoadData();
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      color: Colors.white,
      direction: Axis.vertical,
      children: [
        SizedBox(
          child: Table(
            defaultColumnWidth: const FlexColumnWidth(0.4),
            columnWidths: getColumnWidths(),
            children: [TableRow(children: getTopList())],
          ),
        ),
        Expanded(
          child: CustomScrollView(
            controller: widget.scrollController,
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics()),
            slivers: <Widget>[
              //下拉刷新组件
              widget.isNeedRefresh
                  ? CupertinoSliverRefreshControl(
                      //下拉刷新回调
                      onRefresh: () async {
                        pageIndex = 1;
                        isHasMoreData = true;
                        onLoadData();
                      },
                    )
                  : SliverToBoxAdapter(child: Container()),
              //列表
              SliverToBoxAdapter(
                  child: Table(
                border: const TableBorder(
                  bottom: BorderSide(width: 1, color: AppColors.dividerColor),
                ),
                columnWidths: getColumnWidths(),
                defaultColumnWidth: const FlexColumnWidth(0.4),
                children: dataSource,
              )),
              SliverToBoxAdapter(
                  child: Visibility(
                      visible: widget.isNeedLoadMore,
                      child: _buildLoadMoreWidget()))
            ],
          ),
        )
      ],
    );
  }

  Widget _buildLoadMoreWidget() {
    Widget tipsWidget;
    if (!isHasMoreData) {
      tipsWidget = const Center(
        child: Text("没有更多数据",
            style: TextStyle(
                color: _defaultTextColor, decoration: TextDecoration.none)),
      );
    } else if (isLoadMore) {
      tipsWidget = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          CupertinoActivityIndicator(
            radius: 10,
          ),
          Padding(
              padding: EdgeInsets.only(left: 6),
              child: Text(
                "正在加载数据",
                style: TextStyle(
                    color: _defaultTextColor, decoration: TextDecoration.none),
              ))
        ],
      );
    } else {
      tipsWidget = const Center(
        child: Text("上拉加载更多",
            style: TextStyle(
                color: _defaultTextColor, decoration: TextDecoration.none)),
      );
    }

    return SizedBox(width: double.infinity, height: 40, child: tipsWidget);
  }

  ///网络请求
  Future<LoadState> onLoadData() async {
    return onRequestData().then((result) {
      if (null == result) {
        return LoadState.LoadFail;
      }
      if (pageIndex <= 1) {
        dataSource.clear();
      }
      setState(() {
        dataSource.addAll(result);
      });
      if (result.length < pageSize) {
        setState(() {
          isHasMoreData = false;
        });
        return LoadState.AllFinish;
      }
      setState(() {
        isHasMoreData = true;
      });
      return LoadState.LoadFinish;
    }).catchError((msg, stack) {
      return LoadState.LoadFinish;
    });
  }

  ///加载更多
  Future<LoadState> onLoadMore() async {
    pageIndex++;
    List<TableRow>? result = await onRequestData();
    if (null == result) {
      return LoadState.LoadFail;
    }
    if (pageIndex <= 1) {
      dataSource.clear();
    }
    setState(() {
      dataSource.addAll(result);
    });

    if (result.length < pageSize) {
      setState(() {
        isHasMoreData = false;
      });
      return LoadState.AllFinish;
    }
    return LoadState.LoadFinish;
  }

  _doLoadMore() {
    isLoadMore = true;
    setState(() {});
    onLoadMore().then((value) {
      isLoadMore = false;
      setState(() {});
    }).catchError((error) {
      isLoadMore = false;
      setState(() {});
    });
  }

  //重写方法
  List<Widget> getTopList();

  HashMap<int, TableColumnWidth> getColumnWidths();

  Future<List<TableRow>?> onRequestData();
}
