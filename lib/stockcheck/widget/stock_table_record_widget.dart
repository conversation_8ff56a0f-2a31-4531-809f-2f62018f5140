import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:halo_pos/common/tool/date_util.dart';
import '../../common/tool/sp_tool.dart';
import '../../iconfont/icon_font.dart';
import '../model/stock_check_model.dart';
import 'table_list_widget.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../entity/system/permission_dto.dart';
import '../../widgets/halo_pos_label.dart';
import '../entity/stock_check_record_entity.dart';
import '../request/stock_check_record_request_entity.dart';
import '../stock_check_record.dart';

const List<String> tableTopTitles = [
  "序号",
  "操作",
  "盘点编号",
  "盘点人",
  "盘点时间",
  "完成状态",
];

typedef CheckCallback = void Function(StockCheckRecordEntity checkRecordEntity);

class StockTableWidget extends TableList {
  final FilterStockController filterController;
  final CheckCallback checkCallback;
  final ScrollController scrollC;

  StockTableWidget(
      {Key? key,
      required this.filterController,
      required this.scrollC,
      required this.checkCallback})
      : super(key: key, scrollController: scrollC);

  @override
  StockTableWidgetState createState() => StockTableWidgetState();
}

class StockTableWidgetState extends TableListState<StockTableWidget> {
  StockCheckRecordRequestEntity stockCheckRecordRequestEntity =
      StockCheckRecordRequestEntity();

  ///权限
  final PermissionDto _permissionDto = SpTool.getPermission();

  @override
  void initState() {
    // TODO: implement initState
    getFilterParams();
    //开始监听变动
    widget.filterController.addListener(onFilterParamsChange);
    super.initState();
  }

  @override
  List<TableCell> getTopList() {
    return tableTopTitles.map((e) {
      return TableCell(
          child: SizedBox(
              height: 50,
              child: HaloContainer(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text(e, textAlign: TextAlign.center)],
              )));
    }).toList();
  }

  @override
  Future<List<TableRow>?> onRequestData() {
    getFilterParams();
    return StockCheckModel.getListStockCheckRecord(
            context, stockCheckRecordRequestEntity)
        .then((value) {
      int i = pageIndex == 1 ? 0 : dataSource.length;
      return value.map((StockCheckRecordEntity e) {
        i++;
        return TableRow(children: [
          TableCell(
              child: HaloContainer(
            height: 50,
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [Text(i.toString(), textAlign: TextAlign.center)],
          )),
          TableCell(
              child: GestureDetector(
            onTap: () {
              _deleteCheck(e);
            },
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [IconFont(IconNames.qingkong)],
            ),
          )),
          TableCell(
              child: GestureDetector(
            onTap: () {
              onClickTable(e);
            },
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [Text(e.number!, textAlign: TextAlign.center)],
            ),
          )),
          TableCell(
              child: GestureDetector(
            onTap: () {
              onClickTable(e);
            },
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    child: HaloPosLabel(e.etypeName ?? "无",
                        textAlign: TextAlign.center))
              ],
            ),
          )),
          TableCell(
              child: GestureDetector(
            onTap: () {
              onClickTable(e);
            },
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(formatDateStringToLocal(e.checkTime),
                    textAlign: TextAlign.center)
              ],
            ),
          )),
          TableCell(
              child: GestureDetector(
            onTap: () {
              onClickTable(e);
            },
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [Text(e.checkStateName!, textAlign: TextAlign.center)],
            ),
          )),
        ]);
      }).toList();
    });
  }

  @override
  HashMap<int, TableColumnWidth> getColumnWidths() {
    HashMap<int, TableColumnWidth> columnWidths = HashMap();
    columnWidths[tableTopTitles.length - 1] =
        FlexColumnWidth(0.05 * tableTopTitles.length);
    for (int i = 0; i < tableTopTitles.length; i++) {
      var element = tableTopTitles[i];
      if (element == "盘点编号") {
        columnWidths[i] = const FlexColumnWidth(1.2);
      }
      if (element == "盘点时间") {
        columnWidths[i] = const FlexColumnWidth(1.2);
      }

      if (element == "盘点人") {
        columnWidths[i] = const FlexColumnWidth(1);
      }
    }
    return columnWidths;
  }

  ///获取筛选条件
  void getFilterParams() {
    stockCheckRecordRequestEntity.pageIndex =
        _permissionDto.analysiscloudStockCheckRecordview == false
            ? 0
            : pageIndex;
    stockCheckRecordRequestEntity.pageSize = pageSize;
    stockCheckRecordRequestEntity.refresh = true;
    stockCheckRecordRequestEntity.queryParams.startDate =
        formatDateStringToUtc(widget.filterController.startDate.value);
    stockCheckRecordRequestEntity.queryParams.endDate =
        formatDateStringToUtc(widget.filterController.endDate.value);
    stockCheckRecordRequestEntity.queryParams.isPC = 'true';
    stockCheckRecordRequestEntity.queryParams.filterValue =
        widget.filterController.filterValue.value;
    stockCheckRecordRequestEntity.queryParams.filterFinish = int.parse(
        widget.filterController.filterFinish.value == ""
            ? "0"
            : widget.filterController.filterFinish.value);
    stockCheckRecordRequestEntity.queryParams.etypeId =
        widget.filterController.etypeId.value;
    stockCheckRecordRequestEntity.queryParams.ktypeId =
        SpTool.getStoreInfo()?.ktypeId ?? "0";

    if (_permissionDto.analysiscloudStockCheckRecordview == false) {
      HaloToast.show(context, msg: "没有盘点记录查看权限");
    }
  }

  ///筛选条件更改的回调方法
  void onFilterParamsChange() {
    getFilterParams();
    pageIndex = 1;
    dataSource.clear();
    onLoadData();
  }

  //删除
  _deleteCheck(StockCheckRecordEntity entity) {
    if (_permissionDto.analysiscloudStockCheckRecorddelete ?? false) {
      StockCheckModel.deleteCheck(context, entity).then((value) {
        if (value == 1) {
          HaloToast.showSuccess(context, msg: "删除记录成功");
          pageIndex = 1;
          onLoadData();
        } else {
          HaloToast.showSuccess(context, msg: "删除记录失败");
        }
      });
    } else {
      HaloToast.showError(context, msg: "没有删除盘点记录的权限，请到PC端设置");
    }
  }

  onClickTable(StockCheckRecordEntity entity) {
    widget.checkCallback(entity);
  }

  @override
  void didUpdateWidget(StockTableWidget oldWidget) {
    if (oldWidget.filterController != widget.filterController) {
      oldWidget.filterController.removeListener(onFilterParamsChange);
      widget.filterController.addListener(onFilterParamsChange);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.filterController.removeListener(onFilterParamsChange);
    super.dispose();
  }
}
