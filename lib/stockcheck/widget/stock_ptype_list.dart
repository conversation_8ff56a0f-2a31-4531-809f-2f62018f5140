import 'package:decimal/decimal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../bill/entity/bill_ptype_request.dart';
import '../../bill/entity/goods_batch_dto.dart';
import '../../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../../bill/model/bill_model.dart';
import '../../bill/model/ptype_model.dart';
import '../../common/change_notifier.dart';
import '../../common/style/app_color_helper.dart';
import '../../common/tool/sp_tool.dart';
import '../../login/entity/store/store_info.dart';
import '../../report/entity/stock_statistics_dto.dart';
import '../../report/inventory_helper.dart';
import '../../widgets/base/base_list.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/halo_pos_label.dart';
import '../entity/stock_check_bill_query_entity.dart';
import '../entity/stock_check_info_entity.dart';
import '../model/stock_check_model.dart';
import '../request/stock_check_infos_request_entity.dart';

class StockPtypeList extends BaseListPage {
  final Function(int, dynamic item) onChanged;
  final InterceptValueNotifier filterValue;

  const StockPtypeList(
      {Key? key, required this.onChanged, required this.filterValue})
      : super(key: key);

  @override
  BaseStatefulPageState<BaseStatefulPage> createState() =>
      StockPtypeListState();
}

class StockPtypeListState extends BaseListPageState<StockPtypeList> {
  StockCheckBillQueryEntity stockCheckBillQueryEntity =
      StockCheckBillQueryEntity();
  StoreInfo storeInfo = SpTool.getStoreInfo()!;

  @override
  void initState() {
    // TODO: implement initState
    widget.filterValue.addListener(() {
      pageIndex = 1;
      onLoadData();
    });
    super.initState();
  }

  @override
  Widget buildItemView(int index, item) {
    // TODO: implement buildItemView
    return HaloContainer(
      padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 16.h),
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
            flex: 3,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                HaloPosLabel(
                  item.fullname ?? "",
                  maxLines: 1,
                  textStyle: TextStyle(
                      color: AppColorHelper(context).getTitleBoldTextColor(),
                      fontSize: 22.sp),
                ),
                Visibility(
                  visible: false,
                  child: Text(
                    item.barcode ?? "",
                    style: TextStyle(
                        color: AppColorHelper(context).getTitleBoldTextColor(),
                        fontSize: 22.sp,
                        fontWeight: FontWeight.w600),
                  ),
                )
              ],
            )),
        SizedBox(
          width: 250.w,
          child: Text(
            item.barcode ?? '',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 350.w,
          child: Text(
            item.propValues ?? '',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 100.w,
          child: Text(
            item.unitName ?? '',
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 22.sp),
          ),
        ),
        SizedBox(
          width: 150.w,
          child: Text(
            item.stockQty != 0
                ? Decimal.parse(item.stockQty.toString()).toString()
                : "0",
            textAlign: TextAlign.center,
            style: TextStyle(
                color: ColorUtil.stringColor("#4679FC"), fontSize: 22.sp),
          ),
        ),
      ],
    );
  }

  @override
  String getActionBarTitle() {
    // TODO: implement getActionBarTitle
    throw UnimplementedError();
  }

  @override
  onItemClick(int index, item) {
    getParams(item);
    StockCheckModel.getPtypes(context, stockCheckBillQueryEntity).then((value) {
      widget.onChanged(index, value);
      Navigator.pop(context);
    });
  }

  getParams(StockCheckInfoEntity entity) {
    stockCheckBillQueryEntity.ptypeIds = [entity.id!];
    stockCheckBillQueryEntity.skuIds = [entity.skuId ?? "0"];
    stockCheckBillQueryEntity.skuIds = [entity.skuId ?? "0"];
    stockCheckBillQueryEntity.ktypeId = storeInfo.ktypeId;
    stockCheckBillQueryEntity.checkBatchStock = true;
    StockCheckBillQueryCheckPtypes checkPtypes =
        StockCheckBillQueryCheckPtypes();
    checkPtypes.id = entity.id;
    checkPtypes.skuId = entity.skuId ?? "0";
    checkPtypes.propenabled = entity.propenabled;
    checkPtypes.propvalueName1 = entity.propvalueName1;
    checkPtypes.propvalueName2 = entity.propvalueName2;
    checkPtypes.propvalueName3 = entity.propvalueName3;
    checkPtypes.propvalueName4 = entity.propvalueName4;
    checkPtypes.propvalueName5 = entity.propvalueName5;
    checkPtypes.propvalueName6 = entity.propvalueName6;
    stockCheckBillQueryEntity.checkPtypes = [checkPtypes];
  }

  @override
  Future<List> onRequestData() async {
    // TODO: implement onRequestData

    // StockCheckInfosRequestEntity infosRequestEntity = StockCheckInfosRequestEntity();
    // infosRequestEntity.pageIndex = pageIndex;
    // infosRequestEntity.pageSize = pageSize;
    // infosRequestEntity.queryParams.checkAllStock = false;
    // infosRequestEntity.queryParams.ktypeId =
    //     SpTool.getStoreInfo()?.ktypeId ?? "0";
    // infosRequestEntity.queryParams.filterValue = widget.filterValue.value.toString();
    //
    //  List<StockCheckInfoEntity> dataList = await  StockCheckModel.getListCheckPtypeSelector(context,infosRequestEntity);
    // return dataList;

    BillPtypeRequest paramRequest = BillPtypeRequest();
    paramRequest.btypeId = storeInfo.btypeId;
    paramRequest.otypeId = storeInfo.otypeId;
    paramRequest.ktypeId = storeInfo.ktypeId;
    paramRequest.fullBarCode = false;
    paramRequest.searchBySn = true;
    paramRequest.stockSelect = true;
    paramRequest.filterValue = widget.filterValue.value.toString();

    List<PtypeListModel> list = await PtypeModel.selectPtypeAndCombo(context,
        queryParam: paramRequest, pageIndex: pageIndex, pageSize: pageSize);

    return list.map((PtypeListModel e) {
      StockCheckInfoEntity entity = StockCheckInfoEntity();
      entity.ptypeId = e.id;
      entity.id = e.id;
      entity.fullname = e.fullname;
      entity.barcode = e.fullbarcode;
      entity.propValues = e.sku?.propvalueNames ?? "";
      entity.unitName = e.unit?.unitName ?? "";
      entity.stockQty = double.parse(e.qty ?? "0");
      entity.batchenabled = e.batchenabled;
      entity.skuId = e.sku?.id ?? "";
      entity.costMode = e.costMode;
      entity.snenabled = e.snenabled ?? 0;
      entity.protectDays = e.protectDays;
      entity.protectDaysUnit = e.protectDaysUnit;
      entity.propenabled = e.propenabled;
      entity.propvalueName1 = e.sku?.propvalueName1 ?? "";
      entity.propvalueName2 = e.sku?.propvalueName2 ?? "";
      entity.propvalueName3 = e.sku?.propvalueName3 ?? "";
      entity.propvalueName4 = e.sku?.propvalueName4 ?? "";
      entity.propvalueName5 = e.sku?.propvalueName5 ?? "";
      entity.propvalueName6 = e.sku?.propvalueName6 ?? "";
      return entity;
    }).toList();
  }
}
