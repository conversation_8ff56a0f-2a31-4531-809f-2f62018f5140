import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/datepicker/pduration.dart';
import 'package:haloui/widget/halo_container.dart';
import 'package:haloui/widget/halo_textfield.dart';
import 'package:haloui/widget/halo_toast.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';
import 'package:halo_utils/utils/date_util.dart' as DateUtil;
import 'package:haloui/haloui.dart';
import '../../common/keyboard_hidden.dart';
import '../../common/style/app_color_helper.dart';
import '../../common/widget/datetime_filter.dart';
import '../../iconfont/icon_font.dart';
import '../../../common/keyboard_hidden.dart' as Keyboard;
import '../../widgets/halo_pos_label.dart';

typedef StringCallback = void Function(String);

enum FilterBoxType {
  text,
  date,
  drop,
  scan,
  multi,
}

class FilterBox<T> extends StatefulWidget {
  final FilterBoxType filterBoxType;
  final String filterTitle;
  final String? hit;
  final T? value;
  final String placeholderTitle;
  final TextEditingController? textStartTimeController;
  final TextEditingController? textEndTimeController;
  final TextEditingController? textEditingController;
  final DateTime? beforeTime;
  final DateTime? afterTime;
  final double width;
  final List<FilterBoxItem>? dropItems;
  final List<FilterBoxItem>? selectDropItems;
  final bool enabled;
  final StringCallback? callback;

  const FilterBox(
      {Key? key,
      this.callback,
      this.filterBoxType = FilterBoxType.text,
      this.hit,
      this.value,
      required this.filterTitle,
      this.placeholderTitle = "",
      this.textStartTimeController,
      this.textEndTimeController,
      this.textEditingController,
      this.afterTime,
      this.beforeTime,
      this.width = 450,
      this.enabled = true,
      this.dropItems,this.selectDropItems})
      : super(key: key);

  @override
  State<FilterBox> createState() => _FilterBoxState();
}

class _FilterBoxState extends State<FilterBox> with DateTimeFilterMixin {
  double get height => 65.h;
  Color borderColor = const Color(0xFFCACCCE);

  EdgeInsets get margin => EdgeInsets.only(left: 12.w, top: 14.h, bottom: 14.h);

  EdgeInsets get padding => EdgeInsets.only(left: 15.w,right: 15.w);
  final KeyboardHiddenFocusNode searchFocusNode = KeyboardHiddenFocusNode();


  @override
  void initState() {
    super.initState();
    if(widget.textStartTimeController!=null){
      textStartTimeController = widget.textStartTimeController!;
    }
    if(widget.textEndTimeController!=null){
      textEndTimeController = widget.textEndTimeController!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return HaloContainer(children: [
      Visibility(
          visible: widget.filterBoxType == FilterBoxType.date,
          child: _buildTimeQuery()),
      Visibility(
          visible: widget.filterBoxType == FilterBoxType.text,
          child: _buildSaleBillNumberQuery()),
      Visibility(
          visible: widget.filterBoxType == FilterBoxType.drop,
          child: _buildDropDown()),
      Visibility(
          visible: widget.filterBoxType == FilterBoxType.scan,
          child: _buildScanQuery()),
      Visibility(
          visible: widget.filterBoxType == FilterBoxType.multi,
          child: _buildMultiSelectDropdown()),
    ]);
  }

  //时间
  Widget _buildTimeQuery() {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      color: Colors.white,
      margin: margin,
      padding: padding,
      width: widget.width,
      height: height,
      border: Border.all(color: borderColor, width: 1),
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 3),
          child: Text(
            "${widget.filterTitle}: ",
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 24.sp,
                color: AppColorHelper(context).getTitleBoldTextColor()),
          ),
        ),
        buildDateTimeQuery(context)
      ],
    );
  }

  Widget _buildSaleBillNumberQuery() {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      color: widget.enabled ? Colors.white : Colors.black12,
      margin: margin,
      padding: padding,
      width: widget.width,
      height: height,
      border: Border.all(color: borderColor, width: 1),
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 3, right: 16),
          child: HaloPosLabel(
            "${widget.filterTitle}: ",
            textAlign: TextAlign.center,
            textStyle: TextStyle(
                fontSize: 24.sp,
                color: AppColorHelper(context).getTitleBoldTextColor()),
          ),
        ),
        Expanded(
          child: HaloContainer(height: height, children: [
            Expanded(
              child: HaloTextField(
                enabled: widget.enabled,
                controller: widget.textEditingController,
                fontSize: 24.sp,
                contentPadding: 0,
                maxLines: 1,
                backgroundColor: Colors.transparent,
              ),
            ),
          ]),
        )
      ],
    );
  }

  Widget _buildDropDown() {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      color: widget.enabled ? Colors.white : Colors.black12,
      margin: margin,
      padding: padding,
      width: widget.width,
      height: height,
      border: Border.all(color: borderColor, width: 1),
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 3),
          child: HaloPosLabel(
            "${widget.filterTitle}: ",
            textAlign: TextAlign.center,
            textStyle: TextStyle(
                fontSize: 24.sp,
                color: AppColorHelper(context).getTitleBoldTextColor()),
          ),
        ),
        Expanded(
            child: DropdownButtonFormField(
          hint: Text(
            widget.hit ?? "",
            style: TextStyle(fontSize: 15),
          ),
          isExpanded: true,
          decoration: const InputDecoration(
              contentPadding: EdgeInsets.only(left: 5, right: 5, bottom: 15),
              border: InputBorder.none),
          items: getDropDownItem(widget.dropItems ?? []),
          value: widget.value,
          onChanged: widget.enabled
              ? (dynamic value) {
                  setState(() {
                    widget.textEditingController!.text = value;
                  });
                }
              : null,
        ))
      ],
    );
  }

  Widget _buildMultiSelectDropdown() {
    return PopupMenuButton(
        itemBuilder: (BuildContext context) {
          return widget.dropItems!.map((FilterBoxItem item) {
            return PopupMenuItem(
              value: item.text,
              child:StatefulBuilder(
                builder: (BuildContext context, StateSetter setState) {
                  return  CheckboxListTile(
                    title: Text(item.text),
                    value: widget.selectDropItems?.any((element) => element.value==item.value)??false,
                    onChanged: (value) {
                      if(value!){
                        widget.selectDropItems!.add(item);
                      }else{
                        /// fix 直接用remove 数据的内存空间会变导致移除不掉，只能重写remove方法或者用value找到index
                        int index =  widget.selectDropItems!.indexWhere((element) => element.value == item.value);
                        widget.selectDropItems!.removeAt(index);
                      }
                      setState(() {
                        List<String>list = widget.selectDropItems!.map((FilterBoxItem e){
                          return e.text;
                        }).toList();
                        widget.textEditingController!.text =  list.join(",");
                      });
                    },
                  );
            }
            ));
          }).toList();
        },
        child: HaloContainer(
          mainAxisSize: MainAxisSize.max,
          color: widget.enabled ? Colors.white : Colors.black12,
          margin: margin,
          padding: padding,
          width: widget.width,
          height: height,
          border: Border.all(color: borderColor, width: 1),
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 3, right: 16),
              child: HaloPosLabel(
                "${widget.filterTitle}: ",
                textAlign: TextAlign.center,
                textStyle: TextStyle(
                    fontSize: 24.sp,
                    color: AppColorHelper(context).getTitleBoldTextColor()),
              ),
            ),
            Expanded(
              child: HaloContainer(height: height, children: [
                Expanded(
                  child: HaloTextField(
                    enabled:false,
                    controller: widget.textEditingController,
                    fontSize: 24.sp,
                    contentPadding: 0,
                    maxLines: 1,
                    backgroundColor: Colors.transparent,
                  ),
                ),
              ]),
            )
          ],
        ));
  }

  Widget _buildScanQuery() {
    return HaloContainer(
      mainAxisSize: MainAxisSize.max,
      color: widget.enabled ? Colors.white : Colors.black12,
      margin: margin,
      padding: padding,
      width: widget.width,
      height: height,
      border: Border.all(color: borderColor, width: 1),
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 3, right: 16),
          child: HaloPosLabel(
            "${widget.filterTitle}: ",
            textAlign: TextAlign.center,
            textStyle: TextStyle(
                fontSize: 24.sp,
                color: AppColorHelper(context).getTitleBoldTextColor()),
          ),
        ),
        Expanded(
          child: Keyboard.KeyboardHiddenTextField(
            hint: "支持扫码序列号/条码",
            focusNode: searchFocusNode,
            isShowClean: false,
            enabled: widget.enabled,
            controller: widget.textEditingController,
            cleanTextWhenSearch: true,
            style: TextStyle(
                textBaseline: TextBaseline.alphabetic,
                color: AppColorHelper(context).getTitleBoldTextColor(),
                fontSize: 24.sp),
            onSubmitted: (text) {
              onSubmittedButton();
              searchFocusNode.requestFocus();
            },
          ),
        ),
      ],
    );
  }

  bool _checkStartTime(DateTime dateTime) {
    if (dateTime.millisecondsSinceEpoch -
            DateUtil.DateUtil.getDateTime(widget.textEndTimeController!.text)!
                .millisecondsSinceEpoch >
        0) {
      HaloToast.showError(context, msg: "开始时间不可以大于结束时间，请重新选择!");
      return false;
    }
    return true;
  }

  bool _checkEndTime(DateTime dateTime) {
    if (dateTime.millisecondsSinceEpoch -
            DateUtil.DateUtil.getDateTime(widget.textStartTimeController!.text)!
                .millisecondsSinceEpoch <
        0) {
      HaloToast.showError(context, msg: "结束时间不可以小于开始时间，请重新选择!");
      return false;
    }
    return true;
  }

  List<DropdownMenuItem> getDropDownItem(List<FilterBoxItem> data) {
    return data.map((e) {
      return DropdownMenuItem(
        value: e.value,
        child: Text(e.text, style: TextStyle(fontSize: 15))
      );
    }).toList();
  }

  onSubmittedButton() {
    if (widget.callback != null) {
      widget.callback!(widget.textEditingController!.text);
    }
    searchFocusNode.unfocus();
  }
}

class FilterBoxItem<T> {
  late String text;
  late T value;
  bool isSelect = false;
  FilterBoxItem({required this.text, required this.value});

  FilterBoxItem.fromJson(Map<String,dynamic> map){
    text = map["text"];
    value = map["value"];
    isSelect = map["isSelect"];
  }

  Map<String,dynamic>  toJson()=>{
    "text" : text,
    "value" : value,
    "isSelect" : isSelect,
  };
}
