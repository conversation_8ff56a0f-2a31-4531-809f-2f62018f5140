import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../iconfont/icon_font.dart';
import 'table_list_widget.dart';

const List<String> tableTopTitles = ["行号", "操作", "序列号"];

class StockTableSnWidget extends StatefulWidget {
  List<TextEditingController> dataSource; //内容
  final ValueChanged<bool>? onFocusChanged;

   StockTableSnWidget({Key? key,required this.dataSource,this.onFocusChanged}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return StockTableSnWidgetState();
  }
}

class StockTableSnWidgetState<T extends StockTableSnWidget> extends State<T> {

  @override
  void initState() {
    super.initState();
    widget.dataSource.add(TextEditingController());
    // TODO: implement initState
  }

  TableRow _buildItemTabRow(
      int index, TextEditingController textEditingController) {
    return TableRow(children: [
      TableCell(
          child: HaloContainer(
        height: 50,
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children:  [Text((index).toString(), textAlign: TextAlign.center)],
      )),
      TableCell(
          child: HaloContainer(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              addNewData();
            },
            child: Visibility(
              visible:  widget.dataSource.length  == index,
              child: HaloContainer(
                height: 50,
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [IconFont(IconNames.add)],
              ),
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          GestureDetector(
            onTap: () {
              removeData(textEditingController);
            },
            child: Visibility(
              visible: widget.dataSource.length>1,
              child: HaloContainer(
                height: 50,
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [IconFont(IconNames.qingkong)],
              ),
            ),
          ),
        ],
      )),
      TableCell(
          child: HaloContainer(
        height: 50,
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: HaloTextField(
              controller: textEditingController,
              textAlign: TextAlign.center,
              fontSize: 24.sp,
              contentPadding: 0,
              backgroundColor: Colors.transparent,
              maxLines: 1,
              onFocusChanged: (v){
                if(widget.onFocusChanged != null){
                  widget.onFocusChanged!(v);
                }
              },
            ),
          )
        ],
      )),
    ]);
  }

  List<TableRow> getDataSource() {
    int i = 0;
    return  widget.dataSource.map((e) {
      i++;
      return _buildItemTabRow(i, e);
    }).toList();
  }

  addNewData(){
    setState(() {
      widget.dataSource.add(TextEditingController());
    });
  }

  removeData(TextEditingController textEditingController){
    setState(() {
      widget.dataSource.remove(textEditingController);

    });
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return HaloContainer(
      color: Colors.white,
      margin: EdgeInsets.only(top: 15.w),
      padding: EdgeInsets.only(
          left: ScreenUtil().setWidth(24),
          right: ScreenUtil().setWidth(24),
          bottom: ScreenUtil().setWidth(24)),
      direction: Axis.vertical,
      children: [
        SizedBox(
          child: Table(
            border: TableBorder.all(color: colorTab),
            defaultColumnWidth: const FlexColumnWidth(0.3),
            columnWidths: getColumnWidths(),
            children: [TableRow(children: getTopList())],
          ),
        ),
        Expanded(
          child: CustomScrollView(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(
                parent: AlwaysScrollableScrollPhysics()),
            slivers: <Widget>[
              //列表
              SliverToBoxAdapter(
                  child: Table(
                border: const TableBorder(
                  bottom: BorderSide(
                      width: 1,
                      color: Colors.black26,
                      style: BorderStyle.solid),
                  left: BorderSide(
                      width: 1,
                      color: Colors.black26,
                      style: BorderStyle.solid),
                  right: BorderSide(
                      width: 1,
                      color: Colors.black26,
                      style: BorderStyle.solid),
                  verticalInside: BorderSide(
                      width: 1,
                      color: Colors.black26,
                      style: BorderStyle.solid),
                  horizontalInside: BorderSide(
                      width: 1,
                      color: Colors.black26,
                      style: BorderStyle.solid),
                ),
                columnWidths: getColumnWidths(),
                defaultColumnWidth: const FlexColumnWidth(0.3),
                children: getDataSource(),
              )),
            ],
          ),
        )
      ],
    );
  }

  HashMap<int, TableColumnWidth> getColumnWidths() {
    HashMap<int, TableColumnWidth> columnWidths = HashMap();
    columnWidths[tableTopTitles.length - 1] =
        FlexColumnWidth(0.05 * tableTopTitles.length);
    for (int i = 0; i < tableTopTitles.length; i++) {
      var element = tableTopTitles[i];
      if (element == "序列号") {
        columnWidths[i] = const FlexColumnWidth(1);
      }
    }
    return columnWidths;
  }

  List<TableCell> getTopList() {
    return tableTopTitles.map((e) {
      return TableCell(
          child: SizedBox(
              height: 50,
              child: HaloContainer(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text(e, textAlign: TextAlign.center)],
              )));
    }).toList();
  }
}
