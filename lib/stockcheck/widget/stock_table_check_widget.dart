import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'stock_addsn_dialog.dart';
import 'table_list_widget.dart';
import '../../widgets/halo_pos_label.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/date_util.dart' as DateUtil;
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_container.dart';

import '../../common/tool/sp_tool.dart';
import '../../iconfont/icon_font.dart';
import '../entity/stock_check_info_entity.dart';
import '../model/stock_check_model.dart';
import '../request/stock_check_infos_request_entity.dart';
import 'stock_add_batch_dialog.dart';

const List<String> tableTopTitles = [
  "序号",
  "操作",
  "商品名称",
  "条码",
  "属性组合",
  "单位",
  "仓内库存",
  "盘点数量",
  "差异数量",
  "序列号",
  "批次号",
  "生产日期",
  "到期日期",
];

typedef CheckCallback =
    void Function(List<StockCheckInfoEntity>, bool insert, bool batch);
typedef CheckDeleteCallback =
    void Function(String detailId, bool isDeleteCheckId);

enum CheckTableType { all, select }

class StockCheckTableWidget extends TableList {
  CheckCallback? checkCallback;
  CheckDeleteCallback? deleteCallback;
  String checkId;
  bool isEdit;
  final CheckTableType checkTableType;

  // ValueNotifierList<StockCheckInfoEntity> dataSourceSelect;
  final ScrollController scrollC;

  StockCheckTableWidget({
    Key? key,
    required this.checkId,
    required this.isEdit,
    required this.checkTableType,
    // required this.dataSourceSelect,
    this.checkCallback,
    this.deleteCallback,
    required this.scrollC,
  }) : super(key: key, scrollController: scrollC);

  @override
  StockCheckTableWidgetState createState() => StockCheckTableWidgetState();
}

class StockCheckTableWidgetState extends TableListState<StockCheckTableWidget> {
  final List<StockCheckInfoEntity> stockCheckInfosEntity = [];

  StockCheckInfosRequestEntity infosRequestEntity =
      StockCheckInfosRequestEntity();

  bool checkScanPtype = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StockCheckTableWidget oldWidget) {
    if (oldWidget.checkTableType != widget.checkTableType) {
      onLoadData();
    }
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
  }

  @override
  List<TableCell> getTopList() {
    // TODO: implement getTopList
    return tableTopTitles.map((e) {
      return TableCell(
        child: SizedBox(
          height: 50,
          child: HaloContainer(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [Text(e, textAlign: TextAlign.center)],
          ),
        ),
      );
    }).toList();
  }

  reloadData(checkId, {isScanPtype}) {
    widget.checkId = checkId;
    if (isScanPtype != null) {
      checkScanPtype = isScanPtype;
    }
    //刷新都要置为1;
    pageIndex = 1;
    onLoadData();
  }

  refreshItem() {
    //刷新
    setState(() {
      dataSource = getRowList(stockCheckInfosEntity);
    });
  }

  @override
  Future<List<TableRow>?> onRequestData() {
    getParams();
    // return Future.value(null);
    return StockCheckModel.getListStockCheckInfos(
      context,
      infosRequestEntity,
    ).then((value) {
      if (pageIndex == 1) {
        stockCheckInfosEntity.clear();
      }
      stockCheckInfosEntity.addAll(value);
      return getRowList(value);
    });
  }

  @override
  HashMap<int, TableColumnWidth> getColumnWidths() {
    HashMap<int, TableColumnWidth> columnWidths = HashMap();
    columnWidths[tableTopTitles.length - 1] = FlexColumnWidth(
      0.05 * tableTopTitles.length,
    );
    for (int i = 0; i < tableTopTitles.length; i++) {
      var element = tableTopTitles[i];
      if (element == "商品名称") {
        columnWidths[i] = const FlexColumnWidth(0.8);
      }
      if (element == "条码") {
        columnWidths[i] = const FlexColumnWidth(0.5);
      }

      if (element == "属性组合") {
        columnWidths[i] = const FlexColumnWidth(0.5);
      }

      if (element == "序列号") {
        columnWidths[i] = const FlexColumnWidth(0.4);
      }
      if (element == "批次号") {
        columnWidths[i] = const FlexColumnWidth(0.4);
      }
      if (element == "生产日期") {
        columnWidths[i] = const FlexColumnWidth(0.4);
      }
      if (element == "到期日期") {
        columnWidths[i] = const FlexColumnWidth(0.4);
      }
    }
    return columnWidths;
  }

  getParams() {
    if (widget.checkTableType == CheckTableType.all) {
      infosRequestEntity.pageIndex = pageIndex;
      infosRequestEntity.pageSize = pageSize;
      infosRequestEntity.queryParams.checkType = 2;
      infosRequestEntity.queryParams.checkId = widget.checkId;
      infosRequestEntity.queryParams.checkScanPtype = checkScanPtype;
      infosRequestEntity.queryParams.viewMode = 2;
      infosRequestEntity.queryParams.checkAllStock = true;
      infosRequestEntity.queryParams.ktypeId =
          SpTool.getStoreInfo()?.ktypeId ?? "0";
    } else {
      infosRequestEntity.pageIndex = pageIndex;
      infosRequestEntity.pageSize = pageSize;
      infosRequestEntity.queryParams.checkType = 1;
      infosRequestEntity.queryParams.checkId = widget.checkId;
      infosRequestEntity.queryParams.checkScanPtype = checkScanPtype;
      infosRequestEntity.queryParams.viewMode = 2;
      infosRequestEntity.queryParams.checkAllStock = false;
      infosRequestEntity.queryParams.ktypeId =
          SpTool.getStoreInfo()?.ktypeId ?? "0";
    }
  }

  showBottomSheet(StockCheckInfoEntity infosEntity) {
    HaloBottomSheet.show(
      context,
      HaloContainer(
        height: 110,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        direction: Axis.vertical,
        margin: EdgeInsets.only(bottom: 0.w),
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              if (infosEntity.batchenabled!) {
                Navigator.pop(context);
                showBatchView(infosEntity);
              } else {
                HaloToast.show(context, msg: "当前商品不支持批次号管理");
                Navigator.pop(context);
              }
            },
            child: HaloContainer(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              color: Colors.white,
              height: 50,
              children: [
                Text(
                  "新增批次",
                  style: TextStyle(
                    fontSize: 30.sp,
                    color: Colors.black,
                    decoration: TextDecoration.none,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: HaloContainer(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              color: Colors.white,
              height: 50,
              children: [
                Text(
                  "取消",
                  style: TextStyle(
                    fontSize: 30.sp,
                    color: Colors.red,
                    decoration: TextDecoration.none,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<TableRow> getRowList(List<StockCheckInfoEntity> value) {
    int i =
        widget.checkTableType == CheckTableType.all
            ? (pageIndex == 1 ? 0 : dataSource.length)
            : 0;
    return value.map((StockCheckInfoEntity e) {
      i++;
      e.index = i;
      return TableRow(
        children: [
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    i.toString(),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap:
                      widget.isEdit
                          ? () {
                            if (widget.checkTableType != CheckTableType.all) {
                              //全仓不允许删除
                              if (widget.deleteCallback != null) {
                                widget.deleteCallback!(
                                  e.detailId ?? "",
                                  stockCheckInfosEntity.length <= 1,
                                );
                              }
                            } else {
                              HaloToast.showError(context, msg: "全仓盘点不允许删除商品");
                            }
                          }
                          : null,
                  child: HaloContainer(
                    height: 50,
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [IconFont(IconNames.qingkong)],
                  ),
                ),
                const SizedBox(width: 10),
                GestureDetector(
                  onTap:
                      widget.isEdit
                          ? () {
                            showBottomSheet(e);
                          }
                          : null,
                  child: HaloContainer(
                    height: 50,
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [IconFont(IconNames.gengduo)],
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.fullname!,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.barcode ?? "无",
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.propValues ?? "无",
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.unitName ?? "无",
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.stockQty.toString(),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Form(
                    child: HaloTextField(
                      enabled: widget.isEdit,
                      textAlign: TextAlign.center,
                      fontSize: 24.sp,
                      contentPadding: 0,
                      maxLength: 10,
                      controller: e.textEditingController,
                      keyboardType: TextInputType.number,
                      maxLines: 1,
                      onSubmitted: (v) {
                        if (cnIsNumber(v)) {
                          e.checkQty = double.parse(v);

                          if (e.snEnabled! > 0) {
                            if (e.checkQty! >= 0 &&
                                e.checkQty != e.checkSnList!.length) {
                              if (e.snEnabled == 2) {
                                if (e.checkSnList!.isNotEmpty) {
                                  HaloToast.show(
                                    context,
                                    msg: "严格序列号下盘点数量必须和序列号相等",
                                  );
                                } else {
                                  HaloToast.show(
                                    context,
                                    msg:
                                        ("严格序列号下，盘点数量必须和序列号相同,盘点数量: ${e.checkQty}  序列号数量: ${e.checkSnList!.length} 请点击【序列号录入】录入序列号或修改盘点数量"),
                                  );
                                }
                                e.checkQty = e.checkSnList!.length.toDouble();
                                e.textEditingController.text =
                                    e.checkQty.toString();
                                refreshItem();
                                return;
                              } else {
                                HaloToast.show(
                                  context,
                                  msg:
                                      ("盘点数量: ${e.checkQty}  和序列号数量:${e.checkSnList!.length} 不同,请点击【序列号录入】录入序列号或者修改盘点数量"),
                                );
                              }
                            }
                          }

                          buildQty(e);
                          //回调刷新
                          if (widget.checkCallback != null) {
                            widget.checkCallback!(
                              [e],
                              e.detailId?.isEmpty ?? true,
                              false,
                            );
                          }
                        } else {
                          HaloToast.showError(context, msg: "请输入纯数字");
                        }
                      },
                      onFocusChanged: (v) {
                        if (v) {
                        } else {
                          if (cnIsNumber(e.textEditingController.text)) {
                            e.checkQty = double.parse(
                              e.textEditingController.text,
                            );

                            if (e.snEnabled! > 0) {
                              if (e.checkQty! >= 0 &&
                                  e.checkQty != e.checkSnList!.length) {
                                if (e.snEnabled == 2) {
                                  if (e.checkSnList!.isNotEmpty) {
                                    HaloToast.show(
                                      context,
                                      msg: "严格序列号下盘点数量必须和序列号相等",
                                    );
                                  } else {
                                    HaloToast.show(
                                      context,
                                      msg:
                                          ("严格序列号下，盘点数量必须和序列号相同,盘点数量: ${e.checkQty}  序列号数量: ${e.checkSnList!.length} 请点击【序列号录入】录入序列号或修改盘点数量"),
                                    );
                                  }
                                  e.checkQty = e.checkSnList!.length.toDouble();
                                  e.textEditingController.text =
                                      e.checkQty.toString();
                                  refreshItem();
                                  return;
                                } else {
                                  HaloToast.show(
                                    context,
                                    msg:
                                        ("盘点数量: ${e.checkQty}  和序列号数量:${e.checkSnList!.length} 不同,请点击【序列号录入】录入序列号或者修改盘点数量"),
                                  );
                                }
                              }
                            }

                            buildQty(e);
                            //回调刷新
                            if (widget.checkCallback != null) {
                              widget.checkCallback!(
                                [e],
                                e.detailId?.isEmpty ?? true,
                                false,
                              );
                            }
                          } else {
                            HaloToast.showError(context, msg: "请输入纯数字");
                          }
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.adjustQty == null ? "" : e.adjustQty.toString(),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: GestureDetector(
              onTap: () {
                if (e.snEnabled != 0) {
                  showSnView(e);
                }
              },
              child: HaloContainer(
                height: 50,
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: HaloPosLabel(
                      e.snEnabled != 0 ? "序列号录入" : "",
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      textStyle: TextStyle(color: Colors.blue),
                    ),
                  ),
                ],
              ),
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.batchno ?? "",
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.produceDate!.isNotEmpty
                        ? DateUtil.DateUtil.formatDateStr(
                          e.produceDate,
                          format: "yyyy.MM.dd",
                        )
                        : "",
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          TableCell(
            child: HaloContainer(
              height: 50,
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: HaloPosLabel(
                    e.expireDate!.isNotEmpty
                        ? DateUtil.DateUtil.formatDateStr(
                          e.expireDate,
                          format: "yyyy.MM.dd",
                        )
                        : "",
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }).toList();
  }

  showBatchView(StockCheckInfoEntity infosEntity) {
    showDialog(
      context: context,
      builder:
          (context) => StockAddBatchDialog(
            infosEntity: infosEntity,
            batchCallback: (infosEntity) {
              //回调刷新
              if (widget.checkCallback != null) {
                widget.checkCallback!([infosEntity], true, true);
              }
            },
          ),
    );
  }

  showSnView(StockCheckInfoEntity infosEntity) {
    showDialog(
      context: context,
      builder: (context) => StockAddSnDialog(infosEntity),
    ).then((value) {
      if (value != null) {
        infosEntity.checkSnList = value;
        infosEntity.checkQty = infosEntity.checkSnList!.length.toDouble();
        infosEntity.textEditingController.text =
            infosEntity.checkQty.toString();
        buildQty(infosEntity);
        //回调更新
        if (widget.checkCallback != null) {
          widget.checkCallback!([infosEntity], false, false);
        }
        refreshItem();
      }
    });
  }

  buildQty(StockCheckInfoEntity infosEntity) {
    if (infosEntity.textEditingController.text.isEmpty) {
      infosEntity.checked = false;
      infosEntity.adjustQty = null;
      infosEntity.checkQty = null;
    } else {
      infosEntity.checked = true;
      infosEntity.adjustQty = infosEntity.checkQty! - infosEntity.stockQty!;
    }
  }

  bool cnIsNumber(String str) {
    if (str.isEmpty) return true;
    final reg = RegExp(r'^-?[0-9.]+$');
    return reg.hasMatch(str);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    // widget.dataSourceSelect.removeListener(() {});
    super.dispose();
  }
}
