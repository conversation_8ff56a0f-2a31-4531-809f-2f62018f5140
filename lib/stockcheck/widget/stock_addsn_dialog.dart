import 'package:flutter/cupertino.dart';
import '../widget/stock_dialog_mixin.dart';
import '../widget/stock_table_sn_widget.dart';
import 'package:halo_utils/halo_utils.dart';

import '../../bill/entity/ptype/ptype_serial_no_dto.dart';
import '../entity/stock_check_info_entity.dart';


class StockAddSnDialog extends StatefulWidget {

  final StockCheckInfoEntity infosEntity;

  const StockAddSnDialog._(this.infosEntity);
  factory StockAddSnDialog(StockCheckInfoEntity infosEntity) {
    return  StockAddSnDialog._(infosEntity);
  }
  @override
  State<StockAddSnDialog> createState() => _StockAddSnDialogState();

}

class _StockAddSnDialogState extends State<StockAddSnDialog> with StockDialogMixin {

  List<TextEditingController> dataSource = [];

  @override
  // TODO: implement title
  String get title => "录入序列号";

  @override
  // TODO: implement heightDialog
  double get heightContent => 600.w;
  // TODO: implement heightDialog

  @override
  void initState() {
    // TODO: implement initState
    setHeightDialog(600.w);
    List<TextEditingController> list = widget.infosEntity.checkSnList!.map((e){
      TextEditingController textEditingController = TextEditingController();
      textEditingController.text = e.snno??"";
      return textEditingController;
    }).toList();
    dataSource.addAll(list);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return buildDialog(context);}

  ///content
  @override
  Widget buildContent(BuildContext cotext) {
    // TODO: implement buildContent
    return StockTableSnWidget(dataSource:dataSource,onFocusChanged: (v){
      if(v){
        setState(() {
          setHeightDialog(heightContent+400.w);
        });
      }else{
        setState(() {
          setHeightDialog(heightContent);
        });
      }
    },);
  }

  @override
  clickBtn() {
   List<StockCheckInfoMySnList>serialNoList =  dataSource.where((element) => element.text.isNotEmpty).map((e) {
     StockCheckInfoMySnList snList =  StockCheckInfoMySnList();
     snList.snno = e.text;
     return snList;
    }).toList();
    Navigator.pop(context,serialNoList);
    return super.clickBtn();
  }

}