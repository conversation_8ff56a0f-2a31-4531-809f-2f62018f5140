import 'dart:convert';

import 'package:flutter/material.dart';
import '../../widgets/halo_pos_label.dart';
import 'stock_dialog_mixin.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/date_util.dart' as DateUtil;
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/time_picker/halo_date_time_pickers.dart';

import '../../common/style/app_color_helper.dart';
import '../../iconfont/icon_font.dart';
import '../entity/stock_check_info_entity.dart';

typedef BatchCallback = void Function(StockCheckInfoEntity);

class StockAddBatchDialog extends StatefulWidget {

  final BatchCallback batchCallback;
  final StockCheckInfoEntity infosEntity;
  const StockAddBatchDialog._({required this.infosEntity,required this.batchCallback});
  factory StockAddBatchDialog({required StockCheckInfoEntity infosEntity,required BatchCallback batchCallback}) {
    return  StockAddBatchDialog._(infosEntity:infosEntity,batchCallback: batchCallback,);
  }
  @override
  State<StockAddBatchDialog> createState() => _StockAddBatchDialogState();

}

class _StockAddBatchDialogState extends State<StockAddBatchDialog> with StockDialogMixin, WidgetsBindingObserver {

  @override
  // TODO: implement title
  String get title => "新增批次";
  double width = 450.w;
  double get height => 50.h;
  @override
  // TODO: implement heightDialog
  double get heightContent => 480.w;

  Color borderColor = const Color(0xFFF6F6F6);
  final TextEditingController textProductStartController = TextEditingController();
  final TextEditingController textProductEndController = TextEditingController();
  final TextEditingController textBatchController = TextEditingController();
  final TextEditingController textQtyController = TextEditingController();

  DateTime currentTime = DateTime.now();

  @override
  void initState() {
    // TODO: implement initState
    textProductStartController.text =  DateUtil.DateUtil.getDateStrByDateTime(currentTime,format: DateFormat.YEAR_MONTH_DAY)!;
    DateTime endTime = currentTime.add(Duration(days: widget.infosEntity.protectDays!));
    textProductEndController.text =  DateUtil.DateUtil.getDateStrByDateTime(endTime,format: DateFormat.YEAR_MONTH_DAY)!;
    setHeightDialog(heightContent);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return buildDialog(context);}

  ///content
 @override
  Widget buildContent(BuildContext cotext) {
    // TODO: implement buildContent
    return HaloContainer(
      direction: Axis.vertical,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      height: 700.w,
      children: [
        HaloContainer(
          direction: Axis.vertical,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          padding: EdgeInsets.all(25.w),
          children: [
            HaloContainer(
              height: 50.w,
              children: [Expanded(child: HaloPosLabel("商品名称：${widget.infosEntity.fullname??""}",textStyle: TextStyle(fontSize: 24.sp),))],
            ),
            HaloContainer(
              height: 50.w,
              children: [Expanded(child: HaloPosLabel("保质期    ：${getUnitProtectDays(widget.infosEntity.protectDays, widget.infosEntity.protectDaysUnit)}${getUnitName(widget.infosEntity.protectDaysUnit)}",textStyle: TextStyle(fontSize: 24.sp)))],
            ),
            Visibility(
              visible: widget.infosEntity.protectDays!>0,
              child: HaloContainer(margin: EdgeInsets.only(top: 15.w), height: 50.w, children: [
                _buildDateTime("生产日期", textProductStartController,callBack: (time){
                  DateTime endTime = time.add(Duration(days: widget.infosEntity.protectDays!));
                  setState(() {
                    textProductEndController.text = DateUtil.DateUtil.getDateStrByDateTime(endTime,format: DateFormat.YEAR_MONTH_DAY)!;
                  });
                }),
                const SizedBox(width: 30,),
                _buildDateTime("到期日期", textProductEndController,callBack: (time){
                  DateTime starTime =  time.subtract(Duration(days: widget.infosEntity.protectDays!));
                  setState(() {
                    textProductStartController.text = DateUtil.DateUtil.getDateStrByDateTime(starTime,format: DateFormat.YEAR_MONTH_DAY)!;
                  });
                }),
              ],),
            ),
            const SizedBox(height: 15,),
            HaloContainer(height: 50.w,children: [
              _buildTextField("批次号    ", textBatchController,TextInputType.text),
              const SizedBox(width: 30,),
              _buildTextField("盘点数量", textQtyController,TextInputType.number),
            ],)

          ],
        )
      ],
    );
  }

  Widget _buildDateTime(String title,TextEditingController textTimeController,{Function(DateTime time)? callBack}){
    return  HaloContainer(
      mainAxisSize: MainAxisSize.max,
      width: width,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 3,right: 16),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 24.sp,
                color: AppColorHelper(context).getNormalTitleTextColor()),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              HaloDateTimePickers.showDateTimePickerView(context,
                  currentDateTime:
                  DateUtil.DateUtil.getDateTime(textTimeController.text),
                  onSubmitted: (selectedDateTime) {
                    //  DateTime.now().add(Duration(days: -30)),
                    textTimeController.text =
                    DateUtil.DateUtil.getDateStrByDateTime(selectedDateTime,format: DateFormat.YEAR_MONTH_DAY)!;
                    //计算结束时间
                    if(callBack!=null){
                      callBack(selectedDateTime!);
                    }
                  });
            },
            child: HaloContainer(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              color: Colors.white,
                height: height,
              padding: EdgeInsets.only(left: 15.w,right: 15.w),
              border: Border.all(color:borderColor, width: 1),
              children: [HaloTextField(
                controller: textTimeController,
                contentPadding: 0,
                fontSize: 24.sp,
                maxLines: 1,
                enabled: false,
              ),        IconFont(IconNames.rili),
              ]
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField(String title,TextEditingController editingController,TextInputType keyboardType){
    return  HaloContainer(
      mainAxisSize: MainAxisSize.max,
      width: width,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 3,right: 16),
          child: HaloPosLabel(
            title,
            textAlign: TextAlign.center,
            textStyle: TextStyle(
                fontSize: 24.sp,
                color: AppColorHelper(context).getNormalTitleTextColor()),
          ),
        ),
        Expanded(
          child: HaloContainer(
              color: Colors.white,
              border: Border.all(color: borderColor, width: 1),
              padding: EdgeInsets.only(left: 15.w,right: 15.w),
              height: height,
              children: [Expanded(
                child: HaloTextField(
                  controller: editingController,
                  fontSize: 24.sp,
                  contentPadding: 0,
                  maxLines: 1,
                  keyboardType: keyboardType,
                  backgroundColor: Colors.transparent,
                    onFocusChanged: (v){
                    if(v){
                      setState(() {
                        setHeightDialog(heightContent+300.w);
                      });
                    }else{
                      setState(() {
                        setHeightDialog(heightContent);
                      });
                    }
                    },
                ),
              ),]
          ),
        )
      ],
    );
  }

  String getUnitName(unit) {
    if (unit == 0) {
      return "天";
    } else if (unit == 1) {
      return "周";
    } else if (unit == 2) {
      return "月";
    } else if (unit == 3) {
      return "年";
    }
    return "";
  }

  int getUnitProtectDays(days, unit) {
    if (unit == 0) {
      return days;
    } else if (unit == 1) {
      return days / 7;
    } else if (unit == 2) {
      return days / 30;
    } else if (unit == 3) {
      return days / 365;
    }
    return 0;
  }


  @override
  clickBtn(){
    super.clickBtn();
    if(verifyInfo()){
      //确定
      //深拷贝
      Map<String,dynamic> cloneObject = jsonDecode(jsonEncode(widget.infosEntity));
      StockCheckInfoEntity infosEntity = StockCheckInfoEntity.fromJson(cloneObject);
      infosEntity.produceDate = widget.infosEntity.protectDays!>0?textProductStartController.text:"";
      infosEntity.expireDate = widget.infosEntity.protectDays!>0?textProductEndController.text:"";
      infosEntity.textEditingController.text = textQtyController.text;
      infosEntity.batchno = textBatchController.text;
      infosEntity.index = widget.infosEntity.index;
      infosEntity.textEditingController.text = textQtyController.text;
      infosEntity.checkSnList = [];
      infosEntity.checkQty =  double.parse(textQtyController.text);
      infosEntity.checked = true;
      widget.batchCallback(infosEntity);
      Navigator.pop(context);
    }
  }

  bool verifyInfo(){
    if(textQtyController.text.isEmpty){
      HaloToast.showError(context,msg: "请输入盘点数量");
      return false;
    }
    if(!cnIsNumber(textQtyController.text)){
      HaloToast.showError(context,msg: "盘点数量请输入纯数字");
      return false;
    }

    if(textProductStartController.text.isEmpty&&widget.infosEntity.protectDays!>0){
      HaloToast.showError(context,msg: "请输入生产日期");
      return false;
    }
    if(textProductEndController.text.isEmpty&&widget.infosEntity.protectDays!>0){
      HaloToast.showError(context,msg: "请输入到期日期");
      return false;
    }
    if(textBatchController.text.isEmpty){
      HaloToast.showError(context,msg: "请输入批次号");
      return false;
    }

    return true;
  }
  bool cnIsNumber(String str) {
    final reg = RegExp(r'^-?[0-9.]+$');
    return reg.hasMatch(str);
  }



}
