
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../common/standard.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../../iconfont/icon_font.dart';

mixin StockDialogMixin<T extends StatefulWidget> on State<T> {


  String get title => "标题";

  double get widthDialog => 1000.w;
  double _heightDialog = 400.w;
  double get heightContent=> 400.w;

  final FocusNode _focusNode = FocusNode();
  final EdgeInsets _padding = EdgeInsets.symmetric(horizontal: 22.w);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      FocusScope.of(context).requestFocus(_focusNode);
    });
  }

  double getHeightDialog(){
    return _heightDialog;
  }

   setHeightDialog(height){
    setState(() {
      _heightDialog = height;
    });
  }

  Widget buildDialog(BuildContext context) {
    return UnconstrainedBox(
      child: SizedBox(
        width: widthDialog,
        height: getHeightDialog(),
        child: ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(10.w)),
          child: GestureDetector(
            onTap: (){
              FocusScope.of(context).requestFocus(_focusNode);
            },
            child: HaloContainer(
              height: heightContent,
              direction: Axis.vertical,
              color: Colors.transparent,
              children:[Container(
                  decoration:  BoxDecoration(color: Colors.transparent,
                    borderRadius: BorderRadius.all(Radius.circular(15.w)),
                  ),
                  child: Material(
                    child: HaloContainer(
                      height: heightContent,
                      borderRadius:BorderRadius.all(Radius.circular(15.w)),
                      color: Colors.white,
                      direction: Axis.vertical,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        _buildTitle(context),
                        Divider(height: 1.h, color: const Color(0xFFDBDBDB)),
                        Expanded(child: buildContent(context)),
                        _buildBottomButtons(),
                      ],
                    ),
                  ))] ,
            ),
          ),
        ),
      ),
    );
  }
  ///标题行
  Widget _buildTitle(BuildContext context) {
    return Container(
      color: Colors.transparent,
      height: 80.h,
      padding: _padding,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              color: const Color(0xFF333333),
              fontSize: 28.sp,
            ),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => Navigator.pop(context),
            child: Padding(
              padding: EdgeInsets.all(10.w),
              child: IconFont(IconNames.close, size: 20.w),
            ),
          ),
        ],
      ),
    );
  }

  ///content
  Widget buildContent(BuildContext cotext){
    return Container();
  }

  ///底部
  Widget _buildBottomButtons(){
    return Container(
      color: Colors.white,
      height: 50.h,
      margin: EdgeInsets.only(bottom: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildButton(
              context,
              "确定",
              textColor: Colors.white,
              background: const Color(0xFF4679FC),
              onTap: clickBtn
          ),
          SizedBox(width: 30.w),
          _buildButton(context, "取消", onTap: () => Navigator.pop(context)),

        ],
      ),
    );
  }
  ///底部按钮
  Widget _buildButton(
      BuildContext context,
      String content, {
        Color textColor = const Color(0xFF333333),
        Color background = Colors.white,
        Color borderColor = const Color(0xFF999999),
        VoidCallback? onTap,
      }) {
    return GestureDetector(
        onTap: onTap,
        child: Container(
          height: 50.h,
          width: 196.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: background,
            borderRadius: BorderRadius.circular(4.w),
            border: borderColor.let(
                    (borderColor) => Border.all(color: borderColor, width: 2.w)),
          ),
          child: Text(content,
              style: TextStyle(color: textColor, fontSize: 26.sp)),
        ));
  }

  clickBtn(){
    FocusScope.of(context).requestFocus(_focusNode);
  }
}