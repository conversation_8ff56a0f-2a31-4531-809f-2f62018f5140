// import 'dart:io';
//
// import 'package:dotted_border/dotted_border.dart';
// import 'package:flutter/material.dart';
// import 'package:halo_pos/common/tool/sp_tool.dart';
// import 'package:halo_utils/halo_utils.dart';
// import 'package:halo_utils/utils/string_util.dart';
// import 'package:haloui/haloui.dart';
// import 'package:keyboard_avoider/keyboard_avoider.dart';
// import 'package:qr_flutter/qr_flutter.dart';
//
// import '../bill/tool/decimal_display_helper.dart';
// import '../common/login/login_center.dart';
// import '../common/string_res.dart';
// import '../common/style/app_colors.dart';
// import '../common/tool/image_tool.dart';
// import '../entity/system/system_config_dto.dart';
// import '../enum/bill_type.dart';
// import '../iconfont/icon_font.dart';
// import '../login/entity/store/store_info.dart';
// import '../login/model/login_user_model.dart';
// import '../widgets/base/base_stateful_page.dart';
// import '../widgets/base/halo_pos_alert_dialog.dart';
// import '../widgets/dotted_line.dart';
// import '../widgets/halo_pos_label.dart';
// import 'tool/print_config.dart';
// import 'tool/print_tool.dart';
//
// ///
// ///@ClassName: print_template_config
// ///@Description: 小票打印模版配置
// ///@Author: tanglan
// ///@Date: 2024/8/13
// class PrintTemplateConfigPage extends BaseStatefulPage {
//   const PrintTemplateConfigPage({Key? key}) : super(key: key, rightFlex: 0.32);
//
//   @override
//   BaseStatefulPageState<BaseStatefulPage> createState() {
//     return _PrintTemplateConfigState();
//   }
// }
//
// class _PrintTemplateConfigState
//     extends BaseStatefulPageState<PrintTemplateConfigPage> {
//   BillType _selectPrintBill = BillType.SaleBill;
//   late ShopConfigInfo _shopConfigInfo;
//   late Map<String, dynamic> _printConfig;
//   final StoreInfo _storeInfo = SpTool.getStoreInfo() ?? StoreInfo();
//   final SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
//
//   @override
//   String getActionBarTitle() {
//     return "收银小票样式设置";
//   }
//
//   @override
//   Future<void>? onInitState() {
//     _shopConfigInfo = ShopConfigInfo();
//     _printConfig =
//         PrintTool.getBillPrintMap(BillTypeData[_selectPrintBill] ?? "",[]);
//     return Future.value(null);
//   }
//
//   @override
//   Widget buildLeftBody(BuildContext context) {
//     return HaloContainer(
//       mainAxisSize: MainAxisSize.max,
//       direction: Axis.horizontal,
//       color: AppColors.pageBackgroundColor,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Expanded(
//             child: _BillPrintFieldPage(
//                 _selectPrintBill, _printConfig, systemConfigDto,
//                 onBillPrintConfigChangeCallBack:
//                     (BillType billType, Map<String, dynamic> printConfig) {
//                   setState(() {
//                     _selectPrintBill = billType;
//                     _printConfig = printConfig;
//                   });
//                 })),
//         Expanded(
//             child: _PrintViewPage(
//               shopConfigInfo: _shopConfigInfo,
//               printConfig: _printConfig,
//               billType: _selectPrintBill,
//               storeInfo: _storeInfo,
//               systemConfigDto: systemConfigDto,
//             ))
//         // ,
//       ],
//     );
//   }
//
//   @override
//   Widget buildRightBody(BuildContext context) {
//     return _BaseShopPrintConfigPage(
//       shopConfigInfo: _shopConfigInfo,
//       onEditChangeBack: (ShopConfigInfo shopConfigInfo) {
//         setState(() {
//           _shopConfigInfo = shopConfigInfo;
//           // SpTool.saveShopInfoPrintConfig(_shopConfigInfo);
//         });
//       },
//     );
//   }
// }
//
// //region  单据打印字段配置展示
// class _BillPrintFieldPage extends StatefulWidget {
//   final Function(BillType billType, Map<String, dynamic> printConfig)
//   onBillPrintConfigChangeCallBack;
//   final BillType billType;
//   final Map<String, dynamic> printConfig;
//   final SystemConfigDto systemConfigDto;
//
//   const _BillPrintFieldPage(
//       this.billType, this.printConfig, this.systemConfigDto,
//       {required this.onBillPrintConfigChangeCallBack});
//
//   @override
//   State<StatefulWidget> createState() {
//     return _BillPrintFieldPageState();
//   }
// }
//
// class _BillPrintFieldPageState extends State<_BillPrintFieldPage> {
//   final GlobalKey _printBillGlobalKey = GlobalKey(); //门店选择框key
//   late Map<String, dynamic> _printConfig;
//
//   @override
//   void initState() {
//     _printConfig = widget.printConfig;
//     super.initState();
//   }
//
//   ///支持配置打印样式的单据
//   final Map<BillType, String> _printBillList = {
//     BillType.SaleBill: "销售单小票样式", //销售单
//     BillType.SaleBackBill: "退货单小票样式", //销售退货
//     BillType.SaleChangeBill: "换货单小票样式", //销售退货
//     BillType.GoodsTrans: "调拨单小票样式", //调拨单 --直接过账
//     BillType.TransferOrder: "调拨订单小票样式", //调拨订单
//     BillType.ChannelBill: "全渠道订单小票样式", //全渠道订单
//   };
//
//   @override
//   void didUpdateWidget(covariant _BillPrintFieldPage oldWidget) {
//     _printConfig = widget.printConfig;
//     super.didUpdateWidget(oldWidget);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return HaloContainer(
//         direction: Axis.vertical,
//         mainAxisSize: MainAxisSize.max,
//         color: Colors.white,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           buildBillListSelect(),
//           Expanded(
//               child: _PrintFieldShowConfigPage(
//                   printConfig: _printConfig,
//                   printConfigName: PrintFieldConfig.printConfigName(
//                       isTaxEnable:
//                       widget.systemConfigDto.sysGlobalEnabledTax &&
//                           widget.systemConfigDto
//                               .sysGlobalEnabledSaleTax)[
//                   BillTypeData[widget.billType]] ??
//                       [],
//                   onEditPrintConfigCallBack: (Map<String, dynamic> printConfig,
//                       {bool? isRefresh}) {
//                     _printConfig = printConfig;
//                     if (null == isRefresh || isRefresh) {
//                       widget.onBillPrintConfigChangeCallBack(
//                           widget.billType, _printConfig);
//                     }
//                   })),
//           HaloContainer(
//             mainAxisSize: MainAxisSize.max,
//             mainAxisAlignment: MainAxisAlignment.center,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             border: Border(
//                 top: BorderSide(
//                     color: AppColors.pageBackgroundColor, width: 1.h)),
//             margin: EdgeInsets.only(top: 12.h),
//             padding: EdgeInsets.symmetric(vertical: 6.h),
//             children: [
//               HaloButton(
//                 text: "保存",
//                 height: 42.h,
//                 backgroundColor: AppColors.accentColor,
//                 onPressed: () {
//                   SpTool.savePrintField(
//                       BillTypeData[widget.billType], _printConfig);
//                   widget.onBillPrintConfigChangeCallBack(
//                       widget.billType, _printConfig);
//                   HaloToast.show(context, msg: "打印信息保存成功");
//                 },
//               ),
//               HaloContainer(
//                 margin: EdgeInsets.only(left: 40.w),
//                 children: [
//                   HaloButton(
//                     text: "取消",
//                     buttonType: HaloButtonType.outlinedButton,
//                     borderColor: AppColors.btnBorderColor,
//                     textColor: AppColors.normalTextColor,
//                     height: 42.h,
//                     outLineWidth: 1.h,
//                     onPressed: () {
//                       widget.onBillPrintConfigChangeCallBack(
//                           widget.billType,
//                           PrintTool.getPrintConfig(
//                               BillTypeData[widget.billType] ?? ""));
//                     },
//                   ),
//                 ],
//               )
//             ],
//           )
//         ]);
//   }
//
//   //region 单据选择项配置
//   ///构建打印单据选择
//   Widget buildBillListSelect() {
//     return GestureDetector(
//         onTap: () {
//           _showBillSelectWindow(context);
//         },
//         behavior: HitTestBehavior.opaque,
//         child: HaloContainer(
//           borderRadius: const BorderRadius.all(Radius.circular(6)),
//           border: Border.all(color: AppColors.borderColor),
//           margin: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
//           padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 8.h),
//           key: _printBillGlobalKey,
//           children: [
//             Expanded(
//                 child: HaloContainer(children: [
//                   IconFont(
//                     IconNames.xiaopiao_1,
//                     size: 24.w,
//                   ),
//                   SizedBox(
//                     width: 8.w,
//                   ),
//                   Text(
//                     _printBillList[widget.billType] ?? "",
//                     style: TextStyle(
//                         color: AppColors.titleBoldTextColor,
//                         fontSize: 24.sp,
//                         fontWeight: FontWeight.w500),
//                   ),
//                 ])),
//             IconFont(
//               IconNames.jiantou,
//               size: 12.w,
//               color: "#FF979797",
//             )
//           ],
//         ));
//   }
//
//   //单据选择列表
//   _showBillSelectWindow(BuildContext context) {
//     HaloPopWindow().show(_printBillGlobalKey,
//         intervalLeft: 24.w,
//         intervalTop: -18.h,
//         gravity: PopWindowGravity.bottom,
//         backgroundColor: Colors.transparent,
//         // intervalTop: 10,
//         child: Container(
//           width: 660.w,
//           height: _printBillList.length * 61.w,
//           constraints: BoxConstraints(maxHeight: 800.w),
//           decoration: BoxDecoration(
//               color: Colors.white,
//               border: Border.all(color: AppColors.dividerColor),
//               borderRadius: BorderRadius.all(Radius.circular(8.w)),
//               boxShadow: [
//                 BoxShadow(
//                     color: Colors.black.withOpacity(0.5),
//                     offset: const Offset(10, 40),
//                     blurRadius: 45,
//                     spreadRadius: 0)
//               ]),
//           child: ListView.builder(
//               padding: const EdgeInsets.all(0),
//               itemCount: _printBillList.values.length,
//               itemBuilder: (buildContext, index) {
//                 return buildBillSelectItem(
//                   name: _printBillList.values.elementAt(index),
//                   index: index,
//                   onSelectItem: (selectIndex) {
//                     HaloPopWindow().disMiss();
//                     BillType selectBillType =
//                     _printBillList.keys.elementAt(selectIndex);
//                     if (selectBillType == widget.billType) {
//                       return;
//                     }
//
//                     if (checkUnSaveTips()) {
//                       HaloPosAlertDialog.showAlertDialog(context,
//                           dismissOnTouchOutside: false,
//                           dismissOnBackKeyPress: false,
//                           content: StringRes.TIP_PRINT_CHANGE_BILL
//                               .getText(context), onSubmitCallBack: () {
//                             widget.onBillPrintConfigChangeCallBack(
//                                 selectBillType,
//                                 PrintTool.getPrintConfig(
//                                     BillTypeData[selectBillType] ?? ""));
//                           });
//                       return;
//                     }
//                     widget.onBillPrintConfigChangeCallBack(
//                         selectBillType,
//                         PrintTool.getPrintConfig(
//                             BillTypeData[selectBillType] ?? ""));
//                   },
//                 );
//               }),
//         ));
//   }
//
//   ///单据弹框行组件
//   Widget buildBillSelectItem(
//       {required String name, required int index, Function(int)? onSelectItem}) {
//     return GestureDetector(
//         behavior: HitTestBehavior.opaque,
//         onTap: () {
//           if (null != onSelectItem) {
//             onSelectItem(index);
//           }
//         },
//         child: Container(
//           height: 60.w,
//           alignment: Alignment.centerLeft,
//           padding: EdgeInsets.symmetric(horizontal: 24.w),
//           decoration: const BoxDecoration(
//             border: Border(bottom: BorderSide(color: AppColors.dividerColor)),
//           ),
//           child: Text(
//             name,
//             maxLines: 1,
//             overflow: TextOverflow.ellipsis,
//             style: TextStyle(fontSize: 24.sp, color: AppColors.normalTextColor),
//           ),
//         ));
//   }
//
//   ///单据切换未保存提示
//   bool checkUnSaveTips() {
//     Map<String, dynamic> oldSelectPrintConfig =
//     PrintTool.getPrintConfig(BillTypeData[widget.billType] ?? "");
//
//     for (String key in _printConfig.keys) {
//       if (!oldSelectPrintConfig.containsKey(key) ||
//           oldSelectPrintConfig[key] != _printConfig[key]) {
//         return true;
//       }
//     }
//     return false;
//   }
// //endregion
// }
//
// //region 打印字段显示配置信息组件
// class _PrintFieldShowConfigPage extends StatefulWidget {
//   final Function(Map<String, dynamic> printConfig, {bool? isRefresh})
//   onEditPrintConfigCallBack;
//
//   ///获取对应单据的打印配置名称
//   final List<String> printConfigName;
//
//   ///获取对应单据的打印配置项
//   final Map<String, dynamic> printConfig;
//
//   const _PrintFieldShowConfigPage(
//       {required this.printConfig,
//         required this.printConfigName,
//         required this.onEditPrintConfigCallBack});
//
//   @override
//   State<StatefulWidget> createState() {
//     return _PrintFieldShowConfigPageState();
//   }
// }
//
// class _PrintFieldShowConfigPageState extends State<_PrintFieldShowConfigPage> {
//   late Map<String, dynamic> _printConfig;
//
//   TextEditingController decorateTopEditController = TextEditingController();
//   TextEditingController decorateTailEditController = TextEditingController();
//
//   ScrollController scrollController = ScrollController();
//
//   @override
//   void initState() {
//     _printConfig = widget.printConfig;
//     super.initState();
//   }
//
//   @override
//   void didUpdateWidget(covariant _PrintFieldShowConfigPage oldWidget) {
//     _printConfig = widget.printConfig;
//     decorateTopEditController.text =
//         _printConfig[PrintFieldConfig.decorateTopText] ?? "";
//     decorateTailEditController.text =
//         _printConfig[PrintFieldConfig.decorateTailText] ?? "";
//     super.didUpdateWidget(oldWidget);
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return KeyboardAvoider(
//         autoScroll: true,
//         child: SingleChildScrollView(
//             controller: scrollController,
//             child: HaloContainer(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               direction: Axis.vertical,
//               children: getPrintConfigListWidget(),
//             )));
//   }
//
//   ///构建单据打印字段内容
//   List<Widget> getPrintConfigListWidget() {
//     List<Widget> printItems = [];
//     for (PrintConfigGroup item in PrintConfigGroup.values) {
//       List<String>? groupFieldList =
//       PrintFieldConfig.printConfigGroupList[item];
//       if (null == groupFieldList || groupFieldList.isEmpty) {
//         continue;
//       }
//
//       ///将单据配置所需配置的打印字段进行分组
//       List<String> billGroupPrintField = widget.printConfigName
//           .where((element) => groupFieldList.contains(element))
//           .toList();
//
//       if (billGroupPrintField.isEmpty) {
//         continue;
//       }
//       String groupName = PrintFieldConfig.printConfigGroupName[item] ?? "";
//       printItems.add(_buildItem(item, groupName, billGroupPrintField));
//     }
//     return printItems;
//   }
//
//   ///构建打印信息编辑项（目前仅票头装饰）
//   Widget buildItemContent(bool showContent) {
//     if (!showContent) {
//       return Container();
//     }
//
//     return HaloContainer(
//       visible: (_printConfig[PrintFieldConfig.decorateTop] ?? false) ||
//           (_printConfig[PrintFieldConfig.decorateTail] ?? false),
//       direction: Axis.vertical,
//       margin: EdgeInsets.only(top: 16.h),
//       border: const Border(
//           top: BorderSide(width: 0.5, color: AppColors.borderColor)),
//       children: [
//         buildDecorateItem(
//             PrintFieldConfig.decorateTop, decorateTopEditController),
//         buildDecorateItem(
//             PrintFieldConfig.decorateTail, decorateTailEditController),
//       ],
//     );
//   }
//
//   ///票装饰内容项
//   buildDecorateItem(
//       String fieldKey, TextEditingController textEditingController) {
//     ///装饰打印格式
//     String printStyleModelKey = PrintFieldConfig.decorateTopModel;
//
//     ///装饰文本字段
//     String printStyleModelTextKey = PrintFieldConfig.decorateTopText;
//
//     ///装饰图片字段
//     String printStyleModelImgUrlKey = PrintFieldConfig.decorateTopImgUrl;
//     if (fieldKey == PrintFieldConfig.decorateTail) {
//       printStyleModelKey = PrintFieldConfig.decorateTailModel;
//       printStyleModelTextKey = PrintFieldConfig.decorateTailText;
//       printStyleModelImgUrlKey = PrintFieldConfig.decorateTailImgUrl;
//     }
//
//     int index = _printConfig[printStyleModelKey] ?? 0;
//     return HaloContainer(
//       visible: _printConfig[fieldKey] ?? false,
//       mainAxisSize: MainAxisSize.max,
//       margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         HaloPosLabel(PrintFieldConfig.printConfigNameStr[fieldKey] ?? ''),
//         Expanded(
//             child: HaloContainer(
//               direction: Axis.vertical,
//               margin: EdgeInsets.only(left: 10.w),
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 _buildPrintStyleModel(printStyleModelKey),
//                 HaloContainer(
//                     visible: index == PrintStyleModeEnum.text.index,
//                     padding: EdgeInsets.all(8.w),
//                     mainAxisSize: MainAxisSize.max,
//                     margin: EdgeInsets.symmetric(vertical: 10.h),
//                     border: Border.all(color: AppColors.borderColor),
//                     borderRadius: const BorderRadius.all(Radius.circular(4)),
//                     children: [
//                       Expanded(
//                           child: HaloTextField(
//                             showCounter: true,
//                             controller: textEditingController,
//                             fontSize: 20.sp,
//                             maxLength: 500,
//                             hintText: "添加文本内容",
//                             contentPadding: 0,
//                             maxLines: 3,
//                             onChanged: (String text) {
//                               _printConfig[printStyleModelTextKey] =
//                                   textEditingController.text;
//                               widget.onEditPrintConfigCallBack(_printConfig,
//                                   isRefresh: false);
//                             },
//                             onEditingComplete: () {
//                               widget.onEditPrintConfigCallBack(_printConfig);
//                             },
//                           ))
//                     ]),
//                 Visibility(
//                     visible: index == PrintStyleModeEnum.image.index,
//                     child: GestureDetector(
//                         onTap: () {
//                           ImageTool.pickGallery(context,
//                               onSelectBack: (List<String>? selectBackList) {
//                                 if (null == selectBackList || selectBackList.isEmpty) {
//                                   return;
//                                 }
//                                 _printConfig[printStyleModelImgUrlKey] =
//                                     selectBackList.first;
//                                 widget.onEditPrintConfigCallBack(_printConfig);
//                                 setState(() {});
//                               });
//                         },
//                         child: Container(
//                             margin: EdgeInsets.symmetric(vertical: 10.h),
//                             child: DottedBorder(
//                                 color: AppColors.borderColor,
//                                 strokeWidth: 1,
//                                 borderType: BorderType.RRect,
//                                 child: HaloContainer(
//                                     height: 150.h,
//                                     mainAxisAlignment: MainAxisAlignment.center,
//                                     mainAxisSize: MainAxisSize.max,
//                                     children: [
//                                       HaloContainer(
//                                           visible: !_printConfig.containsKey(
//                                               printStyleModelImgUrlKey) ||
//                                               StringUtil.isEmpty(_printConfig[
//                                               printStyleModelImgUrlKey]),
//                                           padding: EdgeInsets.only(
//                                               top: 24.h,
//                                               bottom: 16.h,
//                                               left: 8.w,
//                                               right: 8.w),
//                                           direction: Axis.vertical,
//                                           // border: Border.all(color: AppColors.borderColor),
//                                           borderRadius: const BorderRadius.all(
//                                               Radius.circular(4)),
//                                           children: [
//                                             IconFont(
//                                               IconNames.shangchuantupian,
//                                               size: 26,
//                                             ),
//                                             SizedBox(
//                                               height: 4.h,
//                                             ),
//                                             HaloPosLabel(
//                                               "点击上传图片",
//                                               textStyle: TextStyle(
//                                                   color: AppColors.normalTextColor,
//                                                   fontWeight: FontWeight.w500,
//                                                   fontSize: 22.sp),
//                                             ),
//                                             SizedBox(
//                                               height: 4.h,
//                                             ),
//                                             HaloPosLabel(
//                                               "图片支持JPG,JPEG,BMP,PNG格式，大小不超过2M",
//                                               textStyle: TextStyle(
//                                                   color: AppColors.hintColor,
//                                                   fontSize: 18.sp),
//                                             )
//                                           ]),
//                                       Visibility(
//                                           visible: _printConfig.containsKey(
//                                               printStyleModelImgUrlKey) &&
//                                               StringUtil.isNotEmpty(_printConfig[
//                                               printStyleModelImgUrlKey]),
//                                           child: buildImageView(_printConfig[
//                                           printStyleModelImgUrlKey]))
//                                     ]))))),
//               ],
//             ))
//       ],
//     );
//   }
//
//   Widget _buildPrintStyleModel(String fieldKey) {
//     return HaloContainer(
//       mainAxisSize: MainAxisSize.max,
//       children: [
//         Expanded(
//             child:
//             _buildPrintStyleModelItem(fieldKey, PrintStyleModeEnum.text)),
//         SizedBox(
//           width: 10.w,
//         ),
//         Expanded(
//             child:
//             _buildPrintStyleModelItem(fieldKey, PrintStyleModeEnum.image)),
//         const Expanded(
//           flex: 3,
//           child: SizedBox(),
//         )
//       ],
//     );
//   }
//
//   Widget _buildPrintStyleModelItem(
//       String fieldKey, PrintStyleModeEnum modeEnum) {
//     int index = widget.printConfig[fieldKey] ?? 0;
//
//     return GestureDetector(
//         onTap: () {
//           _printConfig[fieldKey] = modeEnum.index;
//           widget.onEditPrintConfigCallBack(_printConfig);
//         },
//         child: Container(
//           alignment: Alignment.center,
//           constraints: BoxConstraints(minHeight: 48.h),
//           decoration: BoxDecoration(
//             color: index == modeEnum.index
//                 ? AppColors.selectbackGroundColor
//                 : Colors.white,
//             border: Border.all(
//                 color: index == modeEnum.index
//                     ? AppColors.accentColor
//                     : AppColors.unEnableBorderColor),
//             borderRadius: const BorderRadius.all(Radius.circular(4)),
//           ),
//           child: HaloPosLabel(
//             PrintFieldConfig.printStyleModeEnumName[modeEnum] ?? '',
//             textAlign: TextAlign.center,
//             textStyle: TextStyle(
//                 color: index == modeEnum.index
//                     ? AppColors.accentColor
//                     : AppColors.normalTextColor,
//                 fontSize: 20.sp),
//           ),
//         ));
//   }
//
//   ///图片展示
//   Widget buildImageView(String? photoUrl) {
//     if (StringUtil.isNotEmpty(photoUrl)) {
//       return Image.file(
//         File(photoUrl!),
//         height: 150.h,
//       );
//     }
//     return Image.asset(
//       'assets/images/quanyika.png',
//       width: 110.w,
//       height: 110.w,
//     );
//   }
//
//   ///构建单据配置的每一类分组组件
//   ///billGroupPrintField 单据配置中每一组分组的内容
//   Widget _buildItem(
//       PrintConfigGroup item,
//       String title,
//       List<String> billGroupPrintField,
//       ) {
//     return HaloContainer(
//         direction: Axis.vertical,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         margin: EdgeInsets.only(bottom: 20.h),
//         children: [
//           HaloContainer(
//             color: AppColors.pageBackgroundColor,
//             mainAxisSize: MainAxisSize.max,
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             padding:
//             EdgeInsets.only(left: 16.w, top: 8.h, bottom: 8.h, right: 28.h),
//             children: [
//               HaloPosLabel(
//                 title,
//                 textStyle: TextStyle(
//                     fontSize: 22.sp,
//                     color: Colors.black,
//                     fontWeight: FontWeight.w500),
//               ),
//               IconFont(
//                 IconNames.jiantou,
//                 size: 12.w,
//                 color: "#FF979797",
//               )
//             ],
//           ),
//           SizedBox(
//             height: 18.h,
//           ),
//           GridListView(
//             dataCount: billGroupPrintField.length,
//             columnCount: 5,
//             itemBuilder: (context, index) {
//               String fieldKey = billGroupPrintField[index];
//               // bool isCheck = _printConfig[fieldKey] ?? false;
//               return buildFieldItem(fieldKey);
//             },
//           ),
//           buildItemContent(item.index == PrintConfigGroup.otherInfo.index),
//         ]);
//   }
//
//   ///构建打印字段项
//   Widget buildFieldItem(String fieldKey) {
//     bool isCheck = _printConfig[fieldKey] ?? false;
//     return GestureDetector(
//         onTap: () {
//           if (PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)) {
//             return;
//           }
//           _printConfig[fieldKey] = !isCheck;
//           widget.onEditPrintConfigCallBack(_printConfig);
//         },
//         child: Container(
//             decoration: BoxDecoration(
//               color: getFieldItemBackGroundColor(fieldKey, isCheck),
//               border:
//               Border.all(color: getFieldItemBorderColor(fieldKey, isCheck)),
//               borderRadius: const BorderRadius.all(Radius.circular(4)),
//             ),
//             child: Stack(clipBehavior: Clip.hardEdge, children: [
//               Container(
//                   constraints: BoxConstraints(minHeight: 48.h),
//                   alignment: Alignment.center,
//                   child: Text(
//                     PrintFieldConfig.printConfigNameStr[fieldKey] ?? "未配置",
//                     textAlign: TextAlign.center,
//                     overflow: TextOverflow.ellipsis,
//                     style: TextStyle(
//                         color: getFieldItemFontColor(fieldKey, isCheck),
//                         fontSize: 20.sp),
//                   )),
//               Visibility(
//                   visible: isCheck ||
//                       PrintFieldConfig.unCancelPrintConfig.contains(fieldKey),
//                   child: Positioned(
//                       right: -1,
//                       bottom: -1,
//                       child: IconFont(IconNames.xuanzhonggou,
//                           color: PrintFieldConfig.unCancelPrintConfig
//                               .contains(fieldKey)
//                               ? "#FFB9BAC0"
//                               : "#FF2769FF")))
//             ])));
//   }
//
//   //region 打印显示字段颜色配置
//
//   ///获取字段配置项背景色
//   Color getFieldItemBackGroundColor(String fieldKey, bool isCheck) {
//     //选择选中不能取消选择的打印字段（必选字段）
//     if (PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)) {
//       return AppColors.unEnableBackgroundBColor;
//     }
//     if (isCheck) {
//       return AppColors.selectbackGroundColor;
//     }
//     return Colors.white;
//   }
//
//   ///获取字段配置项边框色
//   Color getFieldItemBorderColor(String fieldKey, bool isCheck) {
//     //选择选中不能取消选择的打印字段（必选字段）
//     if (PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)) {
//       return AppColors.unEnableBorderColor;
//     }
//     if (isCheck) {
//       return AppColors.accentColor;
//     }
//     return AppColors.unEnableBorderColor;
//   }
//
//   ///获取字段配置项的边框颜色
//   Color getFieldItemFontColor(String fieldKey, bool isCheck) {
//     //选择选中不能取消选择的打印字段（必选字段）
//     if (PrintFieldConfig.unCancelPrintConfig.contains(fieldKey)) {
//       return AppColors.titleBoldTextColor;
//     }
//     if (isCheck) {
//       return AppColors.accentColor;
//     }
//     return AppColors.titleBoldTextColor;
//   }
// //endregion
// }
// //endregion
//
// //endregion
//
// //region 打印预览信息组件
// class _PrintViewPage extends StatefulWidget {
//   final ShopConfigInfo shopConfigInfo;
//   final Map<String, dynamic> printConfig;
//   final BillType billType;
//   final StoreInfo storeInfo;
//   final SystemConfigDto systemConfigDto;
//
//   const _PrintViewPage(
//       {Key? key,
//         required this.shopConfigInfo,
//         required this.printConfig,
//         required this.billType,
//         required this.storeInfo,
//         required this.systemConfigDto})
//       : super(key: key);
//
//   @override
//   State<StatefulWidget> createState() {
//     return _PrintViewPageState();
//   }
// }
//
// class _PrintViewPageState extends State<_PrintViewPage> {
//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//         child: HaloContainer(
//           direction: Axis.vertical,
//           borderShadow: [
//             BoxShadow(
//                 color: Colors.black.withOpacity(0.5),
//                 blurRadius: 5,
//                 spreadRadius: 0)
//           ],
//           mainAxisSize: MainAxisSize.max,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           margin: EdgeInsets.symmetric(horizontal: 120.w, vertical: 60.h),
//           padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
//           color: Colors.white,
//           children: [
//             _buildPrintTitle(),
//             _buildPrintDetail(widget.shopConfigInfo),
//             Container(
//               padding: EdgeInsets.symmetric(vertical: 6.h),
//               child: const DottedLine(),
//             ),
//             _buildPrintSumAndFeeInfo(),
//             _buildVipInfo(),
//             _buildReceiverInfo(),
//             _buildShopInfo(),
//             _buildPrintInfo()
//           ],
//         ));
//   }
//
//   String getPrintTitle() {
//     String printTitle = widget.storeInfo.fullname ?? "";
//     if ((widget.billType == BillType.SaleBill ||
//         widget.billType == BillType.SaleBackBill ||
//         widget.billType == BillType.SaleChangeBill) &&
//         !widget.printConfig[PrintFieldConfig.billName]) {
//       return printTitle;
//     }
//     return "$printTitle·${billTypeDataName[widget.billType] ?? ''}";
//   }
//
//   ///构建打印表头
//   Widget _buildPrintTitle() {
//     List<Widget> weights = [];
//
//     ///表头信息
//     _addDecorateInfo(weights,
//         fieldKey: PrintFieldConfig.decorateTop,
//         fieldKeyModel: PrintFieldConfig.decorateTopModel,
//         textFieldKey: PrintFieldConfig.decorateTopText,
//         imgFieldKey: PrintFieldConfig.decorateTopImgUrl);
//
//     weights.add(Center(
//         child: Text(
//           getPrintTitle(),
//           style: TextStyle(fontSize: 26.sp, color: AppColors.normalTextColor),
//         )));
//
//     ///门店表头信息
//     weights.add(_buildShopLogo());
//
//     ///收银员名称
//     _addRowWidget(
//         widgets: weights,
//         fieldKey: PrintFieldConfig.cashierName,
//         value: LoginCenter.getLoginUser().user);
//
//     ///收银员工号
//     _addRowWidget(
//         widgets: weights, value: "0001", fieldKey: PrintFieldConfig.cashierNo);
//
//     ///订单编号（全渠道订单使用）
//     weights.add(_buildBillNumber(PrintFieldConfig.billNumber));
//     weights.add(_buildBillNumber(PrintFieldConfig.orderNumber));
//     _addRowWidget(
//         widgets: weights, value: "xxx", fieldKey: PrintFieldConfig.kFullName);
//     _addRowWidget(
//         widgets: weights, value: "xxx", fieldKey: PrintFieldConfig.kFullName2);
//     _addRowWidget(
//         widgets: weights, value: "xxx", fieldKey: PrintFieldConfig.eTypeName);
//     _addRowWidget(
//         widgets: weights,
//         value: "xxx",
//         fieldKey: PrintFieldConfig.createETypeName);
//     _addRowWidget(
//         widgets: weights,
//         value: "2021-08-21 18:08:09",
//         fieldKey: PrintFieldConfig.billDate);
//     _addRowWidget(
//         widgets: weights, value: "张三", fieldKey: PrintFieldConfig.billGuide);
//     _addRowWidget(
//         widgets: weights, value: "xxxxx", fieldKey: PrintFieldConfig.billMemo);
//     _addRowWidget(
//         widgets: weights, value: "xxxxx", fieldKey: PrintFieldConfig.summary);
//     _addRowWidget(
//         widgets: weights, value: "xxxx", fieldKey: PrintFieldConfig.comment);
//
//     _addRowWidget(
//         widgets: weights,
//         value: "2021-08-21 18:08:09",
//         fieldKey: PrintFieldConfig.payDate);
//     _addRowWidget(
//         widgets: weights,
//         value: "xxxxx",
//         fieldKey: PrintFieldConfig.buyerMessage);
//
//     return buildSinglePartWidget(weights);
//   }
//
//   ///门店logo
//   Widget _buildShopLogo() {
//     return HaloContainer(
//       visible: widget.printConfig[PrintFieldConfig.shopLogo] ?? false,
//       mainAxisAlignment: PrintFieldConfig
//           .printLocationAlignment[widget.shopConfigInfo.shopLogoLocation] ??
//           MainAxisAlignment.start,
//       mainAxisSize: MainAxisSize.max,
//       children: [
//         buildImageView(widget.storeInfo.shopLogoUrl,
//             width: 120, localFile: false)
//       ],
//     );
//   }
//
//   ///构建单据编号
//   Widget _buildBillNumber(String fieldKey) {
//     return HaloContainer(
//       visible: widget.printConfig[fieldKey] ?? false,
//       mainAxisAlignment: MainAxisAlignment.center,
//       mainAxisSize: MainAxisSize.max,
//       children: [
//         HaloPosLabel(
//             "${PrintFieldConfig.printConfigNameStr[fieldKey]!}:WSD-20210821-001",
//             visible: widget.shopConfigInfo.billNumberModel ==
//                 BillNumberModeEnum.text.index,
//             textStyle: buildPrintViewStyle()),
//         Expanded(
//             child: HaloContainer(
//           visible: widget.shopConfigInfo.billNumberModel ==
//               BillNumberModeEnum.scan.index,
//           direction: Axis.vertical,
//           crossAxisAlignment: CrossAxisAlignment.center,
//           children: [
//             QrImageView(
//               data: "WSD-20210821-001",
//               version: QrVersions.auto,
//               size: 100.0,
//             ),
//             HaloPosLabel("WSD-20210821-001",
//                 textStyle: TextStyle(
//                     fontSize: 22.sp, color: AppColors.normalTextColor)),
//           ],
//         ))
//       ],
//     );
//   }
//
//   Widget _buildPrintDetail(ShopConfigInfo shopConfigInfo) {
//     ///换货单需要打印换入和换出，单独处理
//     if (widget.billType == BillType.SaleChangeBill) {
//       return _buildChangePrintContent(widget.shopConfigInfo, showPrice: true);
//     }
//
//     ///调拨单以及调拨订单不打印价格
//     return _buildPrintContent(widget.shopConfigInfo,
//         showPrice: widget.billType != BillType.TransferOrder &&
//             widget.billType != BillType.GoodsTrans);
//   }
//
//   ///构建打印表体
//   Widget _buildPrintContent(ShopConfigInfo shopConfigInfo,
//       {bool showPrice = true}) {
//     ///预览商品数据
//     List<Map<String, dynamic>> demoPTypeList = [
//       {
//         "name": "    名称",
//         "retailPrice":
//         PrintFieldConfig.printConfigNameStr[PrintFieldConfig.retailPrice],
//         "price": "现价",
//         "qty": "数量",
//         "total": "小计"
//       },
//       {
//         "name": "蜂蜜芥末脆皮鸡",
//         "retailPrice": "10.0",
//         "price": "10.0",
//         "qty": "1",
//         "total": "10.0",
//         "batchNo": "x001",
//         "protectDay": "2023-10-12",
//         "pTypeUserCode": "2323",
//         "skuBarCode": "3344",
//         "taxRate": "4",
//         "taxTotal": "0.38",
//       },
//       {
//         "name": "蜂蜜芥末脆皮鸡爽炸鸡腿堡末脆皮鸡爽炸鸡腿堡",
//         "retailPrice": "12.0",
//         "price": "12.0",
//         "qty": "2",
//         "total": "24",
//         "pTypeUserCode": "123",
//         "sn": "343;3434",
//         "standard": "规格",
//         "type": "型号",
//         "taxRate": "13",
//         "taxTotal": "1.5",
//       },
//       {
//         "name": "汉堡套餐",
//         "retailPrice": "30",
//         "price": "30",
//         "qty": "1",
//         "total": "30",
//         "pTypeUserCode": "4577",
//         "skuBarCode": "5566",
//         "isCombo": true
//       },
//       {
//         "name": "中杯可乐",
//         "retailPrice": "30",
//         "price": "30",
//         "qty": "1000",
//         "total": "30000",
//         "pTypeUserCode": "4577",
//         "skuBarCode": "5566",
//         "standard": "规格",
//       },
//     ];
//
//     return ListView.separated(
//         shrinkWrap: true,
//         physics: const NeverScrollableScrollPhysics(),
//         itemBuilder: (context, index) => _createBillDetailRow(
//             shopConfigInfo, demoPTypeList[index], index,
//             showPrice: showPrice),
//         separatorBuilder: (context, index) => SizedBox(height: 8.h),
//         itemCount: demoPTypeList.length);
//   }
//
//   ///构建打印表体
//   Widget _buildChangePrintContent(ShopConfigInfo shopConfigInfo,
//       {bool showPrice = true}) {
//     return HaloContainer(
//       direction: Axis.vertical,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         HaloLabel("换入明细："),
//         Container(
//           padding: EdgeInsets.symmetric(vertical: 6.h),
//           child: const DottedLine(),
//         ),
//         _buildPrintContent(shopConfigInfo, showPrice: showPrice),
//         Container(
//           margin: EdgeInsets.only(bottom: 16.h),
//           padding: EdgeInsets.symmetric(vertical: 6.h),
//           child: const DottedLine(),
//         ),
//         HaloLabel("换出明细："),
//         Container(
//           padding: EdgeInsets.symmetric(vertical: 6.h),
//           child: const DottedLine(),
//         ),
//         _buildPrintContent(shopConfigInfo, showPrice: showPrice),
//       ],
//     );
//   }
//
//   ///打印标题行
//   Widget _createBillDetailRow(
//       ShopConfigInfo shopConfigInfo, Map<String, dynamic> itemData, int index,
//       {bool showPrice = true}) {
//     ///标题行打印名称
//     if (index == 0) {
//       return buildBillDetailItem(
//           name: itemData["name"],
//           price: itemData["price"],
//           retailPrice: itemData["retailPrice"],
//           qty: itemData["qty"],
//           total: itemData["total"],
//           showPrice: showPrice);
//     } else {
//       ///打印单据明细内容
//       return buildContentItem(
//         shopConfigInfo,
//         itemData,
//         prefix: "${index.toString()}.",
//         showPrice: showPrice,
//       );
//     }
//   }
//
//   HaloContainer buildContentItem(
//       ShopConfigInfo shopConfigInfo, Map<String, dynamic> itemData,
//       {String? prefix, bool showPrice = true}) {
//     ///商品编码
//     String pTypeCode = "";
//
//     //商品编码 可展示条码或者商品编号
//     if (widget.printConfig[PrintFieldConfig.pTypeCode] ?? false) {
//       pTypeCode =
//       widget.shopConfigInfo.pTypeCodeMode == PTypeModeEnum.skuBarCode.index
//           ? itemData["skuBarCode"] ?? ""
//           : itemData["pTypeUserCode"] ?? "";
//     }
//
//     return HaloContainer(
//         mainAxisSize: MainAxisSize.max,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           ///隐藏也保留空间占位
//           Visibility(
//               visible: null != prefix,
//               maintainState: true,
//               child: Container(
//                   margin: EdgeInsets.only(top: 4.h),
//                   child: Text(
//                     "$prefix",
//                     style: buildPrintViewStyle(),
//                   ))),
//           Expanded(
//               child: HaloContainer(
//                   direction: Axis.vertical,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       buildPrintPtypeTitle(itemData),
//                       style: buildPrintViewStyle(),
//                     ),
//                     Visibility(
//                         visible: getBatchInfo(
//                             itemData["batchNo"], itemData["protectDay"])
//                             .isNotEmpty,
//                         child: Container(
//                             padding: EdgeInsets.only(bottom: 10.w),
//                             child: HaloPosLabel(
//                               "批次:${getBatchInfo(itemData["batchNo"], itemData["protectDay"])}",
//                               textStyle: buildPrintViewStyle(),
//                             ))),
//                     Visibility(
//                         visible: (widget.printConfig[PrintFieldConfig.pTypeSn] ??
//                             false) &&
//                             StringUtil.isNotEmpty(itemData["sn"]),
//                         child: Container(
//                             padding: EdgeInsets.only(bottom: 10.w),
//                             child: HaloPosLabel(
//                               "序列号:${itemData["sn"]}",
//                               textStyle: buildPrintViewStyle(),
//                             ))),
//                     Visibility(
//                         visible: (widget.systemConfigDto.sysGlobalEnabledTax &&
//                             widget.systemConfigDto.sysGlobalEnabledSaleTax) &&
//                             ((widget.printConfig[PrintFieldConfig.taxRate] ??
//                                 false) ||
//                                 (widget.printConfig[PrintFieldConfig.taxTotal] ??
//                                     false)),
//                         child: Container(
//                             padding: EdgeInsets.only(bottom: 10.w),
//                             child: HaloPosLabel(
//                               buildTaxInfo(itemData),
//                               textStyle: buildPrintViewStyle(),
//                             ))),
//
//                     ///套餐明细 仅打印明细数量
//                     buildBillDetailItem(
//                         name: pTypeCode,
//                         price: DecimalDisplayHelper.getPriceFixed(
//                             itemData["price"] ?? ""),
//                         retailPrice: DecimalDisplayHelper.getPriceFixed(
//                             itemData["retailPrice"] ?? ""),
//                         qty: itemData["qty"] ?? "",
//                         total: DecimalDisplayHelper.getTotalFixed(
//                             itemData["total"] ?? ""),
//                         showPrice: showPrice),
//                     buildComboDetail(
//                         shopConfigInfo, itemData["isCombo"] ?? false, showPrice)
//                   ]))
//         ]);
//   }
//
//   String buildPrintPtypeTitle(Map<String, dynamic> itemData) {
//     String name = "";
//     if ((widget.printConfig[PrintFieldConfig.pTypeStandard] ?? false) &&
//         StringUtil.isNotEmpty(itemData["standard"])) {
//       name = itemData["standard"];
//     }
//     if ((widget.printConfig[PrintFieldConfig.pTypeType] ?? false) &&
//         StringUtil.isNotEmpty(itemData["type"])) {
//       name = "$name:${itemData["type"]}";
//     }
//     name = StringUtil.trim(name, trimStr: ":");
//     if (StringUtil.isNotEmpty(name)) {
//       return "${itemData["name"]}($name)";
//     }
//     return itemData["name"] ?? "";
//   }
//
//   String buildTaxInfo(Map<String, dynamic> itemData) {
//     String taxMsg = "";
//     if (!(widget.systemConfigDto.sysGlobalEnabledTax &&
//         widget.systemConfigDto.sysGlobalEnabledSaleTax)) {
//       return taxMsg;
//     }
//     if (widget.printConfig[PrintFieldConfig.taxRate] ?? false) {
//       taxMsg = "税率:${itemData["taxRate"] ?? "0"}%；";
//     }
//     if (widget.printConfig[PrintFieldConfig.taxTotal] ?? false) {
//       taxMsg += "税额:￥${itemData["taxTotal"] ?? "0"};";
//     }
//     return taxMsg;
//   }
//
//   Widget buildBillDetailItem({
//     String name = "",
//     String retailPrice = "",
//     String price = "",
//     String qty = "",
//     String total = "",
//     bool showPrice = true,
//   }) {
//     List<Widget> contentWidgets = [];
//
//     contentWidgets.add(buildDetailItem(name, textAlign: TextAlign.left));
//     if (showPrice &&
//         (widget.printConfig[PrintFieldConfig.retailPrice] ?? false)) {
//       contentWidgets.add(buildDetailItem(retailPrice, defaultValue: "0"));
//     }
//     if (showPrice) {
//       contentWidgets.add(buildDetailItem(price, defaultValue: "0"));
//     }
//
//     contentWidgets.add(buildDetailItem(qty, defaultValue: "0"));
//     if (showPrice) {
//       contentWidgets.add(buildDetailItem(total, defaultValue: "0"));
//     }
//     return HaloContainer(
//         mainAxisSize: MainAxisSize.max, children: contentWidgets);
//   }
//
//   ///拼接批次号信息
//   String getBatchInfo(String? bathNo, String? productDay) {
//     String bathNoStr = "";
//     if ((bathNo?.isNotEmpty ?? false) &&
//         (widget.printConfig[PrintFieldConfig.pTypeBatchNo] ?? false)) {
//       bathNoStr += "${bathNo ?? ""};";
//     }
//     if ((productDay?.isNotEmpty ?? false) &&
//         (widget.printConfig[PrintFieldConfig.pTypeProductDay] ?? false)) {
//       bathNoStr += productDay ?? "";
//     }
//     return bathNoStr;
//   }
//
//   ///构建套餐明细
//   Widget buildComboDetail(
//       ShopConfigInfo shopConfigInfo, bool showComboDetail, bool isShowPrice) {
//     ///非套餐或者仅展示套餐名称
//     if (!showComboDetail ||
//         shopConfigInfo.comboMode == ComboModeEnum.name.index) {
//       return const SizedBox();
//     }
//
//     ///预览商品数据
//     List<Map<String, dynamic>> demoComboDetailList = [
//       {
//         "name": "可乐",
//         "qty": "1",
//         "batchNo": "x001",
//         "protectDay": "2023-10-12",
//         "pTypeUserCode": "2323",
//         "skuBarCode": "3344"
//       },
//       {"name": "汉堡", "qty": "2", "pTypeUserCode": "123"},
//       {
//         "name": "薯条",
//         "qty": "1",
//         "pTypeUserCode": "4577",
//         "skuBarCode": "5566",
//       },
//     ];
//
//     return HaloContainer(
//       direction: Axis.vertical,
//       children: [
//         buildContentItem(shopConfigInfo, demoComboDetailList[0],
//             prefix: "- ", showPrice: isShowPrice),
//         buildContentItem(shopConfigInfo, demoComboDetailList[1],
//             prefix: "- ", showPrice: isShowPrice),
//         buildContentItem(shopConfigInfo, demoComboDetailList[2],
//             prefix: "- ", showPrice: isShowPrice),
//       ],
//     );
//   }
//
//   ///通用打印字体样式
//   TextStyle buildPrintViewStyle() {
//     return TextStyle(color: AppColors.normalTextColor, fontSize: 22.sp);
//   }
//
//   ///构建明细行的item
//   Widget buildDetailItem(String? value,
//       {String defaultValue = "", int flex = 1, textAlign = TextAlign.center}) {
//     return Expanded(
//         flex: flex,
//         child: Text(
//           value ?? defaultValue,
//           textAlign: textAlign,
//           style: buildPrintViewStyle(),
//         ));
//   }
//
//   ///构建打印合计以及费用信息
//   Widget _buildPrintSumAndFeeInfo() {
//     List<Widget> weights = [];
//     _addRowWidget(
//         widgets: weights, value: "121.05", fieldKey: PrintFieldConfig.sumQty);
//     _addRowWidget(
//         widgets: weights, value: "4", fieldKey: PrintFieldConfig.tenderSumQty);
//     _addRowWidget(
//         widgets: weights, value: "30064", fieldKey: PrintFieldConfig.sumTotal);
//     _addRowWidget(
//       widgets: weights,
//       value: "1.88",
//       fieldKey: PrintFieldConfig.sumTaxTotal,
//     );
//     _addRowWidget(
//         widgets: weights, value: "121.05", fieldKey: PrintFieldConfig.backQty);
//     _addRowWidget(
//         widgets: weights,
//         value: "1000.55",
//         fieldKey: PrintFieldConfig.backTotal);
//
//     _addRowWidget(
//         widgets: weights,
//         value: "换入(4)；换出(4)",
//         fieldKey: PrintFieldConfig.changeQty);
//     _addRowWidget(
//         widgets: weights, value: "0", fieldKey: PrintFieldConfig.changeTotal);
//
//     _addRowWidget(
//         widgets: weights, value: "10.12", fieldKey: PrintFieldConfig.wTotal);
//     _addRowWidget(
//         widgets: weights, value: "3.55", fieldKey: PrintFieldConfig.pettyCash);
//     _addRowWidget(
//         widgets: weights, value: "现金(50)", fieldKey: PrintFieldConfig.payment);
//     _addRowWidget(
//         widgets: weights, value: "8", fieldKey: PrintFieldConfig.freightFee);
//     _addRowWidget(
//         widgets: weights, value: "12", fieldKey: PrintFieldConfig.buyerFreight);
//
//     _addRowWidget(
//         widgets: weights, value: "", fieldKey: PrintFieldConfig.signOut);
//     _addRowWidget(
//         widgets: weights, value: "", fieldKey: PrintFieldConfig.signIn);
//
//     return buildSinglePartWidget(weights);
//   }
//
//   Widget buildSinglePartWidget(List<Widget> weights) {
//     if (weights.isNotEmpty) {
//       weights.add(Container(
//         padding: EdgeInsets.symmetric(vertical: 6.h),
//         child: const DottedLine(),
//       ));
//     }
//     return HaloContainer(
//         direction: Axis.vertical,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: weights);
//   }
//
//   ///构建打印会员信息
//   Widget _buildVipInfo() {
//     List<Widget> weights = [];
//
//     _addRowWidget(
//         widgets: weights, value: "张三", fieldKey: PrintFieldConfig.vipName);
//     _addRowWidget(
//         widgets: weights,
//         value: "1380000000",
//         fieldKey: PrintFieldConfig.vipPhone);
//     _addRowWidget(
//         widgets: weights, value: "100", fieldKey: PrintFieldConfig.storeTotal);
//     _addRowWidget(
//         widgets: weights, value: "100", fieldKey: PrintFieldConfig.saleScore);
//     _addRowWidget(
//         widgets: weights, value: "1000", fieldKey: PrintFieldConfig.scoreTotal);
//     _addRowWidget(
//         widgets: weights, value: "8", fieldKey: PrintFieldConfig.freightFee);
//     _addRowWidget(
//         widgets: weights, value: "12", fieldKey: PrintFieldConfig.buyerFreight);
//     return buildSinglePartWidget(weights);
//   }
//
//   ///构建收货人信息
//   Widget _buildReceiverInfo() {
//     List<Widget> weights = [];
//
//     _addRowWidget(
//         widgets: weights, value: "张三", fieldKey: PrintFieldConfig.receiverName);
//     _addRowWidget(
//         widgets: weights,
//         value: "13888888888",
//         fieldKey: PrintFieldConfig.receiverMobile);
//     _addRowWidget(
//         widgets: weights,
//         value: "成都市武侯区xxx小区",
//         fieldKey: PrintFieldConfig.receiverAddress);
//     return buildSinglePartWidget(weights);
//   }
//
//   ///构建店铺信息
//   Widget _buildShopInfo() {
//     List<Widget> weights = [];
//     _addRowWidget(
//         widgets: weights,
//         value: "13809090900",
//         fieldKey: PrintFieldConfig.shopPhone);
//     _addRowWidget(
//         widgets: weights,
//         value: "成都市高新区天软件园件xxx",
//         fieldKey: PrintFieldConfig.address);
//     _addRowWidget(
//         widgets: weights,
//         value: "门店搞活动",
//         fieldKey: PrintFieldConfig.shopNotice);
//     _addShopScan(weights);
//
//     _addRowWidget(
//         widgets: weights,
//         value: "xxxxxx",
//         fieldKey: PrintFieldConfig.pickupCode);
//     _addInvoiceCode(weights);
//     _addDecorateInfo(weights,
//         fieldKey: PrintFieldConfig.decorateTail,
//         fieldKeyModel: PrintFieldConfig.decorateTailModel,
//         textFieldKey: PrintFieldConfig.decorateTailText,
//         imgFieldKey: PrintFieldConfig.decorateTailImgUrl);
//
//     return buildSinglePartWidget(weights);
//   }
//
//   ///构建门店二维码
//   void _addShopScan(List<Widget> list) {
//     if (!(widget.printConfig[PrintFieldConfig.shopScan] ?? false)) {
//       return;
//     }
//
//     list.add(HaloContainer(
//       mainAxisAlignment: MainAxisAlignment.center,
//       mainAxisSize: MainAxisSize.max,
//       children: [
//         buildImageView(widget.storeInfo.shopScanUrl, localFile: false),
//       ],
//     ));
//     list.add(HaloContainer(
//       mainAxisSize: MainAxisSize.max,
//       children: [
//         Expanded(
//             child: HaloPosLabel("${widget.storeInfo.shopScanMemo}",
//                 textAlign: TextAlign.center, textStyle: buildPrintViewStyle())),
//       ],
//     ));
//   }
//
//   ///构建票装饰
//   void _addDecorateInfo(
//       List<Widget> list, {
//         required String fieldKey,
//         required String fieldKeyModel,
//         required String textFieldKey,
//         required String imgFieldKey,
//       }) {
//     if (!(widget.printConfig[fieldKey] ?? false)) {
//       return;
//     }
//     list.add(HaloContainer(
//       mainAxisSize: MainAxisSize.max,
//       mainAxisAlignment: MainAxisAlignment.center,
//       visible: widget.printConfig[fieldKey] ?? false,
//       children: [
//         Visibility(
//             visible: (widget.printConfig[fieldKeyModel] ?? 0) ==
//                 PrintStyleModeEnum.text.index,
//             child: Expanded(
//                 child: HaloPosLabel(
//                   "${widget.printConfig[textFieldKey] ?? ""}",
//                   textAlign: TextAlign.center,
//                   textStyle: buildPrintViewStyle(),
//                   maxLines: 5,
//                 ))),
//         HaloContainer(
//           margin: EdgeInsets.only(top: 6.h),
//           visible: (widget.printConfig[fieldKeyModel] ?? 0) ==
//               PrintStyleModeEnum.image.index,
//           mainAxisAlignment: MainAxisAlignment.center,
//           mainAxisSize: MainAxisSize.max,
//           children: [buildImageView(widget.printConfig[imgFieldKey])],
//         )
//       ],
//     ));
//   }
//
//   ///构建打印信息
//   Widget _buildPrintInfo() {
//     List<Widget> weights = [];
//     _addRowWidget(
//         widgets: weights,
//         value: "2",
//         fieldKey: PrintFieldConfig.tenderPrintCount);
//     _addRowWidget(
//         widgets: weights,
//         name: "打印张数",
//         value: "3/2",
//         fieldKey: PrintFieldConfig.printCount);
//
//     _addRowWidget(
//         widgets: weights,
//         value: "2021-08-31 18:20:30",
//         fieldKey: PrintFieldConfig.printDate);
//
//     _addRowWidget(
//         widgets: weights,
//         value: "2021-08-31 18:20:30",
//         fieldKey: PrintFieldConfig.tenderPrintDate);
//
//     return HaloContainer(
//         direction: Axis.vertical,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: weights);
//   }
//
//   void _addRowWidget(
//       {required List<Widget> widgets,
//         required String fieldKey,
//         String? value,
//         String? name,
//         TextAlign align = TextAlign.start}) {
//     if (!widget.printConfig.containsKey(fieldKey) ||
//         !widget.printConfig[fieldKey]) {
//       return;
//     }
//
//     name ??= PrintFieldConfig.printConfigNameStr[fieldKey];
//     widgets.add(HaloContainer(
//       visible: widget.printConfig[fieldKey] ?? false,
//       mainAxisSize: MainAxisSize.max,
//       children: [
//         Expanded(
//             child: HaloPosLabel(
//                 "$name${StringUtil.isEmpty(value) ? "" : ":$value"}",
//                 textAlign: align,
//                 textStyle: buildPrintViewStyle())),
//       ],
//     ));
//   }
//
//   ///构建发票打印二维码
//   void _addInvoiceCode(List<Widget> widgets) {
//     if (!(widget.printConfig[PrintFieldConfig.invoiceCode] ?? false)) {
//       return;
//     }
//     widgets.add(Center(
//         child: HaloContainer(
//             direction: Axis.vertical,
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               QrImageView(
//                 data: buildInvoiceUrl(),
//                 version: QrVersions.auto,
//                 size: 180.0,
//               ),
//               HaloPosLabel(widget.shopConfigInfo.billInvoiceMemo,
//                   textStyle: buildPrintViewStyle()),
//             ])));
// //  ,
//   }
//
//   ///生成发票链接
//   String buildInvoiceUrl() {
//     LoginUserModel loginUserModel = LoginCenter.getLoginUser();
//     return "${loginUserModel.requestUrl}/sale/OpenInvoice.html?vchcode=123&profileid=343343&sign=234234234";
//   }
//
//   ///图片展示
//   Widget buildImageView(String? photoUrl,
//       {int width = 220, bool localFile = true}) {
//     //展示默认图片
//     if (StringUtil.isEmpty(photoUrl) || photoUrl!.contains("blank.gif")) {
//       return Image.asset(
//         "assets/images/no_photo.png",
//         width: width.w,
//       );
//     }
//     //本地文件
//     if (localFile) {
//       return Image.file(File(photoUrl!), width: width.w, fit: BoxFit.fitWidth);
//     }
//     //网络图片
//     return Image.network(photoUrl!, width: width.w, fit: BoxFit.fitWidth);
//   }
// }
//
// //endregion
//
// //region 店铺基本信息配置组件
// class _BaseShopPrintConfigPage extends StatefulWidget {
//   final ShopConfigInfo shopConfigInfo;
//   final Function(ShopConfigInfo shopConfigInfo) onEditChangeBack;
//
//   const _BaseShopPrintConfigPage(
//       {required this.shopConfigInfo, required this.onEditChangeBack});
//
//   @override
//   State<StatefulWidget> createState() {
//     return _BaseShopPrintConfigPageState();
//   }
// }
//
// class _BaseShopPrintConfigPageState extends State<_BaseShopPrintConfigPage> {
//   late ShopConfigInfo _shopConfigInfo;
//
//   ///二维码发票说明
//   TextEditingController textShopNoticeController = TextEditingController();
//
//   @override
//   void initState() {
//     super.initState();
//     textShopNoticeController.text = widget.shopConfigInfo.billInvoiceMemo;
//     _shopConfigInfo = widget.shopConfigInfo;
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//         child: HaloContainer(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 12.w),
//           direction: Axis.vertical,
//           children: [
//             HaloContainer(
//               mainAxisSize: MainAxisSize.max,
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 HaloPosLabel(
//                   "小票公用样式设置",
//                   textStyle: TextStyle(color: Colors.black, fontSize: 24.sp),
//                 )
//               ],
//             ),
//
//             // ///店铺logo
//             buildShopLogo(),
//
//             ///单据编号
//             buildBillNumber(),
//
//             ///商品编码
//             buildPTypeCode(),
//
//             ///套餐打印模式
//             buildComboMode(),
//
//             ///店铺公告
//             buildBillInvoiceMemo(),
//
//             SizedBox(
//               height: 16.h,
//             ),
//
//             ///打印张数
//             buildPrintCount(),
//
//             ///打印纸宽度
//             buildPrintWidth()
//           ],
//         ));
//   }
//
//   //region 构建店铺logo
//   Widget buildShopLogo() {
//     return buildItemRow("门店LOGO",
//         data: PrintFieldConfig.printLocationEnumName.values.toList(),
//         selectIndex: _shopConfigInfo.shopLogoLocation, onSelectClick: (index) {
//           _shopConfigInfo.shopLogoLocation = index;
//           widget.onEditChangeBack(_shopConfigInfo);
//         });
//   }
//
//   //endregion
//
//   //region 单据编号
//   Widget buildBillNumber() {
//     return buildItemRow("单据编号",
//         data: PrintFieldConfig.billNumberModeEnumName.values.toList(),
//         selectIndex: _shopConfigInfo.billNumberModel, onSelectClick: (index) {
//           _shopConfigInfo.billNumberModel = index;
//           widget.onEditChangeBack(_shopConfigInfo);
//         });
//   }
//
//   //endregion
//
//   //region 商品编码
//   Widget buildPTypeCode() {
//     return buildItemRow("商品编码",
//         data: PrintFieldConfig.pTypeModeEnumName.values.toList(),
//         selectIndex: _shopConfigInfo.pTypeCodeMode, onSelectClick: (index) {
//           _shopConfigInfo.pTypeCodeMode = index;
//           widget.onEditChangeBack(_shopConfigInfo);
//         });
//   }
//
//   //endregion
//
//   //region 套餐打印模式
//   Widget buildComboMode() {
//     return buildItemRow("套餐打印模式",
//         data: PrintFieldConfig.comboModeEnumName.values.toList(),
//         columnCount: 2,
//         selectIndex: _shopConfigInfo.comboMode, onSelectClick: (index) {
//           _shopConfigInfo.comboMode = index;
//           widget.onEditChangeBack(_shopConfigInfo);
//         });
//   }
//
//   //endregion
//
//   //region 发票二维码说明
//   Widget buildBillInvoiceMemo() {
//     return HaloContainer(
//         direction: Axis.vertical,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//             margin: EdgeInsets.symmetric(horizontal: 10.w),
//             child: HaloPosLabel(
//               "发票二维码说明",
//               textStyle: TextStyle(
//                   fontSize: 22.sp,
//                   color: Colors.black,
//                   fontWeight: FontWeight.w500),
//             ),
//           ),
//
//           HaloContainer(
//               padding: EdgeInsets.all(8.w),
//               mainAxisSize: MainAxisSize.max,
//               margin: EdgeInsets.symmetric(horizontal: 10.w),
//               border: Border.all(color: AppColors.borderColor),
//               borderRadius: const BorderRadius.all(Radius.circular(4)),
//               children: [
//                 Expanded(
//                     child: HaloTextField(
//                       showCounter: true,
//                       controller: textShopNoticeController,
//                       fontSize: 20.sp,
//                       maxLength: 20,
//                       hintText: "(选填)添加文本内容",
//                       contentPadding: 0,
//                       maxLines: 2,
//                       onChanged: (String? text) {
//                         widget.shopConfigInfo.billInvoiceMemo = text ?? "";
//                         widget.onEditChangeBack(_shopConfigInfo);
//                       },
//                     ))
//               ]),
//         ]);
//   }
//
//   //endregion
//
//   //region 打印张数
//   ///小票张数存实际张数，widget的index比实际值+1
//   Widget buildPrintCount() {
//     return buildItemRow("小票打印张数",
//         data: PrintFieldConfig.printCountName.values.toList(),
//         selectIndex: _shopConfigInfo.printCount - 1, onSelectClick: (index) {
//           _shopConfigInfo.printCount = index + 1;
//           widget.onEditChangeBack(_shopConfigInfo);
//         });
//   }
//
//   //endregion
//
//   //region 打印纸宽度
//   Widget buildPrintWidth() {
//     return buildItemRow("打印纸宽度",
//         data: PrintFieldConfig.printWidthName.values.toList(),
//         selectIndex: _shopConfigInfo.printWidth, onSelectClick: (index) {
//           _shopConfigInfo.printWidth = index;
//           widget.onEditChangeBack(_shopConfigInfo);
//         });
//   }
//
//   //endregion
//
//   buildItemRow(String title,
//       {required List<String> data,
//         int selectIndex = 0,
//         int columnCount = 3,
//         required Function(int index) onSelectClick}) {
//     return HaloContainer(
//         direction: Axis.vertical,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         margin: EdgeInsets.only(bottom: 20.h),
//         children: [
//           Container(
//             margin: EdgeInsets.symmetric(horizontal: 10.w),
//             child: HaloPosLabel(
//               title,
//               textStyle: TextStyle(
//                   fontSize: 22.sp,
//                   color: Colors.black,
//                   fontWeight: FontWeight.w500),
//             ),
//           ),
//           SizedBox(
//             height: 4.h,
//           ),
//           GridListView(
//               dataCount: data.length,
//               columnCount: columnCount,
//               itemBuilder: (context, index) {
//                 return buildFieldItem(
//                     content: data[index],
//                     selectIndex: selectIndex,
//                     index: index,
//                     onSelected: onSelectClick);
//               })
//         ]);
//   }
//
//   ///构建配置item
//   Widget buildFieldItem(
//       {required String content,
//         required int selectIndex,
//         required int index,
//         required Function(int selectIndex) onSelected}) {
//     return GestureDetector(
//         onTap: () {
//           onSelected(index);
//         },
//         child: Container(
//           alignment: Alignment.center,
//           constraints: BoxConstraints(minHeight: 48.h),
//           decoration: BoxDecoration(
//             color: selectIndex == index
//                 ? AppColors.selectbackGroundColor
//                 : Colors.white,
//             border: Border.all(
//                 color: selectIndex == index
//                     ? AppColors.accentColor
//                     : AppColors.unEnableBorderColor),
//             borderRadius: const BorderRadius.all(Radius.circular(4)),
//           ),
//           child: Text(
//             content,
//             maxLines: 1,
//             textAlign: TextAlign.center,
//             overflow: TextOverflow.ellipsis,
//             style: TextStyle(
//                 color: selectIndex == index
//                     ? AppColors.accentColor
//                     : AppColors.normalTextColor,
//                 fontSize: 20.sp),
//           ),
//         ));
//   }
// }
//
// //endregion
//
// //region 网格列表
// class GridListView extends StatefulWidget {
//   final int dataCount;
//   final int columnCount;
//   final int columnSplitWidth; //item的行空隙
//   final IndexedWidgetBuilder itemBuilder;
//
//   const GridListView(
//       {Key? key,
//         required this.dataCount,
//         required this.columnCount,
//         required this.itemBuilder,
//         this.columnSplitWidth = 10})
//       : super(key: key);
//
//   @override
//   State<StatefulWidget> createState() {
//     return GridListViewsState();
//   }
// }
//
// class GridListViewsState extends State<GridListView> {
//   @override
//   Widget build(BuildContext context) {
//     return ListView.separated(
//         physics: const NeverScrollableScrollPhysics(),
//         shrinkWrap: true,
//         itemBuilder: (context1, index) => buildFieldShowConfig(index),
//         separatorBuilder: (context, index) => SizedBox(height: 16.h),
//         itemCount: (widget.dataCount / widget.columnCount).ceil());
//   }
//
//   ///构建行
//   Widget buildFieldShowConfig(int index) {
//     List<Widget> items = [];
//     for (int i = 0; i < widget.columnCount; i++) {
//       if (index * widget.columnCount + i > widget.dataCount - 1) {
//         items.add(const Expanded(child: SizedBox()));
//       } else {
//         items.add(Expanded(
//             child:
//             widget.itemBuilder(context, index * widget.columnCount + i)));
//
//         if (i != widget.columnCount - 1) {
//           items.add(SizedBox(
//             width: widget.columnSplitWidth.w,
//           ));
//         }
//       }
//     }
//     return HaloContainer(
//         margin: EdgeInsets.symmetric(horizontal: 16.w),
//         mainAxisSize: MainAxisSize.max,
//         children: items);
//   }
// }
//
// //endregion
