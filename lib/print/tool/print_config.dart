import 'package:bluetooth_print/enum/print_align_enum.dart';
import 'package:flutter/material.dart';

import '../../common/tool/sp_tool.dart';
import '../../entity/system/system_config_dto.dart';
import '../../enum/bill_type.dart';
import '../../enum/setting/print_width_type.dart';

///
///@ClassName: print_config
///@Description:  打印配置信息
///@Author: tanglan
///@Date: 2024/9/12

// region 打印字段

class PrintFieldConfig {
  //region 基础信息
  static const String shopLogo = "shopLogo"; //店铺Logo
  static const String shopName = "shopName"; //店铺名称
  static const String billName = "billName"; //单据名称

  static const String cashierName = "cashierName"; //收银员姓名
  static const String cashierNo = "cashierNo"; //收银员工号
  static const String cashNo = "cashNo"; //收银机

  static const String billNumber = "billNumber"; //单据编号
  static const String billDate = "bill_date"; //单据日期
  static const String eTypeName = "etypeName"; //经手人
  static const String createETypeName = "createEtypeName"; //制单人
  static const String billMemo = "memo"; //单据手工备注
  static const String summary = "summary"; //摘要
  static const String comment = "comment"; //附加说明

  static const String orderNumber = "orderNumber"; //订单编号
  static const String tenderPrintDate = "TENDER_PRINT_DATE"; //调拨单打印日期
  static const String billGuide = "guide"; //业务员

  static const String kFullName = "kfullname"; //调出仓库
  static const String kFullName2 = "kfullname2"; //调入仓库
  static const String signOut = "SIGN_OUT"; //调出店签名
  static const String signIn = "SIGN_IN"; //调入店签名

  static const String payDate = "PAY_DATE"; //付款时间
  static const String printDate = "printDate"; //打印日期
  static const String changeStartTime = "changeStartTime"; //接班时间
  static const String changeEndTime = "changeEndTime"; //交接班时间
  static const String chargeTime = "chargeTime"; //充值时间
  static const String tenderPrintCount = "TENDER_PRINT_COUNT"; //打印次数
  static const String printCount = "print_count"; //打印张数
  static const String buyerMessage = "BUYER_MESSAGE"; //买家备注
  static const String buyerFreight = "BUYER_FREIGHT"; //买家运费
  static const String pickupCode = "PICKUP_CODE"; //提货码
  //endregion

  //region 商品信息
  static const String pTypeSn = "ptypeSn"; //商品序列号
  static const String pTypeBatchNo = "ptypeBatchNo"; //商品批次号
  static const String pTypeProductDay = "ptypeProductDay"; //商品保质期
  static const String pFullName = "pfullname"; //商品名称
  static const String barcode = "BARCODE"; //条码
  static const String qty = "QTY"; //数量
  static const String brand = "brand"; //品牌
  static const String retailPrice = "RETAIL_PRICE"; //零售价
  static const String price = "PRICE"; //现价
  static const String total = "TOTAL"; //小计
  static const String userCode = "usercode"; //商品编号
  static const String pTypeCode = "pTypeCode"; //商品编码
  static const String pTypeStandard = "pTypeStandard"; //规格
  static const String pTypeType = "pTypeType"; //型号

  static const String tenderSumQty = "TENDER_SUM_QTY"; //调拨单商品合计
  static const String taxRate = "taxRate"; //税率
  static const String taxTotal = "taxTotal"; //税额

  //endregion

  //region 支付信息
  static const String freightFee = "freightee"; //物流费用
  static const String sumQty = "sumQty"; //合计数量
  static const String sumTotal = "sumTotal"; //合计金额
  static const String backQty = "backQty"; //合计退货数量
  static const String sumTaxTotal = "sumTaxTotal"; //合计税额
  static const String backTotal = "backTotal"; //退货金额
  static const String changeQty = "changeQty"; //合计换货数量
  static const String changeTotal = "changeTotal"; //换货金额
  static const String wTotal = "wotal"; //优惠金额
  static const String pettyCash = "pettyCash"; //找零金额
  static const String payment = "payment"; //支付方式
  static const String paymentDetail = "paymentDetail"; //支付明细
  //endregion

  //region 会员信息
  static const String vipName = "vipName"; //会员姓名
  static const String vipPhone = "vipPhone"; //会员手机号
  static const String storeTotal = "store_total"; //储值余额
  static const String saleScore = "sale_score"; //本次积分
  static const String scoreTotal = "score_total"; //积分余额
  static const String chargeTotal = "chargeTotal"; //充值金额
  static const String giftStoreTotal = "giftStoreTotal"; //赠送金额
  static const String storeBeforeCharge = "storeBeforeCharge"; //充值前余额
  static const String storeAfterCharge = "storeAfterCharge"; //充值后余额
  static const String payTotal = "payTotal"; //支付金额
  //endregion

  //region 收货人信息
  static const String receiverName = "RECIVER_NAME"; //收货人姓名
  static const String receiverMobile = "RECIVER_MOBILE"; //收货人手机
  static const String receiverAddress = "RECIVER_ADDRESS"; //收货人地址
  //endregion

  //region 其他信息
  static const String address = "address"; //店铺地址
  static const String shopPhone = "shopPhone"; //店铺电话
  static const String invoiceCode = "InvoiceCode"; //开票二维码
  static const String decorateTop = "decorateTop"; //票头装饰
  // static const String decorateTopModel = "decorateTopModel"; //票头装饰
  // static const String decorateTopImgUrl = "decorateTopImgUrl"; //票头装饰图片
  // static const String decorateTopText = "decorateTopText"; //票头装饰文字
  static const String decorateTail = "decorateTail"; //票尾装饰
  // static const String decorateTailImgUrl = "decorateTailImgUrl"; //票尾装饰图片
  // static const String decorateTailModel = "decorateTailModel"; //票头装饰
  // static const String decorateTailText = "decorateTailText"; //票尾装饰文字
  static const String shopNotice = "shopNotice"; //店铺公告
  static const String shopScan = "shopScan"; //店铺二维码

  //endregion

  // static const String BILL_NUMBER_QRCODE = "bill_number_qrcode"; //单据编号打印为二维码

  // ///商品编号
  // static const String pTypUserCode = "pTypUserCode";
  //
  // ///条码
  // static const String pTypeSkuCode = "pTypeSkuCode";

  static const Map<PrintConfigGroup, List<String>> printConfigGroupList = {
    PrintConfigGroup.baseInfo: [
      shopLogo, //店铺Logo
      shopName, //店铺名称
      billName, //单据名称
      cashierName, //收银员姓名
      cashierNo, //收银员工号
      cashNo, //收银机
      billNumber, //单据编号
      billDate, //单据日期
      eTypeName, //经手人
      createETypeName, //制单人
      billMemo, //单据手工备注
      summary, //摘要
      comment, //附加说明

      orderNumber, //订单编号
      tenderPrintDate, //调拨单打印日期
      billGuide, //业务员

      kFullName, //调出仓库
      kFullName2, //调入仓库
      signOut, //调出店签名
      signIn, //调入店签名

      payDate, //付款时间
      printDate, //打印日期
      changeStartTime, //交接班开始时间
      changeEndTime, //交接班结束时间
      chargeTime, //充值时间
      tenderPrintCount, //打印次数
      printCount, //打印张数
      buyerMessage, //买家备注
      buyerFreight, //买家运费
      pickupCode, //提货码
    ],
    PrintConfigGroup.pTypeInfo: [
      pTypeSn, //商品序列号
      pTypeBatchNo, //商品批次号
      pTypeProductDay, //商品保质期
      pFullName, //商品名称
      barcode, //条码
      qty, //数量
      retailPrice, //零售价
      price, //现价
      total, //小计
      userCode, //商品编号
      pTypeCode, //商品编码

      pTypeStandard, //商品规格
      pTypeType, //商品型号
      brand, //品牌
      taxRate,
      taxTotal,
    ],
    PrintConfigGroup.paymentInfo: [
      sumQty, //合计数量
      tenderSumQty, //调拨单商品合计
      backQty, //合计退货数量
      sumTotal, //合计金额
      sumTaxTotal, //合计税额
      backTotal, //退货金额
      changeQty, //换货数量
      changeTotal, //换货金额
      freightFee, //物流费用
      wTotal, //优惠金额
      pettyCash, //找零金额
      payment, //支付方式
      paymentDetail, //支付明细
    ],
    PrintConfigGroup.vipInfo: [
      vipName, //会员姓名
      vipPhone, //会员手机号
      storeTotal, //储值余额
      saleScore, //本次积分
      scoreTotal, //积分余额
      chargeTotal, //充值金额
      giftStoreTotal, //赠送金额
      storeBeforeCharge, //充值前余额
      storeAfterCharge, //充值后余额
      payTotal, //支付金额
    ],
    PrintConfigGroup.otherInfo: [
      address, //店铺地址
      shopPhone, //店铺电话
      invoiceCode, //开票二维码
      decorateTop, //票头装饰
      decorateTail, //票尾装饰
      shopNotice, //店铺公告
      shopScan, //店铺二维码
    ],
    PrintConfigGroup.receiverInfo: [
      receiverName, //收货人姓名
      receiverMobile, //收货人手机
      receiverAddress, //收货人地址
    ],
  };

  ///打印分组名称
  static const Map<PrintConfigGroup, String> printConfigGroupName = {
    PrintConfigGroup.baseInfo: "基本信息",
    PrintConfigGroup.pTypeInfo: "商品信息",
    PrintConfigGroup.paymentInfo: "支付信息",
    PrintConfigGroup.vipInfo: "会员信息",
    PrintConfigGroup.otherInfo: "其他信息",
    PrintConfigGroup.receiverInfo: "收货人信息",
  };

  ///全部单据打印常量配置
  static Map<BillType, List<String>> printConfigName({bool? isTaxEnable}) {
    return {
      BillType.SaleBill: printConfigSaleName(isTaxEnable: isTaxEnable),
      BillType.SaleBackBill: printConfigSaleBackName(isTaxEnable: isTaxEnable),
      BillType.SaleChangeBill: printConfigSaleChangeName(
        isTaxEnable: isTaxEnable,
      ),
      BillType.GoodsTrans: printConfigGoodsTransName,
      BillType.TransferOrder: printConfigTransferOrderName,
      BillType.ChannelBill: printConfigBillChannelName,
    };
  }

  ///获取指定单据单据打印常量配置
  static List<String> getBillPrintConfigName({required BillType? billType}) {
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
    bool isTaxEnable =
        systemConfigDto.sysGlobalEnabledTax &&
        systemConfigDto.sysGlobalEnabledSaleTax;
    return printConfigName(isTaxEnable: isTaxEnable)[billType] ?? [];
  }

  ///打印位置名称
  static const Map<PrintLocationEnum, String> printLocationEnumName = {
    PrintLocationEnum.left: "居左",
    PrintLocationEnum.middle: "居中",
    PrintLocationEnum.right: "居右",
  };

  ///打印位置名称
  static const Map<int, MainAxisAlignment> printLocationAlignment = {
    0: MainAxisAlignment.start,
    1: MainAxisAlignment.center,
    2: MainAxisAlignment.end,
  };

  ///打印时位置名称
  static const Map<int, PrintAlignEnum> printLocationPrintAlign = {
    1: PrintAlignEnum.CENTER,
    0: PrintAlignEnum.LEFT,
    2: PrintAlignEnum.RIGHT,
  };

  ///单据编号打印名称
  static const Map<BillNumberModeEnum, String> billNumberModeEnumName = {
    BillNumberModeEnum.text: "文本",
    BillNumberModeEnum.scan: "二维码",
  };

  ///打印格式
  static const Map<PrintStyleModeEnum, String> printStyleModeEnumName = {
    PrintStyleModeEnum.text: "文本",
    PrintStyleModeEnum.image: "图片",
  };

  ///商品编码打印名称
  static const Map<PTypeModeEnum, String> pTypeModeEnumName = {
    PTypeModeEnum.pTypeUserCode: "商品编号",
    PTypeModeEnum.skuBarCode: "条码",
  };

  ///套餐打印模式名称
  static const Map<ComboModeEnum, String> comboModeEnumName = {
    ComboModeEnum.name: "仅套餐名",
    ComboModeEnum.content: "套餐名+明细",
  };

  ///选择模版
  static const Map<int, String> printSelected = {0: "否", 1: "是"};

  ///打印张数名称
  static const Map<int, String> printCountName = {1: "1张", 2: "2张", 3: "3张"};

  ///打印纸张宽度名称
  static const Map<PrintWidth, String> printWidthName = {
    PrintWidth.w_58mm: "58mm",
    PrintWidth.w_80mm: "80mm",
  };

  //region 单据打印对应名称
  static const Map<String, String> printConfigNameStr = {
    shopName: "门店名称",
    shopLogo: "门店Logo",
    billName: "单据名称",
    cashierName: "收银员姓名",
    cashierNo: "收银员工号",
    billNumber: "单据编号",
    billDate: "单据日期",
    // BILL_NUMBER_QRCODE: "打印为二维码",
    billGuide: "业务员",
    billMemo: "备注",
    sumQty: "合计数量",
    sumTotal: "合计金额",
    sumTaxTotal: "合计税额",
    backQty: "退货数量",
    backTotal: "退货金额",
    changeQty: "换货数量",
    changeTotal: "换货金额",
    summary: "摘要",
    comment: "附加说明",
    wTotal: "优惠金额",
    pettyCash: "找零金额",
    payment: "支付方式",
    storeTotal: "储值余额",
    freightFee: "物流费用",
    printDate: "打印日期",
    printCount: "打印张数",
    cashNo: "收银机",
    changeStartTime: "交接班开始时间",
    changeEndTime: "交接班结束时间",
    chargeTime: "充值时间",

    userCode: "商品编号",
    barcode: "条码",
    pFullName: "商品名称",
    eTypeName: "经手人",
    createETypeName: "制单人",
    kFullName: "调出仓库",
    kFullName2: "调入仓库",
    signOut: "调出店签名",
    signIn: "调入店签名",
    tenderSumQty: "合计数量",
    tenderPrintDate: "打印日期",
    tenderPrintCount: "打印次数",
    qty: "数量",
    retailPrice: "零售价",
    saleScore: "本次积分",
    scoreTotal: "积分余额",
    orderNumber: "订单编号",
    payDate: "付款时间",
    buyerMessage: "买家备注",
    buyerFreight: "买家运费",
    pickupCode: "提货码",
    receiverName: "收货人姓名",
    receiverMobile: "收货人手机",
    receiverAddress: "收货人地址",
    pTypeSn: "商品序列号",
    pTypeBatchNo: "商品批次号",
    pTypeProductDay: "商品保质期",
    invoiceCode: "开票二维码",
    pTypeCode: "商品编码",
    taxRate: "税率",
    taxTotal: "税额",
    pTypeStandard: "规格",
    pTypeType: "型号",
    brand: "品牌",
    // pTypUserCode: "商品编号",
    // pTypeSkuCode: "条码",
    vipName: "会员姓名",
    vipPhone: "会员手机号",
    chargeTotal: "充值金额",
    giftStoreTotal: "赠送金额",
    storeBeforeCharge: "充值前余额",
    storeAfterCharge: "充值后余额",
    payTotal: "支付金额",

    shopPhone: "门店电话",
    shopNotice: "门店公告",
    address: "门店地址",
    shopScan: "门店二维码",
    decorateTail: "票尾装饰",
    decorateTop: "票头装饰",
  };

  //endregion

  //region 出库单打印配置项
  static List<String> printConfigSaleName({bool? isTaxEnable}) {
    List<String> printConfig = [
      shopName,
      billName,
      shopLogo,
      cashierName,
      cashierNo,
      billNumber,
      billDate,
      pFullName,
      retailPrice,
      pTypeCode,
      billGuide,
      billMemo,
      sumTotal,
      sumQty,
      // SUMMARY,
      // COMMENT,
      wTotal,
      pettyCash,
      payment,
      vipName,
      vipPhone,
      storeTotal,
      saleScore,
      scoreTotal,
      pTypeBatchNo,
      pTypeProductDay,
      pTypeSn,
      pTypeType,
      pTypeStandard,
      brand,
      // FREIGHTFEE,
      printDate,
      printCount,

      shopPhone,
      address,
      shopNotice,
      shopScan,
      invoiceCode,
      decorateTop,
      decorateTail,
    ];
    if (null != isTaxEnable && isTaxEnable) {
      printConfig.addAll([taxRate, taxTotal, sumTaxTotal]);
    }
    return printConfig;
  }

  //endregion

  ///支持配置打印样式的单据
  static Map<BillType, String> printBillList = {
    BillType.SaleBill: "销售单小票样式", //销售单
    BillType.SaleBackBill: "退货单小票样式", //销售退货
    BillType.SaleChangeBill: "换货单小票样式", //销售退货
    BillType.GoodsTrans: "调拨单小票样式", //调拨单 --直接过账
    BillType.TransferOrder: "调拨订单小票样式", //调拨订单
    BillType.ChannelBill: "全渠道订单小票样式", //全渠道订单
  };

  //region 退货单打印配置项
  static List<String> printConfigSaleBackName({bool? isTaxEnable}) {
    List<String> printConfig = [
      shopName,
      billName,
      shopLogo,
      cashierName,
      cashierNo,
      billNumber,
      billDate,
      pFullName,
      retailPrice,
      pTypeCode,
      billGuide,
      billMemo,
      backQty,
      backTotal,
      summary,
      comment,
      wTotal,
      pettyCash,
      payment,
      vipName,
      vipPhone,

      pTypeBatchNo,
      pTypeProductDay,
      pTypeSn,
      pTypeType, brand,
      pTypeStandard,
      // FREIGHTFEE,
      printDate,
      printCount,

      shopPhone,
      address,
      shopNotice,
      shopScan,
      decorateTop,
      decorateTail,
    ];
    if (null != isTaxEnable && isTaxEnable) {
      printConfig.addAll([taxRate, taxTotal]);
    }
    return printConfig;
  }

  //endregion

  //region 换货单打印配置项
  static List<String> printConfigSaleChangeName({bool? isTaxEnable}) {
    List<String> printConfig = [
      shopName,
      billName,
      shopLogo,
      cashierName,
      cashierNo,
      billNumber,
      billDate,
      pFullName,
      retailPrice,
      pTypeCode,
      billGuide,
      billMemo,
      changeQty,
      changeTotal,
      wTotal,
      pettyCash,
      payment,
      vipName,
      vipPhone,

      pTypeBatchNo,
      pTypeProductDay,
      pTypeSn,
      pTypeType,
      pTypeStandard, brand,
      // FREIGHTFEE,
      printDate,
      printCount,

      shopPhone,
      address,
      shopNotice,
      shopScan,
      decorateTop,
      decorateTail,
    ];
    if (null != isTaxEnable && isTaxEnable) {
      printConfig.addAll([taxRate, taxTotal]);
    }
    return printConfig;
  }

  //endregion

  //region 调拨单打印配置项
  static const List<String> printConfigGoodsTransName = [
    shopName,
    billName,
    billNumber,
    kFullName,
    kFullName2,
    eTypeName,
    createETypeName,
    billDate,
    pFullName,
    pTypeCode,
    brand,
    tenderPrintDate,
    tenderSumQty,
    signOut,
    signIn,
    qty,
    tenderPrintCount,
    billMemo,
  ];

  //endregion

  //region 调拨订单打印配置项
  static const List<String> printConfigTransferOrderName = [
    shopName,
    billName,
    billNumber,
    kFullName,
    kFullName2,
    eTypeName,
    createETypeName,
    billDate,
    pTypeCode,
    brand,
    pFullName,
    tenderPrintDate,
    tenderSumQty,
    signOut,
    signIn,
    qty,
    tenderPrintCount,
  ];

  //endregion

  //region 全渠道订单打印配置项
  static const List<String> printConfigBillChannelName = [
    shopName,
    billName,
    cashierName,
    cashierNo,
    orderNumber,
    payDate,
    buyerMessage,
    sumQty,
    sumTotal,
    printDate,
    address,
    pickupCode,
    receiverName,
    receiverMobile,
    receiverAddress,
  ];

  //endregion

  /// 固定配置不能取消
  static const List<String> unCancelPrintConfig = [shopName, pFullName, qty];

  ///默认选选中配置
  static const List<String> defaultPrintConfig = [
    billName,
    printCount,
    kFullName,
    kFullName2,
    eTypeName,
    billDate,
    tenderSumQty,
    tenderPrintDate,
  ];

  ///不允许配置自定义名称的字段
  static const List<String> unAllowCustomNameConfig = [
    shopName,
    shopLogo,
    pTypeStandard,
    pTypeType,
    pTypeCode,
    brand,
    pTypeSn,
    pTypeBatchNo,
    shopScan,
    pTypeProductDay,
    invoiceCode,
    decorateTail,
    decorateTop,
    printCount,
  ];

  ///允许配置打印位置的字段
  static const List<String> allowPrintLocationConfig = [shopLogo];
}

//endregion

//region 门店级别配置打印信息
class ShopConfigInfo {
  ///单据编号显示格式 显示文本或者二维码（0=文本 1=二维码）
  int billNumberModel = BillNumberModeEnum.text.index;

  ///商品编码格式   0=商品编码  1 = 条码
  int pTypeCodeMode = PTypeModeEnum.pTypeUserCode.index;

  ///发票二维码说明
  String billInvoiceMemo = "";

  ///店铺公告位置（0=居左 1=居中 2=居右）
  int shopLocation = PrintLocationEnum.left.index;

  ///店铺Logo公告位置（0=居左 1=居中 2=居右）
  int shopLogoLocation = PrintLocationEnum.left.index;

  ///打印张数
  int printCount = 1;

  ///打印纸宽度
  int printWidth = PrintWidth.w_58mm.index;

  /// 套餐模式
  int comboMode = ComboModeEnum.name.index;

  static ShopConfigInfo fromMap(Map<String, dynamic> map) {
    ShopConfigInfo shopConfigInfo = ShopConfigInfo();
    shopConfigInfo.billNumberModel =
        map["billNumberModel"] ?? BillNumberModeEnum.text.index;
    shopConfigInfo.pTypeCodeMode =
        map['pTypeCodeMode'] ?? PTypeModeEnum.pTypeUserCode.index;
    shopConfigInfo.billInvoiceMemo = map['billInvoiceMemo'] ?? "";
    shopConfigInfo.shopLocation =
        map['shopLocation'] ?? PrintLocationEnum.left.index;
    shopConfigInfo.shopLogoLocation = map["shopLogoLocation"];
    shopConfigInfo.printCount = map["printCount"];
    shopConfigInfo.printWidth = map["printWidth"];
    shopConfigInfo.comboMode = map["comboMode"];
    return shopConfigInfo;
  }

  Map toJson() => {
    "shopLogoLocation": shopLogoLocation,
    "billNumberModel": billNumberModel,
    "pTypeCodeMode": pTypeCodeMode,
    "billInvoiceMemo": billInvoiceMemo,
    "shopLocation": shopLocation,
    "printCount": printCount,
    "printWidth": printWidth,
    "comboMode": comboMode,
  };
}

enum PTypeModeEnum {
  ///商品条码
  pTypeUserCode,

  ///条码
  skuBarCode,
}

enum BillNumberModeEnum {
  ///文本
  text,

  ///二维码
  scan,
}

enum PrintStyleModeEnum {
  ///文本
  text,

  ///图片
  image,
}

enum PrintLocationEnum {
  ///居左
  left,

  ///居中
  middle,

  ///居右
  right,
}

enum ComboModeEnum {
  ///仅套餐名称
  name,

  ///套餐名称+明细
  content,
}

///打印字段分组
enum PrintConfigGroup {
  ///基础信息
  baseInfo,

  ///商品信息
  pTypeInfo,

  ///支付信息
  paymentInfo,

  ///会员信息
  vipInfo,

  ///其他信息
  otherInfo,

  ///收货人信息
  receiverInfo,
}

//endregion
