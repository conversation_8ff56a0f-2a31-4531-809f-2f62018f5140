// import 'package:image_v3/image_v3.dart' as img;
import 'package:image/image.dart' as img;

/// Draw the image [src] onto the image [dst].
///
/// In other words, drawImage will take an rectangular area from src of
/// width [src_w] and height [src_h] at position ([src_x],[src_y]) and place it
/// in a rectangular area of [dst] of width [dst_w] and height [dst_h] at
/// position ([dst_x],[dst_y]).
///
/// If the source and destination coordinates and width and heights differ,
/// appropriate stretching or shrinking of the image fragment will be performed.
/// The coordinates refer to the upper left corner. This function can be used to
/// copy regions within the same image (if [dst] is the same as [src])
/// but if the regions overlap the results will be unpredictable.
img.Image drawImage(img.Image dst, img.Image src,
    {int? dstX,
      int? dstY,
      int? dstW,
      int? dstH,
      int? srcX,
      int? srcY,
      int? srcW,
      int? srcH,
      bool blend = true}) {
  dstX ??= 0;
  dstY ??= 0;
  srcX ??= 0;
  srcY ??= 0;
  srcW ??= src.width;
  srcH ??= src.height;
  dstW ??= (dst.width < src.width) ? dstW = dst.width : src.width;
  dstH ??= (dst.height < src.height) ? dst.height : src.height;

  if (blend) {
    for (var y = 0; y < dstH; ++y) {
      for (var x = 0; x < dstW; ++x) {
        final stepX = (x * (srcW / dstW)).toInt();
        final stepY = (y * (srcH / dstH)).toInt();
        final srcPixel = src.getPixel(srcX + stepX, srcY + stepY);
        img.drawPixel(dst, dstX + x, dstY + y, srcPixel);
      }
    }
  } else {
    for (var y = 0; y < dstH; ++y) {
      for (var x = 0; x < dstW; ++x) {
        final stepX = (x * (srcW / dstW)).toInt();
        final stepY = (y * (srcH / dstH)).toInt();
        final srcPixel = src.getPixel(srcX + stepX, srcY + stepY);
        dst.setPixel(dstX + x, dstY + y, srcPixel);
      }
    }
  }

  return dst;
}

List<int> draw2PxPoint(img.Image bmp, String align) {
  List<int> tmp = [];
  int k = 0;
  // 设置行距为0 ESC 3 NUL
  // tmp.add(0x1B); //ESC
  // tmp.add(0x33); //3
  // tmp.add(0x00); //NUL
  tmp.addAll([0x1B, 0x33, 0x00]);
  switch (align) {
    case "center":
    // 居中打印 ESC a 1
    // tmp[k++] = 0x1B;
    // tmp[k++] = 0x61;
    // tmp[k++] = 1;
      tmp.addAll([0x1B, 0x61, 1]);
      break;
    case "left":
    // 居中打印
    // tmp[k++] = 0x1B;
    // tmp[k++] = 0x61;
    // tmp[k++] = 0;
      tmp.addAll([0x1B, 0x61, 0]);
      break;
    case "right":
    // 居中打印
    // tmp[k++] = 0x1B;
    // tmp[k++] = 0x61;
    // tmp[k++] = 2;
      tmp.addAll([0x1B, 0x61, 2]);
      break;
  }
  for (int j = 0; j < bmp.height / 24; j++) {
    //ESC * m nL nH d1...dk
    tmp.add(0x1B) ;
    tmp.add(0x2A); // 0x1B 2A 表示图片打印指令
    tmp.add(33); // m=33时，选择24点密度打印
    //nL nH 分别表示宽度换算成长度为两个字节的整数时，低8位，和高8位的值
    tmp.add(bmp.width % 256); // nL
    tmp.add(bmp.width ~/ 256); // nH
    for (int i = 0; i < bmp.width; i++) {
      for (int m = 0; m < 3; m++) {
        int byte = 0;
        for (int n = 0; n < 8; n++) {
          int b = px2Byte(i, j * 24 + m * 8 + n, bmp);
          //将8个像素点（值为0或者1）累加起来，八个像素点为一个字节
          byte = byte * 2 + b;
          //byte += byte + b;
          // byte = byte << 1 + b;
        }
        tmp.add(byte);
      }
    }
    tmp.add(10); // 换行
  }
  // 恢复默认行距 ESC 2
  // tmp[k++] = 0x1B;
  // tmp[k++] = 0x32;
  tmp.addAll([0x1B,0x32]);
  return tmp;
}

/// 图片二值化，黑色是1，白色是0
///
/// @param x   横坐标
/// @param y   纵坐标
/// @param bit 位图
/// @return
int px2Byte(int x, int y, img.Image bit) {
  if (x < bit.width && y < bit.height) {
    int b;
    img.Pixel pixel = bit.getPixel(x, y);
    num gray = img.getLuminance(pixel);
    if (gray < 128) {
      b = 1;
    } else {
      b = 0;
    }
    return b;
  }
  return 0;
}
