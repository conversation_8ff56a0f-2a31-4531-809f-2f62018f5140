import 'dart:convert';
import 'dart:io';

import 'package:bluetooth/bluetooth.dart';
import 'package:bluetooth/bluetooth_dto.dart';
import 'package:bluetooth_print/dto/print_config_dto.dart';
import 'package:bluetooth_print/dto/print_data_dto.dart';
import 'package:bluetooth_print/enum/print_align_enum.dart';
import 'package:bluetooth_print/enum/print_font_size_enum.dart';
import 'package:bluetooth_print/enum/print_type_enum.dart';
import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:halo_pos/common/tool/image_tool.dart';
import 'package:halo_pos/login/entity/store/store_cashier.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/utils/math_util.dart';
import 'package:image/image.dart' as Img;
import 'package:qr_flutter/qr_flutter.dart';

import '../../../bill/entity/abstract_bill_dto.dart';
import '../../../bill/entity/goods_bill.dto.dart';
import '../../../bill/entity/goods_detail_dto.dart';
import '../../../bill/entity/order_bill_dto.dart';
import '../../../bill/model/bill_model.dart';
import '../../../bill/tool/bill_tool.dart';
import '../../../common/login/login_center.dart';
import '../../../common/standard.dart';
import '../../../common/tool/date_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../enum/bill_type.dart';
import '../../../enum/setting/print_width_type.dart';
import '../../../enum/setting/printer_type.dart';
import '../../../login/entity/store/store_info.dart';
import '../../../shiftchange/entity/shift_changes_dto.dart';
import '../../bill/bill/channel/model/bill_channel_detail.dart';
import '../../bill/entity/bill_sale_bill_detail_dto.dart';
import '../../bill/entity/payment_dto.dart';
import '../../bill/entity/ptype/ptype_serial_no_dto.dart';
import '../../bill/tool/decimal_display_helper.dart';
import '../../common/string_res.dart';
import '../../entity/print/print_config_info.dart';
import '../../entity/print/print_decorate_config.dart';
import '../../entity/print/print_general_config.dart';
import '../../entity/system/system_config_dto.dart';
import '../../login/model/login_user_model.dart';
import '../../plugin/cash_box_plugin.dart';
import '../../shiftchange/entity/account_balance_dto.dart';
import 'print_config.dart';
import 'print_util.dart';

///
///@ClassName: print_tool
///@Description:打印工具类
///@Author: tanglan
///@Date: 8/19/21 4:26 PM
class PrintTool {
  ///隐藏默认构造函数
  PrintTool._();

  //region  打印准备
  ///进行打印前的准备
  ///检查是否连接蓝牙(或内置蓝牙)
  ///获取打印机配置
  ///如打印纸宽度
  static Future<PrintConfigDto?> _preparePrint(BuildContext context) async {
    final setting = SpTool.getSetting();
    PrintConfigDto printConfig = PrintConfigDto();
    if (setting.printerType == PrinterType.usb.index) {
      if (TextUtil.isEmpty(setting.usbPrinter?.name)) {
        HaloToast.show(context, msg: "还未选择任何USB打印机");
        return null;
      }
    } else {
      if (!Platform.isWindows) {
        BluetoothDto innerPrint = await getPrinter();
        if (StringUtil.isEmpty(innerPrint.address)) {
          if (context.mounted) {
            HaloToast.show(context, msg: "请打开蓝牙连接");
          }
          return null;
        }
        printConfig.address = innerPrint.address;
      } else {
        if (setting.windowsBluetoothPrinter == null) {
          HaloToast.show(context, msg: "还未选择任何蓝牙打印机");
          return null;
        }
      }
    }

    printConfig.isPagerCut = true;
    printConfig.pageSize = PrintWidth.values[SpTool.getPrintPageWidth()].name;
    return printConfig;
  }

  ///获取内置打印机
  static Future<BluetoothDto> getInnerPrinter() async {
    List<BluetoothDto> list = await Bluetooth()
        .getBodedDevices("")
        .onError((error, stackTrace) => []);
    //2022.06.06 新增对新大陆设备的内置蓝牙打印机支持
    return list.firstWhere(
      (element) => element.name == "InnerPrinter" || element.name == "内置打印机",
      orElse: () {
        return BluetoothDto();
      },
    );
  }

  ///获取保存的打印机
  static Future<BluetoothDto> getPrinter() async {
    BluetoothDto? saveBluetooth = SpTool.getSetting().androidBluetoothPrinter;
    if (saveBluetooth == null) {
      return await getInnerPrinter();
    }
    List<BluetoothDto> list = await Bluetooth()
        .getBodedDevices("")
        .onError((error, stackTrace) => []);
    return list.firstWhere((element) {
      if (saveBluetooth.name == "InnerPrinter" ||
          saveBluetooth.name == "内置打印机") {
        return saveBluetooth.name == element.name;
      }
      return saveBluetooth.address == element.address;
    }, orElse: () => BluetoothDto());
  }

  //endregion

  //region 打印入口
  ///单据打印
  /// [printCount], 打印张数
  /// [uploadPrintCount] 是否上传打印次数(默认上传)
  /// [printDataConfig] 打印配置项(特殊指定打印字段时处理)
  static printBill(
    BuildContext context,
    AbstractBillDto goodsBill, {
    int? printCount,
    bool uploadPrintCount = true,
    Map<String, PrintConfigInfo>? printDataConfig,
    String? billTitle,
  }) async {
    try {
      ///获取打印机配置
      PrintConfigDto? printConfig = await _preparePrint(context);

      ///未配置打印信息
      if (null == printConfig) {
        return;
      }
      String vchType = goodsBill.vchtype;

      ///兼容处理  本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill
      if (goodsBill.vchtype == "GoodsTransInternalBill") {
        vchType = BillTypeData[BillType.GoodsTrans] ?? "GoodsTrans";
      }

      ///获取单据允许配置的打印项
      List<String> billPrintConfig = PrintFieldConfig.getBillPrintConfigName(
        billType: BillTypeString[vchType],
      );

      ///未传入打印配置项 通过单据类型打印配置项（用于特殊要求打印信息）
      printDataConfig ??= PrintTool.getBillPrintMap(vchType);

      ///未配置打印张数 取配置中打印张数
      if (printCount == null) {
        PrintGeneralConfig printGeneralConfig = getPrintGeneralConfig(
          printDataConfig[PrintFieldConfig.printCount],
        );

        ///未配置过默认打印1张
        printCount =
            printGeneralConfig.content == 0 ? 1 : printGeneralConfig.content;
      }

      if (context.mounted) {
        await fillBillVipInfo(context, goodsBill, printDataConfig);
      }

      ///打印构建数据
      List<PrintDataDto> printData = [];
      printData.addAll(
        await buildBillPrintData(
          billPrintConfig,
          goodsBill,
          printDataConfig,
          billTitle: billTitle,
        ),
      );

      ///执行打印

      ///因要记录打印次数，每次打印一张，通过多次构造打印数据实现打印多张
      printConfig.printCount = printCount;
      printConfig.isPagerCut = true;
      printConfig.printedCount = goodsBill.printCount;

      if (context.mounted) {
        startPrint(context, printData, printConfig);
      }
      goodsBill.printCount = (goodsBill.printCount ?? 0) + printCount;

      ///上传打印次数
      if (context.mounted && uploadPrintCount) {
        BillModel.writeBatchPrintCount(
          context,
          goodsBill.vchcode,
          //这里本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill
          vchType,
          printCount,
        );
      }
    } catch (e) {
      if (context.mounted) {
        HaloToast.show(context, msg: "打印失败，请联系客服！,失败原因:$e");
      }
    }
  }

  static Future<void> fillBillVipInfo(
    BuildContext context,
    AbstractBillDto goodsBill,
    Map<String, PrintConfigInfo> printDataConfig,
  ) async {
    ///单据中无会员或者未配置打印会员信息则不处理
    if (StringUtil.isZeroOrEmpty(goodsBill.vipCardId) ||
        !checkPrintVipInfo(printDataConfig)) {
      return;
    }

    await Future.delayed(const Duration(seconds: 1));
    if (context.mounted) {
      ///获取会员信息
      Map<String, dynamic> scoreData = await BillModel.getBillVipScoreInfo(
        context,
        goodsBill.vchcode,
        goodsBill.vipCardId,
      );
      goodsBill.vipBillInfo = VipBillInfoBean.fromMap(scoreData);
    }
  }

  ///通过单据id查询单据，然后再打印
  static Future<void> printBillByVchCode(
    BuildContext context,
    BillType billType, {
    String? vchCode,
    int? printCount,
  }) async {
    if (StringUtil.isZeroOrEmpty(vchCode)) {
      HaloToast.show(context, msg: "无效单据id，无法打印，请联系客服！");
      return;
    }
    GoodsBillDto? goodsBill = await BillModel.getGoodsBill(
      context,
      vchCode,
      BillTypeData[billType],
      BillBusinessTypeString[BillBusinessType.SaleNormal],
    );

    if (null == goodsBill) {
      if (context.mounted) {
        HaloToast.show(context, msg: "未获取单据信息，无法打印，请联系客服！");
      }
      return;
    }
    if (context.mounted) {
      printBill(context, goodsBill, printCount: printCount);
    }
  }

  ///打印预结单
  static Future<void> printBillPreView(
    BuildContext context,
    BillType billType,
    GoodsBillDto goodsBill,
  ) async {
    ///打印预结单 仅打印指定部分字段
    ///只展示商品，数量，合计金额，优惠金额，打印日期
    const previewConfigKey = [
      PrintFieldConfig.shopName,
      PrintFieldConfig.billName,
      PrintFieldConfig.retailPrice,
      PrintFieldConfig.sumTotal,
      PrintFieldConfig.sumQty,
      PrintFieldConfig.wTotal,
      PrintFieldConfig.pFullName,
      PrintFieldConfig.pTypeCode,
      PrintFieldConfig.pTypeBatchNo,
      PrintFieldConfig.pTypeProductDay,
      PrintFieldConfig.printDate,
      PrintFieldConfig.address,
    ];

    ///获取打印配置
    Map<String, PrintConfigInfo> printConfig = PrintTool.getBillPrintMap(
      goodsBill.vchtype,
    );

    ///移除预结单无需打印字段
    printConfig.removeWhere((key, value) => !previewConfigKey.contains(key));
    if (context.mounted) {
      printBill(
        context,
        goodsBill,
        printCount: 1,
        uploadPrintCount: false,
        printDataConfig: printConfig,
        billTitle: "预结单",
      );
    }
  }

  ///打印交接班数据
  static Future<void> printShiftChanges(
    BuildContext context, {
    required ShiftChangesDto shiftChangesDto,
  }) async {
    PrintConfigDto? printConfig = await _preparePrint(context);

    ///未配置打印机信息
    if (null == printConfig) {
      return;
    }
    if (context.mounted) {
      await startPrint(
        context,
        _buildShiftChangesData(shiftChangesDto),
        printConfig,
      );
    }
  }

  ///打印全渠道订单
  static Future<void> printChannelBill(
    BuildContext context,
    BillChannelDetailDto channelDetailDto,
  ) async {
    PrintConfigDto? printConfig = await _preparePrint(context);
    if (null == printConfig) {
      return;
    }
    if (context.mounted) {
      startPrint(
        context,
        await _buildChannelBillPrintData(context, channelDetailDto),
        printConfig,
      );
    }
  }

  ///打印会员充值
  static Future<void> printRecharge(
    BuildContext context, {
    required String vipName,
    required String phone,
    required String rechargeMoney,
    required String giveMoney,
    required String beforeMoney,
    required String afterMoney,
    required String payMoney,
    required String atypeNames,
  }) async {
    PrintConfigDto? printConfig = await _preparePrint(context);
    if (null == printConfig) {
      return;
    }

    List<PrintDataDto> printData = [];
    printData.addAll(_buildRechargeTopPrintData());
    printData.addAll(
      _buildRechargeCenterPrintData(
        vipName: vipName,
        phone: phone,
        rechargeMoney: rechargeMoney,
        giveMoney: giveMoney,
        beforeMoney: beforeMoney,
        afterMoney: afterMoney,
        payMoney: payMoney,
        atypeNames: atypeNames,
      ),
    );
    printData.addAll(_buildRechargeFootPrintData());
    if (context.mounted) {
      await startPrint(context, printData, printConfig);
    }
  }

  //endregion

  //region 构建单据打印数据

  ///验证是否打印会员信息
  static bool checkPrintVipInfo(Map<String, PrintConfigInfo> printDataConfig) {
    ///本次积分/积分余额/会员姓名/会员手机号/储值余额开启任意一个，则需要获取会员信息
    return (printDataConfig[PrintFieldConfig.saleScore]?.selected ?? false) ||
        (printDataConfig[PrintFieldConfig.scoreTotal]?.selected ?? false) ||
        (printDataConfig[PrintFieldConfig.vipPhone]?.selected ?? false) ||
        (printDataConfig[PrintFieldConfig.vipName]?.selected ?? false) ||
        (printDataConfig[PrintFieldConfig.storeTotal]?.selected ?? false);
  }

  //构建打印打印数据
  static buildBillPrintData(
    List<String> billPrintConfig,
    AbstractBillDto goodsBillDto,
    Map<String, PrintConfigInfo> printConfig, {
    String? billTitle,
  }) async {
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();

    List<PrintDataDto> printData = [];
    printData.addAll(
      await buildTitlePrintData(
        billPrintConfig,
        goodsBillDto,
        printConfig,
        billTitle: billTitle,
      ),
    );

    ///兼容处理  本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill
    ///调拨单/调拨订单不展示金额
    bool isShowPrice =
        goodsBillDto.vchtype != BillTypeData[BillType.TransferOrder] &&
        goodsBillDto.vchtype != BillTypeData[BillType.GoodsTrans] &&
        goodsBillDto.vchtype != "GoodsTransInternalBill";

    ///商品明细打印信息
    ///换货单需要打印换入和换出，单独处理
    if (goodsBillDto.vchtype == BillTypeData[BillType.SaleChangeBill]) {
      printData.addAll(
        buildChangeSaleContent(
          billPrintConfig,
          printConfig,
          goodsBillDto as GoodsBillDto,
          systemConfigDto,
        ),
      );
    } else {
      List<GoodsDetailDto> goodsDetails = getPrintDetails(goodsBillDto);
      printData.addAll(
        getGoodsDetail(
          billPrintConfig,
          printConfig,
          goodsDetails,
          systemConfigDto,
          isShowPrice: isShowPrice,
        ),
      );
    }

    ///支付信息
    printData.addAll(
      buildPrintSumAndFeeInfo(
        billPrintConfig,
        printConfig,
        goodsBillDto,
        systemConfigDto,
      ),
    );

    ///会员信息
    printData.addAll(buildVipInfo(billPrintConfig, printConfig, goodsBillDto));

    ///门店信息
    printData.addAll(
      await buildShopInfo(
        billPrintConfig,
        printConfig,
        invoiceUrl: buildInvoiceUrl(goodsBillDto),
      ),
    );
    printData.add(getLineBreak());

    ///打印信息
    printData.addAll(
      buildPrintInfo(billPrintConfig, printConfig, goodsBillDto),
    );

    return printData;
  }

  static String getThumbnailUrl(String? url) {
    if (StringUtil.isEmpty(url) ||
        !url!.contains("picture.qiniu.mygjp.com.cn") ||
        url.contains("200x200")) {
      return url ?? "";
    }

    return "$url!200x200";
  }

  ///构建表头信息
  static Future<List<PrintDataDto>> buildTitlePrintData(
    List<String> billPrintConfig,
    AbstractBillDto goodsBillDto,
    Map<String, PrintConfigInfo> printConfigMap, {
    String? billTitle,
  }) async {
    List<PrintDataDto> list = [];

    ///票头装饰
    await addDecorateInfo(
      billPrintConfig,
      list,
      printConfigMap,
      PrintFieldConfig.decorateTop,
    );

    ///未传入单据名称，根据配置进行获取

    billTitle ??= PrintTool.buildShowFieldName(
      printConfigMap,
      PrintFieldConfig.billName,
      defaultShowName: billTypeStrName[goodsBillDto.vchtype] ?? '',
    );

    StoreInfo storeInfo = SpTool.getStoreInfo() ?? StoreInfo();

    ///门店表头信息
    list.add(
      _buildShopNameData(billPrintConfig, printConfigMap, billTitle: billTitle),
    );

    ///门店LOGO
    await addImageUrl(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.shopLogo,
      storeInfo.shopLogoUrl,

      localFile: false,
      bytesContent: storeInfo.shopLogoContent,
      imgWidth: 300,
      printAlignEnum:
          PrintFieldConfig
              .printLocationPrintAlign[printConfigMap[PrintFieldConfig.shopLogo]
                  ?.printLocation ??
              PrintLocationEnum.middle.index] ??
          PrintAlignEnum.CENTER,
    );

    ///增加空白
    list.add(getLineBreak());

    ///收银员名称
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.cashierName,
      value: goodsBillDto.createEfullname,
    );

    ///收银员工号
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.cashierNo,
      value: goodsBillDto.createUserCode,
    );

    ///单据编号
    await addBillNumberData(
      list,
      PrintFieldConfig.billNumber,
      billPrintConfig,
      printConfigMap,
      billNumber: goodsBillDto.number ?? "",
    );

    ///订单编号（全渠道订单使用）
    await addBillNumberData(
      list,
      PrintFieldConfig.orderNumber,
      billPrintConfig,
      printConfigMap,
      billNumber: goodsBillDto.number ?? "",
    );

    ///调出仓库
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.kFullName,
      value: goodsBillDto.kfullname,
    );

    ///调出仓库
    String? kFullName2 = "";
    if (goodsBillDto is OrderBillDTO) {
      kFullName2 = goodsBillDto.kfullname2;
    } else if (goodsBillDto is GoodsBillDto) {
      kFullName2 = goodsBillDto.kfullname2;
    }
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.kFullName2,
      value: kFullName2,
    );

    ///业务员
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.eTypeName,
      value: goodsBillDto.efullname,
    );

    ///制单人
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.createETypeName,
      value: goodsBillDto.createEfullname,
    );

    ///付款时间
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.payDate,
      value: _getTimeStr(goodsBillDto.postTime),
    );

    ///单据日期
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.billDate,
      value: _getTimeStr(goodsBillDto.date),
    );

    ///业务员
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.billGuide,
      value: goodsBillDto.efullname,
    );

    ///备注
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.billMemo,
      value: goodsBillDto.memo,
    );

    ///摘要
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.summary,
      value: goodsBillDto.summary,
    );

    ///附加说明
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.comment,
      value: goodsBillDto.memo,
    );

    ///买家备注
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfigMap,
      PrintFieldConfig.buyerMessage,
      value: goodsBillDto.memo,
    );

    ///增加分割线
    if (list.isNotEmpty) {
      list.add(getLine());
    }
    return list;
  }

  ///构建门店+单据名称
  static PrintDataDto _buildShopNameData(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfigMap, {
    String? billTitle,
  }) {
    StoreInfo storeInfo = SpTool.getStoreInfo() ?? StoreInfo();
    PrintDataDto shopPrint = PrintDataDto();
    shopPrint.content = storeInfo.fullname ?? "";
    if (billPrintConfig.isNotEmpty &&
        printConfigMap.isNotEmpty &&
        PrintTool.isShowFieldName(
          billPrintConfig,
          printConfigMap,
          PrintFieldConfig.billName,
        )) {
      shopPrint.content = "${shopPrint.content} - $billTitle";
    }

    bool is80 = SpTool.getPrintPageWidth() == PrintWidth.w_80mm.index;
    shopPrint.isBold = true;
    shopPrint.align = PrintAlignEnum.CENTER;
    shopPrint.fontSize = PrintFontSizeEnum.MIDDLE;
    shopPrint.width = is80 ? 46 : 30; //顶部归位占了2个字符，导致第一行数据换行问题
    return shopPrint;
  }

  ///无配置打印表头信息
  static PrintDataDto _buildShopNameDataWithNoConfig() {
    return _buildShopNameData([], {});
  }

  ///构建单据编号
  static Future<void> addBillNumberData(
    List<PrintDataDto> list,
    String fieldKey,
    List<String>? billPrintConfig,
    Map<String, PrintConfigInfo> printMapConfig, {
    String billNumber = "",
  }) async {
    ///未配置打印单据编号
    if (!PrintTool.isShowFieldName(billPrintConfig, printMapConfig, fieldKey)) {
      return;
    }

    PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
      printMapConfig[fieldKey],
    );

    ///单据编号打印成二维码
    if (printGeneralConfig.content == BillNumberModeEnum.scan.index) {
      list.add(
        PrintDataDto()
          ..type = PrintTypeEnum.IMAGE
          ..imageWidth = 300
          ..content = await toQrImageData(billNumber, imageSize: 300)
          ..align = PrintAlignEnum.CENTER,
      );
      list.add(
        PrintDataDto()
          ..type = PrintTypeEnum.TEXT
          ..content = billNumber
          ..align = PrintAlignEnum.CENTER,
      );
      return;
    }

    ///单据编号打印成文本
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printMapConfig,
      fieldKey,
      value: billNumber,
    );
  }

  ///构建换货单商品明细打印数据
  static List<PrintDataDto> buildChangeSaleContent(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    GoodsBillDto goodsBillDto,
    SystemConfigDto systemConfigDto,
  ) {
    List<PrintDataDto> printData = [];
    _addNormalRowItem(printData, title: "换入明细");
    printData.addAll(
      getGoodsDetail(
        billPrintConfig,
        printConfig,
        goodsBillDto.inDetail,
        systemConfigDto,
        isShowPrice: true,
      ),
    );
    printData.add(getLine());
    printData.add(getLineBreak());
    _addNormalRowItem(printData, title: "换出明细");
    printData.addAll(
      getGoodsDetail(
        billPrintConfig,
        printConfig,
        goodsBillDto.outDetail,
        systemConfigDto,
        isShowPrice: true,
      ),
    );
    return printData;
  }

  ///构建商品明细打印数据
  static List<PrintDataDto> getGoodsDetail(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    List<GoodsDetailDto> details,
    SystemConfigDto systemConfigDto, {
    bool isShowPrice = true,
  }) {
    List<PrintDataDto> arr = [];

    ///明细标题
    arr.add(
      PrintDataDto()
        ..type = PrintTypeEnum.GROUP
        ..data = buildDetailRowData(
          billPrintConfig,
          printConfig,
          name: PrintTool.buildShowFieldName(
            printConfig,
            PrintFieldConfig.pFullName,
            defaultShowName: StringRes.PRODUCT.value,
          ),
          retailPrice: PrintTool.buildShowFieldName(
            printConfig,
            PrintFieldConfig.retailPrice,
          ),
          price: PrintTool.buildShowFieldName(
            printConfig,
            PrintFieldConfig.price,
            defaultShowName: StringRes.PRICE.value,
          ),
          qty: PrintTool.buildShowFieldName(
            printConfig,
            PrintFieldConfig.qty,
            defaultShowName: StringRes.QTY.value,
          ),
          total: PrintTool.buildShowFieldName(
            printConfig,
            PrintFieldConfig.total,
            defaultShowName: StringRes.SUM_TOTAL.value,
          ),
          isShowPrice: isShowPrice,
        ),
    );

    ///数量合计
    Decimal totalQty = Decimal.zero;

    ///金额合计
    Decimal totalTotal = Decimal.zero;

    ///不包含套餐明细行的数据
    List<GoodsDetailDto> unComboDetailDetails =
        details
            .where((element) => StringUtil.isZeroOrEmpty(element.comboRowParId))
            .toList();

    ///套餐明细分组
    Map<String, List<GoodsDetailDto>> comboDetailList = details
        .where((element) => StringUtil.isNotZeroOrEmpty(element.comboRowParId))
        .groupListsBy((element) => element.comboRowParId);

    ///明细项
    for (int index = 0; index < unComboDetailDetails.length; index++) {
      GoodsDetailDto detail = unComboDetailDetails[index];

      ///打印套餐行，套餐明细行不打印
      if (StringUtil.isNotZeroOrEmpty(detail.comboRowParId)) {
        continue;
      }

      ///统计合计数量
      totalQty = MathUtil.add(
        DecimalDisplayHelper.getQtyFixed(detail.unitQty.toString()),
        totalQty.toString(),
      );

      ///统计合计金额(存在储值赠金，需要将赠金加到合计金额中)
      totalTotal =
          MathUtil.parseToDecimal(
            MathUtil.add(
              detail.currencyDisedTaxedTotal.toString(),
              detail.currencyGivePreferentialTotal.abs().toString(),
            ).toString(),
          ) +
          totalTotal;

      ///商品明细内容
      arr.addAll(
        buildDetailContent(
          billPrintConfig,
          printConfig,
          detail,
          isShowPrice,
          systemConfigDto,
          preFix: "${index + 1}.",
        ),
      );

      PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
        printConfig[PrintFieldConfig.pFullName],
      );

      ///打印套餐明细
      if (detail.comboRow &&
          printGeneralConfig.content == ComboModeEnum.content.index) {
        arr.addAll(
          buildComboDetails(
            billPrintConfig,
            printConfig,
            systemConfigDto,
            comboDetailList[detail.comboRowId] ?? [],
            isShowPrice,
          ),
        );
      }
    }

    arr.add(getLine());

    //合计
    arr.add(
      calSumInfoWidget(
        billPrintConfig,
        printConfig,
        isShowPrice,
        totalQty,
        totalTotal,
      ),
    );
    arr.add(getLineBreak());
    return arr;
  }

  static String concatPFullName(GoodsDetailDto detail) {
    ///商品名称
    String productName = detail.pFullName ?? "";

    ///赠金打标
    if (detail.gift) {
      productName += "【${StringRes.LABEL_GIFT.value}】";
    }

    return productName;
  }

  ///获取打印明细列表
  static List<GoodsDetailDto> getPrintDetails(AbstractBillDto goodsBillDto) {
    ///调拨单/销售出库单/换货单
    if (goodsBillDto.vchtype == "GoodsTransInternalBill" ||
        goodsBillDto.vchtype == BillTypeData[BillType.SaleBill] ||
        goodsBillDto.vchtype == BillTypeData[BillType.SaleChangeBill]) {
      if (goodsBillDto is GoodsBillDto) {
        return goodsBillDto.outDetail;
      }
    }
    ///调拨订单
    else if (goodsBillDto.vchtype == BillTypeData[BillType.TransferOrder]) {
      if (goodsBillDto is OrderBillDTO) {
        return goodsBillDto.detail ?? [];
      }
    }
    ///退货单
    else if (goodsBillDto.vchtype == BillTypeData[BillType.SaleBackBill]) {
      if (goodsBillDto is GoodsBillDto) {
        return goodsBillDto.inDetail;
      }
    }

    return [];
  }

  ///构造支付信息
  static List<PrintDataDto> buildPrintSumAndFeeInfo(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    AbstractBillDto goodsBillDto,
    SystemConfigDto systemConfigDto,
  ) {
    List<GoodsDetailDto> goodsDetails = getPrintDetails(goodsBillDto);
    List<PrintDataDto> list = [];

    ///数量合计(调拨单使用)
    addNormalRowCheckField(
      list,
      billPrintConfig,

      printConfig,
      PrintFieldConfig.tenderSumQty,
      value: getSumQty(goodsDetails),
    );

    ///数量合计
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.sumQty,
      value: getSumQty(goodsDetails),
    );

    ///合计金额
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.sumTotal,
      value: getSumTotal(goodsDetails),
    );

    ///税额合计金额
    if (systemConfigDto.sysGlobalEnabledTax &&
        systemConfigDto.sysGlobalEnabledSaleTax) {
      addNormalRowCheckField(
        list,
        billPrintConfig,
        printConfig,
        PrintFieldConfig.sumTaxTotal,
        value: getTaxTotal(goodsDetails),
      );
    }

    ///退货数量
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.backQty,
      value: getSumQty(goodsDetails),
    );

    ///退货金额
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.backTotal,
      value:
          "${MathUtil.add(goodsBillDto.currencyBillTotal, goodsBillDto.currencyGivePreferentialTotal.toString())}",
    );

    ///换货数量
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.changeQty,
      value: getChangeQty(goodsBillDto),
    );

    ///换货金额
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.changeTotal,
      value:
          "${MathUtil.add(goodsBillDto.currencyBillTotal, goodsBillDto.currencyGivePreferentialTotal.toString())} ${num.parse(goodsBillDto.currencyBillTotal) < 0 ? "(退款)" : ""} ",
    );

    ///优惠金额
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.wTotal,
      value: getPreferentialTotal(goodsDetails),
    );

    ///找零金额
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.pettyCash,
      value: DecimalDisplayHelper.getTotalFixed(goodsBillDto.pettyCash),
    );

    ///支付方式
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.payment,
      value: getPayment(goodsBillDto),
    );

    ///物流费用
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.freightFee,
      value:
          goodsBillDto is GoodsBillDto
              ? DecimalDisplayHelper.getTotalFixed(
                goodsBillDto.currencyBuyerFreightFee.toString(),
              )
              : "",
    );

    ///买家运费
    // addNormalRowItem(list, printConfig, PrintFieldConfig.buyerFreight,
    //     value: StringUtil.double2FixedString(
    //         goodsBillDto is BillChannelDetailDto ? goodsBillDto.freightInfo?.buyerFreightFee ?? 0));
    ///调出店签名(调拨单使用)
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.signOut,
      value: "",
    );

    ///调入店签名(调拨单使用)
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.signIn,
      value: "",
    );
    if (list.isNotEmpty) {
      list.add(getLine());
    }
    return list;
  }

  static List<PrintDataDto> buildVipInfo(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    AbstractBillDto goodsBillDto,
  ) {
    List<PrintDataDto> list = [];

    ///会员姓名
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.vipName,
      value: goodsBillDto.vipBillInfo?.name,
    );

    ///会员手机号
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.vipPhone,
      value: goodsBillDto.vipBillInfo?.phone,
    );

    ///储值余额
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.storeTotal,
      value: (goodsBillDto.vipBillInfo?.chargeTotal ?? 0).toString(),
    );

    ///本次积分
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.saleScore,
      value: (goodsBillDto.vipBillInfo?.saleScore ?? 0).toString(),
    );

    ///积分余额
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.scoreTotal,
      value: (goodsBillDto.vipBillInfo?.scoreTotal ?? 0).toString(),
    );
    if (list.isNotEmpty) {
      list.add(getLine());
    }
    return list;
  }

  ///门店信息
  static Future<List<PrintDataDto>> buildShopInfo(
    List<String>? billPrintConfig,
    Map<String, PrintConfigInfo> printConfig, {
    String? pickupCode,
    String invoiceUrl = "",
  }) async {
    StoreInfo? storeInfo = SpTool.getStoreInfo();
    if (null == storeInfo) {
      return [];
    }
    List<PrintDataDto> list = [];

    ///门店电话
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.shopPhone,
      value: storeInfo.cellphone,
    );

    ///门店地址打印
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.address,
      value:
          "${storeInfo.province ?? ""}${storeInfo.city ?? ""}${storeInfo.district ?? ""}${storeInfo.address ?? ""}",
    );

    ///门店公告
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.shopNotice,
      value: storeInfo.shopNotice,
    );

    ///门店二维码
    await addImageUrl(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.shopScan,
      storeInfo.shopScanUrl,
      bytesContent: storeInfo.shopScanContent,
      localFile: false,
    );

    ///门店二维码说明
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.shopScan,
      title: "",
      value: storeInfo.shopScanMemo,
      align: PrintAlignEnum.CENTER,
    );

    /// 提货码
    addPickCode(list, printConfig, pickupCode);

    ///开票信息打印
    if (PrintTool.isShowFieldName(
      billPrintConfig,
      printConfig,
      PrintFieldConfig.invoiceCode,
    )) {
      list.add(PrintDataDto());
      list.add(
        PrintDataDto()
          ..type = PrintTypeEnum.IMAGE
          ..imageWidth = 350
          ..content = await toQrImageData(invoiceUrl, imageSize: 380)
          ..align = PrintAlignEnum.CENTER,
      );
      PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
        printConfig[PrintFieldConfig.invoiceCode],
      );
      if (StringUtil.isNotEmpty(printGeneralConfig.text)) {
        list.add(
          PrintDataDto()
            ..type = PrintTypeEnum.TEXT
            ..content = printGeneralConfig.text
            ..align = PrintAlignEnum.CENTER,
        );
      }
    }

    ///票尾装饰
    await addDecorateInfo(
      billPrintConfig,
      list,
      printConfig,
      PrintFieldConfig.decorateTail,
    );
    if (list.isNotEmpty) {
      list.add(getLine());
    }
    return list;
  }

  static List<PrintDataDto> buildPrintInfo(
    List<String>? billPrintConfig,
    Map<String, PrintConfigInfo> printMapConfig,
    AbstractBillDto goodsBillDto,
  ) {
    List<PrintDataDto> list = [];

    PrintGeneralConfig tenderPrintCountGeneralConfig = getPrintGeneralConfig(
      printMapConfig[PrintFieldConfig.tenderPrintCount],
      defaultContent: 1,
    );

    ///打印次数(调拨单、调拨订单使用)
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printMapConfig,
      PrintFieldConfig.tenderPrintCount,
      align: PrintAlignEnum.LEFT,
      value:
          "${PrintFieldConfig.printCount}/${tenderPrintCountGeneralConfig.content}",
    );

    PrintGeneralConfig printGeneralConfig = getPrintGeneralConfig(
      printMapConfig[PrintFieldConfig.printCount],
      defaultContent: 1,
    );

    ///打印次数
    ///value中${PrintFieldConfig.printCount} 用于在打印插件中处理成已打印次数
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printMapConfig,
      align: PrintAlignEnum.LEFT,
      PrintFieldConfig.printCount,
      value: "${PrintFieldConfig.printCount}/${printGeneralConfig.content}",
    );

    ///打印日期
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printMapConfig,
      align: PrintAlignEnum.LEFT,
      PrintFieldConfig.printDate,
      value: DateUtil.formatDate(DateTime.now(), format: DateFormats.full),
    );

    ///打印日期（调拨单使用）
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printMapConfig,
      align: PrintAlignEnum.LEFT,
      PrintFieldConfig.tenderPrintDate,
      value: DateUtil.formatDate(DateTime.now(), format: DateFormats.full),
    );
    if (list.isNotEmpty) {
      list.add(getLineBreak());
    }
    return list;
  }

  ///构建商品名称打印
  static String buildPrintBatchNo(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    GoodsDetailDto detail,
  ) {
    if (!detail.batchenabled) {
      return "";
    }
    String batchStr = "";
    if (isShowFieldName(
          billPrintConfig,
          printConfig,
          PrintFieldConfig.pTypeBatchNo,
        ) &&
        (detail.batchNo.isNotEmpty)) {
      batchStr = "${detail.batchNo};";
    }
    if (isShowFieldName(
          billPrintConfig,
          printConfig,
          PrintFieldConfig.pTypeProductDay,
        ) &&
        (detail.produceDate?.isNotEmpty ?? false)) {
      //时区处理
      DateTime? localDate = DateTime.tryParse(detail.produceDate!)?.toLocal();
      batchStr +=
          "${DateUtil.formatDateStr(localDate.toString(), format: DateFormats.y_mo_d)};";
    }
    if (isShowFieldName(
          billPrintConfig,
          printConfig,
          PrintFieldConfig.pTypeProductDay,
        ) &&
        (detail.expireDate?.isNotEmpty ?? false)) {
      //时区处理
      DateTime? localExpireDate =
          DateTime.tryParse(detail.expireDate!)?.toLocal();
      batchStr += DateUtil.formatDateStr(
        localExpireDate.toString(),
        format: DateFormats.y_mo_d,
      );
    }
    return batchStr;
  }

  ///构建商品序列号数据
  static String buildPrintSn(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    List<PtypeSerialNoDto> serialNoList,
  ) {
    if (serialNoList.isEmpty ||
        !isShowFieldName(
          billPrintConfig,
          printConfig,
          PrintFieldConfig.pTypeSn,
        )) {
      return "";
    }
    return serialNoList.map((e) => e.snno).join(",");
  }

  ///构建税率信息
  static String buildTaxInfo(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    SystemConfigDto systemConfigDto,
    GoodsDetailDto goodsDetail,
  ) {
    String taxMsg = "";
    if (!(systemConfigDto.sysGlobalEnabledTax &&
        systemConfigDto.sysGlobalEnabledSaleTax)) {
      return taxMsg;
    }
    if (isShowFieldName(
      billPrintConfig,
      printConfig,
      PrintFieldConfig.taxRate,
    )) {
      taxMsg =
          "${buildShowFieldName(printConfig, PrintFieldConfig.taxRate, defaultShowName: '税率')}:${goodsDetail.taxRate}%；";
    }
    if (isShowFieldName(
      billPrintConfig,
      printConfig,
      PrintFieldConfig.taxTotal,
    )) {
      taxMsg +=
          "${buildShowFieldName(printConfig, PrintFieldConfig.taxTotal, defaultShowName: '税额')}:￥${goodsDetail.currencyTaxTotal};";
    }
    return taxMsg;
  }

  static String string2Space(String str) {
    String temp = "";
    int l = str.length;
    if (str.isNotEmpty) {
      for (int i = 0; i < l; i++) {
        temp += " ";
      }
    }
    return temp;
  }

  ///构建支付信息
  static String getPayment(AbstractBillDto goodsBillDto) {
    String payment = "";
    for (var i = 0; i < goodsBillDto.payment.length; i++) {
      PaymentDto paymentDto = goodsBillDto.payment[i];

      String? paymentTotal;

      ///储值支付方式需要增加赠金优惠
      if (paymentDto.paywayType == 3) {
        paymentTotal =
            MathUtil.add(
              paymentDto.currencyAtypeTotal,
              goodsBillDto.currencyGivePreferentialTotal.toString(),
            ).toString();
      } else {
        paymentTotal = paymentDto.currencyAtypeTotal;
      }
      if (i > 0) {
        payment += "+";
      }
      payment += "${paymentDto.paywayFullname ?? ""}($paymentTotal)";
    }

    return payment;
  }

  ///优惠金额计算
  static String getPreferentialTotal(List<GoodsDetailDto> details) {
    Decimal sumPreferentialTotal = Decimal.zero;
    for (GoodsDetailDto item in details) {
      //由于在后端已经计算过套餐的优惠金额，所有要排除这里，否则会计算错误
      if (StringUtil.isNotZeroOrEmpty(item.comboRowParId)) {
        continue;
      }
      sumPreferentialTotal += MathUtil.subtraction(
        item.currencyPreferentialTotal.toString(),
        item.currencyGivePreferentialTotal.abs().toString(),
      );
    }
    return DecimalDisplayHelper.getTotalFixed(sumPreferentialTotal.toString());
  }

  ///明细行内容打印信息
  static List<PrintDataDto> buildDetailContent(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    GoodsDetailDto detail,
    bool isShowPrice,
    SystemConfigDto systemConfigDto, {
    String preFix = "",
    bool isComboDetail = false,
  }) {
    List<PrintDataDto> detailPrintData = [];

    ///商品名称
    String concatName = concatPFullName(detail);
    if (StringUtil.isNotEmpty(preFix)) {
      concatName = "$preFix$concatName";
    }

    ///属性规格型号品牌
    String standardAndType = detail.skuName ?? '';
    if (isShowFieldName(
          billPrintConfig,
          printConfig,
          PrintFieldConfig.pTypeStandard,
        ) &&
        StringUtil.isNotEmpty(detail.standard)) {
      standardAndType = "$standardAndType:${detail.standard}";
    }
    if (isShowFieldName(
          billPrintConfig,
          printConfig,
          PrintFieldConfig.pTypeType,
        ) &&
        StringUtil.isNotEmpty(detail.ptypetype)) {
      standardAndType = "$standardAndType:${detail.ptypetype}";
    }
    //品牌
    if (isShowFieldName(billPrintConfig, printConfig, PrintFieldConfig.brand) &&
        StringUtil.isNotEmpty(detail.brandName)) {
      standardAndType = "$standardAndType:${detail.brandName}";
    }

    standardAndType = StringUtil.trim(standardAndType, trimStr: ":");

    if (StringUtil.isNotEmpty(standardAndType)) {
      concatName += "($standardAndType)";
    }

    ///名称行
    detailPrintData.add(PrintDataDto()..content = concatName);

    ///批次信息
    String batchStr = buildPrintBatchNo(billPrintConfig, printConfig, detail);
    if (StringUtil.isNotEmpty(batchStr)) {
      detailPrintData.add(
        PrintDataDto()..content = "  ${StringRes.BATCH.value} : $batchStr",
      );
    }

    ///序列号信息
    String snStr = buildPrintSn(
      billPrintConfig,
      printConfig,
      detail.serialNoList,
    );
    if (StringUtil.isNotEmpty(snStr)) {
      detailPrintData.add(
        PrintDataDto()..content = "  ${StringRes.pTypeSn.value} : $snStr",
      );
    }

    ///税率信息
    String taxStr = buildTaxInfo(
      billPrintConfig,
      printConfig,
      systemConfigDto,
      detail,
    );
    if (StringUtil.isNotEmpty(taxStr)) {
      detailPrintData.add(PrintDataDto()..content = "   $taxStr");
    }

    ///商品编码
    String? pTypeCode = " ";

    ///处理商品编码打印
    if (isShowFieldName(
      billPrintConfig,
      printConfig,
      PrintFieldConfig.pTypeCode,
    )) {
      PrintGeneralConfig printGeneralConfig = PrintTool.getPrintGeneralConfig(
        printConfig[PrintFieldConfig.pTypeCode],
      );

      ///商品编号选中或者品编码选中且商品编码和条码都未选择默认选中商品编号
      pTypeCode =
          printGeneralConfig.content == PTypeModeEnum.skuBarCode.index
              ? detail.fullbarcode
              : detail.pUserCode;
    }

    ///数量=单位数量+单位
    String qtyStr =
        " ${DecimalDisplayHelper.getQtyFixed(detail.unitQty.toString())}${detail.unitName}";

    ///小计=单据支付金额+赠金
    String total =
        " ${DecimalDisplayHelper.getTotalFixed((MathUtil.add(detail.currencyDisedTaxedTotal.toString(), detail.currencyGivePreferentialTotal.abs().toString()).toString()))}";

    ///计算现价 现价 =小计/数量+赠金分摊到单数量上的价格
    String calPrice = "0";
    if (isShowPrice) {
      calPrice = DecimalDisplayHelper.getPriceFixed(
        MathUtil.division(total.trim(), detail.unitQty.toString()).toString(),
      );
    }

    ///套餐明细行仅打印数量，且布局不变和其他商品的数据列对齐
    ///打印内容需要增加一个空格，esc_pos_util打印组件中，其中有一个字符串[0] ,当获取的内容为空字符串时，会打印失败，
    detailPrintData.add(
      PrintDataDto()
        ..type = PrintTypeEnum.GROUP
        ..data = buildDetailRowData(
          billPrintConfig,
          printConfig,
          name: StringUtil.isNotEmpty(pTypeCode) ? pTypeCode! : " ",
          retailPrice:
              isComboDetail
                  ? " "
                  : " ${DecimalDisplayHelper.getPriceFixed(detail.currencyPrice.toString())}",
          price: isComboDetail ? " " : " $calPrice",
          qty: qtyStr,
          total: isComboDetail ? " " : total,
          isShowPrice: isShowPrice,
        ),
    );
    return detailPrintData;
  }

  ///套餐明细行内容打印信息
  static List<PrintDataDto> buildComboDetails(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    SystemConfigDto systemConfigDto,
    List<GoodsDetailDto> details,
    bool isShowPrice,
  ) {
    List<PrintDataDto> printData = [];
    for (GoodsDetailDto detail in details) {
      ///套餐明细内容
      printData.addAll(
        buildDetailContent(
          billPrintConfig,
          printConfig,
          detail,
          isShowPrice,
          systemConfigDto,
          isComboDetail: true,
          preFix: " -",
        ),
      );
    }
    return printData;
  }

  ///构建套餐明细行数据
  static List<PrintDataDto> buildDetailRowData(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig, {
    String name = "",
    String retailPrice = "",
    String price = "",
    String qty = "",
    String total = "",
    bool isShowPrice = true,
  }) {
    List<PrintDataDto> rowPrintData = [];
    rowPrintData.add(
      PrintDataDto()
        ..isBold = true
        ..align = PrintAlignEnum.LEFT
        ..content = name,
    );

    ///零售价
    if (isShowPrice &&
        PrintTool.isShowFieldName(
          billPrintConfig,
          printConfig,
          PrintFieldConfig.retailPrice,
        )) {
      rowPrintData.add(
        PrintDataDto()
          ..content = retailPrice
          ..isBold = true
          ..align = PrintAlignEnum.CENTER,
      );
    }
    if (isShowPrice) {
      ///单价
      rowPrintData.add(
        PrintDataDto()
          ..content = price
          ..isBold = true
          ..align = PrintAlignEnum.CENTER,
      );
    }

    ///数量
    rowPrintData.add(
      PrintDataDto()
        ..content = qty
        ..isBold = true
        ..align = PrintAlignEnum.CENTER,
    );

    if (isShowPrice) {
      ///金额
      rowPrintData.add(
        PrintDataDto()
          ..content = total
          ..isBold = true
          ..weight = getTotalScale(billPrintConfig, printConfig)
          ..align = PrintAlignEnum.RIGHT,
      );
    }
    return rowPrintData;
  }

  static double getTotalScale(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
  ) {
    return PrintTool.isShowFieldName(
          //当展示零售价时，占比1.2
          billPrintConfig,
          printConfig,
          PrintFieldConfig.retailPrice,
        )
        ? 1.5
        : 1.0;
  }

  ///合计信息打印统计
  static PrintDataDto calSumInfoWidget(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    bool isShowPrice,
    Decimal totalQty,
    Decimal totalTotal,
  ) {
    double titleRat = 2;

    ///不展示价格时，占比3:1
    if (!isShowPrice) {
      titleRat = 3;
    }

    ///展示零售价格时
    if (printConfig[PrintFieldConfig.retailPrice]?.selected ?? false) {
      titleRat = 2.5;
    }

    List<PrintDataDto> sumPrintData = [];
    sumPrintData.add(
      PrintDataDto()
        ..weight = titleRat
        ..align = PrintAlignEnum.LEFT
        ..content = StringRes.TOTAL.value,
    );

    sumPrintData.add(
      PrintDataDto()
        ..content = DecimalDisplayHelper.getQtyFixed(totalQty.toString())
        ..align = PrintAlignEnum.CENTER,
    );
    if (isShowPrice) {
      sumPrintData.add(
        PrintDataDto()
          ..content =
              " ${DecimalDisplayHelper.getTotalFixed(totalTotal.toString())}"
          ..weight = getTotalScale(billPrintConfig, printConfig)
          ..align = PrintAlignEnum.RIGHT,
      );
    }
    return PrintDataDto()
      ..type = PrintTypeEnum.GROUP
      ..align = PrintAlignEnum.LEFT
      ..data = sumPrintData;
  }

  ///金额合计计算
  static String getSumTotal(List<GoodsDetailDto> details) {
    Decimal total = Decimal.zero;
    for (GoodsDetailDto item in details) {
      //由于在后端已经计算过套餐的优惠金额，所有要排除这里，否则会计算错误
      if (StringUtil.isNotZeroOrEmpty(item.comboRowParId)) {
        continue;
      }
      total += MathUtil.add(
        item.currencyDisedTaxedTotal.toString(),
        item.currencyGivePreferentialTotal.toString(),
      );
    }
    return DecimalDisplayHelper.getTotalFixed(total.toString());
  }

  ///税额合计计算
  static String getTaxTotal(List<GoodsDetailDto> details) {
    Decimal taxTotal = Decimal.zero;
    for (GoodsDetailDto item in details) {
      //由于在后端已经计算过套餐的优惠金额，所有要排除这里，否则会计算错误
      if (StringUtil.isNotZeroOrEmpty(item.comboRowParId)) {
        continue;
      }
      taxTotal = MathUtil.add(
        item.currencyTaxTotal.toString(),
        taxTotal.toString(),
      );
    }
    return DecimalDisplayHelper.getTotalFixed(taxTotal.toString());
  }

  ///票装饰
  static Future<void> addDecorateInfo(
    List<String>? selectPrintConfigName,
    List<PrintDataDto> list,
    Map<String, PrintConfigInfo> printConfig,
    String fieldKey,
  ) async {
    ///未开启配置，未选中，打印内容为空都不展示
    bool isShowField = isShowFieldName(
      selectPrintConfigName,
      printConfig,
      fieldKey,
    );
    if (!isShowField) {
      return;
    }
    String content = printConfig[fieldKey]?.contentConfig ?? "";

    if (StringUtil.isEmpty(content)) {
      return;
    }

    PrintDecorateConfig printDecorateConfig = PrintDecorateConfig.fromMap(
      jsonDecode(content),
    );

    if (printDecorateConfig.printStyleMode == PrintStyleModeEnum.image.index) {
      await addImageUrl(
        list,
        selectPrintConfigName,
        printConfig,
        fieldKey,
        printDecorateConfig.imgUrl,
        bytesContent: printConfig[fieldKey]?.imgBytesContent,
        localFile: false,
      );
    } else {
      addNormalRowCheckField(
        list,
        selectPrintConfigName,
        printConfig,
        fieldKey,
        title: "",
        value: printDecorateConfig.content,
        align: PrintAlignEnum.CENTER,
      );
    }
  }

  ///构建图片打印
  static Future<void> addImageUrl(
    List<PrintDataDto> printData,
    List<String>? billPrintList,
    Map<String, PrintConfigInfo> printMapConfig,
    String fieldKey,
    String? url, {
    bool localFile = false,
    int imgWidth = 300,
    String? bytesContent,
    PrintAlignEnum printAlignEnum = PrintAlignEnum.CENTER,
  }) async {
    ///不展示
    if (!PrintTool.isShowFieldName(billPrintList, printMapConfig, fieldKey)) {
      return;
    }

    ///无图片，打印默认图片
    if (StringUtil.isEmpty(url) || url!.contains("blank.gif")) {
      ByteData data = await rootBundle.load("assets/images/no_photo_black.png");
      List<int> bytes = data.buffer.asUint8List();
      printData.add(
        PrintDataDto()
          ..type = PrintTypeEnum.IMAGE
          ..imageWidth = imgWidth
          ..content = base64Encode(bytes)
          ..align = printAlignEnum,
      );
      return;
    }
    if (StringUtil.isEmpty(bytesContent)) {
      bytesContent = await ImageTool.buildImage(
        url,
        localFile: localFile,
        imgWidth: imgWidth,
      );
    }
    printData.add(
      PrintDataDto()
        ..type = PrintTypeEnum.IMAGE
        ..imageWidth = imgWidth
        ..content = bytesContent!
        ..align = printAlignEnum,
    );
  }

  static Future<String> buildImage(
    String url, {
    bool localFile = true,
    int imgWidth = 300,
  }) async {
    Uint8List? imageBytes = Uint8List(0);

    ///本地图片
    if (localFile) {
      if (Platform.isAndroid) {
        imageBytes = await FlutterImageCompress.compressWithFile(
          url,
          quality: 60,
          minWidth: imgWidth,
          minHeight: imgWidth,
          format: CompressFormat.png,
        );
      } else {
        File file = File(url);
        imageBytes = file.readAsBytesSync();
      }
    } else {
      ///网络图片
      try {
        imageBytes =
            (await NetworkAssetBundle(
              Uri.parse(getThumbnailUrl(url)),
            ).load(url)).buffer.asUint8List();
      } catch (e) {
        ByteData data = await rootBundle.load(
          "assets/images/no_photo_black.png",
        );
        Uint8List defaultBytes = data.buffer.asUint8List();
        return base64Encode(await compressImage2(defaultBytes, imgWidth));
      }
      imageBytes =
          (await NetworkAssetBundle(
            Uri.parse(url),
          ).load(url)).buffer.asUint8List();
    }
    return base64Encode(await compressImage2(imageBytes, imgWidth));
  }

  static Future<Uint8List> compressImage(
    Uint8List bytes,
    int targetSize,
  ) async {
    int startTime = DateTime.now().millisecondsSinceEpoch;

    Img.Image image = Img.decodeImage(bytes)!;
    print(
      "*****decodeImage Time${DateTime.now().millisecondsSinceEpoch - startTime}",
    );
    startTime = DateTime.now().millisecondsSinceEpoch;
    print(
      "*****decodeImageFromList Time${DateTime.now().millisecondsSinceEpoch - startTime}",
    );
    startTime = DateTime.now().millisecondsSinceEpoch;
    int oldWidth = image.width;
    int oldHeight = image.height;

    ///当图片尺寸比目标尺寸小，无需压缩
    if (oldWidth < targetSize && oldHeight < targetSize) {
      return bytes;
    }

    num scale = 1;
    int newWidth = 0;
    int newHeight = 0;

    if (oldWidth >= oldHeight) {
      scale = oldWidth / targetSize;
      newWidth = targetSize;
      newHeight = oldHeight ~/ scale;
    } else {
      scale = oldHeight / targetSize;
      newHeight = targetSize;
      newWidth = oldWidth ~/ scale;
    }
    //
    Img.Image resizedImage = Img.copyResize(
      image,
      width: newWidth,
      height: newHeight,
    );
    return Uint8List.fromList(Img.encodePng(resizedImage));
  }

  ///压缩图片大小
  static Future<Uint8List> compressImage2(
    Uint8List? imageBytes,
    int targetSize,
  ) async {
    int quality = 60;

    if (null == imageBytes) {
      return Uint8List(0);
    }

    if (Platform.isAndroid) {
      while (imageBytes!.length > 1024 * 1024 * 0.01 && quality > 0) {
        imageBytes = await FlutterImageCompress.compressWithList(
          imageBytes,
          quality: quality,
          minWidth: targetSize,
          minHeight: targetSize,
          format: CompressFormat.png,
        );
        quality -= 30;
      }
    }

    return imageBytes;
  }

  static List<GoodsDetailDto> getDetails(AbstractBillDto billDto) {
    List<GoodsDetailDto>? goodsDetails;
    if (billDto.vchtype == "GoodsTransInternalBill") {
      //这里本地和jxc叫GoodsTrans,而sale后端枚举中叫GoodsTransInternalBill
      if (billDto is GoodsBillDto) {
        goodsDetails = billDto.outDetail;
      }
    } else if (billDto.vchtype == BillTypeData[BillType.TransferOrder]) {
      if (billDto is OrderBillDTO) {
        goodsDetails = billDto.detail ?? [];
      }
    } else if (billDto.vchtype == BillTypeData[BillType.SaleBill]) {
      if (billDto is GoodsBillDto) {
        goodsDetails = billDto.outDetail;
      }
    } else if (billDto.vchtype == BillTypeData[BillType.SaleBackBill]) {
      if (billDto is GoodsBillDto) {
        goodsDetails = billDto.inDetail;
      }
    }
    return goodsDetails ?? [];
  }

  static Future<String> toQrImageData(
    String data, {
    double imageSize = 380,
  }) async {
    ByteData? image = await QrPainter(
      data: data,
      version: QrVersions.auto,
    ).toImageData(imageSize);
    return base64Encode(image?.buffer.asUint8List() ?? []);
  }

  static _getTimeStr(String? time) {
    if (StringUtil.isEmpty(time)) {
      return "";
    }
    return formatDateStringToLocal(time);
  }

  static String buildInvoiceUrl(AbstractBillDto goodsBillDto) {
    LoginUserModel loginUserModel = LoginCenter.getLoginUser();
    String url =
        "${loginUserModel.requestUrl}/sale/OpenInvoice.html?vchcode=${goodsBillDto.vchcode}&token=${goodsBillDto.invoiceSign}";
    return url;
  }

  static String getSumQty(List<GoodsDetailDto>? details) {
    if (null == details || details.isEmpty) {
      return "0";
    }

    double subQty = 0;
    for (int i = 0; i < details.length; i++) {
      ///非套餐明细才需要加数量
      if (!BillTool.comboDetailRow(details[i])) {
        subQty += details[i].unitQty;
      }
    }
    return DecimalDisplayHelper.getQtyFixed(subQty.toString());
  }

  static String getChangeQty(AbstractBillDto goodsBillDto) {
    if (goodsBillDto is GoodsBillDto) {
      String inDetailQty = getSumQty(goodsBillDto.inDetail);
      String outDetailQty = getSumQty(goodsBillDto.outDetail);
      return "换入($inDetailQty);换出($outDetailQty)";
    }
    return "";
  }

  ///增加 普通左侧标题，右侧值项并验证是否开启
  static void addNormalRowCheckField(
    List<PrintDataDto> list,
    List<String>? billPrintConfig,
    Map<String, PrintConfigInfo> printMapConfig,
    String fieldKey, {
    String? title,
    String? value,
    PrintFontSizeEnum fontSize = PrintFontSizeEnum.SMALL,
    bool isBold = false,
    PrintAlignEnum align = PrintAlignEnum.LEFT,
    double weight = 1,
  }) {
    if (!PrintTool.isShowFieldName(billPrintConfig, printMapConfig, fieldKey)) {
      return;
    }

    ///未传入title，取默认配置
    title ??= PrintTool.buildShowFieldName(printMapConfig, fieldKey);
    _addNormalRowItem(
      list,
      title: title,
      value: value,
      fontSize: fontSize,
      isBold: isBold,
      align: align,
      weight: weight,
    );
  }

  ///强制增加一行数据，不验证是否开启（用于固定需要打印的配置）
  static void _addNormalRowItem(
    List<PrintDataDto> list, {
    String? title,
    String? value,
    PrintFontSizeEnum fontSize = PrintFontSizeEnum.SMALL,
    bool isBold = false,
    PrintAlignEnum align = PrintAlignEnum.LEFT,
    double weight = 1,
  }) {
    String content = StringUtil.isEmpty(title) ? "" : "$title:";
    content += value ?? "";
    return list.add(
      PrintDataDto()
        ..weight = weight
        ..isBold = isBold
        ..fontSize = fontSize
        ..align = align
        ..content = content,
    );
  }

  //endregion

  //region 构建全渠道订单打印
  ///全渠道订单
  static Future<List<PrintDataDto>> _buildChannelBillPrintData(
    BuildContext context,
    BillChannelDetailDto channelDetailDto,
  ) async {
    ///获取单据允许配置的打印项
    List<String> billPrintConfig = PrintFieldConfig.getBillPrintConfigName(
      billType: BillTypeString[BillType.ChannelBill],
    );

    Map<String, PrintConfigInfo> printMapConfig = PrintTool.getBillPrintMap(
      BillTypeData[BillType.ChannelBill] ?? "",
    );

    List<PrintDataDto> printData = [];

    printData.addAll(
      await buildTitlePrintData(
        billPrintConfig,
        _buildGoodsBillDto(channelDetailDto),
        printMapConfig,
      ),
    );

    printData.addAll(
      getGoodsDetail(
        billPrintConfig,
        printMapConfig,
        channelDetailDto.originalDetail ?? [],
        SpTool.getSystemConfig(),
      ),
    );

    printData.addAll(
      await _buildChannelFooterPrintData(
        billPrintConfig,
        printMapConfig,
        channelDetailDto,
      ),
    );
    return printData;
  }

  ///全渠道表头信息
  static AbstractBillDto _buildGoodsBillDto(
    BillChannelDetailDto channelDetailDto,
  ) {
    AbstractBillDto goodsBillDto = AbstractBillDto();
    goodsBillDto.createEfullname = LoginCenter.getLoginUser().etypeName ?? "";
    goodsBillDto.createUserCode = LoginCenter.getLoginUser().userCode ?? "";
    goodsBillDto.number = channelDetailDto.billNumber;
    goodsBillDto.postTime = channelDetailDto.payTime;
    goodsBillDto.memo = channelDetailDto.buyerMessage ?? "";
    goodsBillDto.number = channelDetailDto.billNumber;
    return goodsBillDto;
  }

  ///全渠道订单底部信息
  static Future<List<PrintDataDto>> _buildChannelFooterPrintData(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    BillChannelDetailDto goodsBillDto,
  ) async {
    List<PrintDataDto> list = [];

    ///合计数量
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.sumQty,
      value: getSumQty(goodsBillDto.deliverDetail),
    );

    ///合计金额
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.sumTotal,
      value: StringUtil.double2FixedString(goodsBillDto.disedTaxedTotal ?? 0),
    );

    ///优惠金额
    double num = goodsBillDto.deliverDetail!.fold<double>(
      0,
      (preValue, data) =>
          preValue + double.parse(data.currencyPreferentialTotal.toString()),
    );
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.wTotal,
      value: StringUtil.double2FixedString(num),
    );

    ///买家运费
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.buyerFreight,
      value: StringUtil.double2FixedString(
        goodsBillDto.freightInfo?.buyerFreightFee ?? 0,
      ),
    );

    ///门店信息

    list.addAll(
      await buildShopInfo(
        billPrintConfig,
        printConfig,
        pickupCode: goodsBillDto.pickupCode,
      ),
    );

    ///收货人信息
    list.addAll(buildReceiverInfo(billPrintConfig, printConfig, goodsBillDto));

    ///打印日期
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.printDate,
      value: DateUtil.formatDate(DateTime.now(), format: DateFormats.full),
    );
    return list;
  }

  ///收货人信息（用于全渠道订单）
  static List<PrintDataDto> buildReceiverInfo(
    List<String> billPrintConfig,
    Map<String, PrintConfigInfo> printConfig,
    BillChannelDetailDto goodsBillDto,
  ) {
    List<PrintDataDto> list = [];

    ///收货人姓名
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.receiverName,
      value: goodsBillDto.buyer?.customerReceiver,
    );

    ///收货人姓名
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.receiverMobile,
      value: goodsBillDto.buyer?.customerReceiverMobile,
    );

    ///收货人地址
    addNormalRowCheckField(
      list,
      billPrintConfig,
      printConfig,
      PrintFieldConfig.receiverAddress,
      value: goodsBillDto.buyer?.customerReceiverFullAddress,
    );
    if (list.isNotEmpty) {
      list.add(getLine());
    }
    return list;
  }

  ///提货码
  static addPickCode(
    List<PrintDataDto> list,
    Map<String, dynamic> printConfig,
    String? pickCode,
  ) {
    if (!(printConfig.keys.contains(PrintFieldConfig.pickupCode)) ||
        !(printConfig[PrintFieldConfig.pickupCode] ?? false)) {
      return;
    }
    List<PrintDataDto> pickupCodePrint = [];
    PrintDataDto()
      ..content =
          "${PrintFieldConfig.printConfigNameStr[PrintFieldConfig.pickupCode]}:"
      ..align = PrintAlignEnum.LEFT
      ..weight = 1
      ..let((value) => pickupCodePrint.add(value));
    PrintDataDto()
      ..content = pickCode ?? ""
      ..align = PrintAlignEnum.LEFT
      ..fontSize = PrintFontSizeEnum.MIDDLE
      ..isBold = true
      ..weight = 3
      ..let((value) => pickupCodePrint.add(value));
    PrintDataDto()
      ..type = PrintTypeEnum.GROUP
      ..data = pickupCodePrint
      ..let((value) => list.add(value));
  }

  //endregion

  //region 构建交接班打印数据
  static List<PrintDataDto> _buildShiftChangesData(
    ShiftChangesDto shiftChangesDto,
  ) {
    List<PrintDataDto> printData = [];
    printData.addAll(_buildShiftChangeTopPrintData(shiftChangesDto));
    printData.addAll(_buildShiftChangeCenterPrintData(shiftChangesDto));
    printData.addAll(_buildShiftChangeFootPrintData(shiftChangesDto));
    return printData;
  }

  ///构建交接班的表头收据
  static List<PrintDataDto> _buildShiftChangeTopPrintData(
    ShiftChangesDto shiftChangesDto,
  ) {
    List<PrintDataDto> printData = [];

    // 店铺名称 + 交接班小票
    printData.add(
      PrintDataDto()
        ..content = "${SpTool.getStoreInfo()?.fullname ?? ""} — 交接班小票"
        ..fontSize = PrintFontSizeEnum.MIDDLE
        ..align = PrintAlignEnum.CENTER
        ..isBold = true,
    );

    // 收银员信息
    _addNormalRowItem(
      printData,
      title: "收银员姓名",
      value: LoginCenter.getLoginUser().user,
    );

    _addNormalRowItem(
      printData,
      title: "收银机",
      value: SpTool.getCashierInfo().fullname,
    );

    // 班次时间
    _addNormalRowItem(
      printData,
      title: "班次开始时间",
      value: _getTimeStr(DateUtil.formatDateMs(SpTool.getLoginTime())),
    );
    _addNormalRowItem(
      printData,
      title: "班次结束时间",
      value:
          shiftChangesDto.changeTime != null
              ? _getTimeStr(shiftChangesDto.changeTime)
              : _getTimeStr(DateUtil.getNowDateStr()),
    );

    printData.add(getLine());
    return printData;
  }

  ///交接班的表体数据
  static List<PrintDataDto> _buildShiftChangeCenterPrintData(
    ShiftChangesDto shiftChangesDto,
  ) {
    List<PrintDataDto> printData = [];

    // 【单据】部分
    printData.add(
      PrintDataDto()
        ..content = "【单据】"
        ..isBold = true
        ..align = PrintAlignEnum.LEFT,
    );

    // 将销售、退货、换货三项内容放在一行等分显示改为3行分别显示
    _addNormalRowItem(
      printData,
      title: "销售",
      value: shiftChangesDto.shopOrderCount?.toStringAsFixed(0) ?? "0",
    );
    _addNormalRowItem(
      printData,
      title: "退货",
      value: shiftChangesDto.returnOrderCount?.toStringAsFixed(0) ?? "0",
    );
    _addNormalRowItem(
      printData,
      title: "换货",
      value: shiftChangesDto.exchangeOrderCount?.toStringAsFixed(0) ?? "0",
    );

    printData.add(getLine());

    // 【销售】部分
    printData.add(
      PrintDataDto()
        ..content = "【销售】"
        ..isBold = true
        ..align = PrintAlignEnum.LEFT,
    );

    _addNormalRowItem(
      printData,
      title: "销售出库",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.saleTotal.toString(),
      ),
    );
    _addNormalRowItem(
      printData,
      title: "销售退款",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.returnTotal.toString(),
      ),
    );
    _addNormalRowItem(
      printData,
      title: "销售换货",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.exChangTotal.toString(),
      ),
    );
    _addNormalRowItem(
      printData,
      title: "销售收入",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.saleAmount.toString(),
      ),
    );

    printData.add(getLine());

    // 【储值】部分
    printData.add(
      PrintDataDto()
        ..content = "【储值】"
        ..isBold = true
        ..align = PrintAlignEnum.LEFT,
    );

    _addNormalRowItem(
      printData,
      title: "充值金额",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.recharge.toString(),
      ),
    );
    _addNormalRowItem(
      printData,
      title: "储值退款",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.rechargeReturn.toString(),
      ),
    );

    printData.add(getLine());

    // 【支付方式】部分
    printData.add(
      PrintDataDto()
        ..content = "【支付方式】"
        ..isBold = true
        ..align = PrintAlignEnum.LEFT,
    );

    // 添加各种支付方式
    for (AccountBalanceItem model in PrintTool._getAccountBalanceItemList(
      shiftChangesDto.accountBalanceList,
    )) {
      _addNormalRowItem(
        printData,
        title: model.atypeFullname ?? "",
        value: DecimalDisplayHelper.getTotalFixed(model.total.toString()),
      );
    }

    // 添加标准支付方式
    // _addNormalRowItem(printData,
    //     title: "储值",
    //     value: DecimalDisplayHelper.getTotalFixed(
    //         shiftChangesDto.storedValueAtype.total.toString()));
    // _addNormalRowItem(printData,
    //     title: "现金",
    //     value: DecimalDisplayHelper.getTotalFixed(
    //         shiftChangesDto.cashierAtype.total.toString()));
    // _addNormalRowItem(printData,
    //     title: "聚合支付",
    //     value: DecimalDisplayHelper.getTotalFixed(
    //         shiftChangesDto.payAtype.total.toString()));

    printData.add(getLine());

    // 【钱箱】部分
    printData.add(
      PrintDataDto()
        ..content = "【钱箱】"
        ..isBold = true
        ..align = PrintAlignEnum.LEFT,
    );

    _addNormalRowItem(
      printData,
      title: "收款金额",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.recAmount.toString(),
      ),
    );
    _addNormalRowItem(
      printData,
      title: "退款金额",
      value: DecimalDisplayHelper.getTotalFixed(
        (shiftChangesDto.returnBoxAmount ?? 0).toString(),
      ),
    );
    _addNormalRowItem(
      printData,
      title: "存入金额",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.depositAmount.toString(),
      ),
    );
    _addNormalRowItem(
      printData,
      title: "取出金额",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.takeoutAmount.toString(),
      ),
    );
    _addNormalRowItem(
      printData,
      title: "钱箱余额",
      value: DecimalDisplayHelper.getTotalFixed(
        shiftChangesDto.cashBoxAmount.toString(),
      ),
    );

    return printData;
  }

  static List<AccountBalanceItem> _getAccountBalanceItemList(
    List<AccountBalanceItem> accountBalanceListNotZero,
  ) {
    accountBalanceListNotZero =
        accountBalanceListNotZero
            .where(
              (element) =>
                  element.atypeId != null &&
                  element.atypeId != "********" &&
                  element.atypeId != "********",
            )
            .toList();
    var newMap = groupBy(
      accountBalanceListNotZero,
      (AccountBalanceItem obj) => obj.atypeId,
    );

    List<AccountBalanceItem> temp = [];
    newMap.forEach((key, value) {
      double sum = value
          .map((e) => double.parse(e.total))
          .fold(
            0,
            (num previousValue, element) => double.parse(
              DecimalDisplayHelper.getTotalFixed(
                (previousValue + element).toString(),
              ),
            ),
          );
      temp.add(
        AccountBalanceItem(
          atypeFullname: value[0].atypeFullname ?? "",
          total: sum,
        ),
      );
    });
    return temp;
  }

  static List<PrintDataDto> _buildShiftChangeFootPrintData(
    ShiftChangesDto shiftChangesDto,
  ) {
    List<PrintDataDto> list = [];

    // 在打印时间前添加一行虚线
    list.add(getLine());

    _addNormalRowItem(list, title: "打印时间", value: DateUtil.getNowDateStr());
    return list;
  }

  //endregion

  //region 构建充值打印数据
  ///头部
  static List<PrintDataDto> _buildRechargeTopPrintData() {
    List<PrintDataDto> printData = [];

    printData.add(_buildShopNameDataWithNoConfig());

    _addNormalRowItem(
      printData,
      title: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.cashierName],
      value: LoginCenter.getLoginUser().user,
    );
    _addNormalRowItem(
      printData,
      title: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.chargeTime],
      value: DateUtil.getNowDateStr(),
    );
    printData.add(getLine());
    return printData;
  }

  //充值内容中间
  static List<PrintDataDto> _buildRechargeCenterPrintData({
    required String vipName,
    required String phone,
    required String rechargeMoney,
    required String giveMoney,
    required String beforeMoney,
    required String afterMoney,
    required String payMoney,
    required String atypeNames,
  }) {
    List<PrintDataDto> printData = [];

    _addNormalRowItem(
      printData,
      title: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.vipName],
      value: vipName,
    );
    _addNormalRowItem(
      printData,
      title: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.vipPhone],
      value: phone,
    );
    _addNormalRowItem(
      printData,
      title: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.chargeTotal],
      value: rechargeMoney,
    );
    _addNormalRowItem(
      printData,
      title:
          PrintFieldConfig.printConfigNameStr[PrintFieldConfig.giftStoreTotal],
      value: giveMoney,
    );
    _addNormalRowItem(
      printData,
      title:
          PrintFieldConfig.printConfigNameStr[PrintFieldConfig
              .storeBeforeCharge],
      value: beforeMoney,
    );
    _addNormalRowItem(
      printData,
      title:
          PrintFieldConfig.printConfigNameStr[PrintFieldConfig
              .storeAfterCharge],
      value: afterMoney,
    );
    printData.add(getLine());
    _addNormalRowItem(
      printData,
      title: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.payTotal],
      value: payMoney,
    );
    _addNormalRowItem(
      printData,
      title: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.payment],
      value: atypeNames,
    );
    return printData;
  }

  //尾部
  static List<PrintDataDto> _buildRechargeFootPrintData() {
    List<PrintDataDto> list = [];
    _addNormalRowItem(
      list,
      title: PrintFieldConfig.printConfigNameStr[PrintFieldConfig.printDate],
      value: DateUtil.getNowDateStr(),
    );
    return list;
  }

  //endregion

  //region 通用打印数据构造
  ///双虚线
  static PrintDataDto getDoubleLine() {
    return PrintDataDto()..type = PrintTypeEnum.DOUBLE_LINE;
  }

  ///单虚线
  static PrintDataDto getLine() {
    return PrintDataDto()..type = PrintTypeEnum.LINE;
  }

  ///换行
  static PrintDataDto getLineBreak() {
    return PrintDataDto()..content = " ";
  }

  ///走纸（feedRows :走纸行数）
  static PrintDataDto feed(int feedRows) {
    return PrintDataDto()
      ..type = PrintTypeEnum.EMPTY
      ..lines = feedRows;
  }

  //endregion

  //region  钱箱

  ///打开箱
  static Future<void> openCashBox(BuildContext context) async {
    if (Platform.isAndroid) {
      CashBoxPlugin.openCashBox();
    } else if (Platform.isWindows) {
      PrintConfigDto? printConfig = await _preparePrint(context);
      if (null == printConfig) {
        return;
      }
      if (context.mounted) {
        startOpenCashBox(context);
      }
    }
  }

  //endregion

  ///打印字段名称展示
  static String buildShowFieldName(
    Map<String, PrintConfigInfo> printConfigMap,
    String fieldName, {
    String? defaultShowName,
  }) {
    ///未配置过取默认字段名称
    if (!printConfigMap.containsKey(fieldName)) {
      return defaultShowName ??
          (PrintFieldConfig.printConfigNameStr[fieldName] ?? "");
    }

    ///未配置过取默认字段名称
    PrintConfigInfo? printConfigInfo = printConfigMap[fieldName];
    return buildShowFieldNameByPrintConfig(
      printConfigInfo,
      fieldName,
      defaultShowName: defaultShowName,
    );
  }

  ///打印字段名称展示
  static String buildShowFieldNameByPrintConfig(
    PrintConfigInfo? printConfigInfo,
    String fieldName, {
    String? defaultShowName,
  }) {
    ///未配置过取默认字段名称
    if (null == printConfigInfo ||
        StringUtil.isEmpty(printConfigInfo.customFieldName)) {
      return defaultShowName ??
          (PrintFieldConfig.printConfigNameStr[fieldName] ?? "");
    }
    return printConfigInfo.customFieldName;
  }

  static bool isShowFieldName(
    List<String>? selectPrintConfigName,
    Map<String, PrintConfigInfo> printConfigMap,
    String fieldName,
  ) {
    ///单据未配置字段，不展示
    if (null == selectPrintConfigName ||
        !selectPrintConfigName.contains(fieldName)) {
      return false;
    }

    // ///未配置过模版
    // if (!printConfigMap.containsKey(fieldName) ||
    //     null == printConfigMap[fieldName]) {
    //   ///是否默认显示或不允许取消的配置
    //   return PrintFieldConfig.defaultPrintConfig.contains(fieldName) ||
    //       PrintFieldConfig.unCancelPrintConfig.contains(fieldName);
    // }
    return printConfigMap[fieldName]?.selected ?? false;
  }

  static Map<String, PrintConfigInfo> getBillPrintMap(
    String billType, {
    List<PrintConfigInfo>? printConfigList,
  }) {
    StoreCashier storeCashier = SpTool.getCashierInfo();

    ///未传入配置项，从本地缓存中获取
    printConfigList ??= SpTool.getBillPrintFieldInfo(billType);
    Map<String, PrintConfigInfo> mapPrintConfig = {};

    ///获取单据的可配置的打印项
    List<String> billPrintConfig = PrintFieldConfig.getBillPrintConfigName(
      billType: BillTypeString[billType],
    );

    /// 未通过新方案保存，兼容老方案的
    if (printConfigList.isEmpty) {
      Map<String, dynamic> printConfig = SpTool.getPrintField(billType);

      ///升级本地数据
      for (String item in printConfig.keys) {
        if (!mapPrintConfig.containsKey(item) && printConfig[item]) {
          mapPrintConfig[item] = buildNewPrintConfig(
            item,
            billType,
            storeCashier,
          );
        }
      }
    } else {
      for (PrintConfigInfo item in printConfigList) {
        if (storeCashier.id == item.cashierId && item.printType == billType) {
          mapPrintConfig[item.fieldKey] = item;
        }
      }
    }

    ///处理不能取消的打印字段(必选)
    for (String item in PrintFieldConfig.unCancelPrintConfig) {
      if (!mapPrintConfig.containsKey(item) && billPrintConfig.contains(item)) {
        mapPrintConfig[item] = buildNewPrintConfig(
          item,
          billType,
          storeCashier,
        );
      }
    }

    ///处理默认选中
    for (String item in PrintFieldConfig.defaultPrintConfig) {
      if (!mapPrintConfig.containsKey(item) && billPrintConfig.contains(item)) {
        mapPrintConfig[item] = buildNewPrintConfig(
          item,
          billType,
          storeCashier,
        );
      }
    }
    return mapPrintConfig;
  }

  static PrintConfigInfo buildNewPrintConfig(
    String fieldKey,
    String billType,
    StoreCashier storeCashier,
  ) {
    PrintConfigInfo printConfigDto = PrintConfigInfo();
    printConfigDto.id = BillTool.getVchcode();
    printConfigDto.selected = true;
    printConfigDto.fieldKey = fieldKey;
    printConfigDto.printType = billType;
    printConfigDto.otypeId = storeCashier.otypeId ?? "0";
    printConfigDto.profileId = storeCashier.profileId ?? "0";
    printConfigDto.cashierId = storeCashier.id ?? "0";
    return printConfigDto;
  }

  static PrintGeneralConfig getPrintGeneralConfig(
    PrintConfigInfo? printInfo, {
    int? defaultContent = 0,
  }) {
    if (null == printInfo || StringUtil.isEmpty(printInfo.contentConfig)) {
      return PrintGeneralConfig(defaultValue: defaultContent);
    }
    return PrintGeneralConfig.fromMap(jsonDecode(printInfo.contentConfig));
  }

  ///允许配置打印位置的字段
  static bool isAllowSyncOtherTemplateConfig(String fieldKey) {
    ///打印张数不允许配置
    if (fieldKey == PrintFieldConfig.printCount) {
      return false;
    }

    ///商品编号
    if (fieldKey == PrintFieldConfig.pTypeCode) {
      return true;
    }

    ///编号打印模式
    if (fieldKey == PrintFieldConfig.billNumber) {
      return true;
    }

    ///发票二维码说明
    if (fieldKey == PrintFieldConfig.invoiceCode) {
      return true;
    }

    ///商品名称
    if (fieldKey == PrintFieldConfig.pFullName) {
      return true;
    }

    ///票头票尾
    if (fieldKey == PrintFieldConfig.decorateTop ||
        fieldKey == PrintFieldConfig.decorateTail) {
      return true;
    }

    ///配置了自定义字段的名称的支持
    if (!PrintFieldConfig.unAllowCustomNameConfig.contains(fieldKey)) {
      return true;
    }

    ///配置允许打印位置的字段
    if (PrintFieldConfig.allowPrintLocationConfig.contains(fieldKey)) {
      return true;
    }

    return false;
  }

  ///图片展示
  static Widget buildImageView(
    String? photoUrl, {
    int width = 220,
    bool localFile = true,
  }) {
    //展示默认图片
    if (StringUtil.isEmpty(photoUrl) || photoUrl!.contains("blank.gif")) {
      return Image.asset("assets/images/no_photo.png", width: width.w);
    }
    //本地文件
    if (localFile) {
      return Image.file(File(photoUrl), width: width.w, fit: BoxFit.fitWidth);
    }
    if (!photoUrl.contains("http") && !photoUrl.contains("https")) {
      return Image.asset("assets/images/no_photo.png", width: width.w);
    }
    //网络图片
    return Image.network(
      photoUrl,
      width: width.w,
      fit: BoxFit.fitWidth,
      errorBuilder: (context, error, stackTrace) {
        return Image.asset("assets/images/no_photo.png", width: width.w);
      },
    );
  }
}
