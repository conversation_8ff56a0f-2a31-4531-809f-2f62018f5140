import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:bluetooth_print/bluetooth_print.dart';
import 'package:bluetooth_print/dto/print_config_dto.dart';
import 'package:bluetooth_print/dto/print_data_dto.dart';
import 'package:bluetooth_print/enum/print_align_enum.dart';
import 'package:bluetooth_print/enum/print_font_size_enum.dart';
import 'package:bluetooth_print/enum/print_type_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart'
    show
        CapabilityProfile,
        Generator,
        PaperSize,
        PosAlign,
        PosColumn,
        PosStyles,
        PosTextSize;
import 'package:flutter_pos_printer_platform_image_3/flutter_pos_printer_platform_image_3.dart';
import 'package:flutter_windows_bluetooth/flutter_windows_bluetooth.dart';
import 'package:halo_pos/print/tool/print_config.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:image/image.dart' as img;

import '../../../common/tool/sp_tool.dart';
import '../../../enum/setting/print_width_type.dart';
import "image_utils.dart";

Future startPrint(
  BuildContext context,
  List<PrintDataDto> printData,
  PrintConfigDto printConfig,
) async {
  try {
    await startPrintExecute(printData, printConfig);
  } catch (e) {
    if (context.mounted) {
      HaloToast.show(context, msg: "打印失败，$e");
    }
  }
}

Future startPrintExecute(
  List<PrintDataDto> printData,
  PrintConfigDto printConfig,
) async {
  final setting = SpTool.getSetting();
  if (setting.printerType == PrinterType.usb.index) {
    if (setting.usbPrinter == null) {
      return null;
    }
  } else {
    if (!Platform.isWindows) {
      await BluetoothPrint().print(printData, printConfig);
      return Future.value();
    }
    if (setting.windowsBluetoothPrinter == null) {
      return null;
    }
  }
  final profile = await CapabilityProfile.load();
  final generator = Generator(
    printConfig.pageSize == PrintWidth.w_80mm.name
        ? PaperSize.mm80
        : PaperSize.mm58,
    profile,
    spaceBetweenRows: 0,
  );

  int printCount = printConfig.printCount ?? 1;

  if (setting.printerType == PrinterType.usb.index) {
    try {
      await PrinterManager.instance.connect(
        type: PrinterType.usb,
        model: UsbPrinterInput(
          name: setting.usbPrinter?.name,
          vendorId: setting.usbPrinter?.vendorId,
          productId: setting.usbPrinter?.productId,
        ),
      );
      for (int i = 0; i < printCount; i++) {
        List<int> bytes = _generateList(
          generator,
          printData,
          printTime: (printConfig.printedCount ?? 0) + i + 1,
        );
        //走纸4行
        bytes += generator.feed(4);
        //切纸
        bytes += [0x1B, 0x69];
        await PrinterManager.instance.send(type: PrinterType.usb, bytes: bytes);
      }
    } catch (e) {
      throw ("请检查usb连接");
    }
  } else {
    try {
      for (int i = 0; i < printCount; i++) {
        List<int> bytes = _generateList(
          generator,
          printData,
          printTime: (printConfig.printedCount ?? 0) + i + 1,
        );
        //走纸4行
        bytes += generator.feed(4);
        //切纸
        bytes += [0x1B, 0x69];
        await FlutterWindowsBluetooth().print(
          setting.windowsBluetoothPrinter!.deviceId,
          bytes,
        );
      }
    } catch (e) {
      throw ("请检查蓝牙连接");
    }
  }
}

Future startOpenCashBox(BuildContext context) async {
  final setting = SpTool.getSetting();

  ///打开钱箱指令
  List<int> bytes = [0x1B, 0x70, 0x0, 0x3c, 0xFF];

  if (setting.printerType == PrinterType.usb.index) {
    try {
      await PrinterManager.instance.connect(
        type: PrinterType.usb,
        model: UsbPrinterInput(
          name: setting.usbPrinter?.name,
          vendorId: setting.usbPrinter?.vendorId,
          productId: setting.usbPrinter?.productId,
        ),
      );
      await PrinterManager.instance.send(type: PrinterType.usb, bytes: bytes);
    } catch (e) {
      if (context.mounted) HaloToast.showError(context, msg: "打印失败，请检查usb连接");
    }
  } else {
    try {
      await FlutterWindowsBluetooth().print(
        setting.windowsBluetoothPrinter!.deviceId,
        bytes,
      );
    } catch (e) {
      if (context.mounted) HaloToast.showError(context, msg: "打印失败，请检查蓝牙连接");
    }
  }
}

List<int> _generateList(
  Generator generator,
  List<PrintDataDto> printData, {
  int? printTime,
}) {
  List<int> bytes = [];
  for (var data in printData) {
    switch (data.type) {
      case PrintTypeEnum.TEXT:
        bytes += _generateText(generator, data, printTime: printTime);
        break;
      case PrintTypeEnum.IMAGE:
        bytes += _generateImage(generator, data);
        break;
      case PrintTypeEnum.GROUP:
        bytes += _generateGroup(generator, data);
        break;
      case PrintTypeEnum.LINE:
        bytes += generator.hr();
        break;
      case PrintTypeEnum.DOUBLE_LINE:
        bytes += generator.hr(ch: '=');
        break;
      case PrintTypeEnum.TABLE:
        //todo table暂不支持，另外pos暂时也没用到table类型
        break;
      case PrintTypeEnum.EMPTY:
        bytes += _generateEmpty(generator, data);
    }
  }
  return bytes;
}

List<int> _generateImage(Generator generator, PrintDataDto data) {
  if (TextUtil.isEmpty(data.content)) {
    return [];
  }
  List<int> bytes = [];
  Uint8List imageBytes = base64Decode(data.content);
  // decode the bytes into an image
  final decodedImage = img.decodeImage(imageBytes);
  if (decodedImage == null) {
    return [];
  }
  int oldWidth = decodedImage.width;
  int oldHeight = decodedImage.height;

  ///当图片尺寸比目标尺寸小，无需压缩
  int newWidth = 0;
  int newHeight = 0;
  if (oldWidth < data.imageWidth && oldHeight < data.imageWidth) {
    newWidth = oldWidth;
    newHeight = oldHeight;
  } else {
    num scale = 1;
    if (oldWidth >= oldHeight) {
      scale = oldWidth / data.imageWidth;
      newWidth = data.imageWidth;
      newHeight = oldHeight ~/ scale;
    } else {
      scale = oldHeight / data.imageWidth;
      newHeight = data.imageWidth;
      newWidth = oldWidth ~/ scale;
    }
  }

  img.Image thumbnail = img.copyResize(decodedImage, height: newHeight);
  // creates a copy of the original image with set dimensions
  img.Image originalImg = img.copyResize(
    decodedImage,
    width: newWidth,
    height: newHeight,
    backgroundColor: img.ColorRgb8(255, 255, 255),
  );
  // // fills the original image with a white background
  img.fill(originalImg, color: img.ColorRgb8(255, 255, 255));

  var padding = (originalImg.width - thumbnail.width) / 2;

  //insert the image inside the frame and center it
  drawImage(originalImg, thumbnail, dstX: padding.toInt());

  // convert image to grayscale
  var grayscaleImage = img.grayscale(originalImg);

  bytes += generator.feed(1);
  // bytes += generator.imageRaster(img.decodeImage(imageBytes)!, align: PosAlign.center);
  // todo 该库使用的指令在蓝牙打印机(CS2)上似乎不支持
  // bytes += generator.imageRaster(grayscaleImage, align: PosAlign.center);
  bytes += draw2PxPoint(
    grayscaleImage,
    PrintAlignEnumData[data.align] ?? "center",
  );
  bytes += generator.feed(1);

  return bytes;
}

List<int> _generateText(
  Generator generator,
  PrintDataDto data, {
  int? printTime,
}) {
  if (TextUtil.isEmpty(data.content)) {
    return [];
  }
  String content = data.content;
  if (content.contains(PrintFieldConfig.printCount) && null != printTime) {
    content = content.replaceAll(
      PrintFieldConfig.printCount,
      printTime.toString(),
    );
  }
  return generator.text(
    content,
    styles: _transformStyle(data),
    containsChinese: true,
  );
}

List<int> _generateEmpty(Generator generator, PrintDataDto data) {
  if (null == data.lines || 0 == data.lines) {
    return [];
  }
  return generator.feed(data.lines ?? 0);
}

List<int> _generateGroup(Generator generator, PrintDataDto data) {
  List<PrintDataDto>? childrenData =
      data.data
          ?.where(
            (element) =>
                element.type == PrintTypeEnum.TEXT && element.weight > 0,
          )
          .toList();
  if (childrenData?.isNotEmpty != true) {
    return [];
  }
  List<PrintDataDto> children = childrenData!;
  if (childrenData.length > 12) {
    throw Exception("一个group最多12列");
  }
  //这个库要求一组之和必须为12
  const weightCount = 12;
  double weightSum = children.fold(
    0,
    (previousValue, element) => previousValue + element.weight,
  );
  double scale = weightCount / weightSum;
  List<PosColumn> columns = [];
  for (var child in children) {
    int width = (child.weight * scale).toInt();
    PosColumn column = PosColumn(
      text: child.content,
      width: width,
      styles: _transformStyle(child),
      containsChinese: true,
    );
    columns.add(column);
  }
  _transformWidth(<PosColumn>[...columns], weightCount);
  return generator.row(columns);
}

void _transformWidth(List<PosColumn> list, int widthSum) {
  int sum = 0;
  for (int i = list.length - 1; i >= 0; i--) {
    PosColumn column = list[i];
    //换算过后的宽度小于1，则赋值为1，否则会看不到这个数据
    if (column.width <= 1) {
      column.width = 1;
      list.remove(column);
      widthSum--;
    } else {
      sum += column.width;
    }
  }
  if (sum == widthSum) {
    return;
  } else {
    double scale = widthSum / sum;
    sum = 0;
    for (var column in list) {
      int width = (column.width * scale).toInt();
      column.width = width;
      sum += width;
    }

    int temp = widthSum - sum;

    ///存在未分摊完的数据，将剩余空间从前循环拆分到各item上
    ///
    for (var column in list) {
      if (temp <= 0) {
        return;
      }
      column.width += 1;
      temp--;
    }
    if (temp > 0) {
      _transformWidth(list, widthSum);
    }
  }
}

PosStyles _transformStyle(PrintDataDto data) {
  PosTextSize size = _transformTextSize(data.fontSize);
  final style = PosStyles(
    bold: (data.isBold ?? false),
    height: size,
    width: size,
    align: _transformAlign(data.align),
  );
  return style;
}

PosAlign _transformAlign(PrintAlignEnum? alignOrigin) {
  PosAlign align = PosAlign.left;
  if (alignOrigin == PrintAlignEnum.CENTER) {
    align = PosAlign.center;
  } else if (alignOrigin == PrintAlignEnum.CENTER) {
    align = PosAlign.right;
  }
  return align;
}

PosTextSize _transformTextSize(PrintFontSizeEnum? sizeOrigin) {
  PosTextSize size = PosTextSize.size1;
  if (sizeOrigin == PrintFontSizeEnum.MIDDLE) {
    size = PosTextSize.size2;
  } else if (sizeOrigin == PrintFontSizeEnum.BIG) {
    size = PosTextSize.size3;
  }
  return size;
}
