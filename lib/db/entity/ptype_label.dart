///商品标签关系实体类 - 对应 cf_data_label_ptype 表
class PtypeLabelDO {
  /// 主键ID
  String id;

  /// 账套ID
  String profileId;

  /// 场景字段ID
  String sceneFieldId;

  /// 商品ID (resource_id)
  String resourceId;

  /// 标签值ID
  String labelfieldValueId;

  /// 标签数量（不使用）
  int labelNum;

  /// 创建时间
  String? createTime;

  /// 修改时间
  String? updateTime;

  PtypeLabelDO({
    String? id,
    String? profileId,
    String? sceneFieldId,
    String? resourceId,
    String? labelfieldValueId,
    int? labelNum,
    this.createTime,
    this.updateTime,
  }) : id = id ?? "0",
       profileId = profileId ?? "0",
       sceneFieldId = sceneFieldId ?? "0",
       resourceId = resourceId ?? "0",
       labelfieldValueId = labelfieldValueId ?? "0",
       labelNum = labelNum ?? 0;

  PtypeLabelDO.fromJson(Map<String, dynamic> json)
    : id = json['id']?.toString() ?? "0",
      profileId = json['profileId']?.toString() ?? "0",
      sceneFieldId = json['sceneFieldId']?.toString() ?? "0",
      resourceId = json['resourceId']?.toString() ?? "0",
      labelfieldValueId = json['labelfieldValueId']?.toString() ?? "0",
      labelNum = json['labelNum'] ?? 0,
      createTime = json['createTime'],
      updateTime = json['updateTime'];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'profileId': profileId,
      'sceneFieldId': sceneFieldId,
      'resourceId': resourceId,
      'labelfieldValueId': labelfieldValueId,
      'labelNum': labelNum,
      'createTime': createTime,
      'updateTime': updateTime,
    };
  }

  /// 获取商品ID（兼容原有接口）
  String get ptypeId => resourceId;

  /// 获取标签ID（兼容原有接口）
  String get labelId => labelfieldValueId;
}
