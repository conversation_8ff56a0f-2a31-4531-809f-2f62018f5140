/// 属性值实体类
class PropValueName {
  /// 属性值ID
  String id;

  /// 账套ID
  String profileId;

  /// 属性ID
  String propId;

  /// 属性值名称
  String propvalueName;

  /// 条码
  String barcode;

  /// 备注
  String memo;

  /// 排序索引
  int rowindex;

  /// 是否停用
  bool stoped;

  /// 是否删除
  bool deleted;

  PropValueName({
    String? id,
    String? profileId,
    String? propId,
    String? propvalueName,
    String? barcode,
    String? memo,
    int? rowindex,
    bool? stoped,
    bool? deleted,
  }) : id = id ?? "0",
       profileId = profileId ?? "0",
       propId = propId ?? "0",
       propvalueName = propvalueName ?? "",
       barcode = barcode ?? "",
       memo = memo ?? "",
       rowindex = rowindex ?? 0,
       stoped = stoped ?? false,
       deleted = deleted ?? false;

  static PropValueName fromMap(Map<String, dynamic> map) {
    return PropValueName(
      id: map['id']?.toString(),
      profileId: map['profileId']?.toString(),
      propId: map['propId']?.toString(),
      propvalueName: map['propvalueName']?.toString(),
      barcode: map['barcode']?.toString(),
      memo: map['memo']?.toString(),
      rowindex: map['rowindex'] is int ? map['rowindex'] : int.tryParse(map['rowindex']?.toString() ?? '0') ?? 0,
      stoped: map['stoped'] == true || map['stoped'] == 1,
      deleted: map['deleted'] == true || map['deleted'] == 1,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'profileId': profileId,
    'propId': propId,
    'propvalueName': propvalueName,
    'barcode': barcode,
    'memo': memo,
    'rowindex': rowindex,
    'stoped': stoped,
    'deleted': deleted,
  };
}
