import 'dart:convert';
import 'package:sqflite/sqflite.dart';

import '../bill/tool/encryption_tool.dart';
import '../entity/system/permission_config.dart';
import '../login/model/login_user_model.dart';
import 'database_helper.dart';


/// 创建时间：2023/12/21
/// 作者：xiaotiaochong
/// 描述：用户离线数据

class LoginUserDBManager {
  //登录用户表
  static const String loginUserTable = "login_user";
  //权限表
  static const String permissionConfigTable = "permission_config";

  //region table
  /*
   创建离线 登录用户 表
    */
  static Future<void> createLoginUserTable(Database db) async {
    await db.execute(
        'CREATE TABLE IF NOT EXISTS $loginUserTable ( id INTEGER PRIMARY KEY AUTOINCREMENT,'
        'company VARCHAR(100), '
        'user VARCHAR(100), '
        'pwd VARCHAR(200),'
        'json VARCHAR  )');
  }
  /*
   创建离线 权限 表
    */
  static Future<void> createPermissionConfigTable(Database db) async {
    await db.execute(
        'CREATE TABLE IF NOT EXISTS $permissionConfigTable ( id INTEGER PRIMARY KEY AUTOINCREMENT,'
            'profile_id VARCHAR(100), '
            'json VARCHAR  )');
  }
  //endregion

  //region permission
  static Future<int> insertPermissionConfig({required String profileId,required String json}) async {
    final db = await DatabaseHelper.instance.database;
    List<Map> list = await db.rawQuery(
        'SELECT * FROM $permissionConfigTable'
            ' WHERE profile_id = ?',
        [profileId]);
    if (list.isNotEmpty) {
      return await db.rawUpdate(
          'UPDATE $permissionConfigTable SET profile_id = ?, json = ? WHERE profile_id = ?',
          [profileId, json, profileId]);
    } else {
      return await db.insert(permissionConfigTable,
          {'profile_id': profileId, 'json': json},
          conflictAlgorithm: ConflictAlgorithm.replace);
    }
  }

  static Future<List<PermissionConfig>> selectPermissionConfig(
      {required String profileId,}) async {
    final db = await DatabaseHelper.instance.database;
    List<Map> list = await db.rawQuery(
        'SELECT * FROM $permissionConfigTable'
            ' WHERE profile_id = ?',
        [profileId]);
    return list
        .map((e) => PermissionConfig.fromMap(jsonDecode(e['json'])))
        .toList();
  }

  //endregion

  //region loginuser
  ///json loginUserMode.toJson
  ///<insert> method return id
  static Future<int> insertLoginUser(
      {required String company,
      required String user,
      required String pwd,
      required String json}) async {
    final db = await DatabaseHelper.instance.database;
    List<Map> list = await db.rawQuery(
        'SELECT * FROM $loginUserTable'
        ' WHERE company = ? AND user = ?',
        [company, user]);

    String md5Pwd = EncryptionTool.offlineEnCodeMD5(pwd,company,user);
    if (list.isNotEmpty) {
      return await db.rawUpdate(
          'UPDATE $loginUserTable SET company = ?, user = ? ,pwd = ? , json = ? WHERE company = ? AND user = ?',
          [company, user, md5Pwd, json, company, user]);
    } else {
      return await db.insert(loginUserTable,
          {'company': company, 'user': user, 'pwd': md5Pwd, 'json': json},
          conflictAlgorithm: ConflictAlgorithm.replace);
    }
  }

  static Future<List<LoginUserModel>> selectLoginUser(
      {required String company,
      required String user,
      required String pwd}) async {
    final db = await DatabaseHelper.instance.database;
    String md5Pwd = EncryptionTool.offlineEnCodeMD5(pwd,company,user);
    List<Map> list = await db.rawQuery(
        'SELECT * FROM $loginUserTable'
        ' WHERE company = ? AND user = ? AND pwd = ?',
        [company, user, md5Pwd]);
    return list
        .map((e) => LoginUserModel.fromMap(jsonDecode(e['json'])))
        .toList();
  }

  static Future<List<LoginUserModel>> selectLoginUserAll() async {
    final db = await DatabaseHelper.instance.database;
    List<Map> list = await db.rawQuery(
        'SELECT * FROM $loginUserTable',);
    return list
        .map((e) => LoginUserModel.fromMap(jsonDecode(e['json'])))
        .toList();
  }
  //endregion
}
