import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

import '../bill/entity/goods_bill.dto.dart';
import 'database_helper.dart';

/// 创建时间：2023/12/26
/// 作者：xiaotiaochong
/// 描述：单据离线数据

class BillDBManager {
  //登录用户表
  static const String saleBillTable = "offline_sale_bill";

  //region table
  /*
   创建离线 登录用户 表
    */
  static Future<void> createSaleBillTable(Database db) async {
    await db.execute(
      'CREATE TABLE IF NOT EXISTS $saleBillTable ( id INTEGER PRIMARY KEY AUTOINCREMENT, '
      'profile_id VARCHAR(100), '
      'vchcode VARCHAR(100), '
      'json VARCHAR  )',
    );
  }

  /// 升级离线单据表结构，添加门店字段
  static Future<void> upgradeOfflineSaleBillTable(Database db) async {
    try {
      // 检查是否已存在otype_id字段
      List<Map<String, dynamic>> columns = await db.rawQuery(
        'PRAGMA table_info($saleBillTable)',
      );
      bool hasOtypeId = columns.any((column) => column['name'] == 'otype_id');

      if (!hasOtypeId) {
        // 添加otype_id字段
        await db.execute(
          'ALTER TABLE $saleBillTable ADD COLUMN otype_id VARCHAR(100)',
        );

        // 迁移现有数据：从JSON中提取otypeId并更新到新字段
        await _migrateExistingData(db);
      }

      // 确保索引存在（无论字段是否新添加）
      try {
        await db.execute(
          'CREATE INDEX IF NOT EXISTS idx_offline_sale_bill_profile_otype '
          'ON $saleBillTable (profile_id, otype_id)',
        );
      } catch (indexError) {
        // 索引创建失败不影响功能，只是查询性能可能受影响
        debugPrint('创建离线单据索引失败: $indexError');
      }
    } catch (e) {
      debugPrint('升级离线单据表结构失败: $e');
      // 升级失败不影响正常使用，只是无法按门店过滤
    }
  }

  /// 迁移现有数据，从JSON中提取门店ID
  static Future<void> _migrateExistingData(Database db) async {
    try {
      List<Map> existingRecords = await db.rawQuery(
        'SELECT id, json FROM $saleBillTable WHERE otype_id IS NULL',
      );

      debugPrint('开始迁移离线单据数据，待迁移记录数: ${existingRecords.length}');

      for (Map record in existingRecords) {
        try {
          Map<String, dynamic> billData = jsonDecode(record['json']);
          String? otypeId = billData['otypeId'];

          debugPrint('记录 ${record['id']}: 从JSON中提取的otypeId = $otypeId');

          if (otypeId != null && otypeId.isNotEmpty) {
            int updateCount = await db.update(
              saleBillTable,
              {'otype_id': otypeId},
              where: 'id = ?',
              whereArgs: [record['id']],
            );
            debugPrint('记录 ${record['id']}: 更新成功，影响行数: $updateCount');
          } else {
            debugPrint('记录 ${record['id']}: JSON中没有找到有效的otypeId');
          }
        } catch (e) {
          // 单条记录解析失败不影响其他记录
          debugPrint('迁移单据数据失败，记录ID: ${record['id']}, 错误: $e');
        }
      }

      debugPrint('离线单据数据迁移完成');
    } catch (e) {
      debugPrint('迁移现有离线单据数据失败: $e');
    }
  }

  static Future<int> insertSaleBill({
    required String profileId,
    required String vchcode,
    required String json,
    String? otypeId,
  }) async {
    final db = await DatabaseHelper.instance.database;

    // 确保数据库表结构是最新的
    await upgradeOfflineSaleBillTable(db);

    return await db.insert(saleBillTable, {
      'profile_id': profileId,
      'vchcode': vchcode,
      'otype_id': otypeId,
      'json': json,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  static Future<List<GoodsBillDto>> selectSaleBill({
    required String profileId,
    String? otypeId,
  }) async {
    final db = await DatabaseHelper.instance.database;

    // 确保数据库表结构是最新的
    await upgradeOfflineSaleBillTable(db);

    String whereClause = 'profile_id = ?';
    List<dynamic> whereArgs = [profileId];

    // 如果指定了门店ID，则添加门店过滤条件
    if (otypeId != null && otypeId.isNotEmpty) {
      whereClause += ' AND otype_id = ?';
      whereArgs.add(otypeId);
    }

    List<Map> list = await db.rawQuery(
      'SELECT * FROM $saleBillTable WHERE $whereClause',
      whereArgs,
    );

    return list
        .map((e) => GoodsBillDto.fromMap(jsonDecode(e['json'])))
        .toList();
  }

  /// 专门按门店查询离线单据的方法
  static Future<List<GoodsBillDto>> selectSaleBillByStore({
    required String profileId,
    required String otypeId,
  }) async {
    return await selectSaleBill(profileId: profileId, otypeId: otypeId);
  }

  static Future<int> selectSaleBillCount({
    required String profileId,
    String? otypeId,
  }) async {
    final db = await DatabaseHelper.instance.database;

    // 确保数据库表结构是最新的
    await upgradeOfflineSaleBillTable(db);

    // 先查看所有数据用于调试
    List<Map> allData = await db.rawQuery(
      'SELECT profile_id, otype_id, vchcode FROM $saleBillTable',
    );
    debugPrint('=== 离线单据调试信息 ===');
    debugPrint('总记录数: ${allData.length}');
    debugPrint('查询参数 - profileId: $profileId, otypeId: $otypeId');
    for (var row in allData) {
      debugPrint(
        '数据行: profile_id=${row['profile_id']}, otype_id=${row['otype_id']}, vchcode=${row['vchcode']}',
      );
    }

    String whereClause = 'profile_id = ?';
    List<dynamic> whereArgs = [profileId];

    // 如果指定了门店ID，则添加门店过滤条件
    if (otypeId != null && otypeId.isNotEmpty) {
      whereClause += ' AND otype_id = ?';
      whereArgs.add(otypeId);
    }

    debugPrint(
      '执行查询 SQL: SELECT count(0) as count FROM $saleBillTable WHERE $whereClause',
    );
    debugPrint('查询参数: $whereArgs');

    int? count = Sqflite.firstIntValue(
      await db.rawQuery(
        'SELECT count(0) as count FROM $saleBillTable WHERE $whereClause',
        whereArgs,
      ),
    );

    debugPrint('查询结果: $count');
    debugPrint('=== 调试信息结束 ===');

    return count ?? 0;
  }

  static Future<int> deleteSaleBillList({
    required String profileId,
    required List<String> vchcodeList,
    String? otypeId,
  }) async {
    final db = await DatabaseHelper.instance.database;

    if (vchcodeList.isEmpty) {
      return 0;
    }

    // 构建WHERE条件
    String whereClause =
        'profile_id = ? AND vchcode IN (${List.filled(vchcodeList.length, '?').join(', ')})';
    List<dynamic> whereArgs = [profileId, ...vchcodeList];

    // 如果指定了门店ID，则添加门店过滤条件
    if (otypeId != null && otypeId.isNotEmpty) {
      whereClause += ' AND otype_id = ?';
      whereArgs.add(otypeId);
    }

    return await db.rawDelete(
      'DELETE FROM $saleBillTable WHERE $whereClause',
      whereArgs,
    );
  }

  static Future<int> deleteSaleBillFromProfileId({
    required String profileId,
  }) async {
    final db = await DatabaseHelper.instance.database;
    return await db.rawDelete(
      'DELETE FROM $saleBillTable WHERE profile_id = ?',
      [profileId],
    );
  }
}
