import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:sqflite/sqflite.dart';

import '../../../bill/entity/ptype/ptype_dto.dart';
import '../../../common/num_extension.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../db/database_config.dart';
import '../bill/entity/bill_ptype_request.dart';
import '../bill/entity/goods_detail_dto.dart';
import '../bill/entity/ptype/goods_type.dart';
import '../bill/entity/ptype/ptype_and_sku_dto.dart';
import '../bill/entity/ptype/ptype_unit_dto.dart';
import '../common/login/login_center.dart';
import 'database_helper.dart';

///
///@ClassName: ptype_db_manager
///@Description:
///@Author: tanglan
///@Date: 2023/12/18
class PtypeDbManager {
  static const String ptypeTableName = "ptype";
  static const String ptypeSkuTableName = "ptype_sku";
  static const String comboDetailTableName = "ptype_combo_detail";
  static const String ptypePriceTableName = "ptype_price";
  static const String otypePriceTableName = "ptype_otype_price";
  static const String ptypeStockTableName = "ptype_stock";
  static const String ptypeLimitTableName = "ptype_limit";
  static const String ptypeUnitTableName = "ptype_unit";
  static const String ptypeFullBarcodeTableName = "ptype_fullbarcode";
  static const String ptypeXcodeTableName = "ptype_xcode";
  static const String ptypeLabelTableName = "ptype_label";
  static const String propValueTableName = "prop_value";

  ///创建离线商品主表
  static Future<void> createPtypeTable(Database db, int timeStamp) async {
    final tableName = getTimeSpanTableName(ptypeTableName, timeStamp);
    await db.execute(
      'CREATE TABLE IF NOT EXISTS $tableName '
      '( id TEXT PRIMARY KEY ,'
      'profile_id TEXT,'
      'usercode TEXT, '
      'partypeid TEXT,'
      'snenabled  INTEGER,'
      'propenabled INTEGER,'
      'batchenabled INTEGER,'
      'typeid TEXT,'
      'fullname TEXT,'
      'shortname TEXT,'
      'namepy TEXT,'
      'standard TEXT,'
      'ptype_type TEXT,'
      'tax_rate REAL,'
      'cost_mode INTEGER,'
      'cost_price  REAL,'
      'weight REAL,'
      'weight_unit INTEGER,'
      'sku_price INTEGER,'
      'protect_days INTEGER,'
      'protect_days_unit INTEGER,'
      'pcategory INTEGER,'
      'classed INTEGER,'
      'ktype_limit INTEGER,'
      'barcode TEXT,'
      'pic_url TEXT,'
      'rowindex TEXT'
      '); '
      'CREATE INDEX IF NOT EXISTS idx_classed_typeid ON $tableName(classed, typeid);'
      'CREATE INDEX IF NOT EXISTS idx_barcode ON $tableName(barcode);',
    );
  }

  ///商品价格表
  static Future<void> createPtypePriceTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(ptypePriceTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "ptype_id TEXT,"
      "sku_id TEXT,"
      "unit_id TEXT,"
      "retail_price REAL"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_ptypeId_skuId_unitId ON $tableName(ptype_id, sku_id, unit_id);",
    );
  }

  ///门店价格表
  static Future<void> createOtypePriceTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(otypePriceTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "ptype_id TEXT,"
      "sku_id TEXT,"
      "unit_id TEXT,"
      "xtype_id TEXT,"
      "sale_price REAL,"
      "sale_otype_vip_price REAL"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_ptypeId_skuId_unitId_xtypeId ON $tableName(ptype_id, sku_id, unit_id, xtype_id);",
    );
  }

  ///商品库存
  static Future<void> createPtypeStockTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(ptypeStockTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "ptype_id TEXT,"
      "sku_id TEXT,"
      "ktype_id TEXT,"
      "qty REAL"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_skuId_ktypeId ON $tableName(sku_id, ktype_id);",
    );
  }

  ///商品权限
  static Future<void> createPtypeLimitTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(ptypeLimitTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "ptype_id TEXT,"
      "etype_id TEXT"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_ptypeId_etypeId ON $tableName(ptype_id, etype_id);",
    );
  }

  ///商品sku
  static Future<void> createPtypeSkuTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(ptypeSkuTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "ptype_id TEXT,"
      "prop_id1 TEXT,"
      "prop_name1 TEXT,"
      "propvalue_id1 TEXT,"
      "propvalue_name1 TEXT,"
      "prop_id2 TEXT,"
      "prop_name2 TEXT,"
      "propvalue_id2 TEXT,"
      "propvalue_name2 TEXT,"
      "prop_id3 TEXT,"
      "prop_name3 TEXT,"
      "propvalue_id3 TEXT,"
      "propvalue_name3 TEXT,"
      "prop_id4 TEXT,"
      "prop_name4 TEXT,"
      "propvalue_id4 TEXT,"
      "propvalue_name4 TEXT,"
      "prop_id5 TEXT,"
      "prop_name5 TEXT,"
      "propvalue_id5 TEXT,"
      "propvalue_name5 TEXT,"
      "prop_id6 TEXT,"
      "prop_name6 TEXT,"
      "propvalue_id6 TEXT,"
      "propvalue_name6 TEXT,"
      "rowindex1 INTEGER,"
      "rowindex2 INTEGER,"
      "rowindex3 INTEGER,"
      "rowindex4 INTEGER,"
      "rowindex5 INTEGER,"
      "rowindex6 INTEGER,"
      "pic_url TEXT,"
      "prop_names TEXT,"
      "propvalue_names TEXT"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_ptypeId ON $tableName(ptype_id);",
    );
  }

  ///商品unit
  static Future<void> createPtypeUnitTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(ptypeUnitTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "ptype_id TEXT,"
      "unit_code INTEGER,"
      "unit_name TEXT,"
      "unit_rate REAL,"
      "ptype_weight REAL"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_ptypeId ON $tableName(ptype_id);",
    );
  }

  ///套餐明细表
  static Future<void> createComboDetailTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(comboDetailTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "combo_id TEXT,"
      "ptype_id TEXT,"
      "sku_id TEXT,"
      "unit_id TEXT,"
      "qty REAL,"
      "price REAL,"
      "total REAL,"
      "scale REAL,"
      "gifted INTEGER"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_comboId ON $tableName(combo_id);",
    );
  }

  ///商品条码表
  static Future<void> createFullBarcodeTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(
      ptypeFullBarcodeTableName,
      timeStamp,
    );
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "ptype_id TEXT,"
      "sku_id TEXT,"
      "unit_id TEXT,"
      "fullbarcode TEXT,"
      "defaulted INTEGER"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_ptypeId_skuId_unitId ON $tableName(ptype_id, sku_id, unit_id);"
      "CREATE INDEX IF NOT EXISTS idx_fullbarcode ON $tableName(fullbarcode);",
    );
  }

  ///商品编码表
  static Future<void> createXcodeTable(Database db, int timeStamp) async {
    String tableName = getTimeSpanTableName(ptypeXcodeTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "ptype_id TEXT,"
      "sku_id TEXT,"
      "unit_id TEXT,"
      "xcode TEXT,"
      "defaulted INTEGER,"
      "info_type INTEGER"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_ptypeId_skuId_unitId ON $tableName(ptype_id, sku_id, unit_id);"
      "CREATE INDEX IF NOT EXISTS idx_xcode ON $tableName(xcode);",
    );
  }

  ///创建离线商品标签关系表
  static Future<void> createPtypeLabelTable(Database db, int timeStamp) async {
    final tableName = getTimeSpanTableName(ptypeLabelTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "scene_field_id TEXT,"
      "resource_id TEXT,"
      "labelfield_value_id TEXT,"
      "label_num INTEGER DEFAULT 0,"
      "create_time TEXT,"
      "update_time TEXT"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_resourceId ON $tableName(resource_id);"
      "CREATE INDEX IF NOT EXISTS idx_labelfieldValueId ON $tableName(labelfield_value_id);",
    );
  }

  ///创建属性值表
  static Future<void> createPropValueTable(Database db, int timeStamp) async {
    final tableName = getTimeSpanTableName(propValueTableName, timeStamp);
    await db.execute(
      "CREATE TABLE IF NOT EXISTS $tableName ("
      "id TEXT PRIMARY KEY,"
      "profile_id TEXT,"
      "prop_id TEXT,"
      "propvalue_name TEXT,"
      "barcode TEXT,"
      "memo TEXT,"
      "rowindex INTEGER,"
      "stoped INTEGER,"
      "deleted INTEGER"
      "); "
      "CREATE INDEX IF NOT EXISTS idx_propId ON $tableName(prop_id);"
      "CREATE INDEX IF NOT EXISTS idx_rowindex ON $tableName(rowindex);",
    );
  }

  //获取时间戳表名
  static String getTimeSpanTableName(String tableName, int timeStamp) {
    return "${tableName}_$timeStamp";
  }

  static Future<void> dropPtypeTables({
    Database? db,
    int? excludeTimeStamp,
  }) async {
    db ??= await DatabaseHelper.instance.database;
    List<Map<String, Object?>> selectResult = await db.rawQuery(
      "select name from sqlite_master where type='table' and ("
      "name like '$ptypeTableName%' or "
      "name like '$ptypePriceTableName%' or "
      "name like '$otypePriceTableName%' or "
      "name like '$ptypeFullBarcodeTableName%' or "
      "name like '$ptypeStockTableName%' or "
      "name like '$ptypeLimitTableName%' or "
      "name like '$ptypeSkuTableName%' or "
      "name like '$ptypeUnitTableName%' or "
      "name like '$ptypeXcodeTableName%' or "
      "name like '$ptypeLabelTableName%' or "
      "name like '$propValueTableName%' or "
      "name like '$comboDetailTableName%' )",
    );

    List<String> deleteTables =
        selectResult
            .map((map) {
              final tableName = map["name"];
              if (tableName is String) {
                return tableName;
              }
              return "";
            })
            .where((element) {
              if (excludeTimeStamp != null) {
                return element.isNotEmpty &&
                    !element.endsWith(excludeTimeStamp.toString());
              } else {
                return element.isNotEmpty;
              }
            })
            .toList();

    if (deleteTables.isNotEmpty) {
      Batch batch = db.batch();
      for (var table in deleteTables) {
        batch.execute("drop table if exists $table");
      }
      await batch.commit();
    }
  }

  ///批量插入商品基础信息
  static Future<void> insertPtype(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypeTableName, timeStamp),
      list,
    );
  }

  // //清除商品基础表
  // static Future<void> dropPType() async {
  //   await DatabaseHelper.instance
  //       .dropTable(getTimeSpanTableName(ptypeTableName));
  // }

  ///批量插入商品sku信息
  static Future<void> insertPtypeSku(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypeSkuTableName, timeStamp),
      list,
    );
  }

  //
  // //清除商品基础表
  // static Future<void> dropPTypeSku() async {
  //   await DatabaseHelper.instance
  //       .dropTable(getTimeSpanTableName(ptypeSkuTableName));
  // }

  ///批量插入商品unit信息
  static Future<void> insertPtypeUnit(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypeUnitTableName, timeStamp),
      list,
    );
  }

  ///批量插入套餐明细信息
  static Future<void> insertComboDetail(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(comboDetailTableName, timeStamp),
      list,
    );
  }

  // //清除套餐明细表
  // static Future<void> dropComboDetail() async {
  //   await DatabaseHelper.instance
  //       .dropTable(getTimeSpanTableName(comboDetailTableName));
  // }

  ///批量插入商品库存表
  static Future<void> insertPtypeStock(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypeStockTableName, timeStamp),
      list,
    );
  }

  ///批量更新商品库存表(通过id)
  static Future<void> updatePtypeStock(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    if (list.isEmpty) return;
    Database db = await DatabaseHelper.instance.database;
    Batch batch = db.batch();
    for (var item in list) {
      batch.update(
        getTimeSpanTableName(ptypeStockTableName, timeStamp),
        {"qty": item["qty"]},
        where: "id = ?",
        whereArgs: [item["id"]],
      );
    }
    await batch.commit();
  }

  /// 增减商品的库存
  /// [list中的map如下]
  /// ```JSON
  /// {
  ///   "ptypeId":"",
  ///   "skuId":"",
  ///   "ktypeId":"",
  ///   "qty": 0.0,
  /// }
  /// ```
  /// 需要注意：[qty]有正负数，正为增加库存，负为减少库存。并且在使用时必须按照[unitRate]转换为基本单位的库存数量
  static Future<void> changePtypeStockQty(
    List<Map<String, dynamic>> list,
  ) async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return;
    }
    if (list.isEmpty) return;

    Database db = await DatabaseHelper.instance.database;
    Batch batch = db.batch();
    for (var item in list) {
      String sql =
          "UPDATE ${getTimeSpanTableName(ptypeStockTableName, dbConfig.stockTableTimestamp)} "
          "SET qty = (qty + ${item['qty']}) "
          "WHERE ptype_id = '${item['ptypeId']}' AND sku_id = '${item['skuId']}' AND ktype_id = '${item['ktypeId']}' ";
      batch.rawUpdate(sql.toString());
    }
    await batch.commit();
  }

  //批量插入商品库存表
  // static Future<void> updatePTypeStock(List<Map<String, dynamic>> list) async {
  //   await DatabaseHelper.instance
  //       .batchUpdate(getTimeSpanTableName(ptypeStockTableName), list);
  // }

  // //清除商品库存
  // static Future<void> dropPTypeStock() async {
  //   await DatabaseHelper.instance
  //       .dropTable(getTimeSpanTableName(ptypeStockTableName));
  // }

  ///批量插入商品价格
  static Future<void> insertPtypePrice(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypePriceTableName, timeStamp),
      list,
    );
  }

  // ///清除商品价格
  // static Future<void> dropPTypePrice() async {
  //   await DatabaseHelper.instance
  //       .dropTable(getTimeSpanTableName(ptypePriceTableName));
  // }

  ///批量插入商品门店价格
  static Future<void> insertOtypePrice(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(otypePriceTableName, timeStamp),
      list,
    );
  }

  ///批量插入商品权限
  static Future<void> insertPtypeLimit(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypeLimitTableName, timeStamp),
      list,
    );
  }

  ///批量插入商品条码
  static Future<void> insertFullBarcode(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypeFullBarcodeTableName, timeStamp),
      list,
    );
  }

  ///批量插入商品条码
  static Future<void> insertXcode(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypeXcodeTableName, timeStamp),
      list,
    );
  }

  ///批量插入属性值信息
  static Future<void> insertPropValue(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(propValueTableName, timeStamp),
      list,
    );
  }

  ///批量插入商品标签关系
  static Future<void> insertPtypeLabel(
    List<Map<String, dynamic>> list,
    int timeStamp,
  ) async {
    await DatabaseHelper.instance.batchInsert(
      getTimeSpanTableName(ptypeLabelTableName, timeStamp),
      list,
    );
  }

  ///去重主键
  static const String _primaryKeySQL =
      "$ptypeTableName.id||IFNULL($ptypeSkuTableName.id,0)||IFNULL($ptypeUnitTableName.id,0) AS primaryId, ";

  ///商品信息
  static const String _ptypeInfoSQL =
      //商品信息
      "$ptypeTableName.*, "
      "1 AS count, "
      //套餐
      "(CASE WHEN $ptypeTableName.pcategory = 2 THEN $ptypeTableName.id ELSE NULL END) AS combo_id, "
      "(CASE WHEN $ptypeTableName.pcategory = 2 THEN 1 ELSE 0 END) AS combo_row, ";

  ///条码信息
  ///套餐并没有关联base_ptype_fullbarcode这张表，套餐条码直接取商品表的barcode字段
  static const String _barcodeInfoSQL =
      "(CASE WHEN $ptypeTableName.pcategory != 2 THEN $ptypeFullBarcodeTableName.fullbarcode ELSE $ptypeTableName.barcode END) AS fullbarcode, ";

  ///商品xcode 编码信息
  static const String _xcodeInfoSQL = "$ptypeXcodeTableName.xcode, ";

  ///商品价格和门店价格
  static const String _otypeAndPtypePriceInfoSQL =
      //优先取门店价格本的价格(大于0)，再取零售价
      "(CASE WHEN IFNULL($otypePriceTableName.sale_price,0) > 0 THEN $otypePriceTableName.sale_price ELSE IFNULL($ptypePriceTableName.retail_price,0) END) AS currency_price, "
      //店铺价格本会员价
      "$otypePriceTableName.sale_otype_vip_price, ";

  ///仅包含商品价格，不含门店价格
  static const String _ptypePriceInfoSQL =
      "IFNULL($ptypePriceTableName.retail_price,0) AS currency_price, ";

  ///库存信息
  static const String _stockInfoSQL =
      "IFNULL($ptypeStockTableName.qty,0) AS stock_qty, ";

  ///sku信息SQL
  static const String _skuInfoSQL =
      "$ptypeSkuTableName.id AS sku_id, "
      "$ptypeSkuTableName.prop_id1, $ptypeSkuTableName.prop_name1, $ptypeSkuTableName.propvalue_id1, $ptypeSkuTableName.propvalue_name1, "
      "$ptypeSkuTableName.prop_id2, $ptypeSkuTableName.prop_name2, $ptypeSkuTableName.propvalue_id2, $ptypeSkuTableName.propvalue_name2, "
      "$ptypeSkuTableName.prop_id3, $ptypeSkuTableName.prop_name3, $ptypeSkuTableName.propvalue_id3, $ptypeSkuTableName.propvalue_name3, "
      "$ptypeSkuTableName.prop_id4, $ptypeSkuTableName.prop_name4, $ptypeSkuTableName.propvalue_id4, $ptypeSkuTableName.propvalue_name4, "
      "$ptypeSkuTableName.prop_id5, $ptypeSkuTableName.prop_name5, $ptypeSkuTableName.propvalue_id5, $ptypeSkuTableName.propvalue_name5, "
      "$ptypeSkuTableName.prop_id6, $ptypeSkuTableName.prop_name6, $ptypeSkuTableName.propvalue_id6, $ptypeSkuTableName.propvalue_name6, "
      "$ptypeSkuTableName.pic_url AS sku_pic_url, $ptypeSkuTableName.prop_names, $ptypeSkuTableName.propvalue_names, ";

  ///unit信息SQL
  static const String _unitInfoSQL =
      "$ptypeUnitTableName.id AS unit_id, $ptypeUnitTableName.unit_code, $ptypeUnitTableName.unit_name, $ptypeUnitTableName.unit_rate, $ptypeUnitTableName.ptype_weight, ";

  ///from ptype
  static String _fromPtypeSQL(DatabaseConfig dbConfig) =>
      " ${getTimeSpanTableName(ptypeTableName, dbConfig.ptypeTableTimestamp)} $ptypeTableName ";

  ///left join ptype_sku
  static String _joinSkuSQL(DatabaseConfig dbConfig) =>
      "LEFT JOIN ${getTimeSpanTableName(ptypeSkuTableName, dbConfig.skuTableTimestamp)} $ptypeSkuTableName "
      "ON $ptypeTableName.id = $ptypeSkuTableName.ptype_id ";

  ///left join ptype_unit
  static String _joinUnitSQL(DatabaseConfig dbConfig) =>
      "LEFT JOIN ${getTimeSpanTableName(ptypeUnitTableName, dbConfig.unitTableTimestamp)} $ptypeUnitTableName "
      "ON $ptypeTableName.id = $ptypeUnitTableName.ptype_id ";

  ///left join ptype_fullbarcode
  static String _joinBarcodeSQL(
    DatabaseConfig dbConfig, {
    required String ptypeId,
    String? skuId,
    String? unitId,
    bool defaulted = false,
  }) {
    StringBuffer sb = StringBuffer(
      "LEFT JOIN ${getTimeSpanTableName(ptypeFullBarcodeTableName, dbConfig.fullBarcodeTableTimestamp)} $ptypeFullBarcodeTableName "
      "ON $ptypeId = $ptypeFullBarcodeTableName.ptype_id ",
    );
    if (StringUtil.isNotEmpty(skuId)) {
      sb.write("AND $skuId = $ptypeFullBarcodeTableName.sku_id ");
    }
    if (StringUtil.isNotEmpty(unitId)) {
      sb.write("AND $unitId = $ptypeFullBarcodeTableName.unit_id ");
    }
    if (defaulted) {
      sb.write("AND $ptypeFullBarcodeTableName.defaulted = 1 ");
    }
    return sb.toString();
  }

  static String _joinPtypeXcodeSQL(
    DatabaseConfig dbConfig, {
    required String ptypeId,
    String? skuId,
    String? unitId,
  }) {
    StringBuffer sb = StringBuffer(
      "LEFT JOIN ${getTimeSpanTableName(ptypeXcodeTableName, dbConfig.xcodeTableTimestamp)} $ptypeXcodeTableName "
      "ON $ptypeId = $ptypeXcodeTableName.ptype_id ",
    );
    if (StringUtil.isNotEmpty(skuId)) {
      sb.write("AND $skuId = $ptypeXcodeTableName.sku_id ");
    }
    if (StringUtil.isNotEmpty(unitId)) {
      sb.write("AND $unitId = $ptypeXcodeTableName.unit_id ");
    }
    return sb.toString();
  }

  ///left join ptype_price
  static String _joinPtypePriceSQL(
    DatabaseConfig dbConfig, {
    required String ptypeId,
    required String skuId,
    required String unitId,
  }) =>
      "LEFT JOIN ${getTimeSpanTableName(ptypePriceTableName, dbConfig.ptypePriceTableTimestamp)} $ptypePriceTableName "
      "ON $ptypeId = $ptypePriceTableName.ptype_id "
      "AND $skuId = $ptypePriceTableName.sku_id "
      "AND $unitId = $ptypePriceTableName.unit_id ";

  ///left join ptype_otype_price
  static String _joinOtypePriceSQL(
    DatabaseConfig dbConfig, {
    required String ptypeId,
    required String otypeId,
    required String skuId,
    required String unitId,
  }) =>
      "LEFT JOIN ${getTimeSpanTableName(otypePriceTableName, dbConfig.otypePriceTableTimestamp)} $otypePriceTableName "
      "ON $ptypeId = $otypePriceTableName.ptype_id "
      "AND $skuId = $otypePriceTableName.sku_id "
      "AND $unitId = $otypePriceTableName.unit_id "
      "AND '$otypeId' = $otypePriceTableName.xtype_id ";

  ///left join ptype_stock
  static String _joinStockSQL(
    DatabaseConfig dbConfig, {
    required String ktypeId,
    required String skuId,
  }) =>
      "LEFT JOIN ${getTimeSpanTableName(ptypeStockTableName, dbConfig.stockTableTimestamp)} $ptypeStockTableName "
      "ON $skuId = $ptypeStockTableName.sku_id "
      "AND '$ktypeId' = $ptypeStockTableName.ktype_id ";

  ///left join ptype_limit
  static String _joinPtypeLimitSQL(
    DatabaseConfig dbConfig, {
    required String ptypeId,
    required String etypeId,
  }) {
    return "LEFT JOIN ${getTimeSpanTableName(ptypeLimitTableName, dbConfig.ptypeLimitTableTimestamp)} $ptypeLimitTableName "
        "ON $ptypeLimitTableName.id = $ptypeId "
        "AND '$etypeId' = $ptypeLimitTableName.etype_id ";
  }

  ///是否应该开启商品权限
  ///1.当前用户是管理员，则不开启 (isAdmin = true)
  ///2.当前用户配置了所有商品的权限，则不开启 (ptypeLimited = 0)
  static bool _shouldLimitPtype({
    bool searchByPtypeLimit = true,
    bool isAdmin = false,
    String? etypeId,
    int? ptypeLimited,
  }) {
    return searchByPtypeLimit &&
        !isAdmin &&
        etypeId?.isNotEmpty == true &&
        ptypeLimited != 0;
  }

  ///权限筛选
  static void _limitFilter(
    StringBuffer sql, {
    bool searchByPtypeLimit = true,
    bool isAdmin = false,
    String? etypeId,
    int? ptypeLimited,
  }) {
    //权限筛选
    if (_shouldLimitPtype(
      searchByPtypeLimit: searchByPtypeLimit,
      isAdmin: isAdmin,
      etypeId: etypeId,
      ptypeLimited: ptypeLimited,
    )) {
      sql.write(
        "AND ($ptypeTableName.id = 0 OR $ptypeLimitTableName.ptype_id IS NOT NULL OR $ptypeTableName.pcategory == 2) ",
      );
    }
  }

  ///商品类型筛选
  static void _typeFilter(StringBuffer sql, String? typeId) {
    if (typeId?.isNotEmpty == true) {
      sql.write("AND $ptypeTableName.typeid LIKE '$typeId%' ");
    }
  }

  ///商品名称、商品码，商品拼音码筛选
  static void _ptypeFilter(StringBuffer sql, String filterValue) {
    sql.write(
      "$ptypeTableName.fullname LIKE '%$filterValue%' OR "
      "$ptypeTableName.usercode LIKE '%$filterValue%' OR "
      "$ptypeTableName.namepy LIKE '%$filterValue%' OR ",
    );
  }

  ///unitSku查询sql
  static StringBuffer _selectPtypeSQL({
    required DatabaseConfig dbConfig,
    String? otypeId,
    String? ktypeId,
    String? etypeId,
    String? typeId,
    bool isAdmin = false,
    bool searchByPtypeLimit = true,
    int? ptypeLimited,
  }) {
    StringBuffer sql = StringBuffer();
    //开始查询
    sql.write(
      "SELECT "
      //去重主键
      "$_primaryKeySQL "
      //商品和套餐信息
      "$_ptypeInfoSQL "
      //sku
      "$_skuInfoSQL "
      //unit
      "$_unitInfoSQL "
      //条码信息
      "$_barcodeInfoSQL "
      //条码信息
      "$_xcodeInfoSQL ",
    );

    //价格
    if (otypeId?.isNotEmpty == true) {
      sql.write("$_otypeAndPtypePriceInfoSQL ");
    } else {
      sql.write("$_ptypePriceInfoSQL ");
    }

    //库存
    if (ktypeId?.isNotEmpty == true) {
      sql.write("$_stockInfoSQL ");
    }

    //去除多余的,
    DatabaseHelper.removeEnd(sql, ",");

    //from
    sql.write(
      " FROM "
      //ptype
      "${_fromPtypeSQL(dbConfig)} "
      //sku
      "${_joinSkuSQL(dbConfig)} "
      //unit
      "${_joinUnitSQL(dbConfig)} "
      //barcode
      "${_joinBarcodeSQL(dbConfig, ptypeId: "$ptypeTableName.id", skuId: "$ptypeSkuTableName.id", unitId: "$ptypeUnitTableName.id")} "
      //商品价格表
      "${_joinPtypePriceSQL(
        dbConfig,
        ptypeId: "$ptypeTableName.id",
        unitId: "$ptypeUnitTableName.id",
        //套餐和未开启sku价格管理的情况下，价格表中skuId为0
        skuId: "(CASE WHEN $ptypeTableName.sku_price = 1 THEN $ptypeSkuTableName.id ELSE 0 END)",
      )} "
      //xcode
      "${_joinPtypeXcodeSQL(
        dbConfig,
        ptypeId: "$ptypeTableName.id",
        unitId: "$ptypeUnitTableName.id",
        //套餐和未开启sku价格管理的情况下，价格表中skuId为0
        skuId: "$ptypeSkuTableName.id",
      )} ",
    );

    //门店价格表
    if (otypeId?.isNotEmpty == true) {
      sql.write(
        "${_joinOtypePriceSQL(
          dbConfig,
          otypeId: otypeId!,
          ptypeId: "$ptypeTableName.id",
          unitId: "$ptypeUnitTableName.id",
          //套餐和未开启sku价格管理的情况下，价格表中skuId为0
          skuId: "(CASE WHEN $ptypeTableName.sku_price = 1 THEN $ptypeSkuTableName.id ELSE 0 END)",
        )} ",
      );
    }

    //库存
    if (ktypeId?.isNotEmpty == true) {
      sql.write(
        "${_joinStockSQL(dbConfig, ktypeId: ktypeId!, skuId: "$ptypeSkuTableName.id")} ",
      );
    }

    //商品权限
    if (_shouldLimitPtype(
      searchByPtypeLimit: searchByPtypeLimit,
      isAdmin: isAdmin,
      etypeId: etypeId,
      ptypeLimited: ptypeLimited,
    )) {
      sql.write(
        "${_joinPtypeLimitSQL(dbConfig, ptypeId: "$ptypeTableName.ptype_id", etypeId: etypeId!)} ",
      );
    }

    //where筛选
    sql.write("WHERE $ptypeTableName.classed = 0 ");
    //分类筛选
    _typeFilter(sql, typeId);
    //仅查询启用sku和套餐,套餐sku为空
    sql.write(
      "AND ($ptypeTableName.pcategory = 2 OR $ptypeSkuTableName.id IS NOT NULL) ",
    );

    //权限筛选
    _limitFilter(
      sql,
      searchByPtypeLimit: searchByPtypeLimit,
      isAdmin: isAdmin,
      etypeId: etypeId,
      ptypeLimited: ptypeLimited,
    );

    return sql;
  }

  static String _searchUnitSkuByPtype({
    required DatabaseConfig dbConfig,
    required String? filterValue,
    required bool searchByBarcode,
  }) {
    StringBuffer sql = StringBuffer();
    sql.write(
      "SELECT "
      "$ptypeTableName.id as ptype_id, "
      "$ptypeSkuTableName.id as sku_id, "
      "$ptypeUnitTableName.id as unit_id ",
    );
    sql.write(
      "FROM "
      "${_fromPtypeSQL(dbConfig)} "
      "${_joinSkuSQL(dbConfig)}"
      "${_joinUnitSQL(dbConfig)}",
    );
    if (filterValue?.isNotEmpty == true) {
      sql.write("WHERE ");
      if (!searchByBarcode) {
        _ptypeFilter(sql, filterValue!);
        sql.write("$ptypeTableName.barcode LIKE '%$filterValue%' ");
      } else {
        sql.write(
          "$ptypeTableName.barcode = '$filterValue' "
          "AND $ptypeTableName.classed = 0 ",
        );
      }
    }
    return sql.toString();
  }

  static String _searchPtypeByFullbarcode({
    required DatabaseConfig dbConfig,
    required String? filterValue,
    required bool searchByBarcode,
  }) {
    StringBuffer sql = StringBuffer();
    sql.write(
      "SELECT "
      "$ptypeFullBarcodeTableName.ptype_id as ptype_id, "
      "$ptypeFullBarcodeTableName.sku_id as sku_id, "
      "$ptypeFullBarcodeTableName.unit_id as unit_id ",
    );
    sql.write(
      "FROM ${getTimeSpanTableName(ptypeFullBarcodeTableName, dbConfig.fullBarcodeTableTimestamp)} $ptypeFullBarcodeTableName ",
    );
    if (filterValue?.isNotEmpty == true) {
      sql.write("WHERE ");
      if (!searchByBarcode) {
        sql.write(
          "$ptypeFullBarcodeTableName.fullbarcode LIKE '%$filterValue%' ",
        );
      } else {
        sql.write("$ptypeFullBarcodeTableName.fullbarcode = '$filterValue' ");
      }
    }
    return sql.toString();
  }

  static String _searchPtypeByXcode({
    required DatabaseConfig dbConfig,
    required String? filterValue,
  }) {
    StringBuffer sql = StringBuffer();
    sql.write(
      "SELECT "
      "$ptypeXcodeTableName.ptype_id as ptype_id, "
      "$ptypeXcodeTableName.sku_id as sku_id, "
      "$ptypeXcodeTableName.unit_id as unit_id ",
    );
    sql.write(
      "FROM ${getTimeSpanTableName(ptypeXcodeTableName, dbConfig.xcodeTableTimestamp)} $ptypeXcodeTableName ",
    );
    if (filterValue?.isNotEmpty == true) {
      sql.write("WHERE ");
      sql.write("$ptypeXcodeTableName.xcode = '$filterValue' ");
    }
    return sql.toString();
  }

  ///unitsku商品搜索
  static Future<List<PtypeListModel>> searchUnitSku({
    required int pageIndex,
    required int pageSize,
    required BillPtypeRequest queryParam,
    int? ptypeLimited,
    bool isAdmin = false,
    bool searchByPtypeLimit = true,
  }) async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return [];
    }
    const innerTableName = "inner";
    final otypeId = queryParam.otypeId;
    final ktypeId = queryParam.ktypeId;
    final etypeId = queryParam.etypeId;
    StringBuffer sql = StringBuffer();
    //开始查询
    sql.write(
      "SELECT "
      //去重主键
      "$_primaryKeySQL "
      //商品和套餐信息
      "$_ptypeInfoSQL "
      //sku
      "$_skuInfoSQL "
      //unit
      "$_unitInfoSQL "
      //条码信息
      "$_barcodeInfoSQL "
      //条码信息
      "$_xcodeInfoSQL ",
    );

    //价格
    if (otypeId?.isNotEmpty == true) {
      sql.write("$_otypeAndPtypePriceInfoSQL ");
    } else {
      sql.write("$_ptypePriceInfoSQL ");
    }

    //库存
    if (ktypeId?.isNotEmpty == true) {
      sql.write("$_stockInfoSQL ");
    }

    //去除多余的,
    DatabaseHelper.removeEnd(sql, ",");

    //from
    sql.write(" FROM ");
    if (queryParam.filterValue?.isNotEmpty == true) {
      //先根据筛选条件，查询商品表、条码表、xcode表
      StringBuffer inner = StringBuffer();
      inner.write(
        "SELECT DISTINCT ptype_id,sku_id,unit_id "
        "FROM ( ",
      );
      inner.write(
        "${_searchUnitSkuByPtype(dbConfig: dbConfig, filterValue: queryParam.filterValue, searchByBarcode: queryParam.fullBarCode)} "
        "UNION ALL "
        "${_searchPtypeByFullbarcode(dbConfig: dbConfig, filterValue: queryParam.filterValue, searchByBarcode: queryParam.fullBarCode)} ",
      );
      if (queryParam.fullBarCode && queryParam.skuBarCode?.isNotEmpty == true) {
        inner.write(
          "UNION ALL "
          "${_searchPtypeByXcode(dbConfig: dbConfig, filterValue: queryParam.filterValue)} ",
        );
      }
      inner.write(" ) ");

      sql.write(
        " ( "
        "${inner.toString()}"
        " ) AS $innerTableName ",
      );
      sql.write(
        "INNER JOIN "
        "${_fromPtypeSQL(dbConfig)} "
        "ON $ptypeTableName.id = $innerTableName.ptype_id ",
      );
      sql.write(
        "INNER JOIN "
        "${getTimeSpanTableName(ptypeSkuTableName, dbConfig.skuTableTimestamp)} $ptypeSkuTableName "
        "ON $ptypeSkuTableName.id = $innerTableName.sku_id ",
      );
      sql.write(
        "INNER JOIN "
        "${getTimeSpanTableName(ptypeUnitTableName, dbConfig.unitTableTimestamp)} $ptypeUnitTableName "
        "ON $ptypeUnitTableName.id = $innerTableName.unit_id ",
      );
    } else {
      sql.write(
        //ptype
        "${_fromPtypeSQL(dbConfig)} "
        //sku
        "${_joinSkuSQL(dbConfig)} "
        //unit
        "${_joinUnitSQL(dbConfig)} ",
      );
    }
    sql.write(
      //barcode
      "${_joinBarcodeSQL(dbConfig, ptypeId: "$ptypeTableName.id", skuId: "$ptypeSkuTableName.id", unitId: "$ptypeUnitTableName.id")} "
      //商品价格表
      "${_joinPtypePriceSQL(
        dbConfig,
        ptypeId: "$ptypeTableName.id",
        unitId: "$ptypeUnitTableName.id",
        //套餐和未开启sku价格管理的情况下，价格表中skuId为0
        skuId: "(CASE WHEN $ptypeTableName.sku_price = 1 THEN $ptypeSkuTableName.id ELSE 0 END)",
      )} "
      //xcode
      "${_joinPtypeXcodeSQL(dbConfig, ptypeId: "$ptypeTableName.id", unitId: "$ptypeUnitTableName.id", skuId: "$ptypeSkuTableName.id")} ",
    );

    //门店价格表
    if (queryParam.otypeId?.isNotEmpty == true) {
      sql.write(
        "${_joinOtypePriceSQL(
          dbConfig,
          otypeId: otypeId!,
          ptypeId: "$ptypeTableName.id",
          unitId: "$ptypeUnitTableName.id",
          //套餐和未开启sku价格管理的情况下，价格表中skuId为0
          skuId: "(CASE WHEN $ptypeTableName.sku_price = 1 THEN $ptypeSkuTableName.id ELSE 0 END)",
        )} ",
      );
    }

    //库存
    if (ktypeId?.isNotEmpty == true) {
      sql.write(
        "${_joinStockSQL(dbConfig, ktypeId: ktypeId!, skuId: "$ptypeSkuTableName.id")} ",
      );
    }

    //商品权限
    if (_shouldLimitPtype(
      searchByPtypeLimit: searchByPtypeLimit,
      isAdmin: isAdmin,
      etypeId: etypeId,
      ptypeLimited: ptypeLimited,
    )) {
      sql.write(
        "${_joinPtypeLimitSQL(dbConfig, ptypeId: "$ptypeTableName.ptype_id", etypeId: etypeId!)} ",
      );
    }

    //where筛选
    sql.write("WHERE $ptypeTableName.classed = 0 ");

    //分类筛选
    _typeFilter(sql, queryParam.typeId);

    sql.write(
      //仅查询启用sku和套餐,套餐sku为空
      "AND ($ptypeTableName.pcategory = 2 OR $ptypeSkuTableName.id IS NOT NULL) ",
    );

    //权限筛选
    _limitFilter(
      sql,
      searchByPtypeLimit: searchByPtypeLimit,
      isAdmin: isAdmin,
      etypeId: etypeId,
      ptypeLimited: ptypeLimited,
    );

    //去重
    sql.write("GROUP BY primaryId ");
    //分页
    sql = DatabaseHelper.addSearchLimit(
      pageIndex,
      pageSize,
      sourceSqlBuffer: sql,
    );

    Database db = await DatabaseHelper.instance.database;
    List<Map<String, Object?>> queryResult = await db.rawQuery(sql.toString());
    List<PtypeListModel> result = await changePtypeListModelFromSelectResult(
      queryResult,
    );
    //获取套餐明细
    await getComboDetails(
      result.where((element) => element.comboRow).toList(growable: false),
    );
    return result;
  }

  ///获取套餐明细行
  static Future<void> getComboDetails(List<PtypeListModel> comboList) async {
    if (comboList.isNotEmpty) {
      Map<String, List<PtypeListModel>> comboDetails =
          await getComboDetailsByIds(comboList.map((e) => e.id).toList());
      for (var combo in comboList) {
        combo.comboDetails = comboDetails[combo.id];
      }
    }
  }

  ///根据套餐id查询套餐明细
  static Future<Map<String, List<PtypeListModel>>> getComboDetailsByIds(
    List<String> comboIds,
  ) async {
    if (comboIds.isEmpty) return {};
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return {};
    }
    Database db = await DatabaseHelper.instance.database;
    StringBuffer sql = StringBuffer();
    //开始查询
    sql.write(
      "SELECT "
      //商品信息
      "$ptypeTableName.*, "
      //套餐信息
      "0 AS combo_row, "
      "$comboDetailTableName.combo_id, "
      "$comboDetailTableName.gifted, "
      "$comboDetailTableName.total, "
      "$comboDetailTableName.scale, "
      //数量
      "$comboDetailTableName.qty AS count, "
      //单价
      "IFNULL($comboDetailTableName.price,0) AS currency_price, "
      //sku
      "$_skuInfoSQL "
      //unit
      "$_unitInfoSQL "
      //条码
      "$ptypeFullBarcodeTableName.fullbarcode, ",
    );
    //去除多余的,
    DatabaseHelper.removeEnd(sql, ",");
    //from
    sql.write(
      " FROM "
      //comboDetail
      "${getTimeSpanTableName(comboDetailTableName, dbConfig.comboDetailTableTimestamp)} $comboDetailTableName "
      //ptype
      "JOIN ${_fromPtypeSQL(dbConfig)} ON $ptypeTableName.id = $comboDetailTableName.ptype_id "
      //sku
      "${_joinSkuSQL(dbConfig)} AND $ptypeSkuTableName.id = $comboDetailTableName.sku_id "
      //unit
      "${_joinUnitSQL(dbConfig)} AND $ptypeUnitTableName.id = $comboDetailTableName.unit_id "
      //barcode
      "${_joinBarcodeSQL(dbConfig, defaulted: true, ptypeId: "$ptypeTableName.id", skuId: "$ptypeSkuTableName.id", unitId: "$ptypeUnitTableName.id")} ",
    );
    //where
    sql.write(
      "WHERE $comboDetailTableName.combo_id IN (${comboIds.join(",")}) ",
    );

    List<Map<String, Object?>> queryResult = await db.rawQuery(sql.toString());
    List<PtypeListModel> comboDetails =
        await changePtypeListModelFromSelectResult(queryResult);
    return comboDetails.groupListsBy((comboDetail) => comboDetail.comboId!);
  }

  ///按商品维度搜索商品
  static Future<List<PtypeListModel>> searchPtype({
    required int pageIndex,
    required int pageSize,
    required BillPtypeRequest queryParam,
    bool isAdmin = false,
    int? ptypeLimited,
  }) async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return [];
    }
    Database db = await DatabaseHelper.instance.database;
    StringBuffer sql = StringBuffer();
    //开始查询
    sql.write("SELECT $_ptypeInfoSQL ");
    DatabaseHelper.removeEnd(sql, ",");
    //from
    sql.write("FROM ${_fromPtypeSQL(dbConfig)} ");
    //商品权限
    if (_shouldLimitPtype(
      searchByPtypeLimit: queryParam.ptypeLimit,
      isAdmin: isAdmin,
      etypeId: queryParam.etypeId,
      ptypeLimited: ptypeLimited,
    )) {
      sql.write(
        "${_joinPtypeLimitSQL(dbConfig, ptypeId: "$ptypeTableName.ptype_id", etypeId: queryParam.etypeId!)} ",
      );
    }
    //where
    sql.write("WHERE $ptypeTableName.classed = 0 ");
    //分类筛选
    _typeFilter(sql, queryParam.typeId);
    //权限筛选
    _limitFilter(
      sql,
      searchByPtypeLimit: queryParam.ptypeLimit,
      isAdmin: isAdmin,
      etypeId: queryParam.etypeId,
      ptypeLimited: ptypeLimited,
    );
    //条件筛选
    if (queryParam.filterValue?.isNotEmpty == true) {
      sql.write("AND ");
      _ptypeFilter(sql, queryParam.filterValue!);
      DatabaseHelper.removeEnd(sql, "OR");
    }
    //分页
    sql = DatabaseHelper.addSearchLimit(
      pageIndex,
      pageSize,
      sourceSqlBuffer: sql,
    );

    //查询商品
    List<Map<String, Object?>> queryResult = await db.rawQuery(sql.toString());
    List<PtypeListModel> ptypeResult =
        await changePtypeListModelFromSelectResult(queryResult);

    //根据商品id查询unitSku
    List<PtypeListModel> unitSkuList = await _selectPtypeByIds(
      ptypeIds: ptypeResult.map((e) => e.id).toList(growable: false),
      otypeId: queryParam.otypeId,
      ktypeId: queryParam.ktypeId,
    );

    List<PtypeListModel> comboList = [];
    for (var ptype in ptypeResult) {
      ptype.unitSkuPtypeList ??= [];
      Decimal stockQty = Decimal.parse((ptype.stockQty ?? 0).toString());
      Iterator<PtypeListModel> iterator = unitSkuList.iterator;
      while (iterator.moveNext()) {
        PtypeListModel unitSku = iterator.current;
        //找到该商品对应的unitSku
        if (unitSku.id == ptype.id) {
          //找到该商品最低价
          if (ptype.currencyPrice == null ||
              (unitSku.currencyPrice ?? 0) < ptype.currencyPrice!) {
            ptype.currencyPrice = unitSku.currencyPrice;
          }
          //这里只累加基本单位的库存，避免多单位时，重复累加库存
          //因为每个sku存的库存都是基本单位库存
          if (unitSku.unit?.unitCode == 1) {
            Decimal unitSkuStockQty = Decimal.parse(
              (unitSku.stockQty ?? 0).toString(),
            );
            stockQty += unitSkuStockQty;
          }
          ptype.unitSkuPtypeList!.add(unitSku);
          //套餐
          if (unitSku.pcategory == 2) {
            comboList.add(unitSku);
            break;
          }
        }
      }
      ptype.stockQty = stockQty.toDouble().getIntWhenInteger;
    }
    //获取套餐明细
    await getComboDetails(comboList);
    return ptypeResult;
  }

  ///根据商品id查询商品信息
  static Future<List<PtypeListModel>> _selectPtypeByIds({
    required List<String> ptypeIds,
    String? otypeId,
    String? ktypeId,
  }) async {
    if (ptypeIds.isEmpty) return [];
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return [];
    }
    StringBuffer sql = _selectPtypeSQL(
      dbConfig: dbConfig,
      otypeId: otypeId,
      ktypeId: ktypeId,
    );

    sql.write("AND $ptypeTableName.id IN (${ptypeIds.join(",")}) ");
    //去重
    sql.write("GROUP BY primaryId ");
    Database db = await DatabaseHelper.instance.database;
    List<Map<String, Object?>> queryResult = await db.rawQuery(sql.toString());
    List<PtypeListModel> result = await changePtypeListModelFromSelectResult(
      queryResult,
    );
    return result;
  }

  ///将商品sql查询结果转化为实体类
  static Future<List<PtypeListModel>> changePtypeListModelFromSelectResult(
    List<Map<String, Object?>> sqlResult,
  ) async {
    if (sqlResult.isEmpty) return [];

    // 收集所有需要查询rowindex的属性值对
    List<Map<String, String>> propValuePairs = [];
    for (var e in sqlResult) {
      Map<String, dynamic> camelCaseMap =
          DatabaseHelper.changeUnderscoreCaseMapToCamelCaseMap(e);
      String? skuId = camelCaseMap["skuId"];
      if (skuId?.isNotEmpty == true) {
        // 收集所有属性值对
        for (int i = 1; i <= 6; i++) {
          String? propId = camelCaseMap["propId$i"];
          String? propvalueId = camelCaseMap["propvalueId$i"];
          if (propId?.isNotEmpty == true && propvalueId?.isNotEmpty == true) {
            propValuePairs.add({
              'propId': propId!,
              'propvalueId': propvalueId!,
            });
          }
        }
      }
    }

    // 批量查询rowindex信息
    Map<String, Map<String, int>> rowindexMap = {};
    if (propValuePairs.isNotEmpty) {
      rowindexMap = await getBatchPropValueRowindex(propValuePairs);
    }

    return sqlResult.map((e) {
      Map<String, dynamic> camelCaseMap =
          DatabaseHelper.changeUnderscoreCaseMapToCamelCaseMap(e);
      // 这里数据库使用下划线命名法，而本地数据使用驼峰命名法，抛开[PtypeListModel.unit][PtypeListModel.sku]等特殊嵌套结构需要单独处理外，
      // 其他的均可以使用fromMap直接赋值，省去麻烦
      // todo 由于本地使用的其实都是GoodsDetailDto，而[PtypeListModel]其实是后端返回的类，属于多转了一编，后续可以删除这个类
      PtypeListModel ptype = PtypeListModel.fromMap(camelCaseMap);
      //sku
      String? skuId = camelCaseMap["skuId"];
      if (skuId?.isNotEmpty == true) {
        SkuBean? skuBeanTemp = SkuBean.fromMap(camelCaseMap);
        if (skuBeanTemp == null) return ptype;
        SkuBean skuBean =
            skuBeanTemp
              ..id = skuId
              // ..costPrice = camelCaseMap["skuCostPrice"]
              ..picUrl = camelCaseMap["skuPicUrl"];

        // 填充rowindex信息
        for (int i = 1; i <= 6; i++) {
          String? propId = camelCaseMap["propId$i"];
          String? propvalueId = camelCaseMap["propvalueId$i"];
          if (propId?.isNotEmpty == true && propvalueId?.isNotEmpty == true) {
            int rowindex = rowindexMap[propId]?[propvalueId] ?? 0;
            switch (i) {
              case 1:
                skuBean.rowindex1 = rowindex;
                break;
              case 2:
                skuBean.rowindex2 = rowindex;
                break;
              case 3:
                skuBean.rowindex3 = rowindex;
                break;
              case 4:
                skuBean.rowindex4 = rowindex;
                break;
              case 5:
                skuBean.rowindex5 = rowindex;
                break;
              case 6:
                skuBean.rowindex6 = rowindex;
                break;
            }
          }
        }

        ptype.sku = skuBean;
      }
      //unit
      String? unitId = camelCaseMap["unitId"];
      if (unitId?.isNotEmpty == true) {
        ptype.unit = UnitsBean.fromMap(camelCaseMap)..id = unitId;
      }

      // 获取商品标签信息（异步获取，这里先设置为空，后续通过GoodsScopeUtil.fillGoodsLabelIds填充）
      ptype.labelIds = [];

      return ptype;
    }).toList();
  }

  ///从本地数据查询当前所有商品一二级分类
  static Future<List<GoodsType>> getPtypeAllRootType() async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return [];
    }
    String sql =
        "SELECT typeid,fullname,partypeid "
        "FROM ${getTimeSpanTableName(ptypeTableName, dbConfig.ptypeTableTimestamp)} "
        "WHERE classed = 1 AND LENGTH(typeid) <= 10 "
        "ORDER BY rowindex desc, id ASC ";
    Database db = await DatabaseHelper.instance.database;
    List<Map<String, Object?>> queryResult = await db.rawQuery(sql);
    List<GoodsType> typeList =
        queryResult.map((e) => GoodsType.fromMap(e)).toList();
    List<GoodsType> firstTypeList = [];
    Map<String, List<GoodsType>> secondTypeMap = {};
    for (var type in typeList) {
      if ((type.typeid?.length ?? 0) <= 5) {
        firstTypeList.add(type);
      } else {
        secondTypeMap[type.partypeid!] ??= [];
        secondTypeMap[type.partypeid!]!.add(type);
      }
    }
    for (var type in firstTypeList) {
      type.children.addAll(secondTypeMap[type.typeid!] ?? []);
    }
    return firstTypeList;
  }

  ///查询商品的单位和对应unitSku的fullbarcode列表
  static Future<List<PtypeUnitDto>> getPtypeUnitList(
    String ptypeId,
    String skuId,
  ) async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return [];
    }
    String sql =
        "SELECT $ptypeUnitTableName.id,"
        "$ptypeUnitTableName.ptype_id,"
        "$ptypeUnitTableName.unit_code,"
        "$ptypeUnitTableName.unit_name,"
        "$ptypeUnitTableName.unit_rate,"
        "$ptypeFullBarcodeTableName.fullbarcode AS barcode "
        "FROM ${getTimeSpanTableName(ptypeUnitTableName, dbConfig.unitTableTimestamp)} $ptypeUnitTableName "
        "${_joinBarcodeSQL(dbConfig, ptypeId: "'$ptypeId'", skuId: "'$skuId'", unitId: "$ptypeUnitTableName.id")}"
        "WHERE $ptypeUnitTableName.ptype_id='$ptypeId' ";
    Database db = await DatabaseHelper.instance.database;
    List<Map<String, Object?>> queryResult = await db.rawQuery(sql);
    List<Map<String, dynamic>> list =
        queryResult
            .map((e) => DatabaseHelper.changeUnderscoreCaseMapToCamelCaseMap(e))
            .toList();
    return list.map((e) => PtypeUnitDto.fromJson(e)).toList();
  }

  /// 批量查询商品的价格、库存和条码
  /// 其中[list]中的Map结构如下:
  /// ```JSON
  /// {
  ///   "ptypeId":"",
  ///   "unitId":"",
  ///   "skuId":"",
  ///   "skuPrice": 0
  /// }
  /// ```
  /// 其中，skuPrice为商品是否开启sku定价管理，如果为1，则在价格本中skuId为0;
  static Future<List<Map<String, dynamic>>> selectPriceList({
    required List<Map<String, dynamic>> list,
    required String otypeId,
  }) async {
    if (list.isEmpty) return [];
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return [];
    }
    String tempTableName = "temp";
    String ptypeIdSQL = "$tempTableName.ptype_id";
    String skuIdSQL = "$tempTableName.sku_id";
    String unitIdSQL = "$tempTableName.unit_id";
    StringBuffer sql = StringBuffer();
    sql.write(
      "SELECT "
      "$tempTableName.*, "
      "IFNULL($ptypePriceTableName.retail_price,0) AS retail_price, "
      "IFNULL($otypePriceTableName.sale_price,0) AS otype_price, "
      "IFNULL($otypePriceTableName.sale_otype_vip_price,0) AS vip_price ",
    );
    //去除多余的,
    DatabaseHelper.removeEnd(sql, ",");
    //临时表
    sql.write(" FROM (");
    int i = 0;
    sql.write(
      list
          .map((map) {
            String ptypeId = map["ptypeId"];
            String skuId = map["skuId"];
            String unitId = map["unitId"];
            int? skuPrice = map["skuPrice"];
            //套餐和未开启sku价格管理的情况下，价格表中skuId为0
            if (skuPrice != 1) {
              skuId = "0";
            }
            return "SELECT ${i++} AS primaryId, '$ptypeId' AS ptype_id,'$skuId' AS sku_id,'$unitId' AS unit_id, $skuPrice AS sku_price ";
          })
          .join("UNION ALL "),
    );
    sql.write(") AS $tempTableName ");
    sql.write(
      //商品价格表
      "${_joinPtypePriceSQL(dbConfig, ptypeId: ptypeIdSQL, unitId: unitIdSQL, skuId: skuIdSQL)} "
      //门店价格本
      "${_joinOtypePriceSQL(dbConfig, otypeId: otypeId, ptypeId: ptypeIdSQL, unitId: unitIdSQL, skuId: skuIdSQL)} ",
    );

    //当价格本中没有价格时，不会返回结果，导致前端更改单位或者sku，不会更改价格，应该改为0
    // sql.write(
    //     "WHERE $ptypePriceTableName.id IS NOT NULL OR $otypePriceTableName.id IS NOT NULL ");
    //去重
    sql.write("GROUP BY primaryId ");
    Database db = await DatabaseHelper.instance.database;
    List<Map<String, Object?>> queryResult = await db.rawQuery(sql.toString());
    List<Map<String, dynamic>> result =
        queryResult
            .map((e) => DatabaseHelper.changeUnderscoreCaseMapToCamelCaseMap(e))
            .toList();
    return result;
  }

  ///查询商品的价格信息、库存和条码
  static Future<Map<String, dynamic>?> selectPriceStockAndBarcode({
    required String ptypeId,
    required String skuId,
    required String unitId,
    required String otypeId,
    required int skuPrice,
    String? ktypeId,
  }) async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return null;
    }
    StringBuffer sql = StringBuffer();
    //开始查询
    sql.write(
      "SELECT "
      //条码信息
      "$_barcodeInfoSQL "
      //价格
      "$_otypeAndPtypePriceInfoSQL ",
    );
    //库存
    if (ktypeId?.isNotEmpty == true) {
      sql.write("$_stockInfoSQL ");
    }
    //去除多余的,
    DatabaseHelper.removeEnd(sql, ",");

    String priceSkuId = (skuPrice == 1) ? skuId : "0";
    //from
    sql.write(
      " FROM "
      //ptype
      "${_fromPtypeSQL(dbConfig)} "
      //商品价格表
      "${_joinPtypePriceSQL(
        dbConfig,
        ptypeId: "'$ptypeId'",
        unitId: "'$unitId'",
        //套餐和未开启sku价格管理的情况下，价格表中skuId为0
        skuId: "'$priceSkuId'",
      )} "
      //barcode
      "${_joinBarcodeSQL(dbConfig, ptypeId: "'$ptypeId'", skuId: "'$skuId'", unitId: "'$unitId'")} "
      //门店价格表
      "${_joinOtypePriceSQL(
        dbConfig,
        otypeId: otypeId,
        ptypeId: "'$ptypeId'",
        unitId: "'$unitId'",
        //套餐和未开启sku价格管理的情况下，价格表中skuId为0
        skuId: "'$priceSkuId'",
      )} ",
    );

    //库存
    if (ktypeId?.isNotEmpty == true) {
      sql.write(
        "${_joinStockSQL(dbConfig, ktypeId: ktypeId!, skuId: "'$skuId'")} ",
      );
    }

    sql.write("WHERE $ptypeTableName.id = '$ptypeId' ");

    Database db = await DatabaseHelper.instance.database;
    List<Map<String, Object?>> queryResult = await db.rawQuery(sql.toString());
    List<Map<String, dynamic>> result =
        queryResult
            .map((e) => DatabaseHelper.changeUnderscoreCaseMapToCamelCaseMap(e))
            .toList();
    if (result.isNotEmpty) {
      return result.first;
    }
    return null;
  }

  ///添加或更新门店价格
  static Future<void> addOrUpdateOtypePrice(
    GoodsDetailDto goods,
    String id,
  ) async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return;
    }
    String skuId = goods.skuId ?? "0";
    if (goods.comboRow || goods.skuPrice != 1) {
      skuId = "0";
    }
    String? otypeId = SpTool.getStoreInfo()?.otypeId;
    if (otypeId == null) return;
    StringBuffer sql = StringBuffer();
    sql.write(
      "SELECT * "
      "FROM ${getTimeSpanTableName(otypePriceTableName, dbConfig.otypePriceTableTimestamp)} $otypePriceTableName "
      "WHERE '$id' = $otypePriceTableName.id ",
    );
    Database db = await DatabaseHelper.instance.database;
    List<Map<String, Object?>> queryResult = await db.rawQuery(sql.toString());
    List<Map<String, dynamic>> result =
        queryResult
            // .map((e) => DatabaseHelper.changeUnderscoreCaseMapToCamelCaseMap(e))
            .toList();
    if (result.isNotEmpty) {
      if (goods.currencyPrice == result.first["sale_price"]) {
        return;
      }
      await db.update(
        getTimeSpanTableName(
          otypePriceTableName,
          dbConfig.otypePriceTableTimestamp,
        ),
        {"sale_price": goods.currencyPrice},
        where: "id = ?",
        whereArgs: [id],
      );
    } else {
      await insertOtypePrice([
        {
          "id": id,
          "profile_id": LoginCenter.getLoginUser().profileId ?? "",
          "ptype_id": goods.ptypeId,
          "sku_id": skuId,
          "unit_id": goods.unitId,
          "xtype_id": otypeId,
          "sale_price": goods.currencyPrice,
          "sale_otype_vip_price": 0,
        },
      ], dbConfig.otypePriceTableTimestamp);
    }
  }

  ///根据商品ID查询商品标签
  static Future<List<String>> getPtypeLabelIds(String ptypeId) async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData) {
      return [];
    }

    final tableName = getTimeSpanTableName(
      ptypeLabelTableName,
      dbConfig.ptypeLabelTableTimestamp,
    );
    final db = await DatabaseHelper.instance.database;

    List<Map<String, dynamic>> result = await db.rawQuery(
      "SELECT labelfield_value_id FROM $tableName WHERE resource_id = ?",
      [ptypeId],
    );

    return result.map((e) => e['labelfield_value_id'] as String).toList();
  }

  ///批量获取属性值的rowindex信息
  static Future<Map<String, Map<String, int>>> getBatchPropValueRowindex(
    List<Map<String, String>> propValuePairs,
  ) async {
    DatabaseConfig dbConfig = SpTool.getDatabaseConfig();
    if (!dbConfig.hasPtypeData || propValuePairs.isEmpty) {
      return {};
    }

    Database db = await DatabaseHelper.instance.database;

    // 构建批量查询的SQL
    StringBuffer whereClause = StringBuffer();
    List<String> conditions = [];
    List<String> args = [];

    for (var pair in propValuePairs) {
      conditions.add("(prop_id = ? AND id = ?)");
      args.add(pair['propId'] ?? '');
      args.add(pair['propvalueId'] ?? '');
    }

    whereClause.write(conditions.join(' OR '));

    List<Map<String, Object?>> queryResult = await db.rawQuery(
      "SELECT prop_id, id, rowindex "
      "FROM ${getTimeSpanTableName(propValueTableName, dbConfig.ptypeTableTimestamp)} "
      "WHERE (${whereClause.toString()}) AND deleted = 0",
      args,
    );

    Map<String, Map<String, int>> result = {};
    for (var row in queryResult) {
      String propId = row['prop_id'] as String? ?? '';
      String propvalueId = row['id'] as String? ?? '';
      int rowindex = row['rowindex'] as int? ?? 0;

      if (!result.containsKey(propId)) {
        result[propId] = {};
      }
      result[propId]![propvalueId] = rowindex;
    }

    return result;
  }
}
