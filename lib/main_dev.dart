import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../../../common/keyboard_hidden.dart';
import '../../../plugin/secondary_screen_windows.dart';
import '../../../startup.dart';
import 'application.dart';
import 'hotkey/hotkey_manager.dart';

import 'package:timezone/data/latest.dart' as tz;

///
///
///@ClassName: main_dev
///@Description: 测试启动类
///@Author: tanglan
///@Date: 9/3/21 10:07 AM
///

void main(List<String> args) async {
  debugPaintSizeEnabled = false;
  tz.initializeTimeZones();
  Application.isDebug = true;
  //本地地
  Application.debugAPIURL = "http://**********:10041/sale/";
  if (Platform.isWindows) {
    if (!runSecondaryWindowApp(args)) {
      WidgetsFlutterBinding.ensureInitialized();
      await HotkeyManager.unregisterAllHotkey();
      runApp(const StartUpPage());
    }
  } else {
    TextInputBinding();

    runApp(const StartUpPage());
    // runZonedGuarded<Future<void>>(() async {
    //   ///textInput 重写了flutter framework层拦截
    //   TextInputBinding();
    //
    //   /// 捕获flutter framework 异常
    //   ExceptionReportUtil.initExceptionCatchConfig();
    //   runApp(const StartUpPage());
    // }, (error, stackTrace) {
    //   BuglyPlugin.exceptionReport(error, stackTrace);
    // }, zoneSpecification: ZoneSpecification(
    //   print: (Zone self, ZoneDelegate parent, Zone zone, String line) {
    //     // 所有的打印日志
    //     parent.print(zone, line);
    //   },
    // ));
  }
}
