import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/screenutil/halo_screen_util.dart';
import 'package:haloui/widget/halo_toast.dart';

import 'application.dart';
import 'common/login/login_center.dart';
import 'common/net/http_util.dart';
import 'common/system/system_util.dart';
import 'common/tool/performance_capture_util.dart';
import 'common/tool/sp_custom_util.dart';
import 'common/tool/sp_tool.dart';
import 'common/tool/version_check.dart';
import 'entity/system/permission_config.dart';
import 'login/login_page.dart';
import 'login/model/login_user_model.dart';
import 'login/tool/shop_config_model.dart';
import 'plugin/scanner_plugin.dart';

///
///@ClassName: startup
///@Description: 启动页
///@Author: tanglan

///@Date: 7/27/21 12:59 PM
///
class StartUpPage extends StatelessWidget  with WidgetsBindingObserver {
  const StartUpPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    // 强制横屏
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    return MaterialApp(
        theme: ThemeData(useMaterial3: false),
        localizationsDelegates: const [
          // 本地化的代理类,国际化
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale("zh", "CN")
        ],
        locale: const Locale("zh", "CN"),
        home: const WelcomeWidget(),
        builder: FlutterSmartDialog.init(),
        navigatorKey: Application.navigatorKey,
        scrollBehavior: Platform.isWindows ? WindowsScrollBehavior() : null);
  }

}


class WelcomeWidget extends StatefulWidget {
  const WelcomeWidget({Key? key}) : super(key: key);

  @override
  State<WelcomeWidget> createState() {
    return _WelcomeWidgetState();
  }
}

class _WelcomeWidgetState extends State<WelcomeWidget> {
  @override
  void initState() {
    // Future.delayed(const Duration(seconds: 1), () {
    //check state
    WidgetsBinding.instance
        .addPostFrameCallback((_) => _loadSyncDataAndForward(context));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      height: double.infinity,
      width: double.infinity,
      alignment: Alignment.center,
      child: const Image(image: AssetImage("assets/images/startup.png")),
    );
  }

  Future<void> _loadSyncDataAndForward(context) async {
    try {
      Application.init(context);
      SystemUtil.bindWindowClose();
      LoginCenter.initLoginCenter(Application.isDebug);
      await SpCustomUtil.getInstance();
      // LoginUserModel? loginUser = LoginCenter.getLoginUser();
      HttpUtil.getInstance(context);
      ScannerPlugin.init();
      // bool isUnAutoLogin = StringUtil.isEmpty(loginUser.authorization) ||
      //     null == SpTool.getStoreInfo()?.otypeId ||
      //     null == SpTool.getCashierInfo().id;
      //
      // if (isUnAutoLogin || !SpTool.getAutoLogin()!) {
      //   NavigateUtil.navigateTo(context, const LoginPage(), replace: true);
      // } else {
      //   PermissionConfig? permissionConfig =
      //       await ShopConfigModel.getSysConfigAndPermission(context);
      //   if (!(permissionConfig?.module?["storeFunc"] ?? false)) {
      //     if (context.mounted) {
      //       HaloToast.show(context, msg: "未开通云零售模块，请联系管理员!");
      //       NavigateUtil.navigateTo(context, const LoginPage(), replace: true);
      //     }
      //     return;
      //   }
      //   await SpTool.saveVersionInfo(permissionConfig?.versionInfo);
      //   await SpTool.savePermission(permissionConfig?.permissions);
      //   await SpTool.saveQiNiuConfig(permissionConfig?.qiniuConfigDTO);
      //   await ShopConfigModel.savePermission(context);
      //   try {
      //     Platform.resolvedExecutable;
      //     await VersionCheck.checkVersionUpdate(
      //         context, permissionConfig?.versionInfo);
      //   } catch (e) {
      // NavigateUtil.navigateTo(context, const LoginPage(), replace: true);
      //     return;
      //   }
      //   NavigateUtil.navigateTo(context, VersionCheck.getVersionRootWidget(),
      //       replace: true);
      // }
    } finally {
      NavigateUtil.navigateTo(context, const LoginPage(), replace: true);
    }
  }
}

class WindowsScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices =>
      {...super.dragDevices, PointerDeviceKind.mouse};
}
