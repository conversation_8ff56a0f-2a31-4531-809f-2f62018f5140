import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:timezone/data/latest.dart' as tz;

import 'application.dart';
import 'common/keyboard_hidden.dart';
import 'hotkey/hotkey_manager.dart';
import 'plugin/exception_report_util.dart';
import 'plugin/secondary_screen_windows.dart';
import 'startup.dart';

void main(List<String> args) async {
  debugPaintSizeEnabled = false;
  Application.isDebug = false;
  tz.initializeTimeZones();
  if (Platform.isWindows) {
    if (!runSecondaryWindowApp(args)) {
      WidgetsFlutterBinding.ensureInitialized();
      await HotkeyManager.unregisterAllHotkey();
      runApp(const StartUpPage());
    }
  } else {
    runZonedGuarded<Future<void>>(
      () async {
        ///textInput 重写了flutter framework层拦截
        TextInputBinding();

        /// 捕获flutter framework 异常
        ExceptionReportUtil.initExceptionCatchConfig();
        runApp(const StartUpPage());
      },
      (error, stackTrace) {
        BuglyPlugin.exceptionReport(error, stackTrace);
      },
      zoneSpecification: ZoneSpecification(
        print: (Zone self, ZoneDelegate parent, Zone zone, String line) {
          // 所有的打印日志
          parent.print(zone, line);
        },
      ),
    );
  }
}
