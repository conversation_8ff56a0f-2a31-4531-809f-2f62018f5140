import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/screenutil/halo_screen_util.dart';
// import 'package:halo_pos_lower/application.dart' as lower_applicaiton;

import 'login/login_page.dart';

///
///@ClassName: application
///@Description: 程序类
///@Author: tanglan
///@Date: 7/27/21 1:04 PM
class Application {
  static int currentClientApiVersion = 520;
  static bool isDebug = false;
  static String? debugAPIURL;
  static String? proxyAddress = ""; //测试代理地址
  static Function(BuildContext)? loginOut;

  ///MaterialApp中的navigator的key，用来全局获取context
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  static void init(context) {
    var boxConstraints = BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width,
        maxHeight: MediaQuery.of(context).size.height);
    HaloScreenUtil.init(boxConstraints, designSize: const Size(1920, 1080));
    ScreenUtil.ensureScreenSizeAndInit(context, designSize: const Size(1920, 1080));

    // lower_applicaiton.Application.loginOut = (lowerContext) => {
    //       Navigator.of(lowerContext).pushAndRemoveUntil(
    //           MaterialPageRoute(builder: (lowerContext) => const LoginPage()),
    //           (route) => false)
    //     };
  }
}
