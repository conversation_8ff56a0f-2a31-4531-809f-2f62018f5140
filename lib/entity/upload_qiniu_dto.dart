class UploadQiniuDto {
  String? domain;
  String? key;
  String? originalFilename;
  String? thumbnail;
  String? url;

  UploadQiniuDto();

  factory UploadQiniuDto.fromMap(Map<String, dynamic>? map) {
    if (map == null) return UploadQiniuDto();
    UploadQiniuDto uploadDtoBean = UploadQiniuDto();
    uploadDtoBean.domain = map['domain'];
    uploadDtoBean.key = map['key'];
    uploadDtoBean.originalFilename = map['originalFilename'];
    uploadDtoBean.thumbnail = map['thumbnail'];
    uploadDtoBean.url = map['url'];
    return uploadDtoBean;
  }

  Map toJson() => {
        "domain": domain,
        "key": key,
        "originalFilename": originalFilename,
        "thumbnail": thumbnail,
        "url": url,
      };
}
