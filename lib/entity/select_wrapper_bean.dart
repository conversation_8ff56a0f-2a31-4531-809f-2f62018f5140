///多选包装类
class SelectWrapperBean<T> {
  ///数据
  final T data;

  ///是否可勾选
  final bool enable;

  ///当前是否选中
  bool selected = false;

  SelectWrapperBean(
      {required this.data, this.selected = false, this.enable = true});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SelectWrapperBean &&
          runtimeType == other.runtimeType &&
          data == other.data;

  @override
  int get hashCode => data.hashCode;
}
