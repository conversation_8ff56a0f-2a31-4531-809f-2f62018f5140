import '../../../common/base_type_util.dart';

///对应后端PageHelper中的PageInfo
class PageInfo<T> {
  //当前页
  int pageNum;

  //每页的数量
  int pageSize;

  //当前页的数量
  int size;

  //总页数
  int pages;

  //是否为第一页
  bool isFirstPage;

  //是否为最后一页
  bool isLastPage;

  //是否有前一页
  bool hasPreviousPage;

  //是否有下一页
  bool hasNextPage;

  //总条数
  int total;

  //结果集
  List<T> list;

  PageInfo({
    int? pageNum,
    int? pageSize,
    int? size,
    int? pages,
    bool? isFirstPage,
    bool? isLastPage,
    bool? hasPreviousPage,
    bool? hasNextPage,
    int? total,
    List<T>? list,
  })  : pageNum = pageNum ?? 0,
        pageSize = pageSize ?? 0,
        size = size ?? 0,
        pages = pages ?? 0,
        isFirstPage = isFirstPage ?? false,
        isLastPage = isLastPage ?? false,
        hasPreviousPage = hasPreviousPage ?? false,
        hasNextPage = hasNextPage ?? false,
        total = total ?? 0,
        list = list ?? <T>[];

  Map<String, dynamic> toJson() {
    return {
      'pageNum': pageNum,
      'pageSize': pageSize,
      'size': size,
      'pages': pages,
      'isFirstPage': isFirstPage,
      'isLastPage': isLastPage,
      'hasPreviousPage': hasPreviousPage,
      'hasNextPage': hasNextPage,
      'total': total,
      'list': list,
    };
  }

  ///list和mapper二选一即可
  factory PageInfo.fromMap(Map<String, dynamic> map,
      {List<T>? list, T Function(Map<String, dynamic> json)? mapper}) {
    if (list == null && mapper != null) {
      list = (map['list'] as List?)?.map((e) => mapper(e)).toList();
    }
    return PageInfo(
      pageNum: map['pageNum'],
      pageSize: map['pageSize'],
      size: map['size'],
      pages: map['pages'],
      isFirstPage: map['isFirstPage'],
      isLastPage: map['isLastPage'],
      hasPreviousPage: map['hasPreviousPage'],
      hasNextPage: map['hasNextPage'],
      total: BaseTypeUtil.toInt(map['total']),
      list: list,
    );
  }
}
