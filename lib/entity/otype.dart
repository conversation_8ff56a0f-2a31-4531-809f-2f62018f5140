///销售机构
class Otype {
  Otype({
    this.id,
    this.profileId,
    this.fullname,
    this.enable,
    this.ocategory,
  });

  Otype.fromJson(dynamic json) {
    id = json['id'];
    profileId = json['profileId'];
    fullname = json['fullname'];
    enable = json['enable'];
    ocategory = json['ocategory'];
  }

  ///销售机构id
  String? id;

  ///账套id
  String? profileId;

  ///销售机构名称
  String? fullname;

  ///是否启用
  bool? enable;

  ///
  int? ocategory;

  Otype copyWith({
    String? id,
    String? profileId,
    String? fullname,
    bool? enable,
    int? ocategory,
  }) =>
      Otype(
        id: id ?? this.id,
        profileId: profileId ?? this.profileId,
        fullname: fullname ?? this.fullname,
        enable: enable ?? this.enable,
        ocategory: ocategory ?? this.ocategory,
      );

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['profileId'] = profileId;
    map['fullname'] = fullname;
    map['enable'] = enable;
    map['ocategory'] = ocategory;
    return map;
  }
}
