/// fullVersionStr : ""
/// updateUrl : ""

class VersionInfo {
  ///安装包下载链接，注意需要和"_versionName.apk"进行拼接
  String updateUrl;

  ///是否强制升级
  bool forceUpdateAndroid;

  ///versionCode|versionName|版本信息
  String fullVersionAndroid;

  ///服务器版本号，若[Application.currentClientApiVersion]大于当前服务器的版本编号，
  ///则说明该用户账套还未升级到当前包对应的账套版本，则使用旧包内容。
  int apiVersion;

  ///当前账套允许的最低app版本号，若低于此版本号，则强制升级
  String minVersion;

  ///埋点数据上传地址
  String reportUrl;

  VersionInfo.fromMap(Map<String, dynamic> map)
      : forceUpdateAndroid = map['forceUpdateAndroid'] ?? false,
        fullVersionAndroid = map['fullVersionAndroid'] ?? "",
        updateUrl = map['updateUrl'] ?? "",
        minVersion = map['minVersion'] ?? "",
        apiVersion = map['apiVersion'] ?? 500,
        reportUrl = map["reportUrl"] ?? "";

  String get versionCode {
    List? strList = fullVersionAndroid.split("|");
    if (strList.length < 3) {
      return "";
    }
    return strList[0];
  }

  String get versionName {
    List? strList = fullVersionAndroid.split("|");
    if (strList.length < 3) {
      return "";
    }
    return strList[1];
  }

  String get updateMsg {
    List? strList = fullVersionAndroid.split("|");
    if (strList.length < 3) {
      return "";
    }
    String msg = strList[2].substring(0, strList[2].length - 1);
    return msg;
  }

  Map toJson() => {
        "updateUrl": updateUrl,
        "fullVersionAndroid": fullVersionAndroid,
        "forceUpdateAndroid": forceUpdateAndroid,
        "minVersion": minVersion,
        "apiVersion": apiVersion,
        "reportUrl": reportUrl
      };
}
