/// btypeId : "0000100020"
/// ktypeFullname : "默认仓库"
/// ktypeId : "00001"
/// btypeFullname : " 线下配送"

class ShopConfigEntity {
  String? btypeId;
  String? ktypeFullname;
  String? ktypeId;
  String? btypeFullname;
  String? noBarPtypeId;

  String? noBarPtypeFullname;

  static ShopConfigEntity? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    ShopConfigEntity shopConfigEntityBean = ShopConfigEntity();
    shopConfigEntityBean.btypeId = map['btypeId'];
    shopConfigEntityBean.ktypeFullname = map['ktypeFullname'];
    shopConfigEntityBean.ktypeId = map['ktypeId'];
    shopConfigEntityBean.btypeFullname = map['btypeFullname'];
    shopConfigEntityBean.noBarPtypeId = map["noBarPtypeId"];
    shopConfigEntityBean.noBarPtypeFullname = map["noBarPtypeFullname"];
    return shopConfigEntityBean;
  }

  Map toJson() => {
        "btypeId": btypeId,
        "ktypeFullname": ktypeFullname,
        "ktypeId": ktypeId,
        "btypeFullname": btypeFullname,
        "noBarPtypeId": noBarPtypeId,
        "noBarPtypeFullname": noBarPtypeFullname
      };
}
