import '../../print/tool/print_config.dart';

///
///@ClassName: print_decorate_config
///@Description:打印票头票尾装饰
///@Author: tanglan
///@Date: 2025/4/29
class PrintDecorateConfig {
  ///装饰打印类型 0=文本 1=图片
  int printStyleMode = PrintStyleModeEnum.text.index;
  String content = "";

  ///门店id
  String imgUrl = "";

  PrintDecorateConfig();

  factory PrintDecorateConfig.fromMap(Map<String, dynamic>? map) {
    if (map == null) return PrintDecorateConfig();
    PrintDecorateConfig printConfigDto = PrintDecorateConfig();
    printConfigDto.printStyleMode =
        map['printStyleMode'] ??= PrintStyleModeEnum.text.index;
    printConfigDto.content = map['content'] ?? "";
    printConfigDto.imgUrl = map['imgUrl'] ?? "";
    return printConfigDto;
  }

  Map toJson() =>
      {"printStyleMode": printStyleMode, "content": content, "imgUrl": imgUrl};
}
