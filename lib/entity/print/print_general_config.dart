///
///@ClassName: print_count_config
///@Description: 打印张数配置
///@Author: tanglan
///@Date: 2025/4/29
class PrintGeneralConfig {
  int content = 1;
  String text = "";

  PrintGeneralConfig({int? defaultValue}) {
    content = defaultValue ?? 0;
  }

  factory PrintGeneralConfig.fromMap(Map<String, dynamic>? map) {
    if (map == null) return PrintGeneralConfig();

    PrintGeneralConfig printGeneralConfig = PrintGeneralConfig();
    printGeneralConfig.content = map['content'] ?? 0;
    printGeneralConfig.text = map['text'];
    return printGeneralConfig;
  }

  Map toJson() => {"content": content, "text": text};
}
