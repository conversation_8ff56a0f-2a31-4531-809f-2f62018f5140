
import '../../bill/entity/etype_list_dto.dart';
import '../update/version_info.dart';

///
///@ClassName: module_version
///@Description: 资产开关和api版本信息
///@Author: tanglan
///@Date: 2024/1/2
class ModuleVersion {
  Map<String, dynamic>? module;
  VersionInfo? versionInfo;
  EtypeItemDto? employeeInfo;

  static ModuleVersion fromMap(Map<String, dynamic> map) {
    ModuleVersion moduleVersion = ModuleVersion();
    moduleVersion.module = map['module'];
    if (map['versionInfo'] != null) {
      moduleVersion.versionInfo =  VersionInfo.fromMap(map["versionInfo"]);
    }
    moduleVersion.employeeInfo = EtypeItemDto.fromMap(map["employeeInfo"]);
    return moduleVersion;
  }

  Map toJson() => {
        "module": module,
        "versionInfo": versionInfo,
        "employeeInfo": employeeInfo
      };
}
