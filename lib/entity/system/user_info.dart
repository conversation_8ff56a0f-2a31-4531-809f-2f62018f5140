/// fullname : "管理员"
/// shortname : "管理员"
/// isadmin : true
/// companyName : "hb"
/// gwProductId : 36
/// groupId : 31

class UserInfo {
  String? fullname;
  String? shortname;
  bool? isadmin;
  String? companyName;
  int? gwProductId;
  int? groupId;

  ///是否开启商品权限搜索
  int? ptypeLimited;

  static UserInfo? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    UserInfo userInfoBean = UserInfo();
    userInfoBean.fullname = map['fullname'];
    userInfoBean.shortname = map['shortname'];
    userInfoBean.isadmin = map['isadmin'];
    userInfoBean.companyName = map['companyName'];
    userInfoBean.gwProductId = map['gwProductId'];
    userInfoBean.groupId = map['groupId'];
    userInfoBean.ptypeLimited = map['ptypeLimited'];
    return userInfoBean;
  }

  Map toJson() => {
        "fullname": fullname,
        "shortname": shortname,
        "isadmin": isadmin,
        "companyName": companyName,
        "gwProductId": gwProductId,
        "groupId": groupId,
        "ptypeLimited": ptypeLimited,
      };
}
