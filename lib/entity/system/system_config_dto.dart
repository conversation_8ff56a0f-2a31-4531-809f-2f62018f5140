import 'dart:convert';

import '../../../entity/system/user_info.dart';
import '../../common/base_type_util.dart';

/// sysIndustryEnabledProps : true

class SystemConfigDto {
  //region industry
  ///是否开启属性行
  bool sysIndustryEnabledProps = false;

  ///是否开启了批次号行业
  bool sysIndustryEnabledBatch = false;

  ///是否开启了序列号行业
  bool sysIndustryEnabledSerialNumber = false;

  ///是否开启严格序列号
  bool sysIndustryEnabledStrictSerialNumber = false;

  ///是否开启了税率
  bool sysGlobalEnabledTax = false;

  ///采购类单据 商品是否含税
  bool sysGlobalEnabledBuyTax = false;

  ///销售类单据 商品是否含税
  bool sysGlobalEnabledSaleTax = false;

  ///账期配置项
  dynamic sysGlobalEnabledPayment;

  ///是否开启一品多码
  bool sysGlobalMultipleBarcodeEnabled = false;

  //endregion industry

  //region business
  ///是否启用多仓库
  bool sysBusinessEnabledMultiStock = false;

  ///开单是否默认经手人
  bool recordsheetBillEnabledDefaultEtype = false;

  //region 小数点位数
  ///单价
  int sysDigitalPrice = 4;

  ///总金额
  int sysDigitalTotal = 2;

  ///税率
  int sysDigitalTax = 0;

  ///折扣
  int sysDigitalDiscount = 2;

  ///数量
  int sysDigitalQty = 2;

  ///数字输入最大值
  double sysGlobalDecimalMax = 0;

  //endregion 小数点位数
  ///是否显示未确认
  bool recordsheetRetailOrder = false;

  ///是否允许条码重复
  bool sysGlobalDuplicateBarcodeEnabled = false;

  ///是否开启批次号 先进先出 保质期先到期先出
  ///已废弃，使用[enabledPtypeBatchSort]
  @Deprecated("使用enabledPtypeBatchSort")
  bool sysBusinessEnabledPtypeSort = false;

  ///是否开启批次号 先进先出 保质期先到期先出
  bool enabledPtypeBatchSort = false;

  ///保质期商品批次号必填
  bool sysGlobalBatchNoRequired = false;

  ///负库存
  bool sysGlobalAllowNegative = false;

  //endregion business

  ///用户信息
  UserInfo? userInfo;

  //region 采购设置
  /// 无成本权限允许录入单价
  bool recordsheetBuyEnableNoCostPricePermAllowToEditPrice = false;

  //endregion 采购设置

  //region 销售设置
  ///抹零设置
  //小数规则 0 保留一位小数；1不保留小数
  int recordsheetSaleEshopEraseZeroDecimalRule = 0;

  //是否开启门店抹零设置
  bool recordsheetSaleEshopEraseZeroEnable = false;

  //舍弃规则：0只舍不入；1只入不舍
  int recordsheetSaleEshopEraseZeroRoundingOffRule = 0;

  ///是否开启会员名称必填
  bool posVipNameRequired = false;

  ///性能监控埋点url
  String costTimeReportUrl = "";

  ///是否有提示音
  int closePosScanTipSound = 0;

  //endregion 销售设置

  static SystemConfigDto fromMap(Map<String, dynamic>? map) {
    if (map == null) return SystemConfigDto();
    SystemConfigDto systemConfigDtoBean = SystemConfigDto();
    systemConfigDtoBean.recordsheetSaleEshopEraseZeroDecimalRule =
        BaseTypeUtil.toInt(map["recordsheetSaleEshopEraseZeroDecimalRule"]) ??
            0;
    systemConfigDtoBean.recordsheetSaleEshopEraseZeroEnable =
        BaseTypeUtil.toBoolean(map["recordsheetSaleEshopEraseZeroEnable"]) ??
            false;
    systemConfigDtoBean.recordsheetSaleEshopEraseZeroRoundingOffRule =
        BaseTypeUtil.toInt(
                map["recordsheetSaleEshopEraseZeroRoundingOffRule"]) ??
            0;
    systemConfigDtoBean.sysIndustryEnabledProps =
        BaseTypeUtil.toBoolean(map['sysIndustryEnabledProps']) ?? false;
    systemConfigDtoBean.sysIndustryEnabledBatch =
        BaseTypeUtil.toBoolean(map['sysIndustryEnabledBatch']) ?? false;
    systemConfigDtoBean.sysIndustryEnabledSerialNumber =
        BaseTypeUtil.toBoolean(map['sysIndustryEnabledSerialNumber']) ?? false;

    systemConfigDtoBean.sysIndustryEnabledStrictSerialNumber =
        BaseTypeUtil.toBoolean(map['sysIndustryEnabledStrictSerialNumber']) ??
            false;

    systemConfigDtoBean.sysBusinessEnabledMultiStock =
        BaseTypeUtil.toBoolean(map['sysBusinessEnabledMultiStock']) ?? false;

    systemConfigDtoBean.sysGlobalEnabledTax =
        BaseTypeUtil.toBoolean(map['sysGlobalEnabledTax']) ?? false;

    systemConfigDtoBean.sysGlobalEnabledBuyTax =
        BaseTypeUtil.toBoolean(map['sysGlobalEnabledBuyTax']) ?? false;

    systemConfigDtoBean.sysGlobalEnabledSaleTax =
        BaseTypeUtil.toBoolean(map['sysGlobalEnabledSaleTax']) ?? false;

    systemConfigDtoBean.sysGlobalEnabledPayment =
        map["sysGlobalEnabledPayment"];
    systemConfigDtoBean.sysGlobalMultipleBarcodeEnabled =
        BaseTypeUtil.toBoolean(map['sysGlobalMultipleBarcodeEnabled']) ?? false;

    systemConfigDtoBean.recordsheetBillEnabledDefaultEtype =
        BaseTypeUtil.toBoolean(map["recordsheetBillEnabledDefaultEtype"]) ??
            false;

    systemConfigDtoBean.sysDigitalPrice = map['sysDigitalPrice'];
    systemConfigDtoBean.sysDigitalTotal = map['sysDigitalTotal'];
    systemConfigDtoBean.sysDigitalTax = map['sysDigitalTax'];
    systemConfigDtoBean.sysDigitalDiscount = map['sysDigitalDiscount'];
    systemConfigDtoBean.sysDigitalQty = map['sysDigitalQty'];
    systemConfigDtoBean.recordsheetRetailOrder =
        BaseTypeUtil.toBoolean(map['recordsheetRetailOrder']) ?? false;

    systemConfigDtoBean.userInfo = UserInfo.fromMap(map['userInfo'] is String
        ? jsonDecode(map['userInfo'])
        : map['userInfo']);
    systemConfigDtoBean.sysGlobalDecimalMax = map['sysGlobalDecimalMax'];
    systemConfigDtoBean.sysGlobalDuplicateBarcodeEnabled =
        BaseTypeUtil.toBoolean(map['sysGlobalDuplicateBarcodeEnabled']) ??
            false;
    systemConfigDtoBean.sysBusinessEnabledPtypeSort =
        BaseTypeUtil.toBoolean(map['sysBusinessEnabledPtypeSort']) ?? false;
    systemConfigDtoBean.enabledPtypeBatchSort =
        BaseTypeUtil.toBoolean(map['enabledPtypeBatchSort']) ?? false;
    systemConfigDtoBean.sysGlobalBatchNoRequired =
        BaseTypeUtil.toBoolean(map['sysGlobalBatchNoRequired']) ?? false;
    systemConfigDtoBean.recordsheetBuyEnableNoCostPricePermAllowToEditPrice =
        BaseTypeUtil.toBoolean(
                map['recordsheetBuyEnableNoCostPricePermAllowToEditPrice']) ??
            false;
    systemConfigDtoBean.sysGlobalAllowNegative =
        BaseTypeUtil.toBoolean(map['sysGlobalAllowNegative']) ?? false;
    systemConfigDtoBean.costTimeReportUrl = map["costTimeReportUrl"] ?? "";
    systemConfigDtoBean.posVipNameRequired = BaseTypeUtil.toBoolean(map["posVipNameRequired"]) ?? false;
    systemConfigDtoBean.closePosScanTipSound = BaseTypeUtil.toInt(map["closePosScanTipSound"]) ?? 0;
    return systemConfigDtoBean;
  }

  Map toJson() => {
        "sysIndustryEnabledProps": sysIndustryEnabledProps,
        "sysIndustryEnabledBatch": sysIndustryEnabledBatch,
        "sysIndustryEnabledSerialNumber": sysIndustryEnabledSerialNumber,
        "sysIndustryEnabledStrictSerialNumber":
            sysIndustryEnabledStrictSerialNumber,
        "sysBusinessEnabledMultiStock": sysBusinessEnabledMultiStock,
        "sysGlobalEnabledTax": sysGlobalEnabledTax,
        "sysGlobalEnabledBuyTax": sysGlobalEnabledBuyTax,
        "sysGlobalEnabledSaleTax": sysGlobalEnabledSaleTax,
        "sysGlobalEnabledPayment": sysGlobalEnabledPayment,
        "sysGlobalMultipleBarcodeEnabled": sysGlobalMultipleBarcodeEnabled,
        "recordsheetBillEnabledDefaultEtype":
            recordsheetBillEnabledDefaultEtype,
        "sysDigitalPrice": sysDigitalPrice,
        "sysDigitalTotal": sysDigitalTotal,
        "sysDigitalTax": sysDigitalTax,
        "sysDigitalDiscount": sysDigitalDiscount,
        "sysDigitalQty": sysDigitalQty,
        "recordsheetRetailOrder": recordsheetRetailOrder,
        "userInfo": jsonEncode(userInfo),
        "sysGlobalDecimalMax": sysGlobalDecimalMax,
        "sysGlobalDuplicateBarcodeEnabled": sysGlobalDuplicateBarcodeEnabled,
        "sysBusinessEnabledPtypeSort": sysBusinessEnabledPtypeSort,
        "enabledPtypeBatchSort": enabledPtypeBatchSort,
        "sysGlobalBatchNoRequired": sysGlobalBatchNoRequired,
        "recordsheetBuyEnableNoCostPricePermAllowToEditPrice":
            recordsheetBuyEnableNoCostPricePermAllowToEditPrice,
        "sysGlobalAllowNegative": sysGlobalAllowNegative,
        "costTimeReportUrl": costTimeReportUrl,
        "posVipNameRequired": posVipNameRequired,
    "closePosScanTipSound": closePosScanTipSound,
  };
}
