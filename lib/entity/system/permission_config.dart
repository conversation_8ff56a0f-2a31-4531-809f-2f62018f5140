import '../update/version_info.dart';

///
///@ClassName: PermissionConfig
///@Description:
///@Author: tanglan
///@Date: 2023/12/4
class PermissionConfig {
  Map<String, dynamic>? permissions; //权限
  Map<String, dynamic>? qiniuConfigDTO;

  Map<String, dynamic>? systemConfig;

  static PermissionConfig fromMap(Map<String, dynamic> map) {
    PermissionConfig permissionConfig = PermissionConfig();
    permissionConfig.permissions = map['permissions'];
    permissionConfig.qiniuConfigDTO = map['qiniuConfigDTO'];
    permissionConfig.systemConfig = map["systemConfig"];
    return permissionConfig;
  }

  Map toJson() => {
    "permissions": permissions,
    "qiniuConfigDTO": qiniuConfigDTO,
    "systemConfig": systemConfig,
  };
}
