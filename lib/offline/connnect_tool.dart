import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:haloui/haloui.dart';

import '../common/login/login_center.dart';
import '../common/tool/sp_tool.dart';
import '../login/login_page.dart';
import '../widgets/halo_pos_label.dart';
import 'offline_dialog.dart';
import 'offline_tool.dart';

/// 创建时间：2023/12/18
/// 作者：xiaotiaochong
/// 描述：

class ConnectTool {
  static final ConnectTool _singletonPattern = ConnectTool._internal();

  ///工厂构造函数
  factory ConnectTool() {
    return _singletonPattern;
  }

  ///构造函数私有化，防止被误创建
  ConnectTool._internal();

  StreamSubscription? subscription;

  addConnectListen(BuildContext context) {
    subscription?.cancel();
    subscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> connectivityResult) {
      // if (connectivityResult == ConnectivityResult.mobile) {
      //   // I am connected to a mobile network.
      // } else if (connectivityResult == ConnectivityResult.wifi) {
      //   // I am connected to a wifi network.
      // } else if (connectivityResult == ConnectivityResult.ethernet) {
      //   // I am connected to a ethernet network.
      // } else if (connectivityResult == ConnectivityResult.vpn) {
      //   // I am connected to a vpn network.
      //   // Note for iOS and macOS:
      //   // There is no separate network interface type for [vpn].
      //   // It returns [other] on any device (also simulator)
      // } else if (connectivityResult == ConnectivityResult.bluetooth) {
      //   // I am connected to a bluetooth.
      // } else if (connectivityResult == ConnectivityResult.other) {
      //   // I am connected to a network which is not in the above mentioned networks.
      // } else if (connectivityResult == ConnectivityResult.none) {
      //}
      if (connectivityResult.contains(ConnectivityResult.none)) {
        // I am not connected to any network.
        OfflineDialogTool.showOfflineDialog(context);
      } else {
        OfflineDialogTool.showOnlineBillDialog(context);
      }
      debugPrint(connectivityResult.toString());
    });
  }
}
