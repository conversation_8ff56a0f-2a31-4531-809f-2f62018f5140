import 'package:flutter/material.dart';
import 'package:haloui/haloui.dart';

import '../db/login_user_db_mannager.dart';
import '../login/model/login_user_model.dart';

/// 创建时间：2023/12/18 
/// 作者：xiaotia<PERSON>ong
/// 描述：

class OffLineTool {
  static final OffLineTool _singletonPattern = OffLineTool._internal();

  ///工厂构造函数
  factory OffLineTool() {
    return _singletonPattern;
  }

  ///构造函数私有化，防止被误创建
  OffLineTool._internal();

  ///离线登录
  bool isOfflineLogin = false;

  Future<List<LoginUserModel>> offlineLogin(BuildContext context,{required String company,
    required String user,
    required String pwd,}) async {
    showOffLineToast(context);

    List<LoginUserModel> list = await LoginUserDBManager.selectLoginUserAll();
    debugPrint(list.length.toString());
    return LoginUserDBManager.selectLoginUser(company: company, user: user, pwd: pwd);
  }

  static void showOffLineToast(BuildContext context) {
    if (context.mounted)HaloToast.show(context, msg: "离线模式只能使用基础开单功能");
  }
}