import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';

import '../bill/tool/bill_tool.dart';
import '../widgets/base/halo_pos_alert_dialog.dart';
import '../widgets/halo_pos_label.dart';
import 'offline_tool.dart';

/// 创建时间：2023/12/18
/// 作者：xiaotiaochong
/// 描述：

class OfflineDialogTool {
  static showOfflineDialog(BuildContext context) {

    SmartDialog.show(
        keepSingle: true,
        builder: (_) {
          return HaloPosAlertDialog(
            title: "提示",
            content: "无网络连接，是否使用离线收银？",
            autoAfterCancel: false,
            autoAfterSubmit: false,
            onCancelCallBack: () {
              OffLineTool().isOfflineLogin = false;
              SmartDialog.dismiss();
            },
            onSubmitCallBack: () async {
              OffLineTool().isOfflineLogin = true;
              SmartDialog.dismiss();
            },
          );
        });
  }

  static showSyncBillDialog(BuildContext context) {
    HaloPosAlertDialog.showAlertDialog(context,
        showCancel: false, title: "提示", content: "已保存为离线单据，网络恢复后\n可手动同步到系统");
  }

  static showOnlineBillDialog(BuildContext context) {
    SmartDialog.show(
        keepSingle: true,
        builder: (_) {
          return HaloPosAlertDialog(
            title: "提示",
            content: "网络已恢复\n可手动同步离线单据到系统",
            onCancelCallBack: () {
              OffLineTool().isOfflineLogin = false;
              SmartDialog.dismiss();
            },
            onSubmitCallBack: () async {
              OffLineTool().isOfflineLogin = false;
              await BillTool.syncOfflineBillSubmit(context);
              SmartDialog.dismiss();
            },
            button1Text: "立即同步",
            button2Text: "稍后手动同步",
          );
        });
  }
}
