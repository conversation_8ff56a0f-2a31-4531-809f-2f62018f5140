import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../bill/model/bill_model.dart';
import '../../../cashierbox/entity/cash_box_payment_request_dto.dart';
import '../../../common/login/login_center.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../iconfont/icon_font.dart';
import '../../../plugin/cash_box_plugin.dart';
import '../../../widgets/price_keyboard.dart' hide Key;
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/haloui.dart' hide KeyboardHiddenTextField;

import '../common/keyboard_hidden.dart';
import '../common/tool/decimal_scale_input_formatter.dart';
import '../print/tool/print_tool.dart';

class CashBoxDialog extends StatelessWidget {
  const CashBoxDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return UnconstrainedBox(
      child: SizedBox(
        width: 720.w,
        height: 724.h,
        child: const Dialog(
          insetPadding: EdgeInsets.zero,
          child: _CashBoxDialog(),
        ),
      ),
    );
  }
}

class _CashBoxDialog extends StatefulWidget {
  const _CashBoxDialog({Key? key}) : super(key: key);

  @override
  _CashBoxDialogState createState() => _CashBoxDialogState();
}

class _CashBoxDialogState extends State<_CashBoxDialog> {
  ///输入的数字
  TextEditingController numStr = TextEditingController(text: "0");

  @override
  Widget build(BuildContext context) {
    return HaloContainer(
      color: Colors.white,
      direction: Axis.vertical,
      mainAxisSize: MainAxisSize.max,
      padding: EdgeInsets.only(left: 32.w, right: 42.w),
      borderRadius: BorderRadius.circular(6.w),
      children: [
        _buildTitle(context),
        _buildCount(context),
        _buildKeyBoard(context),
        _buildBottom(context)
      ],
    );
  }

  ///构建金额栏
  HaloContainer _buildCount(BuildContext context) {
    return HaloContainer(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      height: 80.h,
      mainAxisSize: MainAxisSize.max,
      borderRadius: BorderRadius.circular(6.w),
      border: Border.all(color: const Color(0xFFCFCFCF), width: 2.w),
      children: [
        Text("金额",
            style: TextStyle(
                color: const Color(0xff333333),
                fontSize: 32.sp,
                fontWeight: FontWeight.bold)),
        Expanded(
          child: KeyboardHiddenTextField(
            controller: numStr,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: const Color(0xff333333),
                fontWeight: FontWeight.bold,
                backgroundColor: const Color(0xFFC3D4FF),
                fontSize: 28.sp),
            onTapBefore: () => false,
            cleanTextWhenSearch: false,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              LengthLimitingTextInputFormatter(14),
              DecimalScaleInputFormatter(scale: 2),
            ],
          ),
        ),
      ],
    );
  }

  ///标题栏
  Widget _buildTitle(BuildContext context) {
    return Container(
        alignment: Alignment.centerRight,
        constraints: const BoxConstraints(minWidth: double.infinity),
        padding: EdgeInsets.only(top: 32.h, bottom: 42.h),
        child: GestureDetector(
            child: IconFont(IconNames.close, size: 22.w),
            onTap: () => Navigator.pop(context)));
  }

  ///构建小键盘
  Widget _buildKeyBoard(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(top: 42.h, bottom: 38.h),
        child: PriceKeyBoard(
          controller: numStr,
            scale:SpTool.getSystemConfig().sysDigitalTotal
          // numberChangeCallback: (number) =>
          //     setState(() => numStr.text = number)
          //
        ));
  }

  ///底部按钮
  Widget _buildBottom(BuildContext context) {
    return SizedBox(
      height: 80.h,
      child: Row(
        children: [
          Expanded(
              flex: 1,
              child: _buildButton(context, "换零",
                  () =>PrintTool.openCashBox(context), const Color(0xFF4679FC))),
          Container(width: 12.w),
          Expanded(
              flex: 1,
              child: _buildButton(
                  context,
                  "取出",
                  () => _requestSavePayment(context, 1),
                  const Color(0xFFFCAC46))),
          Container(width: 12.w),
          Expanded(
              flex: 1,
              child: _buildButton(
                  context,
                  "存入",
                  () => _requestSavePayment(context, 0),
                  const Color(0xFF20CA10))),
        ],
      ),
    );
  }

  ///按钮
  Widget _buildButton(
      BuildContext context, String text, VoidCallback onTap, Color background) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        decoration: BoxDecoration(
          color: background,
          borderRadius: BorderRadius.circular(6.w),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 30.sp),
        ),
      ),
    );
  }

  ///请求数据，先提交，再打开钱箱
  ///[paymentType]为存取款类型 0为存入，1为取出
  void _requestSavePayment(BuildContext context, int paymentType) async {
    double num = double.tryParse(numStr.text) ?? 0;
    if (num <= 0) {
      HaloToast.show(context, msg: "请输入金额");
      return;
    }
    String? cashierId = SpTool.getCashierInfo().id;
    String? otypeId = SpTool.getStoreInfo()!.otypeId;
    String? etypeId = LoginCenter.getLoginUser().employeeId;
    if (StringUtil.isNotEmpty(cashierId) &&
        StringUtil.isNotEmpty(otypeId) &&
        StringUtil.isNotEmpty(etypeId)) {
      BillModel.uploadCashBoxPayment(
        context,
        CashBoxPaymentRequestDto(
            num, paymentType, cashierId!, etypeId!, otypeId!),
      ).then((success) {
        if (success) {
         PrintTool.openCashBox(context);
          Navigator.pop(context, num);
        }
      });
    }
  }
}
