///钱箱存取款请求实体类
class CashBoxPaymentRequestDto {
  ///id
  String? id;

  ///收支金额
  double amount;

  ///存取类型 0为存，1为取
  final int paymentType;

  ///收银机id
  final String cashierId;

  ///操作人id
  final String etypeId;

  ///销售机构id
  final String otypeId;

  CashBoxPaymentRequestDto(this.amount, this.paymentType, this.cashierId,
      this.etypeId, this.otypeId, {this.id});

  Map toJson() => {
        "amount": amount,
        "paymentType": paymentType,
        "cashierId": cashierId,
        "etypeId": etypeId,
        "otypeId": otypeId,
        "id": id
      };

  static CashBoxPaymentRequestDto? fromMap(Map<dynamic, dynamic>? map) {
    if (map == null) return null;
    CashBoxPaymentRequestDto cashBoxPaymentRequestDto =
        CashBoxPaymentRequestDto(
      map["amount"],
      map["paymentType"],
      map["cashierId"],
      map["etypeId"],
      map["otypeId"],
      id: map["id"]??"",
    );
    return cashBoxPaymentRequestDto;
  }
}
