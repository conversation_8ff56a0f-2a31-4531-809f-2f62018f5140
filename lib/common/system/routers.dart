import 'package:flutter/material.dart';
import '../../../login/login_page.dart';
import '../../../home/<USER>';

import '../tool/version_check.dart';

///
///@ClassName: routers
///@Description:路由信息
///@Author: tanglan
///@Date: 7/27/21 1:19 PM
class Routers {
  static String root = "/root";
  static String login = "/login/login_page";

  static Map<String, WidgetBuilder> configureRoutes() {
    Map<String, WidgetBuilder> routers = {};
    routers[root] =
        (BuildContext context) => VersionCheck.getVersionRootWidget();
    routers[login] = (BuildContext context) => LoginPage();
    return routers;
  }
}
