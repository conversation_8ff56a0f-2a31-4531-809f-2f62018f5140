import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:flutter_window_close/flutter_window_close.dart';
import 'package:halo_pos/application.dart';

import '../tool/dialog_util.dart';

///系统工具类
class SystemUtil {
  SystemUtil._();

  static void bindWindowClose() {
    if (!Platform.isWindows) return;
    FlutterWindowClose.setWindowShouldCloseHandler(() async {
      BuildContext? context = Application.navigatorKey.currentContext;
      if (context == null || !context.mounted) return true;
      int index = await DialogUtil.showConfirmDialog(context,
          content: "确认要退出POS收银系统吗？", actionLabels: ["取消", "确定"]);
      return index == 1;
    });
  }

  static void unBindWindowClose() {
    if (!Platform.isWindows) return;
    FlutterWindowClose.setWindowShouldCloseHandler(null);
  }
}
