import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../common/style/app_colors.dart';

///
///@ClassName: support_theme
///@Description: 主题类
///@Author: tanglan
///@Date: 7/27/21 1:16 PM
class SupportTheme {
  static List<ThemeData> getSupportThemes() {
    List<ThemeData> themeList = [];
    themeList
      .add( ThemeData(
        brightness: Brightness.light,
        primaryColor: AppColors.primaryColor,
        colorScheme: ColorScheme.light(
            primary: AppColors.primaryColor, secondary: AppColors.accentColor),
        textSelectionTheme: TextSelectionThemeData(
            cursorColor: AppColors.accentColor,
            selectionHandleColor: AppColors.accentColor,
            selectionColor: AppColors.accentColor),
        textTheme: TextTheme(
          displayLarge: TextStyle(color: AppColors.weightTitleTextColor),
          //深色标题 #333333
          //次要标题文本颜色 #999999
          titleMedium: TextStyle(color: AppColors.normalSecondTitleTextColor),
          //次要标题文本颜色 #999999
          titleLarge: TextStyle(color: AppColors.normalTitleTextColor),
          //标题文本颜色 #666666
          bodyLarge: TextStyle(color: AppColors.secondTextColor),
          //次要文本颜色 #666666
          bodyMedium:
              TextStyle(color: AppColors.normalTextColor), //默认字体颜色 #333333
        ),
        cardColor: AppColors.cardColor,
        scaffoldBackgroundColor: AppColors.scaffoldBackgroundColor,
        //窗口背景色
        hintColor: AppColors.hintColor,
        dividerColor: AppColors.dividerColor,
        elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.accentColor,
        )),
        //主要按钮颜色
        appBarTheme: AppBarTheme(
            iconTheme: IconThemeData(color: AppColors.normalTextColor),
            titleTextStyle: TextStyle(
                color: AppColors.normalTextColor,
                fontSize: 17,
                fontWeight: FontWeight.bold),
            elevation: 0.5,
            centerTitle: true, systemOverlayStyle: SystemUiOverlayStyle.dark),
      ));
    return themeList;
  }

  static ThemeData getDarkTheme() {
    return getSupportThemes()[1];
  }
}
