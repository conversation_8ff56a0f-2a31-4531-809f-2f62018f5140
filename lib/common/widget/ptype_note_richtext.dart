import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/color_util.dart';

import '../../bill/entity/goods_detail_dto.dart';
import '../../bill/tool/bill_goods_util.dart';
import '../../bill/tool/bill_tool.dart';
import '../../widgets/halo_pos_label.dart';
import '../style/app_colors.dart';
import '../style/app_pos_size.dart';

class PtypeNoteRichText extends StatelessWidget {
  /// 商品明细行信息
  final GoodsDetailDto goodsDetailDto;

  /// 是否展示商品属性
  final bool? showProp;

  ///商品名称文本样式
  final TextStyle? textStyle;

  ///只标记商品类型
  final bool? isOnlyType;

  const PtypeNoteRichText(
      {Key? key,
      required this.goodsDetailDto,
      this.showProp,
      this.textStyle,
      this.isOnlyType = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text.rich(_buildRichTextSpan(context));
  }

  _buildRichTextSpan(BuildContext context) {
    List<InlineSpan> spans = [];
    List<InlineSpan> ptypeTypeSpans = _getPtypeTypeSpanList();
    List<InlineSpan> ptypePreferentialSpans = _getPtypePreferentialSpanList();

    if (ptypePreferentialSpans.isNotEmpty && !isOnlyType!) {
      spans.addAll(ptypePreferentialSpans);
    }
    spans.add(
      TextSpan(
          text: (goodsDetailDto.pFullName ?? "") +
              (showProp ?? false ? BillTool.getPropName(goodsDetailDto) : ""),
          style: textStyle ??
              TextStyle(
                  color: AppColors.normalFontColor,
                  fontSize: AppPosSize.secondaryTitleFontSize.sp,
                  fontWeight: FontWeight.w400)),
    );
    if (ptypeTypeSpans.isNotEmpty) {
      spans.addAll(ptypeTypeSpans);
    }

    return TextSpan(
      children: spans,
    );
  }

  List<InlineSpan> _getPtypeTypeSpanList() {
    List<InlineSpan> spans = [];
    if (goodsDetailDto.comboRow) {
      spans.add(_buildTextSpan(ColorUtil.stringColor("#8E3DC7"), "套",
          bgColor: ColorUtil.stringColor("#F1E3FF")));
    }
    if (goodsDetailDto.batchenabled) {
      spans.add(_buildTextSpan(ColorUtil.stringColor("#426AF5"), "批",
          bgColor: ColorUtil.stringColor("#E4E7FF")));
    }
    if (goodsDetailDto.snenabled > 0) {
      spans.add(_buildTextSpan(ColorUtil.stringColor("#24CCC7"), "序",
          bgColor: ColorUtil.stringColor("#E1FFFF")));
    }
    if (goodsDetailDto.propenabled) {
      spans.add(_buildTextSpan(ColorUtil.stringColor("#3AA8FB"), "属",
          bgColor: ColorUtil.stringColor("#E0F5FF")));
    }
    if (goodsDetailDto.pcategory == 1) {
      spans.add(_buildTextSpan(ColorUtil.stringColor("#0589ED"), "服",
          bgColor: ColorUtil.stringColor("#DAF2FF")));
    }
    return spans;
  }

  List<InlineSpan> _getPtypePreferentialSpanList() {
    List<InlineSpan> spans = [];
    if (BillGoodsUtil.isPromotionGoods(goodsDetailDto)) {
      spans.add(_buildTextSpan(const Color(0xffF03B36), "促"));
    }
    if (BillGoodsUtil.isDiscountGoods(goodsDetailDto, markGift: false)) {
      spans.add(_buildTextSpan(const Color(0xffF03B36), "折"));
    }
    if (goodsDetailDto.gift) {
      spans.add(_buildTextSpan(const Color(0xffDE858E), "赠"));
    }
    return spans;
  }

  InlineSpan _buildTextSpan(Color textColor, String text, {Color? bgColor}) {
    return WidgetSpan(
      child: Container(
        width: 26.w,
        height: 26.w,
        margin: EdgeInsets.only(right: 8.w, left: 4.w),
        alignment: Alignment.center,
        decoration: BoxDecoration(
            color: bgColor ?? textColor,
            border: Border.all(color: bgColor ?? textColor, width: 1.0),
            // 设置边框颜色
            borderRadius: const BorderRadius.all(Radius.circular(3))),
        child: HaloPosLabel(text,
            textStyle: TextStyle(
                height: 1.2,
                color: bgColor != null ? textColor : Colors.white,
                decoration: TextDecoration.none,
                fontSize: 15.sp,
                fontWeight: FontWeight.normal)),
      ),
    );
  }
}
