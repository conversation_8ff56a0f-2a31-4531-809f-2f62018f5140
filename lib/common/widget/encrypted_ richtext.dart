import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/bill/entity/pos_encrypt_info.dart';
import 'package:halo_pos/bill/model/base_info_model.dart';
import 'package:halo_utils/halo_utils.dart';

import '../style/app_color_helper.dart';

class EncryptedRichText extends StatefulWidget {
  final String? text;
  final String? encryptedText;
  final String? buyerId;
  final TextStyle? encryptedStyle;
  ValueSetter<String>? setPhone;
   EncryptedRichText({this.text, this.encryptedText,this.encryptedStyle,this.buyerId,this.setPhone, Key? key})
      : super(key: key);

  @override
  State<EncryptedRichText> createState() => _EncryptedRichTextState();
}

class _EncryptedRichTextState extends State<EncryptedRichText> {
  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        text: widget.text ?? "",
        children: [
          TextSpan(
            text: widget.encryptedText ?? "",
            style: widget.encryptedStyle ?? const TextStyle(
              color: Colors.blue,
              decoration: TextDecoration.underline,
              decorationColor: Colors.blue,
              decorationStyle: TextDecorationStyle.solid,
              // 其他文本样式
            ),
            recognizer: TapGestureRecognizer()..onTap = () {
              // batchDecryptBuyers();
            },
          ),
        ],
      ),
    );
  }

  void batchDecryptBuyers(){
    if(widget.buyerId != null && widget.buyerId!.isNotEmpty){
      BaseInfoModel.batchDecryptBuyers(context, [widget.buyerId!]).then((value) {
        if(value.isNotEmpty){
          PosBuyer buyer = value[0];
          if(widget.setPhone != null){
            widget.setPhone!(buyer.customerReceiverPhone??"");
          }
        }
      });
    }
  }
}
