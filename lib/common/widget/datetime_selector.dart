import 'package:flutter/material.dart';
import 'package:halo_pos/common/style/app_pos_size.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/navigator/navigate_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_pop_window.dart';

import '../../iconfont/icon_font.dart';
import '../style/app_color_helper.dart';
import '../style/app_colors.dart';

///
///@ClassName: datetime_selector
///@Description:
///@Author: tanglan
///@Date: 2025/7/15

class DateTimeSelector extends StatefulWidget {
  ///初始化时间
  final DateTime initialDate;

  ///点击确定的回调
  final Function(DateTime selectTime) onSelectDateTimeCallback;

  const DateTimeSelector({
    super.key,
    required this.initialDate,
    required this.onSelectDateTimeCallback,
    this.minDateTime,
    this.maxDateTime,
  });

  final DateTime? minDateTime;
  final DateTime? maxDateTime;

  @override
  State<DateTimeSelector> createState() => _DateTimeSelectorState();

  ///弹出popupWindows
  static showDateTimeSelector(
    BuildContext context,
    GlobalKey positionKey, {
    DateTime? selectDateTime,
    DateTime? minDateTime,
    DateTime? maxDateTime,
    required Function(DateTime selectTime) onCallBack,
    required VoidCallback onCancelCallback,
  }) {
    HaloPopWindow().show(
      positionKey,
      gravity: PopWindowGravity.bottom,
      intervalLeft: 0.w,
      // 向左偏移，修正偏右问题
      intervalTop: 15.h,
      // 增加顶部间距，修正偏上问题
      backgroundColor: Colors.transparent,
      onCancelCallback: onCancelCallback,
      child: DateTimeSelector(
        initialDate: selectDateTime ?? DateTime.now(),
        onSelectDateTimeCallback: (DateTime dateTime) {
          onCallBack(dateTime);
          HaloPopWindow().disMiss();
        },
        minDateTime: minDateTime,
        maxDateTime: maxDateTime,
      ),
    );
  }

  ///弹出Dialog
  static showDateTimeSelectorDialog(
    BuildContext context, {
    DateTime? selectDateTime,
    required Function(DateTime selectTime) onCallBack,
  }) {
    HaloDialog(
      dismissOnTouchOutside: true,
      context,
      child: Center(
        child: DateTimeSelector(
          initialDate: selectDateTime ?? DateTime.now(),
          onSelectDateTimeCallback: (DateTime dateTime) {
            onCallBack(dateTime);
            NavigateUtil.pop(context);
          },
        ),
      ),
    ).show();
  }
}

class _DateTimeSelectorState extends State<DateTimeSelector> {
  ///选择日期
  late DateTime _date;

  ///选择时间
  late Duration _time;

  ///小时滚动
  FixedExtentScrollController? _hCtrl;

  ///分滚动
  FixedExtentScrollController? _mCtrl;

  ///秒滚动
  FixedExtentScrollController? _sCtrl;

  static const _weekdays = ['日', '一', '二', '三', '四', '五', '六'];

  ///true显示日期选择器 false显示时间选择器
  bool _showDatePicker = true;

  @override
  void initState() {
    super.initState();
    _parseInitial(widget.initialDate);
    _createControllers();
    WidgetsBinding.instance.addPostFrameCallback((_) => _syncAll());
  }

  @override
  void didUpdateWidget(covariant DateTimeSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialDate != widget.initialDate) {
      _parseInitial(widget.initialDate);
      _syncAll();
    }
  }

  @override
  void dispose() {
    _hCtrl?.dispose();
    _mCtrl?.dispose();
    _sCtrl?.dispose();
    super.dispose();
  }

  /* ---------- 初始化DateTime 解析成日期和时间 ---------- */
  void _parseInitial(DateTime dt) {
    _date = dt;
    _time = Duration(hours: dt.hour, minutes: dt.minute, seconds: dt.second);
  }

  ///初始化时间滚动器
  void _createControllers() {
    _hCtrl = FixedExtentScrollController(initialItem: _time.inHours % 24);
    _mCtrl = FixedExtentScrollController(
      initialItem: _time.inMinutes.remainder(60),
    );
    _sCtrl = FixedExtentScrollController(
      initialItem: _time.inSeconds.remainder(60),
    );
  }

  ///时间滚动器，定位到已选时间
  void _syncAll() {
    _hCtrl?.jumpToItem(_time.inHours % 24);
    _mCtrl?.jumpToItem(_time.inMinutes.remainder(60));
    _sCtrl?.jumpToItem(_time.inSeconds.remainder(60));
  }

  /* ---------- 工具 ---------- */
  String get _dateStr => DateUtil.formatDate(_date, format: 'yyyy-MM-dd');

  String get _timeStr {
    final h = _time.inHours % 24;
    final m = _time.inMinutes.remainder(60);
    final s = _time.inSeconds.remainder(60);
    return '${h.toString().padLeft(2, '0')}:'
        '${m.toString().padLeft(2, '0')}:'
        '${s.toString().padLeft(2, '0')}';
  }

  /// 检查日期是否被禁用
  bool _isDateDisabled(DateTime date) {
    // 只比较日期部分，忽略时间
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (widget.minDateTime != null) {
      final minDateOnly = DateTime(
        widget.minDateTime!.year,
        widget.minDateTime!.month,
        widget.minDateTime!.day,
      );
      if (dateOnly.isBefore(minDateOnly)) {
        return true;
      }
    }

    if (widget.maxDateTime != null) {
      final maxDateOnly = DateTime(
        widget.maxDateTime!.year,
        widget.maxDateTime!.month,
        widget.maxDateTime!.day,
      );
      if (dateOnly.isAfter(maxDateOnly)) {
        return true;
      }
    }

    return false;
  }

  /* ---------- UI ---------- */
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 600.w,
      height: 550.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [BoxShadow(color: Colors.black26, blurRadius: 8)],
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
              child: _showDatePicker ? _buildCalendar() : _buildTimeWheel(),
            ),
          ),
          _buildBottom(),
        ],
      ),
    );
  }

  ///顶部已选日期时间
  Widget _buildHeader() {
    return SizedBox(
      height: 68.h,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          const Divider(height: 1, thickness: 1, color: AppColors.dividerColor),
          HaloContainer(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            children: [
              buildMonthChangeArrow(IconNames.ngp_left_back, () {
                monthChange(true);
              }),
              const Spacer(),
              buildSelectorDateTime(
                _dateStr,
                _showDatePicker,
                () => setState(() => _showDatePicker = true),
              ),
              const SizedBox(width: 24),
              buildSelectorDateTime(
                _timeStr,
                !_showDatePicker,
                () => setState(() => _showDatePicker = false),
              ),

              const Spacer(),
              buildMonthChangeArrow(IconNames.ngp_right_back, () {
                monthChange(false);
                ;
              }),
            ],
          ),
        ],
      ),
    );
  }

  ///构建顶部箭头
  Widget buildMonthChangeArrow(IconNames iconName, Function() onTap) {
    return Visibility(
      visible: _showDatePicker,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        child: SizedBox(
          width: 48.w,
          height: 68.h,
          child: Center(child: IconFont(iconName, size: 24.w)),
        ),
      ),
    );
  }

  ///构建顶部时间/日期选择器
  Widget buildSelectorDateTime(String text, bool isSelect, Function() onPress) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onPress,
      child: Container(
        alignment: Alignment.center,
        height: 68.h,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        decoration:
            isSelect
                ? BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.borderColor,
                      width: 4.h,
                    ),
                  ),
                )
                : null,
        child: Text(
          text,
          style: TextStyle(
            fontSize: AppPosSize.secondaryTitleFontSize.sp,
            fontWeight: isSelect ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  ///日历月份变更
  ///isPreMonth 是否是上个月 true=上个月 false=下个月
  void monthChange(bool isPreMonth) {
    setState(() {
      final newMonth = DateTime(
        _date.year,
        isPreMonth ? _date.month - 1 : _date.month + 1,
      );
      final lastDay = DateTime(newMonth.year, newMonth.month + 1, 0).day;
      _date = DateTime(
        newMonth.year,
        newMonth.month,
        _date.day.clamp(1, lastDay),
      );
    });
  }

  ///构建日历组件
  Widget _buildCalendar() {
    final first = DateTime(_date.year, _date.month, 1);
    final startOffset = first.weekday % 7;
    final daysInMonth = DateTime(_date.year, _date.month + 1, 0).day;
    final total = ((daysInMonth + startOffset) / 7).ceil() * 7;

    return Column(
      children: [
        // 星期行
        HaloContainer(
          height: 36.h,
          children:
              _weekdays
                  .map(
                    (w) => Expanded(
                      child: Center(
                        child: Text(
                          w,
                          style: TextStyle(
                            fontSize: AppPosSize.totalFontSize.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.black54,
                          ),
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),

        // 日期网格
        Expanded(
          child: GridView.builder(
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1,
            ),
            itemCount: total,
            itemBuilder: (_, index) {
              final day = DateTime(
                _date.year,
                _date.month,
                index + 1 - startOffset,
              );

              ///是否是当前月
              final isCurrentMonth = day.month == _date.month;

              ///是否是选中日
              final isSelectDay =
                  day.year == _date.year &&
                  day.month == _date.month &&
                  day.day == _date.day;
              final isDisabled = _isDateDisabled(day);
              return GestureDetector(
                onTap: isDisabled ? null : () => setState(() => _date = day),
                child: Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: isSelectDay ? Colors.blue : Colors.transparent,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${day.day}',
                    style: TextStyle(
                      fontSize: AppPosSize.totalFontSize.sp,
                      color:
                          isDisabled
                              ? Colors.grey.withValues(alpha: 0.3)
                              : isSelectDay
                              ? Colors.white
                              : isCurrentMonth
                              ? Colors.black
                              : Colors.grey,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  ///构建时间选择器
  Widget _buildTimeWheel() {
    return HaloContainer(
      padding: EdgeInsets.symmetric(horizontal: 36.w),
      children: [
        _wheel(24, _hCtrl, (i) {
          setState(() {
            _time = Duration(
              hours: i,
              minutes: _time.inMinutes.remainder(60),
              seconds: _time.inSeconds.remainder(60),
            );
          });
        }),
        _wheel(60, _mCtrl, (i) {
          setState(() {
            _time = Duration(
              hours: _time.inHours,
              minutes: i,
              seconds: _time.inSeconds.remainder(60),
            );
          });
        }),
        _wheel(60, _sCtrl, (i) {
          setState(() {
            _time = Duration(
              hours: _time.inHours,
              minutes: _time.inMinutes.remainder(60),
              seconds: i,
            );
          });
        }),
      ],
    );
  }

  ///时分秒滑轮
  Widget _wheel(
    int count,
    FixedExtentScrollController? ctrl,
    ValueChanged<int> onChanged,
  ) {
    return Expanded(
      child: ListWheelScrollView.useDelegate(
        itemExtent: 40,
        diameterRatio: 1.8,
        perspective: 0.005,
        physics: const FixedExtentScrollPhysics(),
        useMagnifier: true,
        magnification: 1.2,
        controller: ctrl,
        onSelectedItemChanged: onChanged,
        childDelegate: ListWheelChildBuilderDelegate(
          builder: (context, index) {
            final value = index % count;

            // 判断当前 value 是不是正在正中间
            final bool isCenter =
                null == ctrl ? false : value == ctrl.selectedItem % count;

            return Center(
              child: Text(
                value.toString().padLeft(2, '0'),
                style: TextStyle(
                  fontSize:
                      isCenter
                          ? AppPosSize.secondaryTitleFontSize.sp
                          : AppPosSize.totalFontSize.sp,
                  fontWeight: isCenter ? FontWeight.bold : FontWeight.normal,
                  color: isCenter ? Colors.black : Colors.grey,
                ),
              ),
            );
          },
          childCount: 9999,
        ),
      ),
    );
  }

  ///底部确定按钮
  Widget _buildBottom() {
    return HaloContainer(
      crossAxisAlignment: CrossAxisAlignment.end,
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      border: Border(
        top: BorderSide(color: AppColors.dividerColor, width: 1.h),
      ),
      children: [
        Expanded(
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              DateTime dt = DateTime(
                _date.year,
                _date.month,
                _date.day,
                _time.inHours % 24,
                _time.inMinutes.remainder(60),
                _time.inSeconds.remainder(60),
              );
              widget.onSelectDateTimeCallback(dt);
            },
            child: Container(
              height: 48.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: AppColorHelper(context).getAccentColor(),
                borderRadius: BorderRadius.all(Radius.circular(6.w)),
              ),
              child: Text(
                "确定",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: AppPosSize.secondaryTitleFontSize.sp,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
