import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:halo_pos/common/style/app_colors.dart';
import 'package:halo_utils/utils/date_util.dart';
import 'package:haloui/haloui.dart';

import '../../iconfont/icon_font.dart';
import '../../widgets/halo_pos_label.dart';
import '../style/app_pos_size.dart';
import '../tool/DateTimePickUtil.dart';
import 'datetime_selector.dart';

mixin DateTimeFilterMixin<T extends StatefulWidget> on State<T> {
  TextEditingController textStartTimeController = TextEditingController();
  TextEditingController textEndTimeController = TextEditingController();
  FastTimeType defaultFastTime = FastTimeType.last30Day;

  // GlobalKeys for DateTimeSelector positioning
  final GlobalKey _startTimeKey = GlobalKey();
  final GlobalKey _endTimeKey = GlobalKey();

  // 选中状态跟踪：0-未选中，1-开始时间选中，2-结束时间选中
  int selectedTimeType = 0;

  @override
  void initState() {
    fastSetDate(defaultFastTime);
    super.initState();
  }

  void fastSetDate(FastTimeType type) {
    var now = DateTime.now();
    DateTime startDate = DateTime(now.year, now.month, now.day, 00, 00, 00);
    DateTime endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
    if (type == FastTimeType.yesterday) {
      startDate = startDate.add(const Duration(days: -1));
      endDate = endDate.add(const Duration(days: -1));
    }
    if (type == FastTimeType.last7Day) {
      startDate = startDate.add(const Duration(days: -6));
    } else if (type == FastTimeType.last30Day) {
      startDate = startDate.add(const Duration(days: -29));
    }

    textStartTimeController.text = DateUtil.getDateStrByDateTime(startDate)!;
    textEndTimeController.text = DateUtil.getDateStrByDateTime(endDate)!;
  }

  // Widget buildDateTimeQuery(BuildContext context, {double? width}) {
  //   return HaloContainer(
  //     color: Colors.white,
  //     margin: EdgeInsets.only(
  //       left: 17.w,
  //     ),
  //     padding: EdgeInsets.symmetric(horizontal: 16.w),
  //     width: 500.w,
  //     border: Border.all(color: AppColors.borderColor, width: 1.w),
  //     borderRadius: BorderRadius.circular(6),
  //     children: [
  //       buildDateTimeQuery(context),
  //     ],
  //   );
  // }

  /// 时间选择器，
  /// showFastSelect 快速选择时间 今日/昨日/近七天/近三十天
  buildDateTimeQuery(
    BuildContext context, {
    double? height,
    BoxBorder? border,
    double? width,
    bool showFastSelect = false,
  }) {
    return HaloContainer(
      children: [
        HaloContainer(
          border: Border.all(
            color:
                selectedTimeType != 0
                    ? AppColors.accentColor
                    : AppColors.borderColor,
            width: 1.w,
          ),
          borderRadius: BorderRadius.circular(6.w),
          margin: EdgeInsets.only(left: 17.w),
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          height: height ?? 56.h,
          children: [
            GestureDetector(
              key: _startTimeKey,
              onTap: () {
                setState(() {
                  selectedTimeType = 1;
                });
                DateTimePickUtil.showStartDateTimeSelector(
                  context,
                  _startTimeKey,
                  currentTime: textStartTimeController.text,
                  endTime: textEndTimeController.text,
                  onCallBack: (DateTime dateTime) {
                    setState(() {
                      selectedTimeType = 0;
                      textStartTimeController.text =
                          DateUtil.getDateStrByDateTime(dateTime)!;
                      DateTime? endTime = DateTimePickUtil.resetDateTime(
                        textStartTimeController.text,
                        textEndTimeController.text,
                      );
                      if (endTime != null) {
                        textEndTimeController.text =
                            DateUtil.getDateStrByDateTime(endTime)!;
                      }
                    });
                  },
                    onCancelCallback: (){
                      setState(() {
                        selectedTimeType = 0;
                      });
                    }
                );
              },
              child: HaloContainer(
                direction: Axis.vertical,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(child: Container()),
                  HaloTextField(
                    controller: textStartTimeController,
                    contentPadding: 0,
                    textColor: AppColors.normalFontColor,
                    fontSize: AppPosSize.totalFontSize.sp,
                    maxLines: 1,
                    enabled: false,
                  ),
                  Expanded(child: Container()),

                  ///增加蓝色横线
                  Visibility(
                    visible: selectedTimeType == 1,
                    child: buildBlueLine(),
                  ),
                ],
              ),
            ),
            SizedBox(width: 4.w),
            HaloPosLabel(
              "~",
              textStyle: TextStyle(
                color: AppColors.normalFontColor,
                decoration: TextDecoration.none,
                fontSize: AppPosSize.totalFontSize.sp,
              ),
            ),
            SizedBox(width: 4.w),
            GestureDetector(
              key: _endTimeKey,
              onTap: () {
                setState(() {
                  selectedTimeType = 2;
                });
                DateTimePickUtil.showEndDateTimeSelector(
                  context,
                  _endTimeKey,
                  currentTime: textEndTimeController.text,
                  startTime: textStartTimeController.text,
                  onCallBack: (DateTime dateTime) {
                    setState(() {
                      selectedTimeType = 0;
                      textEndTimeController.text =
                          DateUtil.getDateStrByDateTime(dateTime)!;
                    });
                  },
                  onCancelCallback: (){
                    setState(() {
                      selectedTimeType = 0;
                    });
                  }
                );
              },
              child: HaloContainer(
                direction: Axis.vertical,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(child: Container()),
                  HaloTextField(
                    controller: textEndTimeController,
                    fontSize: AppPosSize.totalFontSize.sp,
                    contentPadding: 0,
                    textColor: AppColors.normalFontColor,
                    maxLines: 1,
                    enabled: false,
                  ),
                  Expanded(child: Container()),

                  ///增加蓝色横线
                  Visibility(
                    visible: selectedTimeType == 2,
                    child: buildBlueLine(),
                  ),
                ],
              ),
            ),
            SizedBox(width: 12.w),
            IconFont(IconNames.rili),
          ],
        ),
        buildFastDateQuery(showFastSelect),
      ],
    );
  }

  ///蓝色横线
  Widget buildBlueLine() {
    return Container(
      margin: EdgeInsets.only(left: 0.w, top: 0.w),
      height: 2.w,
      width: 250.w,
      color: AppColors.accentColor,
    );
  }

  ///快速时间筛选
  Widget buildFastDateQuery(bool showFastSelect) {
    //时间范围筛选
    return HaloContainer(
      visible: showFastSelect,
      margin: EdgeInsets.only(left: 16.w),
      borderRadius: BorderRadius.all(Radius.circular(6.w)),
      children: [
        buildFastTimeBtn(
          FastTimeType.today,
          borderRadius: BorderRadius.horizontal(left: Radius.circular(6.w)),
        ),
        buildFastTimeBtn(FastTimeType.yesterday),
        buildFastTimeBtn(FastTimeType.last7Day),
        buildFastTimeBtn(
          FastTimeType.last30Day,
          borderRadius: BorderRadius.horizontal(right: Radius.circular(6.w)),
        ),
      ],
    );
  }

  bool getDateIsCheck(FastTimeType type) {
    var now = DateTime.now();
    DateTime startDate = DateTime(now.year, now.month, now.day, 00, 00, 00);
    DateTime endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
    if (type == FastTimeType.yesterday) {
      startDate = startDate.add(const Duration(days: -1));
      endDate = endDate.add(const Duration(days: -1));
    }
    if (type == FastTimeType.last7Day) {
      startDate = startDate.add(const Duration(days: -6));
    } else if (type == FastTimeType.last30Day) {
      startDate = startDate.add(const Duration(days: -29));
    }

    return DateUtil.getDateStrByDateTime(startDate) ==
            textStartTimeController.text &&
        DateUtil.getDateStrByDateTime(endDate) == textEndTimeController.text;
  }

  Widget buildFastTimeBtn(FastTimeType type, {BorderRadius? borderRadius}) {
    bool dateIsCheck = getDateIsCheck(type);
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        var now = DateTime.now();
        DateTime startDate = DateTime(now.year, now.month, now.day, 00, 00, 00);
        DateTime endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        if (type == FastTimeType.yesterday) {
          startDate = startDate.add(const Duration(days: -1));
          endDate = endDate.add(const Duration(days: -1));
        }
        if (type == FastTimeType.last7Day) {
          startDate = startDate.add(const Duration(days: -6));
        } else if (type == FastTimeType.last30Day) {
          startDate = startDate.add(const Duration(days: -29));
        }

        textStartTimeController.text =
            DateUtil.getDateStrByDateTime(startDate)!;
        textEndTimeController.text = DateUtil.getDateStrByDateTime(endDate)!;
        setState(() {});
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
        decoration: BoxDecoration(
          borderRadius: borderRadius,
          border: Border.all(
            width: 1.w,
            color: dateIsCheck ? AppColors.accentColor : AppColors.borderColor,
          ),
        ),
        child: HaloLabel(
          fastTimeTypeName[type],
          textStyle: TextStyle(
            fontSize: AppPosSize.totalFontSize.sp,
            color:
                dateIsCheck ? AppColors.accentColor : AppColors.normalFontColor,
          ),
        ),
      ),
    );

    //
    // return Container(
    //     margin: EdgeInsets.only(left: 12.w),
    //     child: HaloButton(
    //         text: fastTimeTypeName[type],
    //         height: 56.h,
    //         buttonType: HaloButtonType.outlinedButton,
    //         fontSize: AppPosSize.totalFontSize.sp,
    //         textColor: AppColors.normalFontColor,
    //         borderColor: AppColors.borderColor,
    //         borderRadius: 6.w,
    //         outLineWidth: 1.w,
    //         onPressed: () {
    //           var now = DateTime.now();
    //           DateTime startDate =
    //               DateTime(now.year, now.month, now.day, 00, 00, 00);
    //           DateTime endDate =
    //               DateTime(now.year, now.month, now.day, 23, 59, 59);
    //           if (type == FastTimeType.yesterday) {
    //             startDate = startDate.add(const Duration(days: -1));
    //             endDate = endDate.add(const Duration(days: -1));
    //           }
    //           if (type == FastTimeType.last7Day) {
    //             startDate = startDate.add(const Duration(days: -6));
    //           } else if (type == FastTimeType.last30Day) {
    //             startDate = startDate.add(const Duration(days: -29));
    //           }
    //
    //           textStartTimeController.text =
    //               DateUtil.getDateStrByDateTime(startDate)!;
    //           textEndTimeController.text =
    //               DateUtil.getDateStrByDateTime(endDate)!;
    //         }));
  }
}

enum FastTimeType {
  ///今日
  today,

  ///昨日
  yesterday,

  ///近七天
  last7Day,

  /// 近30天
  last30Day,
}

///单据名称
const Map<FastTimeType, String> fastTimeTypeName = {
  FastTimeType.today: "今日",
  FastTimeType.yesterday: "昨日",
  FastTimeType.last7Day: "近7天",
  FastTimeType.last30Day: "近30天",
};
