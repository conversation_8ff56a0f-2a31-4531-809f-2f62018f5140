/// domain : "http://picture.qiniu.mygjp.com.cn/"
/// region : "z0"
/// thumbnailCode : "!200x200"

class QiniuViewConfig {
  String? domain;
  String? region;
  String? thumbnailCode;

  static QiniuViewConfig? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    QiniuViewConfig qiniuViewConfigBean = QiniuViewConfig();
    qiniuViewConfigBean.domain = map['domain'];
    qiniuViewConfigBean.region = map['region'];
    qiniuViewConfigBean.thumbnailCode = map['thumbnailCode'];
    return qiniuViewConfigBean;
  }

  Map toJson() => {
        "domain": domain,
        "region": region,
        "thumbnailCode": thumbnailCode,
      };
}
