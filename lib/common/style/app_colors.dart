import 'package:flutter/material.dart';

///
///@ClassName: app_colors
///@Description:颜色类
///@Author: tanglan
///@Date: 7/27/21 1:17 PM
class AppColors {
  //--------------需要跟随系统主题的颜色值 -------------------------------------
  //App主要部分的背景色（ToolBar,TabBar等)
  static const Color primaryColor = Color(0xFFFFFFFF); //主题颜色
  static const Color primaryColorDark = Color(0xFF161A22); //主题颜色

  //accentColor - Color类型，前景色(按钮、文本、覆盖边缘效果等)
  // static const Color accentColor = Color(0xFF2869FD); //主题颜色
  // static const Color accentColorDark = Color(0xFF2869FD); //主题颜色

  //标题分组 加粗 强调的文字颜色 如首页 常用功能-今日销售额推荐颜色
  static const Color titleBoldTextColor = Color(0xff333333); //标题黑色文字
  static const Color titleBoldTextColorDark = Color(0xffd2d2d2); //标题黑色文字

  //通用主要标题文字 如开单行标题
  static const Color normalTitleTextColor = Color(0xFF666666); //通用文本字体颜色
  static const Color normalTitleTextColorDark = Color(0xFF666666); //通用文本字体颜色

  //通用主要标题文字 如开单行标题
  static const Color weightTitleTextColor = Color(0xFF333333); //通用文本字体颜色
  static const Color weightTitleTextColorDark = Color(0xFF333333); //通用文本字体颜色

  //通用次要标题文字 如开单行标题
  static const Color normalSecondTitleTextColor = Color(
    0xFF999999,
  ); //非选中态 次要文本字体颜色
  static const Color normalSecondTitleTextColorDark = Color(
    0xFF999999,
  ); //非选中态 次要文本字体颜色

  //常规标题灰色、如设计界面、应用列表的标题颜色
  static const Color secondTextColor = Color(0xFF666666); //较浅通用字体颜色
  static const Color secondTextColorDark = Color(0xFF666666); //标题黑色文字

  //通用主要标题文字 如开单行标题
  static const Color normalTextColor = Color(0xFF333333); //通用文本字体颜色
  static const Color normalTextColorDark = Color(0xFF333333); //通用文本字体颜色

  static const Color hintColor = Color(0xFFE1E1E1); //文本框空值占位颜色
  static const Color hintColorDark = Color(0xFFE1E1E1); //文本框空值占位颜色

  //通用值内容 如开单行内容
  static const Color cardColor = Color(0xffffffff); //标题黑色文字
  static const Color cardColorDark = Color(0xff161A22); //标题黑色文字

  //页背景色
  static const Color scaffoldBackgroundColor = Color(0xFFF6F6F6); //标题黑色文字
  static const Color scaffoldBackgroundColorDark = Color(0xff090B0F); //标题黑色文字
  static const Color pageBackgroundColor = Color(0xFFEDEEF0); //界面背景颜色

  //分割线颜色
  static const Color dividerColor = Color(0xFFE1E1E1);
  static const Color borderColor = Color(0xFFCFCFCF);

  static const Color buttonBorderColor = Color(0xFFD9D9D9);

  static const Color txtBorderColor = Color(0xFFC9C9C9);

  //--------------不跟随系统的颜色值--------------------
  //错误文字颜色
  static const Color errorTextColor = Colors.grey;

  //红色字体
  static const Color redTextColor = Color(0xFFE42C2D);

  //分割线
  static const Color lineColor = Color(0xFFDBDBDB);

  //选中按钮边框线
  static const Color selectBtnBorder = Color(0XFFF3F6FF);

  //选中按钮背景色
  static const Color selectBtnBackgroundColor = Color(0xFF4679fc);

  //打印箭头颜色
  static const Color printDownArrowColor = Color(0xFF979797);

  //选中背景色线
  static const Color selectbackGroundColor = Color(0xFFF0F5FF);

  //不可用背景色
  static const Color unEnableBackgroundBColor = Color(0xFFF5F5F5);

  //不可用边框颜色
  static const Color unEnableBorderColor = Color(0xFFDDDDDD);

  ///2024-12-31

  ///常用标准规范色号（后期统一使用以下色号（若遇特别说明的颜色，需和UI确认））
  //region 标准色号定义
  ///主题色(所有使用主题色的颜色，若UI图存在偏差，和UI确认是否使用主题色)
  ///用于主题色的按钮背景，边框，文字
  static const Color accentColor = Color(0xFF2769FF); //主题颜色
  static const Color accentColorDark = Color(0xFF2769FF); //主题颜色

  ///通用界面背景色
  static const Color pageBackgroundDark = Color(0xFFF1F3F3); //界面背景色

  //region 字体颜色
  ///常规字体颜色（用于标题、重要核心文字、重点文字，常规内容信息）

  static const Color normalFontColor = Color(0xFF333333);

  ///次要字体颜色（用于副本文字的展示或次级重要的文本颜色）
  static const Color secondaryFontColor = Color(0xFF666666);

  ///描述性字体颜色（用于描述或者备注性文字，引用性文字，次要按钮文字，分割线色号）
  static const Color describeFontColor = Color(0xFF999999);

  ///提示性字体颜色（用于hint的水印提示等文字）
  static const Color tipsFontColor = Color(0xFFBBBBBB);

  ///红色金额字体颜色（用于需要红色金额的色号）
  static const Color totalFontColor = Color(0xFFFF2700);

  ///按钮边框颜色
  static const Color btnBorderColor = Color(0xFFC3C3C3);

  ///特殊使用场景
  ///体重
  static const Color weightBackgroundFontColor = Color(0xFFF1F4F8); //称重
  static const Color levelFontColor = Color(0xFF77310B); //等级字体
  static const Color levelBackgroundColor = Color(0xFFFFe9B6); //等级背景色
  static const Color promotionTipsFontColor = Color(0xFFFF6501); //促销提示
  static const Color billMarkBgColor = Color(0xFFFFF1F0); //单据标签背景色
  static const Color billMarkFontColor = Color(0xFFFE4343); //单据标签字体色
  static const Color payedStateColor = Color(0xFF1BB745); //已支付颜色
  static const Color unPayStateColor = Color(0xFFEC2E16); //已支付边框颜色

  static const Color dasherLineColor = Color(0xFF393536); //已支付边框颜色
  //endregion
  //endregion
}
