import 'package:flutter/material.dart';
import '../../../common/style/app_colors.dart';

///
///@ClassName: app_color_helper
///@Author: tanglan
///@Date: 7/29/21 2:34 PM
class AppColorHelper {
  BuildContext context;

  AppColorHelper(this.context);

  //验证是否为黑色模式
  bool isDarkMode() {
    return Theme.of(context).brightness == Brightness.dark;
  }

  //获取主题色  actionBar tabBar 等
  Color getPrimaryColor() {
    return Theme.of(context).primaryColor;
  }

  //获取强调色 如按钮、复选框的前景颜色
  Color getAccentColor() {
    return Theme.of(context).colorScheme.secondary;
  }

  Color getScaffoldBackgroundColor() {
    return Theme.of(context).scaffoldBackgroundColor;
  }

  //错误文字颜色
  Color getErrorTextColor() {
    return AppColors.errorTextColor;
  }

  //文本标题强调色，如:黑色加粗的字体颜色 //深色标题 #333333
  Color getTitleBoldTextColor() {
    return Theme.of(context).textTheme.displayLarge?.color ?? Colors.black;
  }

  //常规标题灰色、如设计界面、应用列表的标题颜色 0xFF666666
  Color getTitleTextColor() {
    return Theme.of(context).textTheme.titleLarge?.color ?? Colors.black;
  }

  //常规标题灰色、如设计界面、应用列表的标题颜色  #999999
  Color getSubTitleTextColor() {
    return Theme.of(context).textTheme.titleMedium?.color ?? Colors.black;
  }

  //通用主要标题文字 如开单行标题 次要文本颜色 #666666
  Color getNormalTitleTextColor() {
    return Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black;
  }

  //通用值内容 如开单行内容 主要文本颜色 #333333
  Color getNormalValueTextColor() {
    return Theme.of(context).textTheme.bodyMedium?.color ?? Colors.black;
  }

  //通用值内容 如开单行内容 主要文本颜色 #333333
  Color getAppBarColor() {
    return Colors.white;
  }
}
