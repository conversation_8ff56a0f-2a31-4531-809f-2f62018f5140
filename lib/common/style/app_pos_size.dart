import 'package:halo_utils/halo_utils.dart';

///
///@ClassName: app_font_size
///@Description: 字号大小定义
///@Author: tanglan
///@Date: 2024/12/31
///
class AppPosSize {
  ///2024-12-31

  ///常用标准规范字号（后期统一使用以下色号（若遇特别说明的字号，需和UI确认））
//region 标准色号定义
  ///看板字号(用于看板标题，或者统计数据展示)
  static const double boardFontSize = 28;

//region 字体字号
  ///一级标题字号（用于一级标题、）
  static const double firstTitleFontSize = 24;

  ///标准字号/正常字号/二级标题字体字号（用于副本文字的展示或次级重要文字）
  static const double secondaryTitleFontSize = 22;

  ///金额展示字号（用于hint的水印提示等文字）
  static const double totalFontSize = 20;

  ///内容字体字号（ 用于副本文字的展示或次级重要文字）
  static const double contentFontSize = 18;

  ///描述性字体字号（用于描述或者备注性文字，引用性文字）
  static const double describeFontSize = 16;

  ///提示字号（用于hint的水印提示等文字）
  static const double tipsFontSize = 14;

//endregion

  ///按钮图标尺寸
  static const double btnIconSize = 20;

  ///按钮图标尺寸
  static const double btnHeight = 70;
//endregion
}
