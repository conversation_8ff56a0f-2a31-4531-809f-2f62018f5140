import 'dart:ffi';
import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/bill/entity/goods_bill.dto.dart';
import 'package:halo_pos/common/tool/payment_orders_mixin.dart';
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_pos/common/tool/tts_util.dart';
import 'package:halo_pos/widgets/halo_pos_label.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/math_util.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_dialog.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../bill/entity/bill_save_exception_dto.dart';
import '../../bill/entity/bill_save_reslut_dto.dart';
import '../../bill/entity/bill_save_result_type.dart';
import '../../bill/entity/pay_result_dto.dart';
import '../../bill/model/bill_model.dart';
import '../../bill/settlement/entity/atype_info_bean.dart';
import '../../bill/settlement/widget/exception_widget.dart';
import '../../bill/tool/bill_tool.dart';
import '../../bill/tool/sale_event_buds.dart';
import '../../bill/widget/exception_item_widget.dart';
import '../../bill/widget/payment_error_dialog.dart';
import '../../cashierbox/entity/cash_box_payment_request_dto.dart';
import '../../enum/bill_type.dart';
import '../../enum/payment_enum.dart';
import '../../enum/sync_info_type.dart';
import '../../login/entity/store/store_payment.dart';
import '../../plugin/notify_voice_plugin.dart';
import '../../widgets/Info_sync_dialog.dart';
import '../login/login_center.dart';
import '../string_res.dart';
import 'dialog_util.dart';

//region 支付工具类
/// 支付相关工具类
/// 用于处理支付流程、结果查询和错误处理
class PaymentUtil {
  /// 单例实例
  static final PaymentUtil instance = PaymentUtil._();

  /// 私有构造函数
  PaymentUtil._();

  //region 常量定义
  /// 支付状态提示文案
  static const String _STATUS_PROCESSING = "支付中...";
  static const String _STATUS_CONFIRMING = "正在确认支付状态";
  static const String _STATUS_WAITING_PASSWORD = "等待用户输入密码确认";

  /// 弹窗类型
  static const String _DIALOG_TYPE_WAITING = "waiting_payment";
  static const String _DIALOG_TYPE_ERROR = "error_dialog";
  //endregion

  //region 成员变量
  /// 轮询定时器Map, key为outNo
  final Map<String, Timer> _pollTimers = {};

  /// 当前显示的弹窗key
  String? _currentDialogKey;

  /// 当前显示的弹窗上下文
  BuildContext? _currentDialogContext;

  /// 当前弹窗内容通知器
  ValueNotifier<PaymentErrorDialogData>? _dialogNotifier;

  /// 正在处理的支付结果Map, key为outNo
  final Map<String, bool> _processingResults = {};
  //endregion

  //region 弹窗管理
  /// 关闭当前弹窗
  void _closeCurrentDialog(BuildContext context) {
    try {
      if (_currentDialogContext != null && _currentDialogContext!.mounted) {
        Navigator.maybeOf(_currentDialogContext!)?.pop();
      }
    } catch (e) {
      // 忽略关闭过程中的错误
    } finally {
      _currentDialogKey = null;
      _currentDialogContext = null;
      _dialogNotifier = null;
    }
  }
  //endregion

  //region 支付结果查询
  /// 开始轮询查询支付结果
  /// [context] 上下文
  /// [payResult] 支付结果
  /// [onActionListener] 操作回调
  void startQueryPayResult(BuildContext context, PayResult payResult,
      Function(int value) onActionListener) {
    String outNo = payResult.payInfo!["outNo"] ?? "";

    // 如果已经存在轮询任务，先停止
    stopQueryPayResult(outNo);

    // 重置处理状态
    _processingResults[outNo] = false;

    if (payResult.payInfo!["queryTypeEnum"] == null) {
      payResult.payInfo!["queryTypeEnum"] = payResult.queryType ?? "PAY";
    }

    // vchcode
    String vchcode = payResult.resultDTO?.vchcode??payResult.payInfo!["vchcode"]??"";


    // 显示等待支付弹窗
    if (context.mounted) {
      showErrorInfo(context, _STATUS_CONFIRMING, errorTitle: _STATUS_PROCESSING,
          onActionListener: (index) {
        stopQueryPayResult(outNo);
        onActionListener(index);
      });
    }

    // 创建新的轮询任务
    Timer timer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!context.mounted) {
        stopQueryPayResult(outNo);
        return;
      }

      // 如果已经在处理结果，不再发送新的请求
      if (_processingResults[outNo] == true) {
        stopQueryPayResult(outNo);
        return;
      }

      BillModel.queryPayResult(context, payResult.payInfo!, {}, vchcode).then((value) {
        // 如果context已经不可用，或者已经在处理其他结果，直接返回
        if (!context.mounted || _processingResults[outNo] == true) {
          return;
        }
        if (PayResultType.WAITING_PASSWORD == PayResultTypeData[value.status]) {
          showErrorInfo(context, _STATUS_WAITING_PASSWORD,
              errorTitle: _STATUS_PROCESSING, onActionListener: (index) {
            stopQueryPayResult(outNo);
            onActionListener(index);
          });
        } else if (PayResultType.PENDING == PayResultTypeData[value.status]) {
          showErrorInfo(context, _STATUS_CONFIRMING,
              errorTitle: _STATUS_PROCESSING, onActionListener: (index) {
            stopQueryPayResult(outNo);
            onActionListener(index);
          });
        }

        if (PayResultType.SUCCEEDED == PayResultTypeData[value.status] ||
            PayResultType.FAILED == PayResultTypeData[value.status] ||
            PayResultType.CANCELLED == PayResultTypeData[value.status]) {
          // 立即标记为正在处理，防止其他请求的结果被处理
          _processingResults[outNo] = true;

          // 先停止轮询，防止新的请求
          stopQueryPayResult(outNo);

          // 关闭等待支付弹窗
          if (_currentDialogKey == _DIALOG_TYPE_WAITING) {
            _closeCurrentDialog(context);
          }

          // 处理支付结果
          processingPayResult(context, value,
              result: (PayResultType resultType) async {
            if (resultType == PayResultType.SUCCEEDED) {
              onActionListener(2);
            }
          }, onActionListener: onActionListener);
        }
      }).catchError((error) {
        if (!context.mounted || _processingResults[outNo] == true) {
          return;
        }

        // 发生错误时标记为已处理并停止轮询
        _processingResults[outNo] = true;
        stopQueryPayResult(outNo);

        // 关闭等待支付弹窗
        if (_currentDialogKey == _DIALOG_TYPE_WAITING) {
          _closeCurrentDialog(context);
        }

        showErrorInfo(context, "查询支付结果失败", onActionListener: onActionListener);
      });
    });

    _pollTimers[outNo] = timer;
  }

  /// 停止轮询查询支付结果
  /// [outNo] 订单号
  void stopQueryPayResult(String outNo) {
    _pollTimers[outNo]?.cancel();
    _pollTimers.remove(outNo);
  }

  /// 停止所有轮询任务
  void stopAllQueryPayResult() {
    for (var timer in _pollTimers.values) {
      timer.cancel();
    }
    _pollTimers.clear();
    _processingResults.clear();
  }

  //endregion

  //region 单据校验
  /// 校验商品单据
  /// [context] 上下文
  /// [goodsBillDto] 商品单据数据
  /// [successCallback] 成功回调
  static validaGoodsBill(BuildContext context, GoodsBillDto goodsBillDto,
      ValueChanged successCallback) {
    Map<String, dynamic> data = goodsBillDto.toJson();
    BillModel.validaGoodsBill(context, data).then((value) {
      switch (value.resultType) {
        case BillSaveResultType.SUCCESS:
          successCallback(value);
          break;
        case BillSaveResultType.ERROR:
          DialogUtil.showAlertDialog(
            context,
            childContent: Container(
              margin: const EdgeInsets.only(top: 10),
              child: ExceptionWidget(
                value.exceptionInfo!,
                (context, index, BillSaveExceptionDto itemData) {
                  return ExceptionItemWidget(
                    itemData.message ??
                        StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
                    BillTool.createSaveExceptionMessage(
                        itemData.detailList ?? []),
                    errorList: const [],
                  );
                },
              ),
            ),
            title: value.message ?? "",
          );
          break;
        case BillSaveResultType.INIOVER:
          HaloToast.showError(context,
              msg: value.exceptionInfo?.firstOrNull?.message ?? "支付失败");
          break;
        case BillSaveResultType.CONFIRM:
          PaymentUtil.instance
              .handleConfirmCase(context, value, successCallback);
          break;
        default:
          HaloToast.showError(context, msg: "未知错误");
          break;
      }
    });
  }

  /// 处理确认场景
  /// [context] 上下文
  /// [value] 单据保存结果
  /// [successCallback] 成功回调
  void handleConfirmCase(BuildContext context, BillSaveResultDto value,
      ValueChanged successCallback) {
    if (value.message != null && value.message!.contains("促销核验不通过")) {
      DialogUtil.showConfirmDialog(
        context,
        actionLabels: ["更新促销", "继续"],
        confirmCallback: () {
          successCallback(value);
        },
        cancelCallback: () {
          HaloDialog(context,
              child: InfoSyncDialog(
                context,
                sycInfoList: const [SyncInfoType.promotion],
                isAutoClose: true,
                afterSync: () async {
                  SaleEventBus.getInstance()
                      .fire(SaleEventBus.updateGoodsDetail);
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  }
                },
              )).show();
        },
        title: value.message!,
      );
    } else {
      DialogUtil.showConfirmDialog(
        context,
        actionLabels: ["取消", "继续"],
        confirmCallback: () {
          successCallback(value);
        },
        childContent: Container(
          margin: const EdgeInsets.only(top: 10),
          child: ExceptionWidget(
            value.exceptionInfo!,
            (context, index, BillSaveExceptionDto itemData) {
              return ExceptionItemWidget(
                itemData.message ??
                    StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
                BillTool.createSaveExceptionMessage(itemData.detailList ?? []),
                errorList: const [],
              );
            },
          ),
        ),
        title: value.message!,
      );
    }
  }

  //endregion

  //region 支付结果处理
  /// 处理支付结果
  /// [context] 上下文
  /// [payResult] 支付结果
  /// [result] 结果回调
  /// [onActionListener] 操作回调
  static processingPayResult(BuildContext context, PayResult? payResult,
      {required Function(PayResultType resultType) result,
      required Function(int value) onActionListener}) {
    if (!context.mounted) return;

    if (payResult != null) {
      if (PayResultType.SUCCEEDED == PayResultTypeData[payResult.status]) {
        result(PayResultType.SUCCEEDED);
        if (context.mounted) {
          HaloToast.show(context, msg: "交易成功");
          NotifyVoicePlugin.success();
        }
      } else if (PayResultType.CANCELLED ==
          PayResultTypeData[payResult.status]) {
        result(PayResultType.CANCELLED);
        String? errorMsg = "交易失败";
        if (payResult.tradeResult?.errorMsg != null) {
          errorMsg = payResult.tradeResult?.errorMsg;
        } else if (payResult.resultDTO?.message != null) {
          errorMsg = payResult.resultDTO?.message;
        }
        if (context.mounted) {
          PaymentUtil.instance.showErrorInfo(context, errorMsg!,
              onActionListener: onActionListener,
              status: payResult.status ?? "");
        }
      } else if (PayResultType.PENDING == PayResultTypeData[payResult.status] ||
          PayResultType.WAITING_PASSWORD ==
              PayResultTypeData[payResult.status]) {
        if (payResult.payInfo == null) {
          if (context.mounted) {
            PaymentUtil.instance.showErrorInfo(
              context,
              payResult.message ?? "交易失败",
            );
          }
          return;
        }

        ///轮训的outNo设置为返回的outNo
        payResult.payInfo!["outNo"] = payResult.outNo;
        result(PayResultType.PENDING);
        if (context.mounted) {
          PaymentUtil.instance
              .startQueryPayResult(context, payResult, onActionListener);
        }
      } else {
        result(PayResultType.FAILED);
        if (context.mounted) {
          PaymentUtil.instance.showErrorInfo(
            context,
            payResult.message ?? "交易失败",
            onActionListener: onActionListener,
          );
        }
      }
    } else {
      result(PayResultType.FAILED);
      if (context.mounted) {
        PaymentUtil.instance
            .showErrorInfo(context, "支付失败", onActionListener: onActionListener);
      }
    }
  }

  /// 获取支付查询类型枚举
  /// [resultDTO] 单据保存结果
  getPaymentQueryTypeEnum(BillSaveResultDto? resultDTO) {
    if (resultDTO == null) {
      return PaymentQueryTypeEnum.PAY;
    }
    if (resultDTO.vchtype == BillTypeData[BillType.SaleChangeBill]) {
      return PaymentQueryTypeEnum.PAY;
    }
  }

  /// 查询支付状态
  /// [context] 上下文
  /// [payResult] 支付结果
  /// [onActionListener] 操作回调
  queryPayStatus(BuildContext context, PayResult payResult,
      Function(int value) onActionListener) {
    if (payResult.payInfo!["queryTypeEnum"] == null) {
      payResult.payInfo!["queryTypeEnum"] = payResult.queryType ?? "PAY";
    }
    BillModel.queryPayStatus(context, payResult.payInfo!, {})
        .then((value) async {
      processingPayResult(context, value,
          result: (PayResultType resultType) async {
        if (resultType == PayResultType.SUCCEEDED) {
          onActionListener(0);
        }
      }, onActionListener: onActionListener);
    });
  }

  //endregion

  //region 错误处理
  /// 显示错误信息
  /// [context] 上下文
  /// [error] 错误信息
  /// [errorTitle] 错误标题
  /// [onActionListener] 操作回调
  /// [status] 状态
  showErrorInfo(BuildContext context, String error,
      {String? errorTitle,
      Function(int value)? onActionListener,
      String status = "FAILED"}) {
    if (!context.mounted) return;

    if (PayResultType.CANCELLED == PayResultTypeData[status]) {
      TTSUtil()
        ..initTTS()
        ..speak("顾客取消扫码支付");
    } else if (errorTitle != _STATUS_PROCESSING) {
      TTSUtil()
        ..initTTS()
        ..speak("扫码支付失败，请稍后再试");
      NotifyVoicePlugin.failure();
    }

    if (!context.mounted) return;

    // 根据不同场景设置不同的dialogKey
    String dialogKey = errorTitle == _STATUS_PROCESSING
        ? _DIALOG_TYPE_WAITING
        : _DIALOG_TYPE_ERROR;

    // 检查当前弹窗是否仍然存在
    bool isDialogStillShowing = false;
    if (_currentDialogContext != null && _currentDialogContext!.mounted) {
      try {
        // 尝试获取当前弹窗的上下文，如果弹窗已关闭会抛出异常
        final navigator = Navigator.maybeOf(_currentDialogContext!);
        isDialogStillShowing = navigator != null;
      } catch (e) {
        isDialogStillShowing = false;
      }
    }

    // 如果已经存在相同类型的弹窗且弹窗仍在显示，只更新内容
    if (_currentDialogKey == dialogKey &&
        _dialogNotifier != null &&
        isDialogStillShowing) {
      _dialogNotifier!.value = PaymentErrorDialogData(
        errorTitle: errorTitle,
        errorDesc: error,
        onActionListener: onActionListener,
      );
      return;
    }

    // 如果弹窗已关闭但引用未清除，先清除引用
    if (!isDialogStillShowing) {
      _currentDialogKey = null;
      _currentDialogContext = null;
      _dialogNotifier = null;
    } else {
      // 关闭当前错误弹窗
      _closeCurrentDialog(context);
    }

    // 创建新的通知器
    _dialogNotifier = ValueNotifier(PaymentErrorDialogData(
      errorTitle: errorTitle,
      errorDesc: error,
      onActionListener: onActionListener,
    ));

    _currentDialogKey = dialogKey;

    DialogUtil.showAlertDialog(
      context,
      dismissOnTouchOutside: false,
      child: Builder(
        builder: (dialogContext) {
          _currentDialogContext = dialogContext;
          return PaymentErrorDialog(
            errorTitle: errorTitle,
            errorDesc: error,
            onActionListener: onActionListener,
            notifier: _dialogNotifier,
            onDialogClose: () {
              // 弹窗被关闭时清理资源
              _currentDialogKey = null;
              _currentDialogContext = null;
              _dialogNotifier = null;
            },
          );
        },
      ),
    );
  }

  //endregion

  //region 现金支付处理
  /// 设置现金支付失败
  /// [context] 上下文
  /// [pettyCash] 零钱
  /// [paymentList] 支付方式列表
  /// [paymentType] 支付类型
  static CashBoxPaymentRequestDto? setCashPaymentFail(BuildContext context,
      String pettyCash, List<AtypeInfoBean> paymentList, int paymentType) {
    AtypeInfoBean? cashPayment = paymentList.firstWhere(
        (element) => element.enable && element.storePayway.paywayType == 0,
        orElse: () => AtypeInfoBean(StorePayway(), "0"));
    if (cashPayment.storePayway.id != null) {
      //如果选择了现金支付，则打开钱箱
      String cashierId = SpTool.getCashierInfo().id!;
      String otypeId = SpTool.getStoreInfo()!.otypeId!;
      String etypeId = LoginCenter.getLoginUser().employeeId!;
      double num =
          MathUtil.subtraction(cashPayment.total, pettyCash).toDouble();
      //3为销售存入，4为退货取出
      if (StringUtil.isNotEmpty(cashierId) &&
          StringUtil.isNotEmpty(otypeId) &&
          StringUtil.isNotEmpty(etypeId)) {
        return CashBoxPaymentRequestDto(
            num, paymentType, cashierId, etypeId, otypeId);
      } else {
        HaloToast.showError(context, msg: "钱箱参数不全");
        return null;
      }
    }
    return null;
  }
//endregion
}
//endregion
