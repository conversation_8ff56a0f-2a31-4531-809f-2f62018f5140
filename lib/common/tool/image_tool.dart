import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/widget/halo_toast.dart';
import 'package:haloui/widget/photo/entity.dart';
import 'package:haloui/widget/photo/photo_picker.dart';
import 'package:haloui/widget/photo/ui/options.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:ui' as ui;

///
///@ClassName: image_tool
///@Description: 图片视频操作工具
///@Author: tanglan
///@Date: 2024/8/14
class ImageTool {
  ///打开相册选择图片或视频
  ///图片最多5张
  static pickGallery(
    BuildContext context, {
    bool isVideo = false,
    int maxSelectCount = 1,
    Function(List<String>?)? onSelectBack,
    String? saveDir,
  }) async {
    List<String>? mediaList;
    if (Platform.isWindows) {
      mediaList = await _pickWindows(context, isVideo);
    } else if (Platform.isAndroid) {
      mediaList = await _pickAndroid(
        context,
        isVideo,
        maxSelectCount: maxSelectCount,
      );
    }
    if (null != onSelectBack) {
      onSelectBack(mediaList);
    }
  }

  static Future<List<String>> _pickWindows(
    BuildContext context,
    bool isVideo,
  ) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: isVideo ? FileType.video : FileType.image,
      allowMultiple: !isVideo,
      allowedExtensions: isVideo ? ['mp4'] : ['jpg', 'png'],
    );
    if (result != null) {
      if (isVideo) {
        if (result.files.single.size > 5 * 1024 * 1024) {
          if (context.mounted) {
            HaloToast.show(context, msg: "选取的视频过大");
          }
          return [];
        }
      }
      return result.paths
          .where((e) => !TextUtil.isEmpty(e))
          .cast<String>()
          .toList();
    }
    return [];
  }

  static Future<List<String>> _pickAndroid(
    BuildContext context,
    bool isVideo, {
    int maxSelectCount = 1,
    String? saveDir,
  }) async {
    List<AssetEntity>? resultList = await PhotoPicker.pickAsset(
      context: context,
      maxSelected: maxSelectCount,
      rowCount: 5,
      pickType: isVideo ? PickType.onlyVideo : PickType.onlyImage,
      onPermissionFailed: (permission) {
        HaloToast.show(context, msg: "请授予图库权限");
      },
    );
    if (null != resultList && resultList.isNotEmpty) {
      if (isVideo) {
        File file = await resultList.first.file;
        if ((await file.length()) > 5 * 1024 * 1024) {
          if (context.mounted) {
            HaloToast.show(context, msg: "选取的视频过大");
          }
          return [];
        }
      }

      List<String> mediaList =
          await Stream.fromFutures(
            resultList.map(
              (e) async =>
                  await _copyFile(
                    (await e.file).path,
                    isVideo: isVideo,
                    saveDir: saveDir,
                  ) ??
                  "",
            ),
          ).where((event) => !TextUtil.isEmpty(event)).toList();
      return mediaList;
    }
    return [];
  }

  ///拷贝文件
  static Future<String?> _copyFile(
    String filePath, {
    isVideo = false,
    String? saveDir,
  }) async {
    // "/storage/emulated/0/Android/data/packname/files"
    Directory? appDir = await getExternalStorageDirectory();
    if (appDir != null) {
      String path = appDir.path;
      if (StringUtil.isNotEmpty(saveDir)) {
        path = "$path/$saveDir";
      }
      String otypeId = SpTool.getStoreInfo()!.otypeId ?? "default";
      path = "$path/$otypeId/${isVideo ? "video" : "image"}";
      Directory dir = Directory(path);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
      File source = File(filePath);
      File result = await source.copy(
        dir.path +
            source.path.substring(
              source.path.lastIndexOf("/"),
              source.path.length,
            ),
      );
      return result.path;
    }
    return null;
  }

  static Future<String> buildImage(
    String? url, {
    bool localFile = false,
    int imgWidth = 300,
  }) async {
    if (StringUtil.isEmpty(url)) {
      return "";
    }

    Uint8List? imageBytes = Uint8List(0);

    ///本地图片
    if (localFile) {
      File file = File(url!);
      imageBytes = file.readAsBytesSync();
      return base64Encode(await compressImage(imageBytes, imgWidth));
    } else {
      ///网络图片
      try {
        imageBytes =
            (await NetworkAssetBundle(
              Uri.parse(getThumbnailUrl(url)),
            ).load(url!)).buffer.asUint8List();
      } catch (e) {
        ByteData data = await rootBundle.load(
          "assets/images/no_photo_black.png",
        );
        return base64Encode(data.buffer.asUint8List());
      }
    }
    return base64Encode(await compressImage(imageBytes, imgWidth));
  }

  static String getThumbnailUrl(String? url) {
    if (StringUtil.isEmpty(url) ||
        !url!.contains("picture.qiniu.mygjp.com.cn") ||
        url.contains("200x200")) {
      return url ?? "";
    }

    return "$url!200x200";
  }

  ///压缩图片大小
  static Future<Uint8List> compressImage(
    Uint8List? imageBytes,
    int targetSize,
  ) async {
    if (null == imageBytes) {
      return Uint8List(0);
    }

    return await ImageTool.decodeAndResizeToPng(
      imageBytes,
      targetWidth: targetSize,
      targetHeight: targetSize,
    );
  }

  /// 解码图片并保持原图宽高比例，原图小于目标尺寸时不放大
  static Future<ui.Image> decodeImageFromBytes(
    Uint8List bytes, {
    int? targetWidth,
    int? targetHeight,
    bool allowUpscaling = false,
  }) async {
    // 先解码图片获取原始尺寸
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();
    final image = frame.image;

    final originalWidth = image.width;
    final originalHeight = image.height;

    // 如果未指定目标宽高，直接返回原图
    if (targetWidth == null && targetHeight == null) {
      return image;
    }

    // 计算缩放比例
    double scale = 1.0;

    if (targetWidth != null && targetHeight != null) {
      final widthScale = targetWidth / originalWidth;
      final heightScale = targetHeight / originalHeight;
      scale = widthScale < heightScale ? widthScale : heightScale;
    } else if (targetWidth != null) {
      scale = targetWidth / originalWidth;
    } else if (targetHeight != null) {
      scale = targetHeight / originalHeight;
    }

    // 如果不允许放大，且原图小于目标尺寸，则保持原图尺寸
    if (!allowUpscaling && scale > 1.0) {
      return image;
    }

    // 计算目标尺寸（保持比例）
    final int scaledWidth = (originalWidth * scale).round();
    final int scaledHeight = (originalHeight * scale).round();

    // 使用目标尺寸重新解码图片
    final resizedCodec = await ui.instantiateImageCodec(
      bytes,
      targetWidth: scaledWidth,
      targetHeight: scaledHeight,
      allowUpscaling: allowUpscaling,
    );

    final resizedFrame = await resizedCodec.getNextFrame();
    return resizedFrame.image;
  }

  /// 解码图片并返回压缩后的 Uint8List（PNG 格式）
  static Future<Uint8List> decodeAndResizeToPng(
    Uint8List bytes, {
    int? targetWidth,
    int? targetHeight,
  }) async {
    final image = await decodeImageFromBytes(
      bytes,
      targetWidth: targetWidth,
      targetHeight: targetHeight,
    );

    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }
}
