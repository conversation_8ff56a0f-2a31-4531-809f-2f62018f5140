import 'package:flutter_tts/flutter_tts.dart';
import 'package:get/get.dart';

import 'sp_tool.dart';

class TTSUtil {
  TTSUtil._();

  static TTSUtil? _manager;

  factory TTSUtil() {
    _manager ??= TTSUtil._();
    return _manager!;
  }

  late FlutterTts flutterTts;

  initTTS() {
    flutterTts = FlutterTts();
  }

  Future speak(String? text) async {
    if (!SpTool.getSetting().openTTS) return;

    ///获取语言
    ///匹配中文
    final regex = RegExp("^zh.*");
    List<dynamic> languages = await flutterTts.getLanguages;

    ///模糊查找第一个中文包并设置默认语言en-US
    String language =
        languages.firstWhereOrNull((element) => regex.hasMatch(element)) ??
            "en-US";

    ///设置语言
    await flutterTts.setLanguage(language);

    /// 设置音量
    await flutterTts.setVolume(1.2);

    /// 设置语速
    await flutterTts.setSpeechRate(0.5);

    /// 音调
    await flutterTts.setPitch(1.0);

    if (text != null) {
      if (text.isNotEmpty) {
        await _stop();
        await flutterTts.speak(text);
      }
    }
  }

  /// 暂停
  Future _pause() async {
    await flutterTts.pause();
  }

  /// 结束
  Future _stop() async {
    await flutterTts.stop();
  }
}
