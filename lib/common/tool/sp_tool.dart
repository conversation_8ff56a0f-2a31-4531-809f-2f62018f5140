import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/entity/print/print_config_info.dart';
import 'package:halo_pos/entity/print/print_decorate_config.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/utils/jwt_utils.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:intl/intl.dart';

import '../../bill/entity/bill_promotion_info_dto.dart';
import '../../bill/settlement/entity/score_configuration.dart';
import '../../bill/tendermanager/entity/tender_manage_config.dart'
    hide ColumnConfig;
import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import '../../common/qi_niu_view_config.dart';
import '../../db/database_config.dart';
import '../../entity/system/column_config.dart';
import '../../entity/system/permission_dto.dart';
import '../../entity/system/system_config_dto.dart';
import '../../entity/update/version_info.dart';
import '../../enum/setting/print_width_type.dart';
import '../../login/entity/store/store_cashier.dart';
import '../../login/entity/store/store_info.dart';
import '../../login/model/login_user_model.dart';
import '../../print/tool/print_config.dart';
import '../../print/tool/print_tool.dart';
import '../../settting/entity/setting_dto.dart';
import '../login/login_center.dart';
import '../string_res.dart';
import 'image_tool.dart';
import 'sp_custom_util.dart';

///
///@ClassName: sp_tool
///@Description: sp存储
///@Author: tanglan
///@Date: 8/2/21 2:39 PM
///
const String LOG_TAG = "loginLog"; //日志tag
const String LOGIN_USER = "login_user"; //
const String VERSION_INFO = "version_info"; //
const String PROFILE = "profile";

class SpTool {
  ///隐藏默认构造函数
  SpTool._();

  ///到职员维度的本地缓存信息
  static String get loginKey =>
      "${LoginCenter.getLoginUser()?.profileId}_${LoginCenter.getLoginUser()?.employeeId}";

  ///到门店维度的本地缓存信息
  static String get loginStoreKey =>
      "${LoginCenter.getLoginUser()?.profileId}_${getStoreInfo()?.otypeId}";

  static saveLoginUser(LoginUserModel loginUser) async {
    LogUtil.e("*****登陆成功账号信息*****${loginUser.toString()}", tag: LOG_TAG);
    await SpCustomUtil.putString(
      PROFILE,
      parseJwt(loginUser.authorization ?? ""),
    );
    await SpCustomUtil.putString(LOGIN_USER, jsonEncode(loginUser));
  }

  static saveVersionInfo(VersionInfo? versionInfo) async {
    LogUtil.e("*****versionInfo信息*****${versionInfo.toString()}", tag: LOG_TAG);
    await SpCustomUtil.putString(
      "${loginKey}_$VERSION_INFO",
      jsonEncode(versionInfo),
    );
  }

  static saveModule(Map<String, dynamic>? moduleInfo) async {
    await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.module}",
      jsonEncode(moduleInfo),
    );
  }

  static Map<String, dynamic> getModule() {
    String? module = SpCustomUtil.getString("${loginKey}_${_SpKey.module}");
    if (module != null && module.isNotEmpty) {
      return jsonDecode(module);
    } else {
      return {"storeFunc": false};
    }
  }

  static VersionInfo? getVersionInfo() {
    String? versionInfo = SpCustomUtil.getString("${loginKey}_$VERSION_INFO");
    if (StringUtil.isEmpty(versionInfo)) {
      return null;
    }
    return VersionInfo.fromMap(jsonDecode(versionInfo!));
  }

  static LoginUserModel getLoginUser() {
    String? loginUser = SpCustomUtil.getString(LOGIN_USER);
    if (StringUtil.isEmpty(loginUser)) {
      return LoginUserModel();
    }
    return LoginUserModel.fromMap(jsonDecode(loginUser!));
  }

  static String? getProfile() {
    return SpCustomUtil.getString(PROFILE);
  }

  static Future<bool?> clearLoginUser() async {
    return await SpCustomUtil.remove(LOGIN_USER);
  }

  static LoginUserModel decodeJWtAndUpdateUser(String authorization) {
    Map<String, dynamic> profileInfo = jsonDecode(
      JWTUtils.parseJwt(authorization)!,
    );
    return LoginUserModel.fromMap(profileInfo);
  }

  static String parseJwt(String jwt) {
    final parts = jwt.split('.');
    if (StringUtil.isEmpty(jwt)) {
      return "";
    }
    if (parts.length != 3) {
      throw Exception(StringRes.LOGIN_CENTER_VALID_ERROR);
    }
    final payload = _decodeBase64(parts[1]);
    final payloadMap = json.decode(payload);
    if (payloadMap is! Map<String, dynamic>) {
      throw Exception(StringRes.LOGIN_CENTER_ERROR);
    }
    return payloadMap["profile"];
  }

  static String _decodeBase64(String str) {
    String output = str.replaceAll('-', '+').replaceAll('_', '/');
    switch (output.length % 4) {
      case 0:
        break;
      case 2:
        output += '==';
        break;
      case 3:
        output += '=';
        break;
      default:
        throw Exception('Illegal base64url string!"');
    }
    return utf8.decode(base64Url.decode(output));
  }

  static Future<bool?> saveShopConfig(Map<String, dynamic> shopConfig) async {
    return await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.SHOP_CONFIG}",
      jsonEncode(shopConfig),
    );
  }

  static Future saveQiNiuConfig(Map<String, dynamic>? qiNiuConfig) async {
    if (null == qiNiuConfig) {
      return;
    }
    await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.QINIU_VIEWCONFIG}",
      jsonEncode(qiNiuConfig),
    );
  }

  ///保存七牛配置
  static Future saveQiNiuViewConfig(BuildContext context) async {
    await HttpUtil.request(
      context,
      method: RequestMethod.GET_QINIU_VIEW_CONFIG,
    ).then((value) {
      SpCustomUtil.putString(
        "${loginKey}_${_SpKey.QINIU_VIEWCONFIG}",
        jsonEncode(value.data),
      );
    });
  }

  ///获取七牛配置
  static QiniuViewConfig? getQiNiuViewConfig() {
    String? configStr = SpCustomUtil.getString(
      "${loginKey}_${_SpKey.QINIU_VIEWCONFIG}",
    );
    if (!StringUtil.isEmpty(configStr)) {
      return QiniuViewConfig.fromMap(jsonDecode(configStr!));
    }
    return null;
  }

  ///获取组装好的图片url地址
  static String getQiNiuThumbnail(String picName, bool isThumb) {
    if (StringUtil.isEmpty(picName)) {
      return "";
    }
    var config = getQiNiuViewConfig();
    if (config == null ||
        config.domain == null ||
        config.thumbnailCode == null) {
      throw "初始数据加载异常，请检查baseinfo/common/qiniu/viewconfig";
    }
    if (isThumb) {
      return config.domain! + "/" + picName + config.thumbnailCode!;
    } else {
      return config.domain! + "/" + picName;
    }
  }

  ///保存权限
  static Future<bool?> savePermission(
    Map<String, dynamic>? systemConfigDto,
  ) async {
    if (null == systemConfigDto) {
      return false;
    }
    return await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.PERMISSION}",
      jsonEncode(systemConfigDto),
    );
  }

  ///获取权限
  static PermissionDto getPermission() {
    String? configStr = SpCustomUtil.getString(
      "${loginKey}_${_SpKey.PERMISSION}",
    );
    if (!StringUtil.isEmpty(configStr)) {
      return PermissionDto.fromMap(jsonDecode(configStr!));
    }
    return PermissionDto();
  }

  ///保存系统配置数据到本地
  static Future<bool?> saveAllSystemConfig(
    Map<String, dynamic> systemConfigDto,
  ) async {
    return await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.SYSTEM_CONFIG}",
      jsonEncode(systemConfigDto),
    );
  }

  ///获取系统配置数据
  static SystemConfigDto getSystemConfig() {
    String? configStr = SpCustomUtil.getString(
      "${loginKey}_${_SpKey.SYSTEM_CONFIG}",
    );
    if (!StringUtil.isEmpty(configStr)) {
      return SystemConfigDto.fromMap(jsonDecode(configStr!));
    }
    return SystemConfigDto();
  }

  ///获取设置信息
  static SettingDto getSetting() {
    //关联店铺id
    String? settingStr = SpCustomUtil.getString(
      "${loginKey}_${_SpKey.SYSTEM_SETTING}_${getStoreInfo()?.otypeId}",
    );
    return settingStr != null && settingStr.isNotEmpty
        ? SettingDto.fromJson(jsonDecode(settingStr))
        : SettingDto.defaultDto();
  }

  ///保存设置信息
  static Future<bool?> saveSetting(SettingDto setting) async {
    //关联店铺id
    return await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.SYSTEM_SETTING}_${getStoreInfo()?.otypeId}",
      jsonEncode(setting),
    );
  }

  ///保存开单列配置
  static Future<bool?> saveBillingColumnConfigSale(
    List<ColumnConfig> config,
  ) async {
    return await SpCustomUtil.putObjectList(
      "${loginKey}_${_SpKey.BILLING_COLUMN_CONFIG_SALE}",
      config,
    );
  }

  ///获取开单列配置
  static List<ColumnConfig> getBillingColumnConfigSale() {
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
    List<ColumnConfig> configList = getColumnBillingConfigListSale(
      isOpenStock: SpTool.getSetting().openStock,
      isTaxEnable:
          systemConfigDto.sysGlobalEnabledTax &&
          systemConfigDto.sysGlobalEnabledSaleTax,
    );

    return handleColumnConfig(
      configList,
      "${loginKey}_${_SpKey.BILLING_COLUMN_CONFIG_SALE}",
    );
  }

  static List<ColumnConfig> handleColumnConfig(
    List<ColumnConfig> configList,
    String spConfigName,
  ) {
    List<ColumnConfig>? bills = SpCustomUtil.getObjList(spConfigName, (v) {
      return ColumnConfig.fromMap(v);
    });
    if (null == bills || bills.isEmpty) {
      return configList;
    }

    for (ColumnConfig item in configList) {
      for (ColumnConfig configItem in bills) {
        if (item.type == configItem.type) {
          item.isShow = configItem.isShow;
          break;
        }
      }
    }
    return configList;
  }

  ///保存退货列配置
  static Future<bool?> saveBillingColumnConfigSaleBack(
    List<ColumnConfig> config,
  ) async {
    return await SpCustomUtil.putObjectList(
      "${loginKey}_${_SpKey.BILLING_COLUMN_CONFIG_SALE_BACK}",
      config,
    );
  }

  ///获取退货列配置
  static List<ColumnConfig> getBillingColumnConfigSaleBack() {
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();

    List<ColumnConfig> configList = columnBillingConfigListSaleBack(
      isTaxEnable:
          systemConfigDto.sysGlobalEnabledTax &&
          systemConfigDto.sysGlobalEnabledSaleTax,
    );
    return handleColumnConfig(
      configList,
      "${loginKey}_${_SpKey.BILLING_COLUMN_CONFIG_SALE_BACK}",
    );
  }

  ///保存单据列配置
  static Future<bool?> saveBillsColumnConfig(List<ColumnConfig> config) async {
    return await SpCustomUtil.putObjectList(
      "${loginKey}_${_SpKey.BILLS_COLUMN_CONFIG}",
      config,
    );
  }

  ///获取销售单据查询列配置
  static List<ColumnConfig> getBillsColumnConfig() {
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();

    List<ColumnConfig> configList = columnBillsConfigList(
      isTaxEnable:
          systemConfigDto.sysGlobalEnabledTax &&
          systemConfigDto.sysGlobalEnabledSaleTax,
    );
    return handleColumnConfig(
      configList,
      "${loginKey}_${_SpKey.BILLS_COLUMN_CONFIG}",
    );
  }

  ///保存打印纸张宽度配置
  static Future<bool?> savePrintPageWidth(int printPageWidth) async {
    return await SpCustomUtil.putInt(
      "${loginStoreKey}_${_SpKey.printPageWidth}",
      printPageWidth,
    );
  }

  ///获取打印纸张宽度配置
  static int getPrintPageWidth() {
    return SpCustomUtil.getInt(
          "${loginStoreKey}_${_SpKey.printPageWidth}",
          defValue: PrintWidth.w_58mm.index,
        ) ??
        PrintWidth.w_58mm.index;
  }

  ///保存打印配置
  static Future<bool?> savePrintFieldInfo(
    List<PrintConfigInfo> printFields,
  ) async {
    bool? isSuccess = await SpCustomUtil.putString(
      "${loginStoreKey}_${_SpKey.printConfig}",
      jsonEncode(printFields),
    );
    buildPrintImageConfig(printFields).then((printFields) async {
      await SpCustomUtil.putString(
        "${loginStoreKey}_${_SpKey.printConfig}",
        jsonEncode(printFields),
      );
    });
    return isSuccess;
  }

  ///保存时将url的图片加载成bytes并encode保存
  static Future<List<PrintConfigInfo>> buildPrintImageConfig(
    List<PrintConfigInfo> printFields,
  ) async {
    for (PrintConfigInfo item in printFields) {
      if (!item.selected) {
        continue;
      }
      printFields
          .where((item) => item.fieldKey != PrintFieldConfig.decorateTop)
          .toList();

      ///当前仅票头/票尾会处理图片
      if ((item.fieldKey != PrintFieldConfig.decorateTop &&
              item.fieldKey != PrintFieldConfig.decorateTail) ||
          StringUtil.isEmpty(item.contentConfig)) {
        continue;
      }
      PrintDecorateConfig printDecorateConfig = PrintDecorateConfig.fromMap(
        jsonDecode(item.contentConfig),
      );
      if (printDecorateConfig.printStyleMode ==
              PrintStyleModeEnum.image.index &&
          StringUtil.isNotEmpty(printDecorateConfig.imgUrl)) {
        item.imgBytesContent = await ImageTool.buildImage(
          printDecorateConfig.imgUrl,
        );
      }
    }
    return printFields;
  }

  ///获取全部单据打印配置
  static Map<String, List<PrintConfigInfo>> getPrintFieldInfo() {
    String? configStr = SpCustomUtil.getString(
      "${loginStoreKey}_${_SpKey.printConfig}",
    );

    if (StringUtil.isEmpty(configStr)) {
      return {};
    }
    List printConfigList = jsonDecode(configStr!) as List;
    List<PrintConfigInfo> result =
        printConfigList.map((o) => PrintConfigInfo.fromMap(o)).toList();
    return result.groupListsBy((e) => e.printType);
  }

  ///获取指定单据打印配置
  static List<PrintConfigInfo> getBillPrintFieldInfo(String billType) {
    return getPrintFieldInfo()[billType] ?? [];
  }

  static Future<bool?> savePrintField(
    String? billType,
    Map<String, dynamic> printFields,
  ) async {
    String? configStr = SpCustomUtil.getString(
      "${loginStoreKey}_${_SpKey.PRINT_CONFIG}",
    );
    Map<String?, dynamic> configPrint = {};

    ///兼容存储到职员级别的打印模版配置信息
    if (StringUtil.isEmpty(configStr)) {
      configStr = SpCustomUtil.getString("${loginKey}_${_SpKey.PRINT_CONFIG}");
    }
    if (!StringUtil.isEmpty(configStr)) {
      configPrint = jsonDecode(configStr!);
    }
    configPrint[billType] = printFields;
    return await SpCustomUtil.putString(
      "${loginStoreKey}_${_SpKey.PRINT_CONFIG}",
      jsonEncode(configPrint),
    );
  }

  @Deprecated("使用getPrintFieldInfo")
  static Map<String, dynamic> getPrintField(String? billType) {
    String? configStr = SpCustomUtil.getString(
      "${loginStoreKey}_${_SpKey.PRINT_CONFIG}",
    );

    ///兼容存储到职员级别的打印模版配置信息
    if (StringUtil.isEmpty(configStr)) {
      configStr = SpCustomUtil.getString("${loginKey}_${_SpKey.PRINT_CONFIG}");
    }
    if (!StringUtil.isEmpty(configStr)) {
      Map<String, dynamic> configPrint = jsonDecode(configStr!);
      return configPrint[billType] ?? {};
    }
    return {};
  }

  //保存当前登陆时间
  static Future<bool?> saveLoginTime(int loginTime) async {
    return await SpCustomUtil.putInt(
      "${loginKey}_${_SpKey.LOGIN_TIME}",
      loginTime,
    );
  }

  //获取登陆时间
  static int getLoginTime() {
    int? beforeTime = getShiftChangesInfo();
    if (DateUtil.getDateTimeByMs(
          SpCustomUtil.getInt("${loginKey}_${_SpKey.LOGIN_TIME}")!,
        ).difference(DateUtil.getDateTimeByMs(beforeTime!)).inHours >
        12) {
      //大于12小时的之前的时间无效
      return SpCustomUtil.getInt("${loginKey}_${_SpKey.LOGIN_TIME}")!;
    } else {
      return beforeTime == 0
          ? SpCustomUtil.getInt("${loginKey}_${_SpKey.LOGIN_TIME}")!
          : beforeTime;
    }
  }

  //保存是否自动登录
  static Future<bool?> saveAutoLogin(bool autoLogin) async {
    return await SpCustomUtil.putBool(
      "${loginKey}_${_SpKey.AUTO_LOGIN}",
      autoLogin,
    );
  }

  //获取是否自动登录
  static bool? getAutoLogin() {
    return SpCustomUtil.getBool(
      "${loginKey}_${_SpKey.AUTO_LOGIN}",
      defValue: false,
    );
  }

  //保存上单的vchcode 以便使用打印上单
  static Future<bool?> saveLastVchCode(String vchcode) async {
    return await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.LAST_VCHCODE}",
      vchcode,
    );
  }

  static String? getLastVchCode() {
    return SpCustomUtil.getString("${loginKey}_${_SpKey.LAST_VCHCODE}");
  }

  //保存业务员
  static Future<bool?> saveEtypeInfo(String etypeId, String etypeName) async {
    return await SpCustomUtil.putObject(
      "${loginKey}_${_SpKey.BILLS_ETYPEINFO}",
      {"etypeId": etypeId, "etypeName": etypeName},
    );
  }

  static String getEtypeId() {
    if (SpCustomUtil.getObject("${loginKey}_${_SpKey.BILLS_ETYPEINFO}") !=
        null) {
      Map? etypeInfo = SpCustomUtil.getObject(
        "${loginKey}_${_SpKey.BILLS_ETYPEINFO}",
      );
      if (StringUtil.isEmpty(etypeInfo!["etypeId"])) {
        return "";
      }
      return etypeInfo["etypeId"];
    }
    return "";
  }

  static String getEtypeName() {
    if (SpCustomUtil.getObject("${loginKey}_${_SpKey.BILLS_ETYPEINFO}") !=
        null) {
      Map? etypeInfo = SpCustomUtil.getObject(
        "${loginKey}_${_SpKey.BILLS_ETYPEINFO}",
      );
      if (StringUtil.isEmpty(etypeInfo!["etypeName"])) {
        return "";
      }
      return etypeInfo["etypeName"];
    }
    return "";
  }

  static bool getPrint() {
    Map? printInfo = SpCustomUtil.getObject(
      "${loginKey}_${_SpKey.BILLS_PRINT}",
    );
    if (printInfo == null || printInfo["print"] == null) {
      printInfo = SpCustomUtil.getObject(_SpKey.BILLS_PRINT);
    }
    if (printInfo == null || printInfo["print"] == null) {
      return true;
    }
    return printInfo["print"];
  }

  //保存打印
  static Future<bool?> savePrint(bool print) async {
    return await SpCustomUtil.putObject("${loginKey}_${_SpKey.BILLS_PRINT}", {
      "print": print,
    });
  }

  //保存是否打印交接班
  static Future<bool?> savePrintShiftChanges(bool print) async {
    return await SpCustomUtil.putObject(
      "${loginKey}_${_SpKey.SHIFTCHANGES_PRINT}",
      {"printShiftChanges": print},
    );
  }

  static bool getPrintShiftChanges() {
    Map? printInfo = SpCustomUtil.getObject(
      "${loginKey}_${_SpKey.SHIFTCHANGES_PRINT}",
    );
    if (printInfo == null || printInfo["printShiftChanges"] == null) {
      printInfo = SpCustomUtil.getObject(_SpKey.SHIFTCHANGES_PRINT);
    }
    if (printInfo == null || printInfo["printShiftChanges"] == null) {
      return false;
    }
    return printInfo["printShiftChanges"];
  }

  ///保存店铺信息
  static Future<bool?> saveStoreInfo(StoreInfo storeInfo) async {
    bool? isSuccess = await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.STORE_INFO}",
      jsonEncode(storeInfo),
    );
    handleStoreInfoImg(storeInfo).then((storeInfo) async {
      return await SpCustomUtil.putString(
        "${loginKey}_${_SpKey.STORE_INFO}",
        jsonEncode(storeInfo),
      );
    });
    return isSuccess;
  }

  static Future<StoreInfo?> handleStoreInfoImg(StoreInfo storeInfo) async {
    storeInfo.shopLogoContent = await ImageTool.buildImage(
      storeInfo.shopLogoUrl,
    );
    storeInfo.shopScanContent = await ImageTool.buildImage(
      storeInfo.shopScanUrl,
    );
    return storeInfo;
  }

  ///获取店铺信息
  static StoreInfo? getStoreInfo() {
    String? storeInfo = SpCustomUtil.getString(
      "${loginKey}_${_SpKey.STORE_INFO}",
    );
    if (StringUtil.isEmpty(storeInfo)) {
      return null;
    }
    return StoreInfo.fromMap(jsonDecode(storeInfo!));
  }

  ///获取积分配置
  static ScoreConfiguration? getScoreConfiguration() {
    String? scoreConfiguration = SpCustomUtil.getString(
      "${loginKey}_${_SpKey.SCORE_CONFIGURATION}",
    );
    if (StringUtil.isEmpty(scoreConfiguration)) {
      return null;
    }
    return ScoreConfiguration.fromJson(jsonDecode(scoreConfiguration!));
  }

  ///保存积分配置
  static Future<bool?> saveScoreConfiguration(
    ScoreConfiguration? scoreConfiguration,
  ) async {
    return await SpCustomUtil.putString(
      "${loginKey}_${_SpKey.SCORE_CONFIGURATION}",
      jsonEncode(scoreConfiguration),
    );
  }

  // ///获取现金账户
  // static StorePayway getCashierAtype() {
  //   return getStoreInfo().paywayList.firstWhere((element) => element.paywayType == 0,orElse: ()=>null);
  // }
  // ///获取银行账户
  // static StorePayway getBankAtype() {
  //   return getStoreInfo().paywayList.firstWhere((element) => element.paywayType == 1,orElse: ()=>null);
  // }
  // ///获取聚合支付账户(淘淘谷)
  // static StorePayway getScanAtype() {
  //   return getStoreInfo().paywayList.firstWhere((element) => element.paywayType == 2,orElse: ()=>null);
  // }
  // ///获取预存款账户(储值)
  // static StorePayway getStoredAtype() {
  //   StorePayway storePayway = getStoreInfo().paywayList.firstWhere(
  //       (element) => element.paywayType == 3,
  //       orElse: () => StorePayway());
  //   storePayway.atypeId = getStoreInfo().storedMoneyAtypeId;
  //   storePayway.atypeFullname = getStoreInfo().storedMoneyAtypeName;
  //   return storePayway;
  // }

  //保存之前的交接班人员门店等数据信息
  static Future<bool?> saveShiftChangesInfo(int loginTime) async {
    String key =
        LoginCenter.getLoginUser().profileId! +
        SpTool.getCashierInfo().id! +
        SpTool.getStoreInfo()!.otypeId!;
    return await SpCustomUtil.putInt(key, loginTime);
  }

  static int? getShiftChangesInfo() {
    String key =
        LoginCenter.getLoginUser().profileId! +
        SpTool.getCashierInfo().id! +
        SpTool.getStoreInfo()!.otypeId!;
    return SpCustomUtil.getInt(key, defValue: 0);
  }

  static Future<bool?> saveCashierInfo(StoreCashier? cashierInfo) async {
    String key = LoginCenter.getLoginUser().profileId! + _SpKey.CASHIER_INFO;
    return await SpCustomUtil.putString(key, jsonEncode(cashierInfo));
  }

  static StoreCashier getCashierInfo() {
    String key = LoginCenter.getLoginUser().profileId! + _SpKey.CASHIER_INFO;

    String? cashier = SpCustomUtil.getString(key);
    if (StringUtil.isEmpty(cashier)) {
      return StoreCashier();
    }
    return StoreCashier.fromMap(jsonDecode(cashier!));
  }

  //保存促销列表
  static Future<bool?> savePromotionList(
    List<BillPromotionInfoDto>? prmotionList,
  ) async {
    String key = LoginCenter.getLoginUser().profileId! + _SpKey.PROMOTION_LIST;
    return await SpCustomUtil.putString(key, jsonEncode(prmotionList));
  }

  //清除促销列表
  static Future<bool>? clearPromotionList() {
    String key = LoginCenter.getLoginUser().profileId! + _SpKey.PROMOTION_LIST;
    return SpCustomUtil.remove(key);
  }

  //获取促销列表
  static List<BillPromotionInfoDto> getPromotionList() {
    String key = LoginCenter.getLoginUser().profileId! + _SpKey.PROMOTION_LIST;

    String? promotionStr = SpCustomUtil.getString(key);
    if (StringUtil.isEmpty(promotionStr)) {
      return [];
    }
    List promotionList = jsonDecode(promotionStr!) as List;
    List<BillPromotionInfoDto> result = [
      ...promotionList.map((e) => BillPromotionInfoDto.fromMap(e)),
    ];

    ///暂停和未开始的促销需要判断当前日期范围并且时间范围 是否已经可以执行
    result =
        result.where((element) {
          DateTime startDate =
              DateUtil.getDateTime(
                "${element.startDate.substring(0, 10)} 00:00:00",
              )!;
          DateTime endDate =
              DateUtil.getDateTime(
                "${element.endDate.substring(0, 10)} 23:59:59",
              )!;
          DateTime now =
              DateUtil.getDateTime(
                DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
              )!;

          String nowString = DateUtil.getNowDateStr().substring(0, 10);
          DateTime startDateTime =
              DateUtil.getDateTime("$nowString ${element.startTime!}")!;
          DateTime endDateTime =
              DateUtil.getDateTime("$nowString ${element.endTime!}")!;

          if (now.isAfter(startDate) && now.isBefore(endDate)) {
            if (now.isAfter(startDateTime) && now.isBefore(endDateTime)) {
              return true;
            } else {
              return false;
            }
          } else {
            return false;
          }
        }).toList();
    return result;
  }

  ///调货管理配置
  static TenderManageConfig getTenderManageConfig() {
    String? json = SpCustomUtil.getString(
      "${loginKey}_${_SpKey.tender_manage_config}",
    );
    if (json != null && json.isNotEmpty) {
      return TenderManageConfig.fromJson(jsonDecode(json));
    }
    return TenderManageConfig.defaultConfig();
  }

  ///调货管理配置
  static void saveTenderManageConfig(TenderManageConfig config) {
    SpCustomUtil.putString(
      "${loginKey}_${_SpKey.tender_manage_config}",
      jsonEncode(config),
    );
  }

  static void cleanTenderManageConfig() {
    SpCustomUtil.remove("${loginKey}_${_SpKey.tender_manage_config}");
  }

  ///调拨订单管理配置
  static TransferAndAllocateConfig getTransferConfig() {
    String? json = SpCustomUtil.getString(
      "${loginKey}_${_SpKey.transfer_and_allocate_config}",
    );
    if (json != null && json.isNotEmpty) {
      return TransferAndAllocateConfig.fromJson(jsonDecode(json));
    }
    return TransferAndAllocateConfig.defaultConfig();
  }

  ///调拨订单管理配置
  static void saveTransferConfig(TransferAndAllocateConfig config) {
    SpCustomUtil.putString(
      "${loginKey}_${_SpKey.transfer_and_allocate_config}",
      jsonEncode(config),
    );
  }

  static void cleanTransferConfig() {
    SpCustomUtil.remove("${loginKey}_${_SpKey.transfer_and_allocate_config}");
  }

  ///保存数据库配置
  static void saveDatabaseConfig(DatabaseConfig config) {
    String key = _SpKey.database_config + LoginCenter.getLoginUser().profileId!;
    SpCustomUtil.putString(key, jsonEncode(config));
  }

  ///读取数据库配置
  static DatabaseConfig getDatabaseConfig() {
    String key = _SpKey.database_config + LoginCenter.getLoginUser().profileId!;
    String? json = SpCustomUtil.getString(key);
    if (json?.isNotEmpty == true) {
      return DatabaseConfig.fromMap(jsonDecode(json!));
    }
    return DatabaseConfig();
  }

  // 处理要货申请列配置的方法
  static List<StoreRequisitionColumnConfig> handleStoreRequisitionColumnConfig(
    List<StoreRequisitionColumnConfig> configList,
    String spConfigName,
  ) {
    List<StoreRequisitionColumnConfig>? saved = SpCustomUtil.getObjList(
      spConfigName,
      (v) {
        return StoreRequisitionColumnConfig.fromMap(v);
      },
    );
    if (null == saved || saved.isEmpty) {
      return configList;
    }

    for (StoreRequisitionColumnConfig item in configList) {
      for (StoreRequisitionColumnConfig configItem in saved) {
        if (item.type == configItem.type) {
          item.isShow = configItem.isShow;
          break;
        }
      }
    }
    return configList;
  }

  // 处理和调拨管理列配置的方法
  static List<TenderColumnConfig> handleTenderColumnConfig(
    List<TenderColumnConfig> configList,
    String spConfigName,
  ) {
    List<TenderColumnConfig>? saved = SpCustomUtil.getObjList(spConfigName, (
      v,
    ) {
      return TenderColumnConfig.fromMap(v);
    });
    if (null == saved || saved.isEmpty) {
      return configList;
    }

    for (TenderColumnConfig item in configList) {
      for (TenderColumnConfig configItem in saved) {
        if (item.type == configItem.type) {
          item.isShow = configItem.isShow;
          break;
        }
      }
    }
    return configList;
  }

  ///保存要货申请列配置
  static Future<bool?> saveTenderRequestColumnConfig(
    List<TenderColumnConfig> config,
  ) async {
    return await SpCustomUtil.putObjectList(
      "${loginKey}_${_SpKey.TENDER_REQUEST_COLUMN_CONFIG}",
      config,
    );
  }

  ///保存要货申请列配置
  static Future<bool?> saveStoreRequisitionColumnConfig(
    List<StoreRequisitionColumnConfig> config,
  ) async {
    return await SpCustomUtil.putObjectList(
      "${loginKey}_${_SpKey.TENDER_REQUEST_COLUMN_CONFIG}",
      config,
    );
  }

  ///获取要货申请列配置
  static List<StoreRequisitionColumnConfig> getStoreRequisitionColumnConfig() {
    List<StoreRequisitionColumnConfig> configList =
        storeRequisitionConfigList();
    return handleStoreRequisitionColumnConfig(
      configList,
      "${loginKey}_${_SpKey.TENDER_REQUEST_COLUMN_CONFIG}",
    );
  }

  ///保存调拨管理列配置
  static Future<bool?> saveTenderManageColumnConfig(
    List<TenderColumnConfig> config,
  ) async {
    return await SpCustomUtil.putObjectList(
      "${loginKey}_${_SpKey.TENDER_MANAGE_COLUMN_CONFIG}",
      config,
    );
  }

  ///获取调拨管理列配置
  static List<TenderColumnConfig> getTenderManageColumnConfig() {
    List<TenderColumnConfig> configList = tenderManageConfigList();
    return handleTenderColumnConfig(
      configList,
      "${loginKey}_${_SpKey.TENDER_MANAGE_COLUMN_CONFIG}",
    );
  }

  ///获取库存调拨专用列配置（基于调拨管理配置，但添加库存列）
  static List<TenderColumnConfig> getStockBillTenderManageColumnConfig() {
    // 先尝试从保存的设置中获取
    List<TenderColumnConfig>? saved = SpCustomUtil.getObjList(
      "${loginKey}_${_SpKey.STOCK_BILL_TENDER_MANAGE_COLUMN_CONFIG}",
      (v) => TenderColumnConfig.fromMap(v),
    );

    if (saved != null && saved.isNotEmpty) {
      // 如果有保存的设置，使用它
      return saved;
    }

    // 否则基于调拨管理配置创建一个新的
    List<TenderColumnConfig> configList = List.from(
      getTenderManageColumnConfig(),
    );

    // 添加库存列
    bool hasStockColumn = configList.any(
      (config) => config.type == TenderColumnType.stock,
    );
    if (!hasStockColumn) {
      configList.add(
        TenderColumnConfig(
          title: "库存",
          type: TenderColumnType.stock,
          isShow: true,
          isRequired: true,
        ),
      );
    } else {
      // 如果存在库存列，确保它设置为显示
      for (var config in configList) {
        if (config.type == TenderColumnType.stock) {
          config.isShow = true;
          break;
        }
      }
    }

    return configList;
  }

  ///获取要货申请列配置
  static List<TenderColumnConfig> getTenderRequestColumnConfig() {
    // 为了保持向后兼容，保留此方法
    // 但需要适配原来使用tenderRequestConfigList()的地方
    // 创建一个新的 TenderColumnConfig 列表，与 StoreRequisitionColumnConfig 对应
    List<TenderColumnConfig> configList = [];
    List<StoreRequisitionColumnConfig> storeReqConfigList =
        getStoreRequisitionColumnConfig();

    // 根据StoreRequisitionColumnConfig生成对应的TenderColumnConfig
    for (var storeConfig in storeReqConfigList) {
      if (storeConfig.type == null) continue;

      // 根据类型映射
      TenderColumnType? tenderType;
      switch (storeConfig.type) {
        case StoreRequisitionColumnType.all:
          tenderType = TenderColumnType.all;
          break;
        case StoreRequisitionColumnType.image:
          tenderType = TenderColumnType.image;
          break;
        case StoreRequisitionColumnType.pName:
          tenderType = TenderColumnType.pName;
          break;
        case StoreRequisitionColumnType.userCode:
          tenderType = TenderColumnType.userCode;
          break;
        case StoreRequisitionColumnType.barCode:
          tenderType = TenderColumnType.barCode;
          break;
        case StoreRequisitionColumnType.attributeFormat:
          tenderType = TenderColumnType.attributeFormat;
          break;
        case StoreRequisitionColumnType.attributeCombo:
          tenderType = TenderColumnType.attributeCombo;
          break;
        case StoreRequisitionColumnType.spec:
          tenderType = TenderColumnType.spec;
          break;
        case StoreRequisitionColumnType.model:
          tenderType = TenderColumnType.model;
          break;
        case StoreRequisitionColumnType.number:
          tenderType = TenderColumnType.number;
          break;
        case StoreRequisitionColumnType.unit:
          tenderType = TenderColumnType.unit;
          break;
        case StoreRequisitionColumnType.stock:
          tenderType = TenderColumnType.stock;
          break;
        case StoreRequisitionColumnType.setting:
          tenderType = TenderColumnType.setting;
          break;
        default:
          continue;
      }

      configList.add(
        TenderColumnConfig(
          title: storeConfig.title,
          type: tenderType,
          isShow: storeConfig.isShow,
          isRequired: storeConfig.isRequired,
        ),
      );
    }

    return configList;
  }

  ///保存库存调拨列配置
  static Future<bool> saveStockBillTenderManageColumnConfig(
    List<TenderColumnConfig> configList,
  ) async {
    return await SpCustomUtil.putObjectList(
          "${loginKey}_${_SpKey.STOCK_BILL_TENDER_MANAGE_COLUMN_CONFIG}",
          configList,
        ) ??
        false;
  }
}

class _SpKey {
  static const String SHOP_CONFIG = "shopConfig";
  static const String module = "module"; // 资产模块开关

  static const String QINIU_VIEWCONFIG = "qiniu_viewconfig"; //七牛viewconfig配置
  static const String SYSTEM_CONFIG = "system_config"; //系统配置
  static const String PERMISSION = "permission"; //权限
  static const String PRINT_CONFIG = "print_config"; //打印配置
  static const String printConfig = "printConfig"; //打印配置
  static const String printPageWidth = "print_Page_width"; //打印纸张宽度配置

  ///系统设置的sp的key，注意后面会跟上店铺id
  static const String SYSTEM_SETTING = "system_setting";
  static const String LAST_VCHCODE = "last_vchcode"; //上单vchcode
  static const String BILLING_COLUMN_CONFIG_SALE =
      "billing_column_config_sale"; //开单页面列配置
  static const String BILLING_COLUMN_CONFIG_SALE_BACK =
      "billing_column_config_sale_back"; //开单页面列配置
  static const String BILLS_COLUMN_CONFIG = "bills_column_config"; //单据页面列配置
  static const String BILLS_ETYPEINFO = "BILLS_ETYPEINFO"; //单据页面列配置
  static const String BILLS_PRINT = "BILLS_PRINT"; //结算页面打印配置
  static const String STORE_INFO = "STORE_INFO"; //选择门店信息
  static const String CASHIER_INFO = "CASHIER_INFO"; //选择的收银机信息

  ///积分配置
  static const String SCORE_CONFIGURATION = "SCORE_CONFIGRATION";

  static const String LOGIN_TIME = "LOGIN_TIME"; //保存登陆时间
  static const String SHIFTCHANGES_PRINT = "SHIFTCHANGES_PRINT"; //是否打印交接班
  static const String PROMOTION_LIST = "PROMOTION_LIST"; //是否打印交接班

  static const String AUTO_LOGIN = "autoLoign"; //是否打印交接班

  ///调货管理的配置
  static const String tender_manage_config = "tender_manage_config";

  ///调拨订单管理配置
  static const String transfer_and_allocate_config =
      "transfer_and_allocate_config";

  ///数据库配置
  static const database_config = "database_config";

  static const String TENDER_REQUEST_COLUMN_CONFIG =
      "TENDER_REQUEST_COLUMN_CONFIG";
  static const String TENDER_MANAGE_COLUMN_CONFIG =
      "TENDER_MANAGE_COLUMN_CONFIG";

  ///库存调拨列配置
  static const String STOCK_BILL_TENDER_MANAGE_COLUMN_CONFIG =
      "STOCK_BILL_TENDER_MANAGE_COLUMN_CONFIG";
}
