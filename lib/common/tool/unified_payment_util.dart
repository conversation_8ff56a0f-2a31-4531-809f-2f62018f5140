/// 统一聚合支付工具类

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:haloui/haloui.dart';
import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/entity/pay_result_dto.dart';
import '../../enum/payment_enum.dart';
import '../../bill/settlement/entity/atype_info_bean.dart';
import '../../common/tool/payment.dart';
import '../../common/tool/UUID.dart';
import '../../bill/model/bill_model.dart';
import '../../vip/entity/vip_level_payment_request.dart';
import '../../vip/entity/vip_level_payment_confirm_request.dart';
import '../../vip/entity/vip_recharge_payment_confirm_request.dart';
import '../../vip/entity/vip_recharge_payment_request.dart' as recharge_payment;
import '../../vip/entity/vip_create_payment_request.dart';
import '../../vip/entity/pay_result_info.dart';
import '../../vip/entity/add_or_edit_vip_request.dart';
import '../../vip/model/vip_model.dart';
import '../../vip/model/vip_recharge_model.dart';
import '../../widgets/scanner_dialog.dart';
import '../../common/net/http_util.dart';
import '../../common/net/request_method.dart';
import 'package:halo_utils/http/base_model.dart';

/// 支付类型枚举
enum PaymentType {
  /// 销售单据
  saleBill,

  /// 会员升级
  vipUpgrade,

  /// 会员充值
  vipRecharge,

  /// 会员开通
  vipCreate,
}

/// 统一聚合支付工具类
class UnifiedPaymentUtil {
  /// 检查是否包含聚合支付
  static bool checkAggregatePayment(List<AtypeInfoBean> paymentList) {
    return paymentList.any(
      (element) => element.enable && element.storePayway.paywayType == 2,
    );
  }

  /// 检查聚合支付金额是否为0
  /// 如果聚合支付金额为0，则不应该走聚合支付流程
  static bool checkAggregatePaymentAmountIsZero(List<AtypeInfoBean> paymentList) {
    AtypeInfoBean? aggregatePayment = paymentList.firstWhereOrNull(
      (element) => element.enable && element.storePayway.paywayType == 2,
    );

    if (aggregatePayment == null) {
      return false;
    }

    // 检查聚合支付金额是否为0或空
    return aggregatePayment.total == "0" ||
           aggregatePayment.total.isEmpty ||
           double.tryParse(aggregatePayment.total) == 0.0;
  }

  /// 构建聚合支付信息
  static Future<Map?> buildAggregatePayInfo(
    BuildContext context,
    List<AtypeInfoBean> paymentList,
    String vchcode, {
    String? scanOutNo,
  }) async {
    // 查找聚合支付方式
    AtypeInfoBean? aggregatePayment = paymentList.firstWhere(
      (element) => element.enable && element.storePayway.paywayType == 2,
      orElse: () => throw Exception("未找到聚合支付方式"),
    );

    // 构建聚合支付信息
    Map<String, dynamic> payInfo = {
      'atypeId': aggregatePayment.storePayway.atypeId,
      'authCode': scanOutNo ?? '',
      'originalAmount': double.parse(aggregatePayment.total),
      'tradeAmount': double.parse(aggregatePayment.total),
      'ignoreAmount': 0,
      'vchcode': vchcode,
      'paywayId': aggregatePayment.storePayway.paywayId,
      'outNo': aggregatePayment.payOutNo,
      'billTotal': _calculateBillTotal(paymentList),
    };

    return payInfo;
  }

  /// 计算单据总金额
  static double _calculateBillTotal(List<AtypeInfoBean> paymentList) {
    return paymentList
        .where((element) => element.enable)
        .fold(0.0, (sum, element) => sum + double.parse(element.total));
  }

  /// 统一聚合支付处理入口
  static Future<void> processAggregatePayment(
    BuildContext context, {
    required PaymentType paymentType,
    required List<AtypeInfoBean> paymentList,
    required String vchcode,
    String? scanOutNo,
    // 业务数据参数
    GoodsBillDto? goodsBill,
    VipUpgradeInfo? upgradeInfo,
    VipRechargeInfo? rechargeInfo,
    AddOrEditVipDTO? vipCreateInfo,
    // 回调函数
    Function(PayResult?)? onResult,
    Function(PayResultType)? onSuccess,
    Function(int)? onAction,
  }) async {
    try {
      // 检查是否包含聚合支付
      bool hasAggregatePayment = checkAggregatePayment(paymentList);

      // 检查聚合支付金额是否为0
      bool aggregateAmountIsZero = checkAggregatePaymentAmountIsZero(paymentList);

      PayResult? payResult;
      Map? payInfo; // 保存聚合支付信息，用于后续赋值给 payResult

      // 保存原始请求数据，用于手动确认收款
      VipLevelPaymentRequest? originalVipLevelRequest;
      VipRechargePaymentRequest? originalVipRechargeRequest;
      VipCreatePaymentRequest? originalVipCreateRequest;

      if (!hasAggregatePayment || aggregateAmountIsZero) {
        // 没有聚合支付方式或聚合支付金额为0，直接调用已有接口
        payResult = await _executeDirectPayment(
          context,
          paymentType,
          paymentList: paymentList,
          goodsBill: goodsBill,
          upgradeInfo: upgradeInfo,
          rechargeInfo: rechargeInfo,
          vipCreateInfo: vipCreateInfo,
        );
      } else {
        // 有聚合支付方式，执行聚合支付流程
        // 如果没有提供scanOutNo，需要弹出扫码弹窗
        String? authCode = scanOutNo;
        if (authCode == null || authCode.isEmpty) {
          // 弹出扫码弹窗获取authCode
          authCode = await _showScanDialog(context);
          if (authCode == null || authCode.isEmpty) {
            return;
          }
        }

        // 构建聚合支付信息
        payInfo = await buildAggregatePayInfo(
          context,
          paymentList,
          vchcode,
          scanOutNo: authCode,
        );

        if (payInfo == null) {
          throw Exception("构建聚合支付信息失败");
        }

        // 根据支付类型保存原始请求数据
        AggregatePaymentRequest aggregatePayInfo =
            AggregatePaymentRequest.fromJson(
              Map<String, dynamic>.from(payInfo),
            );

        switch (paymentType) {
          case PaymentType.vipUpgrade:
            if (upgradeInfo != null) {
              // 确保 VipUpgradeInfo 有正确的 payment 信息
              if (upgradeInfo.payment == null || upgradeInfo.payment!.isEmpty) {
                upgradeInfo.payment =
                    paymentList
                        .map(
                          (e) => {
                            'atypeId': e.storePayway.atypeId,
                            'paywayId': e.storePayway.paywayId,
                            'currencyAtypeTotal': e.total,
                            'paywayType': e.storePayway.paywayType,
                            'paywayFullname': e.storePayway.paywayName,
                            'outNo': e.payOutNo,
                          },
                        )
                        .toList();
              }
              originalVipLevelRequest = VipLevelPaymentRequest(
                upgradeInfo: upgradeInfo,
                payInfo: aggregatePayInfo,
              );
            }
            break;
          case PaymentType.vipRecharge:
            if (rechargeInfo != null) {
              originalVipRechargeRequest = VipRechargePaymentRequest(
                rechargeInfo: rechargeInfo,
                payInfo: aggregatePayInfo,
              );
            }
            break;
          case PaymentType.vipCreate:
            if (vipCreateInfo != null) {
              originalVipCreateRequest = VipCreatePaymentRequest(
                vipInfo: vipCreateInfo,
                payInfo: aggregatePayInfo,
              );
            }
            break;
          case PaymentType.saleBill:
            break;
        }
        //执行聚合支付
        payResult = await _executeAggregatePayment(
          context,
          paymentType,
          payInfo,
          goodsBill: goodsBill,
          upgradeInfo: upgradeInfo,
          rechargeInfo: rechargeInfo,
          vipCreateInfo: vipCreateInfo,
        );
      }

      // 通知支付结果
      if (onResult != null) {
        onResult(payResult);
      }

      if (payResult == null) {
        throw Exception("支付执行失败");
      }

      // 如果是聚合支付且金额不为0，需要处理支付结果
      if (hasAggregatePayment && !aggregateAmountIsZero) {
        // 将最初构建的支付信息赋值给 payResult 的 payInfo
        if (payInfo != null) {
          payResult.payInfo = payInfo;
        }

        // 处理支付结果，集成支付确认逻辑
        PaymentUtil.processingPayResult(
          context,
          payResult,
          result: onSuccess ?? (type) {},
          onActionListener: (int index) {
            if (index == 0) {
              // 手动确认收款 - 聚合支付才需要手动确认
              _handleManualConfirmPaymentForAggregate(
                context,
                paymentType,
                payResult,
                onSuccess ?? (type) {},
                onAction ?? (action) {},
                originalVipLevelRequest: originalVipLevelRequest,
                originalVipRechargeRequest: originalVipRechargeRequest,
                originalVipCreateRequest: originalVipCreateRequest,
                onResult: onResult,
              );
            } else {
              if (onAction != null) {
                onAction(index);
              }
            }
          },
        );
      } else {
        // 直接支付成功
        if (onSuccess != null) {
          onSuccess(PayResultType.SUCCEEDED);
        }
      }

      // 通知支付结果
      if (onResult != null) {
        onResult(payResult);
      }
    } catch (error) {
      if (context.mounted) {
        HaloToast.showError(context, msg: error.toString());
      }
    }
  }

  /// 执行聚合支付确认
  static Future<void> confirmPaymentByPayResult(
    BuildContext context,
    PaymentType paymentType,
    PayResult payResult, {
    VipLevelPaymentRequest? vipLevelRequest,
    VipRechargePaymentRequest? vipRechargeRequest,
    VipCreatePaymentRequest? vipCreateRequest,
    int confirmType = 2,
  }) async {
    switch (paymentType) {
      case PaymentType.vipUpgrade:
        if (vipLevelRequest != null) {
          VipLevelPaymentConfirmRequest confirmRequest =
              VipLevelPaymentConfirmRequest.fromPayResult(
                vipLevelRequest,
                payResult,
                confirmType: confirmType,
              );
          await VipModel.confirmVipLevelPayment(context, confirmRequest);
        }
        break;
      case PaymentType.vipRecharge:
        if (vipRechargeRequest != null) {
          // 转换为VipRechargePaymentConfirmRequest期望的类型
          recharge_payment.VipRechargePaymentRequest convertedRequest =
              recharge_payment.VipRechargePaymentRequest(
                rechargeInfo: vipRechargeRequest.rechargeInfo,
                payInfo: vipRechargeRequest.payInfo,
              );

          VipRechargePaymentConfirmRequest confirmRequest =
              VipRechargePaymentConfirmRequest.fromPayResult(
                convertedRequest,
                payResult,
                confirmType: confirmType,
              );
          await VipRechargeModel.confirmRechargePayment(
            context,
            confirmRequest,
          );
        }
        break;
      case PaymentType.vipCreate:
        if (vipCreateRequest != null && vipCreateRequest.vipInfo != null) {
          // 会员开通支付确认，调用原有的addOrEditVip接口
          // 确保使用完整的原始会员信息
          AddOrEditVipDTO vipInfo = vipCreateRequest.vipInfo!;

          // 确保vchcode是最新的（从支付结果中获取）
          vipInfo.vchcode = payResult.resultDTO?.vchcode ?? vipInfo.vchcode;

          // 手动确认时直接调用addOrEditVip完成会员创建
          String? vipId = await VipModel.addOrEditVip(context, vipInfo);

          // 将返回的vipId保存到payResult中，供后续使用
          if (vipId != null) {
            // 如果payResult是PayResultInfo类型，直接设置vipId
            if (payResult is PayResultInfo) {
              payResult.vipId = vipId;
            } else {
              // 否则保存到data中
              payResult.data ??= {};
              payResult.data!['manualConfirmVipId'] = vipId;
            }
          }
        } else {
          throw Exception("会员开通支付确认失败：缺少完整的会员信息");
        }
        break;
      case PaymentType.saleBill:
        break;
    }
  }

  /// 执行销售单据聚合支付
  static Future<PayResult?> executeAggregatePaymentForSale(
    BuildContext context,
    GoodsBillDto goodsBill,
    Map payInfo,
  ) async {
    // 调用BillModel中的接口方法
    return await BillModel.saveGoodsBill(
      context,
      goodsBill,
      payInfo: payInfo,
      paySaveBillEnum: PaySaveBillEnum.SCANPAY,
    );
  }

  /// 执行会员升级聚合支付
  static Future<PayResult?> executeAggregatePaymentForVipUpgrade(
    BuildContext context,
    VipUpgradeInfo upgradeInfo,
    AggregatePaymentRequest payInfo,
  ) async {
    // 调用VipModel中的接口方法
    return await VipModel.vipLevelPayOrderAndSaveBill(
      context,
      upgradeInfo,
      payInfo,
    );
  }

  /// 执行会员充值聚合支付
  static Future<PayResult?> executeAggregatePaymentForVipRecharge(
    BuildContext context,
    VipRechargeInfo rechargeInfo,
    AggregatePaymentRequest payInfo,
  ) async {
    // 由于 VipRechargeInfo 没有 payment 字段，我们需要创建一个包含 payment 的版本
    // 从 AggregatePaymentRequest 中提取支付信息
    List<Map<String, dynamic>> paymentData = [];

    // 如果 payInfo 中有支付信息，提取出来
    if (payInfo.atypeId != null && payInfo.atypeId!.isNotEmpty) {
      paymentData.add({
        'atypeId': payInfo.atypeId,
        'paywayId': payInfo.paywayId,
        'currencyAtypeTotal': payInfo.tradeAmount,
        'paywayType': 1, // 默认类型
        'paywayFullname': '聚合支付',
        'outNo': payInfo.outNo,
      });
    }

    // 创建包含 payment 字段的充值信息
    Map<String, dynamic> rechargeInfoWithPayment = rechargeInfo.toJson();
    rechargeInfoWithPayment['payment'] = paymentData;

    // 创建一个自定义的请求数据，包含 payment 字段
    Map<String, dynamic> requestData = {
      'rechargeInfo': rechargeInfoWithPayment,
      'payInfo': payInfo.toJson(),
    };

    // 直接调用接口
    ResponseModel response = await HttpUtil.request(
      context,
      method: RequestMethod.rechargePayOrderAndSaveBill,
      data: requestData,
      autoHandleError: false,
    );

    return PayResult.fromJson(response.data);
  }

  /// 执行会员开通聚合支付
  static Future<PayResult?> executeAggregatePaymentForVipCreate(
    BuildContext context,
    AddOrEditVipDTO vipCreateInfo,
    AggregatePaymentRequest payInfo,
  ) async {
    // 调用VipModel中的接口方法
    return await VipModel.vipCreatePayOrderAndSaveBill(
      context,
      vipCreateInfo,
      payInfo,
    );
  }

  /// 生成单据编号
  static String generateVchcode() {
    return UUID.getVchcode();
  }

  /// 显示扫码弹窗
  static Future<String?> _showScanDialog(BuildContext context) async {
    return await showScannerDialog(context);
  }

  /// 执行直接支付（非聚合支付）
  static Future<PayResult?> _executeDirectPayment(
    BuildContext context,
    PaymentType paymentType, {
    required List<AtypeInfoBean> paymentList,
    GoodsBillDto? goodsBill,
    VipUpgradeInfo? upgradeInfo,
    VipRechargeInfo? rechargeInfo,
    AddOrEditVipDTO? vipCreateInfo,
  }) async {
    switch (paymentType) {
      case PaymentType.saleBill:
        if (goodsBill == null) {
          throw Exception("销售单据支付缺少goodsBill参数");
        }
        // 直接调用普通保存单据接口
        return await BillModel.saveGoodsBill(
          context,
          goodsBill,
          paySaveBillEnum: PaySaveBillEnum.COMMON,
        );

      case PaymentType.vipUpgrade:
        if (upgradeInfo == null) {
          throw Exception("会员升级支付缺少upgradeInfo参数");
        }
        // 直接调用会员升级接口
        await VipModel.renewOrUpgradeSVIP(
          context,
          vchcode: upgradeInfo.vchcode ?? '',
          vipId: upgradeInfo.vipId ?? '',
          levelId: upgradeInfo.levelId ?? '',
          levelRuleId: upgradeInfo.levelRuleId ?? '',
          payment: paymentList, // 使用真实的支付信息
          btypeId: upgradeInfo.btypeId ?? '',
          etypeId: upgradeInfo.etypeId,
          otypeId: upgradeInfo.otypeId,
          memo: upgradeInfo.memo,
        );
        return _createSuccessPayResult(vipId: upgradeInfo.vipId);

      case PaymentType.vipRecharge:
        if (rechargeInfo == null) {
          throw Exception("会员充值支付缺少rechargeInfo参数");
        }
        // 直接调用会员充值接口
        await VipRechargeModel.recharge(
          context,
          vchcode: rechargeInfo.vchcode ?? '',
          vipId: rechargeInfo.vipId ?? '',
          rechargeMoney: rechargeInfo.rechargeMoney ?? 0,
          payment: paymentList, // 使用真实的支付信息
          otypeId: rechargeInfo.otypeId ?? '',
          memo: rechargeInfo.memo ?? '',
          summary: rechargeInfo.summary ?? '',
          ktypeId: rechargeInfo.ktypeId ?? '',
          btypeId: rechargeInfo.btypeId ?? '',
          createEtypeId: rechargeInfo.createEtypeId ?? '',
          vipLevelId: rechargeInfo.vipLevelId ?? '',
          rechargeId: rechargeInfo.rechargeId ?? '',
          giveMoney: rechargeInfo.giveMoney,
          score: rechargeInfo.score?.toInt(),
          cardId: rechargeInfo.cardId,
          cardQty: rechargeInfo.cardQty?.toInt(),
          goodsBill: goodsBill, // 添加缺少的 goodsBill 参数
          etypeId: rechargeInfo.etypeId,
          cashierId: rechargeInfo.cashierId,
        );
        return _createSuccessPayResult();

      case PaymentType.vipCreate:
        if (vipCreateInfo == null) {
          throw Exception("会员开通支付缺少vipCreateInfo参数");
        }
        // 直接调用会员创建接口
        String? vipId = await VipModel.addOrEditVip(context, vipCreateInfo);
        return _createSuccessPayResult(vipId: vipId);
    }
  }

  /// 执行聚合支付
  static Future<PayResult?> _executeAggregatePayment(
    BuildContext context,
    PaymentType paymentType,
    Map payInfo, {
    GoodsBillDto? goodsBill,
    VipUpgradeInfo? upgradeInfo,
    VipRechargeInfo? rechargeInfo,
    AddOrEditVipDTO? vipCreateInfo,
  }) async {
    switch (paymentType) {
      case PaymentType.saleBill:
        if (goodsBill == null) {
          throw Exception("销售单据支付缺少goodsBill参数");
        }
        return await executeAggregatePaymentForSale(
          context,
          goodsBill,
          payInfo,
        );

      case PaymentType.vipUpgrade:
        if (upgradeInfo == null) {
          throw Exception("会员升级支付缺少upgradeInfo参数");
        }
        AggregatePaymentRequest aggregatePayInfo =
            AggregatePaymentRequest.fromJson(
              Map<String, dynamic>.from(payInfo),
            );
        return await executeAggregatePaymentForVipUpgrade(
          context,
          upgradeInfo,
          aggregatePayInfo,
        );

      case PaymentType.vipRecharge:
        if (rechargeInfo == null) {
          throw Exception("会员充值支付缺少rechargeInfo参数");
        }
        AggregatePaymentRequest aggregatePayInfo =
            AggregatePaymentRequest.fromJson(
              Map<String, dynamic>.from(payInfo),
            );
        return await executeAggregatePaymentForVipRecharge(
          context,
          rechargeInfo,
          aggregatePayInfo,
        );

      case PaymentType.vipCreate:
        if (vipCreateInfo == null) {
          throw Exception("会员开通支付缺少vipCreateInfo参数");
        }
        AggregatePaymentRequest aggregatePayInfo =
            AggregatePaymentRequest.fromJson(
              Map<String, dynamic>.from(payInfo),
            );
        return await executeAggregatePaymentForVipCreate(
          context,
          vipCreateInfo,
          aggregatePayInfo,
        );
    }
  }

  /// 创建成功的支付结果
  static PayResult _createSuccessPayResult({String? vipId}) {
    PayResult result = PayResult();
    result.successed = true;
    result.status = "1"; // 成功状态
    if (vipId != null) {
      result.data = {'vipId': vipId};
    }
    return result;
  }

  /// 处理聚合支付的手动确认收款
  static Future<void> _handleManualConfirmPaymentForAggregate(
    BuildContext context,
    PaymentType paymentType,
    PayResult? payResult,
    Function(PayResultType) onSuccess,
    Function(int) onAction, {
    VipLevelPaymentRequest? originalVipLevelRequest,
    VipRechargePaymentRequest? originalVipRechargeRequest,
    VipCreatePaymentRequest? originalVipCreateRequest,
    Function(PayResult?)? onResult,
  }) async {
    // 聚合支付的手动确认收款逻辑
    if (payResult == null || !context.mounted) {
      onAction(1); // 取消
      return;
    }

    try {
      // 执行支付确认
      await confirmPaymentByPayResult(
        context,
        paymentType,
        payResult,
        vipLevelRequest: originalVipLevelRequest,
        vipRechargeRequest: originalVipRechargeRequest,
        vipCreateRequest: originalVipCreateRequest,
      );

      // 手动确认成功后，通知支付结果更新
      if (onResult != null) {
        onResult(payResult);
      }

      // 手动确认成功
      if (context.mounted) {
        onSuccess(PayResultType.SUCCEEDED);
      }
    } catch (error) {
      // 手动确认失败
      if (context.mounted) {
        HaloToast.showError(context, msg: error.toString());
      }
    }
  }
}
