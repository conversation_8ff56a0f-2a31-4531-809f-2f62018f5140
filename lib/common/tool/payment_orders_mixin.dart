import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:halo_pos/common/tool/payment.dart';
import 'package:halo_pos/common/tool/performance_capture_util.dart';
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_pos/common/tool/tts_util.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/math_util.dart';
import 'package:haloui/widget/halo_dialog.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../bill/entity/bill_sale_bill_detail_dto.dart';
import '../../bill/entity/bill_save_exception_dto.dart';
import '../../bill/entity/bill_save_reslut_dto.dart';
import '../../bill/entity/bill_save_result_type.dart';
import '../../bill/entity/goods_bill.dto.dart';
import '../../bill/entity/pay_result_dto.dart';
import '../../bill/entity/payment_dto.dart';
import '../../bill/entity/preferential_dto.dart';
import '../../bill/model/bill_model.dart';
import '../../bill/model/ptype_model.dart';
import '../../bill/settlement/entity/atype_info_bean.dart';
import '../../bill/settlement/widget/exception_widget.dart';
import '../../bill/tool/bill_tool.dart';
import '../../bill/tool/decimal_display_helper.dart';
import '../../bill/tool/sale_event_buds.dart';
import '../../bill/widget/exception_item_widget.dart';
import '../../cashierbox/entity/cash_box_payment_request_dto.dart';
import '../../enum/bill_pay_state.dart';
import '../../enum/bill_post_state.dart';
import '../../enum/bill_type.dart';
import '../../enum/payment_enum.dart';
import '../../enum/sync_info_type.dart';
import '../../login/entity/store/store_payment.dart';
import '../../plugin/notify_voice_plugin.dart';
import '../../print/tool/print_tool.dart';
import '../../vip/entity/get_vip_level_score_rights_card_response.dart';
import '../../widgets/Info_sync_dialog.dart';
import '../../widgets/base/base_stateful_page.dart';
import '../../widgets/scanner_dialog.dart';
import '../login/login_center.dart';
import '../string_res.dart';
import 'dialog_util.dart';

mixin PaymentOrdersMixin<T extends StatefulWidget> on State<T> {
  ///单据
  late GoodsBillDto goodsBillDto;

  ///支付方式
  List<AtypeInfoBean> paymentList = [];

  ///单据类型
  late final BillType billType;

  ///是否开启打印
  bool printReceipt = SpTool.getPrint();

  ///会员信息
  VipWithLevelAssertsRightsCardDTO? vipInfo;

  ///退货单要退资产
  VipAssertsBillDtoBean? vipAssertsBillDtoBean;

  ///聚合支付所需参数
  Map payInfo = {};

  getPrintReceipt() {
    printReceipt = SpTool.getPrint();
    if (billType == BillType.SaleChangeBill) {
      printReceipt = false;
    }
  }

  //region 是否包含聚合支付并检查支付条件
  bool checkPay(BuildContext context, GoodsBillDto goodsBillDto,
      List<AtypeInfoBean> paymentList) {
    if (!paymentList.any(
        (element) => element.enable && element.storePayway.paywayType == 2)) {
      return false;
    }
    PaymentDto dto = goodsBillDto.payment.firstWhere(
        (element) => element.paywayType == 2,
        orElse: () => PaymentDto());
    if (dto.paywayId == null || dto.currencyAtypeTotal == "0") {
      return false;
    }
    if (goodsBillDto.vchtype == BillTypeData[BillType.SaleBackBill]) {
      List<PaymentDto> outNoList = goodsBillDto.sourcePayment
          .where((element) => element.outNo?.isNotEmpty ?? false)
          .toList();
      if (outNoList.isEmpty) {
        HaloToast.show(context, msg: "没有可退的聚合支付！");
        return true;
      }
    } else if (goodsBillDto.vchtype == BillTypeData[BillType.SaleChangeBill] &&
        Decimal.parse(dto.currencyAtypeTotal!) < Decimal.zero) {
      String? outNo = goodsBillDto.sourcePayment
          .firstWhereOrNull((element) => element.outNo!.isNotEmpty)
          ?.outNo;
      if (outNo == null || outNo.isEmpty) {
        HaloToast.show(context, msg: "没有可退的聚合支付！");
        return true;
      }
    }
    return true;
  }

  //endregion

  //region 支付订单
  payOrderBill(BuildContext context, GoodsBillDto goodsBillDto) async {
    if (paymentList.isEmpty) {
      setPaymentList();
    }
    getPrintReceipt();
    if (checkPay(context, goodsBillDto, paymentList)) {
      ///聚合支付
      payInfo = await buildPayInfo(context, goodsBillDto);
      if (payInfo.isEmpty) {
        return;
      }
      if (context.mounted) {
        _payAndSaveGoods(goodsBillDto);
      }
    } else {
      ///现金支付
      goodsBillDto.confirm = true;
      _saveGoodsBill(goodsBillDto);
    }
  }

  Future<Map> buildPayInfo(
      BuildContext context, GoodsBillDto goodsBillDto) async {
    ///创建新的支付流水号
    String newOutNo = goodsBillDto.vchcode! +
        DateTime.now().millisecondsSinceEpoch.toString();

    ///找到聚合支付
    PaymentDto dto = goodsBillDto.payment.firstWhere(
        (element) => element.paywayType == 2,
        orElse: () => PaymentDto());

    ///退货单
    if (goodsBillDto.vchtype == BillTypeData[BillType.SaleBackBill]) {
      String? outNo = goodsBillDto.sourcePayment
          .firstWhereOrNull((element) => element.outNo!.isNotEmpty)
          ?.outNo;
      String? orderNo = goodsBillDto.sourcePayment
          .firstWhereOrNull((element) => element.payOrderNo!.isNotEmpty)
          ?.payOrderNo;
      dto.outNo = newOutNo;

      return Future(() => {
            "tradeAmount": dto.currencyAtypeTotal,
            "outNo": outNo,
            "orderNo": orderNo,
            "paywayId": dto.paywayId,
            "refundOutNo": newOutNo,
          });
    } else if (goodsBillDto.vchtype == BillTypeData[BillType.SaleChangeBill] &&
        Decimal.parse(dto.currencyAtypeTotal!) < Decimal.zero) {
      String? outNo = goodsBillDto.sourcePayment
          .firstWhereOrNull((element) => element.outNo!.isNotEmpty)
          ?.outNo;
      String? orderNo = goodsBillDto.sourcePayment
          .firstWhereOrNull((element) => element.payOrderNo!.isNotEmpty)
          ?.payOrderNo;
      dto.outNo = newOutNo;

      return Future(() => {
            "tradeAmount": num.parse(dto.currencyAtypeTotal!).abs().toString(),
            "outNo": outNo,
            "orderNo": orderNo,
            "paywayId": dto.paywayId,
            "refundOutNo": newOutNo,
          });
    } else {
      return await showScannerDialog(context).then((scanResult) {
        if (scanResult == null || scanResult.isEmpty) {
          return Future(() => {});
        }
        //去掉\n
        var reducedString = scanResult;
        if (scanResult.contains("\n")) {
          var indexOfSlashN = scanResult.indexOf("\n");
          reducedString = scanResult.substring(0, indexOfSlashN);
        }
        dto.outNo = newOutNo;
        return Future(() => {
              "paywayId": dto.paywayId,
              "tradeAmount": dto.currencyAtypeTotal,
              "authCode": reducedString,
              "outNo": newOutNo,
            });
      });
    }
  }

  //region 非聚合支付
  _saveGoodsBill(GoodsBillDto goodsBill) {
    ///非聚合支付需要设为核算完成 已支付状态
    goodsBill.postState = BillPostStateString[BillPostState.PROCESS_COMPLETED];
    goodsBill.payState = BillPayStateString[BillPayState.Paied];
    goodsBill.sourceOperation = 3; //操作来源为零售
    BillTool.reCalcStatistic(goodsBill, billType: billType);

    ///构建钱箱数据
    CashBoxPaymentRequestDto? cashBoxPaymentRequestDto =
        PaymentUtil.setCashPaymentFail(
            context, goodsBillDto.pettyCash, paymentList, getPaymentType());
    if (cashBoxPaymentRequestDto != null) {
      goodsBill.cashBoxPayment = cashBoxPaymentRequestDto;
    }
    BillModel.saveGoodsBill(context, goodsBill,
            paySaveBillEnum: PaySaveBillEnum.COMMON)
        .then((payResult) async {
      BillSaveResultDto value = payResult!.resultDTO!;
      if (value.resultType == BillSaveResultType.SUCCESS) {
        //单据保存后回填服务端返回信息
        BillTool.backFillBillAfterSubmit(payResult, goodsBill, goodsBillDto);
        if (goodsBill.vchtype == BillTypeData[BillType.SaleBill]) {
          updateInvoiceAndScoreInfo(payResult.resultDTO, goodsBill);
        }
        successCallback(goodsBill);
      } else if (value.resultType == BillSaveResultType.ERROR) {
        DialogUtil.showAlertDialog(context,
            childContent: Container(
              margin: const EdgeInsets.only(top: 10),
              child: ExceptionWidget(value.exceptionInfo!,
                  (context, index, BillSaveExceptionDto itemData) {
                return ExceptionItemWidget(
                  itemData.message ??
                      StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
                  BillTool.createSaveExceptionMessage(itemData.detailList!),
                  errorList: const [],
                );
              }),
            ),
            title: value.message ?? "");
      } else if (value.resultType == BillSaveResultType.INIOVER) {
        HaloToast.showError(context, msg: value.exceptionInfo![0].message);
      } else if (value.resultType == BillSaveResultType.CONFIRM) {
        DialogUtil.showConfirmDialog(context, actionLabels: ["取消", "继续"],
            confirmCallback: () {
          goodsBill.confirm = true;
          _saveGoodsBill(goodsBill);
        },
            childContent: Container(
              margin: const EdgeInsets.only(top: 10),
              child: ExceptionWidget(value.exceptionInfo!,
                  (context, index, BillSaveExceptionDto itemData) {
                return ExceptionItemWidget(
                  itemData.message ??
                      StringRes.BILL_SAVE_ERROR_TIPS.getText(context),
                  BillTool.createSaveExceptionMessage(itemData.detailList!),
                  errorList: const [],
                );
              }),
            ),
            title: value.message!);
      } else {
        HaloToast.showError(context, msg: value.exceptionInfo![0].message);
      }
    });
  }

  //region 聚合支付
  _payAndSaveGoods(GoodsBillDto goodsBill) {
    ///是否退款
    bool isReturn = (billType == BillType.SaleBackBill) ||
        (billType == BillType.SaleChangeBill &&
            Decimal.parse(goodsBill.currencyBillTotal) < Decimal.zero);

    ///聚合支付需要设置成待出入库 未支付状态
    goodsBill.postState = BillPostStateString[BillPostState.PROCESS_COMPLETED];
    goodsBill.payState = BillPayStateString[BillPayState.UnPay];
    BillTool.reCalcStatistic(goodsBill, billType: billType);

    ///构建钱箱记录 组合支付情况
    CashBoxPaymentRequestDto? cashBoxPaymentRequestDto =
        PaymentUtil.setCashPaymentFail(
            context, goodsBillDto.pettyCash, paymentList, getPaymentType());
    if (cashBoxPaymentRequestDto != null) {
      goodsBill.cashBoxPayment = cashBoxPaymentRequestDto;
    }
    goodsBill.sourceOperation = !isReturn ? 3 : 10;;
    goodsBill.confirm = true; //todo
    BillModel.saveGoodsBill(context, goodsBill,
            payInfo: payInfo,
            paySaveBillEnum: isReturn
                ? PaySaveBillEnum.SCANREFUND
                : PaySaveBillEnum.SCANPAY) //todo
        .then((payResult) async {
      PaymentUtil.processingPayResult(context, payResult,
          result: (PayResultType resultType) async {
        //从支付结果中拿到单据编号
        BillTool.backFillBillAfterSubmit(payResult, goodsBill, goodsBillDto);
        if (resultType == PayResultType.SUCCEEDED) {
          if (goodsBill.vchtype == BillTypeData[BillType.SaleBill]) {
            if (!isReturn) {
              updateInvoiceAndScoreInfo(payResult!.resultDTO, goodsBill);
            }
          }
          successCallback(goodsBill);
        }
      }, onActionListener: (int index) {
        doAction(index, goodsBill);
      });
    }).onError((DioError error, stackTrace) {
      if (error.response?.data["data"] != null) {
        //单据保存后回填服务端返回信息
        BillTool.backFillBillAfterSubmit(
            PayResult.fromJson(error.response?.data["data"]),
            goodsBill,
            goodsBillDto);
      }
      PaymentUtil.instance.showErrorInfo(
          context, error.response?.data["message"] ?? "支付失败-未知错误",
          onActionListener: (int index) {
        doAction(index, goodsBill);
      });
    });
  }

  ///成功后的处理方法
  Future<void> successCallback(GoodsBillDto goodsBill) async {
    if (goodsBill.vchtype == BillTypeData[BillType.SaleBill]) {
      SpTool.saveLastVchCode(goodsBill.vchcode!);
    }

    ///打印
    await _print(goodsBill);
    SpTool.saveEtypeInfo(goodsBill.etypeId ?? "", goodsBill.efullname ?? "");

    ///批量修改库存
    PtypeModel.changePtypeStockQtyByGoodsBill(
        outDetail: goodsBill.outDetail, inDetail: goodsBill.inDetail);

    ///检查是否有现金方式 打开钱箱
    _checkCash();
    if (context.mounted) HaloToast.show(context, msg: "支付成功");
    NotifyVoicePlugin.success();
    paymentSuccessCallBack();
  }

  ///错误弹窗选择事件
  doAction(int index, GoodsBillDto goodsBill) {
    if (index == 0||index==2) {
      goodsBill.whetherPost = true;
      if(index == 0){
        goodsBill.memo = "手工确认已收到钱款";
        BillModel.updateBillMemo(context, goodsBill.memo,goodsBill.vchcode!,goodsBill.etypeId!,goodsBill.dtypeId!,goodsBill.summary??"").then((value) async {
          ///更新备注成功
        });
      }

      BillModel.updateBillPayState(context, goodsBill).then((value) async {
        successCallback(goodsBill);
      });
    } else {
      BillModel.logicalDeleteBill(context, goodsBill.vchcode!).then((value) {
        ///删除成功
      });
    }
  }

  ///发票信息
  void updateInvoiceAndScoreInfo(BillSaveResultDto? saveResultDto, goodsBill) {
    if (null == saveResultDto) {
      return;
    }

    if (null != goodsBill.vipBillInfo) {
      goodsBill.vipBillInfo.saleScore = saveResultDto.saleScore;
      goodsBill.vipBillInfo.scoreTotal = (vipInfo?.asserts?.score ?? 0) +
          (saveResultDto.saleScore ?? 0) -
          (goodsBillDto.preferentialHelp[Preferential.scoreDiscount]?.value ??
              0) as int?;
    }
  }

  //endregion

  ///检查是否有现金支付,有的话打开钱箱
  void _checkCash() async {
    // var cashPayment = paymentList.firstWhereOrNull(
    //         (element) => element.enable && element.storePayway.paywayType == 0);
    // if (cashPayment != null) {
    //   //如果选择了现金支付，则打开钱箱
    //   PrintTool.openCashBox(context);
    // }
  }

  ///打印
  _print(GoodsBillDto goodsBill) async {
    if (printReceipt) {
      await PrintTool.printBill(context, goodsBill);
    }
  }

  //2为销售存入，3为退货取出
  int getPaymentType() {
    if ((billType == BillType.SaleChangeBill &&
            num.parse(goodsBillDto.currencyBillTotal) >= 0) ||
        billType == BillType.SaleBill) {
      return 2;
    } else {
      return 3;
    }
  }

  setPaymentList() {
    paymentList = [];
    List<StorePayway> paywayList = SpTool.getStoreInfo()?.paywayList ?? [];
    for (int i = 0; i < paywayList.length; i++) {
      StorePayway storePayway = paywayList[i];
      //非会员时，屏蔽储值支付
      if (storePayway.paywayType == 3 && vipInfo == null) {
        continue;
      }
      AtypeInfoBean infoBean =
          AtypeInfoBean(storePayway, "0", enable: true, modify: false);
      paymentList.add(infoBean);
    }
  }

  paymentSuccessCallBack() {}

  paymentCancelCallBack() {}
}
