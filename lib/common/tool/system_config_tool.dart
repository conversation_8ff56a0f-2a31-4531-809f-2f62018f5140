import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:haloui/utils/math_util.dart';

import '../../../common/tool/sp_tool.dart';
import '../../../entity/system/system_config_dto.dart';
import '../../../enum/bill_decimal_type.dart';

///
///@ClassName: system_config_tool
///@Description: 类作用描述
///@Author: tanglan
///@Date: 8/10/21 2:58 PM
class SystemConfigTool {
  Future<String> getUniqueId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    // if (Platform.isIOS) {
    //   IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
    //   print("ios唯一设备码："+iosDeviceInfo.identifierForVendor);
    //   return iosDeviceInfo.identifierForVendor; // unique ID on iOS
    // } else {
    AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
    debugPrint("android唯一设备码：${androidDeviceInfo.id}");

    return androidDeviceInfo.id; // unique ID on Android
    // }
  }

  static int getDigital(BillDecimalType type) {
    SystemConfigDto systemConfigDto = SpTool.getSystemConfig();
    switch (type) {
      case BillDecimalType.QTY:
        return systemConfigDto.sysDigitalQty;
      case BillDecimalType.DISCOUNT:
        return systemConfigDto.sysDigitalDiscount;
      case BillDecimalType.PRICE:
        return systemConfigDto.sysDigitalPrice;
      case BillDecimalType.TOTAL:
        return systemConfigDto.sysDigitalTotal;
      default:
        return 8;
    }
  }

  //乘法
  static String doubleMultiple(String v1, String v2, BillDecimalType type) {
    int digital = getDigital(type);
    return MathUtil.multiplication(v1, v2).toStringAsFixed(digital);
  }

  //除法
  static String doubleDivision(String v1, String v2, BillDecimalType type) {
    int digital = getDigital(type);
    return MathUtil.division(v1, v2).toStringAsFixed(digital);
  }

  //乘法
  static num doubleMultipleToDecimal(num v1, num v2, BillDecimalType type) {
    int digital = SystemConfigTool.getDigital(type);
    var res = MathUtil.multiplication(v1.toString(), v2.toString())
        .toStringAsFixed(digital);
    return MathUtil.parseToDecimal(res).toDouble();
  }

  //除法
  static num doubleDivisionToDecimal(num v1, num v2, BillDecimalType type) {
    int digital = SystemConfigTool.getDigital(type);
    var res = MathUtil.division(v1.toString(), v2.toString())
        .toStringAsFixed(digital);
    return MathUtil.parseToDecimal(res).toDouble();
  }

  //除法
  static num divisionToDecimalFromStr(
      String v1, String v2, BillDecimalType type) {
    String? res = doubleDivision(v1, v2, type);
    return MathUtil.parseToDecimal(res).toDouble();
  }

  //减法
  static num doubleSubtractionToDecimal(num v1, num v2, BillDecimalType type) {
    int digital = SystemConfigTool.getDigital(type);
    var res = MathUtil.subtraction(v1.toString(), v2.toString())
        .toStringAsFixed(digital);
    return MathUtil.parseToDecimal(res).toDouble();
  }

  //加法
  static num doubleAddToDecimal(num v1, num v2, BillDecimalType type) {
    int digital = SystemConfigTool.getDigital(type);
    var res =
        MathUtil.add(v1.toString(), v2.toString()).toStringAsFixed(digital);
    return MathUtil.parseToDecimal(res).toDouble();
  }
}
