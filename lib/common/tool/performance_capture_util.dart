import 'dart:isolate';

import 'package:dio/dio.dart';
import 'package:halo_pos/common/tool/sp_tool.dart';
import 'package:halo_utils/utils/String_util.dart';
import 'package:halo_utils/utils/logger.dart';

import '../../entity/update/version_info.dart';
import '../../login/model/login_user_model.dart';

///
///@ClassName: performance_capture_util
///@Description: 性能埋点工具类
///@Author: tanglan
///@Date: 2025/1/15
class PerformanceCaptureUtil {
  static Isolate? _isolate;
  static SendPort? _sendPort;
  static final ReceivePort _receivePort = ReceivePort();

  static final Map _actionList = {};
  static String? costTimeReportUrl;

  /// 初始化子线程和消息队列
  static Future<void> initialize() async {
    Logger.v("性能埋点", "初始化数据管理器...");
    VersionInfo? versionInfo = SpTool.getVersionInfo();
    if (null != versionInfo) {
      costTimeReportUrl = SpTool.getVersionInfo()!.reportUrl;
    }
    _isolate = await Isolate.spawn(_isolateEntry,
        SenderParams(_receivePort.sendPort, costTimeReportUrl ?? ""));
    _receivePort.listen((message) {
      if (message is SendPort) {
        _sendPort = message;
        Logger.v("性能埋点", "子线程已启动并准备接收消息");
      } else {
        Logger.v("性能埋点", "子线程返回消息: $message");
      }
    });
  }

  ///耗时埋点开始
  static void start(String actionTypeName) {
    try {
      if (!_needSaveAction(actionTypeName)) {
        return;
      }
      _actionList[actionTypeName] = DateTime.now().millisecondsSinceEpoch;
    } catch (e) {
      Logger.v("性能埋点", "埋点开始异常：$actionTypeName");
    }
  }

  static _needSaveAction(String actionTypeName) {
    // List<String> allowNoUrlToSave = [
    //   ActionReportType.LoginGetCompany,
    //   ActionReportType.LoginBuProfile
    // ];
    // if (allowNoUrlToSave.contains(actionTypeName)) {
    //   return true;
    // }
    if (StringUtil.isNotEmpty(costTimeReportUrl)) {
      return true;
    }
    // return false;

    return true;
  }

  ///耗时埋点结束
  static void end(String actionTypeName) {
    try {
      if (!_needSaveAction(actionTypeName)) {
        return;
      }
      if (_actionList.containsKey(actionTypeName)) {
        int startTime = _actionList[actionTypeName];
        int endTime = DateTime.now().millisecondsSinceEpoch;
        int executionTime = endTime - startTime;
        _actionList.remove(actionTypeName);
        LoginUserModel loginUserModel = SpTool.getLoginUser();
        PerformanceTimeReport report = PerformanceTimeReport();
        report.funcName = actionTypeName;
        report.deployName = loginUserModel.deploy;
        report.profileId = loginUserModel.profileId;
        report.startTime = startTime;
        report.executionTime = executionTime;
        if (_sendPort != null) {
          _sendPort!.send(report); // 直接发送数据给子线程
        }
      } else {
        Logger.v("性能埋点", "end失败，没有开始的埋点$actionTypeName");
      }
    } catch (e) {
      Logger.v("性能埋点", "埋点结束异常：$actionTypeName");
    }
  }

  /// 释放资源
  static void dispose() {
    Logger.v("性能埋点", "释放数据管理器...");
    _isolate?.kill(priority: Isolate.immediate);
    _isolate = null;
    _receivePort.close();
  }
}

/// 子线程入口函数（顶级函数，必须在类外部定义）
void _isolateEntry(SenderParams sendPortParam) {
  final ReceivePort isolateReceivePort = ReceivePort();
  String costTimeReportUrl = sendPortParam.costTimeReportUrl ?? "";
  sendPortParam.sendPort!.send(isolateReceivePort.sendPort);
  const int threshold = 5; // 数据条数同步阈值
  const int syncInterval = 5; // 同步时间间隔

  List<PerformanceTimeReport> messageQueue = []; // 子线程中的数据队列
  DateTime? lastSyncTime; // 上次上传时间

  final childThreadDioManager = ChildThreadDioManager();
  isolateReceivePort.listen((message) async {
    if (message is PerformanceTimeReport) {
      // 将数据添加到子线程队列
      messageQueue.add(message);
      Logger.v("性能埋点", "子线程：接收到数据，当前队列大小=${messageQueue.length}");

      // 检查同步条件
      final now = DateTime.now();
      final isThresholdReached = messageQueue.length >= threshold; // 条数阈值
      final isTimeElapsed = lastSyncTime == null ||
          now.difference(lastSyncTime!).inMinutes >= syncInterval; // 时间阈值

      if (isThresholdReached || isTimeElapsed) {
        Response response = await childThreadDioManager.request(
            costTimeReportUrl, messageQueue.map((e) => e.toJson()).toList());
        if (response.statusCode == 200) {
          messageQueue.clear();
          // 更新上次同步时间
          lastSyncTime = now;
          Logger.v("性能埋点", "子线程：数据上传完成，队列已清空");
          return;
        }
        Logger.v("性能埋点", "子线程：数据同步失败");
      } else {
        Logger.v("性能埋点", "子线程：条件未满足，暂不上传。");
      }
    }
  });
}

// 子线程中的 Dio 单例管理器(用于后台任务的子线程网络访问)
class ChildThreadDioManager {
  static Dio? _dio;

  ChildThreadDioManager() {
    _dio ??= Dio(BaseOptions(
      connectTimeout: 5000,
      receiveTimeout: 3000,
      headers: {
        'Content-Type': 'application/json',
      },
    ));
  }

  /// 发起网络请求
  Future<Response> request(String url, dynamic params) async {
    return await _dio!.post(url, data: params);
  }
}

class PerformanceTimeReport {
  String? funcName;
  int? startTime;
  int? executionTime;
  String? deployName;
  String? profileId;

  PerformanceTimeReport();

  Map toJson() => {
        "funcName": "云零售->POS/$funcName",
        "executionTime": executionTime,
        "deployName": deployName,
        "profileId": profileId,
      };
}

class SenderParams {
  SendPort sendPort;
  String costTimeReportUrl;

  SenderParams(this.sendPort, this.costTimeReportUrl);
}

class PerformanceTimeName {
  static const String loginByPwd = "账号密码登录";
  static const String loginBySMS = "验证码登录";
  static const String companyList = "验证码登录->获取公司列表";
  static const String storeSelect = "门店选择->查询";

  static const String configSync = "权限配置信息->信息同步";
  static const String pTypeSync = "商品信息->信息同步";
  static const String storeSync = "门店信息->信息同步";
  static const String scoreSync = "积分策略->信息同步";
  static const String promotionSync = "促销->信息同步";

  static const String pTypeSelect = "选择商品";
  static const String scanPType = "扫描选商品->开单";
  static const String choicePType = "选品开单查商品->开单";
  static const String scanPay = "快捷扫码支付->支付";
  static const String settlement = "开单单据保存->开单";
  static const String billQuery = "单据查询-退换货选单-取单->查询";
  static const String tenderOrderBill = "要货申请->要货";
  static const String tenderBill = "库存调拨->调拨";
  static const String channelBill = "全渠道订单查询->查询";

  static const String searchVip = "会员列表查询->会员";
  static const String vipDetail = "会员详情->会员";

  static const String vipRechargeRule = "会员充值策略->充值";
  static const String vipRecharge = "会员充值升级->充值升级";

  static const String shiftChangesLoad = "数据加载->交接班";
  static const String shiftChanges = "交接班提交->交接班";
}
