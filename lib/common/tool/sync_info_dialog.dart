import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:haloui/widget/halo_checkbox.dart';

import '../../bill/bill/channel/widget/base_common_dialog.dart';
import '../../bill/tool/sale_event_buds.dart';
import '../../enum/sync_info_type.dart';
import '../../iconfont/icon_font.dart';
import '../../widgets/info_sync_dialog.dart';
import '../../widgets/halo_pos_label.dart';
import '../style/app_colors.dart';

class SyncInfoDialog extends StatefulWidget {
  const SyncInfoDialog({Key? key}) : super(key: key);

  @override
  State<SyncInfoDialog> createState() => _SyncInfoDialogState();
}

class _SyncInfoDialogState extends BaseCommonDialogState<SyncInfoDialog> {
  List<SyncInfoModel> infoModels = [
    SyncInfoModel(
      title: "商品信息",
      select: false,
      syncInfoType: SyncInfoType.pType,
    ),
    SyncInfoModel(
      title: "门店资料和设置",
      select: false,
      syncInfoType: SyncInfoType.storeInfo,
    ),
    SyncInfoModel(
      title: "职员权限和系统配置",
      select: false,
      syncInfoType: SyncInfoType.permissionConfig,
    ),
  ];

  @override
  double get height => 480.w;

  @override
  String get title => "手动同步信息";

  @override
  double get width => 700.w;

  @override
  Widget buildContent(BuildContext context) {
    return HaloContainer(
      direction: Axis.vertical,
      children: [
        HaloContainer(
          padding: EdgeInsets.all(20.w),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildCheckBox(context, infoModels[0]),
            _buildCheckBox(context, infoModels[1]),
          ],
        ),
        HaloContainer(
          padding: EdgeInsets.all(20.w),
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [_buildCheckBox(context, infoModels[2])],
        ),
        Padding(
          padding: EdgeInsets.all(20.w),
          child: HaloPosLabel(
            "说明:当系统数据发生变动时可手动在pos端进行数据同步，同步时请确保未进行业务操作，同步后会初始化页面",
            textStyle: TextStyle(fontSize: 24.sp, color: Colors.grey),
            maxLines: 2,
          ),
        ),
        _buildBottomBody(context),
      ],
    );
  }

  Widget _buildCheckBox(BuildContext context, SyncInfoModel infoModel) {
    return GestureDetector(
      onTap: () {
        setState(() {
          infoModel.select = !infoModel.select;
        });
      },
      child: HaloContainer(
        borderRadius: BorderRadius.circular(5.w),
        width: 300.w,
        children: [
          HaloCheckBox(
            value: infoModel.select,
            onChanged: (bool value) {
              setState(() {
                infoModel.select = !infoModel.select;
              });
            },
            defaultImage: IconFont(
              IconNames.weixuanzhong,
              size: 32.w,
              color: '#FFBFBFC1',
            ),
            selectedImage: IconFont(
              IconNames.xuanzhong,
              size: 32.w,
              color: '#FF2C68FF',
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: HaloPosLabel(
              infoModel.title,
              textStyle: TextStyle(
                fontSize: 26.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBody(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 100.w,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          HaloButton(
            buttonType: HaloButtonType.elevatedButton,
            outLineWidth: 1.w,
            foregroundColor: AppColors.normalTextColor,
            borderColor: AppColors.btnBorderColor,
            height: 50.h,
            width: 190.w,
            text: "立即同步",
            textColor: Colors.white,
            fontSize: 26.sp,
            onPressed: () {
              showSyncDialog();
            },
          ),
          SizedBox(width: 40.w),
          HaloButton(
            buttonType: HaloButtonType.outlinedButton,
            outLineWidth: 1.w,
            foregroundColor: AppColors.normalTextColor,
            borderColor: AppColors.btnBorderColor,
            height: 50.h,
            width: 190.w,
            text: "取消",
            fontSize: 26.sp,
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  showSyncDialog() {
    List<SyncInfoType> infoType =
        infoModels
            .where((element) => element.select)
            .map((e) => e.syncInfoType)
            .toList();
    //同步积分策略
    if (infoType.contains(SyncInfoType.storeInfo) &&
        !infoType.contains(SyncInfoType.scoreConfiguration)) {
      infoType.add(SyncInfoType.scoreConfiguration);
    }
    //同步打印配置
    if (infoType.contains(SyncInfoType.storeInfo)) {
      infoType.add(SyncInfoType.printConfiguration);
    }
    //TODO TL 每次登录是否开启商品自动同步
    if (infoType.isEmpty) {
      HaloToast.showInfo(context, msg: "请选择需要同步的内容");
      return;
    }
    HaloDialog(
      context,
      child: InfoSyncDialog(
        context,
        sycInfoList: infoType,
        isAutoClose: true,
        afterSync: () async {
          if (infoType.contains(SyncInfoType.pType)) {
            SaleEventBus.getInstance().fire(SaleEventBus.doClear);
            SaleEventBus.getInstance().fire(SaleEventBus.refreshSearchedGoods);
          }
          SaleEventBus.getInstance().fire(SaleEventBus.updateSaleBillView);
          Navigator.pop(context);
        },
      ),
    ).show();
  }
}

class SyncInfoModel {
  String title = "";
  bool select = false;
  late SyncInfoType syncInfoType;

  SyncInfoModel({
    required this.title,
    required this.select,
    required this.syncInfoType,
  });
}
