//获取随机4位+时间戳
import 'dart:math';

class UUID{
  static String getUUid() {
    String randomstr = "1${Random().nextInt(10)}";
    for (var i = 0; i < 4; i++) {
      var str = Random().nextInt(10);
      randomstr = "$randomstr$str";
    }
    var timeNumber = DateTime.now().millisecondsSinceEpoch;//时间
    return "$randomstr$timeNumber";
  }

  static String getVchcode() {
    // 1. 获取当前时间戳（毫秒）
    int milliseconds = DateTime.now().millisecondsSinceEpoch;

    // 2. 将毫秒转换为微秒
    int microseconds = milliseconds * 1000;

    // 3. 将微秒转换为字符串
    String microsecondsStr = microseconds.toString();

    // 4. 目标数字的长度 (835881630294872377 的长度)
    int targetLength = 18;

    // 5. 计算需要补齐的位数
    int paddingLength = targetLength - microsecondsStr.length;

    // 6. 如果需要补齐，生成随机数
    String randomNumberStr = "";
    if (paddingLength > 0) {
      Random random = Random();
      for (int i = 0; i < paddingLength; i++) {
        randomNumberStr += random.nextInt(10).toString(); // 生成 0-9 的随机数字
      }
    }

    // 7. 拼接微秒和随机数
    String combinedNumber = microsecondsStr + randomNumberStr;

    //8. 如果combinedNumber位数大于targetLength
    if (combinedNumber.length > targetLength){
      combinedNumber = combinedNumber.substring(0,targetLength);
    }

    return combinedNumber;
  }

}
