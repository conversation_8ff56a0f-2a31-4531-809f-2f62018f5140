import 'package:flutter/material.dart';

///插入字符
void insert(TextEditingController controller, String content) {
  String text = controller.text;
  TextSelection selection = controller.selection;
  int start = selection.start;
  int end = selection.end;
  //如果选中了文字，则替换选中的文字为要插入的字符
  ////普通光标，直接插入字符
  text = text.replaceRange(start, end, content);
  start = start + content.length;
  controller.value = controller.value.copyWith(
    text: text,
    selection: TextSelection.collapsed(offset: start),
  );
  // controller.text = text;
  // controller.selection =
  //     TextSelection.fromPosition(TextPosition(offset: start));
}

///删除字符
void delete(TextEditingController controller) {
  String text = controller.text;
  if (text.isEmpty) {
    return;
  }
  TextSelection selection = controller.selection;
  int start = selection.start;
  int end = selection.end;
  //如果选中了字符，则删除选中的字符
  if (start != end) {
    text = text.replaceRange(start, end, "");
  }
  //普通光标，没有选中文字，删除一个字符
  else {
    if (start == 0) {
      return;
    }
    text = text.replaceRange(--start, end, "");
  }
  controller.value = controller.value.copyWith(
    text: text,
    selection: TextSelection.collapsed(offset: start),
  );
  // controller.text = text;
  // controller.selection =
  //     TextSelection.fromPosition(TextPosition(offset: start));
}

///清空
void clean(TextEditingController controller) {
  setValue(controller, "");
  // controller.selection =
  //     TextSelection.fromPosition(const TextPosition(offset: 0));
  // controller.text = "";
}

///设置文本，并且将光标置于最后
void setValue(TextEditingController controller, String value) {
  controller.value = controller.value.copyWith(
    text: value,
    selection: TextSelection.collapsed(offset: value.length),
  );
}
