import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

///限制小数位数的输入
///不支持负数
///键盘输入也不能判断最大值最小值（如最小值，当设置最小值10，现在输入框内是15，我想输入14，删掉5变成1，就小于最小值。当使用负数时，最大值同理）
class DecimalScaleInputFormatter extends TextInputFormatter {
  ///小数位数
  final int scale;

  final num max;

  ///是否去除开头多余的0
  final bool removeZero;

  DecimalScaleInputFormatter._(this.scale, this.max, this.removeZero);

  factory DecimalScaleInputFormatter(
      {int scale = 0, num max = -1, bool removeZero = true}) {
    if (scale < 0) {
      scale = 0;
    }
    return DecimalScaleInputFormatter._(scale, max, removeZero);
  }

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String value = newValue.text.trim();
    //空字符串，先给个0
    if (value.isEmpty) {
      value = "0";
    }
    bool isDecimal = scale > 0;
    //如果不是数字字符串，则不做更改，返回老字符串
    if (!_isNumber(value, isDecimal)) {
      return oldValue;
    }
    List<String> split = value.split(".");
    //如果是小数,判断小数位数
    if (isDecimal) {
      //字符串有小数点
      if (split.length > 1) {
        //如果小数点前面没有数字，则在小数点前面加个0
        if (split[0].isEmpty) {
          split[0] = "0";
        }
        //判断小数位数是否超过规定小数位数
        if (split[1].length > scale) {
          split[1] = split[1].substring(0, scale);
        }
        value = "${split[0]}.${split[1]}";
      }
    } else {
      value = split[0];
    }
    //去掉数字前面多余的0
    if (removeZero) {
      Characters characters;
      while ((characters = value.characters).length > 1 &&
          characters.characterAt(0).string == "0" &&
          (!isDecimal || characters.characterAt(1).string != ".")) {
        value = value.substring(1);
      }
    }
    //判断最大值
    if (max >= 0 && (num.tryParse(value) ?? 0) > max) {
      value = max.toString();
    }
    return TextEditingValue(
        text: value, selection: TextSelection.collapsed(offset: value.length));
  }

  bool _isNumber(String text, bool isDecimal) {
    RegExp regExp = RegExp(isDecimal ? r'[^0-9.]' : r'[^0-9]');
    return !regExp.hasMatch(text) && num.tryParse(text) != null;
  }
}
