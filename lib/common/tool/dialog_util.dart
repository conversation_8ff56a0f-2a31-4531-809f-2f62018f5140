import 'package:flutter/material.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/widget/custom_dialog.dart';
import 'package:haloui/widget/halo_dialog.dart';

class DialogUtil {
  static Future<dynamic> showAlertDialog(
    BuildContext context, {
    double childPadding = 25,
    String title = "", //标题
    TextStyle? titleTextStyle,
    String content = "", //内容
    bool dismissOnTouchOutside = true,
    int actionCount =
        1, //此参数主要用于不传actionLabels的时候，显示默认的按钮label，按钮个数，默认为1，如果传了actionLabels则不需要传此参数
    Widget?
        childContent, //如果不需要自定义布局，则传content，不传child，如果需要自定义布局，则传child，不传content
    Widget? child,
    List<String>? actionLabels, //按钮，顺序为从左到右
    List<Color>? actionColors, //按钮文字颜色，从0开始，顺序为从左到右
    Function(int value)? onActionListener, //按钮监听，顺序为从左到右，以0开始
  }) {
    List<CustomButtonModel> actionModels = [];
    actionLabels = (actionLabels == null || actionLabels.isEmpty)
        ? (actionCount == 1
            ? ["确定"]
            : (actionCount == 2
                ? [
                    "取消",
                    "确定",
                  ]
                : actionLabels))
        : actionLabels;
    if (actionLabels != null && actionLabels.isNotEmpty) {
      for (var title in actionLabels) {
        actionModels.add(CustomButtonModel(textString: title));
      }
    }

    return HaloDialog(context,
        title: title,
        titleTextStyle: titleTextStyle ??
            TextStyle(fontSize: ScreenUtil().setSp(36), color: Colors.grey),
        desc: content,
        contentWidget: childContent,
        child: child,
        dismissOnTouchOutside: dismissOnTouchOutside,
        actionModels: actionModels,
        barrierColor: const Color(0x7F000000),
        marginHorizontal: 600.w,
        actionHeight: 100.w,
        onMultiActionListener: onActionListener, okActionListener: () {
      if (null == onActionListener) {
        return;
      }
      onActionListener(1);
    }, cancelActionListener: () {
      if (null == onActionListener) {
        return;
      }
      onActionListener(0);
    }).show();
  }

  static Future<dynamic> showConfirmDialog(
    BuildContext context, {
    double childPadding = 25,
    String title = "提示", //标题
    TextStyle? titleTextStyle,
    String content = "", //内容
    Widget?
        childContent, //如果不需要自定义布局，则传content，不传child，如果需要自定义布局，则传child，不传content
    Widget? child,
    List<String>? actionLabels, //按钮，顺序为从左到右
    List<Color>? actionColors, //按钮文字颜色，从0开始，顺序为从左到右
    VoidCallback? confirmCallback,
    VoidCallback? cancelCallback,
  }) {
    List<CustomButtonModel> actionModels = [];
    if (actionLabels != null && actionLabels.isNotEmpty) {
      for (var title in actionLabels) {
        actionModels.add(CustomButtonModel(textString: title));
      }
    }
    return HaloDialog(context,
            isShowAction: true,
            title: title,
            titleTextStyle: titleTextStyle ??
                TextStyle(fontSize: ScreenUtil().setSp(36), color: Colors.grey),
            desc: content,
            contentWidget: childContent,
            child: child,
            barrierColor: const Color(0x7F000000),
            marginHorizontal: 600.w,
            actionHeight: 100.w,
            actionModels: actionModels,
            okActionListener: confirmCallback,
            cancelActionListener: cancelCallback)
        .show();
  }
}
