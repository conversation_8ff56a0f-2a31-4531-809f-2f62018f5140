import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';

// import 'package:halo_pos_lower/home/<USER>' as lower_version_root;
import 'package:halo_utils/halo_utils.dart';
import 'package:haloui/haloui.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../entity/update/version_info.dart';
import '../../../settting/model/update_model.dart';
import '../../../settting/widget/update/update_progress_dialog.dart';
import '../../application.dart';
import '../../home/<USER>';
import '../../settting/widget/update/update_dialog.dart';
import '../../widgets/base/halo_pos_alert_dialog.dart';
import 'sp_tool.dart';
// import 'package:halo_pos_lower/home/<USER>' as lower_version_root;

class VersionCheck {
  static Future<bool> checkUpdate(
    BuildContext context, {
    bool onlyForce = false,
  }) async {
    VersionInfo? versionInfo = await UpdateModel.checkVersionInfo(context);
    if (versionInfo != null) {
      await SpTool.saveVersionInfo(versionInfo);
      if (context.mounted) {
        return VersionCheck.checkVersion(
          context,
          versionInfo,
          onlyForce: onlyForce,
        );
      }
    }
    return false;
  }

  static Future<bool> checkVersionUpdate(
    BuildContext context,
    VersionInfo? versionInfo, {
    bool onlyForce = false,
  }) async {
    if (versionInfo != null) {
      return VersionCheck.checkVersion(
        context,
        versionInfo,
        onlyForce: onlyForce,
      );
    }
    return false;
  }

  ///验证升级
  ///onlyForce 仅强制升级弹框
  static Future<bool> checkIsForce(VersionInfo versionInfo) async {
    String localVersion = await getLocalVersionName();
    return versionInfo.forceUpdateAndroid ||
        VersionCheck.compareVersion(
          localVersion: localVersion,
          remoteVersion: versionInfo.minVersion,
        );
  }

  ///校验此包版本号和当前登录账号所部署的服务器所支持的POS版本号
  ///如果相等，则说明此包是最新版本
  ///如果此包版本号>账号部署的版本号，则中断更新流程，弹窗提示用户卸载并且降级
  ///反之，则更新
  static Future<bool> checkVersion(
    BuildContext context,
    VersionInfo versionInfo, {
    bool onlyForce = false,
  }) async {
    //判断更新信息是否合法
    if (TextUtil.isEmpty(versionInfo.updateUrl) ||
        TextUtil.isEmpty(versionInfo.fullVersionAndroid) ||
        TextUtil.isEmpty(versionInfo.minVersion) ||
        TextUtil.isEmpty(versionInfo.versionName)) {
      return false;
    }
    String localVersion = await getLocalVersionName();
    if (compareVersion(
      localVersion: localVersion,
      remoteVersion: versionInfo.versionName,
    )) {
      bool isForce =
          versionInfo.forceUpdateAndroid ||
          VersionCheck.compareVersion(
            localVersion: localVersion,
            remoteVersion: versionInfo.minVersion,
          );

      ///非强制升级，且仅强制升级才弹框
      if (!isForce && onlyForce) {
        return false;
      }
      if (context.mounted) {
        await _showUpdateDialog(context, versionInfo, localVersion);
      }
      return true;
    }
    return false;
  }

  static Future<bool> haveNewVersion() async {
    String localVersion = await getLocalVersionName();
    VersionInfo? remoteVersion = SpTool.getVersionInfo();
    if (null == remoteVersion) {
      return false;
    }
    bool hasNewVersion = compareVersion(
      localVersion: localVersion,
      remoteVersion: remoteVersion.versionName,
    );
    return hasNewVersion;
  }

  ///对比本地版本号和线上版本号
  ///若有更新则返回true
  static bool compareVersion({
    required String localVersion,
    required String remoteVersion,
  }) {
    List<String> local = localVersion.split(".");
    List<String> remote = remoteVersion.split(".");

    ///这里拆分成数组之后，依次判断数组各元素大小
    for (int i = 0; i < max(local.length, remote.length); i++) {
      //是否更新
      bool hasNewVersion = false;
      if (i < local.length && i < remote.length) {
        int numLocal = int.tryParse(local[i]) ?? 0;
        int numRemote = int.tryParse(remote[i]) ?? 0;
        if (numLocal == numRemote) {
          continue;
        }
        if (numLocal > numRemote) {
          return false;
        }
        hasNewVersion = numLocal < numRemote;
      }
      //本地版本低于服务器版本，正常更新
      if (i >= local.length || hasNewVersion) {
        return true;
      }
    }
    //如果完全相等
    return false;
  }

  ///检查是否有存储权限
  static Future<bool> checkPermission(BuildContext context) async {
    if (Theme.of(context).platform == TargetPlatform.android) {
      var storePermissionStatus = await Permission.storage.request();
      if (!storePermissionStatus.isGranted) {
        var permission = await Permission.storage.request();
        if (permission.isPermanentlyDenied) {
          if (context.mounted) {
            HaloDialog(
              context,
              desc: "需要设置文件存储权限，是否打开应用设置？",
              cancelActionListener: () {
                HaloToast.show(context, msg: "取消");
              },
              okActionListener: () {
                openAppSettings();
              },
            ).show();
          }
        }
        return permission.isGranted;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  ///展示提示更新的弹窗，点击确定后，展示下载进度弹窗并开始下载
  static Future _showUpdateDialog(
    BuildContext context,
    VersionInfo versionInfo,
    String localVersion,
  ) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => UpdateDialog(versionInfo, localVersion),
    );
  }

  ///展示更新进度弹窗
  static Future showUpdatePage(
    BuildContext context,
    VersionInfo versionInfo,
    bool forceUpdate,
  ) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (buildContext) {
        return PopScope(
          canPop: false,
          child: UpdateProgressDialog(
            apkUrl: "${versionInfo.updateUrl}_${versionInfo.versionName}.apk",
            versionName: versionInfo.versionName,
            allowClosed: !forceUpdate,
          ),
        );
      },
    );
  }

  ///windows跳转到浏览器下载
  static Future downloadWindows(
    BuildContext context,
    VersionInfo versionInfo,
  ) async {
    await launchUrl(
      Uri.parse("${versionInfo.updateUrl}_${versionInfo.versionName}.exe"),
    );
  }

  // ///提示用户当前账号部署的服务器版本过低
  // static Future _showRemoteVersionIsLowDialog(
  //     BuildContext context, VersionInfo versionInfo) {
  //   return RemoteVersionIsLowDialog.show(context, versionInfo);
  // }

  ///获取此包版本号
  static Future<String> getLocalVersionName() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  static Widget getVersionRootWidget() {
    // VersionInfo? versionInfo = SpTool.getVersionInfo();
    // if (null != versionInfo &&
    //     versionInfo.apiVersion < Application.currentClientApiVersion) {
    //   return const lower_version_root.Root();
    // }
    return const Root();
  }
}
