import 'package:halo_utils/utils/date_util.dart';

// import 'package:timezone/data/latest.dart' as tz;
// import 'package:timezone/timezone.dart' as tz;

/// 获取结束时间
/// 获取的时间其实是相对date第2天的凌晨
/// 例如：date是 2023/11/15 11:24 ，返回的时间就是 2023/11/16 00:00:00.000
/// 这样处理的原因是,dart中日期before是<,而不是<=
DateTime getEndDate(DateTime date) {
  date = date.add(const Duration(days: 1));
  date = date.copyWith(hour: 0, minute: 0, second: 0, millisecond: 0);
  return date;
}

/// 获取结束时间
/// 获取的时间其实是相对date前1天的最后一秒
/// 例如：date是 2023/11/15 11:24 ，返回的时间就是 2023/11/14 23:59:59.000
/// 这样处理的原因是,dart中日期after是>,而不是>=
DateTime getStartDate(DateTime date) {
  date = date.subtract(const Duration(days: 1));
  date = date.copyWith(hour: 23, minute: 59, second: 59, millisecond: 0);
  return date;
}

///将日期转换成utc格式(供接口使用)
String formatDateToUtc(DateTime? date) {
  return date?.toUtc().toIso8601String() ?? "";
}

///将日期转换成本地格式(供本地使用)
String formatDateToLocal(DateTime? date, {String? format}) {
  return DateUtil.formatDate(date, isUtc: false, format: format);
}

///将日期字符串转换成utc格式(供接口使用)
///等同于[DateUtil.getUTCDateStr]
String formatDateStringToUtc(String? dateString) {
  return formatDateToUtc(DateUtil.getDateTime(dateString, isUtc: true));
}

///将日期字符串转换成本地格式(供本地使用)
String formatDateStringToLocal(String? dateString, {String? format}) {
  return DateUtil.formatDateStr(dateString, isUtc: false, format: format);
}

// String _parseDate(String? date) {
//   if (date?.isNotEmpty == true) {
//     //Z代表utc时间,0时区，所以只需将其转换为本地时区即可
//     DateTime? dateTime = DateTime.tryParse(date!)?.toLocal();
//     if (dateTime == null) {
//       return "";
//     }
//     return "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}";
//   }
//   return "";
// }

///将dateTime切换时区至UTC时间，然后再转换成字符串 yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
// String? _convertDateToString(DateTime dateTime) {
//   if (dateTime == null) {
//     return null;
//   }
//   final utcDate = dateTime.toUtc();
//   return "${utcDate.year}"
//       "-"
//       "${utcDate.month.toString().padLeft(2, "0")}"
//       "-"
//       "${utcDate.day.toString().padLeft(2, "0")}"
//       "T"
//       "${utcDate.hour.toString().padLeft(2, "0")}"
//       ":"
//       "${utcDate.minute.toString().padLeft(2, "0")}"
//       ":"
//       "${utcDate.second.toString().padLeft(2, "0")}"
//       "."
//       "${utcDate.millisecond.toString().padLeft(3, "0")}"
//       "Z";
// }
