///
///@ClassName: request_url
///@Description:
///@Author: tanglan
///@Date: 2023/3/15
class RequestURL {
  ///正式地址
  static const LOGIN_URL = "https://passport.wsgjp.com"; //正式登录地址
  static const String REGISTER_URL =
      "https://m.wsgjp.com.cn/ngp/index.html?sourceprovideid=0&agentid=0&Source=15"; //注册URL
  static const String GW_URL = "https://xzyapi.wsgjp.com/gw/api"; //官网正式地址

  static const String HALO_APP_SERVICE_URL =
      "https://www.wsgjp.com/Agreement/index.html"; //HaloAPP服务协议地址

  static const String HALO_APP_PRIVACY_URL =
      "https://fuwu.wsgjp.com/dynamic/ngppos-private"; //HaloAPP隐私协议

  ///测试地址
  static const LOGIN_URL_DEBUG = "http://************:1099"; //测试登录地址
  static const String REGISTER_URL_DEBUG = "http://************:8190/ngp/index"
      ".html?sourceprovideid=0&agentid=0&Source=15"; //测试注册URL
  static const String GW_URL_DEBUG = "http://************/gw/api"; //官网测试地址
}
