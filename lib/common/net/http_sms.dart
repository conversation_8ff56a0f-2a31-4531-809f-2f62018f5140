


/// 创建时间：2023/12/5
/// 作者：xiaotiaochong
/// 描述：

class HttpSms {
  // static Future<String> sendMsg(
  //     BuildContext context,) async {
  //   ResponseModel res = await HttpUtil.request(context, method: RequestMethod.POST_VIP_SEND_SMS);
  //   return res.data.toString();
  // }

//   static String encKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDRQhkM0rM6hNBySk2bTxY+om6xHqUpwpaqgebm4J/62/YKCwYaKCNyCj097xV4DSjykt2XiPlrJJaCpBe/bOetSe7fTjIZooik2KwfCI+4nTDIix5i+ifdW+dwybU33op4gFimKRB5t2+jVszCzSnThewHT4dDpT5OJ2fIrqnBCwIDAQAB";
//   static String signKey = "by+k0sms0own1c3_7qrmokh6p53bk6a!";
//
//   static sendMsg() async {
//     Map messageMap = {"code": "123456"};
//     String message = jsonEncode(messageMap);
//     String signname = "网上管家婆";
//     String phonelist = "18030576400";
//     String companyname = "sms01";
//     String template_code = "SMS_461730153";
//
//     String timestamp = DateUtil.formatDate(DateTime.now(),format: "yyyy-MM-dd HH:mm:ss");
//      String p = "signname=$signname&message=$message&phonelist=$phonelist&companyname=$companyname&template_code=$template_code";
//     p = EncryptionTool.rsaEncodeString(p, encKey);
//     String sign = EncryptionTool.md5FromString(timestamp + p + signKey);
//
//     String requestBody = '''<?xml version="1.0" encoding="utf-8"?>
// <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
//   <soap:Body>
//     <SendInternalMessage xmlns="https://tempuri.org/">
//       <timestamp>$timestamp</timestamp>
//       <p>$p</p>
//       <sign>$sign</sign>
//     </SendInternalMessage>
//   </soap:Body>
// </soap:Envelope>''';
//
//     Uri url = Uri.parse("https://smsnew.mygjp.com.cn/webService/MessageWebService.asmx");
//     http.Response response = await http.post(url,
//         headers: {
//           "SOAPAction": "https://tempuri.org/SendInternalMessage",
//           "Content-Type": "text/xml;charset=utf-8",
//           "Accept": "*/*",
//         },
//         body: utf8.encode(requestBody),
//         encoding: Encoding.getByName("UTF-8"));
//
//     XmlDocument document =  XmlDocument.parse(response.body);
//     final values = document.findAllElements('key');
//     if (values.isNotEmpty) {
//       values.map((e) => e.text).forEach((element) {
//         print(element);
//       });
//     }
//   }
}
