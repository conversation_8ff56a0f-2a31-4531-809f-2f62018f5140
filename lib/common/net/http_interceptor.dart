import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:halo_pos/login/tool/login_util.dart';
import '../../../common/login/login_center.dart';
import '../../../common/tool/dialog_util.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../login/login_page.dart';
import 'package:haloui/haloui.dart';

class HttpInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (options.extra["isLoading"]) {
      BuildContext context = options.extra["dioContext"];
      HaloDialog(
        context,
        isShowProgress: true,
        dismissOnTouchOutside: false,
      ).show();
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (response.requestOptions.extra["isLoading"]) {
      BuildContext context = response.requestOptions.extra["dioContext"];
      Navigator.pop(context);
    }
    handler.next(response);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    BuildContext context = err.requestOptions.extra["dioContext"];
    if (err.requestOptions.extra["isLoading"]) {
      Navigator.pop(context);
    }
    if (null == err.response) {
      if (err.type == DioErrorType.connectTimeout ||
          err.type == DioErrorType.receiveTimeout ||
          err.type == DioErrorType.sendTimeout ||
          err.message.contains("Connection failed") ||
          err.message.contains("失败")) {
        HaloToast.show(context, msg: "网络连接超时,请检查网络再次重试！");
        return;
      }
      if (null != err.error &&
          (err.error.toString().contains("refused") ||
              err.error.toString().contains("拒绝"))) {
        HaloToast.show(context, msg: "服务访问被拒，请重新登陆或联系管理员！${err.toString()}");
        return;
      }
      if (err.type == DioErrorType.other) {
        HaloToast.show(context, msg: "访问失败！错误原因：${err.error.toString()}");
      }
      handler.next(err);
      return;
    }
    if (err.response?.statusCode == 401) {
      LoginUtil.tipsLoginOut(
          context, "您的登录信息已过期\n原因:${err.response!.data["message"] ?? ""}");
    } else if (err.response != null && err.response!.statusCode == 404) {
      HaloToast.show(context, msg: "找不到请求地址，请检查重试！${err.response.toString()}");
      handler.next(err);
    } else {
      handler.next(err);
    }
  }
}
