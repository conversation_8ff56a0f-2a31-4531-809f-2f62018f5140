///
///@ClassName: request_method
///@Description: 请求方法类
///@Author: tanglan
///@Date: 8/2/21 1:52 PM
class RequestMethod {
  RequestMethod._();

  ///获取门店配置信息
  // static const String GET_SHOP_CONFIG = "/systemConfig/getShopConfig";

  ///发送短信
  static const String POST_VIP_SEND_SMS = "shopsale/bill/sendSMS";

  ///验证会员密码
  static const String VALID_VIP_PASSWORD = "shopsale/baseInfo/validVipPassword";

  ///更新会员密码
  static const String UP_DATE_VIP_PASSWORD =
      "shopsale/baseInfo/updateVipPassword";

  ///取单
  static const String POST_ORDER_LIST = "shopsale/billcore/list";

  ///支付方式获取
  static const String GET_PAYMENT_LIST_BY_OTYPEID =
      "shopsale/store/getPaymentListByOtypeIdMap";

  ///调拨入库
  static const String POST_GOODS_TRANS_IN_STOCK = "shopsale/bill/goodsTrans/in";

  ///查所有状态单据
  static const String POST_ORDER_ALL_LIST = "shopsale/orderbill/getAllBillList";

  ///获取商品分类
  static const String POST_GETBASEINFOCLASS = "shopsale/bill/getBaseInfoClass";

  ///保存商品
  static const String POST_SAVEBASEINFOPTYPE =
      "shopsale/bill/saveBaseInfoPtype";

  ///新增或更新商品门店价格本零售价
  static const String POST_ADD_OR_UPDATE_OTYPE_PRICE =
      "shopsale/ptype/addOrUpdateOtypePrice";

  ///选择单据(退货) 此处有储值
  static const String POST_ORDER_LIST_PREFERENTIAL =
      "shopsale/billcore/listAndPreferential";

  ///删单
  static const String POST_DELETE_ORDER = "shopsale/billcore/check/deleteBill";

  ///逻辑删除
  static const String POST_logicalDeleteBill =
      "shopsale/bill/logicalDeleteBill";

  ///修改单据备注
  static const String POST_updateBillMemo = "shopsale/bill/updateBill";

  ///获取单据详情和会员电话
  static const String POST_GOODS_BILL = "shopsale/bill/getGoodsBill";

  ///获取商品库存
  static const String POST_GETSTOCKQTY = "shopsale/bill/getStockQty";

  ///获取单据详情、会员电话、优惠辅助表
  static const String POST_GOODS_DETAILS_AND_PREFERENTIAL =
      "shopsale/bill/getGoodsDetailsAndPreferential";

  ///商品库存查询
  static const String POST_GET_STOCK_STATISTICS =
      "shopsale/analysiscloud/getInventoryPositionList";

  ///盘点记录
  static const String POST_GET_STOCK_CHECKRECORD =
      "shopsale/analysiscloud/getListStockCheckRecord";

  ///获取盘点信息
  static const String POST_GET_STOCK_GETCHECKINFO =
      "shopsale/analysiscloud/getCheckBillInfo";

  ///盘点记录删除
  static const String POST_GET_STOCK_DELETECHECK =
      "shopsale/analysiscloud/deleteCheck";

  ///全仓盘点获取商品列表
  static const String POST_GET_STOCK_CHECKRE_INFOS =
      "shopsale/analysiscloud/getListStockCheckInfos";

  ///选择商品获取商品列表
  static const String POST_GET_STOCK_CHECKRE_PTYPESELECTOR =
      "shopsale/analysiscloud/getListCheckPtypeSelector";

  ///盘点获取默认单据信息
  static const String POST_GET_STOCK_CHECKRE_BILLNUMBER =
      "shopsale/analysiscloud/getDefaultNumber";

  ///盘点创建盘点id
  static const String POST_GET_STOCK_CHECKRE_CREATE =
      "shopsale/analysiscloud/createCheck";

  ///选商品获取盘点信息
  static const String POST_GET_STOCK_CHECKRE_GETPTYPES =
      "shopsale/analysiscloud/getPtypes";

  ///扫码获取盘点信息
  static const String POST_GET_STOCK_CHECKRE_BYSCAN =
      "shopsale/analysiscloud/searchPtypeByScan";

  ///插入盘点商品
  static const String POST_GET_STOCK_CHECKRE_INSERT =
      "shopsale/analysiscloud/insertCheckDetail";

  ///更新盘点商品
  static const String POST_GET_STOCK_CHECKRE_UPDATECHECK =
      "shopsale/analysiscloud/updateCheckDetail";

  ///删除盘点商品
  static const String POST_GET_STOCK_CHECKRE_DELETEDETAIL =
      "shopsale/analysiscloud/deleteDetail";

  ///删除盘点
  static const String POST_GET_STOCK_CHECKRE_DELETECHECKID =
      "shopsale/analysiscloud/deleteDetailByCheckId";

  ///完成盘点
  static const String POST_GET_STOCK_CHECKRE_SAVEINOUT =
      "shopsale/analysiscloud/saveInout";

  static const String POST_GET_STOCK_CHECKRE_UPDATACHECKMODEL =
      "shopsale/analysiscloud/updateCheckModel";

  ///盘点记录保存
  static const String POST_GET_STOCK_CHECKRE_SAVECHECK =
      "shopsale/analysiscloud/saveCheck";

  ///完成盘点前验证
  static const String POST_GET_STOCK_CHECKRE_SAVEOVERCHECK =
      "shopsale/analysiscloud/saveOverCheck";

  ///分仓库存库存查询
  static const String POST_NventoryGoodsDistList =
      "shopsale/analysiscloud/getNventoryGoodsDistList";

  ///根据id获取套餐信息
  static const String GET_COMBO_BY_ID = "shopsale/bill/getComboById";

  ///获取促销配置
  static const String GET_PROMOTION_AUTOMATION =
      "shopsale/promotion/getPromotionAutomation";

  ///获取商品价格
  static const String GET_PTYPE_PRICE = "shopsale/bill/getPosPtypePrice";

  ///获取系统配置
  static const String GET_SYSTEM_CONFIG = "shopsale/systemConfig/getConfig";

  ///获取权限
  static const String GET_PERMISSION = "shopsale/systemConfig/getPermission";

  ///获取权限
  static const String getModelSwitchAndVersion =
      "shopsale/systemConfig/getModelSwitchAndVersion";

  ///获取订单商品剩余优惠分摊及数量
  static const String GET_DETAIL_COMPLETE = "shopsale/bill/getDetailComplete";

  ///获取所有门店列表
  static const String POST_GET_OTYPE_LIST = "shopsale/baseInfo/getOtypeList";

  ///职员列表
  static const String GET_ETYPE__LIST = "shopsale/baseInfo/getetypelist";

  ///上传文件到七牛
  static const String POST_UPLOAD_FILE_TO_QINIU =
      "shopsale/baseinfo/qiniu/upload";

  ///上传文件到七牛
  static const String GET_QINIU_VIEW_CONFIG =
      "shopsale/bill/getQiniuViewConfig";

  ///聚合支付
  static const String POST_PAYORDERANDSAVEBILL =
      "shopsale/bill/payOrderAndSaveBillNew";

  ///聚合支付退款
  static const String POST_PAYREFUNDANDSAVEBILL =
      "shopsale/bill/payRefundAndSaveBillNew";

  ///聚合支付轮询
  static const String POST_PAYQUERYPAYSTATUSNew =
      "shopsale/bill/queryPayStatusNew";

  ///查询支付状态
  static const String POST_PAYQUERYPAYRESULT = "shopsale/bill/queryPayResult";

  //修改支付状态并过账
  static const String POST_UPDATEBILLPAYSTATE =
      "shopsale/bill/updateBillPayState";

  ///提交商品类单据
  static const String SUBMIT_GOODS_BILL = "shopsale/bill/submitGoodsBill";

  ///提交商品类单据(离线批量)
  static const String SUBMIT_GOODS_BILL_LIST =
      "shopsale/bill/submitGoodsBillList";

  ///提交单据验证
  static const String VALIDA_GOODS_BILL = "shopsale/bill/validationGoodsBill";

  ///提交商品类单据（调用的是JXC的接口）
  static const String JXC_SUBMIT_GOODS_BILL =
      "shopsale/bill/jxcSubmitGoodsBill";

  ///仓库列表
  static const String GET_KTYPE_LIST = "shopsale/baseInfo/ktype/list";

  ///往来单位列表
  static const String GET_BTYPE_LIST = "shopsale/btype/list";

  ///获取更新信息
  static const String GET_VERSION_INFO = "shopsale/appVersion/getAppVersion";

  ///获取门店列表
  static const String GET_STORE_LIST = "shopsale/store/getStoreNameList";

  ///获取门店完整配置信息（不包含停用和非登录门店）
  static const String getUsingFullStoreInfo =
      "shopsale/store/getUsingFullStoreInfo";

  ///获取门店完整配置信息（包含停用和非登录门店，用于提示）
  static const String getFullStoreInfo = "shopsale/store/getFullStoreInfo";

  ///获取套餐和商品列表（分页）
  static const String POST_SELECT_PTYPE_AND_COMBO =
      "shopsale/bill/selectPtypeAndCombo";

  ///获取商品列表
  static const String GET_GOODS_LIST = "shopsale/bill/getSkuList";

  ///获取套餐列表
  static const String GET_COMBO_LIST = "shopsale/bill/getComboList";

  ///交接班保存
  static const String POST_SHIFTCHANGES_RECROD =
      "shopsale/shiftchanges/insertShiftChangesRecord";

  ///交接班统计
  static const String POST_SHIFTCHANGES_STATISTICS =
      "shopsale/shiftchanges/getShiftChangeStatistics";

  ///会员积分调整
  static const String POST_GIVING_SCORE = "member/vip/vipAssertsChange";

  ///获取商品的批次列表
  static const String GET_GOODS_BATCH_LIST = "shopsale/bill/getGoodsBatchList";

  ///根据序列号获取商品详情(不含价格)
  ///注意，这里仅仅是查询了商品，并未处理价格、库存等。在pos端中，用于校验序列号是否存在（当使用该序列号查不到商品，说明序列号就不存在）
  static const String GET_PTYPE_DETAIL_BY_SN_WITHOUT_PRICE =
      "shopsale/bill/getGoodsInfoBySnWithoutPrice";

  ///销售额明细查询
  static const String GET_AccountBalanceChangeDetail =
      "shopsale/analysiscloud/getAccountBalanceChangeDetail";

  static const String POST_getPostTimeAndVchtypeList =
      "shopsale/shiftchanges/getPostTimeAndVchtypeList";

  static const String POST_getShiftChangesSaleRecord =
      "shopsale/shiftchanges/getShiftChangesSaleRecord";

  ///上传一条钱箱存取记录
  static const String POST_CASH_BOX_PAYMENT = "shopsale/cashbox/insertPayment";

  ///获取完整促销列表
  static const String GET_FULL_PROMOTION_LIST =
      "shopsale/promotionExecute/getFullPromotionList";

  ///验证促销是否过期
  static const String GET_VALID_PROMOTION_ID =
      "shopsale/promotionExecute/getValidPromotionId";

  ///聚合支付
  static const String POST_PAYORDER = "shopsale/bill/payOrder";

  //聚合支付退款
  static const String POST_PAYREFUND = "shopsale/bill/payRefund";

  ///聚合支付查询
  static const String POST_PAYQUERYPAYSTATUS =
      "shopsale/bill/payQueryPayStatus";

  ///根据商品id列表获取各商品下单位列表
  static const String POST_GET_PTYPE_UNIT =
      "shopsale/baseInfo/getPtypeUnitList";

  ///获取仓库列表
  static const String POST_GETKTYPELIST = "shopsale/baseInfo/getKtypeList";

  //region 会员信息相关接口
  ///根据电话号码查询会员信息（列表）
  static const String POST_GET_VIP_LEVEL_ASSERTS_RIGHTS_CARD_BY_PHONE =
      "member/vip/getVipWithLevelScoreRightsCardByPhone";

  ///根据微信会员码和电话号码查询会员信息（列表）
  static const String POST_GET_VIP_LEVEL_ASSERTS_RIGHTS_CARD_BY_CODE =
      "member/vip/getVipByCodeNew";

  ///根据微信会员码和电话号码查询会员信息（分页查询）
  static const String POST_GET_VIP_BY_CODE_NEW = "member/vip/getVipByCodeNew";

  ///根据会员id查询会员信息
  static const String POST_GET_VIP_LEVEL_ASSERTS_RIGHTS_CARD_BY_ID =
      "member/vip/getVipWithRightsAndOtypeById";

  ///获取会员等级列表
  static const String POST_GET_VIP_LEVEL_LIST =
      "member/vipLevel/getVipLevelList";

  ///获取当前创建的全部会员标签
  static const String POST_GET_VIP_TAG = "member/vip/getAllTags";

  ///获取全部的权益卡模板信息
  static const String POST_GET_RIGHTS_CARD_TEMPLATE_LIST =
      "member/rightscard/getRightsCardList";

  ///新增或编辑一个会员
  static const String POST_ADD_OR_EDIT_VIP = "member/vip/addOrEditVip";

  //endregion 会员相关接口

  ///获取积分策略
  static const String GET_GET_SCORE_CONFIGURATION =
      "member/scoreConfiguration/getScoreConfiguration";

  ///获取权益价值
  static const String POST_GET_CARD_TEMPLATE_AND_DETAILS =
      "member/card/getCardTemplateAndDetails";

  ///通过id获取优惠券,批量
  static const String POST_GET_CARD_TEMPLATE_LIST_IDS =
      "member/cardTemplate/getCardTemplateListByIds";

  ///通过id获取优惠券
  static const String POST_CARD_TEMPLATE_BY_ID =
      "member/cardTemplate/getCardTemplateInit/";

  ///获取优惠券列表
  static const String POST_GET_CARD_TEMPLATE_LIST =
      "member/cardTemplate/getCardTemplateListGrant";

  ///根据会员获取优惠券列表
  static const String POST_GET_CARD_BY_VIP_ID = "member/card/getCardByVipId";

  ///充值规则列表
  static const String POST_GET_RECHARGE_LIST =
      "member/vipRecharge/getRechargeList";

  ///根据会员信息获取可用的充值活动
  static const String POST_GET_RECHARGE_LIST_BY_VIP =
      "member/vipRecharge/getRechargeListByVip";

  ///会员充值
  static const String POST_VIP_RECHARGE = "member/vipRecharge/recharge";

  ///会员充值 生成收款单草稿
  static const String POST_VIP_RECHARGEBILL =
      "member/vipRecharge/submitRechargeBill";

  ///记录单据打印次数
  static const String WRITE_PRINT_COUNT = "shopsale/bill/writePrintCount";

  ///商品批量自动获取批次号
  static const String POST_GET_PTYPE_BATCH_QTY =
      "shopsale/bill/getPtypeBatchQty";

  ///全渠道核销
  static const String POST_VERIFICATE_DOVERIFICATE =
      "shopsale/verificate/doVerificate";

  ///全渠道核销
  static const String POST_VERIFICATE_VERIFYWITHORDER =
      "shopsale/verificate/verifyWithOrder";

  ///更新批次序列号
  static const String POST_VERIFICATE_UPDATETASKDETAILS =
      "shopsale/verificate/updateTaskDetails";

  ///系统发货
  static const String POST_DOYUNLI_LOCALSEND =
      "shopsale/verificate/doYunLiLocalSend";

  ///获取筛选条件
  static const String POST_VERIFICATE_GETSTATUS =
      "shopsale/verificate/getStatus";

  ///全渠道查询各状态的条数
  static const String POST_VERIFICATE_QUERYVERIFICATESTATECOUNT =
      "shopsale/verificate/queryVerificateStateCount";

  ///全渠道查询各状态详情
  static const String POST_VERIFICATE_QUERYVERIFICATEBILLINFO =
      "shopsale/verificate/queryVerificateBillInfo";

  ///全渠道查询获取表头信息
  static const String QUERY_VERIFICATE_HEADER_INFO =
      "shopsale/verificate/queryVerificateHeaderInfo";

  static const String POST_VERIFICATE_QUERYBILLINFOBYNUMBER =
      "shopsale/verificate/queryBillInfoByNumber";

  ///获取调拨订单列表
  static const String POST_GET_TRANSFER_ORDER_LIST =
      "shopsale/orderbill/getTransferOrderList";

  ///获取订单详情/拉取新订单
  static const String POST_GET_ORDER_BILL = "shopsale/orderbill/getBill";

  ///提交订单
  static const String POST_SUBMIT_ORDER_BILL = "shopsale/orderbill/submitBill";

  ///删除订单
  static const String POST_DELETE_ORDER_BILL = "shopsale/orderbill/delete";

  ///校验订单是否满足生单条件(明细生单)
  static const String POST_CHECK_BILL_BY_DETAIL =
      "shopsale/orderbill/checkBillByDetail";

  ///勾选订单明细生成单据
  static const String POST_SELECT_DELETE_CREATE_BILL =
      "shopsale/orderbill/selectDetailCreateBill";

  ///查询门店和关联的仓库
  static const String POST_SELECT_OTYPE_WITH_OTYPE =
      "shopsale/baseinfo/selectOtypeWithKtypeInfo";

  ///获取记录的单据消费后关联的剩余会员储值信息
  static const String POST_GET_VIP_BILL_BY_VCHCODE =
      "shopsale/bill/getVipBillByVchcode";

  ///获取权限
  static const String GET_AUTO_LOGINUSING =
      "shopsale/systemConfig/registerLoginUsing";

  ///获取运力列表
  static const String GET_PLATFORM_TEMPLATE_LIST =
      "shopsale/yunli/getPlatformTemplateList";

  ///保存运力
  static const String DO_SAVE_YUN_LI_TEMPLATE =
      "shopsale/yunli/doSaveYunLiTemplate";

  ///确认运力
  static const String DO_SUBMIT_YUN_LI_TEMPLATE =
      "shopsale/yunli/doSubmitYunLiTemplate";

  ///取消运力
  static const String CANCEL_YUN_LI = "shopsale/yunli/cancelYunLi";

  ///查询运力状态
  static const String QUERY_YUN_LI_STATUS = "shopsale/yunli/queryYunLiStatus";

  ///获取运力取消原因
  static const String QUERY_YUN_LI_CANCEL_REASON =
      "shopsale/yunli/queryYunLiCancelReason";

  ///添加消费
  static const String addYunLiPrice = "shopsale/yunli/addYunLiPrice";

  ///获取所有商品一级分类
  static const String getPtypeAllRootType = "shopsale/bill/getPtypeAllRootType";

  ///获取会员等级的权益
  static const String getCardByLevelId = "member/vipLevel/getCardByLevelId";

  ///续费或升级付费会员
  static const String renewOrUpgradeSVIP = "member/vip/renewOrUpgradeSVIP";

  ///续费或升级付费会员生成草稿单
  static const String submitVipLevelBill = "member/vip/submitVipLevelBill";

  ///付费会员升级聚合支付
  static const String vipLevelPayOrderAndSaveBill =
      "member/vip/vipLevelPayOrderAndSaveBill";

  ///会员开通聚合支付
  static const String vipCreatePayOrderAndSaveBill =
      "member/vip/vipCreatePayOrderAndSaveBill";

  ///会员储值聚合支付
  static const String rechargePayOrderAndSaveBill =
      "member/vipRecharge/rechargePayOrderAndSaveBill";

  ///会员储值支付确认处理
  static const String confirmRechargePayment =
      "member/vipRecharge/confirmRechargePayment";

  ///付费会员升级支付确认处理
  static const String confirmVipLevelPayment =
      "member/vip/confirmVipLevelPayment";

  ///根据会员id查询未过期的会员等级付费记录
  static const String selectFeeRecordByVipId =
      "member/vip/selectFeeRecordByVipId";

  ///获取积分变动快捷原因
  static const String getScoreQuickReasonList =
      "member/vip/getScoreQuickReasonList";

  //region 离线商品

  ///根据商品id获取离线商品信息(新增商品后同步本地)
  static const String getPosPtypeAllInfoByIds =
      "shopsale/posptype/getPtypeAllInfoByIds";

  ///获取pos离线商品
  static const String getPosPtypeList = "shopsale/posptype/getPtypeList";

  ///获取pos离线商品价格
  static const String getPosPtypePriceList = "shopsale/posptype/getPriceList";

  ///获取pos离线商品权限
  static const String getPosPtypeLimitList =
      "shopsale/posptype/getPtypeLimitList";

  ///获取pos离线商品标签关系
  static const String getPosPtypeLabelList =
      "shopsale/posptype/getDataLabelPtypeList";

  ///获取pos离线商品库存
  static const String getPosPtypeStockList = "shopsale/posptype/getStockList";

  ///获取pos离线商品sku
  static const String getPosPtypeSkuList = "shopsale/posptype/getSkuList";

  ///获取pos离线商品unit
  static const String getPosPtypeUnitList = "shopsale/posptype/getUnitList";

  ///获取pos离线商品套餐明细
  static const String getPosPtypeComboDetailList =
      "shopsale/posptype/getComboDetailList";

  ///获取pos离商品条码
  static const String getPosPtypeFullBarcodeList =
      "shopsale/posptype/getFullBarcodeList";

  ///获取pos离商品条码
  static const String getPtypeXcodeList = "shopsale/posptype/getPtypeXcodeList";

  ///获取属性值信息
  static const String getPropValueList = "shopsale/posptype/getPropValueList";

  //endregion 离线商品

  ///写入pos的错误日志
  static const String addPosErrorLog = "shopsale/systemConfig/addPosErrorLog";

  ///获取会员充值记录
  static const String getRechargeRecordList =
      "member/vipRecharge/getRechargeRecordList";

  ///获取会员充值记录详情
  static const String getRechargeRecordDetail =
      "member/vipRecharge/getRechargeRecordDetail";

  ///会员储值作废
  static const String invalidateRechargeRecord =
      "member/vipRecharge/invalidateRechargeRecord";

  ///获取单据积分信息
  static const String getBillVipScoreInfo = "shopsale/bill/getBillVipScoreInfo";

  ///新增门店登录表并返回第一条未交接班的数据
  static const String insertLoginRecord =
      "shopsale/shiftchanges/insertLoginRecord";

  ///更新所有门店登陆表的交接班为1
  static const String updateAllLoginRecordShiftChanged =
      "shopsale/shiftchanges/updateLoginRecord";

  ///批量解密
  static const String POST_batchDecryptBuyers =
      "shopsale/sisClient/batchDecryptBuyers";

  ///门店收入统计
  static const String POST_getStoreIncomeStatisticsListForPOS =
      "shopsale/report/getStoreIncomeStatisticsListForPOS";

  ///保存打印配置
  static const String savePrintConfig = "shopsale/store/savePrintConfig";

  ///保存打印配置
  static const String getPrintConfig = "shopsale/store/getPrintConfig";
}
