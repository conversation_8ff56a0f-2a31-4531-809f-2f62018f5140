import 'dart:convert';

import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:halo_pos/offline/offline_tool.dart';
import 'package:halo_utils/utils/logger.dart';
import '../../../application.dart';
import '../../../common/login/login_center.dart';
import '../../../common/net/http_interceptor.dart';
import 'package:halo_utils/halo_utils.dart';
import 'package:halo_utils/http/base_model.dart';
import 'package:halo_utils/utils/string_util.dart';
import 'package:haloui/widget/halo_toast.dart';

import '../../offline/offline_tool.dart';

import '../tool/sp_custom_util.dart';

///
///@ClassName: http_util
///@Description: 网络请求封装
///@Author: tanglan
///@Date: 7/29/21 4:17 PM
class HttpUtil {
  static HttpUtil _httpUtil = HttpUtil._();

  static HttpUtil getInstance(BuildContext context) {
    // 保持本地实例直到完全初始化。
    DioManager().openDebug();
    BaseOptions options = BaseOptions();
    options.receiveTimeout = 60000;
    options.connectTimeout = 60000;
    options.sendTimeout = 60000;
    //加入拦截器
    HttpInterceptor interceptor = HttpInterceptor();
    DioManager().addInterceptors(interceptor);
    //设置基础配置
    DioManager().setOptions(options);
    // DioManager().onConnectivityChanged((connectResult) {
    //   if (connectResult == ConnectivityResult.none) {
    //     HaloToast.show(context,
    //         msg: StringRes.NO_CONNECT_NET_MSG.getText(context));
    //   }
    // });
    return _httpUtil;
  }

  HttpUtil._();

  static Future<ResponseModel> request(BuildContext context,
      {String? baseUrl,
      required String method,
      bool isLoading = true,
      DioConfig? config,
      dynamic data,
      NetMethod requestModel = NetMethod.POST,
      bool autoHandleError = true,
      bool Function(dynamic)? onErrorCallBack}) async {
    if (OffLineTool().isOfflineLogin) {
      OffLineTool.showOffLineToast(context);
      return ResponseModel();
    }

    ///这里有个bug shared_preferences 可能会异常变为不可读的二进制编码内容需要替换
    if (LoginCenter.getLoginUser().requestUrl == null) {
      await SpCustomUtil.restorePreferencesFromBackup();
      if (context.mounted) {
        HaloToast.show(context, msg: "检测到本地设置配置文件损坏，已修复，请重新登录");
        return throw "";
      }
    }
    DioManager().config.BASE_URL =
        (LoginCenter.getLoginUser()!.requestUrl ?? "") + "/sale/";
    DioManager().getOptions().headers = {
      "authorization": LoginCenter.getLoginUser()?.authorization
    };
    if (StringUtil.isNotEmpty(Application.debugAPIURL)) {
      DioManager().config.BASE_URL = Application.debugAPIURL;
      DioManager().getOptions().headers.addAll({
        "ngp-router": json.encode({
          "profileId": null == LoginCenter.getLoginUser()
              ? ""
              : LoginCenter.getLoginUser()?.profileId,
          "employeeId": null == LoginCenter.getLoginUser()
              ? ""
              : LoginCenter.getLoginUser()?.employeeId,
          "deploy": LoginCenter.getLoginUser()?.deploy,
          "productId": LoginCenter.getLoginUser()?.productId,
          "serverId": LoginCenter.getLoginUser()?.serverId,
          "adminStatus": LoginCenter.getLoginUser()?.adminStatus,
          // "ngp-debug-": {
          //   "shell": "172.16.3.111:9999",
          //   "baseinfo": "172.16.3.111:28006"
          // },
        })
      });
    }
    DioManager().getOptions().receiveTimeout = config != null
        ? config.RECEIVE_TIMEOUT
        : DioManager().config.RECEIVE_TIMEOUT;
    DioManager().getOptions().sendTimeout = config != null
        ? config.CONNECT_TIMEOUT
        : DioManager().config.CONNECT_TIMEOUT;
    DioManager().getOptions().connectTimeout = config != null
        ? config.CONNECT_TIMEOUT
        : DioManager().config.CONNECT_TIMEOUT;
    setProxy();

    // DioManager().getOptions().headers.addAll({
    //   "ngp-router": jsonEncode({
    //     "profileId": null == LoginCenter.getLoginUser()
    //         ? ""
    //         : LoginCenter.getLoginUser().profileId,
    //     "employeeId": null == LoginCenter.getLoginUser()
    //         ? ""
    //         : LoginCenter.getLoginUser().employeeId,
    //     "deploy": LoginCenter.getLoginUser().deploy,
    //     "productId": LoginCenter.getLoginUser().productId,
    //     "serverId": LoginCenter.getLoginUser().serverId,
    //     "adminStatus": true,
    //     // "ngp-debug-": {
    //     //   "shell": "172.16.3.111:9999",
    //     //   "baseinfo": "172.16.3.111:28006"
    //     // },
    //   })
    // });

    if (requestModel == NetMethod.POST && data is Map) {
      data = Map.from(data);
      data["operationSource"] = "POS";
    }

    Future res = DioManager().request(method, context,
        isLoading: isLoading,
        method: requestModel,
        baseUrl: baseUrl,
        config: config,
        data: data,
        onErrorCallBack: onErrorCallBack);

    if (autoHandleError) {
      res.onError((DioError error, stackTrace) {
        if (error.message.contains("超过系统的支持范围")) {
          HaloToast.show(context, msg: "数据超过系统的支持范围!");
        } else {
          try {
            if (null == error.response) {
              if (error.type == DioErrorType.connectTimeout ||
                  error.type == DioErrorType.receiveTimeout ||
                  error.type == DioErrorType.sendTimeout) {}
            } else {
              HaloToast.show(context,
                  msg: error.response?.data["message"] ?? "未知错误");
            }
          } catch (e) {
            //这里不展示系统错误 考虑用户友好度
          }
          return throw error;
        }
      });
    }
    return await res;
  }

  ///直接录入url直联，用于后台任务获取数据
  static Future<Response> requestByUrl(
      String url, Map<String, Object> data) async {
    return await Dio().post(url, data: data);
  }

  //设置代理
  static void setProxy() {
    String proxyAddress = "";
    bool useProxy = false;
    if (Application.isDebug &&
        StringUtil.isNotEmpty(Application.proxyAddress)) {
      useProxy = true;
      proxyAddress = Application.proxyAddress!;
    }

    if (useProxy && StringUtil.isNotEmpty(proxyAddress)) {
      Logger.v("Use Proxy Address:", proxyAddress);
      //请求代理
      DioManager().setProxy(proxyAddress);
    }
  }
}
