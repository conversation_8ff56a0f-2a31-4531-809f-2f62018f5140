import 'dart:async';
import 'package:flutter/cupertino.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../db/database_config.dart';

import '../application.dart';
import '../db/database_helper.dart';
import '../db/entity/ptype.dart';
import '../db/model/ptype_model.dart';
import '../db/ptype_db_manager.dart';
import 'ptype_sync.dart';

///库存同步任务
class StockSyncTimer {
  static Timer? _timer;

  static const _period = Duration(hours: 1);

  StockSyncTimer._();

  static void start() {
    stop();
    _timer = Timer.periodic(_period, (timer) => _syncStock());
  }

  static void stop() {
    _timer?.cancel();
    _timer = null;
  }

  static bool get isActive => _timer?.isActive == true;

  static void _syncStock() {
    if (!SpTool.getDatabaseConfig().hasPtypeData) return;
    try {
      PtypeSyncUtil.sync<StockDO>(dataGetter: (pageIndex) async {
        BuildContext? context = Application.navigatorKey.currentContext;
        if (context == null || !context.mounted) return null;
        final pageInfo =
            await PtypeModel.getPtypeStock(context, pageIndex: pageIndex);
        DatabaseConfig config = SpTool.getDatabaseConfig();
        if (!config.hasPtypeData) return null;
        if (pageInfo.list.isNotEmpty) {
          await PtypeDbManager.updatePtypeStock(
              pageInfo.list
                  .map((e) =>
                      DatabaseHelper.changeCamelCaseMapToUnderscoreCaseMap(
                          e.toJson()))
                  .toList(growable: false),
              config.stockTableTimestamp);

        }
        return pageInfo;
      });
    } catch (e) {
      debugPrint("库存同步失败: $e");
    }
  }
}
