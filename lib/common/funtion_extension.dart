import 'dart:async';

extension ThrottleExtension on Function {
  ///扩展Function，添加节流功能
  /// 在触发事件时，立即执行目标操作，如果在指定的时间区间内再次触发了事件，则会丢弃该事件不执行，只有超过了指定的时间之后，才会再次触发事件。
  void Function() throttle([int milliseconds = 1000]) {
    bool isAllowed = true;
    Timer? throttleTimer;
    return () {
      if (!isAllowed) return;
      isAllowed = false;
      this();
      throttleTimer?.cancel();
      throttleTimer = Timer(Duration(milliseconds: milliseconds), () {
        isAllowed = true;
      });
    };
  }
}
///扩展Function，添加防抖功能
///防抖是延时操作，在触发事件时，不立即执行目标操作，而是给出一个延迟的时间，如果在指定的时间区间内再次触发了事件，则重置延时时间，只有当延时时间走完了才会真正执行。
extension DebounceExtension on Function {
  void Function() debounce([int milliseconds = 1000]) {
    Timer? debounceTimer;
    return () {
      if (debounceTimer?.isActive ?? false) debounceTimer?.cancel();
      debounceTimer = Timer(Duration(milliseconds: milliseconds), this());
    };
  }
}