import 'package:flutter/material.dart';

///
///@ClassName: string_res
///@Description: 类作用描述
///@Author: tanglan
///@Date: 7/27/21 1:14 PM
class StringResEntity {
  final String key;
  final String value;

  const StringResEntity(this.key, this.value);

  String getText(BuildContext context) {
    return this.value;
    //暂不处理多语言
    //return Translations.of(context).getText(this);
  }
}

class StringRes {
  //region 登录
  static const PASSWORD_VALID = StringResEntity(
    "PASSWORD_VALID",
    "密码已过期，请电脑端修改密码",
  );

  //
  static const LOGIN_FAIL = StringResEntity("LOGIN_FAIL", "登陆失败");

  //
  static const LOGIN_FAIL_WITH_PASSWORD = StringResEntity(
    "LOGIN_FAIL_WITH_PASSWORD",
    "账号或密码错误",
  );

  // static const WARN = StringResEntity("WARN", "提示");

  static const LOGIN_CENTER_VALID_ERROR = "登录中心验证异常，请稍候再试";
  static const LOGIN_CENTER_ERROR = "登录中心异常，请稍候再试";
  static const ACCOUNT_INPUT = StringResEntity("ACCOUNT_INPUT", "请输入登录账号");
  static const PHONE_INPUT = StringResEntity("PHONE_INPUT", "请输入手机号");
  static const GET_SMS_CODE = StringResEntity("GET_SMS_CODE", "获取验证码");
  static const SMS_CODE_INPUT = StringResEntity("SMS_CODE_INPUT", "请输入验证码");
  static const PASSWORD_INPUT = StringResEntity("PASSWORD_INPUT", "请输入密码");
  static const LOGIN_WITH_PHONE = StringResEntity("LOGIN_WITH_PHONE", "验证码登录");
  static const LOGIN_WITH_ACCOUNT = StringResEntity(
    "LOGIN_WITH_ACCOUNT",
    "密码登录",
  );
  static const COMPANY_INPUT = StringResEntity("COMPANY_INPUT", "请输入公司名");
  static const ERROR_CHECK_CODE = StringResEntity(
    "ERROR_CHECK_CODE",
    "验证码输入错误，请重新输入!",
  );
  static const LOGIN_ADDRESS_RETURN_ERROR = StringResEntity(
    "LOGIN_ADDRESS_RETURN_ERROR",
    "登录地址返回异常,请稍后再试！",
  );
  static const REGISTER_SUCCESS_AUTO_LOGIN = StringResEntity(
    "REGISTER_SUCCESS_AUTO_LOGIN",
    "注册成功,正在自动登录...",
  );
  static const PROGRESSBAR_TEXT = StringResEntity("PROGRESSBAR_TEXT", "加载中...");
  static const REGISTER_TITLE = StringResEntity('REGESTER_TITLE', '注册');
  static const WARN = StringResEntity("WARN", "提示");
  static const TIP_NO_STORE = StringResEntity(
    "TIP_NO_STORE",
    "无法找到允许云零售登录的门店， 请检查在NGP资料-门店中是否存在开启【门店登录】的门店且登录职员的拥有该部分门店的店铺数据权限",
  );

  //endregion

  ///辅助方法 请输入XX
  static String getInputHint(StringResEntity resText, BuildContext context) {
    return INPUT_CHECK_TIPS.getText(context) + resText.getText(context);
  }

  //辅助方法 请选择
  static String getSelectHint(StringResEntity resText, BuildContext context) {
    return SELECT_CHECK_TIPS.getText(context) + resText.getText(context);
  }

  static const APP_NAME = StringResEntity("APP_NAME", "网上管家婆云零售");
  static const INPUT_CHECK_TIPS = StringResEntity("INPUT_CHECK_TIPS", "请输入");
  static const SELECT_CHECK_TIPS = StringResEntity("SELECT_CHECK_TIPS", "请选择");
  static const NO_CONNECT_NET_MSG = StringResEntity(
    "NO_CONNECT_NET_MSG",
    "没有可用的网络，请检查网络连接",
  );

  static const QUITE_AFTER_CLICK_AGAIN = StringResEntity(
    "QUITE_AFTER_CLICK_AGAIN",
    "再次点击退出",
  );

  static const MENU_TITLE = StringResEntity("MENU_TITLE", "功能菜单");
  static const MORE_ACCOUNT_TXT = StringResEntity("MORE_ACCOUNT_TXT", "多账户");

  static const BILL_PREFERENTIAL_TOTAL_MUST_LESS_THAN_TOTAL = StringResEntity(
    "BILL_PREFERENTIAL_TOTAL_MUST_LESS_THAN_TOTAL",
    "订单满减优惠金额大于等于参与订单满减商品明细小计，不会执行订单满减，请确认？",
  );

  static const COUPON_BILL_PREFERENTIAL_TOTAL_MUST_LESS_THAN_TOTAL =
      StringResEntity(
        "COUPON_BILL_PREFERENTIAL_TOTAL_MUST_LESS_THAN_TOTAL",
        "优惠券优惠金额大于商品明细小计，不会执行优惠券，请确认？",
      );

  static const BILL_PREFERENTIAL_TOTAL_MUST_LESS_THAN_TOTAL_MANUAL =
      StringResEntity(
        "BILL_PREFERENTIAL_TOTAL_MUST_LESS_THAN_TOTAL_MANUAL",
        "优惠金额大于等于商品明细小计!",
      );

  //region 打印

  static const PRINT_DATE_TIME = StringResEntity("PRINT_DATE_TIME", "打印时间");
  static const WELCOME = StringResEntity("WELCOME", "欢迎光临");
  static const PRODUCT = StringResEntity("PRODUCT", "商品");
  static const QTY = StringResEntity("QTY", "数量");
  static const UNIT = StringResEntity("UNIT", "单位");
  static const PRICE = StringResEntity("PRICE", "现价");
  static const TOTAL = StringResEntity("TOTAL", "合计");
  static const TOTAL_MONEY = StringResEntity("TOTAL_MONEY", "小计");
  static const BILL_GOODS_SUM_TOTAL = StringResEntity(
    "BILL_GOODS_SUM_TOTAL",
    "合计金额",
  );
  static const BILL_GOODS_SUM_QTY = StringResEntity(
    "BILL_GOODS_SUM_QTY",
    "合计数量",
  );
  static const SUM_TOTAL = StringResEntity("SUM_TOTAL", "金额");
  static const LABEL_GIFT = StringResEntity("LABEL_GIFT", "赠");
  static const BATCH = StringResEntity("BATCH", "批次");
  static const pTypeSn = StringResEntity("pTypeSn", "序列号");
  static const BILL_BTYPE_BUYER = StringResEntity("BILL_BTYPE_BUYER", "购买单位");
  static const BILL_ETYPE = StringResEntity("BILL_ETYPE", "业务员");
  static const BILL_DTYPE = StringResEntity("BILL_ETYPE", "部门");
  static const BILL_SUMAARY = StringResEntity("BILL_SUMAARY", "摘要");
  static const BILL_MEMO = StringResEntity("BILL_MEMO", "附加说明");
  static const BILL_DATE = StringResEntity("BILL_DATE", "录单日期");
  static const BILL_NUMBER = StringResEntity("BILL_NUMBER", "单据编号");
  static const BILL_CURRENCY_BUYER_FREIGHT_FEE = StringResEntity(
    "BILL_CURRENCY_BUYER_FREIGHT_FEE",
    "物流费用",
  );
  static const WTOTAL = StringResEntity("WTOTAL", "优惠金额");
  static const CURRENCY_ADVANCE_TOTAL = StringResEntity(
    "CURRENCY_ADVANCE_TOTAL",
    "使用预收",
  );
  static const ENAME = StringResEntity("ENAME", "收银员姓名");
  static const ETYPEID = StringResEntity("ETYPEID", "收银员工号");
  static const ESHOPID = StringResEntity("ESHOPID", "收银机");
  static const PRINT_DATE = StringResEntity("PRINT_DATE", "打印日期");
  static const PRINT_COUNT = StringResEntity("PRINT_COUNT", "实打/应打");

  static const messageSync = StringResEntity("messageSync", "信息同步");
  static const pTypeCode = StringResEntity("pTypeCode", "商品编码");
  static const pTypUserCode = StringResEntity("pTypUserCode", "商品编号");
  static const pTypeSkuCode = StringResEntity("pTypeSkuCode", "条码");
  static const billNumberText = StringResEntity("billNumberText", "文本");
  static const billNumberScan = StringResEntity("billNumberScan", "二维码");
  static const shopLogo = StringResEntity("shopLogo", "店铺LOGO");
  static const billShopScan = StringResEntity("billShopScan", "店铺二维码");
  static const billShopScanMemo = StringResEntity("billShopScanMemo", "二维码说明");
  static const billInvoiceMemo = StringResEntity("billInvoiceMemo", "发票二维码说明");
  static const shopNotice = StringResEntity("shopNotice", "店铺公告");
  static const shopNoticeLeft = StringResEntity("shopNoticeLeft", "居左");
  static const shopNoticeMiddle = StringResEntity("shopNoticeMiddle", "居中");
  static const shopNoticeRight = StringResEntity("shopNoticeRight", "居右");

  //endregion

  //region 提示
  static const TIPS_NO_LAST_BILL_ = StringResEntity(
    "TIPS_NO_LAST_BILL_",
    "不存在上单",
  );
  static const BILL_SAVE_ERROR_TIPS = StringResEntity(
    "BILL_SAVE_ERROR_TIPS",
    "保存失败，请联系管理员！",
  );
  static const BILL_PACK_UP = StringResEntity("BILL_PACK_UP", "收起");
  static const BILL_VIEW_ALL = StringResEntity("BILL_VIEW_ALL", "展开");

  //endregion

  //region
  static const STORE_SELECTOR = StringResEntity("STORE_SELECTOR", "门店选择");
  static const TIP_STORE_SELECTOR = StringResEntity(
    "TIP_STORE_SELECTOR",
    "请选择门店",
  );
  static const TIP_CASHIER_SELECTOR = StringResEntity(
    "TIP_CASHIER_SELECTOR",
    "请选择收银机",
  );
  static const TIP_STORE_CASHIER = StringResEntity(
    "TIP_STORE_CASHIER",
    "该门店不存在可用收银机，请在pc端先设置",
  );
  static const TIP_PRINT_CHANGE_BILL = StringResEntity(
    "TIP_PRINT_CHANGE_BILL",
    "改动内容还未保存，切换会清除改动内容，请确认?",
  );

  //endregion

  //region hint
  static const HINT_FILTER_PTYPE = StringResEntity(
    "HINT_FILTER_PTYPE",
    "商品条码或名称",
  );
  static const HINT_FILTER_ETYPE = StringResEntity(
    "HINT_FILTER_ETYPE",
    "名称/编号",
  );

  //endregion

  //region hint
  static const logisticsInfo = StringResEntity("LOGISTICS_INFO", "配送信息");
  static const LOGISTICS_NAME_STR = StringResEntity(
    "LOGISTICS_NAME_STR",
    "配送平台：",
  );
  static const LOGISTICS_STATUS_STR = StringResEntity(
    "LOGISTICS_STATUS_STR",
    "配送状态：",
  );
  static const LOGISTICS_DISPATCHER_NAME = StringResEntity(
    "LOGISTICS_DISPATCHER_NAME",
    "配送人姓名：",
  );
  static const CAPACITY_CALL_STATUS = StringResEntity(
    "CAPACITY_CALL_STATUS",
    "运力呼叫状态：",
  );
  static const SHIPPING_FEE = StringResEntity("SHIPPING_FEE", "配送费：");
  static const FREIGH_TIPS = StringResEntity("FREIGH_TIPS", "小费：");
  static const LOGISTICS_DISPATCHER_MOBILE = StringResEntity(
    "LOGISTICS_DISPATCHER_MOBILE",
    "配送人手机：",
  );
  static const DELIVERY_CODE = StringResEntity("DELIVERY_CODE", "提货码：");
  static const YUNLI_CALL = StringResEntity("YUNLI_CALL", "呼叫运力");
  static const CANCEL_CALL = StringResEntity("CANCEL_CALL", "取消呼叫");
  static const CANCEL_REASON = StringResEntity("CANCEL_REASON", "取消原因：");
  static const ADD_TIP = StringResEntity("ADD_TIP", "添加小费");
  static const YUNLI_GET_PLATFORM_LIST = StringResEntity(
    "YUNLI_GET_PLATFORM_LIST",
    "获取运力",
  );
  static const senderName = StringResEntity("senderName", "发货人");
  static const senderDate = StringResEntity("senderDate", "发货时间");
  //     StringResEntity("LOGISTICS_STATUS_STR", "配送状态："); endregion
}
