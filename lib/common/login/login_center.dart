import 'package:flutter/material.dart';
import '../../../common/login/product_type.dart';
import '../../../common/tool/sp_tool.dart';
import '../../../login/model/login_user_model.dart';
import 'package:halo_utils/halo_utils.dart';

import '../tool/sp_custom_util.dart';

///
///@ClassName: login_center
///@Description:
///@Author: tanglan
///@Date: 2023/3/6
///
///

class LoginCenter {
  static bool? isDebug = false;
  static LoginCenter? _haloLoginCenter;

  static initLoginCenter(bool? debug) {
    if (null == _haloLoginCenter) {
      _haloLoginCenter = LoginCenter();
      isDebug = debug;
    }
  }

  static LoginUserModel getLoginUser() {
    return SpTool.getLoginUser();
  }

  static String? getProfile() {
    return SpTool.getProfile();
  }

  static bool? isFirstInstall() {
    return SpCustomUtil.getBool("firstInstall", defValue: true);
  }

  static Future<bool> setFirstInstall(bool firstInstall) async {
    return (await SpCustomUtil.putBool("firstInstall", firstInstall)) ?? false;
  }

  static Future<bool?> clearLoginUser() async {
    return await SpTool.clearLoginUser();
  }

  static Future<void> loginOut(BuildContext context,
      {Function? loginOutSuccess}) async {
    LoginUserModel loginUser = getLoginUser()!;
    if (!isDebug!) {
      loginUser.pwd = "";
      loginUser.authorization = "";
    }
    await SpTool.saveLoginUser(loginUser);
    if (null != loginOutSuccess) {
      loginOutSuccess();
    }
  }

  ///获取官网产品id
  static int? getGwProductId(int productId) {
    //云进销存
    if (productId == ProductTypeData[ProductType.WHOLESALE_SMART]) {
      return GwProductTypeData[GwProductTypeEnum.WHOLESALE_SMART];
    }
    //零售
    else if (productId == ProductTypeData[ProductType.RETAIL]) {
      return GwProductTypeData[GwProductTypeEnum.RETAIL];
    }
    //云供应链 (批发)
    else {
      return GwProductTypeData[GwProductTypeEnum.WHOLESALE];
    }
  }

  ///获取官网产品id
  static int? getProductId(int productId) {
    //云进销存
    if (productId == GwProductTypeData[GwProductTypeEnum.WHOLESALE_SMART]) {
      return ProductTypeData[ProductType.WHOLESALE_SMART];
    }
    //零售
    else if (productId == GwProductTypeData[GwProductTypeEnum.RETAIL]) {
      return ProductTypeData[ProductType.RETAIL];
    }
    //云供应链 (批发)
    else {
      return ProductTypeData[ProductType.WHOLESALE];
    }
  }
}
