///
///@ClassName: product_type
///@Description: 官网和产品的id对应
///@Author: tanglan
///@Date: 2023/3/15
///
enum GwProductTypeEnum {
  WHOLESALE, //批发 - 云ERP供应链
  WHOLESALE_SMART, //批发精简版 - 云ERP进销存
  RETAIL //"零售" - 云ERP
}

const Map<GwProductTypeEnum, int> GwProductTypeData = {
  GwProductTypeEnum.WHOLESALE: 36,
  GwProductTypeEnum.RETAIL: 35,
  GwProductTypeEnum.WHOLESALE_SMART: 37,
};

enum ProductType {
  WHOLESALE, //批发 - 云ERP供应链
  WHOLESALE_SMART, //批发精简版 - 云ERP进销存
  RETAIL //"零售" - 云ERP
}

const Map<ProductType, int> ProductTypeData = {
  ProductType.WHOLESALE: 88,
  ProductType.RETAIL: 66,
  ProductType.WHOLESALE_SMART: 77,
};
