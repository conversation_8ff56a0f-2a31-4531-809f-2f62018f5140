import 'package:flutter/foundation.dart';

///带拦截的notifier
///当[interceptNotify]
mixin InterceptValueNotifierMixin on ChangeNotifier {
  bool interceptNotify = false;

  @override
  void notifyListeners() {
    if (interceptNotify) {
      return;
    }
    super.notifyListeners();
  }
}

///带拦截的notifier的默认实现
class InterceptValueNotifier<T> extends ValueNotifier<T>
    with InterceptValueNotifierMixin {
  InterceptValueNotifier(T value) : super(value);

  ///通过此方法给value赋值，将不会触发监听回调
  set valueWithoutNotify(T value) {
    interceptNotify = true;
    this.value = value;
    interceptNotify = false;
  }
}

///一个监听的集合，
///用于，例如：
///当一个查询有多个筛选条件时，更改任意一个筛选条件均会触发查询
class ListNotifier<KeyType> extends ChangeNotifier
    with InterceptValueNotifierMixin {
  final Map<KeyType, Listenable> children;

  ListNotifier(this.children);

  @override
  void addListener(VoidCallback listener) {
    super.addListener(listener);
    for (var child in children.values) {
      child.addListener(listener);
    }
  }

  @override
  void removeListener(VoidCallback listener) {
    super.removeListener(listener);
    for (var child in children.values) {
      child.removeListener(listener);
    }
  }

  @override
  void dispose() {
    super.dispose();
    for (var child in children.values) {
      if (child is ChangeNotifier) {
        child.dispose();
      }
    }
  }
}
