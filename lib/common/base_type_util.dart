import 'standard.dart';

///解决后端乱返回基本数据类型导致报错
class BaseTypeUtil {
  BaseTypeUtil._();

  static num? toNum(Object? obj, [num? defaultValue]) {
    if (obj is num) return obj;
    return obj?.let((it) => num.tryParse(it.toString())) ?? defaultValue;
  }

  static int? toInt(Object? obj, [int? defaultValue]) {
    if (obj is int) return obj;
    return obj?.let((it) => int.tryParse(it.toString())) ?? defaultValue;
  }

  static double? toDouble(Object? obj, [double? defaultValue]) {
    if (obj is double) return obj;
    return obj?.let((it) => double.tryParse(it.toString())) ?? defaultValue;
  }

  static bool? toBoolean(Object? obj, [bool? defaultValue]) {
    if (obj is bool) return obj;
    if (obj == 1) return true;
    if (obj == 0) return false;
    if (obj == "1" || obj == "true") return true;
    if (obj == "0" || obj == "false") return false;
    return defaultValue;
  }

  ///将驼峰命名转化为下划线命名，用于数据库实体序列号
  static String changeCamelCaseToUnderscoreCase(String camelCase) {
    if (camelCase.isEmpty) return camelCase;
    camelCase = camelCase.trim();
    if (camelCase.toLowerCase() != camelCase) {
      for (int i = camelCase.length - 1; i >= 0; i--) {
        String charAtIndex = camelCase[i];
        String lower = charAtIndex.toLowerCase();
        if (lower != charAtIndex) {
          camelCase = camelCase.replaceRange(i, i + 1, "_$lower");
        }
      }
    }
    return camelCase;
  }

  ///将下划线命名转化为驼峰命名，用于数据库反序列化
  static String changeUnderscoreCaseToCamelCase(String underscoreCase) {
    if (underscoreCase.isEmpty) return underscoreCase;
    underscoreCase = underscoreCase.trim();
    if (underscoreCase.contains("_")) {
      for (int i = 0; i < underscoreCase.length; i++) {
        String charAtIndex = underscoreCase[i];
        if (charAtIndex == "_") {
          int next = i + 1;
          String charAfter_ = next < underscoreCase.length
              ? underscoreCase[next].toUpperCase()
              : "";
          underscoreCase = underscoreCase.replaceRange(
              i, i + 1 + charAfter_.length, charAfter_);
        }
      }
    }
    return underscoreCase;
  }
}
