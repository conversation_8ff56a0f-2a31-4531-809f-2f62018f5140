import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'common/string_res.dart';
import 'common/system/routers.dart';
import 'common/system/support_theme.dart';
import 'login/login_page.dart';

///
///@ClassName: app
///@Description: app
///@Author: tanglan
///@Date: 7/27/21 1:13 PM
final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

class MyApp extends StatelessWidget {
  final String initPage;

  MyApp(this.initPage);

  @override
  Widget build(BuildContext context) {

    return MaterialApp(
      title: StringRes.APP_NAME.getText(context),
      theme: SupportTheme.getSupportThemes().first,

      initialRoute: initPage,
      routes: Routers.configureRoutes(),
      navigatorObservers: [routeObserver],
      localizationsDelegates: const [
        GlobalCupertinoLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
      supportedLocales: const [Locale('zh', 'CN')],
      //  home: initPage,
    );
  }
}
