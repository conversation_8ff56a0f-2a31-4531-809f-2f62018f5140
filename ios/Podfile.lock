PODS:
  - bluetooth (0.0.1):
    - Flutter
  - bluetooth_print (0.0.1):
    - Flutter
    - SVProgressHUD
  - connectivity_plus (0.0.1):
    - Flutter
    - Reachability
  - DKImagePickerController/Core (4.3.2):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.2)
  - DKImagePickerController/PhotoGallery (4.3.2):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.2)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_webview_plugin (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - halo_login_center (0.0.1):
    - Flutter
  - halo_utils (0.0.1):
    - Flutter
  - holophoto (0.0.1):
    - Flutter
  - image_gallery_saver (1.5.0):
    - Flutter
  - image_picker (0.0.1):
    - Flutter
  - launch_review (0.0.1):
    - Flutter
  - package_info (0.0.1):
    - Flutter
  - path_provider_ios (0.0.1):
    - Flutter
  - "permission_handler (5.1.0+2)":
    - Flutter
  - Reachability (3.2)
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - shared_preferences_ios (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - SVProgressHUD (2.2.5)
  - SwiftyGif (5.4.0)
  - Toast (4.0.0)
  - video_player (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - bluetooth (from `.symlinks/plugins/bluetooth/ios`)
  - bluetooth_print (from `.symlinks/plugins/bluetooth_print/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_webview_plugin (from `.symlinks/plugins/flutter_webview_plugin/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - halo_login_center (from `.symlinks/plugins/halo_login_center/ios`)
  - halo_utils (from `.symlinks/plugins/halo_utils/ios`)
  - holophoto (from `.symlinks/plugins/holophoto/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker (from `.symlinks/plugins/image_picker/ios`)
  - launch_review (from `.symlinks/plugins/launch_review/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - video_player (from `.symlinks/plugins/video_player/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - FMDB
    - Reachability
    - SDWebImage
    - SVProgressHUD
    - SwiftyGif
    - Toast

EXTERNAL SOURCES:
  bluetooth:
    :path: ".symlinks/plugins/bluetooth/ios"
  bluetooth_print:
    :path: ".symlinks/plugins/bluetooth_print/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_webview_plugin:
    :path: ".symlinks/plugins/flutter_webview_plugin/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  halo_login_center:
    :path: ".symlinks/plugins/halo_login_center/ios"
  halo_utils:
    :path: ".symlinks/plugins/halo_utils/ios"
  holophoto:
    :path: ".symlinks/plugins/holophoto/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker:
    :path: ".symlinks/plugins/image_picker/ios"
  launch_review:
    :path: ".symlinks/plugins/launch_review/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  video_player:
    :path: ".symlinks/plugins/video_player/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  bluetooth: cbc5930081a814054518f6a8dce82fbe9645e819
  bluetooth_print: 14e08a342b86baf0e6d6b34ed14bb1a52a5a2925
  connectivity_plus: 5f0eb61093bec56935f21a1699dd2758bc895587
  DKImagePickerController: b5eb7f7a388e4643264105d648d01f727110fc3d
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  file_picker: 3e6c3790de664ccf9b882732d9db5eaf6b8d4eb1
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  flutter_downloader: 058b9c41564a90500f67f3e432e3524613a7fd83
  flutter_webview_plugin: ed9e8a6a96baf0c867e90e1bce2673913eeac694
  fluttertoast: 6122fa75143e992b1d3470f61000f591a798cc58
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  halo_login_center: a85ba0cca044413bd23acc52e97a672412000f33
  halo_utils: 78db1f399b243c2cb68995888bcfa8c1acfc0850
  holophoto: 83a5561a7dde63f710f621114d58c84b80947f06
  image_gallery_saver: 259eab68fb271cfd57d599904f7acdc7832e7ef2
  image_picker: 9aa50e1d8cdacdbed739e925b7eea16d014367e6
  launch_review: 75d5a956ba8eaa493e9c9d4bf4c05e505e8d5ed0
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  path_provider_ios: 7d7ce634493af4477d156294792024ec3485acd5
  permission_handler: ccb20a9fad0ee9b1314a52b70b76b473c5f8dab0
  Reachability: 33e18b67625424e47b6cde6d202dce689ad7af96
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  shared_preferences_ios: aef470a42dc4675a1cdd50e3158b42e3d1232b32
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  SVProgressHUD: 1428aafac632c1f86f62aa4243ec12008d7a51d6
  SwiftyGif: 5d4af95df24caf1c570dbbcb32a3b8a0763bc6d7
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  video_player: ecd305f42e9044793efd34846e1ce64c31ea6fcb
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_flutter_wkwebview: 005fbd90c888a42c5690919a1527ecc6649e1162

PODFILE CHECKSUM: aafe91acc616949ddb318b77800a7f51bffa2a4c

COCOAPODS: 1.10.1
