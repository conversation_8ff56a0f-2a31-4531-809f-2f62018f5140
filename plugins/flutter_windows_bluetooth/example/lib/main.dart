import 'package:flutter/material.dart';
import 'package:flutter_windows_bluetooth/entity/bluetooth_device.dart';
import 'package:flutter_windows_bluetooth/flutter_windows_bluetooth.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final _list = <BluetoothDevice>[];

  final _plugin = FlutterWindowsBluetooth();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text(""),
        ),
        body: Center(
            child: ListView.separated(
                shrinkWrap: true,
                itemBuilder: (context, index) => GestureDetector(
                      onTap: () {
                        String deviceId = _list[index].deviceId;
                        if (deviceId.isNotEmpty) {
                          List<int> bytes = [];
                          // ESC @ 打印机初始化
                          bytes.addAll([0x1B, 0x40]);
                          // ESC < 打印头归位
                          bytes.addAll([0x1B, 0x3C]);
                          // ascii hello world
                          bytes.addAll([
                            0x48,
                            0x65,
                            0x6C,
                            0x6C,
                            0x6F,
                            0x20,
                            0x57,
                            0x6F,
                            0x72,
                            0x6C,
                            0x64
                          ]);
                          _plugin.print(deviceId, bytes);
                        }
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Text(_list[index].name),
                    ),
                separatorBuilder: (context, index) => const Divider(),
                itemCount: _list.length)),
        floatingActionButton: FloatingActionButton(
          onPressed: getPairedDevice,
          child: const Icon(Icons.search),
        ),
      ),
    );
  }

  void getPairedDevice() async {
    final list = await _plugin.getAllPairedDevice();
    if (list != null) {
      setState(() {
        _list.clear();
        _list.addAll(list);
      });
    }
  }
}
