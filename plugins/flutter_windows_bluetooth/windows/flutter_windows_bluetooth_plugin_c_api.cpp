#include "include/flutter_windows_bluetooth/flutter_windows_bluetooth_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "flutter_windows_bluetooth_plugin.h"

void FlutterWindowsBluetoothPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  flutter_windows_bluetooth::FlutterWindowsBluetoothPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
