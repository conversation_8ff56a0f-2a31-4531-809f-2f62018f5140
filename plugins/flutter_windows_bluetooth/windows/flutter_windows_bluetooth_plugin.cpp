#include "flutter_windows_bluetooth_plugin.h"

// This must be included before many other Windows headers.
#include <windows.h>
#include <winrt/Windows.Foundation.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Windows.Storage.Streams.h>
#include <winrt/windows.networking.sockets.h>
#include <winrt/Windows.Devices.Radios.h>
#include <winrt/Windows.Devices.Bluetooth.h>
#include <winrt/windows.devices.bluetooth.rfcomm.h>
#include <winrt/Windows.Devices.Bluetooth.Advertisement.h>
#include <winrt/Windows.Devices.Bluetooth.GenericAttributeProfile.h>
#include <winrt/Windows.Devices.Enumeration.h>

// For getPlatformVersion; remove unless needed for your plugin implementation.
#include <VersionHelpers.h>

#include <flutter/method_channel.h>
#include <flutter/basic_message_channel.h>
#include <flutter/event_channel.h>
#include <flutter/event_stream_handler_functions.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>
#include <flutter/standard_message_codec.h>

#include <map>
#include <memory>
#include <sstream>
#include <algorithm>
#include <iomanip>

namespace flutter_windows_bluetooth
{

  using namespace winrt;
  using namespace winrt::Windows::Foundation;
  using namespace winrt::Windows::Foundation::Collections;
  using namespace winrt::Windows::Storage::Streams;
  using namespace Windows::Networking::Sockets;
  // using namespace winrt::Windows::Devices::Radios;
  using namespace winrt::Windows::Devices::Bluetooth;
  using namespace winrt::Windows::Devices::Bluetooth::Rfcomm;
  // using namespace winrt::Windows::Devices::Bluetooth::Advertisement;
  // using namespace winrt::Windows::Devices::Bluetooth::GenericAttributeProfile;
  using namespace winrt::Windows::Devices::Enumeration;

  using flutter::EncodableList;
  using flutter::EncodableMap;
  using flutter::EncodableValue;

  // static
  void FlutterWindowsBluetoothPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarWindows *registrar)
  {
    auto channel =
        std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
            registrar->messenger(), "flutter_windows_bluetooth",
            &flutter::StandardMethodCodec::GetInstance());

    auto plugin = std::make_unique<FlutterWindowsBluetoothPlugin>();

    channel->SetMethodCallHandler(
        [plugin_pointer = plugin.get()](const auto &call, auto result)
        {
          plugin_pointer->HandleMethodCall(call, std::move(result));
        });

    registrar->AddPlugin(std::move(plugin));
  }

  FlutterWindowsBluetoothPlugin::FlutterWindowsBluetoothPlugin() {}

  FlutterWindowsBluetoothPlugin::~FlutterWindowsBluetoothPlugin() {}

  winrt::fire_and_forget FlutterWindowsBluetoothPlugin::HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result)
  {
    if (method_call.method_name().compare("getAllPairedDevice") == 0)
    {
      EncodableList list = EncodableList{};
      try
      {
        DeviceInformationCollection pairedDevices = co_await DeviceInformation::FindAllAsync(BluetoothDevice::GetDeviceSelectorFromPairingState(true));
        for (DeviceInformation deviceInfo : pairedDevices)
        {
          // lookup Address will crash,use deviceId to connect device
          // winrt::hstring address = FlutterWindowsBluetoothPlugin::lookupStringProperty(device, L"System.Devices.Aep.DeviceAddress");
          bool isConnected = FlutterWindowsBluetoothPlugin::lookupBooleanProperty(deviceInfo, L"System.Devices.Aep.IsConnected");
          list.push_back(EncodableMap{
              {"deviceId", winrt::to_string(deviceInfo.Id())},
              {"name", winrt::to_string(deviceInfo.Name())},
              {"isConnected", isConnected},
          });
        }
        result->Success(list);
      }
      catch (winrt::hresult_error const &ex)
      {
        result->Error("-1", winrt::to_string(ex.message()));
      }
    }
    else if (method_call.method_name().compare("print") == 0)
    {
      try
      {
        EncodableMap args = std::get<EncodableMap>(*method_call.arguments());
        std::string deviceId = std::get<std::string>(args[EncodableValue("deviceId")]);
        std::vector<uint8_t> bytes = std::get<std::vector<uint8_t>>(args[EncodableValue("bytes")]);
        // get Bluetooth device by deviceId
        BluetoothDevice bluetoothDevice = co_await BluetoothDevice::FromIdAsync(winrt::to_hstring(deviceId));
        // the connection status is not Connected...
        // if (bluetoothDevice && bluetoothDevice.ConnectionStatus() == BluetoothConnectionStatus::Connected)
        if (bluetoothDevice)
        {
          // get serialPort RfcommService from bluetoothDevice
          RfcommDeviceServicesResult serviceResult = co_await bluetoothDevice.GetRfcommServicesForIdAsync(RfcommServiceId::SerialPort());
          if (serviceResult.Error() == BluetoothError::Success)
          {
            auto rfcommServices = serviceResult.Services();
            if (rfcommServices && rfcommServices.Size() > 0)
            {
              RfcommDeviceService rfcommService = rfcommServices.GetAt(0);
              // create a socket and connect to the target
              StreamSocket socket;
              co_await socket.ConnectAsync(
                  rfcommService.ConnectionHostName(),
                  rfcommService.ConnectionServiceName(),
                  SocketProtectionLevel::BluetoothEncryptionAllowNullAuthentication);
              IOutputStream outputStream = socket.OutputStream();
              DataWriter writer(outputStream);
              // // ESC @
              // writer.WriteBytes(winrt::array_view<byte const>{0x1B, 0x40});
              // writer.WriteBytes(winrt::array_view<byte const>{0x1B, 0x3C});
              // // hello world
              // writer.WriteBytes(winrt::array_view<byte const>{0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0x57, 0x6F, 0x72, 0x6C, 0x64});
              // // writer.WriteBytes(winrt::array_view<byte const>{0x48, 0x65, 0x6C, 0x6C, 0x6F});
              // writer.WriteBytes(winrt::array_view<byte const>{0x0A});
              writer.WriteBytes(winrt::array_view<byte const>{bytes});
              // the writeXXX just write data to buffer, must call this method to commit;
              co_await writer.StoreAsync();
              // close stream
              co_await writer.FlushAsync();
              writer.Close();
              // close socket
              socket.Close();
              // close rfcommService
              rfcommService.Close();
              //
              bluetoothDevice.Close();
            }
          }
        }
        result->Success();
      }
      catch (winrt::hresult_error const &ex)
      {
        result->Error("-1", winrt::to_string(ex.message()));
      }
    }
    else
    {
      result->NotImplemented();
    }
  }

  bool FlutterWindowsBluetoothPlugin::lookupBooleanProperty(DeviceInformation deviceInfo, param::hstring const &property)
  {
    auto value = deviceInfo.Properties().TryLookup(property);
    return value && unbox_value<bool>(value);
  }

  winrt::hstring FlutterWindowsBluetoothPlugin::lookupStringProperty(DeviceInformation deviceInfo, param::hstring const &property)
  {
    auto value = deviceInfo.Properties().TryLookup(property);
    return unbox_value<winrt::hstring>(value);
  }

  // uint64_t FlutterWindowsBluetoothPlugin::string_to_mac(std::string const &s)
  // {
  //   unsigned char a[6];
  //   int last = -1;
  //   int rc = sscanf_s(s.c_str(), "%hhx:%hhx:%hhx:%hhx:%hhx:%hhx%n",
  //                     a + 0, a + 1, a + 2, a + 3, a + 4, a + 5,
  //                     &last);
  //   if (rc != 6 || s.size() != last)
  //     throw std::runtime_error("invalid mac address format " + s);
  //   return uint64_t(a[0]) << 40 |
  //          uint64_t(a[1]) << 32 | (
  //                                     // 32-bit instructions take fewer bytes on x86, so use them as much as possible.
  //                                     uint32_t(a[2]) << 24 | uint32_t(a[3]) << 16 | uint32_t(a[4]) << 8 | uint32_t(a[5]));
  // }

} // namespace flutter_windows_bluetooth
