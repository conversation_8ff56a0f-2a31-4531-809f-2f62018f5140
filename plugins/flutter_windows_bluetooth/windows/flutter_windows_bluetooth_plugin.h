#ifndef FLUTTER_PLUGIN_FLUTTER_WINDOWS_BLUETOOTH_PLUGIN_H_
#define FLUTTER_PLUGIN_FLUTTER_WINDOWS_BLUETOOTH_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <windows.h>
#include <winrt/Windows.Foundation.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Windows.Storage.Streams.h>
#include <winrt/Windows.Devices.Radios.h>
#include <winrt/Windows.Devices.Bluetooth.h>
#include <winrt/Windows.Devices.Bluetooth.Advertisement.h>
#include <winrt/Windows.Devices.Bluetooth.GenericAttributeProfile.h>
#include <winrt/Windows.Devices.Enumeration.h>

// For getPlatformVersion; remove unless needed for your plugin implementation.
#include <VersionHelpers.h>

#include <flutter/method_channel.h>
#include <flutter/basic_message_channel.h>
#include <flutter/event_channel.h>
#include <flutter/event_stream_handler_functions.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>
#include <flutter/standard_message_codec.h>

#include <map>
#include <memory>
#include <sstream>
#include <algorithm>
#include <iomanip>

namespace flutter_windows_bluetooth {
using namespace winrt;
using namespace winrt::Windows::Foundation;
using namespace winrt::Windows::Foundation::Collections;
using namespace winrt::Windows::Storage::Streams;
using namespace winrt::Windows::Devices::Radios;
using namespace winrt::Windows::Devices::Bluetooth;
using namespace winrt::Windows::Devices::Bluetooth::Advertisement;
using namespace winrt::Windows::Devices::Bluetooth::GenericAttributeProfile;
using namespace winrt::Windows::Devices::Enumeration;

using flutter::EncodableValue;
using flutter::EncodableList;
using flutter::EncodableMap;

class FlutterWindowsBluetoothPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  FlutterWindowsBluetoothPlugin();

  virtual ~FlutterWindowsBluetoothPlugin();

  // Disallow copy and assign.
  FlutterWindowsBluetoothPlugin(const FlutterWindowsBluetoothPlugin&) = delete;
  FlutterWindowsBluetoothPlugin& operator=(const FlutterWindowsBluetoothPlugin&) = delete;

 private:
  // Called when a method is called on this plugin's channel from Dart.
  winrt::fire_and_forget HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
  
//   winrt::fire_and_forget getAllPairedDevice(std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);

  bool lookupBooleanProperty(DeviceInformation deviceInfo, param::hstring const& property);

  winrt::hstring lookupStringProperty(DeviceInformation deviceInfo, param::hstring const& property);

  uint64_t string_to_mac(std::string const& s);
};

}  // namespace flutter_windows_bluetooth

#endif  // FLUTTER_PLUGIN_FLUTTER_WINDOWS_BLUETOOTH_PLUGIN_H_
