import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_windows_bluetooth/entity/bluetooth_device.dart';

import 'flutter_windows_bluetooth_platform_interface.dart';

/// An implementation of [FlutterWindowsBluetoothPlatform] that uses method channels.
class MethodChannelFlutterWindowsBluetooth
    extends FlutterWindowsBluetoothPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('flutter_windows_bluetooth');

  @override
  Future<List<BluetoothDevice>?> getAllPairedDevice() async {
    List<dynamic>? list =
        await methodChannel.invokeMethod("getAllPairedDevice");
    return list?.map((e) => BluetoothDevice.fromMap(e)).toList();
  }

  @override
  Future<void> print(String deviceId, List<int> bytes) async {
    await methodChannel.invokeMethod("print",
        {"deviceId": deviceId, "bytes": Uint8List.fromList(bytes)});
  }
}
