import 'package:flutter_windows_bluetooth/entity/bluetooth_device.dart';

import 'flutter_windows_bluetooth_platform_interface.dart';

class FlutterWindowsBluetooth {
  Future<List<BluetoothDevice>?> getAllPairedDevice() {
    return FlutterWindowsBluetoothPlatform.instance.getAllPairedDevice();
  }

  Future<void> print(String deviceId, List<int> bytes) {
    return FlutterWindowsBluetoothPlatform.instance.print(deviceId, bytes);
  }
}
