import 'package:flutter_windows_bluetooth/entity/bluetooth_device.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'flutter_windows_bluetooth_method_channel.dart';

abstract class FlutterWindowsBluetoothPlatform extends PlatformInterface {
  /// Constructs a FlutterWindowsBluetoothPlatform.
  FlutterWindowsBluetoothPlatform() : super(token: _token);

  static final Object _token = Object();

  static FlutterWindowsBluetoothPlatform _instance = MethodChannelFlutterWindowsBluetooth();

  /// The default instance of [FlutterWindowsBluetoothPlatform] to use.
  ///
  /// Defaults to [MethodChannelFlutterWindowsBluetooth].
  static FlutterWindowsBluetoothPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [FlutterWindowsBluetoothPlatform] when
  /// they register themselves.
  static set instance(FlutterWindowsBluetoothPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<List<BluetoothDevice>?> getAllPairedDevice();

  Future<void> print(String deviceId, List<int> bytes);
}
