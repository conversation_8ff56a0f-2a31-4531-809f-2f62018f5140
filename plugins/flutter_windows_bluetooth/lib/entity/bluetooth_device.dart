class BluetoothDevice {
  final String name;

  final String deviceId;

  final bool isConnected;

  BluetoothDevice(this.name, this.deviceId, this.isConnected);

  BluetoothDevice.fromMap(dynamic json)
      : this(json["name"] ?? "", json["deviceId"] ?? "",
            json["isConnected"] ?? false);

  Map<String, dynamic> toJson() => {
        "name": name,
        "deviceId": deviceId,
        "isConnected": isConnected,
      };
}
