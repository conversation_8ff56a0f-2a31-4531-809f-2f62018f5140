## FL Chart Documentation
<PERSON><PERSON><PERSON><PERSON> allows you to draw your charts in the Flutter, currently we support these type of charts,
click and learn more about them.

- [<PERSON><PERSON><PERSON>](line_chart.md)


- [<PERSON><PERSON><PERSON>](bar_chart.md)


- [<PERSON><PERSON><PERSON>](pie_chart.md)


- [<PERSON><PERSON><PERSON>](radar_chart.md)

-----------

- [<PERSON><PERSON> Touches](handle_touches.md)

- [<PERSON>le Animations](handle_animations.md)