# Generated code do not commit.
file(TO_CMAKE_PATH "/home/<USER>/snap/flutter/common/flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "/home/<USER>/IdeaProjects/fl_chart/example" PROJECT_DIR)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=/home/<USER>/snap/flutter/common/flutter"
  "PROJECT_DIR=/home/<USER>/IdeaProjects/fl_chart/example"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl,RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=/home/<USER>/IdeaProjects/fl_chart/example/.dart_tool/package_config.json"
  "FLUTTER_TARGET=/home/<USER>/IdeaProjects/fl_chart/example/lib/main.dart"
)
