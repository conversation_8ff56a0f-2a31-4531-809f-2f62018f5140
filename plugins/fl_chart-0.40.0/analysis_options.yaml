include: package:pedantic/analysis_options.1.9.0.yaml
analyzer:
  strong-mode:
    implicit-dynamic: false
  errors:
    # treat missing required parameters as a warning (not a hint)
    missing_required_param: warning
    # treat missing returns as a warning (not a hint)
    missing_return: warning
    # allow having TODOs in the code
    todo: ignore
    # Ignore analyzer hints for updating pubspecs when using Future or
    # Stream and not importing dart:async
    # Please see https://github.com/flutter/flutter/pull/24528 for details.
    sdk_version_async_exported_from_core: ignore
  exclude:
    - 'bin/cache/**'
    # the following two are relative to the stocks example and the flutter package respectively
    # see https://github.com/dart-lang/sdk/issues/28463
    - 'lib/i18n/stock_messages_*.dart'
    - 'lib/src/http/**'
    - 'test/**'